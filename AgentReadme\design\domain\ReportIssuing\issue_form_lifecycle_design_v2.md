# 发放单生命周期设计文档 (V2)

**版本**: 2.0
**日期**: 2025-06-07
**作者**: AI Assistant

## 1. 概述

本文档旨在定义和阐述`发放单 (IssueForm)`的简化版生命周期模型。经过分析，我们决定对原有的生命周期进行优化，以提高业务流程的清晰度、降低系统的复杂性，并更好地匹配实际操作需求。

此设计将作为后续开发和维护的统一标准。

## 2. 简化后的生命周期

我们将原有的`草稿` → `锁定` → `已确认` → `已归档` → `已删除`的复杂流程，简化为更清晰的三个核心业务状态：`草稿`、`锁定`和`已发放`。

### 2.1. 状态转换图

```mermaid
graph TD
    A["draft<br/>草稿状态"] --> B["locked<br/>锁定状态"]
    B --> C["issued<br/>发放并归档"]
    B --> A
    A --> D1["硬删除<br/>物理删除"]
    C --> D2["软删除<br/>标记删除"]
    
    A -.-> A1["✏️ 可编辑<br/>- 添加/删除条目<br/>- 修改基本信息<br/>- 硬删除"]
    B -.-> B1["🔒 锁定编辑<br/>- 用户自确认<br/>- 可退回草稿<br/>- 不可删除"]
    C -.-> C1["✅ 已完成<br/>- 生成发放记录<br/>- 更新档案状态<br/>- 可软删除"]
```

### 2.2. 状态定义

| 状态名 (`status`) | 中文名称 | 描述 |
| :--- | :--- | :--- |
| `draft` | 草稿 | 发放单的初始状态。在此状态下，用户可以自由添加、删除和修改发放单条目及基本信息。 |
| `locked` | 锁定 | 用户完成编辑并进行自我确认后的状态。此状态下，发放单内容被锁定，不可编辑，但可以退回到草稿状态进行修改。 |
| `issued` | 已发放 | 发放流程的最终状态。在此状态下，系统已根据发放单内容生成了相应的`发放记录 (IssueRecord)`，并更新了档案状态。 |

### 2.3. 删除策略

- **硬删除 (Hard Delete)**: 仅在 `draft` 状态下允许。发放单被物理删除，数据库中不保留记录。
- **软删除 (Soft Delete)**: 仅在 `issued` 状态下允许。发放单被标记为已删除 (`is_deleted = True`)，但记录仍保留在数据库中，以确保发放历史的可追溯性。

## 3. 模型与服务层实现

### 3.1. 模型层 (`IssueForm`)

状态字段将进行如下简化：

```python
# report_issuing/models.py

class IssueForm(models.Model):
    # ...
    # 状态管理 - 简化为三个核心状态
    status = models.CharField(
        choices=[
            ('draft', '草稿'),      # 创建和编辑阶段
            ('locked', '锁定'),     # 用户自确认，锁定编辑
            ('issued', '已发放'),   # 发放并归档，生成发放记录
        ], 
        default='draft', 
        max_length=20, 
        verbose_name='状态'
    )
    # ...
```

### 3.2. 服务层 (`IssueFormBusinessService` & `IssueFormService`)

业务逻辑将围绕新的状态进行重构，核心方法如下：

- `lock_with_validation()`: 执行 `draft` -> `locked` 的转换。
- `unlock_with_validation()`: 执行 `locked` -> `draft` 的回退。
- `issue_with_validation()`: 执行 `locked` -> `issued` 的转换，并触发发放记录的生成。
- `hard_delete_with_validation()`: 在 `draft` 状态下执行物理删除。
- `soft_delete_with_validation()`: 在 `issued` 状态下执行标记删除。

状态转换的核心验证逻辑位于 `IssueFormService._validate_status_transition`:

```python
# report_issuing/services/issue_form_service.py

def _validate_status_transition(self, issue_form, target_status):
    # 定义合法的状态转换 - 简化版
    valid_transitions = {
        'draft': ['locked'],
        'locked': ['draft', 'issued'],
        'issued': []  # 终态，不支持状态转换，只能软删除
    }
    
    current_status = issue_form.status
    
    if target_status not in valid_transitions[current_status]:
        raise InvalidStatusTransition(
            f"不能从 '{current_status}' 状态转换到 '{target_status}' 状态"
        )
```

## 4. 数据迁移策略

对于存量数据，需要执行一次性数据迁移，将旧状态映射到新状态。

### 4.1. SQL迁移脚本

```sql
-- 步骤1: 将原 'confirmed' 状态的数据迁移至 'locked' 状态
-- 业务含义：已确认但未归档的，等同于现在锁定待发放的状态。
UPDATE report_issuing_issueform 
SET status = 'locked' 
WHERE status = 'confirmed';

-- 步骤2: 将原 'archived' 状态的数据迁移至 'issued' 状态
-- 业务含义：已归档等同于现在已发放。
UPDATE report_issuing_issueform 
SET status = 'issued' 
WHERE status = 'archived';

-- 步骤3: (可选) 处理旧的 'deleted' 状态，统一为 is_deleted 标记
-- 业务含义：确保所有删除记录都通过 is_deleted 字段标识。
UPDATE report_issuing_issueform 
SET is_deleted = true 
WHERE status = 'deleted' AND is_deleted = false;
```

### 4.2. Django Migration

建议创建一个`data migration`文件来执行以上逻辑，以确保迁移过程的可追溯性和可重复性。

## 5. 总结

本次生命周期简化设计旨在：

- **提升清晰度**: 使状态与业务操作一一对应。
- **降低复杂度**: 减少不必要的状态和转换路径。
- **增强健壮性**: 明确的删除策略可防止数据误操作。

此设计将作为`发放单`模块后续开发的基础。
