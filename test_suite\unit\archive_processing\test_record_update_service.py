import os
import tempfile
from unittest.mock import patch, MagicMock, ANY
import uuid

from django.test import TestCase
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone

from archive_processing.services.record_update_service import generate_file_url, update_archive_record
from archive_records.models import ArchiveRecord


class GenerateFileUrlTestCase(TestCase):
    """测试 generate_file_url 函数"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建一个临时目录作为 MEDIA_ROOT
        self.temp_dir = tempfile.mkdtemp()
        self.media_root_patcher = patch('django.conf.settings.MEDIA_ROOT', self.temp_dir)
        self.media_url_patcher = patch('django.conf.settings.MEDIA_URL', 'http://example.com/media/')
        self.media_root_mock = self.media_root_patcher.start()
        self.media_url_mock = self.media_url_patcher.start()
        
        # 创建媒体子目录
        self.sub_dir = os.path.join(self.temp_dir, 'archives')
        os.makedirs(self.sub_dir, exist_ok=True)
        
        # 创建测试文件
        self.test_file_path = os.path.join(self.sub_dir, 'test_file.pdf')
        with open(self.test_file_path, 'w') as f:
            f.write('test content')
    
    def tearDown(self):
        """清理测试环境"""
        self.media_root_patcher.stop()
        self.media_url_patcher.stop()
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"临时目录清理失败: {e}")
    
    def test_generate_file_url_with_media_root_path(self):
        """测试处理在 MEDIA_ROOT 下的文件路径"""
        # 预期 URL 应该是 MEDIA_URL + 相对路径
        url = generate_file_url(self.test_file_path)
        expected_url = 'http://example.com/media/archives/test_file.pdf'
        self.assertEqual(url, expected_url, "生成的 URL 与预期不符")
    
    def test_generate_file_url_with_relative_path(self):
        """测试处理相对路径"""
        # 相对于 MEDIA_ROOT 的相对路径
        rel_path = os.path.join('archives', 'test_file.pdf')
        url = generate_file_url(rel_path)
        # 由于不是绝对路径，可能会解析为文件 URI
        self.assertIsNotNone(url, "URL 不应为 None")
        
    def test_generate_file_url_with_none_path(self):
        """测试处理 None 路径"""
        url = generate_file_url(None)
        self.assertIsNone(url, "对于 None 路径应该返回 None")
    
    def test_generate_file_url_with_empty_path(self):
        """测试处理空路径"""
        url = generate_file_url('')
        self.assertIsNone(url, "对于空路径应该返回 None")
    
    def test_generate_file_url_with_non_media_path(self):
        """测试处理不在 MEDIA_ROOT 下的路径"""
        # 创建一个不在 MEDIA_ROOT 下的临时文件
        external_temp_dir = tempfile.mkdtemp()
        try:
            external_file_path = os.path.join(external_temp_dir, 'external.pdf')
            with open(external_file_path, 'w') as f:
                f.write('external content')
                
            url = generate_file_url(external_file_path)
            # 对于不在 MEDIA_ROOT 下的路径，应该返回 file:// URI
            self.assertIsNotNone(url, "URL 不应为 None")
            self.assertTrue(url.startswith('file://'), f"应该返回 file:// URI, 但得到 {url}")
        finally:
            import shutil
            shutil.rmtree(external_temp_dir)


class UpdateArchiveRecordTestCase(TestCase):
    """测试 update_archive_record 函数"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='password123'
        )
        
        # 创建测试档案记录，只使用模型支持的字段
        self.record = ArchiveRecord.objects.create(
            unified_number='TEST-2023-12345',
            # 必填字段
            commission_number='COMM-001',
            sample_number='S001',
            project_name='测试项目',  # 模型要求必填
            client_unit='测试单位',   # 模型要求必填
            # 其他可选字段
            sample_name='测试样品',
            # 移除不支持的字段: test_items, customer_name, create_datetime
        )
        
        # 模拟文件路径
        self.file_path = os.path.join(settings.MEDIA_ROOT, 'archives', 'test_file.pdf')
    
    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_archive_record_success(self, mock_generate_url):
        """测试成功更新档案记录"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 调用函数
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertTrue(result['success'])
        self.assertEqual(result['record_id'], self.record.id)
        self.assertIsNotNone(result['archive_url'])
        self.assertIsNotNone(result['source_file_id'])

        # 验证数据库中的记录
        self.record.refresh_from_db()
        self.assertIsNotNone(self.record.source_file)
        self.assertEqual(self.record.source_file.archive_box_number, 'BOX-001')
        self.assertEqual(self.record.archive_status, '已归档')
    
    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_archive_record_without_box_number(self, mock_generate_url):
        """测试不提供盒号的情况"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 调用函数
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id
        )
        
        # 验证结果
        self.assertTrue(result['success'])
        
        # 验证数据库中的记录
        self.record.refresh_from_db()
        self.assertIsNotNone(self.record.source_file)
        self.assertEqual(self.record.source_file.archive_box_number, 'BOX-001')
        self.assertEqual(self.record.archive_status, '已归档')
    
    def test_update_archive_record_record_not_found(self):
        """测试记录未找到的情况"""
        # 调用函数，使用不存在的统一编号
        result = update_archive_record(
            unified_number='NONEXISTENT-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertEqual(result['status'], 'not_found')
        self.assertIn('error', result)
    
    def test_update_archive_record_missing_unified_number(self):
        """测试缺少统一编号的情况"""
        # 调用函数，统一编号为空
        result = update_archive_record(
            unified_number='',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        self.assertTrue('缺少统一编号' in result['error'])
    
    def test_update_archive_record_missing_file_path(self):
        """测试缺少文件路径的情况"""
        # 调用函数，文件路径为空
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path='',
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        self.assertTrue('缺少文件路径' in result['error'])
    
    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_archive_record_user_not_found(self, mock_generate_url):
        """测试用户不存在的情况"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 调用函数，使用不存在的用户ID
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=999,  # 假设此ID不存在
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertTrue(result['success'])
        
        # 验证数据库中的记录
        self.record.refresh_from_db()
        self.assertIsNotNone(self.record.source_file)
        self.assertEqual(self.record.source_file.archive_box_number, 'BOX-001')
        self.assertEqual(self.record.archive_status, '已归档')
    
    @patch('archive_processing.services.record_update_service.generate_file_url')
    @patch('archive_records.models.ArchiveRecord.save')
    def test_update_archive_record_db_error(self, mock_save, mock_generate_url):
        """测试数据库错误的情况"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 模拟数据库保存异常
        mock_save.side_effect = Exception("数据库错误")
        
        # 调用函数
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        self.assertTrue('数据库更新异常' in result['error'])

    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_nonexistent_record(self, mock_generate_url):
        """测试记录不存在的情况"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 调用函数，使用不存在的记录ID
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertEqual(result['status'], 'not_found')
        self.assertIn('error', result)
        
        # 验证数据库中的记录
        self.record.refresh_from_db()
        self.assertIsNone(self.record.source_file)
        self.assertNotEqual(self.record.archive_status, '已归档')

    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_with_db_error(self, mock_generate_url):
        """测试数据库错误的情况"""
        # 模拟生成URL
        mock_url = 'http://example.com/media/archives/test_file.pdf'
        mock_generate_url.return_value = mock_url
        
        # 模拟数据库保存异常
        mock_generate_url.side_effect = Exception("数据库错误")
        
        # 调用函数
        result = update_archive_record(
            unified_number='TEST-2023-12345',
            file_path=self.file_path,
            user_id=self.user.id,
            assigned_box_number='BOX-001'
        )
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        self.assertTrue('数据库更新异常' in result['error'])

    @patch('archive_processing.services.record_update_service.generate_file_url')
    def test_update_record_with_nonexistent_source_file(self, mock_generate_url):
        """测试使用不存在的源文件ID进行更新"""
        mock_generate_url.return_value = 'http://example.com/file.pdf'
        
        # 使用一个不存在的UUID
        non_existent_uuid = uuid.uuid4()
        
        result = update_archive_record(
            unified_number=self.record.unified_number,
            file_path=self.file_path,
            user_id=self.user.id,
            source_file_id=non_existent_uuid
        )
        
        # 验证更新操作本身是成功的，但会记录一个警告
        self.assertTrue(result['success'])
        
        # 验证数据库记录的source_file字段未被设置
        self.record.refresh_from_db()
        self.assertIsNone(self.record.source_file) 