# CHANGE: [2025-06-08] 创建发放单条目数据服务层
from typing import Dict, List, Optional, Tuple
from django.db import models, transaction
from django.contrib.auth.models import User
from report_issuing.models import IssueForm, IssueFormItem
from archive_records.models import ArchiveRecord
import logging

logger = logging.getLogger(__name__)


class IssueFormItemService:
    """
    发放单条目数据服务层
    
    提供发放单条目的CRUD操作，管理发放单与档案记录的关联关系。
    职责：
    - 发放条目的添加、删除、更新
    - 发放次序的管理（first/second）
    - 条目数据验证和约束检查
    """
    
    def add_issue_form_items(self, form_id: int, items_data: List[Dict]) -> Dict:
        """
        批量添加发放单条目
        
        Args:
            form_id: 发放单ID
            items_data: 条目数据列表 [
                {
                    'archive_record_id': int,        # 档案记录ID
                    'copies': int,                   # 发放份数
                    'first': bool,                   # 是否为第一次发放
                    'second': bool,                  # 是否为第二次发放
                    'receiver_name': str,            # 领取人（可选）
                    'receiver_unit': str,            # 领取单位（可选）
                    'receiver_phone': str,           # 领取人电话（可选）
                    'notes': str,                    # 备注（可选）
                },
                ...
            ]
            
        Returns:
            Dict: {
                'success': bool,
                'data': {
                    'created_items': List[IssueFormItem],
                    'failed_items': List[Dict],      # 失败的条目及原因
                },
                'error': str or None
            }
        """
        try:
            with transaction.atomic():
                # 获取发放单并验证状态
                try:
                    issue_form = IssueForm.objects.get(id=form_id, is_deleted=False)
                except IssueForm.DoesNotExist:
                    return {
                        'success': False,
                        'data': {'created_items': [], 'failed_items': []},
                        'error': '发放单不存在'
                    }
                
                # 检查发放单是否可编辑
                if not issue_form.can_edit():
                    return {
                        'success': False,
                        'data': {'created_items': [], 'failed_items': []},
                        'error': f'发放单当前状态({issue_form.get_status_display()})不允许编辑'
                    }
                
                created_items = []
                failed_items = []
                
                for item_data in items_data:
                    try:
                        # 验证条目数据
                        validation_result = self._validate_item_data(item_data, issue_form)
                        if not validation_result['valid']:
                            failed_items.append({
                                'item_data': item_data,
                                'error': validation_result['error']
                            })
                            continue
                        
                        # 检查是否已存在相同的条目
                        existing_item = IssueFormItem.objects.filter(
                            issue_form=issue_form,
                            archive_record_id=item_data['archive_record_id']
                        ).first()
                        
                        if existing_item:
                            failed_items.append({
                                'item_data': item_data,
                                'error': f'档案记录 {item_data["archive_record_id"]} 已在当前发放单中'
                            })
                            continue
                        
                        # 创建发放条目
                        item = IssueFormItem.objects.create(
                            issue_form=issue_form,
                            archive_record_id=item_data['archive_record_id'],
                            copies=item_data['copies'],
                            first=item_data.get('first', False),
                            second=item_data.get('second', False),
                            receiver_name=item_data.get('receiver_name', ''),
                            receiver_unit=item_data.get('receiver_unit', ''),
                            receiver_phone=item_data.get('receiver_phone', ''),
                            notes=item_data.get('notes', '')
                        )
                        
                        created_items.append(item)
                        
                    except Exception as e:
                        failed_items.append({
                            'item_data': item_data,
                            'error': f'创建失败: {str(e)}'
                        })
                
                logger.info(f"批量添加发放条目完成: form_id={form_id}, 成功={len(created_items)}, 失败={len(failed_items)}")
                
                return {
                    'success': True,
                    'data': {
                        'created_items': created_items,
                        'failed_items': failed_items,
                    },
                    'error': None
                }
                
        except Exception as e:
            logger.error(f"批量添加发放条目失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': {'created_items': [], 'failed_items': []},
                'error': f'添加失败: {str(e)}'
            }
    
    def remove_issue_form_items(self, form_id: int, archive_record_ids: List[int]) -> Dict:
        """
        批量删除发放单条目
        
        Args:
            form_id: 发放单ID
            archive_record_ids: 要删除的档案记录ID列表
            
        Returns:
            Dict: {
                'success': bool,
                'data': {
                    'removed_count': int,
                    'failed_items': List[Dict],
                },
                'error': str or None
            }
        """
        try:
            with transaction.atomic():
                # 获取发放单并验证状态
                try:
                    issue_form = IssueForm.objects.get(id=form_id, is_deleted=False)
                except IssueForm.DoesNotExist:
                    return {
                        'success': False,
                        'data': {'removed_count': 0, 'failed_items': []},
                        'error': '发放单不存在'
                    }
                
                # 检查发放单是否可编辑
                if not issue_form.can_edit():
                    return {
                        'success': False,
                        'data': {'removed_count': 0, 'failed_items': []},
                        'error': f'发放单当前状态({issue_form.get_status_display()})不允许编辑'
                    }
                
                # 查找要删除的条目
                items_to_remove = IssueFormItem.objects.filter(
                    issue_form=issue_form,
                    archive_record_id__in=archive_record_ids
                )
                
                removed_count = 0
                failed_items = []
                
                for item in items_to_remove:
                    try:
                        archive_record_id = item.archive_record_id
                        item.delete()  # 会触发模型的删除逻辑，清理相关数据
                        removed_count += 1
                    except Exception as e:
                        failed_items.append({
                            'archive_record_id': archive_record_id,
                            'error': f'删除失败: {str(e)}'
                        })
                
                # 检查未找到的档案记录
                found_ids = set(items_to_remove.values_list('archive_record_id', flat=True))
                requested_ids = set(archive_record_ids)
                missing_ids = requested_ids - found_ids
                
                for missing_id in missing_ids:
                    failed_items.append({
                        'archive_record_id': missing_id,
                        'error': '在当前发放单中未找到该档案条目'
                    })
                
                logger.info(f"批量删除发放条目完成: form_id={form_id}, 删除={removed_count}, 失败={len(failed_items)}")
                
                return {
                    'success': True,
                    'data': {
                        'removed_count': removed_count,
                        'failed_items': failed_items,
                    },
                    'error': None
                }
                
        except Exception as e:
            logger.error(f"批量删除发放条目失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': {'removed_count': 0, 'failed_items': []},
                'error': f'删除失败: {str(e)}'
            }
    
    def get_items_by_form_id(self, form_id: int) -> List['IssueFormItem']:
        """
        获取发放单的所有条目
        
        Args:
            form_id: 发放单ID
            
        Returns:
            List[IssueFormItem]: 发放条目列表
        """
        try:
            return list(
                IssueFormItem.objects
                .select_related('archive_record', 'issue_form')
                .filter(issue_form_id=form_id)
                .order_by('id')
            )
        except Exception as e:
            logger.error(f"获取发放条目失败: form_id={form_id}, error={str(e)}")
            return []
    
    def get_item_by_id(self, item_id: int) -> Optional['IssueFormItem']:
        """
        根据ID获取发放条目
        
        Args:
            item_id: 条目ID
            
        Returns:
            IssueFormItem instance or None
        """
        try:
            return IssueFormItem.objects.select_related('issue_form', 'archive_record').get(id=item_id)
        except IssueFormItem.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"获取发放条目失败: item_id={item_id}, error={str(e)}")
            return None
    
    def clear_all_items(self, form_id: int) -> Dict:
        """
        清空发放单的所有条目
        
        Args:
            form_id: 发放单ID
            
        Returns:
            Dict: {
                'success': bool,
                'data': {'removed_count': int},
                'error': str or None
            }
        """
        try:
            # 获取发放单并验证状态
            try:
                issue_form = IssueForm.objects.get(id=form_id, is_deleted=False)
            except IssueForm.DoesNotExist:
                return {
                    'success': False,
                    'data': {'removed_count': 0},
                    'error': '发放单不存在'
                }
            
            # 检查发放单是否可编辑
            if not issue_form.can_edit():
                return {
                    'success': False,
                    'data': {'removed_count': 0},
                    'error': f'发放单当前状态({issue_form.get_status_display()})不允许编辑'
                }
            
            # 删除所有条目
            removed_count, _ = IssueFormItem.objects.filter(issue_form=issue_form).delete()
            
            logger.info(f"清空发放单条目成功: form_id={form_id}, 删除数量={removed_count}")
            
            return {
                'success': True,
                'data': {'removed_count': removed_count},
                'error': None
            }
            
        except Exception as e:
            logger.error(f"清空发放单条目失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': {'removed_count': 0},
                'error': f'清空失败: {str(e)}'
            }
    
    def get_items_summary(self, form_id: int) -> Dict:
        """
        获取发放单条目汇总信息
        
        Args:
            form_id: 发放单ID
            
        Returns:
            Dict: {
                'total_items': int,
                'first_issue_items': int,
                'second_issue_items': int,
                'total_copies': int,
                'first_issue_copies': int,
                'second_issue_copies': int,
            }
        """
        try:
            items = self.get_items_by_form_id(form_id)
            
            total_items = len(items)
            first_issue_items = sum(1 for item in items if item.first)
            second_issue_items = sum(1 for item in items if item.second)
            
            total_copies = sum(item.copies for item in items)
            first_issue_copies = sum(item.copies for item in items if item.first)
            second_issue_copies = sum(item.copies for item in items if item.second)
            
            return {
                'total_items': total_items,
                'first_issue_items': first_issue_items,
                'second_issue_items': second_issue_items,
                'total_copies': total_copies,
                'first_issue_copies': first_issue_copies,
                'second_issue_copies': second_issue_copies,
            }
            
        except Exception as e:
            logger.error(f"获取发放条目汇总失败: form_id={form_id}, error={str(e)}")
            return {
                'total_items': 0,
                'first_issue_items': 0,
                'second_issue_items': 0,
                'total_copies': 0,
                'first_issue_copies': 0,
                'second_issue_copies': 0,
            }
    
    def _validate_item_data(self, item_data: Dict, issue_form: 'IssueForm') -> Dict:
        """
        验证发放条目数据
        
        Args:
            item_data: 条目数据
            issue_form: 发放单实例
            
        Returns:
            Dict: {'valid': bool, 'error': str or None}
        """
        # 验证必需字段
        required_fields = ['archive_record_id', 'copies']
        for field in required_fields:
            if field not in item_data or item_data[field] is None:
                return {'valid': False, 'error': f'缺少必需字段: {field}'}
        
        # 验证档案记录是否存在
        try:
            archive_record = ArchiveRecord.objects.get(id=item_data['archive_record_id'])
        except ArchiveRecord.DoesNotExist:
            return {'valid': False, 'error': f'档案记录不存在: {item_data["archive_record_id"]}'}
        
        # 验证发放份数
        copies = item_data['copies']
        if not isinstance(copies, int) or copies <= 0:
            return {'valid': False, 'error': '发放份数必须是大于0的整数'}
        
        # 验证发放次序
        first = item_data.get('first', False)
        second = item_data.get('second', False)
        
        if not first and not second:
            return {'valid': False, 'error': '必须指定发放次序（第一次或第二次）'}
        
        if first and second:
            return {'valid': False, 'error': '不能同时指定为第一次和第二次发放'}
        
        # 验证发放数量是否合理
        total_copies = max(3, archive_record.total_issue_copies or 3)
        if copies > total_copies:
            return {'valid': False, 'error': f'发放份数({copies})不能超过总份数({total_copies})'}
        
        return {'valid': True, 'error': None}

# TODO: [P1] 添加发放条目冲突检测功能
# TODO: [P2] 添加发放条目批量导入功能 