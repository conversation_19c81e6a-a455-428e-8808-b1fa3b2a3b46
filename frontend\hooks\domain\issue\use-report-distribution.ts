"use client"

import type React from "react"
import { useState, useEffect, useCallback, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useToast } from "@/components/ui/use-toast"
import {
  generateTempId,
  isTempId,
  associateTempIdWithSession,
  validateTempIdBelongsToSession,
  removeTempIdFromSession,
  saveTemporaryData,
  getTemporaryData,
  removeTemporaryData,
} from "@/utils/id-utils"
import {
  reportDistributionService,
  localStorageService,
  createIssueForm,
  updateIssueForm,
  convertRecordsToItemsData,
  calculateTotalCopies,
  isRecordSelected,
} from "@/services/domain/issue/report-distribution-service"
import apiClient from "@/lib/apiClient"
import type { SelectedRecord } from "./useReportListService"

// 定义后端API响应的类型结构（已经过 camelCase 转换）
interface ApiResponseData {
  id: string | number
  issueNumber: string // 后端 issue_number -> 自动转换为 issueNumber
  receiverName: string // 后端 receiver_name -> 自动转换为 receiverName
  receiverUnit: string // 后端 receiver_unit -> 自动转换为 receiverUnit
  receiverPhone: string // 后端 receiver_phone -> 自动转换为 receiverPhone
  notes: string
  status: ReportDistributionStatus
  itemsData?: Array<{ // 后端 items_data -> 自动转换为 itemsData
    archiveRecordId: string // 后端 archive_record_id -> 自动转换为 archiveRecordId
    copies: number
  }>
  // 其他可能的后端字段（已转换为 camelCase）
  createdAt?: string // 后端 created_at -> 自动转换为 createdAt
  updatedAt?: string // 后端 updated_at -> 自动转换为 updatedAt
  issuerUsername?: string // 后端 issuer_username -> 自动转换为 issuerUsername
  history?: ReportHistoryEntry[]
  // 保留索引签名以处理未知字段，但限制为基本类型
  [key: string]: string | number | boolean | null | undefined | Array<any> | object
}

// 移除复杂的核心字段提取逻辑

// 定义报告发放单状态类型
export type ReportDistributionStatus = "creating" | "draft" | "locked" | "issued" | "deleted"

// 定义表单数据类型
export interface ReportDistributionFormData {
  issueNumber: string
  receiverName: string
  receiverUnit: string
  receiverPhone: string
  notes: string
}

// 定义历史记录条目类型
export interface ReportHistoryEntry {
  id: string
  action: string
  timestamp: string
  operator: string
  details?: string
}

// 定义报告发放单类型
export interface ReportDistribution {
  id: string
  status: ReportDistributionStatus
  issueNumber: string // 发放单编号
  receiverName: string // 领用人
  receiverUnit: string // 领用单位
  receiverPhone: string // 联系电话
  notes: string // 备注
  createdAt: string
  createdBy: string
  lockedAt?: string
  lockedBy?: string
  issuedAt?: string
  issuedBy?: string
  deletedAt?: string
  deletedBy?: string
  records: SelectedRecord[]
  history: ReportHistoryEntry[]
  [key: string]: any
}

// 定义自动保存结果类型
interface SaveResult {
  success: boolean;
  isNew?: boolean;
  data?: ReportDistribution;
  error?: any;
}

interface UseReportDistributionOptions {
  id: string
  selectedReports?: SelectedRecord[]
}

export function useReportDistribution({ 
  id, 
  selectedReports = []
}: UseReportDistributionOptions) {
  const router = useRouter()
  const { toast } = useToast()
  const { data: session, status: sessionStatus } = useSession()
  
  // 从 NextAuth session 获取用户信息
  const currentUser = session?.user
  const currentUserId = currentUser?.id
  const currentUserName = currentUser?.name || currentUser?.email || '未知用户'
  
  const [lastSavedAt, setLastSavedAt] = useState<string | null>(null)
  const [isSavingReport, setIsSavingReport] = useState(false)
  // 移除未使用的状态
  // const [isProcessingReport, setIsProcessingReport] = useState(false)
  // const [userRoleReport, setUserRoleReport] = useState<any>("admin") // 默认为管理员角色，实际应从认证上下文获取

  // 状态
  const [isLoading, setIsLoading] = useState(true)
  // 移除未使用的状态
  // const [isProcessing, setIsProcessing] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isNewReport, setIsNewReport] = useState(false)
  const [reportDistribution, setReportDistribution] = useState<ReportDistribution | null>(null)
  // 移除未使用的状态
  // const [isSaving, setIsSaving] = useState(false)
  // const [lastSaved, setLastSaved] = useState<Date | null>(null)
  // const [draftId, setDraftId] = useState<string | null>(null)
  const [hasChanges, setHasChanges] = useState(false)
  const [isCompletenessReady, setIsCompletenessReady] = useState(false) 
  const previousCompletenessRef = useRef<boolean>(false) 
  const [urlUpdatedToPermanent, setUrlUpdatedToPermanent] = useState(false)

  // 添加一个 ref 来跟踪初始化是否已完成，防止重复初始化
  const initializationCompletedRef = useRef<boolean>(false)
  
  // 添加一个 ref 来跟踪当前处理的 ID，避免 ID 变化时重复初始化
  const currentIdRef = useRef<string>(id)

  // Refs
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null)
  const statusTransitionRef = useRef<boolean>(false)

  // 使用 useMemo 来计算 selectedReports 相关的状态，避免不必要的 useEffect 触发
  const hasSelectedItems = useMemo(() => {
    return selectedReports.length > 0;
  }, [selectedReports]);

  const isCompletenessReadyCalculated = useMemo(() => {
    if (!reportDistribution) return false;
    
    const requiredFieldsFilled =
        reportDistribution.receiverName?.trim() !== "" &&
        reportDistribution.receiverUnit?.trim() !== "" &&
        reportDistribution.receiverPhone?.trim() !== "";
    
    return requiredFieldsFilled && hasSelectedItems;
  }, [reportDistribution?.receiverName, reportDistribution?.receiverUnit, reportDistribution?.receiverPhone, hasSelectedItems]);

  // 处理 ID 变化的逻辑（从临时 ID 到永久 ID）
  useEffect(() => {
    const previousId = currentIdRef.current
    const currentId = id
    
    // 如果 ID 从临时 ID 变为永久 ID，只更新内部状态，不重新初始化
    if (previousId !== currentId && 
        isTempId(previousId, "report-distribution") && 
        !isTempId(currentId, "report-distribution") &&
        reportDistribution) {
      
      console.log(`%c[调试] ID 从临时 ${previousId} 变为永久 ${currentId}，更新内部状态`, 'color: purple; font-weight: bold;');
      
      // 只更新 reportDistribution 的 ID，保持其他所有状态
      setReportDistribution(prev => prev ? { ...prev, id: currentId } : null)
      setIsNewReport(false)
      setUrlUpdatedToPermanent(true)
      
      // 更新当前 ID 引用
      currentIdRef.current = currentId
      return
    }
    
    // 如果 reportDistribution 的 ID 已经是永久 ID，但 URL 还是临时 ID，则更新 URL
    if (reportDistribution && 
        !isTempId(reportDistribution.id, "report-distribution") && 
        window.location.pathname.includes("temp-")) {
      
      console.log(`%c[调试] 检测到需要更新 URL 从临时到永久`, 'color: blue; font-weight: bold;', {
        id: reportDistribution.id,
        issueNumber: reportDistribution.issueNumber
      });
      
      // 🔧 修复: 使用 issueNumber 作为 URL 参数，这是业务标识符
      const routeParam = reportDistribution.issueNumber;
      const newUrl = `/reports/detail/${routeParam}`;
      // 使用 window.history.replaceState 进行无感的 URL 更新
      window.history.replaceState(null, '', newUrl);
      setUrlUpdatedToPermanent(true);
    }
    
    // 更新当前 ID 引用
    currentIdRef.current = currentId
  }, [id, reportDistribution])

  // 新增: 响应外部 selectedReports 的变化，并将其同步到内部状态
  useEffect(() => {
    // 仅在 reportDistribution 初始化后执行
    if (!reportDistribution) return;

    // 避免不必要的更新，只有在数据实际变化时才同步
    if (JSON.stringify(reportDistribution.records) !== JSON.stringify(selectedReports)) {
      console.log('%c[调试] 检测到 selectedReports 变化，同步到 reportDistribution.records', 'color: lightblue; font-weight: bold;');
      setReportDistribution(prev => prev ? { ...prev, records: selectedReports } : null);
      // 🔧 修复并发问题：选择的报告变化就标记为有变更，无论当前状态
      setHasChanges(true);
      console.log(`%c[调试] selectedReports 变化检测到，强制设置 hasChanges = true`, 'color: orange;');
    }
  }, [selectedReports, reportDistribution]);

  // 初始化加载逻辑 - 只在真正需要时运行
  useEffect(() => {
    // 如果已经初始化过，且不是 ID 从外部变化，则跳过
    if (initializationCompletedRef.current && currentIdRef.current === id) {
      console.log("初始化已完成且 ID 未变化，跳过重复初始化")
      return
    }

    const initializeReportDistribution = async () => {
      // 防止重复初始化
      if (initializationCompletedRef.current && currentIdRef.current === id) {
        console.log(`%c[调试] 初始化已完成，跳过重复初始化: ${id}`, 'color: gray;');
        return;
      }

      setIsLoading(true)

      // 情况1: 如果是"new"，创建新的报告发放单（使用临时ID），不访问API
      if (id === "new") {
        try {
          const tempId = generateTempId("report-distribution")
          const now = new Date()

          const newReport: ReportDistribution = {
            id: tempId,
            status: "creating",
            issueNumber: "待生成", // 明确的占位符，而不是生成临时编号
            receiverName: "",
            receiverUnit: "",
            receiverPhone: "",
            notes: "",
            createdAt: new Date().toISOString(),
            createdBy: currentUserName || "未知用户", // 使用实际的登录用户
            records: [], // 确保初始化为空数组
            history: [
              {
                id: "1",
                action: "create",
                timestamp: new Date().toLocaleString(),
                operator: currentUserName || "未知用户", // 使用实际的登录用户
              },
            ],
          }

          setReportDistribution(newReport)
          setIsNewReport(true)
          associateTempIdWithSession(tempId, "report-distribution")
          saveTemporaryData(tempId, "report-distribution", newReport)
          
          // 更新当前 ID 引用
          currentIdRef.current = tempId
          
          // 只有在 URL 确实需要更新时才更新
          if (window.location.pathname !== `/reports/detail/${tempId}`) {
            router.replace(`/reports/detail/${tempId}`)
          }
          
          initializationCompletedRef.current = true
        } catch (error) {
           console.error("创建新的本地报告时出错:", error);
           toast({ title: "初始化失败", description: "创建本地会话时出错。" });
        } finally {
          setIsLoading(false)
        }
        return // 结束执行
      }

      try {
        // 情况2: 如果是临时ID - 说明用户正在创建新的报告发放单，但尚未保存为草稿
        if (isTempId(id, "report-distribution")) {
          if (!validateTempIdBelongsToSession(id, "report-distribution")) {
            toast({
              title: "访问受限",
              description: "您无权访问此报告发放单",
              variant: "destructive",
            })
            router.replace("/reports")
            return
          }

          const savedData = getTemporaryData(id, "report-distribution")
          if (savedData) {
            if (!savedData.records) savedData.records = []
            setReportDistribution(savedData)
            setIsNewReport(true)
          } else {
            // 如果在session中找不到临时数据，可能是一个过期的URL，重定向到new
             router.replace("/reports/detail/new");
             return;
          }
          initializationCompletedRef.current = true
        } 
        // 情况3: 否则是永久ID - 说明是已保存的报告发放单，从后端获取数据
        else {
          console.log(`%c[调试] 准备根据参数获取报告发放单数据`, 'color: cyan; font-weight: bold;', {
            传入的id参数: id,
            id类型: typeof id,
            是否为数字字符串: /^\d+$/.test(id),
            说明: '如果是纯数字，可能是数据库ID；如果包含字母/特殊字符，可能是issueNumber'
          });
          
          const data = await reportDistributionService.getReportById(id)
          if (!data.records) data.records = []
          
          console.log(`%c[调试] 从后端获取的报告发放单数据`, 'color: cyan; font-weight: bold;', {
            返回的数据: data,
            id: data.id,
            issueNumber: data.issueNumber,
            字段检查: {
              id存在: !!data.id,
              issueNumber存在: !!data.issueNumber,
              URL参数: id,
              数据id匹配: data.id === id,
              数据issueNumber匹配: data.issueNumber === id
            }
          });
          
          setReportDistribution(data as ReportDistribution)
          setIsNewReport(false)
          setUrlUpdatedToPermanent(true)
          initializationCompletedRef.current = true
        }
      } catch (error: any) {
        console.error("Error initializing report distribution:", error)
        if (error?.status === 404 || error.message.includes('404')) {
            toast({
              title: "未找到报告发放单",
              description: "您尝试访问的报告发放单不存在或已被删除。",
              variant: "destructive",
            })
            router.replace("/reports/management");
        } else {
            toast({
              title: "加载失败",
              description: error.message || "无法加载报告发放单数据，请重试",
              variant: "destructive",
            })
        }
      } finally {
        setIsLoading(false)
      }
    }

    initializeReportDistribution()
  }, [id, router, toast]) // 保持对 id 的依赖，但添加了防重复逻辑

  const saveAsDraft = useCallback(async () => {
    setIsSavingReport(true)
    try {
      await reportDistributionService.saveAsDraft(reportDistribution)
      setReportDistribution(prev => prev ? { ...prev, status: "draft" } : null)
      const now = new Date().toISOString()
      localStorageService.setItem(`report_last_saved_${id}`, now)
      setLastSavedAt(new Date(now).toLocaleString())
    } catch (error) {
      console.error("Failed to save as draft:", error)
    } finally {
      setIsSavingReport(false)
    }
  }, [reportDistribution, id])

  // 通用的状态转换处理函数
  const handleStatusTransition = async (
    action: "lock" | "unlock" | "issue" | "delete",
    reason?: string
  ) => {
    console.log(`[调试] handleStatusTransition: 开始处理 action: ${action}`, { reportId: reportDistribution?.id });
    if (!reportDistribution || !reportDistribution.id) {
      toast({
        title: "操作失败",
        description: "报告发放单数据不存在。",
        variant: "destructive",
      })
      console.log(`[调试] handleStatusTransition: 操作被阻止，因为 reportDistribution 或其 ID 不存在。`);
      return
    }

    if (isTempId(reportDistribution.id, "report-distribution")) {
      toast({
        title: "操作失败",
        description: "草稿尚未保存到服务器，无法执行此操作。",
        variant: "destructive",
      })
      console.log(`[调试] handleStatusTransition: 操作被阻止，因为 ID 是临时的: ${reportDistribution.id}`);
      return
    }

    setIsProcessing(true);
    statusTransitionRef.current = true;

    try {
      // 🔧 修复: 只使用 issueNumber 进行状态更新操作
      console.log(`%c[调试] handleStatusTransition: 准备调用状态更新`, 'color: purple; font-weight: bold;', {
        action: action,
        issueNumber: reportDistribution.issueNumber,
        API调用: `PATCH /api/report-issuing/issue-forms/${reportDistribution.issueNumber}/status/`
      });
      const response = await reportDistributionService.updateStatus(
        reportDistribution.issueNumber,
        action,
        reason
      );
      
      console.log(`[调试] 状态转换 ${action} 成功:`, response);

      if (response.success && response.data) {
        toast({
          title: "操作成功",
          description: `报告发放单已${getActionText(action)}。`,
        });

        if (action === "delete") {
          router.push("/reports/management");
          return;
        }
        
        const updatedData = response.data;
        setReportDistribution(prev => {
          if (!prev) return null;
          const updatedReport = {
            ...prev,
            ...updatedData,
            history: updatedData.history || prev.history 
          };
          // 移除复杂的比较逻辑
          setHasChanges(false);
          return updatedReport;
        });

      } else {
        throw new Error(response.error || `未知的服务器错误`);
      }
    } catch (error: any) {
      console.error(`[错误] 执行 ${action} 操作失败:`, error);
      toast({
        title: "操作失败",
        description: error.message || `执行${getActionText(action)}时发生错误。`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      statusTransitionRef.current = false;
    }
  };

  // 锁定报告
  const lockReport = async () => {
    console.log("[调试] lockReport: 开始执行锁定操作。", { hasChanges, id: reportDistribution?.id });
    if (hasChanges) {
      console.log("[调试] lockReport: 检测到变更，执行保存草稿。");
      const saveResult = await handleSaveDraft();
      if (!saveResult.success) {
        toast({
          title: "锁定失败",
          description: "保存草稿时出错，无法锁定。",
          variant: "destructive",
        });
        return;
      }
    }
    console.log("[调试] lockReport: 准备调用 handleStatusTransition('lock')");
    await handleStatusTransition("lock");
    console.log("[调试] lockReport: handleStatusTransition('lock') 调用完成");
  };

  // 解锁报告
  const unlockReport = async () => {
    await handleStatusTransition("unlock");
  };

  // 发放报告
  const issueReport = async () => {
    // 发放前确保数据完整性
    if (!isCompletenessReadyCalculated) {
      toast({
        title: "无法发放",
        description: "请填写所有必填项（领用人、单位、电话）并选择至少一个报告。",
        variant: "destructive",
      });
      return;
    }

    if (hasChanges) {
      const saveResult = await handleSaveDraft();
      if (!saveResult.success) {
        toast({
          title: "发放失败",
          description: "保存最新更改时出错，无法发放。",
          variant: "destructive",
        });
        return;
      }
    }
    await handleStatusTransition("issue");
  };

  // 打印报告
  const printReport = async () => {
    if (!reportDistribution) return
    setIsProcessing(true)
    try {
      await reportDistributionService.printReport(reportDistribution.issueNumber)
      toast({ title: "打印任务已发送" })
    } catch (error: any) {
      toast({ title: "打印失败", description: error.message, variant: "destructive" })
    } finally {
      setIsProcessing(false)
    }
  }

  const deleteReport = async () => {
    if (!reportDistribution) return
    setIsProcessing(true)
    try {
      await reportDistributionService.deleteReport(reportDistribution.issueNumber)
      toast({ title: "报告已删除" })
      router.push("/reports")
    } catch (error: any) {
      toast({ title: "删除失败", description: error.message, variant: "destructive" })
    } finally {
      setIsProcessing(false)
    }
  }

  // 简化的状态更新逻辑
  useEffect(() => {
    // 如果 reportDistribution 不存在，重置所有衍生状态
    if (!reportDistribution) {
      if (hasChanges) setHasChanges(false);
      if (isCompletenessReady) setIsCompletenessReady(false);
      return;
    }

    // 更新完整性状态
    setIsCompletenessReady(isCompletenessReadyCalculated);
  }, [reportDistribution, isCompletenessReadyCalculated]);

  // 自动保存逻辑: 只依赖于计算好的衍生状态
  useEffect(() => {
    // const justBecameReady = isCompletenessReady && !previousCompletenessRef.current; // 移除首次立即保存的逻辑
    const isReadyAndHasChanges = isCompletenessReady && hasChanges;

    console.log('%c[调试] 自动保存状态评估:', 'color: purple; font-weight: bold;', {
        '是否必填完整 (isCompletenessReady)': isCompletenessReady,
        // '是否刚刚达到完整 (justBecameReady)': justBecameReady,
        '有未保存的更改 (hasChanges)': hasChanges,
        '完整且有变化 (isReadyAndHasChanges)': isReadyAndHasChanges
    });

    // 统一保存逻辑：只要表单完整且有变更，就启动防抖保存
    if (isReadyAndHasChanges) {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
      console.log('%c[调试] 检测到变更，将在2秒后触发保存...', 'color: green; font-weight: bold;');
      autoSaveTimerRef.current = setTimeout(() => {
        console.log('%c[调试] 定时器触发，调用 handleSaveDraft', 'color: green; font-weight: bold;');
        handleSaveDraft();
      }, 2000); // 延迟2秒
    }

    // 更新 previousCompletenessRef 的值以供下次比较
    previousCompletenessRef.current = isCompletenessReady;

    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, [isCompletenessReady, hasChanges]); // 依赖于核心状态

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current)
        autoSaveTimerRef.current = null
      }
    }
  }, [])
  
  // 移除复杂的比较基准逻辑

  // 处理表单输入变化
  const handleInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    console.log(`%c[调试] handleInputChange: 字段 "${name}" 的值变为 "${value}"`, 'color: gray;');
    setReportDistribution((prev) => {
      if (!prev) return null
      return { ...prev, [name]: value }
    })
    // 🔧 修复并发问题：无论当前状态如何，用户输入都标记为有变更
    setHasChanges(true);
    console.log(`%c[调试] 用户输入检测到，强制设置 hasChanges = true`, 'color: orange;');
  }, [])

  // updateIssuedAt 函数已删除，因为发放日期由后端自动生成

  // 计算总份数 - 使用服务层函数
  const totalCopies = calculateTotalCopies(selectedReports);

  // 检查记录是否已被选择的包装函数
  const checkRecordSelected = (id: string) => {
    return isRecordSelected(id, selectedReports);
  };

  /**
   * @description 保存草稿的核心逻辑
   * - 如果是新草稿，调用 create API
   * - 如果是现有草稿，调用 update API
   * - 正确处理临时 ID 和永久 ID 的转换
   * @returns {Promise<SaveResult>}
   */
  const handleSaveDraft = async (): Promise<SaveResult> => {
    if (!reportDistribution) {
        console.error("[错误] handleSaveDraft: reportDistribution 为空，无法保存。");
        return { success: false, error: "内部状态错误" };
    }
    
    // 🔧 移除 hasChanges 检查，避免竞争条件
    // 让保存总是执行，确保不会因为时序问题丢失用户输入
    console.log("[调试] handleSaveDraft: 开始执行保存草稿操作（无条件保存）。");

    setIsProcessing(true);
    console.log("[调试] handleSaveDraft: 开始执行保存草稿操作。");

    try {
        const isCreating = isNewReport || isTempId(reportDistribution.id, "report-distribution");
        
        console.log(`[调试] handleSaveDraft: ${isCreating ? '创建新草稿' : '更新现有草稿'}...`);
        
        // 动态生成需要提交给后端的数据
        const itemsData = convertRecordsToItemsData(reportDistribution.records);

        // 构建要发送的表单数据
        const formData: ReportDistributionFormData = {
          issueNumber: reportDistribution.issueNumber,
          receiverName: reportDistribution.receiverName,
          receiverUnit: reportDistribution.receiverUnit,
          receiverPhone: reportDistribution.receiverPhone,
          notes: reportDistribution.notes,
        };

        // 根据是否为新建选择对应的API
        let savedData: ReportDistribution;
        if (isCreating) {
          savedData = await createIssueForm(formData, itemsData);
          // 清理临时ID
          removeTempIdFromSession(reportDistribution.id, "report-distribution");
          removeTemporaryData(reportDistribution.id, "report-distribution");
        } else {
          // 🔧 修复: 只使用 issueNumber 进行更新操作
          console.log(`%c[调试] 保存草稿时使用发放单号`, 'color: orange; font-weight: bold;', {
            issueNumber: reportDistribution.issueNumber,
            API调用: `PUT /api/report-issuing/issue-forms/${reportDistribution.issueNumber}/`
          });
          savedData = await updateIssueForm(reportDistribution.issueNumber, formData, itemsData);
        }

        console.log("[调试] handleSaveDraft: 保存成功，从服务器返回的数据:", savedData);

        toast({
            title: "已保存",
            description: `草稿已于 ${new Date().toLocaleTimeString()} 保存。`,
        });
        
        let finalMergedData: ReportDistribution = savedData; // 默认值
        
        setReportDistribution(prev => {
          if (!prev) {
            finalMergedData = savedData;
            return savedData;
          }
          // 关键逻辑：合并数据以防止输入回滚
          const mergedData = {
            ...savedData,
            ...prev,
            id: savedData.id,
            status: savedData.status,
            issueNumber: savedData.issueNumber,
            history: savedData.history || prev.history,
          };
          finalMergedData = mergedData;
          return mergedData;
        });
        
        // 简单直接：保存成功就重置变更状态
        setHasChanges(false);
        setIsNewReport(false);
        console.log("[调试] 保存成功，重置变更状态");
        
        if (isCreating && !urlUpdatedToPermanent) {
          // 🔧 修复: 只使用 issueNumber 作为业务标识符
          const newUrl = `/reports/detail/${savedData.issueNumber}`;
          router.replace(newUrl);
          setUrlUpdatedToPermanent(true);
          console.log(`%c[调试] URL 已从临时 ID 更新为发放单号: ${newUrl}`, 'color: green; font-weight: bold;', { 
            全部后端数据: savedData,
            issueNumber: savedData.issueNumber,
            路由参数: savedData.issueNumber
          });
        }

        return { success: true, isNew: isCreating, data: savedData };

    } catch (error: any) {
        console.error("[错误] handleSaveDraft: 保存草稿失败:", error);
        toast({
            title: "保存失败",
            description: error.message,
            variant: "destructive",
        });
        return { success: false, error: error };
    } finally {
        setIsProcessing(false);
    }
  };

  // 根据ID获取报告
  const fetchReportById = useCallback(async (currentId: string) => {
    // ... existing code ...
  }, []);

  // 根据 action 获取对应的中文文本
  const getActionText = (action: string) => {
    switch (action) {
      case "lock": return "锁定";
      case "unlock": return "解锁";
      case "issue": return "发放";
      case "delete": return "删除";
      default: return "操作";
    }
  };

  // 简化的变更检测 - 只在用户输入时设置hasChanges
  // 移除复杂的自动检测逻辑，回到简单可靠的方案

  return {
    // 核心状态和数据
    reportDistribution,
    isLoading,
    isNewReport,
    isSaving: isSavingReport,
    isProcessing: isProcessing,
    lastSavedAt,
    hasChanges,
    
    // 用户信息 (来自 NextAuth)
    currentUser,
    currentUserId,
    currentUserName,
    sessionStatus,
    
    // 计算得出的数据
    totalCopies,

    // 核心方法
    handleInputChange,
    handleSaveDraft,
    
    // 状态操作方法
    saveAsDraft,
    lockReport,
    unlockReport,
    issueReport,
    printReport,
    deleteReport,

    // 记录选择检查方法
    isRecordSelected: checkRecordSelected,
  };
}
