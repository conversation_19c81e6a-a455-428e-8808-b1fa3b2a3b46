"use client"

import { useState } from "react"
import { PageTitle } from "@/components/page-title"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import ChangeOrdersFilter from "@/components/change-orders/change-orders-filter"
import { ChangeOrdersTable } from "@/components/change-orders/change-orders-table"

export default function ChangeOrdersLedgerPage() {
  const [filterParams, setFilterParams] = useState({
    status: "",
    dateRange: { from: "", to: "" },
    keyword: "",
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <PageTitle title="更改单台账" subtitle="查看和管理所有档案更改单" />
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/change-orders/new">
              <Plus className="mr-2 h-4 w-4" />
              新建更改单
            </Link>
          </Button>
        </div>
      </div>

      <ChangeOrdersFilter
        searchParams={{
          keyword: filterParams.keyword,
          dateRange: {
            from: filterParams.dateRange.from ? new Date(filterParams.dateRange.from) : undefined,
            to: filterParams.dateRange.to ? new Date(filterParams.dateRange.to) : undefined,
          },
          type: "",
          creator: "",
        }}
        setSearchParams={(params) =>
          setFilterParams({
            keyword: params.keyword,
            dateRange: {
              from: params.dateRange.from?.toISOString() || "",
              to: params.dateRange.to?.toISOString() || "",
            },
            status: filterParams.status,
          })
        }
      />
      <ChangeOrdersTable filterParams={filterParams} />
    </div>
  )
}
