"""
测试环境配置文件
用于为测试提供统一的配置项。
"""
import os
import tempfile
from django.conf import settings

# 创建测试环境临时目录
TEST_TEMP_DIR = os.path.join(tempfile.gettempdir(), 'archive_flow_tests')
os.makedirs(TEST_TEMP_DIR, exist_ok=True)

# CHANGE: [2024-04-18] 添加档案存储位置配置 #AFM-13
# 为测试环境设置默认的档案存储位置
ARCHIVE_STORAGE_LOCATION = os.path.join(TEST_TEMP_DIR, 'archives')
os.makedirs(ARCHIVE_STORAGE_LOCATION, exist_ok=True)

# 其他测试相关配置...
ENABLE_PDF_CACHE = True  # 测试时启用PDF处理缓存
PDF_CACHE_DIR = os.path.join(TEST_TEMP_DIR, 'pdf_cache')
os.makedirs(PDF_CACHE_DIR, exist_ok=True)

def apply_test_settings():
    """
    将测试配置应用到Django设置对象
    应在测试setup函数中调用
    """
    settings.ARCHIVE_STORAGE_LOCATION = ARCHIVE_STORAGE_LOCATION
    settings.ENABLE_PDF_CACHE = ENABLE_PDF_CACHE
    settings.PDF_CACHE_DIR = PDF_CACHE_DIR
    # 添加其他测试配置...

def cleanup_test_files():
    """
    测试完成后清理临时文件
    应在测试tearDown函数中调用
    """
    import shutil
    try:
        # 清理测试临时目录中的文件，但保留目录结构
        for root, dirs, files in os.walk(TEST_TEMP_DIR):
            for f in files:
                try:
                    os.unlink(os.path.join(root, f))
                except:
                    pass
    except Exception as e:
        print(f"清理测试文件时出错: {e}") 