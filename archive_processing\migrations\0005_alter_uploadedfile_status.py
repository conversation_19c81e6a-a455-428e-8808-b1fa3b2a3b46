# Generated by Django 5.1.11 on 2025-06-29 02:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_processing', '0004_remove_reportsplittingtask_archive_record_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='uploadedfile',
            name='status',
            field=models.CharField(choices=[('active', '活跃'), ('deleted', '已删除'), ('archived', '已归档'), ('purged', '已清除')], db_index=True, default='active', max_length=20, verbose_name='文件状态'),
        ),
    ]
