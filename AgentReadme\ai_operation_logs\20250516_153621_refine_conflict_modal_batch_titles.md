# Operation Document: Refine Batch Action Button Titles in ConflictResolutionModal

## 📋 Change Summary

**Purpose**: 修改 `ConflictResolutionModal.tsx` 中批量操作按钮（"全部设为更新"、"全部设为跳过"）的 `title` 属性，使其更准确地描述这些操作的实际作用范围（即针对所有符合条件的冲突记录，而非仅当前AG Grid中可见的行）。
**Scope**: `frontend/components/records/import/conflict-resolution-modal.tsx` 文件。
**Associated**: 对《remaining_excel_import_refactor_plan.md》文档中任务一.1 "冲突解决UI组件细化与功能完整性"的子项的初步细化。

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing Batch Action Logic and Titles

**Precondition**: `ConflictResolutionModal.tsx` 中存在批量操作按钮及其 `title` 属性。
**Operation**:

1. 审查批量操作按钮的 `onClick` 处理函数，确认它们调用 `updateAllActions` 时传递的参数。
2. 回顾 `updateAllActions` (通常在父组件 `ExcelImportWithConflictResolution.tsx` 或 `useExcelImportSession` Hook 中实现) 的逻辑，确定其是作用于AG Grid的可见行还是内部状态中的所有相关记录。
3. 发现当前批量操作逻辑作用于所有符合特定类型（如'update'）的记录，而非仅可见行。
4. 发现现有 `title` 属性包含"可见的"字样，与实际逻辑不完全一致。
**Postcondition**: 确认 `title` 属性需要修改以提高准确性。

### ✏️ OP-002: Modify `title` Attributes

**Precondition**: 已确定 `title` 属性需要修改。
**Operation**:

1. 修改"全部设为更新"按钮的 `title` 属性，将描述从"将所有可见的'可更新'记录的操作设为'更新'"调整为"将所有符合当前筛选条件的'可更新'记录的操作设为'更新'"。
2. 修改"全部设为跳过"按钮的 `title` 属性，进行类似的调整。
**Postcondition**: 按钮的 `title` 属性现在更准确地反映了其当前实现的作用范围。修复了之前可能存在的Linter错误（如重复属性）。

## 📝 Change Details

### CH-001: Update `title` attribute for batch action buttons

**File**: `frontend/components/records/import/conflict-resolution-modal.tsx`
**Before**:

```typescript
// <Button
//   title={disableBatchActions || !batchActionTargetType ? "仅对\'可更新\'或\'所有冲突\'视图下的\'可更新\'项生效" : "将所有可见的\'可更新\'记录的操作设为\'更新\'" }
// >
//   全部设为更新
// </Button>
// <Button
//   title={disableBatchActions || !batchActionTargetType ? "仅对\'可更新\'或\'所有冲突\'视图下的\'可更新\'项生效" : "将所有可见的\'可更新\'记录的操作设为\'跳过\'" }
// >
//   全部设为跳过
// </Button>
```

**After**: (CHANGE: [2025-05-16] 更新title以更准确地描述批量操作的范围)

```typescript
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => batchActionTargetType && updateAllActions(ConflictResolutionAction.UPDATE, batchActionTargetType)}
              disabled={disableBatchActions || !batchActionTargetType}
              title={disableBatchActions || !batchActionTargetType ? "仅对\'可更新\'或\'所有冲突\'视图下的\'可更新\'项生效" : "将所有符合当前筛选条件的\'可更新\'记录的操作设为\'更新\'"}
            >
              全部设为更新
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => batchActionTargetType && updateAllActions(ConflictResolutionAction.SKIP, batchActionTargetType)}
              disabled={disableBatchActions || !batchActionTargetType}
              title={disableBatchActions || !batchActionTargetType ? "仅对\'可更新\'或\'所有冲突\'视图下的\'可更新\'项生效" : "将所有符合当前筛选条件的\'可更新\'记录的操作设为\'跳过\'"}
            >
              全部设为跳过
            </Button>
```

**Rationale**: 确保UI元素的提示信息准确反映组件的实际行为，提升用户体验和理解。
**Potential Impact**: 用户将看到更准确的批量操作提示。批量操作的底层逻辑未改变，但关于其作用范围（可见行 vs. 所有符合筛选条件）的问题仍可作为后续细化的讨论点。

## ✅ Verification Results

**Method**: 代码审查。
**Results**: `ConflictResolutionModal.tsx` 中批量操作按钮的 `title` 属性已更新，更准确。Linter错误已解决。
**Problems**: 暂无。
**Solutions**: 暂无。
