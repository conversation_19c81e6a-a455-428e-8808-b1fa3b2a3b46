"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
// TODO: Replace with NextAuth imports after authentication implementation

import { ChevronDown, FilePlus, Pencil, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"

interface RecordActionsProps {
  recordId: string
}

export function RecordActions({ recordId }: RecordActionsProps) {
  // TODO: Replace with useSession() hook after NextAuth implementation
  const { toast } = useToast()
  const [showErrorCorrectionDialog, setShowErrorCorrectionDialog] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // 模拟记录状态 - TODO: Get from actual record data
  const isArchived = true

  const handleErrorCorrection = async () => {
    setIsProcessing(true)

    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/records/${recordId}/error-correction`, {
      //   method: 'POST',
      // });
      // const data = await response.json();

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setShowErrorCorrectionDialog(false)
      toast({
        title: "错误修正已启动",
        description: "您现在可以编辑记录以修正错误",
      })

      // TODO: Add navigation after NextAuth implementation
      // router.push(`/records/${recordId}/edit?mode=error-correction`);
    } catch (error) {
      toast({
        title: "操作失败",
        description: "启动错误修正时发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
        <Button asChild>
          <Link href={`/records/detail/${recordId}/edit`}>
            <Pencil className="mr-2 h-4 w-4" />
            编辑记录
          </Link>
        </Button>

        {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
        <Button variant="outline" asChild>
          <Link href={`/reports/create?record=${recordId}`}>
            <FilePlus className="mr-2 h-4 w-4" />
            创建发放单
          </Link>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              更多操作
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
            <DropdownMenuItem>导出记录</DropdownMenuItem>
            
            {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowErrorCorrectionDialog(true)}>
              <AlertTriangle className="mr-2 h-4 w-4 text-yellow-500" />
              错误修正
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog open={showErrorCorrectionDialog} onOpenChange={setShowErrorCorrectionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>启动错误修正</DialogTitle>
            <DialogDescription>
              错误修正功能用于修正操作过程中的人为错误或技术故障。此操作将被记录在变更历史中，但不会计入正式变更次数。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              请确认您要启动错误修正流程。启动后，您将能够直接编辑记录以修正错误。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowErrorCorrectionDialog(false)} disabled={isProcessing}>
              取消
            </Button>
            <Button onClick={handleErrorCorrection} disabled={isProcessing}>
              {isProcessing ? "处理中..." : "确认启动"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
