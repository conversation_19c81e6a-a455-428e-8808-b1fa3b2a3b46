---
description: 
globs: 
alwaysApply: false
---
# 档案处理模块指南

## 模块概述

档案处理模块(`archive_processing`)是系统的核心组件之一，负责处理档案文件(主要是PDF文档)、提取信息并生成结构化数据。该模块集成了PaddleOCR等OCR技术，用于从扫描件中提取文本内容。

## 核心组件

1. **服务(Services)**
   - 位于 `archive_processing/services/` 目录
   - 包含档案处理的主要业务逻辑

2. **数据传输对象(DTOs)**
   - 位于 `archive_processing/dto/` 目录
   - 定义了档案处理相关的数据结构

3. **工具函数(Utils)**
   - 位于 `archive_processing/utils/` 目录
   - 提供通用工具函数和辅助方法

## 主要功能

1. **档案文件解析**
   - PDF文档解析和文本提取
   - OCR识别和文本处理
   - 结构化数据提取

2. **档案分类**
   - 根据内容自动分类档案
   - 应用分类规则和算法

3. **数据验证与清洗**
   - 验证提取的数据完整性和准确性
   - 清洗和标准化提取的数据

4. **文件管理**
   - 管理档案文件的存储
   - 处理档案文件的版本控制

## 关键流程

1. **档案上传流程**
   - 用户上传档案文件
   - 系统验证文件格式和完整性
   - 将文件保存到临时目录

2. **档案处理流程**
   - 系统读取待处理档案
   - 应用OCR和文本提取
   - 解析结构化数据
   - 保存处理结果

3. **档案归档流程**
   - 验证处理结果
   - 将档案移至归档目录
   - 更新档案记录

## 技术栈

1. **OCR技术**
   - PaddleOCR - 用于中文文档OCR
   - Tesseract - 作为备选OCR引擎

2. **PDF处理**
   - PyMuPDF - PDF文档解析
   - pdf2image - PDF转图像处理

3. **数据处理**
   - pandas - 数据分析和处理
   - numpy - 数值计算
   - rapidfuzz - 模糊匹配

## 常见问题与解决方案

1. **OCR识别率低**
   - 确保图像清晰度
   - 调整预处理参数
   - 使用适当的OCR模型

2. **处理速度慢**
   - 考虑使用并行处理
   - 优化图像大小和质量
   - 检查资源使用情况

3. **内存占用高**
   - 分批处理大型文档
   - 及时释放不需要的资源
   - 优化图像处理管道

