import { PageTitle } from "@/components/page-title"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default async function UserPermissionsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href={`/users/detail/${id}`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">返回</span>
          </Link>
        </Button>
        <PageTitle title="用户权限管理" subtitle={`管理用户 ID: ${id} 的系统权限`} />
      </div>

      {/* 权限管理内容 */}
      <div className="rounded-md border p-4">
        <p>这里是用户权限管理界面的内容</p>
      </div>
    </div>
  )
}
