#!/bin/bash

# Celery Worker 启动脚本
# 用于在开发环境中启动不同类型的worker

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志目录
LOG_DIR="$PROJECT_ROOT/logs/celery"
mkdir -p "$LOG_DIR"

# PID文件目录
PID_DIR="$PROJECT_ROOT/run"
mkdir -p "$PID_DIR"

# 打印帮助信息
show_help() {
    echo "Celery Worker 管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [worker_type]  - 启动worker (all|default|pdf)"
    echo "  stop [worker_type]   - 停止worker (all|default|pdf)"
    echo "  restart [worker_type] - 重启worker (all|default|pdf)"
    echo "  status               - 查看worker状态"
    echo "  logs [worker_type]   - 查看worker日志"
    echo "  monitor              - 启动监控"
    echo ""
    echo "示例:"
    echo "  $0 start all         # 启动所有worker"
    echo "  $0 start pdf         # 只启动PDF处理worker"
    echo "  $0 stop default      # 停止默认worker"
    echo "  $0 logs pdf          # 查看PDF worker日志"
}

# 检查Redis是否运行
check_redis() {
    echo -e "${BLUE}检查Redis连接...${NC}"
    if ! redis-cli ping > /dev/null 2>&1; then
        echo -e "${RED}❌ Redis未运行，请先启动Redis${NC}"
        echo "提示: docker run -d --name redis -p 6379:6379 redis:latest"
        exit 1
    fi
    echo -e "${GREEN}✅ Redis连接正常${NC}"
}

# 启动单个worker
start_worker() {
    local worker_type=$1
    local queue=$2
    local concurrency=$3
    local hostname=$4
    
    local pid_file="$PID_DIR/celery_${worker_type}.pid"
    local log_file="$LOG_DIR/celery_${worker_type}.log"
    
    echo -e "${BLUE}启动 ${worker_type} worker...${NC}"
    
    # 检查是否已经运行
    if [ -f "$pid_file" ] && kill -0 $(cat "$pid_file") 2>/dev/null; then
        echo -e "${YELLOW}⚠️ ${worker_type} worker 已经在运行${NC}"
        return 0
    fi
    
    # 启动worker
    celery -A archive_flow_manager worker \
        --loglevel=info \
        --queues="$queue" \
        --concurrency="$concurrency" \
        --hostname="$hostname" \
        --pidfile="$pid_file" \
        --logfile="$log_file" \
        --detach
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ${worker_type} worker 启动成功${NC}"
        echo "   PID文件: $pid_file"
        echo "   日志文件: $log_file"
    else
        echo -e "${RED}❌ ${worker_type} worker 启动失败${NC}"
        return 1
    fi
}

# 停止单个worker
stop_worker() {
    local worker_type=$1
    local pid_file="$PID_DIR/celery_${worker_type}.pid"
    
    echo -e "${BLUE}停止 ${worker_type} worker...${NC}"
    
    if [ ! -f "$pid_file" ]; then
        echo -e "${YELLOW}⚠️ ${worker_type} worker 未运行${NC}"
        return 0
    fi
    
    local pid=$(cat "$pid_file")
    if kill -0 "$pid" 2>/dev/null; then
        kill -TERM "$pid"
        
        # 等待进程结束
        local count=0
        while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${YELLOW}强制终止 ${worker_type} worker...${NC}"
            kill -KILL "$pid"
        fi
        
        rm -f "$pid_file"
        echo -e "${GREEN}✅ ${worker_type} worker 已停止${NC}"
    else
        echo -e "${YELLOW}⚠️ ${worker_type} worker 进程不存在，清理PID文件${NC}"
        rm -f "$pid_file"
    fi
}

# 启动所有worker
start_all() {
    check_redis
    echo -e "${BLUE}启动所有Celery workers...${NC}"

    start_worker "default" "default" "4" "default@%h"
    start_worker "pdf" "pdf_processing" "2" "pdf@%h"

    echo -e "${GREEN}✅ 所有worker启动完成${NC}"
}

# 停止所有worker
stop_all() {
    echo -e "${BLUE}停止所有Celery workers...${NC}"

    stop_worker "default"
    stop_worker "pdf"

    echo -e "${GREEN}✅ 所有worker已停止${NC}"
}

# 查看worker状态
show_status() {
    echo -e "${BLUE}Worker 状态:${NC}"
    echo ""
    
    for worker_type in default pdf; do
        local pid_file="$PID_DIR/celery_${worker_type}.pid"
        
        if [ -f "$pid_file" ] && kill -0 $(cat "$pid_file") 2>/dev/null; then
            local pid=$(cat "$pid_file")
            echo -e "  ${worker_type}: ${GREEN}🟢 运行中${NC} (PID: $pid)"
        else
            echo -e "  ${worker_type}: ${RED}🔴 未运行${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}使用 'python scripts/celery_monitor.py status' 查看详细状态${NC}"
}

# 查看日志
show_logs() {
    local worker_type=$1
    local log_file="$LOG_DIR/celery_${worker_type}.log"
    
    if [ ! -f "$log_file" ]; then
        echo -e "${RED}❌ 日志文件不存在: $log_file${NC}"
        return 1
    fi
    
    echo -e "${BLUE}查看 ${worker_type} worker 日志 (按 Ctrl+C 退出):${NC}"
    tail -f "$log_file"
}

# 启动监控
start_monitor() {
    echo -e "${BLUE}启动Celery监控...${NC}"
    python scripts/celery_monitor.py status
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            case "${2:-all}" in
                all)
                    start_all
                    ;;
                default)
                    check_redis
                    start_worker "default" "default" "4" "default@%h"
                    ;;
                pdf)
                    check_redis
                    start_worker "pdf" "pdf_processing" "2" "pdf@%h"
                    ;;
                *)
                    echo -e "${RED}❌ 未知的worker类型: $2${NC}"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        stop)
            case "${2:-all}" in
                all)
                    stop_all
                    ;;
                default|pdf)
                    stop_worker "$2"
                    ;;
                *)
                    echo -e "${RED}❌ 未知的worker类型: $2${NC}"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        restart)
            case "${2:-all}" in
                all)
                    stop_all
                    sleep 2
                    start_all
                    ;;
                default|pdf)
                    stop_worker "$2"
                    sleep 1
                    case "$2" in
                        default)
                            check_redis
                            start_worker "default" "default" "4" "default@%h"
                            ;;
                        pdf)
                            check_redis
                            start_worker "pdf" "pdf_processing" "2" "pdf@%h"
                            ;;
                    esac
                    ;;
                *)
                    echo -e "${RED}❌ 未知的worker类型: $2${NC}"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        status)
            show_status
            ;;
        logs)
            if [ -z "$2" ]; then
                echo -e "${RED}❌ 请指定worker类型${NC}"
                show_help
                exit 1
            fi
            show_logs "$2"
            ;;
        monitor)
            start_monitor
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
