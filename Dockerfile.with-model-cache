# 多阶段构建示例 - 带模型缓存
# 第一阶段：下载模型
FROM python:3.11 as model-downloader

# 安装PaddleOCR
RUN pip install paddleocr

# 下载模型到指定目录
RUN python -c "
from paddleocr import PaddleOCR
import os
os.makedirs('/models', exist_ok=True)
ocr = PaddleOCR(lang='ch', ocr_version='PP-OCRv4')
print('Models downloaded successfully')
"

# 第二阶段：构建应用
FROM python:3.11

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8
ENV CUDA_VISIBLE_DEVICES=""
ENV PADDLEPADDLE_USE_GPU=0
ENV USE_CUDA=0

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    locales \
    libgl1-mesa-glx \
    libglib2.0-0 \
    poppler-utils \
    libmupdf-dev \
    build-essential \
    libffi-dev \
    libjpeg-dev \
    zlib1g-dev \
    ccache \
    && rm -rf /var/lib/apt/lists/*

# 配置UTF-8 locale支持
RUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen

# 安装 Python 依赖
COPY requirements.txt /app/
RUN python -m pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 从第一阶段复制预下载的模型
COPY --from=model-downloader /root/.paddleocr /root/.paddleocr

# 创建非root用户
ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g $GROUP_ID appuser && \
    useradd -ms /bin/bash -d /home/<USER>

# 复制项目代码
COPY . /app/

# 设置权限
RUN chown -R appuser:appuser /app && \
    chown -R appuser:appuser /home/<USER>
    cp -r /root/.paddleocr /home/<USER>/ && \
    chown -R appuser:appuser /home/<USER>/.paddleocr

USER appuser

# 默认启动命令
CMD ["celery", "-A", "archive_flow_manager", "worker", "--loglevel=info"]
