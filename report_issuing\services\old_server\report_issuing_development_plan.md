# 发放单模块开发计划文档

## 📋 文档信息

- **文档名称**: 发放单模块开发计划
- **版本**: v1.0
- **创建日期**: 2025-01-27
- **最后更新**: 2025-01-27
- **计划期间**: 2025-01-27 至 2025-02-15
- **负责人**: 开发团队
- **相关文档**:
  - [发放单模块事务层设计文档](../design/domain/ReportIssuing/transaction_service_layer_design.md)
  - [发放单模块服务层架构设计](../design/domain/ReportIssuing/service_layer_architecture_design.md)

## 🎯 项目目标

### 主要目标

1. **完整实现发放单模块**: 从数据层到视图层的完整实现
2. **确保数据一致性**: 跨模型操作的事务安全和数据完整性
3. **提供完整API**: 支持前端所有发放单相关功能
4. **集成业务逻辑**: 正确的发放类型判断和数量计算
5. **审计功能完备**: 完整的操作审计和追踪机制

### 核心价值

- 解决档案发放的复杂业务逻辑
- 提供可靠的事务管理机制
- 支持完整的发放单生命周期管理
- 为前端提供稳定的API服务

## 📊 当前状态评估

### ✅ 已完成

#### **1. 事务服务层 (IssueTransactionService)**

- ✅ 核心事务操作框架完成
- ✅ 创建类事务: `create_issue_form_with_records`, `add_items_to_existing_form`, `issue_form_with_records`
- ✅ 更新类事务: `update_form_status_with_audit`
- ✅ 同步类事务: `sync_archive_status_from_records`
- ✅ IssueFormItem vs IssueRecord 生命周期边界明确
- ✅ 状态转换验证逻辑完善
- ✅ 审计集成接口预留

#### **2. 模型层基础**

- ✅ IssueForm, IssueFormItem, IssueRecord 模型定义
- ✅ 基础的业务规则验证方法

#### **3. 查询服务层**

- ✅ ArchiveQueryService 档案查询功能

### ⚠️ 部分完成

#### **1. 业务服务层 (IssueBusinessService)**

- ✅ 基础框架和接口定义
- ⚠️ 需要与事务层深度集成
- ⚠️ 发放类型和数量计算逻辑需要完善

### ❌ 待实现

#### **1. 数据服务层**

- ❌ IssueFormService: 发放单数据操作
- ❌ IssueRecordService: 发放记录数据操作
- ❌ ArchiveRecordService: 档案记录数据操作（可能已存在）

#### **2. 审计服务层**

- ❌ AuditService: 统一审计管理
- ❌ 操作日志记录机制

#### **3. 视图层 (API接口)**

- ❌ 发放单CRUD接口
- ❌ 发放操作接口
- ❌ 查询和列表接口

#### **4. 前端集成**

- ❌ 前端页面对接
- ❌ 用户界面优化

## 🚀 开发阶段规划

### 📅 阶段一：数据服务层实现 (2025-01-27 ~ 2025-02-02)

#### **目标**: 为事务层提供稳定的数据操作支持

#### **P1 优先级任务**

##### **1.1 IssueFormService 实现**

- **预计工期**: 2天
- **主要功能**:

  ```python
  class IssueFormService:
      def create_issue_form(data: Dict) -> IssueForm
      def get_issue_form_by_id(form_id: int) -> Optional[IssueForm]
      def update_issue_form(form_id: int, data: Dict) -> IssueForm
      def delete_issue_form(form_id: int) -> bool
      def get_issue_forms_by_status(status: str) -> List[IssueForm]
      
      # 发放条目操作
      def create_issue_form_item(data: Dict) -> IssueFormItem
      def get_issue_form_items_by_form_id(form_id: int) -> List[IssueFormItem]
      def update_issue_form_item(item_id: int, data: Dict) -> IssueFormItem
      def delete_issue_form_item(item_id: int) -> bool
  ```

##### **1.2 IssueRecordService 实现**

- **预计工期**: 1.5天
- **主要功能**:

  ```python
  class IssueRecordService:
      def create_issue_record(data: Dict) -> IssueRecord
      def get_issue_record_by_id(record_id: int) -> Optional[IssueRecord]
      def get_issue_records_by_archive_id(archive_id: int) -> List[IssueRecord]
      def get_issue_records_by_form_id(form_id: int) -> List[IssueRecord]
      def update_issue_record(record_id: int, data: Dict) -> IssueRecord
      def soft_delete_issue_record(record_id: int, reason: str) -> bool
  ```

##### **1.3 ArchiveRecordService 完善**

- **预计工期**: 1天
- **主要任务**:
  - 检查现有实现
  - 补充事务层所需的方法
  - **确保查询时能有效获取并利用 `ArchiveRecord` 模型自身存储的发放相关字段（如 `total_issue_copies` 等），为业务层提供全面的数据基础。**
  - 确保与发放相关的字段操作完备

#### **验收标准**

- [ ] 所有数据服务类通过单元测试
- [ ] 事务服务层可以正常调用数据服务方法
- [ ] 数据操作的异常处理完善
- [ ] 符合项目编码规范

### 📅 阶段二：业务服务层集成 (2025-02-03 ~ 2025-02-07)

#### **目标**: 完善业务逻辑计算，与事务层深度集成

#### **P1 优先级任务**

##### **2.1 业务计算逻辑完善**

- **预计工期**: 2天
- **主要任务**:
  - 完善 `calculate_issue_quantity` 方法
  - 完善 `_determine_issue_type` 方法
  - 实现复杂业务规则验证
  - 与现有档案状态计算逻辑集成

##### **2.2 事务层业务集成**

- **预计工期**: 2天
- **主要任务**:
  - 集成业务层计算到事务层验证
  - 实现 `_validate_issue_plan_with_business_logic`
  - 实现 `_validate_archive_for_final_issue_with_business_calculation`
  - 完善档案状态同步逻辑

##### **2.3 业务规则测试**

- **预计工期**: 1天
- **主要任务**:
  - 编写复杂业务场景测试
  - 验证发放类型判断逻辑
  - 验证发放数量计算逻辑

#### **验收标准**

- [ ] 业务逻辑计算准确性验证通过
- [ ] 事务层与业务层集成测试通过
- [ ] 复杂业务场景覆盖测试通过
- [ ] 性能测试满足要求

### 📅 阶段三：审计服务层实现 (2025-02-08 ~ 2025-02-10)

#### **目标**: 完善审计功能，确保操作可追溯

#### **P2 优先级任务**

##### **3.1 AuditService 实现**

- **预计工期**: 2天
- **主要功能**:

  ```python
  class AuditService:
      def start_audit_batch(operation_type: str) -> str
      def record_operation_audit(batch_id: str, operation_data: Dict) -> None
      def record_data_change_audit(batch_id: str, changes: Dict) -> None
      def complete_audit_batch(batch_id: str, result: str) -> None
      def get_audit_trail(entity_type: str, entity_id: int) -> List[Dict]
  ```

##### **3.2 事务层审计集成**

- **预计工期**: 1天
- **主要任务**:
  - 完善事务层中所有TODO审计记录
  - 实现完整的审计链路
  - 确保异常情况下的审计记录

#### **验收标准**

- [ ] 所有事务操作都有完整的审计记录
- [ ] 审计数据准确完整
- [ ] 审计查询功能正常工作
- [ ] 异常处理中的审计记录完善

### 📅 阶段四：API视图层实现 (2025-02-11 ~ 2025-02-14)

#### **目标**: 提供完整的REST API接口

#### **P1 优先级任务**

##### **4.1 发放单管理API**

- **预计工期**: 2天
- **主要接口**:
  - `POST /api/issue-forms/` - 创建发放单
  - `GET /api/issue-forms/{id}/` - 获取发放单详情
  - `PUT /api/issue-forms/{id}/` - 更新发放单
  - `POST /api/issue-forms/{id}/lock/` - 锁定发放单
  - `POST /api/issue-forms/{id}/issue/` - 执行发放操作
  - `DELETE /api/issue-forms/{id}/` - 删除发放单

##### **4.2 发放条目管理API**

- **预计工期**: 1天
- **主要接口**:
  - `POST /api/issue-forms/{form_id}/items/` - 添加发放条目
  - `PUT /api/issue-form-items/{item_id}/`