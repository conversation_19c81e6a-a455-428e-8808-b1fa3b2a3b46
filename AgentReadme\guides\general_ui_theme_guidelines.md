# 全局UI规范与样式指南

本文档定义了项目级别的通用UI规范和样式指南，用于确保整个应用的视觉一致性。

## 布局基础参数

### 1. 容器边距

- **外层容器**: `p-6` (1.5rem/24px) - 来自 client-layout.tsx
- **页面组件间距**: `space-y-6` - 各个主要页面组件间距
- **页面内容元素间距**: `space-y-8` - 仪表盘内部内容组件间距
- **滚动内容右边距**: `pr-4` - 为滚动条留出空间

### 2. 网格布局参数

- **卡片网格**: `grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4` - 四列布局
- **内容/侧边栏**: `grid gap-6 grid-cols-1 lg:grid-cols-3` - 三列布局(1+2)
- **网格间距**: `gap-6` - 统一的网格项间距

## 标题规范

### 1. 页面主标题 (PageTitle)

- **容器**: `flex-none bg-background border-b mb-6` - 固定标题区带下边框，下方间距6
- **标题文本**: `text-2xl font-bold tracking-tight` - 粗体大标题
- **副标题文本**: `text-sm text-muted-foreground` - 小字浅色副标题
- **标题间距**: `space-y-1` - 标题与副标题间距

### 2. 卡片标题

- **卡片标题**: `text-lg font-semibold text-gray-800` - 卡片内主标题
- **卡片副标题**: `text-sm text-muted-foreground` - 卡片内副标题

## 卡片参数

### 1. 基础卡片

- **普通卡片**: `<Card>` - 使用默认卡片组件样式
- **带渐变卡片**: `<Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none shadow-md">` - 特殊突出卡片

### 2. 卡片内部间距

- **卡片头部**: `<CardHeader>` - 默认内边距
- **卡片内容**: `<CardContent>` - 默认内边距
- **卡片底部**: `<CardFooter>` - 默认内边距

## 常见UI组件边距规范

1. **表单组件间距**:
   - 表单项间距: `space-y-4`
   - 表单组间距: `space-y-6`
   - 输入框内边距: 默认

2. **列表项间距**:
   - 紧凑列表: `space-y-1`
   - 标准列表: `space-y-2`
   - 宽松列表: `space-y-4`

3. **按钮间距**:
   - 按钮组: `space-x-2`
   - 按钮与表单间距: `mt-4`

## 响应式布局断点

- **移动端**: 默认样式
- **平板**: `md:` 前缀 (768px+)
- **桌面**: `lg:` 前缀 (1024px+)
- **大屏幕**: `xl:` 前缀 (1280px+)

常见响应式布局模式:

- 单列 → 双列 → 四列: `grid-cols-1 md:grid-cols-2 lg:grid-cols-4`
- 单列 → 三列(1+2): `grid-cols-1 lg:grid-cols-3` 配合 `lg:col-span-1` 和 `lg:col-span-2`

## 统一视觉风格参考

### 颜色使用

- **主要文本**: 默认前景色
- **次要文本**: `text-muted-foreground`
- **成功/上升状态**: `text-green-600`
- **错误/下降状态**: `text-red-600`
- **警告状态**: `text-amber-600`、`text-orange-600`
- **信息状态**: `text-blue-600`

### 图标使用

- **统一图标库**: Lucide Icons
- **图标大小**:
  - 导航/主要操作: `h-5 w-5`
  - 次要/内联图标: `h-4 w-4`
- **图标颜色**: 与文本颜色一致或使用对应状态颜色

## 与页面布局组件的关系

本文档定义的是全局通用的UI参数和样式规范，而具体的页面布局实现请参考：

1. **`PageLayout` 组件指南**: `AgentReadme/guides/page_layout_usage.md` - 提供关于页面布局组件的详细使用说明、属性和迁移指南。

2. **滚动解决方案指南**: `AgentReadme/guides/layout_scrolling_solution.md` - 提供关于页面滚动实现的详细解决方案和最佳实践。

通过遵循以上规范，可确保所有页面具有一致的视觉层次和间距，提升整体用户体验。
