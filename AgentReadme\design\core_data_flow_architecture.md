# 核心数据流架构

## 概述

本文档描述了档案管理系统中使用的核心数据流架构模式。这种架构模式适用于系统中的多种业务功能，包括档案记录导入、报告签发以及档案记录管理等。核心数据流架构通过标准化的数据处理流程和统一的接口设计，实现了系统的可扩展性、一致性和可追溯性。

## 核心数据流模型

档案管理系统采用了一种五阶段数据流模型：

```flow
数据源表 → 源详情 → 操作批次 → 档案记录 → 操作日志
```

具体对应为：

```flow
数据源表(SourceTable) → 源详情(SourceDetail) → 操作批次(OperationBatch) → 档案记录(ArchiveRecord) → 操作日志(OperationLog)
```

### 各阶段说明

1. **数据源表(SourceTable)**
   - 存储业务数据的原始表格，如Excel导入表、更改单表等
   - 包含基本的元数据（创建人、创建时间等）
   - 可以有多种不同类型的数据源表，但通过接口统一处理

2. **源详情(SourceDetail)**
   - 存储数据源中每一条记录或变更的详细内容
   - 与数据源表是一对多关系
   - 包含具体的字段值或变更内容

3. **操作批次(OperationBatch)**
   - 代表一次完整的业务操作过程
   - 关联数据源和执行结果
   - 记录操作的元数据（执行人、执行时间、状态等）
   - 支持回滚、重试等操作

4. **档案记录(ArchiveRecord)**
   - 系统核心数据实体
   - 存储档案的所有业务字段和元数据
   - 记录来源、变更次数等信息

5. **操作日志(OperationLog)**
   - 记录每次对档案记录的操作详情
   - 包含操作前后的数据状态
   - 支持数据溯源和审计

## 实现模型

核心数据流架构的实现基于以下原则：

1. **数据源表独立**：不同的数据源采用独立的表结构，以适应不同业务场景的特殊需求

2. **服务层统一接口**：通过`ChangeSourceInterface`等接口，统一不同数据源的处理流程

3. **批次处理**：所有数据操作都通过批次进行管理，确保事务完整性和可追溯性

4. **日志完整性**：任何对档案记录的修改都会生成完整的操作日志

### 典型实现示例

```python
class ChangeSourceInterface:
    """变更来源接口，统一不同来源的变更处理流程"""
    
    def get_source_type(self):
        """获取来源类型"""
        raise NotImplementedError
    
    def get_source_id(self):
        """获取来源ID"""
        raise NotImplementedError
    
    def get_change_details(self):
        """获取变更详情列表"""
        raise NotImplementedError
    
    def get_executor(self):
        """获取执行人"""
        raise NotImplementedError
    
    def get_execution_context(self):
        """获取执行上下文信息"""
        raise NotImplementedError
    
    def update_status(self, status, batch_id=None):
        """更新来源状态"""
        raise NotImplementedError
```

## 数据流和处理顺序

核心数据流架构的处理过程一般包括以下几个阶段：

1. **数据采集阶段**：
   - 用户提交数据源（如Excel文件导入、创建更改单等）
   - 系统解析并存储到相应的数据源表和源详情表

2. **数据验证阶段**：
   - 验证数据的有效性和完整性
   - 检查业务规则合规性
   - 准备执行环境

3. **执行阶段**：
   - 创建操作批次记录
   - 根据源详情执行实际操作（如创建/更新档案记录）
   - 生成操作日志
   - 更新相关状态

4. **日志记录阶段**：
   - 记录详细的操作过程和结果
   - 确保数据变更可追溯

## 业务功能应用

### 档案记录导入功能

在档案记录导入功能中，核心数据流的应用如下：

```flow
ExcelImport → ExcelRecordDetail → ImportBatch → ArchiveRecord → ImportLog
```

其中：

- `ExcelImport`：存储导入的Excel文件信息
- `ExcelRecordDetail`：存储Excel中每行数据的详细内容
- `ImportBatch`：管理一次完整的导入过程
- `ArchiveRecord`：最终生成的档案记录
- `ImportLog`：记录导入过程中的每个操作

### 档案记录变更功能

在档案记录变更功能中，核心数据流的应用如下：

```flow
ChangeOrder → ChangeDetail → ChangeLogBatch → ArchiveRecord → RecordChangeLog
```

其中：

- `ChangeOrder`：存储变更申请信息
- `ChangeDetail`：存储每个变更的详细内容
- `ChangeLogBatch`：管理一次完整的变更执行过程
- `ArchiveRecord`：被更新的档案记录
- `RecordChangeLog`：记录变更前后的状态

### 报告签发功能

在报告签发功能中，核心数据流的应用如下：

```flow
ReportRequest → RequestDetail → ReportBatch → Report → ReportOperationLog
```

其中：

- `ReportRequest`：存储报告申请信息
- `RequestDetail`：存储申请的详细内容
- `ReportBatch`：管理一次完整的报告生成过程
- `Report`：生成的报告实体
- `ReportOperationLog`：记录报告生成和操作过程

## 扩展指南

### 添加新的数据源类型

要添加新的数据源类型，需要执行以下步骤：

1. 创建数据源表和源详情表
2. 实现`ChangeSourceInterface`接口
3. 在服务层添加对新数据源的支持

示例：

```python
class NewSourceType(models.Model):
    """新数据源类型"""
    # 实现字段...

class NewSourceDetail(models.Model):
    """新数据源详情"""
    source = models.ForeignKey(NewSourceType, on_delete=models.CASCADE)
    # 实现字段...

class NewSourceTypeAdapter(ChangeSourceInterface):
    """新数据源适配器"""
    def __init__(self, source):
        self.source = source
    
    def get_source_type(self):
        return 'new_source_type'
    
    def get_source_id(self):
        return str(self.source.id)
    
    # 实现其他接口方法...
```

## 核心数据流架构的优势与价值

1. **统一处理流程**：不同来源的数据通过统一的接口和流程处理，降低系统复杂度

2. **完整的可追溯性**：每次操作都有完整的日志记录，支持数据审计和问题追踪

3. **良好的版本控制**：通过操作日志保存变更前后的状态，实现数据版本控制

4. **职责明确分离**：每个组件职责单一，易于维护和扩展

5. **灵活的业务扩展**：可以轻松添加新的数据源类型和处理流程，同时保持核心架构不变

6. **事务完整性保证**：通过批次处理机制，确保操作的原子性和一致性
