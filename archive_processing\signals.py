# CHANGE: [2024-03-29] 注释掉信号处理器，避免与视图中的任务创建逻辑冲突 #AFM-7
# # 定义信号处理器
# @receiver(post_save, sender=UploadedFile)
# def trigger_processing_after_upload(sender, instance, created, **kwargs):
#     """
#     文件上传后自动触发处理任务 (已废弃，逻辑移至 View)
#     """
#     if created:
#         # 创建处理任务
#         from .services.task_service import TaskService
#         # TaskService.create_task(instance.file_id, 'pdf_processing')
#         # 假设 TaskService.create_task 需要 UploadedFile 实例
#         TaskService.create_task(uploaded_file=instance, params=None, user_id=None) # 需要确认调用签名
