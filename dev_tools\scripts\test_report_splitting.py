"""
测试报告分割功能的Django管理命令
"""
from django.core.management.base import BaseCommand
from django.conf import settings
import logging
import os

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '测试报告分割功能是否正常工作'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细输出',
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        
        self.stdout.write("🧪 报告分割功能测试")
        self.stdout.write("=" * 60)
        
        test_results = []
        
        # 运行所有测试
        test_results.append(("服务初始化", self.test_service_initialization(verbose)))
        test_results.append(("模板加载", self.test_template_loading(verbose)))
        test_results.append(("模拟PDF处理", self.test_mock_pdf_processing(verbose)))
        test_results.append(("集成工作流程", self.test_integration_workflow(verbose)))
        
        # 汇总结果
        self.stdout.write("\n📊 测试结果汇总")
        self.stdout.write("=" * 60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            self.stdout.write(f"{status} {test_name}")
            if result:
                passed += 1
        
        self.stdout.write(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            self.stdout.write(self.style.SUCCESS("🎉 所有测试通过！报告分割功能配置正确。"))
        else:
            self.stdout.write(self.style.WARNING("⚠️ 部分测试失败，请检查配置。"))

    def test_service_initialization(self, verbose=False):
        """测试服务初始化"""
        self.stdout.write("\n🔧 测试1: 服务初始化")
        self.stdout.write("=" * 50)
        
        try:
            from archive_processing.services.report_recognition_service import ReportRecognitionService
            from archive_processing.services.report_splitting_service import ReportSplittingService
            
            # 测试ReportRecognitionService初始化
            recognition_service = ReportRecognitionService()
            self.stdout.write("✅ ReportRecognitionService初始化成功")
            if verbose:
                self.stdout.write(f"   - 模板路径: {recognition_service.template_base_path}")
                self.stdout.write(f"   - 加载模板数量: {len(recognition_service.ma_templates)}")
            
            # 测试ReportSplittingService初始化
            splitting_service = ReportSplittingService()
            self.stdout.write("✅ ReportSplittingService初始化成功")
            
            return True
        except Exception as e:
            self.stdout.write(f"❌ 服务初始化失败: {e}")
            return False

    def test_template_loading(self, verbose=False):
        """测试模板加载功能"""
        self.stdout.write("\n🔧 测试2: CMA章模板加载")
        self.stdout.write("=" * 50)
        
        try:
            from archive_processing.services.report_recognition_service import ReportRecognitionService
            
            recognition_service = ReportRecognitionService()
            
            if verbose:
                self.stdout.write(f"📂 模板目录: {recognition_service.template_base_path}")
                self.stdout.write(f"📊 已加载模板: {len(recognition_service.ma_templates)}")
                
                for i, template in enumerate(recognition_service.ma_templates, 1):
                    template_path = template.get('path', f'模板{i}')
                    template_name = os.path.basename(template_path)
                    features = len(template.get('keypoints', []))
                    self.stdout.write(f"   {i}. {template_name} (特征点: {features})")
            
            if len(recognition_service.ma_templates) > 0:
                self.stdout.write("✅ 模板加载成功")
                return True
            else:
                self.stdout.write("⚠️ 未加载到任何模板")
                return False
                
        except Exception as e:
            self.stdout.write(f"❌ 模板加载测试失败: {e}")
            return False

    def test_mock_pdf_processing(self, verbose=False):
        """测试模拟PDF处理"""
        self.stdout.write("\n🔧 测试3: 模拟PDF处理流程")
        self.stdout.write("=" * 50)
        
        try:
            from archive_processing.services.report_recognition_service import ReportRecognitionService
            from archive_processing.services.report_splitting_service import ReportSplittingService
            
            recognition_service = ReportRecognitionService()
            splitting_service = ReportSplittingService()
            
            if verbose:
                # 模拟输入
                mock_pdf_path = "/mock/test.pdf"
                mock_page_count = 10
                
                self.stdout.write(f"📄 模拟PDF路径: {mock_pdf_path}")
                self.stdout.write(f"📊 模拟页数: {mock_page_count}")
            
            # 测试页面范围识别（模拟）
            self.stdout.write("🔍 测试报告范围识别...")
            try:
                # 检查方法是否存在
                if hasattr(recognition_service, 'identify_report_ranges'):
                    self.stdout.write("   - identify_report_ranges 方法存在")
                    self.stdout.write("✅ 报告范围识别接口正常")
                else:
                    self.stdout.write("❌ identify_report_ranges 方法不存在")
                    return False
            except Exception as e:
                self.stdout.write(f"❌ 报告范围识别接口异常: {e}")
                return False
            
            # 测试页面分割（模拟）
            self.stdout.write("📄 测试PDF分割...")
            try:
                if hasattr(splitting_service, 'split_report_by_page_range'):
                    if verbose:
                        # 模拟页面范围
                        mock_ranges = [(1, 5), (6, 10)]
                        self.stdout.write(f"   - 模拟报告范围: {mock_ranges}")
                    self.stdout.write("   - split_report_by_page_range 方法存在")
                    self.stdout.write("✅ PDF分割接口正常")
                else:
                    self.stdout.write("❌ split_report_by_page_range 方法不存在")
                    return False
            except Exception as e:
                self.stdout.write(f"❌ PDF分割接口异常: {e}")
                return False
            
            return True
            
        except Exception as e:
            self.stdout.write(f"❌ 模拟PDF处理测试失败: {e}")
            return False

    def test_integration_workflow(self, verbose=False):
        """测试集成工作流程"""
        self.stdout.write("\n🔧 测试4: 集成工作流程")
        self.stdout.write("=" * 50)
        
        try:
            # 导入相关模块
            from archive_processing.tasks.core_tasks import validate_all_parts_processable
            
            self.stdout.write("📦 测试tasks模块导入...")
            self.stdout.write("✅ validate_all_parts_processable 函数可导入")
            
            # 测试文件存储服务
            from archive_processing.services.file_storage_service import FileStorageService
            storage_service = FileStorageService()
            self.stdout.write("✅ FileStorageService 初始化成功")
            
            # 测试记录更新服务
            from archive_processing.services.record_update_service import update_archive_record
            self.stdout.write("✅ update_archive_record 函数可导入")
            
            self.stdout.write("✅ 所有集成组件导入成功")
            
            return True
            
        except Exception as e:
            self.stdout.write(f"❌ 集成工作流程测试失败: {e}")
            return False 