# 重复导入委托编号问题分析与解决方案

## 问题描述

用户报告在Excel导入功能中，存在导入了相同委托编号的条目情况。即使在数据库中已有相同委托编号的记录，系统仍然创建了新记录而非更新现有记录。

## 问题分析

通过查看代码，发现以下可能导致问题的原因：

1. **数据库缺少唯一性约束**：
   - `ArchiveRecord` 模型中 `commission_number` 字段没有设置唯一性约束

2. **冲突检测逻辑不一致**：
   - 冲突检测阶段(`ExcelConflictAnalyzer`)和实际导入阶段(`ExcelImportService`)使用不完全一致的查询方法
   - 可能导致一个阶段识别为已存在记录，另一个阶段却识别为新记录

3. **导入策略意图与实现不一致**：
   - 前端允许用户选择 CREATE、UPDATE、SKIP 三种操作
   - 但后端代码显示 CREATE 操作已被废弃，即使用户选择了 CREATE 也会使用 `SMART_UPDATE` 策略

4. **查找方法可能不充分规范化**：
   - 不同地方的委托编号处理方式可能不一致(处理空格、大小写等)

5. **事务处理中缺少额外检查**：
   - 在批量导入过程中缺少对重复委托编号的集中检查

## 建议解决方案

### 短期解决方案

1. **加强导入前检查**：

   ```python
   def confirm_import(self, session_id, resolutions, user):
       # 获取冲突记录
       conflict_records = self._get_conflict_records(session_id)
       
       # 预检查与数据库已有记录的重复
       commission_numbers = [r.get('commission_number') for r in conflict_records]
       existing_numbers = set(ArchiveRecord.objects.filter(
           commission_number__in=commission_numbers
       ).values_list('commission_number', flat=True))
       
       # 检查实际重复情况
       for record in conflict_records:
           num = record.get('commission_number')
           if num in existing_numbers and record.get('conflict_type') == 'new':
               logger.warning(f"检测到冲突类型错误: {num} 被标记为 'new' 但实际在数据库中已存在")
               # 修正冲突类型
               record['conflict_type'] = 'update'
               
       # 继续执行导入...
   ```

2. **统一查找方法**：

   ```python
   # 创建辅助方法，然后替换原有的查找调用
   def normalize_commission_number(number):
       """标准化委托编号格式"""
       if number is None:
           return None
       return str(number).strip()  # 可以考虑增加 .upper() 如果需要忽略大小写
   ```

3. **改进前端交互**：
   - 更新冲突解决模态框，移除CREATE选项或明确说明其实际行为会与UPDATE相同

### 长期解决方案

1. **添加数据库唯一约束**：
   - 创建数据库迁移添加唯一约束(需先处理现有重复数据)

   ```python
   class Migration(migrations.Migration):
       operations = [
           migrations.AlterField(
               model_name='archiverecord',
               name='commission_number',
               field=models.CharField(max_length=2000, unique=True, verbose_name='委托编号'),
           ),
       ]
   ```

2. **完整重构导入流程**：
   - 统一冲突检测和导入逻辑
   - 使用单一的规范化方法处理所有委托编号

3. **增强日志和监控**：
   - 添加详细日志记录每个导入阶段的关键操作
   - 考虑为重要操作添加事件通知机制

## 风险评估

1. **添加唯一约束**：
   - 风险: 现有数据中可能存在重复的委托编号，直接添加约束会失败
   - 缓解: 在添加约束前，需要识别并处理现有重复数据

2. **行为变更**：
   - 风险: 用户可能已依赖当前行为，包括创建具有相同委托编号的多条记录
   - 缓解: 提前沟通变更，考虑暂时保留但弃用旧行为，添加警告
