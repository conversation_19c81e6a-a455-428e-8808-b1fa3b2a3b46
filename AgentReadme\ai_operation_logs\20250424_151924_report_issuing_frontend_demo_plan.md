# Operation Document: 报告发放功能前端演示实现计划

## 📋 Change Summary

**Purpose**: 设计并实现报告发放功能的前端演示界面，以便进行端到端测试
**Scope**: 前端演示应用与后端API交互
**Associated**: #AFM-Demo-UI-Update, #AFM-Report-Issuing-Demo

## 🔧 Operation Steps

### 📊 OP-001: 分析报告发放系统现状

**Precondition**: 已完成报告发放系统后端API开发
**Operation**:

1. 分析现有的模型结构 (`IssueForm`, `IssueFormItem`, `IssueRecord`, `IssueRecordHistory`)
2. 确认API端点和功能 (`/api/report-issuing/issue-forms/`, `/api/report-issuing/issue-records/`)
3. 分析集成测试用例，了解API使用方式
**Postcondition**: 确认报告发放系统后端功能已实现并可用于前端演示

### ✏️ OP-002: 设计Streamlit前端演示界面

**Precondition**: 了解报告发放系统功能和API
**Operation**:

1. 设计基于标签页的UI布局，分为三个主要功能区：
   - 发放单管理(创建与列表)
   - 发放单详情操作(添加条目、状态流转)
   - 发放记录查询与历史
2. 设计每个页面的具体组件和交互流程
3. 确定与后端API的集成点
**Postcondition**: 完成详细的UI设计方案，准备开始实现

### ✏️ OP-003: 实现前端演示应用

**Precondition**: 完成UI设计方案
**Operation**:

1. 在`-frontend`目录下创建`issue_demo.py`文件
2. 实现以下功能模块：
   - 发放单创建表单
   - 发放单列表展示
   - 发放单详情与操作(添加条目、锁定、确认、归档、删除)
   - 发放记录查询与历史查看
3. 使用requests库与后端API交互
4. 添加错误处理和用户反馈机制
**Postcondition**: 完成功能齐全的报告发放前端演示应用

### 🧪 OP-004: 端到端测试

**Precondition**: 完成前端演示应用开发
**Operation**:

1. 启动后端服务和前端应用
2. 测试创建发放单并添加条目
3. 测试发放单状态流转(锁定→确认→归档)
4. 测试发放记录查询和历史记录查看
5. 测试边缘情况和错误处理
**Postcondition**: 确认前端演示应用能够正常工作，并与后端API正确交互

## 📝 Change Details

### CH-001: 创建Streamlit前端演示应用设计

**File**: `-frontend/issue_demo.py` (新文件)
**内容概要**:

```python
import streamlit as st
import requests
import os
import pandas as pd
from datetime import datetime

# API基础URL配置
DJANGO_API_BASE_URL = os.getenv('BACKEND_API_URL', 'http://web:8000/api')
ISSUE_FORM_URL = f"{DJANGO_API_BASE_URL}/report-issuing/issue-forms/"
ISSUE_RECORD_URL = f"{DJANGO_API_BASE_URL}/report-issuing/issue-records/"

# 页面配置与标签页设计
st.set_page_config(page_title="报告发放系统演示", layout="wide")
st.title("报告发放系统 - 演示界面")
tab1, tab2, tab3 = st.tabs(["发放单管理", "发放单详情操作", "发放记录查询"])

# Tab 1: 发放单管理
with tab1:
    # 左侧: 创建发放单表单
    # 右侧: 显示发放单列表
    
# Tab 2: 发放单详情操作
with tab2:
    # 选择发放单
    # 显示基本信息与条目
    # 状态流转操作按钮
    
# Tab 3: 发放记录查询
with tab3:
    # 查询条件
    # 记录列表
    # 详情与历史查看
```

**Rationale**: 使用Streamlit创建简单但功能完整的前端演示，便于进行端到端测试
**Potential Impact**: 为系统提供完整的前端演示能力，方便测试和展示发放功能

## ✅ Verification Results

**Method**: 计划阶段，尚未进行实际验证
**Results**: 待实施后验证
**Problems**:

- 前端与后端API交互可能需要处理认证问题
- 需要确保后端API返回的数据格式与前端预期一致
**Solutions**:
- 如有必要，临时调整后端API认证方式以便于演示
- 在实现过程中灵活调整前端数据处理逻辑，适应后端返回格式

## 🧪 端到端测试计划

以下是完整的端到端测试流程，用于验证报告发放功能的前端界面与后端API交互的正确性。

### 测试环境准备

1. **启动后端服务**
   - 运行 Django 开发服务器：`python manage.py runserver 0.0.0.0:8000`
   - 确保 Celery worker 正在运行（如需异步任务支持）

2. **启动前端应用**
   - 进入前端目录：`cd -frontend`
   - 运行 Streamlit 应用：`streamlit run issue_demo.py`

### 测试场景1：发放单完整生命周期

#### 1.1 创建发放单

- 在"发放单管理"标签页，填写完整的发放单信息（领取人、领取单位等）
- 点击"创建发放单"按钮提交
- **预期结果**：显示成功消息，新发放单出现在列表中，状态为"草稿"

#### 1.2 添加档案条目

- 在"发放单详情与操作"标签页，选择刚创建的发放单
- 搜索并添加至少两条档案记录
- **预期结果**：每次添加后，条目列表更新，显示添加的档案信息

#### 1.3 发放单状态流转

- 完成添加条目后，测试发放单状态流转：
  - 点击"锁定发放单"按钮
  - **预期结果**：发放单状态变为"锁定"，条目不可再编辑
  - 点击"确认发放单"按钮
  - **预期结果**：发放单状态变为"已确认"
  - 点击"归档发放单"按钮
  - **预期结果**：发放单状态变为"已归档"

#### 1.4 查询发放记录

- 切换到"发放记录查询"标签页
- 使用发放单中的领取人姓名作为查询条件
- **预期结果**：查询结果中应包含刚才归档的发放单中的记录

### 测试场景2：异常处理与边缘情况

#### 2.1 表单验证

- 尝试提交未填写必填项的发放单
- **预期结果**：出现错误提示，要求填写必填项

#### 2.2 空条目发放单

- 创建发放单但不添加任何条目
- 尝试锁定发放单
- **预期结果**：应提示发放单需要至少一个条目才能锁定

#### 2.3 权限检查

- 尝试对已锁定的发放单添加条目
- **预期结果**：添加条目按钮不可用或操作被拒绝

#### 2.4 删除操作

- 创建发放单，然后尝试删除
- **预期结果**：要求输入删除原因，确认后发放单被标记为删除

### 测试场景3：系统集成验证

#### 3.1 数据一致性

- 创建发放单并完成状态流转
- 从数据库查询相关记录，确认所有状态变化都被正确记录

#### 3.2 性能检查

- 快速创建多个发放单（如5个）
- 给每个发放单添加多个条目（每个至少5个）
- 检查应用响应时间和数据加载性能

### 测试结果记录

建立一个简单的测试结果记录表，包含以下字段：

- 测试场景/步骤
- 测试日期时间
- 测试结果（通过/失败）
- 观察到的问题
- 解决方案或跟进行动

### 测试回归计划

在每次对发放功能进行代码修改后，应至少执行测试场景1的完整流程，确保核心功能不受影响。
