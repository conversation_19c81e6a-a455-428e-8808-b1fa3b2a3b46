"""
发放记录服务测试模块

注意：为简化测试，用户权限处理已被简化为使用超级用户权限。
在实际部署时，应实现完整的权限检查机制。
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction

from archive_records.models import ArchiveRecord
from report_issuing.models import IssueForm, IssueRecord, IssueRecordHistory
from report_issuing.services import IssueRecordService
from datetime import datetime, timedelta


class IssueRecordServiceTest(TestCase):
    def setUp(self):
        """设置测试环境"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='record_test_user', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 将用户设置为超级用户以绕过权限检查
        self.user.is_superuser = True
        self.user.save()
        
        # 创建测试档案记录
        self.archive_record = ArchiveRecord.objects.create(
            sample_number='S001',
            report_number='R001',
            commission_number='C001',
            project_name='测试项目',
            client_unit='测试单位',
            commission_datetime=timezone.now() - timedelta(days=30),
            archive_datetime=timezone.now() - timedelta(days=10),
            archive_status='archived',
            total_issue_copies=3
        )
        
        # 创建测试发放单
        self.issue_form = IssueForm.objects.create(
            number='ISSUE-20230101-001',
            issue_date=timezone.now(),
            receiver_name='表单领取人',
            receiver_unit='表单单位',
            receiver_phone='12345678901',
            status='archived',
            issuer=self.user
        )
        
        # 初始化服务
        self.service = IssueRecordService(user=self.user)
    
    def test_create_issue_record(self):
        """测试创建发放记录"""
        data = {
            'issue_type': 'first',
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位',
            'receiver_phone': '12345678901',
            'notes': '测试备注'
        }
        
        record = self.service.create_issue_record(self.archive_record.id, data)
        
        # 验证记录基本信息
        self.assertIsNotNone(record)
        self.assertEqual(record.archive_record, self.archive_record)
        self.assertEqual(record.issue_type, 'first')
        self.assertEqual(record.receiver_name, '测试领取人')
        self.assertEqual(record.receiver_unit, '测试单位')
        self.assertEqual(record.receiver_phone, '12345678901')
        self.assertEqual(record.source, 'manual_create')
        self.assertEqual(record.notes, '测试备注')
        self.assertTrue(record.is_active)
        self.assertFalse(record.is_deleted)
        self.assertIsNone(record.issue_form)
        
        # 验证生成了历史记录
        history = IssueRecordHistory.objects.filter(issue_record=record)
        self.assertEqual(history.count(), 1)
        self.assertEqual(history.first().operation_type, 'create')
    
    def test_create_duplicate_record(self):
        """测试创建重复发放记录的错误处理"""
        # 先创建一条记录
        data = {
            'issue_type': 'first',
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位',
            'receiver_phone': '12345678901',
        }
        self.service.create_issue_record(self.archive_record.id, data)
        
        # 尝试创建重复记录，应该引发异常
        with self.assertRaises(Exception):
            self.service.create_issue_record(self.archive_record.id, data)
    
    def test_update_issue_record(self):
        """测试更新发放记录"""
        # 先创建记录
        data = {
            'issue_type': 'first',
            'issue_date': timezone.now(),
            'receiver_name': '原领取人',
            'receiver_unit': '原单位',
            'receiver_phone': '12345678901',
            'notes': '原备注'
        }
        record = self.service.create_issue_record(self.archive_record.id, data)
        
        # 更新记录
        update_data = {
            'receiver_name': '新领取人',
            'receiver_unit': '新单位',
            'receiver_phone': '98765432101',
            'notes': '新备注',
            'issue_date': timezone.now() + timedelta(days=1)
        }
        updated_record = self.service.update_issue_record(record.id, update_data, "测试更新")
        
        # 验证原记录变为非活跃
        original_record = IssueRecord.objects.get(id=record.id)
        self.assertFalse(original_record.is_active)
        
        # 验证新记录信息
        self.assertNotEqual(updated_record.id, record.id)
        self.assertEqual(updated_record.receiver_name, '新领取人')
        self.assertEqual(updated_record.receiver_unit, '新单位')
        self.assertEqual(updated_record.receiver_phone, '98765432101')
        self.assertEqual(updated_record.source, 'manual_update')
        self.assertTrue(updated_record.is_active)
        
        # 验证生成了历史记录
        history = IssueRecordHistory.objects.filter(issue_record=updated_record)
        self.assertEqual(history.count(), 1)
        self.assertEqual(history.first().operation_type, 'update')
    
    def test_delete_issue_record(self):
        """测试删除发放记录"""
        # 创建记录
        data = {
            'issue_type': 'first',
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位',
            'receiver_phone': '12345678901',
        }
        record = self.service.create_issue_record(self.archive_record.id, data)
        
        # 删除记录
        result = self.service.delete_issue_record(record.id, "测试删除原因")
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证记录状态
        updated_record = IssueRecord.objects.get(id=record.id)
        self.assertTrue(updated_record.is_deleted)
        self.assertFalse(updated_record.is_active)
        
        # 验证生成了历史记录
        history = IssueRecordHistory.objects.get(
            issue_record=record,
            operation_type='delete'
        )
        self.assertEqual(history.operation_reason, "测试删除原因")
    
    def test_handle_form_deleted_records(self):
        """测试处理发放单删除时关联的发放记录"""
        # 为发放单创建关联记录
        form_record = IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='发放单领取人',
            receiver_unit='发放单单位',
            receiver_phone='12345678901',
            source='issue_form',
            issue_form=self.issue_form,
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        # 创建手动更新的记录
        manual_record = IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='second',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='手动领取人',
            receiver_unit='手动单位',
            receiver_phone='98765432101',
            source='manual_update',
            issue_form=self.issue_form,
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        # 处理发放单删除
        self.service.handle_form_deleted_records(self.issue_form, "测试发放单删除")
        
        # 验证发放单生成的记录状态
        updated_form_record = IssueRecord.objects.get(id=form_record.id)
        self.assertTrue(updated_form_record.is_deleted)
        self.assertFalse(updated_form_record.is_active)
        self.assertIsNone(updated_form_record.issue_form)
        
        # 验证手动更新的记录状态
        updated_manual_record = IssueRecord.objects.get(id=manual_record.id)
        self.assertFalse(updated_manual_record.is_deleted)  # 保持未删除
        self.assertTrue(updated_manual_record.is_active)    # 保持活跃
        self.assertIsNone(updated_manual_record.issue_form) # 但解除关联
        
        # 验证生成了历史记录
        form_history = IssueRecordHistory.objects.get(
            issue_record=form_record,
            operation_type='form_delete'
        )
        self.assertIn("发放单删除", form_history.operation_reason)
        
        manual_history = IssueRecordHistory.objects.get(
            issue_record=manual_record,
            operation_type='form_delete'
        )
        self.assertIn("保留手动更新的记录", manual_history.operation_reason)
    
    def test_get_archive_record_issue_status(self):
        """测试获取档案记录的发放状态"""
        # 创建第一次发放记录
        IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        # 获取状态
        status = self.service.get_archive_record_issue_status(self.archive_record.id)
        
        # 验证结果
        self.assertTrue(status['has_first_issue'])
        self.assertFalse(status['has_second_issue'])
        self.assertFalse(status['can_issue_first'])
        self.assertTrue(status['can_issue_second'])
        self.assertIsNotNone(status['first_issue_id'])
        self.assertIsNone(status['second_issue_id'])
        
        # 添加第二次发放记录
        IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='second',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人2',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        # 再次获取状态
        status = self.service.get_archive_record_issue_status(self.archive_record.id)
        
        # 验证结果
        self.assertTrue(status['has_first_issue'])
        self.assertTrue(status['has_second_issue'])
        self.assertFalse(status['can_issue_first'])
        self.assertFalse(status['can_issue_second'])
        self.assertIsNotNone(status['first_issue_id'])
        self.assertIsNotNone(status['second_issue_id'])

    def test_determine_actual_issue_type(self):
        """测试确定实际发放类型"""
        # 测试发放1份的情况 - 应当始终为first
        actual_type = self.service._determine_actual_issue_type(self.archive_record, 'single')
        self.assertEqual(actual_type, 'first')
        
        # 创建第一次发放记录
        IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            source='manual_create',
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        # 再次测试发放1份的情况 - 应当仍为first，但此时已有first记录，会在验证阶段阻止
        actual_type = self.service._determine_actual_issue_type(self.archive_record, 'single')
        self.assertEqual(actual_type, 'first')
        
        # 测试发放全部/剩余的情况 - 有first记录时应为second
        actual_type = self.service._determine_actual_issue_type(self.archive_record, 'all_remaining')
        self.assertEqual(actual_type, 'second')
        
        # 创建一个没有发放记录的新档案记录
        new_record = ArchiveRecord.objects.create(
            sample_number='S002',
            report_number='R002',
            commission_number='C002',
            project_name='新测试项目',
            client_unit='新测试单位',
            archive_status='archived',
            total_issue_copies=3
        )
        
        # 测试对没有发放记录的档案发放全部/剩余的情况 - 应为first
        actual_type = self.service._determine_actual_issue_type(new_record, 'all_remaining')
        self.assertEqual(actual_type, 'first')
