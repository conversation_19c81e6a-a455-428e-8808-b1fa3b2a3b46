"use client"

import React, { useState, use<PERSON>emo, use<PERSON><PERSON>back, useRef, ReactNode, useEffect } from "react"
import { PlusCircle, MoreHorizontal, FileText, Printer, Upload, Eye, X, Plus } from "lucide-react"
import { TablePageLayout } from "@/components/common/table-page-layout"
import { PageAction } from "@/components/common/page-header"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AgGridReact } from 'ag-grid-react'
import agGridConfig from "@/lib/ag-grid-config"
import {
  ColDef,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  ValueGetterParams,
  FirstDataRenderedEvent,
  EditableCallbackParams,
  ITooltipParams,
  SortChangedEvent,
  DomLayoutType
} from 'ag-grid-enterprise'
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Input } from "@/components/ui/input"
import { DateRangeFilter } from '@/components/common/DateRangeFilter'
import { format } from "date-fns"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { getIssueForms, updateIssueFormNotes, uploadIssueFormReceipt, getReceiptFileUrl, IssueFormData as ReportIssuingIssueFormData, ListParams } from '@/services/domain/issue/reportIssuingService'
import apiClient from '@/lib/apiClient'
import { useToast } from "@/components/ui/use-toast"

const StatusCellRenderer = (params: ICellRendererParams<ReportIssuingIssueFormData>) : ReactNode => {
  const status = params.value as ReportIssuingIssueFormData['status'];
  switch (status) {
    case "draft": return <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">草稿</Badge>;
    case "locked": return <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-300">已锁定</Badge>;
    case "issued": return <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">已发放</Badge>;
    default: return <Badge variant="outline">未知状态</Badge>;
  }
};

const ReceiptCellRenderer = (params: ICellRendererParams<ReportIssuingIssueFormData>) : ReactNode => {
  const hasReceipt = params.value;
  if (hasReceipt) {
    return <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">已上传</Badge>;
  } else {
    return <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">未上传</Badge>;
  }
};

interface ActionDefinition {
  key: string;
  label: string;
  icon: ReactNode;
  iconForMenu?: ReactNode;
  type: 'link' | 'button';
  hrefPattern?: string;
  actionName?: 'onUploadReceiptClick' | 'onPrintConfirmationClick' | 'onViewReceiptClick';
  condition: (report: ReportIssuingIssueFormData) => boolean;
  isDirectActionOnLargeScreen?: boolean;
}

const reportActionDefinitions: ActionDefinition[] = [
  {
    key: "view_detail",
    label: "发放单详情",
    icon: <FileText className="h-4 w-4" />,
    iconForMenu: <FileText className="mr-2 h-4 w-4" />,
    type: "link",
    hrefPattern: "/reports/detail/${issue_number}",
    condition: () => true,
    isDirectActionOnLargeScreen: true,
  },
  {
    key: "upload_receipt",
    label: "上传用户确认单",
    icon: <Upload className="h-4 w-4" />,
    iconForMenu: <Upload className="mr-2 h-4 w-4" />,
    type: "button",
    actionName: "onUploadReceiptClick",
    condition: (report) => report.status === "issued" && !report.confirmationFile,
    isDirectActionOnLargeScreen: true,
  },
  {
    key: "view_receipt",
    label: "查看用户确认单",
    icon: <Eye className="h-4 w-4" />,
    iconForMenu: <Eye className="mr-2 h-4 w-4" />,
    type: "button",
    actionName: "onViewReceiptClick",
    condition: (report) => !!report.confirmationFile,
    isDirectActionOnLargeScreen: true,
  },
  {
    key: "print_confirmation",
    label: "打印确认单",
    icon: <Printer className="h-4 w-4" />,
    iconForMenu: <Printer className="mr-2 h-4 w-4" />,
    type: "button",
    actionName: "onPrintConfirmationClick",
    condition: (report) => report.status === "issued",
    isDirectActionOnLargeScreen: true,
  },
];

interface ActionsCellRendererParams extends ICellRendererParams<ReportIssuingIssueFormData> {
  onUploadReceiptClick: (data: ReportIssuingIssueFormData) => void;
  onPrintConfirmationClick: (data: ReportIssuingIssueFormData) => void;
  onViewReceiptClick: (data: ReportIssuingIssueFormData) => void;
}

const ActionsCellRenderer = (params: ActionsCellRendererParams) : ReactNode => {
  const report = params.data;
  if (!report) return null;

  const { onUploadReceiptClick, onPrintConfirmationClick, onViewReceiptClick } = params;

  const availableActions = reportActionDefinitions.filter(act => act.condition(report));

  const renderIconButtonWithTooltip = (action: ActionDefinition) => {
    const ActualButton = (
      <Button variant="ghost" size="icon" className="h-8 w-8" 
        onClick={action.type === 'button' ? () => {
          if (action.actionName === 'onUploadReceiptClick') onUploadReceiptClick(report);
          if (action.actionName === 'onPrintConfirmationClick') onPrintConfirmationClick(report);
          if (action.actionName === 'onViewReceiptClick') onViewReceiptClick(report);
        } : undefined}
      >
        {action.icon}
      </Button>
    );
    return (
      <TooltipProvider key={`${action.key}_tooltip_direct`}>
        <Tooltip>
          <TooltipTrigger asChild>
            {action.type === 'link' && action.hrefPattern ? (
              <Link href={action.hrefPattern.replace("${issue_number}", String(report.issueNumber))} passHref className="inline-flex items-center">
                {ActualButton}
              </Link>
            ) : (
              ActualButton
            )}
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>{action.label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };
  
  const directActionButtons = availableActions
    .filter(act => act.isDirectActionOnLargeScreen)
    .map(act => renderIconButtonWithTooltip(act));

  const dropdownMenuItems = availableActions.map(act => (
    <DropdownMenuItem 
      key={`${act.key}_dd`} 
      asChild={act.type === 'link'}
      onClick={act.type === 'button' ? () => {
        if (act.actionName === 'onUploadReceiptClick') onUploadReceiptClick(report);
        if (act.actionName === 'onPrintConfirmationClick') onPrintConfirmationClick(report);
        if (act.actionName === 'onViewReceiptClick') onViewReceiptClick(report);
      } : undefined}
    >
      {act.type === 'link' && act.hrefPattern ? (
        <Link href={act.hrefPattern.replace("${issue_number}", String(report.issueNumber))}>
          {act.iconForMenu || act.icon}
          {act.label}
        </Link>
      ) : (
        <>
          {act.iconForMenu || act.icon}
          {act.label}
        </>
      )}
    </DropdownMenuItem>
  ));

  return (
    <div className="flex items-center justify-end space-x-0.5">
      <div className="hidden md:flex items-center space-x-0.5">
        {directActionButtons}
      </div>

      <div className="flex items-center md:hidden">
        {dropdownMenuItems.length > 0 && (
            <TooltipProvider>
                <Tooltip>
                <TooltipTrigger asChild>
                    <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">{dropdownMenuItems}</DropdownMenuContent>
                    </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent side="bottom"><p>更多操作</p></TooltipContent>
                </Tooltip>
            </TooltipProvider>
        )}
      </div>
    </div>
  );
};



const pageActions: PageAction[] = [
  {
    label: "新建发放单",
    icon: <Plus className="mr-2 h-4 w-4" />,
    href: "/reports/new",
    variant: "default", // 使用默认样式，与更改单管理页面一致
  }
];

export default function ReportsManagementPage() {

  const { toast } = useToast();
  const [rowData, setRowData] = useState<ReportIssuingIssueFormData[]>([]);
  const [gridApi, setGridApi] = useState<GridApi<ReportIssuingIssueFormData> | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [globalFilter, setGlobalFilter] = useState('');
  // 默认30天的日期范围
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    return {
      from: thirtyDaysAgo,
      to: today
    };
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingReportId, setUploadingReportId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'in-progress' | 'issued'>('in-progress');
  
  // 使用normal布局以确保表格铺满容器
  const domLayout: DomLayoutType = 'normal';

  const onGridReady = (params: GridReadyEvent<ReportIssuingIssueFormData>) => {
    setGridApi(params.api);
  };
  
  const onFirstDataRendered = (params: FirstDataRenderedEvent) => {
    params.api.autoSizeAllColumns();
  };

  const fetchAndSetData = useCallback(async () => {
    if (!gridApi) return;
    
    setLoading(true);
    gridApi.setGridOption('loading', true);

    // 使用Column State API获取排序信息（v27+新方法）
    let ordering = '-created_at'; // 默认排序
    try {
      const columnState = gridApi.getColumnState();
      const sortedColumn = columnState.find(col => col.sort);
      if (sortedColumn) {
        ordering = `${sortedColumn.sort === 'desc' ? '-' : ''}${sortedColumn.colId}`;
      }
    } catch (error) {
      console.warn('获取列状态失败:', error);
    }
    
    const params: ListParams = {
      ordering,
      // 移除search参数，改用AG Grid的quickFilterText进行客户端搜索
      created_at_after: dateRange.from ? dateRange.from.toISOString() : undefined,
      created_at_before: dateRange.to ? dateRange.to.toISOString() : undefined,
      // 根据当前标签页筛选状态
      status_filter: activeTab === 'in-progress' ? 'active' : 'issued',
    };

    try {
      const paginatedResponse = await getIssueForms();
      setRowData(paginatedResponse.results);
      setTotalCount(paginatedResponse.count);
      
      setLoading(false);
      gridApi.setGridOption('loading', false);
      
      if (paginatedResponse.results.length === 0) {
        gridApi.showNoRowsOverlay();
      }
    } catch (err: any) {
      setLoading(false);
      gridApi.setGridOption('loading', false);
      
      toast({ title: "错误", description: err.message || '获取发放单列表失败', variant: "destructive" });
      gridApi.showNoRowsOverlay();
    }
  }, [gridApi, dateRange, activeTab, toast]);

  useEffect(() => {
    fetchAndSetData();
  }, [fetchAndSetData]);

  // 当标签页切换时重新获取数据
  useEffect(() => {
    fetchAndSetData();
  }, [activeTab, fetchAndSetData]);

  const onSortChanged = () => {
    fetchAndSetData();
  };

  const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setGlobalFilter(e.target.value);
    // AG Grid的quickFilterText会通过props自动更新，无需手动设置
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({
      from: startDate ? new Date(startDate) : undefined,
      to: endDate ? new Date(endDate) : undefined,
    });
  };

  const clearFilters = () => {
    setGlobalFilter('');
    // 重置日期范围为默认30天
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    setDateRange({
      from: thirtyDaysAgo,
      to: today
    });
    if (gridApi) {
        gridApi.setFilterModel(null);
        // quickFilterText会通过props自动清空，无需手动设置
        fetchAndSetData();
    }
  };

  const onCellValueChanged = async (params: any) => {
    const { issueNumber, notes } = params.data;
    try {
      await updateIssueFormNotes(issueNumber, notes);
      toast({ title: "成功", description: "备注更新成功" });
    } catch (err: any) {
      toast({ title: "错误", description: err.message || '更新备注失败', variant: "destructive" });
      params.api.applyTransaction({ update: [{ ...params.data, notes: params.oldValue }] });
    }
  };

  const onUploadReceiptClick = (data: ReportIssuingIssueFormData) => {
    setUploadingReportId(data.issueNumber);
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && uploadingReportId !== null) {
      try {
        await uploadIssueFormReceipt(uploadingReportId, file);
        toast({ title: "成功", description: "确认单上传成功" });
        fetchAndSetData();
      } catch (err: any) {
        toast({ title: "错误", description: err.message || '上传确认单失败', variant: "destructive" });
      } finally {
        setUploadingReportId(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  };

  const onPrintConfirmationClick = (data: ReportIssuingIssueFormData) => {
    toast({ title: "提示", description: `正在准备打印发放单: ${data.issueNumber}` });
  };
  
  const onViewReceiptClick = async (data: ReportIssuingIssueFormData) => {
    if (!data.issueNumber) return;
    try {
      const fileUrl = getReceiptFileUrl(data.issueNumber);
      window.open(fileUrl, '_blank');
    } catch (err: any) {
      toast({ title: "错误", description: err.message, variant: "destructive" });
    }
  };
  
  const colDefs = useMemo<ColDef<ReportIssuingIssueFormData>[]>(() => [
    { headerName: '发放单编号', field: 'issueNumber', filter: 'agTextColumnFilter', sortable: true, pinned: 'left', minWidth: 160 },
    { headerName: '状态', field: 'status', cellRenderer: StatusCellRenderer, filter: 'agSetColumnFilter', sortable: true, width: 100 },
    { 
      headerName: '确认单', 
      field: 'confirmationFile', 
      cellRenderer: ReceiptCellRenderer,
      filter: 'agSetColumnFilter',
      valueGetter: (params: ValueGetterParams<ReportIssuingIssueFormData>) => params.data?.confirmationFile ? '已上传' : '未上传',
      sortable: true,
      width: 110 
    },
    { headerName: '领取单位', field: 'receiverUnit', filter: 'agTextColumnFilter', sortable: true, minWidth: 180 },
    { headerName: '发放人', field: 'issuerUsername', filter: 'agTextColumnFilter', sortable: true, width: 120 },
    { headerName: '领取人', field: 'receiverName', filter: 'agTextColumnFilter', sortable: true, width: 120 },
    { headerName: '领取人电话', field: 'receiverPhone', filter: 'agTextColumnFilter', sortable: true, width: 140 },
    { 
      headerName: '创建时间', 
      field: 'createdAt', 
      filter: 'agDateColumnFilter', 
      sortable: true,
      valueFormatter: params => params.value ? format(new Date(params.value), 'yyyy-MM-dd HH:mm') : '',
      minWidth: 160
    },
    { 
      headerName: '发放时间', 
      field: 'issueDate', 
      filter: 'agDateColumnFilter', 
      sortable: true,
      valueFormatter: params => params.value ? format(new Date(params.value), 'yyyy-MM-dd') : '',
      minWidth: 140
    },
    { 
      headerName: '备注', 
      field: 'notes', 
      editable: true,
      cellEditor: 'agLargeTextCellEditor',
      cellEditorPopup: true,
      minWidth: 200,
      tooltipField: 'notes',
    },
    {
      headerName: '操作',
      colId: 'actions',
      cellRenderer: ActionsCellRenderer,
      cellRendererParams: {
        onUploadReceiptClick,
        onPrintConfirmationClick,
        onViewReceiptClick
      },
      editable: false,
      sortable: false,
      filter: false,
      pinned: 'right',
      width: 160,
    },
  ], [onUploadReceiptClick, onPrintConfirmationClick, onViewReceiptClick]);

  const defaultColDef = useMemo(() => ({
    ...agGridConfig.defaultColDef,
    resizable: true,
    sortable: true,
    filter: true,
    floatingFilter: true,
  }), []);

  return (
    <TablePageLayout
      title="发放单台账"
      subtitle="查看和管理所有档案发放单"
      actions={pageActions}
    >
      {/* 上方控制区域 - 与档案台账保持一致的布局 */}
      <div className="flex justify-between items-center mb-3">
        {/* 左侧：状态标签页 */}
        <div className="flex items-center gap-2">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'in-progress' | 'issued')}>
            <TabsList className="h-9">
              <TabsTrigger value="in-progress">进行中</TabsTrigger>
              <TabsTrigger value="issued">已发放</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        {/* 右侧：搜索和筛选 */}
        <div className="flex items-center gap-2">
          {/* 自定义搜索框实现，与档案台账保持一致 */}
          <div className="relative w-60">
            <input
              type="text"
              placeholder="搜索发放单编号、单位、人员..."
              value={globalFilter}
              onChange={onGlobalFilterChange}
              className="w-full h-9 px-3 py-2 text-sm bg-background border border-input rounded-md focus-visible:outline-none focus-visible:border-primary"
            />
            {loading && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-primary border-opacity-50 border-t-primary rounded-full" />
              </div>
            )}
          </div>

          {/* 使用 DateRangeFilter 组件 */}
          <DateRangeFilter 
            initialStartDate={dateRange.from?.toISOString().split('T')[0]}
            initialEndDate={dateRange.to?.toISOString().split('T')[0]}
            onApplyFilter={handleDateRangeChange} 
          />
        </div>
      </div>



      {/* 表格容器 - 设置固定高度确保铺满布局 */}
      <div className="h-[calc(100%-48px)]">
        <div className="ag-theme-balham h-full w-full">
          <AgGridReact
            rowData={rowData}
            columnDefs={colDefs}
            defaultColDef={defaultColDef}
            domLayout={domLayout}
            pagination={true}
            paginationPageSize={20}
            quickFilterText={globalFilter}
            onGridReady={onGridReady}
            onSortChanged={onSortChanged}
            onFirstDataRendered={onFirstDataRendered}
            onCellValueChanged={onCellValueChanged}
          />
        </div>
      </div>
      <input 
        type="file" 
        ref={fileInputRef} 
        onChange={handleFileChange}
        style={{ display: 'none' }} 
        accept="application/pdf,image/*"
      />
    </TablePageLayout>
  )
}
