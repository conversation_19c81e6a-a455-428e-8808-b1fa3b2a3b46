"""
Excel档案数据导入服务

此模块提供Excel文件导入到档案记录系统的功能。
主要功能包括:
1. 读取Excel文件数据
2. 将数据映射到档案记录模型
3. 处理日期格式转换
4. 记录导入日志
5. 错误处理和报告
"""

import os
import hashlib
import time
import logging
import uuid
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import json
import traceback
from contextlib import contextmanager
import sys
from django.conf import settings
# CHANGE: [2025-06-03] 添加 datetime 模块导入以修复时区API错误
import datetime as dt
import pandas as pd
import numpy as np
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Q
from django.core.exceptions import ValidationError
from enum import Enum
from dataclasses import dataclass
# CHANGE: [2025-06-03] 添加正确的Django数据库异常导入
from django.db import DatabaseError, IntegrityError
from ..utils.datetime_utils import DateTimeComparisonUtil

from ..models import (
    ArchiveRecord,
    ImportLog,
    ChangeLogBatch,
    RecordChangeLog,
    FieldChangeLog,
    ImportSession,
    ImportSessionStatus,
)

# 配置导入服务日志
logger = logging.getLogger(__name__)

# 添加一个控制台处理程序，确保导入过程的日志可见
import_handler = logging.StreamHandler(sys.stdout)
import_handler.setLevel(logging.INFO)  # 设置为INFO级别，确保处理过程信息可见
import_formatter = logging.Formatter("%(asctime)s [EXCEL导入] %(message)s")
import_handler.setFormatter(import_formatter)
logger.addHandler(import_handler)
logger.setLevel(logging.INFO)  # 确保logger本身的级别足够低


# 添加一个函数，直接打印到控制台，用于关键过程信息
def log_import_progress(message: str) -> None:
    """
    记录导入进度信息，确保在Docker日志中可见

    Args:
        message: 要记录的消息
    """
    print(f"[EXCEL导入] {message}", flush=True)  # flush=True确保立即输出
    logger.info(message)


# 定义常量和枚举
class DuplicateStrategy(str, Enum):
    """重复数据处理策略"""

    SMART_UPDATE = "smart_update"  # 智能更新，仅更新有变化的非空字段
    OVERWRITE = "overwrite"  # 完全覆盖现有记录
    SKIP = "skip"  # 跳过已存在的记录
    APPEND = "append"  # 总是创建新记录
    # CHANGE: [2024-07-26] 移除高风险的force_create策略
    # FORCE_CREATE = "force_create"  # 强制创建新记录


# 字段重要性级别
class FieldImportance(str, Enum):
    """字段重要性级别"""

    CRITICAL = "critical"  # 关键字段，如委托编号
    IMPORTANT = "important"  # 重要字段，如样品编号、项目名称
    NORMAL = "normal"  # 普通字段


# 定义结果数据类
@dataclass
class ImportResult:
    """导入结果数据类"""

    success_count: int  # 成功处理的记录数
    error_records: List[Dict]  # 错误记录列表
    skipped_records: List[Dict]  # 跳过记录列表
    created_count: int  # 创建记录数
    updated_count: int  # 更新记录数
    unchanged_count: int  # 完全相同记录数


# Excel导入相关异常
class ExcelImportError(Exception):
    """Excel导入基础异常类"""

    pass


class InvalidFormatError(ExcelImportError):
    """Excel格式错误"""

    pass


class RequiredFieldMissingError(ExcelImportError):
    """必填字段缺失"""

    pass


class DatabaseOperationError(ExcelImportError):
    """数据库操作错误"""

    pass


# 配置类
class ExcelImportConfig:
    """Excel导入配置"""

    # 每批处理记录数
    BATCH_SIZE = 500

    # 定义错误严重程度阈值
    ERROR_RATIO_THRESHOLD = 0.1  # 错误记录比例超过10%视为部分成功

    # 重试设置
    MAX_RETRIES = 3
    RETRY_DELAY = 0.5  # 秒

    # 日志设置
    LOG_LEVEL = "INFO"
    PERFORMANCE_LOGGING = True


# 性能追踪上下文管理器
@contextmanager
def performance_tracker(description, logger=None):
    """
    用于跟踪代码块性能的上下文管理器

    Args:
        description: 描述此性能跟踪的文本
        logger: 可选的日志记录器，如不提供则打印到控制台

    Example:
        with performance_tracker("读取Excel文件", logger):
            df = pd.read_excel(file_path)
    """
    start_time = time.time()
    try:
        yield
    finally:
        elapsed = time.time() - start_time
        message = f"{description} 耗时: {elapsed:.2f}秒"
        if logger:
            logger.info(message)
        else:
            print(message)


class ExcelImportService:
    """Excel导入服务类，处理Excel文件到系统的导入过程"""

    # Excel列名到模型字段的映射
    DEFAULT_FIELD_MAPPING = {
        "样品编号": "sample_number",
        "账号": "account_from_excel",
        "委托编号": "commission_number",
        "报告编号": "report_number",
        "省统一报告编号": "province_unified_number",
        "站点编号": "station_code",
        "机构代号": "organization_code",
        "工程编号": "project_number",
        "工程名称": "project_name",
        "分项工程": "sub_project",
        "工程部位": "project_location",
        "工程地址": "project_address",
        "委托单位": "client_unit",
        "委托人": "client_name",
        "委托日期": "commission_datetime",
        "试验开始日期": "test_start_datetime",
        "试验结束日期": "test_end_datetime",
        "试验人1": "test_person1",
        "试验人2": "test_person2",
        "数据录入人": "data_entry_person",
        "检测结果": "test_result",
        "结论": "conclusion",
        "检测参数": "test_parameters",
        "不合格参数": "unqualified_parameters",
        "报告归档状态": "archive_status",
        "更改次数": "change_count",
        "当前数据状态": "current_status",
        "待处理状态": "processing_status",
        # "档案盒编号": "archive_box_number",
        # "档案URL链接": "archive_url",
        "附件": "attachments_from_excel",
        "入库日期": "storage_datetime",
        "入库人": "storage_person",
        "出库日期": "outbound_datetime",
        "出库人": "outbound_person",
        "归档日期": "archive_datetime",
        "归档人": "archive_person",
        "报告发放状态": "report_issue_status",
        # "发放份数": "first_issue_copies",
        "发放日期": "first_issue_datetime",
        "发放人": "first_issue_person",
        "领取人": "first_receiver_name",
        "领取日期": "first_issue_datetime",     #   值等于发放日期？
        # '领取单位': 'first_receiver_unit',
        # '领取人电话': 'first_receiver_phone',
        "组号": "group_number",
        "样品/项目名称": "sample_name",
        "分配人": "assigned_person",
        "构件(桩)数": "component_count",
        "测点数": "test_point_count",
        "不合格点数": "unqualified_point_count",
        # "样品留样时间": "sample_retention_datetime",
        # "样品剩余时间(天)": "sample_remaining_time",
        "收费状态": "payment_status",
        "价格调整状态": "price_adjustment_status",
        "标准价格费用": "standard_price",
        "折扣价格费用": "discount_price",
        "实际价格费用": "actual_price",
    }

    # 日期字段列表
    DATE_FIELDS = [
        "commission_datetime",
        "test_start_datetime",
        "test_end_datetime",
        "storage_datetime",
        "outbound_datetime",
        "archive_datetime",
        "first_issue_datetime",
        "receive_datetime",
        "sample_retention_datetime",
    ]

    # 数值字段列表
    NUMERIC_FIELDS = [
        "change_count",
        "component_count",
        "test_point_count",
        "unqualified_point_count",
        "total_issue_copies",
        "sample_remaining_time",
        "standard_price",
        "discount_price",
        "actual_price",
    ]

    # 字段重要性定义
    FIELD_IMPORTANCE = {
        "commission_number": FieldImportance.CRITICAL,  # 关键字段，委托编号
        "sample_number": FieldImportance.IMPORTANT,  # 重要字段，样品编号
        "project_name": FieldImportance.IMPORTANT,  # 重要字段，项目名称
        "client_unit": FieldImportance.IMPORTANT,  # 重要字段，委托单位
        "commission_datetime": FieldImportance.IMPORTANT,  # 重要字段，委托日期
        # 其他字段默认为 'normal'
    }

    # 字段长度限制 - 根据数据库模型定义添加
    # 注意：模型中的字段长度已更改为2000，此字典仅用于记录字段限制和日志
    STRING_FIELD_LIMITS = {
        "commission_number": 2000,
        "sample_number": 2000,
        "account_from_excel": 2000,
        "report_number": 2000,
        "province_unified_number": 2000,
        "station_code": 2000,
        "organization_code": 2000,
        "project_number": 2000,
        "project_name": 2000,
        "sub_project": 2000,
        "project_location": 2000,
        "project_address": 2000,
        "client_unit": 2000,
        "client_name": 2000,
        "test_person1": 2000,
        "test_person2": 2000,
        "data_entry_person": 2000,
        "test_result": 2000,
        "conclusion": 2000,
        "test_parameters": 2000,
        "unqualified_parameters": 2000,
        "archive_status": 2000,
        "current_status": 2000,
        "processing_status": 2000,
        "archive_box_number": 2000,
        "archive_url": 2000,
        "attachments_from_excel": 2000,
        "storage_person": 2000,
        "outbound_person": 2000,
        "archive_person": 2000,
        "report_issue_status": 2000,
        "first_issue_person": 2000,
        "first_receiver_name": 2000,
        "group_number": 2000,
        "sample_name": 2000,
        "assigned_person": 2000,
        "payment_status": 2000,
        "price_adjustment_status": 2000,
    }

    def __init__(self, field_mapping: Optional[Dict[str, str]] = None) -> None:
        """
        初始化导入服务

        Args:
            field_mapping: 自定义Excel列名到模型字段的映射。如未提供，使用默认映射。
        """
        self.field_mapping = field_mapping or self.DEFAULT_FIELD_MAPPING
        # 将类变量复制到实例变量
        self.string_field_limits = self.STRING_FIELD_LIMITS
        # 初始化配置
        self.config = ExcelImportConfig()

    def _validate_duplicate_strategy(self, strategy: str) -> str:
        """
        验证重复数据处理策略

        Args:
            strategy: 传入的策略字符串

        Returns:
            有效的策略字符串。如果传入无效策略，返回默认策略
        """
        valid_strategies = [s.value for s in DuplicateStrategy]

        # CHANGE: [2024-07-26] 特别处理force_create策略，这是一个高风险策略已被移除
        if strategy == "force_create":
            logger.warning(
                f"警告: 策略 'force_create' 已被移除(高风险)，使用默认策略 '{DuplicateStrategy.SMART_UPDATE.value}'"
            )
            return DuplicateStrategy.SMART_UPDATE.value

        if strategy not in valid_strategies:
            logger.warning(
                f"警告: 无效的策略 '{strategy}'，使用默认策略 '{DuplicateStrategy.SMART_UPDATE.value}'"
            )
            return DuplicateStrategy.SMART_UPDATE.value

        return strategy

    def _process_excel_data(self, df):
        """处理Excel数据，识别并设置正确的表头"""
        # 查找包含关键字段的表头行
        for idx, row in df.iterrows():
            if any(key in row.values for key in self.DEFAULT_FIELD_MAPPING.keys()):
                # 设置表头并重新处理数据
                df = df.iloc[idx:].reset_index(drop=True)
                df.columns = df.iloc[0]
                return df.iloc[1:].reset_index(drop=True)

        # 如果没找到匹配的表头行，使用第一行作为表头
        df.columns = df.iloc[0]
        return df.iloc[1:].reset_index(drop=True)

    def import_from_file(
        self,
        file_path: str,
        user: Optional[User] = None,
        sheet_name: Union[int, str] = 0,
        duplicate_strategy: str = DuplicateStrategy.SMART_UPDATE.value,
        import_session: Optional[ImportSession] = None
    ) -> ImportLog:
        """从Excel文件导入数据

        Args:
            file_path: Excel文件路径
            user: 导入用户
            sheet_name: 工作表名称或索引
            duplicate_strategy: 重复数据处理策略
            import_session: 导入会话实例

        Returns:
            ImportLog: 导入日志实例
        """
        duplicate_strategy = self._validate_duplicate_strategy(duplicate_strategy)
        import_log: Optional[ImportLog] = None
        method_start_time = time.time() # CHANGE: Record start time

        with performance_tracker(f"整个从文件导入过程 ({file_path})", logger):
            log_import_progress(f"开始从文件导入: {file_path}")

            try:
                # 创建导入日志
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                file_hash = self._calculate_file_hash(file_path)

                import_log = ImportLog.objects.create(
                    file_name=file_name,
                    file_size=file_size,
                    file_hash=file_hash,
                    import_user=user,
                    batch_number=self._generate_batch_number(),
                    status="processing",
                )
                # CHANGE: [2025-06-05] 从导入会话获取创建人信息，不再使用 import_session_id_fk
                if import_session:
                    import_log.created_by = import_session.created_by  # 从会话获取创建人
                    import_log.session_id = str(import_session.session_id)  # 设置会话ID字符串
                    import_log.save(update_fields=['created_by', 'session_id'])

                # 读取Excel文件
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                log_import_progress(f"Excel行数: {len(df)}")
                log_import_progress(f"数据处理策略: {duplicate_strategy}")

                # 验证必填列
                self._validate_required_columns(df, import_log)
                total_rows_in_current_df = len(df)

                # 设置分析阶段统计
                import_log.analysis_total_rows_read = total_rows_in_current_df
                import_log.analysis_successfully_parsed_rows = total_rows_in_current_df
                # import_task_total_records_submitted will be set by _update_import_log_with_results
                import_log.save(update_fields=['analysis_total_rows_read', 'analysis_successfully_parsed_rows'])

                # 预处理和缓存记录
                existing_records_map = self._preprocess_and_cache_records(df)
                change_batch = self._create_change_batch(import_log)

                with performance_tracker("数据处理和导入", logger):
                    try:
                        result = self._process_dataframe(
                            df, 
                            import_log,
                            duplicate_strategy,
                            existing_records_map,
                            change_batch,
                            import_session=import_session
                        )
                        success_count = result.success_count
                        error_records = result.error_records
                        skipped_records = result.skipped_records 
                        created_count = result.created_count
                        updated_count = result.updated_count
                        unchanged_count = result.unchanged_count
                    except Exception as df_error:
                        self._handle_dataframe_processing_error(df_error, df, import_log)
                        raise 
                
                self._update_import_log_with_results(
                    import_log,
                    success_count, error_records, skipped_records, 
                    created_count, updated_count, unchanged_count,
                    total_rows_in_current_df # CHANGE: Use total_rows_in_current_df
                )
                
                log_import_progress(
                    f"ExcelImportService.import_from_file 方法已处理 {total_rows_in_current_df} 条提交的记录。 "
                    f"Task内部成功处理 (创建/更新/内容无变更): {getattr(import_log, 'import_task_processed_successfully_count', 0)}. "
                    f"Task内部失败: {getattr(import_log, 'import_task_failed_count', 0)}."
                )
                if getattr(import_log, 'import_task_failed_count', 0) > 0:
                    logger.warning(f"有 {import_log.import_task_failed_count} 条记录在Task执行中失败")
                
                logger.info(f"import_from_file 成功完成主要逻辑，准备返回 ImportLog ID: {import_log.id if import_log else 'None'}")
                if import_log is None: 
                     logger.critical("CRITICAL: import_log is None before successful return in import_from_file.")
                else: # CHANGE: Calculate and set processing_time on success
                    import_log.processing_time = time.time() - method_start_time
                    import_log.save(update_fields=['processing_time'])
                return import_log
            except Exception as e:
                logger.error(f"进入 import_from_file 的主 except 块。捕获的异常类型: {type(e).__name__}, 错误: {str(e)}", exc_info=True)
                current_df_for_handling = df if 'df' in locals() and isinstance(df, pd.DataFrame) else None
                import_log_for_handling = import_log 
                
                if import_log_for_handling: # CHANGE: Calculate and set processing_time on error if log exists
                    import_log_for_handling.processing_time = time.time() - method_start_time
                    # _handle_import_error will save other fields like status, error_log
                    # We save processing_time here if possible, before _handle_import_error potentially changes status
                    try:
                        import_log_for_handling.save(update_fields=['processing_time'])
                    except Exception as save_err_pt:
                        logger.error(f"Error saving processing_time during exception handling: {save_err_pt}")

                log_details_for_import_log = "None"
                if import_log_for_handling is not None:
                    if hasattr(import_log_for_handling, 'id') and import_log_for_handling.id:
                        log_details_for_import_log = f"an ImportLog object (ID: {str(import_log_for_handling.id)})"
                    else:
                        log_details_for_import_log = "an ImportLog object with no ID yet"
                logger.info(f"在调用 _handle_import_error 之前: import_log_for_handling is {log_details_for_import_log}")
                
                try:
                    self._handle_import_error(e, import_log_for_handling, current_df_for_handling)
                    logger.info("_handle_import_error 调用完成。")
                except UnboundLocalError as ule_in_handler: 
                    logger.critical(f"CRITICAL: UnboundLocalError 在 _handle_import_error 内部: {ule_in_handler}. Original: {type(e).__name__} - {str(e)}", exc_info=True)
                except Exception as handler_fatal_ex:
                    logger.critical(f"CRITICAL: _handle_import_error 方法自身执行时发生严重错误: {handler_fatal_ex}. Original: {type(e).__name__} - {str(e)}", exc_info=True)
                raise e

    def _validate_required_columns(
        self, df: pd.DataFrame, import_log: ImportLog
    ) -> None:
        """
        验证DataFrame中是否包含所有必需的列

        Args:
            df: 待验证的DataFrame
            import_log: 导入日志对象，如果验证失败会更新其状态

        Raises:
            RequiredFieldMissingError: 如果缺少必需的列
        """
        required_columns = [
            "委托编号",
            "样品编号",
            "工程名称",
            "委托单位",
            "委托日期",
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            error_msg = f"Excel文件缺少必需的列: {missing_columns}"
            import_log.error_log = error_msg # CORRECTED: Was error_message
            import_log.status = "failed"
            import_log.save(update_fields=['status', 'error_log', 'updated_at']) # Ensure updated_at is also saved
            raise RequiredFieldMissingError(error_msg)

    def _handle_dataframe_processing_error(
        self, error: Exception, df: Optional[pd.DataFrame], import_log: Optional[ImportLog]
    ) -> None:
        """
        处理数据处理过程中的错误 (在 _process_dataframe 内部被调用)
        """
        error_msg = f"处理DataFrame数据时出错: {str(error)}"
        logger.error(error_msg, exc_info=True)

        if import_log is None:
            logger.error("ImportLog 对象为None，无法在 _handle_dataframe_processing_error 中更新。")
            return

        import_log.status = "failed" # Task执行层面失败
        # 追加错误到 error_log，而不是覆盖
        existing_error_log = import_log.error_log or ""
        import_log.error_log = f"{existing_error_log}\n{error_msg}\nTrace: {traceback.format_exc()}".strip()

        # 如果有部分记录已处理，记录更详细的错误信息
        # if hasattr(error, "__context__") and error.__context__:
        #     import_log.error_log += f"\n上下文: {str(error.__context__)}"

        # 保存错误的详细报告 - 使用新的 import_task_* 字段名
        # df 在这里是 _process_dataframe 接收的完整DataFrame，即代表 import_task_total_records_submitted
        total_submitted_to_task = len(df) if df is not None else getattr(import_log, 'import_task_total_records_submitted', 0)
        
        # 这些计数是Task执行到出错前的累计值，可能不完全准确，但聊胜于无
        # 或者，如果这些字段在 import_log 上还未被 _update_import_log_with_results 设置，它们会是0
        current_task_created = getattr(import_log, 'import_task_created_count', 0)
        current_task_updated = getattr(import_log, 'import_task_updated_count', 0)
        current_task_unchanged = getattr(import_log, 'import_task_unchanged_count', 0)
        current_task_processed_successfully = getattr(import_log, 'import_task_processed_successfully_count', 0)
        
        # failed_in_task 应该是总数减去已明确成功的 (不包括在错误行之前的跳过)
        # 注意：这里的统计是在发生异常时进行的，可能不完全代表最终的 ImportLog 状态
        # _update_import_log_with_results 最终会提供更准确的 Task 统计
        failed_in_task_estimation = total_submitted_to_task - current_task_processed_successfully
        if failed_in_task_estimation < 0 : failed_in_task_estimation = total_submitted_to_task # 如果成功数异常

        detailed_error_summary = {
            'task_total_records_submitted_at_error': total_submitted_to_task,
            'task_created_at_error': current_task_created,
            'task_updated_at_error': current_task_updated,
            'task_unchanged_at_error': current_task_unchanged,
            'task_processed_successfully_before_error': current_task_processed_successfully,
            'task_estimated_failed_at_error': failed_in_task_estimation 
        }

        current_detailed_report = import_log.detailed_report if isinstance(import_log.detailed_report, dict) else {}
        summary_in_report = current_detailed_report.get('summary_execution_phase', {})
        summary_in_report.update(detailed_error_summary) # 添加或更新错误时的摘要
        current_detailed_report['summary_execution_phase'] = summary_in_report

        error_details_list = current_detailed_report.get('task_processing_errors_details', [])
        error_details_list.append({
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
        })
        current_detailed_report['task_processing_errors_details'] = error_details_list
        import_log.detailed_report = current_detailed_report
        
        try:
            # 只保存 status, error_log, detailed_report 和 updated_at
            # 其他统计字段由 _update_import_log_with_results 最终设置
            fields_to_update = ['status', 'error_log', 'detailed_report', 'updated_at']
            import_log.save(update_fields=fields_to_update)
        except Exception as e_save:
            logger.error(f"保存ImportLog时出错 (在_handle_dataframe_processing_error中): {e_save}", exc_info=True)

    def _handle_import_error(
        self, error: Exception, import_log: Optional[ImportLog], df: Optional[pd.DataFrame] = None
    ) -> None:
        """
        处理整体导入过程中的错误

        Args:
            error: 捕获的异常
            import_log: 导入日志对象 (可能为None，如果创建日志本身就失败了)
            df: 可选的数据DataFrame
        """
        logger.error(f"导入过程中出错: {str(error)}", exc_info=True)
        
        if import_log is None:
            logger.error(f"ImportLog 对象未能成功创建，无法更新其状态。捕获的错误: {str(error)}")
            return

        import_log.status = "failed"

        # CORRECTED: Combine error message and traceback into error_log
        error_trace = traceback.format_exc()
        import_log.error_log = f"Error: {str(error)}\nTraceback:\n{error_trace}"

        # 保存错误的详细报告
        detailed_error = {
            "error_type": type(error).__name__,
            "error_message": str(error), # Keep original error message here for structured report
            "traceback": error_trace, 
        }

        # Ensure detailed_report is a dict before updating
        if not isinstance(import_log.detailed_report, dict):
            import_log.detailed_report = {}

        import_log.detailed_report.update({
            "summary": {
                "total": len(df) if df is not None else 0,
                "failed": len(df) if df is not None else 0, # Assuming all failed if this handler is called at top level
            },
            "error_details": detailed_error,
        })
        
        import_log.save(update_fields=['status', 'error_log', 'detailed_report', 'updated_at'])

    def _update_import_log_with_results(
        self,
        import_log: ImportLog,
        success_count_from_result: int, 
        error_records: List[Dict],
        skipped_records_in_task: List[Dict], 
        created_count_from_result: int,
        updated_count_from_result: int,
        unchanged_count_from_result: int, 
        total_records_submitted_to_task: int, # 参数名修改为更清晰
    ) -> None:
        """
        根据导入结果更新导入日志中特定于导入Task执行阶段的统计数据。
        此方法由 ExcelImportService.import_from_file 调用。
        """
        import_log.import_task_total_records_submitted = total_records_submitted_to_task # 使用新参数名
        import_log.import_task_created_count = created_count_from_result
        import_log.import_task_updated_count = updated_count_from_result
        import_log.import_task_unchanged_count = unchanged_count_from_result
        import_log.import_task_processed_successfully_count = success_count_from_result 
        import_log.import_task_failed_count = len(error_records)

        if skipped_records_in_task:
            logger.info(f"[ExcelImportService._update_import_log] 导入Task内部跳过了 {len(skipped_records_in_task)} 条记录 (非用户决策)。")
            if import_log.detailed_report and isinstance(import_log.detailed_report, dict):
                task_details_report = import_log.detailed_report.get('import_task_execution_details', {})
                task_details_report['task_internal_skipped_records_info'] = skipped_records_in_task 
                import_log.detailed_report['import_task_execution_details'] = task_details_report

        if error_records:
            current_error_content = import_log.error_log or "" 
            task_error_summary = f"导入Task处理阶段有 {len(error_records)} 条记录导入失败." 
            import_log.error_log = f"{task_error_summary} {current_error_content}".strip() 
            
            current_detailed_report = import_log.detailed_report if isinstance(import_log.detailed_report, dict) else {}
            task_errors_list = current_detailed_report.get('import_task_processing_errors', [])
            task_errors_list.extend(error_records)
            current_detailed_report['import_task_processing_errors'] = task_errors_list
            import_log.detailed_report = current_detailed_report
            
            # 根据错误情况更新 ImportLog 的 status
            if total_records_submitted_to_task > 0: # 使用新参数名
                error_ratio = len(error_records) / total_records_submitted_to_task # 使用新参数名
                if error_ratio == 1.0: # 全部失败
                    import_log.status = "failed"
                elif error_ratio > self.config.ERROR_RATIO_THRESHOLD: # 假设 ERROR_RATIO_THRESHOLD = 0.1
                    import_log.status = "partial"
                else: # 少量失败，仍算完成但有错误
                    import_log.status = "completed" # 或者 "completed_with_errors" 如果有这个状态
            elif len(error_records) > 0: # 没有总记录数但有错误（理论上不应发生）
                 import_log.status = "failed"
            else: # 没有总记录数，也没有错误
                 import_log.status = "completed"
        else:
            import_log.status = "completed" # Task内部无错误，则Task完成
        
        fields_to_save = [
            'import_task_total_records_submitted', 'import_task_created_count', 'import_task_updated_count',
            'import_task_unchanged_count', 'import_task_processed_successfully_count', 'import_task_failed_count',
            'status', 'error_log', 'detailed_report', 'updated_at' 
        ]
        try:
            current_model_fields = [f.name for f in ImportLog._meta.get_fields()]
            fields_to_save_existing = [f for f in fields_to_save if f in current_model_fields]
            if fields_to_save_existing: 
                 import_log.save(update_fields=fields_to_save_existing)
            logger.info(f"[ExcelImportService._update_import_log] ImportLog {import_log.id} 的导入Task阶段统计已更新。Status: {import_log.status}, ProcessedSuccessfully: {import_log.import_task_processed_successfully_count}")
        except Exception as e_save_log:
            logger.error(f"[ExcelImportService._update_import_log] 保存 ImportLog {import_log.id} 时失败: {e_save_log}", exc_info=True)

    def _process_dataframe(
        self,
        df: pd.DataFrame,
        import_log: ImportLog,
        duplicate_strategy: str = DuplicateStrategy.SMART_UPDATE.value,
        existing_records_map: Optional[Dict] = None,
        change_batch: Optional[ChangeLogBatch] = None,
        import_session: Optional[ImportSession] = None
    ) -> ImportResult:
        """
        处理DataFrame并导入数据
        """
        duplicate_strategy = self._validate_duplicate_strategy(duplicate_strategy)
        # log_import_progress(f"使用导入策略: {duplicate_strategy}") # 这条可以保留或酌情移到更前面

        records_to_create = []
        updated_records = []  # 成功更新的记录
        created_records = []  # 成功创建的记录
        error_records = []  # 处理失败的记录
        skipped_records = []  # 被跳过的记录
        unchanged_records = []  # 完全相同记录列表

        success_count = 0

        df_mapped = self._map_dataframe_columns(df)

        # 创建变更批次（如果未提供）
        if change_batch is None:
            change_batch = ChangeLogBatch.objects.create(
                import_log=import_log,
                change_source="excel_import",
                change_reason=f"Excel导入批次 {import_log.batch_number}",
                changed_by=import_log.import_user,
            )

        # 获取已存在记录映射（如果未提供）
        if existing_records_map is None:
            # 提取所有委托编号进行预处理
            commission_numbers = (
                df_mapped["commission_number"]
                .dropna()
                .astype(str)
                .apply(lambda x: x.strip())
                .unique()
                .tolist()
            )

            logger.warning(f"从Excel中提取了 {len(commission_numbers)} 个唯一委托编号")

            # 批量查询已存在的记录
            with performance_tracker("查询现有记录", logger):
                # 精确匹配查询
                existing_records_queryset = ArchiveRecord.objects.filter(
                    commission_number__in=commission_numbers
                )
                
                logger.info(
                    f"在数据库中找到 {existing_records_queryset.count()} 个精确匹配的记录"
                )

                # 创建委托编号到记录的内存映射
                existing_records_map = {
                    record.commission_number.strip(): record
                    for record in existing_records_queryset
                }
                
                # 对于未找到的委托编号，尝试不区分大小写的匹配
                if len(existing_records_map) < len(commission_numbers):
                    missing_numbers = [num for num in commission_numbers if num not in existing_records_map]
                    logger.info(f"尝试宽松匹配 {len(missing_numbers)} 个未找到的委托编号")
                    
                    # 使用Q对象构建OR查询
                    from django.db.models import Q
                    q_filters = Q()
                    for number in missing_numbers:
                        q_filters |= Q(commission_number__iexact=number)
                        
                    if q_filters:
                        loose_matches = ArchiveRecord.objects.filter(q_filters)
                        
                        # 将宽松匹配添加到映射中
                        new_matches = 0
                        for record in loose_matches:
                            # 查找Excel中最接近的委托编号
                            for number in missing_numbers:
                                if record.commission_number.lower() == number.lower():
                                    existing_records_map[number] = record
                                    new_matches += 1
                                    break
                        
                        logger.info(f"通过宽松匹配额外找到 {new_matches} 条记录")

        # 修改批量处理大小
        batch_size = self.config.BATCH_SIZE
        total_batches = (len(df_mapped) + batch_size - 1) // batch_size

        for batch_index in range(total_batches):
            start_idx = batch_index * batch_size
            end_idx = min((batch_index + 1) * batch_size, len(df_mapped))
            batch_df = df_mapped.iloc[start_idx:end_idx]

            # 在事务中处理当前批次
            with transaction.atomic():
                records_to_create = []  # 重置创建列表

                for idx, row in batch_df.iterrows():
                    original_row_idx = idx
                    # 初始化行特定信息以用于错误报告
                    current_sample_number_for_error_reporting = None
                    current_commission_number_for_error_reporting = None
                    try:
                        # 处理行数据
                        record_data = self._process_row(row)
                        current_sample_number_for_error_reporting = record_data.get(
                            "sample_number"
                        )
                        current_commission_number_for_error_reporting = record_data.get(
                            "commission_number"
                        )

                        # 添加导入批次号
                        record_data["batch_number"] = import_log.batch_number
                        record_data["source_system"] = "excel_import"

                        # 添加用户信息
                        if import_log.import_user:
                            record_data["import_user_id"] = import_log.import_user.id

                        # 检查记录是否已存在
                        sample_number = record_data.get("sample_number")
                        commission_number = record_data.get("commission_number")

                        if commission_number:  # 只要有委托编号就可以
                            # 设置统一编号
                            record_data["unified_number"] = commission_number

                            # 规范化委托编号
                            original_commission_number = commission_number
                            commission_number = str(commission_number).strip()

                            # 使用缓存查找记录而不是每次查询数据库
                            existing_record = existing_records_map.get(
                                commission_number
                            )

                            # 额外调试信息
                            if existing_record:
                                logger.debug(
                                    f"从缓存找到现有记录: ID={existing_record.id}, 委托编号='{existing_record.commission_number}'"
                                )
                            else:
                                logger.debug(
                                    f"未找到委托编号='{commission_number}'的记录"
                                )

                            if existing_record:
                                if duplicate_strategy == "skip":
                                    # 跳过已存在的记录
                                    skipped_records.append(
                                        {
                                            "row": original_row_idx
                                            + 2,  # Excel行号从1开始，加上标题行
                                            "reason": "duplicate_record",
                                            "sample_number": sample_number,
                                            "commission_number": commission_number,
                                            "record_id": existing_record.id,  # 存储ID而不是对象
                                        }
                                    )
                                    continue

                                elif duplicate_strategy == "append":
                                    # 追加策略 - 创建新记录
                                    # print(f"策略为append，创建新记录而不是更新现有记录")
                                    new_record = ArchiveRecord(**record_data)
                                    records_to_create.append(new_record)
                                    continue

                                elif duplicate_strategy in [
                                    "smart_update",
                                    "overwrite",
                                ]:
                                    # 检查是否有变化
                                    changes = {}
                                    has_changes = False

                                    # 智能更新模式下，只更新有变化的非空字段
                                    if duplicate_strategy == "smart_update":
                                        for field, value in record_data.items():
                                            # 跳过系统自动生成的字段
                                            if field in (
                                                "id",
                                                "pk",
                                                "batch_number",
                                                "source_system",
                                            ) or field.endswith("_id"):
                                                continue

                                            # 获取现有记录的字段值
                                            existing_value = getattr(
                                                existing_record, field, None
                                            )

                                            # 处理None和空字符串
                                            if (
                                                value == "" and existing_value is None
                                            ) or (
                                                value is None and existing_value == ""
                                            ):
                                                continue  # 视为相等

                                            # 对于字符串字段，不进行截断，只记录超长的情况
                                            if (
                                                isinstance(value, str)
                                                and field in self.string_field_limits
                                            ):
                                                max_length = (
                                                    self.string_field_limits.get(
                                                        field, 2000
                                                    )
                                                )
                                                if len(value) > max_length:
                                                    logger.warning(
                                                        f"字段 {field} 值长度 ({len(value)}) 超过字典定义最大长度 ({max_length})，不进行截断，保留原始数据。"
                                                    )

                                            # 对于数值字段，转换为浮点数比较
                                            if (
                                                field in self.NUMERIC_FIELDS
                                                and value is not None
                                                and existing_value is not None
                                            ):
                                                try:
                                                    # 转换为浮点数进行比较
                                                    value_float = (
                                                        float(value)
                                                        if value != ""
                                                        else 0.0
                                                    )
                                                    existing_float = (
                                                        float(existing_value)
                                                        if existing_value != ""
                                                        else 0.0
                                                    )
                                                    # 使用近似相等比较，处理浮点数精度问题
                                                    if (
                                                        abs(
                                                            value_float - existing_float
                                                        )
                                                        < 0.001
                                                    ):  # 允许0.001的误差
                                                        continue  # 数值相等，不需要更新
                                                except (ValueError, TypeError):
                                                    # 转换失败，回退到字符串比较
                                                    pass

                                            # 处理日期字段 - CHANGE: [2025-06-03] 修复时区比较问题
                                            if (
                                                field in self.DATE_FIELDS
                                                and value is not None
                                                and existing_value is not None
                                            ):
                                                # CHANGE: [2025-06-03] 使用统一的时间比较方法
                                                if self._compare_datetime_values(value, existing_value, field):
                                                    continue  # 时间相等，不需要更新
                                            # 如果时间不相等，继续执行后续的字符串比较逻辑

                                            # 常规字符串比较 - 清理并规范化
                                            str_value = (
                                                str(value).strip()
                                                if value is not None
                                                else ""
                                            )
                                            str_existing = (
                                                str(existing_value).strip()
                                                if existing_value is not None
                                                else ""
                                            )

                                            # 对于数值字段，再次尝试标准化比较
                                            if field in self.NUMERIC_FIELDS:
                                                # 移除尾随零和小数点
                                                str_value = (
                                                    str_value.rstrip("0").rstrip(".")
                                                    if "." in str_value
                                                    else str_value
                                                )
                                                str_existing = (
                                                    str_existing.rstrip("0").rstrip(".")
                                                    if "." in str_existing
                                                    else str_existing
                                                )

                                            # 最终比较
                                            if str_value != str_existing:
                                                changes[field] = value
                                                has_changes = True
                                                # 获取当前记录的委托编号，用于日志打印
                                                # commission_number_for_log 通常在处理每行数据 (row) 开始时从 record_data 中获取并存储在局部变量中
                                                # 例如 current_commission_number_for_error_reporting 或直接从 record_data 获取
                                                # 此处假设 record_data 在当前作用域可用，并且包含了 commission_number
                                                # current_commission_no = record_data.get('commission_number', '未知委托编号')
                                                # print(
                                                #     f"委托编号: {current_commission_no} - 检测到字段 {field} 变更: '{existing_value}' -> '{value}'"
                                                # )
                                    else:  # overwrite模式，更新所有导入的字段
                                        for field, value in record_data.items():
                                            if (
                                                getattr(existing_record, field, None)
                                                != value
                                            ):
                                                changes[field] = value
                                                has_changes = True

                                    # 在完成所有字段比较后
                                    if not changes:
                                        has_changes = False  # 确保一致性

                                    if has_changes:
                                        # 有变化，需要更新
                                        try:
                                            # 在进行记录更新前，验证记录是否已被其他进程修改
                                            existing_record = (
                                                self._verify_and_refresh_record(
                                                    existing_record, commission_number
                                                )
                                            )

                                            # 如果检测到记录已更新，重新对比数据
                                            # 重新检查变更，因为记录可能已被更新
                                            actual_changes = {}
                                            for field, value in changes.items():
                                                # 获取最新记录的字段值
                                                current_value = getattr(
                                                    existing_record, field, None
                                                )
                                                # 仅当值不同时才添加到实际变更
                                                if current_value != value:
                                                    actual_changes[field] = value

                                            # 使用实际需要变更的字段
                                            changes = actual_changes
                                            has_changes = bool(actual_changes)

                                            # 如果仍有需要更新的字段
                                            if not has_changes:
                                                # 添加到未改变列表
                                                unchanged_records.append(
                                                    {
                                                        "row": original_row_idx + 2,
                                                        "sample_number": sample_number,
                                                        "commission_number": commission_number,
                                                        "record_id": existing_record.id,
                                                    }
                                                )
                                                success_count += 1  # 仍然计为成功处理
                                                continue

                                            # 更新记录
                                            for field, value in changes.items():
                                                # 确保字段值不超过数据库限制
                                                if (
                                                    isinstance(value, str)
                                                    and field
                                                    in self.string_field_limits
                                                ):
                                                    max_length = (
                                                        self.string_field_limits.get(
                                                            field, 2000
                                                        )
                                                    )
                                                    if len(value) > max_length:
                                                        logger.warning(
                                                            f"字段 {field} 值长度 ({len(value)}) 超过字典定义最大长度 ({max_length})，不进行截断，保留原始数据。"
                                                        )
                                                setattr(existing_record, field, value)

                                                # 在保存前再次检查所有字段
                                                for (
                                                    field_name
                                                ) in existing_record.__dict__:
                                                    # 跳过主键、外键关联字段和Django内部字段
                                                    if (
                                                        field_name.startswith("_")
                                                        or field_name == "id"
                                                        or field_name.endswith("_id")
                                                    ):
                                                        continue

                                                    field_value = getattr(
                                                        existing_record,
                                                        field_name,
                                                        None,
                                                    )
                                                    if isinstance(field_value, str):
                                                        max_length = self.string_field_limits.get(
                                                            field_name, 2000
                                                        )
                                                        if (
                                                            len(field_value)
                                                            > max_length
                                                        ):
                                                            logger.warning(
                                                                f"字段 {field_name} 值长度 ({len(field_value)}) 超过字典定义的最大长度 ({max_length})，不进行截断，保留原始数据。"
                                                            )
                                            # logger.info(f"[EXCEL_SAVE_DEBUG] 即将保存记录. 委托编号: {commission_number}, Excel行号: {original_row_idx + 2}, 当前记录ID: {existing_record.id if existing_record else 'N/A'}")
                                            existing_record.save()

                                            # 更新缓存中的记录 - 使用保存后的最新版本
                                            existing_records_map[
                                                commission_number
                                            ] = existing_record

                                            # 记录变更后的完整状态
                                            record_after = {}
                                            try:
                                                for (
                                                    field
                                                ) in existing_record._meta.fields:
                                                    if (
                                                        not field.primary_key
                                                        and field.name != "id"
                                                    ):
                                                        field_value = getattr(
                                                            existing_record,
                                                            field.name,
                                                        )
                                                        record_after[field.name] = (
                                                            str(field_value)
                                                            if field_value
                                                            is not None
                                                            else None
                                                        )
                                            except Exception as e:
                                                # print(
                                                #     f"获取记录状态时出错: {str(e)}"
                                                # )
                                                # 优雅地处理错误，防止字符串索引错误
                                                record_after = {
                                                    field.name: "获取失败"
                                                    for field in existing_record._meta.fields
                                                    if not field.primary_key
                                                    and field.name != "id"
                                                }

                                            # 查找当前记录的最高版本号
                                            next_version = (
                                                self._get_next_version_number(
                                                    existing_record
                                                )
                                            )

                                            # 创建记录级变更日志
                                            self.create_record_change_log(
                                                existing_record,
                                                change_batch,
                                                next_version,
                                                "update",
                                                None,
                                                field_changes=changes,
                                            )

                                            # 添加到更新列表
                                            updated_records.append(
                                                {
                                                    "row": original_row_idx + 2,
                                                    "id": existing_record.id,
                                                    "sample_number": sample_number,
                                                    "commission_number": commission_number,
                                                }
                                            )

                                            success_count += 1
                                        except (
                                            DatabaseError
                                        ) as db_err_update:  # Django DB errors
                                            # CHANGE: [2025-06-03] 修复 django.db.Error 为正确的 DatabaseError
                                            logger.warning(
                                                f"[数据库错误] 更新记录时发生数据库错误 (记录ID: {existing_record.id}, 行号: {original_row_idx + 2}): {db_err_update}",
                                                exc_info=True
                                            )
                                            raise  # Re-raise the exception to trigger transaction rollback
                                        except (
                                            Exception
                                        ) as e:  # Other non-DB errors during update
                                            # 记录错误
                                            logger.error(
                                                f"更新记录 {current_commission_number_for_error_reporting or 'N/A'} 时发生非数据库错误 (Excel行 {original_row_idx + 2}): {e}",
                                                exc_info=True,
                                            )
                                            error_records.append(
                                                {
                                                    "row": original_row_idx + 2,
                                                    "code": "UPDATE_ERROR",  # Kept original code for this type
                                                    "message": f"更新记录时出错: {str(e)}",
                                                    "detail": str(e),
                                                    "field": "multiple_fields",
                                                    "sample_number": current_sample_number_for_error_reporting,
                                                    "commission_number": current_commission_number_for_error_reporting,
                                                    "record_id": existing_record.id,
                                                }
                                            )
                                            # Non-DB errors here might not need to fail the batch.
                                    else:
                                        # 没有变化，添加到未改变列表
                                        unchanged_records.append(
                                            {
                                                "row": original_row_idx + 2,
                                                "sample_number": sample_number,
                                                "commission_number": commission_number,
                                                "record_id": existing_record.id,
                                            }
                                        )
                                        success_count += 1  # 仍然计为成功处理

                            else:
                                # print(
                                #     f"未找到委托编号为 '{commission_number}' 的记录，将创建新记录"
                                # )
                                # 创建新记录
                                new_record = ArchiveRecord(**record_data)
                                records_to_create.append(new_record)

                        else:
                            # 缺少关键字段，记录错误
                            missing = []
                            if not commission_number:
                                missing.append("委托编号")
                            if not sample_number:
                                missing.append("样品编号")
                            error_msg = f"缺少必要字段: {', '.join(missing)}"
                            error_records.append(
                                {
                                    "row": original_row_idx + 2,
                                    "code": "MISSING_REQUIRED_FIELD",
                                    "message": error_msg,
                                    "detail": f"缺少字段: {', '.join(missing)}",
                                    "field": ", ".join(missing),
                                    "sample_number": sample_number,
                                    "commission_number": commission_number,
                                    "record_id": None,
                                }
                            )

                    except (
                        DatabaseError
                    ) as db_row_err:  # Catch DB errors from row processing (e.g., _get_next_version_number)
                        logger.error(
                            f"处理行数据时发生数据库错误 (行 {original_row_idx + 2}, 委托编号: {current_commission_number_for_error_reporting or 'N/A'}): {db_row_err}",
                            exc_info=True,
                        )
                        error_records.append(
                            {
                                "row": original_row_idx + 2,
                                "code": "ROW_DB_ERROR",
                                "message": f"行处理数据库错误: {str(db_row_err)}",
                                "detail": str(db_row_err),
                                "sample_number": current_sample_number_for_error_reporting,
                                "commission_number": current_commission_number_for_error_reporting,
                                "record_id": None,  # record_id might not be known if error is early
                            }
                        )
                        raise  # Re-throw DB error to roll back batch transaction
                    except (
                        ValueError
                    ) as val_err:  # Catch validation errors from _process_row or similar data issues
                        # 尝试从异常对象获取委托编号，或从原始行数据解析
                        commission_number_from_error = getattr(val_err, 'commission_number', None)
                        if commission_number_from_error:
                            current_commission_number_for_error_reporting = commission_number_from_error
                        else:
                            # 尝试从原始行数据中直接解析委托编号
                            try:
                                # 从row中获取委托编号字段
                                for field_name, mapped_name in self.field_mapping.items():
                                    if mapped_name == 'commission_number' and field_name in row.index:
                                        raw_commission = row[field_name]
                                        if pd.notna(raw_commission):
                                            current_commission_number_for_error_reporting = str(raw_commission).strip()
                                            break
                            except Exception:
                                pass  # 如果解析失败，保持为None
                        
                        logger.warning(
                            f"处理行数据时发生数据验证/处理错误 (行 {original_row_idx + 2}, 委托编号: {current_commission_number_for_error_reporting or 'N/A'}): {val_err}",
                            exc_info=False,
                        )
                        error_records.append(
                            {
                                "row": original_row_idx + 2,
                                "code": "ROW_VALIDATION_PROCESSING_ERROR",
                                "message": f"行数据验证或处理错误: {str(val_err)}",
                                "detail": str(val_err),
                                "sample_number": current_sample_number_for_error_reporting,
                                "commission_number": current_commission_number_for_error_reporting,
                                "record_id": None,
                            }
                        )
                        # Continue to the next row, batch transaction should be okay
                    except (
                        Exception
                    ) as e:  # Catch other unexpected non-DB, non-ValueError for the row
                        # 记录错误但不影响整体流程
                        logger.error(
                            f"处理单行数据时发生未预料的非数据库/非验证错误 (行 {original_row_idx + 2}, 委托编号: {current_commission_number_for_error_reporting or 'N/A'}): {e}",
                            exc_info=True,
                        )
                        error_records.append(
                            {
                                "row": original_row_idx + 2,
                                "code": "PROCESSING_ERROR",
                                "message": f"处理记录时出错: {str(e)}",
                                "detail": str(e),
                                "field": "unknown",
                                "sample_number": (
                                    current_sample_number_for_error_reporting
                                    if current_sample_number_for_error_reporting
                                    is not None
                                    else (
                                        sample_number
                                        if "sample_number" in locals()
                                        else None
                                    )
                                ),
                                "commission_number": (
                                    current_commission_number_for_error_reporting
                                    if current_commission_number_for_error_reporting
                                    is not None
                                    else (
                                        commission_number
                                        if "commission_number" in locals()
                                        else None
                                    )
                                ),
                            }
                        )

                # 修改批量创建逻辑
                if records_to_create:
                    try:
                        # 第一次尝试批量创建
                        created_batch = self._safe_bulk_create(records_to_create)
                        created_records.extend(created_batch)

                        # CHANGE: [2024-07-25] 更新内存缓存以包含新创建的记录
                        for new_record in created_batch:
                            if (
                                hasattr(new_record, "commission_number")
                                and new_record.commission_number
                            ):
                                existing_records_map[
                                    new_record.commission_number.strip()
                                ] = new_record

                        # 为新创建的记录创建变更日志
                        for new_record in created_batch:
                            # 记录快照
                            record_after = {
                                field.name: str(getattr(new_record, field.name))
                                for field in new_record._meta.fields
                                if not field.primary_key and field.name != "id"
                            }

                            # 创建记录级变更日志
                            self.create_record_change_log(
                                new_record, change_batch, 1, "create", None, None
                            )
                            success_count += 1

                    except DatabaseError as db_err_bulk:  # Django DB errors
                        logger.error(
                            f"批量创建数据库错误 (批次 {batch_index + 1}/{total_batches}): {db_err_bulk}",
                            exc_info=True,
                        )
                        # 记录错误但继续处理
                        error_records.extend(
                            [
                                {
                                    "row": start_idx + i + 2,  # Approximate row
                                    "code": "DB_BATCH_CREATE_ERROR",  # Changed code
                                    "message": f"数据库批量创建记录时出错: {str(db_err_bulk)}",
                                    "detail": str(db_err_bulk),
                                    "field": "multiple_fields",
                                    "sample_number": (
                                        record.sample_number
                                        if hasattr(record, "sample_number")
                                        else None
                                    ),
                                    "commission_number": (
                                        record.commission_number
                                        if hasattr(record, "commission_number")
                                        else None
                                    ),
                                }
                                for i, record in enumerate(records_to_create)
                            ]
                        )
                        raise  # RE-RAISE DB error
                    except Exception as e:  # Other non-DB errors during bulk create
                        logger.error(
                            f"批次 {batch_index + 1}/{total_batches} 创建时发生非数据库错误: {str(e)}",
                            exc_info=True,
                        )
                        # 记录错误但继续处理
                        error_records.extend(
                            [
                                {
                                    "row": start_idx + i + 2,
                                    "code": "BATCH_CREATE_ERROR",  # Kept original code
                                    "message": f"批量创建记录时出错: {str(e)}",
                                    "detail": str(e),
                                    "field": "multiple_fields",
                                    "sample_number": (
                                        record.sample_number
                                        if hasattr(record, "sample_number")
                                        else None
                                    ),
                                    "commission_number": (
                                        record.commission_number
                                        if hasattr(record, "commission_number")
                                        else None
                                    ),
                                }
                                for i, record in enumerate(records_to_create)
                            ]
                        )
                        # Non-DB errors here might not need to fail the batch unless _safe_bulk_create implies it should.
                        # If _safe_bulk_create raises a non-DB exception after exhausting retries,
                        # it might be severe enough to warrant failing the batch (i.e. by re-raising here too).
                        # For now, only DB errors are re-raised from this specific block.

                # 更新处理进度
                # import_log.processed_records = end_idx # import_log 的 processed_records 可能含义不同，我们直接更新 session
                # import_log.save(update_fields=["processed_records"])
                # CHANGE: [2025-05-18] 更新 ImportSession 进度
                if import_session: 
                    initial_status_for_batch = import_session.status
                    if initial_status_for_batch == ImportSessionStatus.IMPORT_START:
                        import_session.status = ImportSessionStatus.IMPORT_IN_PROGRESS
                    
                    processed_in_batch = end_idx - start_idx
                    current_processed_total_for_stage = (import_session.current_record or 0) + processed_in_batch
                    import_session.current_record = current_processed_total_for_stage
                    
                    total_for_this_import_stage = import_session.record_count 
                    
                    # 细化进度计算：将当前阶段的0-100%映射到总进度的5%-95%
                    IMPORT_START_PROGRESS = 5.0
                    IMPORT_IN_PROGRESS_MAX_REACHABLE_PROGRESS = 95.0 # 在最终汇总前能达到的最大进度
                    PROGRESS_RANGE_FOR_PROCESSING_STAGE = IMPORT_IN_PROGRESS_MAX_REACHABLE_PROGRESS - IMPORT_START_PROGRESS # 例如 90%

                    current_stage_completion_ratio = 0.0 # (0.0 to 1.0)
                    if total_for_this_import_stage > 0:
                        current_stage_completion_ratio = current_processed_total_for_stage / total_for_this_import_stage
                    elif current_processed_total_for_stage == 0 and total_for_this_import_stage == 0: 
                        current_stage_completion_ratio = 1.0 # 如果总数和已处理都为0，视为完成本阶段

                    overall_progress = IMPORT_START_PROGRESS + (current_stage_completion_ratio * PROGRESS_RANGE_FOR_PROCESSING_STAGE)
                    import_session.progress = min(round(overall_progress, 2), IMPORT_IN_PROGRESS_MAX_REACHABLE_PROGRESS)
                    
                    log_status_message = f"正在导入数据: {current_processed_total_for_stage}/{total_for_this_import_stage} (总进度约 {import_session.progress}%)"
                    if current_stage_completion_ratio >= 1.0: # 意味着当前阶段数据处理完毕
                        import_session.progress = IMPORT_IN_PROGRESS_MAX_REACHABLE_PROGRESS # 确保达到95%
                        log_status_message = f"数据导入处理完成 ({current_processed_total_for_stage}/{total_for_this_import_stage})，等待最后汇总 (总进度 {import_session.progress}%)"
                    
                    fields_to_update = ['current_record', 'progress', 'updated_at']
                    if initial_status_for_batch == ImportSessionStatus.IMPORT_START and import_session.status == ImportSessionStatus.IMPORT_IN_PROGRESS:
                        if 'status' not in fields_to_update:
                             fields_to_update.append('status')
                    
                    try:
                        import_session.save(update_fields=fields_to_update)
                        logger.info(f"[ExcelImportService._process_dataframe] 导入会话 {import_session.session_id} 进度更新: "
                                    f"本阶段已处理 {current_processed_total_for_stage}/{total_for_this_import_stage}. "
                                    f"总进度 {import_session.progress}% (状态: {import_session.status}, 日志消息: {log_status_message})")
                    except Exception as e_save_progress:
                        logger.error(f"[ExcelImportService._process_dataframe] 更新导入会话 {import_session.session_id} 进度时失败: {e_save_progress}", exc_info=True)
                # END CHANGE

        # 更新变更批次统计信息
        change_batch.affected_records_count = len(created_records) + len(
            updated_records
        )
        change_batch.summary = {
            "created": len(created_records),
            "updated": len(updated_records),
            "skipped": len(skipped_records),
            "failed": len(error_records),
            "unchanged": len(unchanged_records),
        }
        change_batch.save()

        # 打印处理结果
        log_import_progress(f"\n导入完成，结果统计:")
        log_import_progress(f"总记录数: {len(df)}")
        log_import_progress(f"成功导入: {success_count} 条记录")
        log_import_progress(f"创建记录: {len(created_records)} 条")
        log_import_progress(f"更新记录: {len(updated_records)} 条")
        log_import_progress(f"完全相同记录: {len(unchanged_records)} 条")
        log_import_progress(f"用户手动跳过记录: {len(skipped_records)} 条")
        log_import_progress(f"有 {len(df) - success_count} 条记录导入失败")

        # 更新导入日志的详细报告
        detailed_report = {
            "summary": {
                "total": len(df),
                "created": len(created_records),
                "updated": len(updated_records),
                "unchanged": len(unchanged_records),  # 完全相同记录
                "skipped_by_user": len(skipped_records),  # 用户选择跳过的记录
                "failed": len(error_records),
            },
            "created": [
                {
                    "row": rec["row"] if isinstance(rec, dict) else "?",
                    "record_id": (
                        rec["id"] if isinstance(rec, dict) else getattr(rec, "id", "?")
                    ),
                }
                for rec in created_records
            ],
            "updated": [
                {
                    "row": rec["row"] if isinstance(rec, dict) else "?",
                    "record_id": (
                        rec["id"] if isinstance(rec, dict) else getattr(rec, "id", "?")
                    ),
                }
                for rec in updated_records
            ],
            "unchanged": [
                {"row": rec["row"], "commission_number": rec["commission_number"]}
                for rec in unchanged_records
            ],
            "skipped": [
                {"row": rec["row"], "reason": rec["reason"]} for rec in skipped_records
            ],
            "errors": error_records,
        }
        import_log.detailed_report = detailed_report
        import_log.created_count = len(created_records)
        import_log.updated_count = len(updated_records)

        # CHANGE: [2024-07-26] 更新统计逻辑，调整字段含义
        # system_skipped_records: 只统计完全相同的条目（系统自动识别无需更新）
        import_log.system_skipped_records = len(unchanged_records)
        # user_manual_skipped_records: 用户手动选择跳过的记录（策略为'skip'时）
        import_log.user_manual_skipped_records = len(skipped_records)
        # total_skipped_records: 系统跳过+用户手动跳过
        import_log.total_skipped_records = (
            import_log.system_skipped_records + import_log.user_manual_skipped_records
        )

        # 设置完全相同记录数
        import_log.unchanged_count = len(unchanged_records)
        import_log.failed_records = len(error_records)
        import_log.save()

        # 创建并返回导入结果对象
        created_count = len(created_records)
        updated_count = len(updated_records)
        unchanged_count = len(unchanged_records)

        return ImportResult(
            success_count=success_count,
            error_records=error_records,
            skipped_records=skipped_records,
            created_count=created_count,
            updated_count=updated_count,
            unchanged_count=unchanged_count,
        )

    def _map_dataframe_columns(self, df):
        """
        将DataFrame的列名映射为模型字段名，并进行基本的数据清洗

        Args:
            df (DataFrame): 原始DataFrame

        Returns:
            DataFrame: 处理后的DataFrame
        """
        # 创建映射字典，从Excel列名到模型字段名
        column_mapping = {}
        for col in df.columns:
            if col in self.field_mapping:
                column_mapping[col] = self.field_mapping[col]

        # 应用列映射
        df_mapped = df.rename(columns=column_mapping)

        # 只保留映射到模型字段的列
        valid_columns = [
            col for col in df_mapped.columns if col in self.field_mapping.values()
        ]
        df_mapped = df_mapped[valid_columns]

        # 添加：规范化委托编号
        if "commission_number" in df_mapped.columns:
            df_mapped["commission_number"] = df_mapped["commission_number"].apply(
                lambda x: str(x).strip() if pd.notna(x) else x
            )

        return df_mapped

    def _process_row(self, row):
        """处理单行数据，进行类型转换和数据清洗"""
        processed_data = {}

        # 记录已处理的字段，用于检查必填字段
        processed_fields = set()

        # 中文字段名到英文字段的映射
        cn_to_en = {cn: en for cn, en in self.field_mapping.items()}

        # 验证必填字段
        required_fields = {
            "commission_number": "委托编号",
            "sample_number": "样品编号",
            "client_unit": "委托单位",
            "commission_datetime": "委托日期",
            "project_name": "工程名称",
        }
        # 英文到中文的反向映射
        en_to_cn = {v: k for k, v in required_fields.items()}

        # 字段长度限制 - 根据数据库模型定义添加
        string_field_limits = {
            "commission_number": 2000,
            "sample_number": 2000,
            "account_from_excel": 2000,
            "report_number": 2000,
            "province_unified_number": 2000,
            "station_code": 2000,
            "organization_code": 2000,
            "project_number": 2000,
            "project_name": 2000,
            "sub_project": 2000,
            "project_location": 2000,
            "project_address": 2000,
            "client_unit": 2000,
            "client_name": 2000,
            "test_person1": 2000,
            "test_person2": 2000,
            "data_entry_person": 2000,
            "test_result": 2000,
            "conclusion": 2000,
            "test_parameters": 2000,
            "unqualified_parameters": 2000,
            "archive_status": 2000,
            "current_status": 2000,
            "processing_status": 2000,
            "archive_box_number": 2000,
            "archive_url": 2000,
            "attachments_from_excel": 2000,
            "storage_person": 2000,
            "outbound_person": 2000,
            "archive_person": 2000,
            "report_issue_status": 2000,
            "first_issue_person": 2000,
            "first_receiver_name": 2000,
            "group_number": 2000,
            "sample_name": 2000,
            "assigned_person": 2000,
            "payment_status": 2000,
            "price_adjustment_status": 2000,
        }

        # 处理所有字段
        for field, value in row.items():
            # 跳过非映射字段
            if field not in self.field_mapping.values():
                continue

            # 跳过NaN值，但不跳过必填字段
            if pd.isna(value):
                # 检查这是否是必填字段
                if field in required_fields.values():
                    cn_field = en_to_cn.get(field)
                    if cn_field:
                        raise ValueError(f"必填字段不能为空: {cn_field}")
                processed_data[field] = None
                processed_fields.add(field)
                continue

            # 处理不同类型的字段
            if field in self.DATE_FIELDS:
                # 日期字段处理 - CHANGE: [2025-06-20] 使用现代化的时区处理器
                try:
                    # 使用新的TimezoneHandler解析Excel时间
                    from archive_records.utils.datetime_utils import TimezoneHandler
                    
                    # 解析Excel时间为本地时区感知时间
                    local_aware_dt = TimezoneHandler.parse_excel_datetime(value)
                    if local_aware_dt is not None:
                        # 转换为UTC时间存储到数据库
                        utc_dt = TimezoneHandler.convert_to_utc(local_aware_dt)
                        processed_data[field] = utc_dt
                        logger.debug(f"时间字段处理 - {field}: {value} -> {local_aware_dt} -> {utc_dt}")
                    else:
                        processed_data[field] = None
                except Exception as e:
                    if field == "commission_datetime":  # 只有委托日期是必填的
                        raise ValueError(f"委托日期格式无效: {value}")
                    processed_data[field] = None
            elif field in self.NUMERIC_FIELDS:
                # 数值字段处理 - 增强版，确保固定格式
                processed_data[field] = self._parse_numeric_with_format(value, field)
            else:
                # 字符串字段处理
                str_value = str(value).strip() if value is not None else None

                # 特殊处理委托编号
                if field == "commission_number" and str_value:
                    # 规范化委托编号 - 去除前后空格
                    str_value = str_value.strip()

                # 检查字符串字段长度是否超过限制
                if str_value and field in string_field_limits:
                    max_length = string_field_limits[field]
                    if len(str_value) > max_length:
                        # 获取字段的中文名称用于错误提示
                        field_cn = None
                        for cn, en in self.field_mapping.items():
                            if en == field:
                                field_cn = cn
                                break
                        cn_display = f"{field_cn}({field})" if field_cn else field

                        # 记录超长字段详情用于日志
                        logger.warning(
                            f"字段值超过字典定义最大长度: {cn_display}，最大长度: {max_length}，实际长度: {len(str_value)}，值: '{str_value[:50]}...'"
                        )

                        # 不再截断，数据库字段已改为2000字符
                        # 如果将来实际字段值长度超过数据库限制，将在保存时由Django处理并记录到错误日志

                if field in required_fields.values() and not str_value:
                    cn_field = en_to_cn.get(field)
                    if cn_field:
                        raise ValueError(f"必填字段不能为空: {cn_field}")
                processed_data[field] = str_value

            processed_fields.add(field)

        # 检查所有必填字段是否都存在
        missing_fields = []
        for en_field, cn_field in required_fields.items():
            en_field_mapped = next((f for f in processed_fields if f == en_field), None)
            if not en_field_mapped or processed_data.get(en_field_mapped) is None:
                missing_fields.append(cn_field)

        if missing_fields:
            # 获取已经解析出的委托编号用于错误报告
            commission_number = processed_data.get('commission_number')
            error_msg = f"缺少必填字段: {', '.join(missing_fields)}"
            
            # 创建包含委托编号信息的异常
            error = ValueError(error_msg)
            if commission_number:
                error.commission_number = commission_number
            raise error

        return processed_data

    def _parse_numeric_with_format(self, value, field):
        """
        解析数值字段，并确保按照固定格式存储

        Args:
            value: 输入值
            field: 字段名

        Returns:
            解析后的数值，按照固定格式的字符串
        """
        try:
            # 如果是字符串，尝试转换前预处理
            if isinstance(value, str):
                # 移除货币符号、逗号等
                value = value.replace(",", "").replace("¥", "").replace("$", "").strip()

            # 检查是否为空值
            if value is None or (isinstance(value, str) and not value):
                # 针对不同数值字段返回适当的默认值
                if field in ["standard_price", "discount_price", "actual_price"]:
                    return "0.00"  # 价格字段返回两位小数字符串
                else:
                    return "0"  # 其他字段默认为整数字符串

            # 转换并格式化为特定格式
            if field in ["standard_price", "discount_price", "actual_price"]:
                # 价格字段: 转换为浮点数后格式化为两位小数字符串
                float_value = float(value)
                return f"{float_value:.2f}"  # 始终返回两位小数的字符串
            elif field in [
                "group_number",
                "component_count",
                "test_point_count",
                "unqualified_point_count",
                "total_issue_copies",
                "change_count",
            ]:
                # 整数字段: 转换为整数后返回字符串
                int_value = int(float(value))
                return str(int_value)  # 返回整数字符串 (如 "5")
            elif field == "sample_remaining_time":
                # 剩余时间: 可以为小数，但通常是整数
                float_value = float(value)
                if float_value == int(float_value):
                    return str(int(float_value))  # 整数值，返回整数字符串 (如 "30")
                else:
                    return f"{float_value:.1f}"  # 保留一位小数 (如 "30.5")
            else:
                # 其他数值字段: 尝试判断是整数还是小数
                float_value = float(value)
                if float_value == int(float_value):
                    return str(int(float_value))  # 整数值返回整数字符串
                else:
                    # 检查小数位数，保留实际小数位数但最多两位
                    decimal_str = str(float_value).split(".")
                    if len(decimal_str) > 1 and len(decimal_str[1]) > 0:
                        decimal_places = min(len(decimal_str[1]), 2)  # 最多两位小数
                        format_str = f"{{:.{decimal_places}f}}"
                        return format_str.format(float_value)
                    return str(float_value)

        except (ValueError, TypeError) as e:
            logger.warning(f"无法解析数值: {value} (字段: {field}), 错误: {e}")
            # 出错时返回默认值字符串
            if field in ["standard_price", "discount_price", "actual_price"]:
                return "0.00"
            else:
                return "0"

    def _calculate_file_hash(self, file_path):
        """
        计算文件的MD5哈希值

        Args:
            file_path: 文件路径

        Returns:
            str: MD5哈希值
        """
        md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5.update(chunk)
        return md5.hexdigest()

    def _generate_batch_number(self):
        return uuid.uuid4().hex

    def _get_field_importance(self, field_name):
        """获取字段的重要性级别"""
        return self.FIELD_IMPORTANCE.get(field_name, "normal")

    def _get_field_label(self, field_name):
        """获取字段的中文标签"""
        # 反向查找字段映射以获取中文标签
        for excel_column, model_field in self.field_mapping.items():
            if model_field == field_name:
                return excel_column
        return field_name  # 如果找不到映射，返回字段名

    def _get_next_version_number(self, record):
        """获取记录的下一个版本号"""
        latest_change = (
            RecordChangeLog.objects.filter(record=record)
            .order_by("-version_number")
            .first()
        )

        if latest_change:
            return latest_change.version_number + 1
        return 1  # 如果没有变更记录，则从1开始

    def create_record_change_log(
        self,
        record,
        batch,
        version_number,
        change_type,
        record_before=None,
        field_changes=None,
        is_rollback=False,
        rollback_source_version=None,
    ):
        """创建记录级变更日志

        Args:
            record: 档案记录实例
            batch: 变更批次
            version_number: 版本号
            change_type: 变更类型 ('create', 'update', 'delete', 'rollback')
            record_before: 变更前的状态 (可选)
            field_changes: 变更字段字典 {field_name: new_value} (可选)
            is_rollback: 是否为回滚操作
            rollback_source_version: 回滚源版本号

        Returns:
            RecordChangeLog: 创建的变更日志实例
        """
        # 内部转换为列表格式
        changes_list = []
        if field_changes and isinstance(field_changes, dict):
            for field, value in field_changes.items():
                old_value = (
                    getattr(record, field, None)
                    if record_before is None
                    else record_before.get(field, None)
                )
                changes_list.append(
                    {
                        "field": field,
                        "field_label": self._get_field_label(field),
                        "old_value": old_value,
                        "new_value": value,
                    }
                )
        elif field_changes and isinstance(field_changes, list):
            changes_list = field_changes

        # 记录变更后的完整状态
        record_after = {}
        try:
            for field in record._meta.fields:
                if not field.primary_key and field.name != "id":
                    field_value = getattr(record, field.name)
                    record_after[field.name] = (
                        str(field_value) if field_value is not None else None
                    )
        except Exception as e:
            print(f"获取记录状态时出错: {str(e)}")
            # 优雅地处理错误，防止字符串索引错误
            record_after = {
                field.name: "获取失败"
                for field in record._meta.fields
                if not field.primary_key and field.name != "id"
            }

        # 创建记录级变更日志
        record_change = RecordChangeLog.objects.create(
            batch=batch,
            record=record,
            version_number=version_number,
            change_type=change_type,
            record_before=record_before,
            record_after=record_after,
            changed_fields_count=len(changes_list),
            is_rollback=is_rollback,
            rollback_source_version=rollback_source_version,
        )

        # 如果提供了变更字段列表，创建字段级变更日志
        if changes_list:
            field_logs = []
            for change in changes_list:
                if not isinstance(change, dict):
                    logger.warning(f"Skipping non-dict change: {change}")
                    continue

                try:
                    field_logs.append(
                        FieldChangeLog(
                            record_change=record_change,
                            field_name=change.get("field", "unknown"),
                            field_label=change.get(
                                "field_label",
                                self._get_field_label(change.get("field", "unknown")),
                            ),
                            old_value=(
                                str(change.get("old_value", ""))
                                if change.get("old_value") is not None
                                else None
                            ),
                            new_value=(
                                str(change.get("new_value", ""))
                                if change.get("new_value") is not None
                                else None
                            ),
                            field_importance=self._get_field_importance(
                                change.get("field", "unknown")
                            ),
                        )
                    )
                except Exception as e:
                    logger.error(f"Error creating field change log: {e}", exc_info=True)

            if field_logs:
                FieldChangeLog.objects.bulk_create(field_logs)

        return record_change

    def _clean_json_data(self, data_list):
        """清理JSON数据中的不可序列化对象"""
        if not isinstance(data_list, list):
            return

        for item in data_list:
            if not isinstance(item, dict):
                continue

            for key, value in list(item.items()):
                if hasattr(value, "__dict__"):  # 如果是复杂对象
                    if hasattr(value, "id"):
                        item[key] = value.id  # 存储ID
                    elif hasattr(value, "pk"):
                        item[key] = value.pk  # 存储主键
                    else:
                        item[key] = str(value)  # 转为字符串
                elif isinstance(value, (datetime.date, datetime.datetime)):
                    item[key] = value.isoformat()  # 日期序列化

    def _create_safe_serializable_data(self, data):
        """创建完全可序列化的数据副本"""
        if isinstance(data, list):
            result = []
            for item in data:
                if isinstance(item, dict):
                    safe_item = {}
                    for k, v in item.items():
                        if hasattr(v, "__dict__"):  # 对象
                            if hasattr(v, "id"):
                                safe_item[k] = v.id
                            else:
                                safe_item[k] = str(v)
                        elif isinstance(v, (list, dict)):  # 嵌套结构
                            safe_item[k] = self._create_safe_serializable_data(v)
                        else:  # 基本类型
                            safe_item[k] = v
                    result.append(safe_item)
                elif hasattr(item, "__dict__"):  # 如果是对象
                    result.append(str(item))
                else:
                    result.append(item)
            return result
        elif isinstance(data, dict):
            safe_dict = {}
            for k, v in data.items():
                if hasattr(v, "__dict__"):  # 对象
                    if hasattr(v, "id"):
                        safe_dict[k] = v.id
                    else:
                        safe_dict[k] = str(v)
                elif isinstance(v, (list, dict)):  # 嵌套结构
                    safe_dict[k] = self._create_safe_serializable_data(v)
                else:  # 基本类型
                    safe_dict[k] = v
            return safe_dict
        else:
            return str(data) if hasattr(data, "__dict__") else data

    def _serialize_for_json(self, data):
        """
        将数据转换为JSON可序列化的格式

        Args:
            data: 任何数据对象

        Returns:
            可JSON序列化的数据
        """
        if isinstance(data, list):
            return [self._serialize_for_json(item) for item in data]
        elif isinstance(data, dict):
            return {k: self._serialize_for_json(v) for k, v in data.items()}
        elif hasattr(data, "__dict__"):  # 对象
            if hasattr(data, "id"):
                return data.id  # 只返回ID
            else:
                return str(data)  # 转为字符串
        else:
            return data  # 基本类型直接返回

    def _find_existing_record(self, commission_number):
        """
        根据委托编号查找现有记录

        Args:
            commission_number: 委托编号

        Returns:
            ArchiveRecord或None: 找到的记录或None

        注意:
            此方法仍保留用于兼容旧代码，但在新的批量处理流程中，应优先使用
            预先创建的 existing_records_map 缓存而不是单独调用此方法。
        """
        if not commission_number:
            return None

        # CHANGE: [2024-07-25] 添加性能日志
        logger.debug(f"单独查询委托编号: {commission_number}")

        # 规范化委托编号 - 去除前后空格并转换为小写
        normalized_number = str(commission_number).strip()

        # 精确匹配查询 - 使用索引字段
        query = ArchiveRecord.objects.filter(
            commission_number=normalized_number
        ).order_by("-id")

        # 添加调试日志
        if query.exists():
            record = query.first()
            logger.debug(f"查询委托编号 '{normalized_number}' 找到记录: ID={record.id}")
            return record

        # 如果没有找到记录且原始编号有前后空格或大小写差异，尝试不区分大小写查询
        if normalized_number != commission_number:
            logger.debug(
                f"尝试宽松匹配: 原始值='{commission_number}', 规范化值='{normalized_number}'"
            )
            loose_query = ArchiveRecord.objects.filter(
                commission_number__iexact=normalized_number
            )
            if loose_query.exists():
                record = loose_query.first()
                logger.debug(f"宽松匹配找到记录: ID={record.id}")
                return record

        return None

    @classmethod
    def batch_find_existing_records(cls, commission_numbers):
        """
        批量查找现有记录并创建委托编号到记录的映射

        Args:
            commission_numbers: 委托编号列表

        Returns:
            dict: 委托编号到记录对象的映射
        """
        if not commission_numbers:
            return {}

        # 规范化委托编号
        normalized_numbers = [str(num).strip() for num in commission_numbers if num]

        # 批量查询数据库
        records = ArchiveRecord.objects.filter(commission_number__in=normalized_numbers)

        # 创建映射 - 使用规范化的委托编号作为键
        record_map = {record.commission_number.strip(): record for record in records}

        return record_map

    def _safe_bulk_create(
        self, records: List[ArchiveRecord], max_retries: int = None
    ) -> List[ArchiveRecord]:
        """
        安全的批量创建方法，包含重试机制

        Args:
            records: 要创建的记录列表
            max_retries: 最大重试次数，如不提供则使用配置的MAX_RETRIES

        Returns:
            List[ArchiveRecord]: 成功创建的记录列表
        """
        # 使用配置的最大重试次数（如果未提供）
        if max_retries is None:
            max_retries = self.config.MAX_RETRIES

        # 不再进行截断处理，只记录日志
        for record in records:
            for field_name in record.__dict__:
                # 跳过主键、外键关联字段和Django内部字段
                if (
                    field_name.startswith("_")
                    or field_name == "id"
                    or field_name.endswith("_id")
                ):
                    continue

                value = getattr(record, field_name, None)
                # 只处理字符串类型的值
                if isinstance(value, str) and field_name in self.string_field_limits:
                    max_length = self.string_field_limits.get(field_name, 2000)
                    if len(value) > max_length:
                        # 记录原始长度，以便日志
                        logger.warning(
                            f"批量创建时字段 {field_name} 的值长度 ({len(value)}) 超过字典定义的最大长度 ({max_length})，不进行截断，保留原始数据。"
                        )

        # 正常继续处理
        retries = 0  # Counter for retrying non-DB errors for the main bulk_create
        # The main while loop is for retrying the bulk_create itself for non-DB errors.
        # DB errors (IntegrityError for fallback, or other DBError) should lead to quicker exit or specific handling.
        while retries < max_retries:
            try:
                return ArchiveRecord.objects.bulk_create(records)
            except IntegrityError as ie:
                # Only attempt fallback to individual inserts on the *first* try of bulk_create
                # if it's a UNIQUE constraint violation. Further retries of bulk_create (for other errors)
                # will not re-trigger this fallback.
                if "UNIQUE constraint" in str(ie) and retries == 0:
                    logger.warning(
                        f"批量创建因UNIQUE约束失败 (尝试 {retries + 1})，尝试逐个创建: {str(ie)}"
                    )
                    created_records_individually = []
                    for record in records:
                        try:
                            # 确保不设置ID
                            if hasattr(record, "id"):
                                record.id = None

                            # 在单条创建前再次检查并记录长度超限的字段
                            for field_name in record.__dict__:
                                if (
                                    field_name.startswith("_")
                                    or field_name == "id"
                                    or field_name.endswith("_id")
                                ):
                                    continue

                                value = getattr(record, field_name, None)
                                if (
                                    isinstance(value, str)
                                    and field_name in self.string_field_limits
                                ):
                                    max_length = self.string_field_limits.get(
                                        field_name, 2000
                                    )
                                    if len(value) > max_length:
                                        logger.warning(
                                            f"单条创建时字段 {field_name} 的值长度 ({len(value)}) 超过字典定义的最大长度 ({max_length})，不进行截断，保留原始数据。"
                                        )

                            created_record = ArchiveRecord.objects.create(
                                **{
                                    f: getattr(record, f)
                                    for f in record._meta.fields
                                    if f != "id"
                                }
                            )
                            created_records_individually.append(created_record)
                        except (
                            DatabaseError
                        ) as db_err_individual:  # Specific DB error during individual create
                            logger.error(
                                f"单条记录创建失败 (数据库错误): {str(db_err_individual)}，记录数据可能为: {record.__dict__ if hasattr(record, '__dict__') else record}",
                                exc_info=True,
                            )
                            raise  # Re-throw DB error to propagate to the caller and roll back the transaction
                        except (
                            Exception
                        ) as individual_error:  # Other non-DB errors during individual create
                            logger.error(
                                f"单条记录创建失败 (非数据库错误): {str(individual_error)}，记录数据可能为: {record.__dict__ if hasattr(record, '__dict__') else record}",
                                exc_info=True,
                            )
                            continue  # Continue with the next record for non-DB errors in fallback
                    return created_records_individually  # Return if individual creation path was taken and completed (or partially)
                else:  # Not a UNIQUE constraint for fallback, or IntegrityError on a retry of bulk_create
                    logger.error(
                        f"批量创建时发生IntegrityError (非UNIQUE或重试中): {str(ie)}",
                        exc_info=True,
                    )
                    raise ie  # Re-throw IntegrityError
            except (
                DatabaseError
            ) as dbe:  # Catch other DB errors (e.g., DataError like "value too long")
                logger.error(
                    f"批量创建时发生数据库错误 (例如 DataError): {str(dbe)}",
                    exc_info=True,
                )
                raise dbe  # Re-throw immediately, these usually aren't recoverable by retry
            except Exception as e:  # Catch non-DB errors for the bulk_create operation
                if retries < max_retries - 1:
                    retries += 1
                    logger.warning(
                        f"批量创建时发生非数据库错误，重试 {retries}/{max_retries}: {str(e)}"
                    )
                    time.sleep(self.config.RETRY_DELAY)  # 使用配置的延迟时间
                else:
                    logger.error(
                        f"批量创建时发生非数据库错误，已达最大重试次数: {str(e)}",
                        exc_info=True,
                    )
                    raise e  # Re-throw after max retries for non-DB errors

    def _preprocess_and_cache_records(self, df: pd.DataFrame) -> Dict:
        """
        预处理DataFrame并缓存已存在的记录

        Args:
            df: 处理后的pandas DataFrame

        Returns:
            委托编号到记录对象的映射字典
        """
        # 重命名DataFrame列，转换为模型字段名
        df_mapped = self._map_dataframe_columns(df)

        # 提取所有委托编号进行预处理
        commission_numbers = (
            df_mapped["commission_number"]
            .dropna()
            .astype(str)
            .apply(lambda x: x.strip())
            .unique()
            .tolist()
        )

        logger.info(f"从Excel中提取了 {len(commission_numbers)} 个唯一委托编号")

        # 批量查询已存在的记录
        with performance_tracker("查询现有记录", logger):
            # 精确匹配查询
            existing_records_queryset = ArchiveRecord.objects.filter(
                commission_number__in=commission_numbers
            )
            
            logger.info(
                f"在数据库中找到 {existing_records_queryset.count()} 个精确匹配的记录"
            )

            # 创建委托编号到记录的内存映射
            existing_records_map = {
                record.commission_number.strip(): record
                for record in existing_records_queryset
            }
            
            # 对于未找到的委托编号，尝试不区分大小写的匹配
            if len(existing_records_map) < len(commission_numbers):
                missing_numbers = [num for num in commission_numbers if num not in existing_records_map]
                logger.info(f"尝试宽松匹配 {len(missing_numbers)} 个未找到的委托编号")
                
                # 使用Q对象构建OR查询
                from django.db.models import Q
                q_filters = Q()
                for number in missing_numbers:
                    q_filters |= Q(commission_number__iexact=number)
                    
                if q_filters:
                    loose_matches = ArchiveRecord.objects.filter(q_filters)
                    
                    # 将宽松匹配添加到映射中
                    new_matches = 0
                    for record in loose_matches:
                        # 查找Excel中最接近的委托编号
                        for number in missing_numbers:
                            if record.commission_number.lower() == number.lower():
                                existing_records_map[number] = record
                                new_matches += 1
                                break
                
                    logger.info(f"通过宽松匹配额外找到 {new_matches} 条记录")

        return existing_records_map

    def _create_change_batch(self, import_log: ImportLog) -> ChangeLogBatch:
        """
        创建变更批次

        Args:
            import_log: 导入日志对象

        Returns:
            创建的变更批次对象
        """
        return ChangeLogBatch.objects.create(
            import_log=import_log,
            change_source="excel_import",
            change_reason=f"Excel导入批次 {import_log.batch_number}",
            changed_by=import_log.import_user,
        )

    def _verify_and_refresh_record(
        self, existing_record, commission_number: str
    ) -> ArchiveRecord:
        """
        在更新记录前验证其是否被其他进程修改，如已修改则获取最新版本

        Args:
            existing_record: 缓存中的记录对象
            commission_number: 委托编号

        Returns:
            ArchiveRecord: 最新的记录对象
        """
        try:
            # 使用select_for_update锁定记录，确保在事务内没有其他进程可以同时修改
            fresh_record = ArchiveRecord.objects.select_for_update().get(
                commission_number=commission_number
            )

            # 检查记录是否被其他进程修改过（通过updated_at字段）
            if hasattr(existing_record, "updated_at") and hasattr(
                fresh_record, "updated_at"
            ):
                if fresh_record.updated_at != existing_record.updated_at:
                    logger.warning(
                        f"记录 {commission_number} 在缓存中的版本已过期，获取最新版本"
                    )
                    return fresh_record

            return existing_record
        except ArchiveRecord.DoesNotExist:
            # 记录可能已被删除，返回缓存中的版本
            logger.warning(f"记录 {commission_number} 在尝试刷新时未找到，使用缓存版本")
            return existing_record
        except Exception as e:
            # 其他错误，记录日志并返回缓存版本
            logger.error(
                f"验证记录 {commission_number} 时出错: {str(e)}", exc_info=True
            )
            return existing_record

    # CHANGE: [2025-06-02] 新增 import_from_dataframe_object 方法，用于直接处理DataFrame导入
    def import_from_dataframe_object(
        self,
        dataframe_to_import: pd.DataFrame,
        user: Optional[User] = None,
        duplicate_strategy: str = DuplicateStrategy.SMART_UPDATE.value,
        import_session: Optional[ImportSession] = None,
        original_file_name: Optional[str] = "DataFrame Import",
        original_file_size: int = 0,
        original_file_hash: Optional[str] = None,
    ) -> ImportLog:
        duplicate_strategy = self._validate_duplicate_strategy(duplicate_strategy)
        import_log: Optional[ImportLog] = None
        method_start_time = time.time() # CHANGE: Record start time
        
        with performance_tracker(f"整个从DataFrame对象导入过程 ({original_file_name})", logger):
            log_import_progress(f"开始从DataFrame对象导入: {original_file_name}")
            log_import_progress(f"DataFrame行数: {len(dataframe_to_import)}")
            log_import_progress(f"数据处理策略: {duplicate_strategy}")

            try:
                import_log = ImportLog.objects.create(
                    file_name=original_file_name, 
                    file_size=original_file_size, 
                    file_hash=original_file_hash, 
                    import_user=user,
                    batch_number=self._generate_batch_number(),
                    status="processing",
                )
                # CHANGE: [2025-06-05] 从导入会话获取创建人信息，不再使用 import_session_id_fk
                if import_session:
                    import_log.created_by = import_session.created_by  # 从会话获取创建人
                    import_log.session_id = str(import_session.session_id)  # 设置会话ID字符串
                    import_log.save(update_fields=['created_by', 'session_id', 'updated_at'])

                df = dataframe_to_import
                self._validate_required_columns(df, import_log)
                total_rows_in_current_df = len(df) 
                
                # For import_from_dataframe_object, analysis_successfully_parsed_rows can be set to total_rows_in_current_df
                # as the dataframe is already "parsed" by the caller.
                # CHANGE: [2025-06-02] Set analysis_successfully_parsed_rows
                import_log.analysis_successfully_parsed_rows = total_rows_in_current_df
                # import_task_total_records_submitted will be set by _update_import_log_with_results
                import_log.save(update_fields=['analysis_successfully_parsed_rows'])


                existing_records_map = self._preprocess_and_cache_records(df) 
                change_batch = self._create_change_batch(import_log)

                try:
                    result = self._process_dataframe(
                        df, 
                        import_log, 
                        duplicate_strategy=duplicate_strategy,
                        existing_records_map=existing_records_map,
                        change_batch=change_batch,
                        import_session=import_session
                    )
                    
                    log_import_progress(
                        f"DataFrame处理完成. 成功: {result.success_count}, 错误: {len(result.error_records)}, "
                        f"跳过: {len(result.skipped_records)}, 创建: {result.created_count}, "
                        f"更新: {result.updated_count}, 未变: {result.unchanged_count}"
                    )
                    
                    self._update_import_log_with_results(
                        import_log=import_log,
                        success_count_from_result=result.success_count,
                        error_records=result.error_records,
                        skipped_records_in_task=result.skipped_records,
                        created_count_from_result=result.created_count,
                        updated_count_from_result=result.updated_count,
                        unchanged_count_from_result=result.unchanged_count,
                        total_records_submitted_to_task=total_rows_in_current_df
                    )

                except RequiredFieldMissingError as e_req:
                    self._handle_dataframe_processing_error(e_req, df, import_log)
                    raise 
                except DatabaseOperationError as e_db:
                    logger.error(f"数据库操作错误 during DataFrame import: {e_db}", exc_info=True)
                    detailed_error_info = f"DatabaseOperationError: {str(e_db)}. Trace: {traceback.format_exc()}"
                    self._handle_dataframe_processing_error(DatabaseOperationError(detailed_error_info), df, import_log)
                    raise
                except Exception as e_gen:
                    logger.error(f"处理DataFrame时发生未知错误: {e_gen}", exc_info=True)
                    detailed_error_info = f"Unexpected Error: {str(e_gen)}. Trace: {traceback.format_exc()}"
                    self._handle_dataframe_processing_error(Exception(detailed_error_info), df, import_log)
                    raise
                
                # CHANGE: Moved processing_time calculation to the end of the successful try block
                import_log.processing_time = time.time() - method_start_time
                # Other fields are updated by _update_import_log_with_results or _handle_dataframe_processing_error
                # Status should be final here.
                import_log.save(update_fields=['processing_time', 'status']) # Save final status and processing time
                
                log_import_progress(f"DataFrame导入批次 {import_log.batch_number} 完成，耗时: {import_log.processing_time:.2f}秒. 最终状态: {import_log.status}")
                
                if change_batch:
                    try:
                        # Result should be available if no exception occurred in the inner try
                        change_batch.affected_records_count = result.created_count + result.updated_count
                        change_batch.summary = {
                            "created": result.created_count,
                            "updated": result.updated_count,
                            "skipped": len(result.skipped_records),
                            "errors": len(result.error_records),
                            "unchanged_due_to_identical_content_in_task": result.unchanged_count
                        }
                        change_batch.save()
                    except Exception as e_cl_batch:
                        logger.error(f"更新ChangeLogBatch {change_batch.batch_id} 统计时出错: {e_cl_batch}", exc_info=True)

                return import_log

            except Exception as e: # Outer exception handling
                logger.error(f"从DataFrame导入时发生顶层错误: {e}", exc_info=True)
                if import_log: # If import_log was created
                    import_log.processing_time = time.time() - method_start_time
                    # _handle_import_error (if called by a more specific handler like _handle_dataframe_processing_error)
                    # would have set status and error_log. If error is before that, _handle_import_error will be called here.
                    if not import_log.status or import_log.status == "processing": # If status not yet failed by inner handlers
                         self._handle_import_error(e, import_log, df if 'df' in locals() else dataframe_to_import)
                    else: # Status already set by inner handler, just save processing_time
                        try:
                            import_log.save(update_fields=['processing_time'])
                        except Exception as save_err_pt:
                             logger.error(f"Error saving processing_time during outer exception handling: {save_err_pt}")
                # Ensure the original exception is re-raised if it wasn't handled by a specific dataframe processing error
                # that itself re-raised. If _handle_import_error was called, it doesn't re-raise.
                # The re-raises from inner try-except ensure this outer block catches them.
                raise

    # 在ExcelImportService类中添加统一的时间比较方法，放在类的最后几个方法附近
    def _compare_datetime_values(self, excel_value: Any, db_value: Any, field_name: str = "unknown") -> bool:
        """
        统一的日期时间比较方法，现在委托给DateTimeComparisonUtil。
        
        CHANGE: [2025-06-03] 重构使用统一的时间比较工具
        """
        logger.info(f"[导入阶段-时间比较] 字段: {field_name}")
        logger.info(f"[导入阶段-时间比较] Excel值: {excel_value}, 数据库值: {db_value}")
        result = DateTimeComparisonUtil.compare_datetime_values(excel_value, db_value, field_name)
        logger.info(f"[导入阶段-时间比较] 比较结果: {'相等' if result else '不相等'}")
        return result
