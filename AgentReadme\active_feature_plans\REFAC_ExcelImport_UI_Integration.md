# Refactoring Plan: ExcelImportWithConflictResolution Component

## 1. Objective

To refactor the `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` component to primarily utilize the `frontend/hooks/useExcelImportSession.ts` Hook for its state management, business logic, and API interactions. This will replace most of the component's local state management and direct calls to `excelImportService.ts`.

## 2. Benefits

* **Centralized Logic**: Consolidates Excel import business logic and state management within the `useExcelImportSession` Hook, making it the single source of truth.
* **Improved Maintainability**: Simplifies the UI component by offloading complex logic to the Hook.
* **Consistency**: Ensures consistent behavior if other components also use the import functionality via the Hook.
* **Leverage New Features**: Allows the UI to easily benefit from features built into the Hook, such as heartbeat management, refined permission handling, and robust status polling.
* **Reduced Boilerplate**: Decreases the amount of state and effect management कोड directly within the UI component.

## 3. Key Changes Overview

* Integrate and call `useExcelImportSession()` Hook.
* Replace most local `useState` variables with state provided by the Hook (e.g., `activeSessionInfo`, `currentUserPermissions`, `isLoadingSession`, `errorLoadingSession`, `analysisProgress`, `isSubmitting`).
* Replace direct calls to `excelImportService` methods with the equivalent methods exposed by the Hook (e.g., `startNewImport`, `confirmImport`, `cancelCurrentImport`, `resetImportState`, `getAnalysisResult`, `takeoverImport`).
* Adapt the UI rendering logic (especially within `renderStep()`) to derive its views from the Hook's state.
* Streamline or remove local polling/status update logic, as the Hook now handles this.
* Reconcile or integrate with the existing `useActiveSessionWarning` context to ensure state consistency.

## 4. Prerequisites

* The `useExcelImportSession` Hook (`frontend/hooks/useExcelImportSession.ts`) is complete, tested (at least unit-tested), and functional, including:
  * Real user ID integration (via `useAuth`).
  * Heartbeat mechanism (`startHeartbeat`, `stopHeartbeat`).
  * Robust analysis progress polling.
  * Correct handling of session lifecycle states.
* The `excelImportService` (`frontend/services/excel-import-service.ts`) contains all necessary methods, including `sendHeartbeat`.

## 5. Step-by-Step Refactoring Plan

### Step 0: Preparation & Backup

* `[ ]` **Backup**: Create a backup copy of `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` before starting major changes.
* `[ ]` **Verify Hooks/Services**: Confirm that `useExcelImportSession.ts` and `excelImportService.ts` are up-to-date and reflect all previously discussed and implemented functionalities.

### Step 1: Import and Instantiate the Hook

* **Task**: Add necessary imports for the Hook and its related types. Call the Hook at the top of the `ExcelImportWithConflictResolution` component and destructure its return values.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:

    ```typescript
    import { 
        useExcelImportSession, 
        ImportSessionStatusEnum, // For status comparisons
        type UserPermissionsOnSession // For typing permissions
    } from '@/hooks/useExcelImportSession'; // Adjust path if necessary

    // Inside the component:
    const {
      activeSessionInfo,
      currentUserPermissions,
      isLoadingSession,
      errorLoadingSession,
      analysisProgress: hookAnalysisProgress, // Rename if local analysisProgress state is temporarily kept
      isSubmitting,
      fetchSystemActiveSession,
      startNewImport,
      cancelCurrentImport,
      getAnalysisResult,
      confirmImport,
      takeoverImport,
      resetImportState
    } = useExcelImportSession();
    ```

* **Checklist**: `[ ]` Hook imported and called. Return values destructured.

### Step 2: Replace Core State Management & Derive UI Step

* **Task**: Identify local `useState` variables that manage data now provided by the Hook. Remove or comment them out. Use Hook-provided state. Create a `useEffect` to derive `currentStep` for UI rendering based on Hook state.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * Remove/comment out local states for: `importSessionId`, `fileDetails` (parts of it), `analysisProgress` (the one tracking polling), `isPollingProgress`, `isUploading`, `isSubmittingConfirm`.
  * Retain local states for UI-specific interactions if necessary (e.g., `selectedFile`, `uploadProgress` for XHR, `showConflictModal`, `filterType`, etc., unless these can also be derived or managed by the hook).
  * Create `derivedCurrentStep` state and a `useEffect` to update it based on `activeSessionInfo?.status`, `isLoadingSession`, `errorLoadingSession`.
* **Checklist**:
  * `[ ]` Local session-related states replaced.
  * `[ ]` `derivedCurrentStep` implemented and driven by Hook state.

### Step 3: Refactor Core Action Handlers

* **Task**: Update the main user action handlers to call methods暴露的由Hook。
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * **`handleFileUpload`**:
    * Call `startNewImport(selectedFile, sheetName, onUploadProgressCallback)`.
    * The `onUploadProgressCallback` updates the local `uploadProgress` state for the XHR part.
    * Success/failure will be reflected in Hook's state (`activeSessionInfo`, `errorLoadingSession`).
  * **`handleReset` (formerly `resetForm`)**:
    * Primary action: Call `resetImportState()`.
    * Also clear local UI states like `selectedFile`, file input value, `conflictRecordsInternal`, etc.
  * **`handleConfirmImport` (consolidating old conflict resolution logic)**:
    * Call `confirmImport(sessionId, resolutionsArray)`.
    * Update local `importLogDetails` with the result.
    * Hook state (`activeSessionInfo.status`) will change to `IMPORT_COMPLETE` or `ERROR`.
  * **Cancel Operations**: Ensure any dedicated "cancel" buttons appropriately call `cancelCurrentImport(sessionId)`. Note that `resetImportState` also attempts a cancel.
  * **Takeover Operations**: If UI elements for taking over a session exist (e.g., shown when `currentUserPermissions.canTakeover` is true), they should call `takeoverImport(sessionId)`.
* **Checklist**:
  * `[ ]` `handleFileUpload` refactored.
  * `[ ]` `handleReset` refactored.
  * `[ ]` `handleConfirmImport` refactored.
  * `[ ]` Cancel/Takeover calls mapped to Hook methods.

### Step 4: Refactor Analysis Result Fetching

* **Task**: Adapt the logic that fetches and processes analysis results when the session status becomes `ANALYSIS_COMPLETE`.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * Use a `useEffect` that triggers when `activeSessionInfo?.status` is `ImportSessionStatusEnum.ANALYSIS_COMPLETE` (and `derivedCurrentStep` is not yet `confirm` or `completed`).
  * Inside this effect, call `getAnalysisResult(activeSessionInfo.session_id)`.
  * Use the payload to set local `analysisStats` and `conflictRecordsInternal` (map to `ConflictRecordWithAction`, setting default actions).
  * Based on results (e.g., presence of conflicts), transition `derivedCurrentStep`.
* **Checklist**: `[ ]` Analysis result fetching logic updated to use `getAnalysisResult` and Hook state.

### Step 5: Remove Obsolete Polling Logic

* **Task**: The main UI component previously had its own `useEffect` for polling analysis progress. This should be removed.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * The `useExcelImportSession` Hook now internally handles polling for analysis progress (updating `hookAnalysisProgress` or `activeSessionInfo.progress`) and also manages heartbeats.
  * Remove the `useEffect` block that contained `excelImportService.getAnalysisProgress` calls and `setTimeout` for polling.
* **Checklist**: `[X]` Manual progress polling `useEffect` removed from UI component. (Assumed completed based on prior instruction)

### Step 6: Adapt UI Rendering Logic (`renderStep()` etc.)

* **Task**: Modify the `renderStep()` function and any other rendering logic to use states provided by the Hook.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**: This is a major part of the refactoring. The `renderStep` function needs to be carefully updated to reflect the state managed by `useExcelImportSession`. The following sub-steps provide a more detailed plan for this:

#### Step 6.1: `renderStep` Overall Structure and Initial Checks

* **Task**: Ensure `renderStep` correctly handles global loading/error states from the Hook before proceeding to `derivedCurrentStep` logic.
* **Details**:
  * At the beginning of `renderStep`, check for `isLoadingSession` (especially if `!activeSessionInfo` and `!errorLoadingSession`) to display a primary loading indicator for the entire component or a skeleton UI.
  * Check for `errorLoadingSession`. If present, display a prominent error message (e.g., using `<Alert variant="destructive">`) and potentially a button to call `handleReset`.
  * The main UI logic will then flow through a `switch (derivedCurrentStep)` statement.
* **Checklist**: `[ ]` Global loading state (`isLoadingSession`) handled. `[ ]` Global error state (`errorLoadingSession`) handled.

#### Step 6.2: Refactor `case 'select':` (File Selection Step)

* **Task**: Adapt the UI for the initial file selection step.
* **Key Hook States**: `derivedCurrentStep`, `isSubmitting`, `activeSessionInfo` (used by `useActiveSessionWarning` reconciliation), `currentUserPermissions`.
* **Local States Used**: `selectedFile`, `fileInputRef`.
* **Key Changes**:
  * **File Input (`<Input type="file">`)**: `onChange` handler remains `handleFileChange`. The `disabled` attribute should be true if `isSubmitting` is true, or if `isSessionOverrideActive && activeSessionToProcess` (from `useActiveSessionWarning` context, indicating another session is active) is true.
  * **"Upload File" Button (`<Button onClick={handleFileUpload}>`)**: The `disabled` attribute should be true if `!selectedFile`, or `isSubmitting` is true, or if a blocking `activeSessionToProcess` exists. Button text should change to indicate loading (e.g., "处理中...") when `isSubmitting` is true.
  * **Existing Active Session Display** (if `isSessionOverrideActive && activeSessionToProcess`):
    * Display file name from `activeSessionToProcess.file_name` and status from `activeSessionToProcess.status`.
    * **"Takeover" Button**: Show this button if `currentUserPermissions?.canTakeover` is true AND `activeSessionToProcess?.session_id` exists. The `onClick` should call `handleTakeoverSession(activeSessionToProcess.session_id)`. Disable this button if `isSubmitting` is true.
  * The visual elements for file drag-and-drop or displaying selected file info largely remain, but ensure their interactive states are consistent with `isSubmitting` or other blocking conditions.
* **Checklist**: `[ ]` File input logic updated. `[ ]` Upload button state and text updated. `[ ]` Existing session display and takeover button logic integrated.

#### Step 6.3: Refactor `case 'upload':` (XHR File Uploading Progress)

* **Task**: Adapt the UI to show progress of the actual file being sent to the server (before server-side analysis begins). This step is primarily driven by the `onUploadProgress` callback passed to `startNewImport`.
* **Key Hook States**: `derivedCurrentStep` (should be `'upload'` or a similar state set immediately by `handleFileUpload` before `startNewImport` resolves and changes `activeSessionInfo.status`). `isSubmitting` will be true.
* **Local States Used**: `uploadProgress` (0-100, set by the callback), `selectedFile.name`.
* **Key Changes**:
  * Display an appropriate title like "文件上传中".
  * Show the `selectedFile.name`.
  * Render a progress bar visually bound to the local `uploadProgress` state.
  * Text like "您的文件正在安全上传到服务器..."
* **Checklist**: `[ ]` UI for XHR upload progress correctly uses `uploadProgress`.

#### Step 6.4: Refactor `case 'analyze':` (Server-Side Analysis Progress)

* **Task**: Adapt the UI to show the progress of the server-side analysis after the file has been uploaded.
* **Key Hook States**: `derivedCurrentStep` (should be `'analyze'`), `activeSessionInfo` (for `file_name`, `record_count`, `status`, `progress`), `hookAnalysisProgress` (for detailed `analyzed_records`, `total_records`, `analyzed_percentage`), `isSubmitting` (e.g., if a cancel operation via `handleReset` is in progress).
* **Key Changes**:
  * Display file name: `activeSessionInfo?.file_name`.
  * Display total records being analyzed: `activeSessionInfo?.record_count` or `hookAnalysisProgress?.total_records`.
  * **Progress Bar and Percentage**: Primarily use `hookAnalysisProgress?.analyzed_percentage`. As a fallback or for initial display, `activeSessionInfo?.progress` can be used.
  * Text for scanned records: "已扫描 {`hookAnalysisProgress?.analyzed_records` || 0} / {`hookAnalysisProgress?.total_records` || `activeSessionInfo?.record_count` || 0} 条记录".
  * Status text based on `activeSessionInfo?.status` (e.g., "服务器正在分析数据", "数据分析已完成" if progress is 100% but results are still loading).
  * **"强制取消并重置" Button**: `onClick` calls `handleReset()`. Disable if `isSubmitting` or `!activeSessionInfo?.session_id`.
  * Pulsing animations or loading indicators can be active if `activeSessionInfo.status === ImportSessionStatusEnum.ANALYSIS_IN_PROGRESS` or `derivedCurrentStep === 'analyze'` and `(hookAnalysisProgress?.analyzed_percentage ?? activeSessionInfo?.progress ?? 0) < 100`.
* **Checklist**: `[ ]` File info display updated. `[ ]` Progress bar and text use Hook data. `[ ]` Cancel button logic correct.

#### Step 6.5: Refactor `case 'confirm':` (Conflict Resolution Stage)

* **Task**: Adapt the UI for displaying the analysis summary and allowing users to manage conflicts.
* **Key Hook States**: `derivedCurrentStep` (should be `'confirm'`), `activeSessionInfo` (for context, e.g., `session_id`), `isSubmitting`.
* **Local States Used**: `analysisStats` (from `getAnalysisResult`), `conflictRecordsInternal`, `showConflictModal`, `filteredConflictRecords`, `filterType`.
* **Key Changes**:
  * **Analysis Summary Display**: Render statistics from the local `analysisStats` object (which should have been populated in Step 4 after `getAnalysisResult` succeeded).
  * **"处理冲突记录" Button**: Controls `setShowConflictModal(true)`. Visible if `conflictRecordsInternal.length > 0`. Disable if `isSubmitting`.
  * **Main Confirmation Button**: Text changes based on `conflictRecordsInternal.length` ("全部确认并开始导入" if 0 conflicts with records, or "应用解决方案并导入" if conflicts exist). `onClick` calls the refactored `handleConfirmImport()`. Disable if `isSubmitting` or `!activeSessionInfo?.session_id`. If conflicts exist but modal was not opened or no resolutions were made, this button might need additional logic or rely on modal's confirmation.
  * **"取消并重新选择文件" Button**: `onClick` calls `handleReset()`. Disable if `isSubmitting`.
  * The `ConflictResolutionModal` (opened by `setShowConflictModal`) will use `filteredConflictRecords`, `analysisStats`, and pass actions to `updateConflictAction`, `updateAllConflictsAction`. Its main confirm action will call `handleConfirmImport` (passed as `onResolve` prop - see Step 8).
* **Checklist**: `[ ]` Analysis summary display updated. `[ ]` Conflict handling buttons correctly use local/Hook states. `[ ]` Main confirmation button logic reviewed and updated.

#### Step 6.6: Refactor `case 'completed':` (Import Completion Summary)

* **Task**: Adapt the UI for displaying the final import completion summary.
* **Key Hook States**: `derivedCurrentStep` (should be `'completed'`).
* **Local States Used**: `importLogDetails` (populated by `handleConfirmImport` from `confirmImport` Hook method's result).
* **Key Changes**:
  * Display success message (e.g., "导入成功完成!").
  * Render import statistics using the `importLogDetails` object.
  * **"导入新的Excel文件" Button**: `onClick` calls `handleReset({showToast: false})` to prepare for a new import without a redundant toast.
  * **"查看本次导入日志" Button/Link**: Uses `importLogDetails?.import_log_id` to link to the history page.
* **Checklist**: `[ ]` Completion summary uses `importLogDetails`. `[ ]` Action buttons are correctly configured.

#### Step 6.7: Refactor `default` case and General Error Display

* **Task**: Ensure a graceful fallback for unknown steps and integrate top-level error display from `errorLoadingSession`.
* **Key Hook States**: `derivedCurrentStep`, `errorLoadingSession`.
* **Key Changes**:
  * The `default` case in `switch(derivedCurrentStep)` should display a generic loading message or an "unknown state" message.
  * If `derivedCurrentStep` is specifically set to `'error'` (as planned in the Step 2 `useEffect`), ensure `errorLoadingSession` is displayed clearly, perhaps with a `handleReset` button for recovery.
* **Checklist**: `[ ]` Default case handled. `[ ]` Error step displays `errorLoadingSession`.

### Step 7: Reconcile `useActiveSessionWarning` Context

* **Task**: Review how `useActiveSessionWarning` (providing `activeSessionToProcess`, `setActiveSessionToProcess`, `isSessionOverrideActive`, `clearSession`, `saveSession`) interacts with the new Hook-driven state (`activeSessionInfo` from `useExcelImportSession`). Ensure synchronization and clarify roles.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * **Primary State Source**: `useExcelImportSession` (via `activeSessionInfo`) should be the primary source of truth for the current import session's data and status.
  * **Synchronization (`activeSessionInfo` -> `useActiveSessionWarning` context)**:
    * The `useEffect` that derives `derivedCurrentStep` (from Step 2) already includes logic to call `setActiveSessionToProcess(activeSessionInfo)` when `activeSessionInfo` changes. This is good for keeping the context updated.
    * It also calls `setIsSessionOverrideActive(true)` when `activeSessionInfo` is present, and `setIsSessionOverrideActive(false), setActiveSessionToProcess(null), clearSession()` when `activeSessionInfo` is null. This maintains consistency.
  * **Synchronization (`useActiveSessionWarning` context -> `activeSessionInfo`)**:
    * The component has a `useEffect` that listens to `activeSessionToProcess` (from context) and `activeSessionInfo` (from hook). If `activeSessionToProcess` has data and `activeSessionInfo` is null (and hook is not loading), it calls `fetchSystemActiveSession(true)`. This allows the context (which might load from `sessionStorage` via `useActiveSessionWarning` itself) to trigger the main hook to sync with the backend. This is a reasonable approach.
  * **`saveSession` (from context)**: The `saveImportSessionToStorage` callback (which wraps `saveSession`) is correctly called in Step 4 (after `getAnalysisResult`) to persist `activeSessionInfo` to `sessionStorage` (via the context). This is for page refresh resilience.
  * **`clearSession` (from context)**: This is correctly called in `handleReset` and when `activeSessionInfo` becomes null in the main `useEffect`.
  * **Remove Redundant Session Recovery**: The `ExcelImportWithConflictResolution` component contains an old `useEffect` (approx. original line 267-306) that *directly* reads `sessionStorage` and tries to call `excelImportService.getActiveImportSession()`. This logic is now redundant because:
        1. `useExcelImportSession` hook fetches the active session on its own initialization.
        2. `useActiveSessionWarning` context likely handles its own initialization from `sessionStorage`.
        3. The existing sync effect (checking `activeSessionToProcess && !activeSessionInfo`) already bridges the context to the hook if needed.
    * **Action**: This redundant `useEffect` for direct session recovery within the component should be removed.
* **Checklist**:
  * `[X]` Sync from Hook to Context (`setActiveSessionToProcess`, `setIsSessionOverrideActive`) reviewed and seems correct. (Completed in Step 2's useEffect)
  * `[X]` Sync from Context to Hook (`fetchSystemActiveSession` if mismatch) reviewed and seems correct. (Existing useEffect)
  * `[X]` `saveSession` usage for persistence reviewed. (Used in Step 4's useEffect)
  * `[X]` `clearSession` usage reviewed. (Used in `handleReset` and Step 2's useEffect)
  * `[ ]` Remove redundant direct `sessionStorage` recovery `useEffect` from the component.

### Step 8: Update Child Component Props

* **Task**: Ensure `ConflictResolutionModal` and `ConflictResolutionGrid` receive correct props derived from the Hook's state or refactored handlers.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` (passing props)
* **Details**:
  * **`ConflictResolutionModal`**:
    * `conflicts`: Should be `filteredConflictRecords` (derived from local `conflictRecordsInternal`, which is populated from `getAnalysisResult`).
    * `analysisResult`: Should be local `analysisStats`.
    * `onResolve`: Should call the refactored `handleConfirmImport`.
    * `isProcessing`: Should use `isSubmitting` from the Hook.
* **Checklist**: `[ ]` Props passed to child components updated.

### Step 9: Cleanup and Final Review

* **Task**: Remove unused old state variables, functions, and commented-out code. Perform a final review of the changes.
* **File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
* **Details**:
  * Ensure all direct `excelImportService` calls (except potentially by the Hook itself if it's re-exported for some reason, which is unlikely) are removed from the component.
  * Verify that dependencies in `useCallback` and `useEffect` are correct after the refactor.
  * Check for any new linter/type errors.
* **Checklist**:
  * `[ ]` Unused code removed.
  * `[ ]` Dependencies reviewed.
  * `[ ]` Code linted and type-checked.

## 6. Post-Refactoring

* Thorough manual testing of all import flows and edge cases.
* Consider writing/updating unit/integration tests for the refactored component and the Hook.

## 7. 原始 `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`

```tsx

"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { 
  ActiveImportSessionResponseData,
  SessionInfoData
} from '@/services/excel-import-service';
import { useToast } from '@/components/ui/use-toast';
import {
  Upload, FileSpreadsheet, AlertCircle, CheckCircle2, FileUp, DownloadCloud,
  Clock, ArrowRight, AlertTriangle, Info, RotateCcw, Filter, FileText
} from 'lucide-react'; // Removed Chevron icons for now, will add if used by render logic
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import {
  excelImportService, 
  ExcelImportOptions, 
  ConflictRecord,         // Base type for conflict records from service
  FieldDifference, 
  ConflictResolutionAction,
  ConflictResolution,     // Type for sending resolutions to backend
  AnalysisResult,         // Type for the summary object within ExcelAnalysisResultPayload
  ExcelAnalysisStartupInfoData, 
  ExcelAnalysisResultPayload, 
  ImportConfirmResultData, 
  AnalysisProgressData
} from '@/services/excel-import-service';
import { ConflictResolutionModal } from './conflict-resolution-modal';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useActiveSessionWarning } from '@/contexts/ActiveSessionWarningContext';
import ExcelImportService from '@/services/excel-import-service';

// CHANGE: [2024-07-27] Define a specific type for filter values
export type ConflictFilterType = 'all' | 'new' | 'update' | 'identical';

// 扩展冲突记录类型，供UI内部使用，添加用户选择的action
interface ConflictRecordWithAction extends ConflictRecord { // Extends ConflictRecord from service
  action: ConflictResolutionAction;
}

// 组件属性类型
export interface ExcelImportWithConflictResolutionProps {
  showTitle?: boolean;
  initialActiveSessionData?: ActiveImportSessionResponseData | null;
}

// 定义导入步骤的类型
type ImportStep = 'select' | 'upload' | 'analyze' | 'confirm' | 'complete' | 'completed';

// 更稳健的重试配置常量
const MAX_GET_RESULT_RETRIES = 15; // 最大重试次数

const COMPLETION_CONFIRM_DELAY = 100; // 确认延迟时间

// 新增: 轮询间隔配置常量
const NORMAL_POLL_INTERVAL = 1000; // 正常轮询间隔
const FAST_POLL_INTERVAL = 200;   // 快速轮询间隔(100%进度时)

// 新增：获取结果阶段的UI常量
const DATA_LOADING_PHASES = [
  '准备获取数据', 
  '正在获取分析结果', 
  '处理结果数据',
  '准备显示结果'
];

const ExcelImportWithConflictResolution: React.FC<ExcelImportWithConflictResolutionProps> = ({
  showTitle = true,
  initialActiveSessionData = null
}) => {
  
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const progressPollingRef = useRef<NodeJS.Timeout | null>(null); // For polling timer
  const pollCountRef = useRef(0); // For retry logic in polling
  const lastProgressRef = useRef<number>(0); // To detect stale progress in polling
  
  // 使用ref替代state跟踪状态稳定性
  const previousStatusRef = useRef<string | null>(null);
  const isConfirmingCompletionRef = useRef<boolean>(false);
  
  const { 
    activeSessionToProcess,
    setActiveSessionToProcess, 
    isSessionOverrideActive, 
    setIsSessionOverrideActive,
    saveSession,
    clearSession,
    validateSession
  } = useActiveSessionWarning();

  // --- 核心状态定义 ---
  const [currentStep, setCurrentStep] = useState<ImportStep>('select');
  
  // 文件选择和上传状态
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0); // 0-100, for XHR upload part
  const [isUploading, setIsUploading] = useState(false); // True during file transmission to analyzeExcelFile API

  // 会话和分析状态
  const [importSessionId, setImportSessionId] = useState<string | null>(null);
  const [fileDetails, setFileDetails] = useState<{ name: string; totalRecords: number } | null>(null);
  
  // 分析阶段状态
  const [analysisProgress, setAnalysisProgress] = useState<AnalysisProgressData>({ 
    analyzed_records: 0, total_records: 0, analyzed_percentage: 0, status: '' 
  });
  const [isPollingProgress, setIsPollingProgress] = useState(false); // True while actively polling for analysis progress
  
  // 移除未使用的状态变量:
  // const [getResultRetryCount, setGetResultRetryCount] = useState<number>(0);

  const [analysisStats, setAnalysisStats] = useState<AnalysisResult | null>(null); // Stores summary from getAnalysisResult
  
  // 冲突处理状态
  const [conflictRecords, setConflictRecords] = useState<ConflictRecordWithAction[]>([]);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [isSubmittingConfirm, setIsSubmittingConfirm] = useState(false); // True during the confirmImport API call
  const [filterType, setFilterType] = useState<ConflictFilterType>('all'); // For filtering conflicts on UI
  const [filteredConflictRecords, setFilteredConflictRecords] = useState<ConflictRecordWithAction[]>([]); // Derived from conflictRecords and filterType
  const [conflictSelections, setConflictSelections] = useState<Set<number>>(new Set());
  const [selectionMode, setSelectionMode] = useState<'all'|'none'|'custom'>('all');

  // 导入完成状态
  const [importLogDetails, setImportLogDetails] = useState<ImportConfirmResultData | null>(null);

  // 扩展组件状态以支持稳定性检查
  // 移除未使用的状态变量:
  // const [statusStabilityCounter, setStatusStabilityCounter] = useState<number>(0);
  const [lastAnalysisStatusRef, setLastAnalysisStatusRef] = useState<string | null>(null);
  const [isConfirmingCompletion, setIsConfirmingCompletion] = useState<boolean>(false);

  // 新增：跟踪数据获取阶段
  const [dataLoadingPhase, setDataLoadingPhase] = useState<number>(-1); // -1 表示未开始，0-3表示阶段
  const [dataLoadingProgress, setDataLoadingProgress] = useState<number>(0); // 0-100

  // 在组件内部添加新的 ref ...
  // 在其他 refs 定义后添加
  const isResettingRef = useRef<boolean>(false);  // 防止重复触发重置

  // --- Helper functions for sessionStorage ---
  const saveImportSession = useCallback((sessionId: string, step: 'analyze' | 'confirm') => {
    excelImportService.getSession(sessionId).then((sessionInfo: SessionInfoData | null) => {
      if (sessionInfo) {
        // 使用改进后的 saveSession 接口传递会话信息和步骤
        saveSession(sessionInfo, step);
      }
    }).catch((error: Error) => {
      console.error('Failed to store session:', error);
      toast({
        title: "会话存储警告",
        description: "无法保存会话信息，如果离开页面可能会丢失导入进度。",
        variant: "destructive"
      });
    });
  }, [saveSession, toast]);
  
  const clearSessionStorage = useCallback(() => {
    try {
      clearSession();
      console.log('[ExcelImport] 会话已清除');
    } catch (e) {
      console.error('[ExcelImport] 会话清除失败:', e);
    }
  }, [clearSession]);
  
  // --- Core functions ---

  // 1. Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast({
          title: "文件类型错误",
          description: "请上传Excel格式的文件（.xlsx或.xls）",
          variant: "destructive",
        });
        if (fileInputRef.current) fileInputRef.current.value = ""; // Clear input
        setSelectedFile(null);
        return;
      }
      setSelectedFile(file);
      // Reset previous analysis/import states if a new file is selected
      setImportSessionId(null);
      setFileDetails(null);
      setAnalysisProgress({ analyzed_records: 0, total_records: 0, analyzed_percentage: 0, status: '' });
      setIsPollingProgress(false);
      setAnalysisStats(null);
      setConflictRecords([]);
      setImportLogDetails(null);
      // currentStep will be reset if upload starts, or stays 'select'
      console.log('[ExcelImport] File selected:', file.name);
    } else {
      setSelectedFile(null);
    }
  };

  // Callback to process an active session (either from props or storage)
  const handleProcessActiveSession = useCallback(async (sessionToProcess: SessionInfoData) => {
    console.info('[ExcelImport] handleProcessActiveSession called with:', sessionToProcess);
    if (!sessionToProcess || !sessionToProcess.session_id) {
      console.error('[ExcelImport] handleProcessActiveSession: Invalid sessionData provided.');
      toast({ title: "错误", description: "尝试处理的会话信息无效。", variant: "destructive" });
      return;
    }

    // Set the core session details for the component
    setImportSessionId(sessionToProcess.session_id);
    setFileDetails({ 
      name: sessionToProcess.file_name || 'N/A', 
      totalRecords: sessionToProcess.record_count || 0 
    });
    setActiveSessionToProcess(sessionToProcess); // Store the detailed session info
    setIsSessionOverrideActive(true); // Indicate this component is now driven by an active session

    // Update UI based on session status
    const status = sessionToProcess.status;
    if (status === 'analyzed' || status === 'conflict_resolution') {
      console.log('[ExcelImport] Active session is ', status, '. Setting step to analyze to fetch/display results.');
      setCurrentStep('analyze');
      setIsPollingProgress(true); 
    } else if (status === 'upload' || status === 'analysis_start' || status === 'analyzing') {
      console.log('[ExcelImport] Active session is ', status, '. Setting step to analyze to continue/start polling progress.');
      setCurrentStep('analyze');
      setIsPollingProgress(true);
    } else {
      console.warn('[ExcelImport] handleProcessActiveSession: Received session with unhandled status for auto-processing:', status);
      setCurrentStep('select');
    }
  }, [toast, setImportSessionId, setFileDetails, setActiveSessionToProcess, setIsSessionOverrideActive, setCurrentStep, setIsPollingProgress]);

  // useEffect for initialActiveSessionData (ensure it calls the new handleProcessActiveSession)
  useEffect(() => {
    if (
      initialActiveSessionData &&
      initialActiveSessionData.has_active_session &&
      initialActiveSessionData.session_info
    ) {
      const sessionInfoFromProp: SessionInfoData = initialActiveSessionData.session_info;
      const { status } = sessionInfoFromProp;
      if (status === 'cancelled' || status === 'imported' || status === 'error') {
        // ... (terminal state logic as before)
        setIsSessionOverrideActive(false);
        setActiveSessionToProcess(null);
        return;
      }
      if (currentStep === 'select' && 
          (status === 'analyzed' || status === 'analyzing' || status === 'conflict_resolution' || status === 'upload')){
          // Pass the session_info part to handleProcessActiveSession
          handleProcessActiveSession(sessionInfoFromProp);
      } else {
        // If not in 'select' step, or status not suitable for immediate processing by this component,
        // still update the active session override flags if a valid session is passed.
        if (status !== 'cancelled' && status !== 'imported' && status !== 'error') {
            setIsSessionOverrideActive(true);
            setActiveSessionToProcess(sessionInfoFromProp);
        } else {
            setIsSessionOverrideActive(false);
            setActiveSessionToProcess(null);
        }
      }
    } else if (initialActiveSessionData && !initialActiveSessionData.has_active_session) {
      setIsSessionOverrideActive(false);
      setActiveSessionToProcess(null);
    }
  }, [initialActiveSessionData, currentStep, handleProcessActiveSession]);

  // useEffect for session recovery (ensure it calls the new handleProcessActiveSession)
  useEffect(() => {
    if (initialActiveSessionData?.has_active_session || currentStep !== 'select') return;
    const savedSessionId = sessionStorage.getItem('importSessionId');
    const savedStep = sessionStorage.getItem('importStep') as ImportStep; // Type assertion
    const savedTimestamp = sessionStorage.getItem('importTimestamp');

    if (savedSessionId && savedStep && savedTimestamp) {
      const timestamp = parseInt(savedTimestamp, 10);
      const now = Date.now();
      const minutesPassed = (now - timestamp) / (1000 * 60);

      // Consider session storage stale after ~28 minutes (slightly less than backend expiry)
      if (minutesPassed > 28) {
        console.log('[ExcelImport] SessionStorage data is stale, clearing.');
        clearSessionStorage(); // clearSessionStorage needs to be defined or imported
      return;
    }
    
      console.log('[ExcelImport] Found session in sessionStorage:', savedSessionId, 'Step:', savedStep);
      // At this point, we have a potentially valid session ID from storage.
      // We need to verify its status with the backend.
      // This is similar to how initialActiveSessionData is handled.
      // We can call a function that fetches the session details and then processes it.
      
      const verifyAndRestoreSession = async (sessionId: string) => {
        try {
          // Fetch the current status of this session from the backend
          // We use getActiveImportSession because it returns all necessary flags
          const sessionResponse = await excelImportService.getActiveImportSession(); 
          // Note: getActiveImportSession currently doesn't take a session ID. 
          // It finds THE active session. If the stored one is THE active one, this works.
          // If not, we might need a getSpecificSessionDetails(id) in the service.
          // For now, assuming getActiveImportSession will return our stored session if it's still the one.

          if (sessionResponse && sessionResponse.has_active_session && sessionResponse.session_info && sessionResponse.session_info.session_id === sessionId) {
            const liveSessionInfo = sessionResponse.session_info;
            console.log('[ExcelImport] Verified session from storage with backend:', liveSessionInfo);
            
            if (liveSessionInfo.status === 'cancelled' || liveSessionInfo.status === 'imported' || liveSessionInfo.status === 'error') {
              console.log('[ExcelImport] Restored session is already in a terminal state:', liveSessionInfo.status);
        clearSessionStorage();
        return;
      }
    
            // Pass the session_info part to handleProcessActiveSession
            handleProcessActiveSession(liveSessionInfo);
            toast({ title: "导入会话已恢复", description: `继续处理文件 ${liveSessionInfo.file_name}.`, duration: 3000 });

        } else {
            console.log('[ExcelImport] Session from storage not found активным or mismatch with current active session on backend. Clearing storage.');
            clearSessionStorage();
          }
        } catch (error) {
          console.error('[ExcelImport] Error verifying session from storage with backend:', error);
          clearSessionStorage(); // Clear storage on error to prevent loops
        }
      };

      verifyAndRestoreSession(savedSessionId);
    }
  // Add dependencies: initialActiveSessionData, currentStep, clearSessionStorage, toast
  // handleProcessActiveSession might not be directly needed here if we set states to trigger polling
  }, [initialActiveSessionData, currentStep, clearSessionStorage, toast, handleProcessActiveSession]); 

  // CHANGE: [2024-07-27] Moved resetForm definition before polling useEffect
  const resetForm = useCallback(async (options?: { showToast?: boolean, toastMessage?: string }) => {
    console.log('[ExcelImport] Resetting form and cancelling session if active...');
    const { showToast = true, toastMessage } = options || {};
    
    // 已经在处理重置，防止重复调用
    if (isResettingRef.current) {
      console.log('[ExcelImport] 重置操作已在进行中，忽略重复调用');
      return;
    }
    
    // 设置重置标志
    isResettingRef.current = true;
    
    // 确保立即停止轮询 - 首先清除计时器和状态标志
    if (progressPollingRef.current) {
      clearTimeout(progressPollingRef.current);
      progressPollingRef.current = null;
    }
    setIsPollingProgress(false);
    
    pollCountRef.current = 0;
    lastProgressRef.current = -1;
    const currentSessionIdToCancel = importSessionId; // Capture before resetting state

    // 重要：强制立即更新主要状态，确保UI能立即反应
    // 使用函数形式的setState确保基于最新状态更新，避免批处理问题
    const forceStateReset = () => {
      console.log('[ExcelImport] 强制重置组件状态...');
      
      // 最重要的UI状态先更新 - currentStep决定渲染内容
    setCurrentStep('select');
      
      // 重置所有轮询和进度相关状态
      setIsPollingProgress(false);
      setAnalysisProgress({ analyzed_records: 0, total_records: 0, analyzed_percentage: 0, status: '' });
      setDataLoadingPhase(-1);
      setDataLoadingProgress(0);
      
      // 重置其他状态
    setSelectedFile(null);
    setUploadProgress(0);
        setIsUploading(false);
    setImportSessionId(null);
    setFileDetails(null);
    setAnalysisStats(null);
          setConflictRecords([]);
          setShowConflictModal(false);
    setIsSubmittingConfirm(false);
    setFilterType('all');
    setFilteredConflictRecords([]);
    setImportLogDetails(null);
    setActiveSessionToProcess(null);
    setIsSessionOverrideActive(false);
      setIsConfirmingCompletion(false);

      // 确保引用类型的状态也被重置
      isConfirmingCompletionRef.current = false;
      previousStatusRef.current = null;
      
      // 重置文件输入
    if (fileInputRef.current) fileInputRef.current.value = "";
      
      // 清除会话存储
    clearSessionStorage();
    };
    
    // 立即执行状态重置
    forceStateReset();

    // 新增: 记录重置时间，用于防止用户立即进行新上传
    // @ts-ignore - 添加全局变量跟踪最后重置时间
    window.lastResetTime = Date.now();

    // 尝试取消后端会话（如果存在）
    if (currentSessionIdToCancel) {
      if (showToast) toast({ title: "正在取消操作", description: "正在尝试取消任何进行中的服务器会话...", duration: 2000 });
      try {
        await excelImportService.cancelImport(currentSessionIdToCancel);
        // 再次重置状态以防万一 - 确保取消后的状态也是干净的
        forceStateReset();
        
        if (showToast) toast({ 
          title: "操作已取消", 
          description: toastMessage || "之前的导入操作已被取消。",
          duration: 3000  // 增加持续时间
        });
      } catch (cancelError: any) {
        // 改进错误处理：当会话已处于取消状态时不显示错误
        const isAlreadyCancelledError = 
          cancelError.message?.includes("已取消") || 
          cancelError.message?.includes("无需取消");
        
        if (isAlreadyCancelledError) {
          console.log('[ExcelImport] 会话已处于取消状态，无需重复取消');
          // 即使会话已处于取消状态，也再次重置UI以确保一致性
          forceStateReset();
          
          if (showToast) toast({ 
            title: "操作已取消", 
            description: toastMessage || "导入操作已处于取消状态。",
            duration: 3000
          });
        } else {
          // 其他错误正常显示
          console.error('[ExcelImport] 取消会话时发生错误:', cancelError);
          // 即使出错，也要确保UI重置
          forceStateReset();
          
          if (showToast) toast({ 
            title: "取消后端会话失败", 
            description: cancelError.message || "未能成功取消服务器上的会话，但本地已重置。", 
            variant: "destructive",
            duration: 5000
          });
        }
      } finally {
        // 在短暂延迟后重置标志，确保React有足够时间更新UI
        setTimeout(() => {
          isResettingRef.current = false;
          console.log('[ExcelImport] 重置标志已清除，可以接受新的重置操作');
        }, 100);
      }
    } else {
      if (showToast && toastMessage) toast({ 
        title: "操作完成", 
        description: toastMessage,
        duration: 3000
      });
      
      // 在短暂延迟后重置标志
      setTimeout(() => {
        isResettingRef.current = false;
      }, 100);
    }
    
    console.log('[ExcelImport] Form has been reset.');
  }, [
    importSessionId, toast, clearSessionStorage, 
    setCurrentStep, setSelectedFile, setUploadProgress, setIsUploading, 
    setImportSessionId, setFileDetails, setAnalysisProgress, setIsPollingProgress,
    setAnalysisStats, setConflictRecords, setShowConflictModal, setIsSubmittingConfirm,
    setFilterType, setFilteredConflictRecords, setImportLogDetails, 
    setActiveSessionToProcess, setIsSessionOverrideActive,
    setDataLoadingPhase, setDataLoadingProgress, setIsConfirmingCompletion
  ]);

  // Function to confirm import when no conflicts are found
  const confirmImportWithoutConflicts = useCallback(async (sessionIdToConfirm: string) => {
    if (!sessionIdToConfirm) {
        console.error('[ExcelImport] confirmImportWithoutConflicts: sessionId is missing.');
        toast({ title: "错误", description: "无法确认导入，会话ID丢失。", variant: "destructive" });
        return;
    }
    console.log('[ExcelImport] No conflicts found. Proceeding with direct import for session:', sessionIdToConfirm);
    setIsSubmittingConfirm(true); 
    try {
        const result: ImportConfirmResultData = await excelImportService.confirmImport(sessionIdToConfirm, []);
        setImportLogDetails(result);
        setCurrentStep('completed'); 
        setIsSubmittingConfirm(false);
        clearSessionStorage(); // Clear local session on successful import
        toast({
            title: "导入成功",
            description: `Excel文件已成功导入。共处理 ${result.total_records} 条记录。`,
            variant: "default",
          duration: 5000,
            action: result.import_log_id ? (
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/records/import-history/${result.import_log_id}`}>查看导入日志</Link>
                </Button>
            ) : undefined,
        });
    } catch (error: any) {
        console.error("[ExcelImport] confirmImportWithoutConflicts - Error during import:", error);
        setIsSubmittingConfirm(false);
        // setCurrentStep('select'); // Don't revert to select, let user see error and manually reset if needed
      toast({
            title: "导入失败",
            description: error.message || "直接确认导入过程中发生未知错误。",
        variant: "destructive",
            duration: 7000,
        });
        // Do not call resetForm here, as it might conflict with the session state on the server if it errored.
        // Let user manually reset or retry if applicable.
        // clearSessionStorage(); // Only clear on success to allow retries or inspection
    }
  }, [toast, clearSessionStorage, setCurrentStep, setIsSubmittingConfirm, setImportLogDetails]);

  // useEffect for polling the analysis progress and result - modify the logic here
  useEffect(() => {
    if (!importSessionId || currentStep !== 'analyze' || !isPollingProgress) return;
    
    let isMounted = true;
    console.log(`[ExcelImport] 开始轮询导入会话分析进度，会话ID: ${importSessionId}`);
    
    // 立即清除任何可能存在的计时器，确保没有重复轮询
    if (progressPollingRef.current) {
      clearTimeout(progressPollingRef.current);
    progressPollingRef.current = null;
    }
    
    // 终止轮询的辅助函数，确保清理所有状态
    const stopPollingImmediately = () => {
      console.log(`[ExcelImport] 轮询停止函数被调用，立即清除计时器和状态`);
      if (progressPollingRef.current) {
        clearTimeout(progressPollingRef.current);
        progressPollingRef.current = null;
      }
      
      if (isMounted) {
        setIsPollingProgress(false);
      }
    };
    
    const poll = async () => {
      if (!isMounted || !importSessionId) return;
      
      try {
        // 第一步：获取分析进度
        const progressData = await excelImportService.getAnalysisProgress(importSessionId);
        if (!isMounted) return;
        
        // 记录接收到的状态以便调试
        console.log(`[ExcelImport] 收到状态: ${progressData.status}, 进度: ${progressData.analyzed_percentage}%, 前一状态: ${previousStatusRef.current}`);
        
        // 优先检查终止状态
        if (progressData.status === 'cancelled' || progressData.status === 'error' || progressData.status === 'failed') {
          console.log(`[ExcelImport] 检测到终止状态: ${progressData.status}，立即停止轮询`);
          
          // 1. 立即停止轮询状态和清除计时器
          stopPollingImmediately();
          
          // 2. 使用setTimeout确保状态更新已完成后再执行重置操作
          setTimeout(() => {
            if (!isMounted) return;
            
            // 3. 根据终止类型显示不同的通知
            if (progressData.status === 'cancelled') {
              toast({
                title: "导入已取消",
                description: "导入会话已被取消，您可以重新开始导入。",
                variant: "default"
              });
            } else {
              toast({
                title: "导入发生错误",
                description: `导入会话在处理过程中遇到${progressData.status === 'error' ? '错误' : '失败'}，请尝试重新导入。`,
                variant: "destructive"
              });
            }
            
            // 4. 重置表单，但不再显示额外的toast，因为已经显示了特定的通知
            resetForm({ showToast: false });
          }, 10);
          
          return; // 立即退出poll函数，防止任何后续代码执行
        }
        
        // 简化的状态检测 - 只检查是否分析已完成
        const isFullyAnalyzed = progressData.status === 'analyzed' && progressData.analyzed_percentage >= 100;
        const isStateJustChanged = previousStatusRef.current !== progressData.status;
        previousStatusRef.current = progressData.status;
        
        // 更新UI显示进度
        setAnalysisProgress(progressData);
        if (progressData.total_records > 0 && (!fileDetails || progressData.total_records !== fileDetails.totalRecords)) {
          setFileDetails(prev => ({ 
            name: prev?.name || selectedFile?.name || 'N/A', 
            totalRecords: progressData.total_records 
          }));
        }
        
        // 轮询间隔提示
        if (progressData.analyzed_percentage === lastProgressRef.current && pollCountRef.current > 3 && pollCountRef.current % 3 === 0) {
          toast({
            title: '分析正在进行中',
            description: `已处理 ${progressData.analyzed_records}/${progressData.total_records} 条记录 (${progressData.analyzed_percentage.toFixed(0)}%)，请耐心等待...`,
            duration: 3000
          });
        }
        
        // 提示分析将进入最终阶段
        if (progressData.analyzed_percentage >= 95 && lastProgressRef.current < 95) {
          toast({
            title: '分析即将完成',
            description: '即将进入最终处理阶段，系统可能需要额外的时间准备结果...',
            duration: 5000
          });
        }
        
        // 分析已完成状态处理 - 仅在真正的"analyzed"状态时获取结果
        if (isFullyAnalyzed && !isConfirmingCompletionRef.current) {
          console.log('[ExcelImport] 检测到分析已完成，准备获取结果');
          
          // 添加重要的状态二次确认 - 确保在进入加载阶段前验证会话状态
          try {
            // 再次获取最新的会话状态进行二次确认
            const latestProgress = await excelImportService.getAnalysisProgress(importSessionId);
            if (!isMounted || !isPollingProgress) return;
            
            // 如果状态已变成终止状态，则不继续进入数据加载阶段
            if (['cancelled', 'error', 'failed'].includes(latestProgress.status)) {
              console.log(`[ExcelImport] 二次确认检测到会话已是终止状态 ${latestProgress.status}，放弃进入数据加载阶段`);
              stopPollingImmediately();
              // 根据终止类型选择处理方式
              setTimeout(() => {
                if (!isMounted) return;
                
                if (latestProgress.status === 'cancelled') {
                  toast({
                    title: "导入已取消",
                    description: "导入会话已被取消，您可以重新开始导入。",
                    variant: "default"
                  });
                } else {
                  toast({
                    title: "导入发生错误",
                    description: `导入会话在处理过程中遇到${latestProgress.status === 'error' ? '错误' : '失败'}，请尝试重新导入。`,
                    variant: "destructive"
                  });
                }
                
                resetForm({ showToast: false });
              }, 10);
              return;
            }
            
            // 仅当状态确认为analyzed才继续
            if (latestProgress.status !== 'analyzed') {
              console.log(`[ExcelImport] 虽然进度100%，但状态为 ${latestProgress.status}，暂不加载结果，继续轮询`);
              // 继续轮询，不设置isConfirmingCompletionRef
              if (isPollingProgress && isMounted) {
                progressPollingRef.current = setTimeout(poll, FAST_POLL_INTERVAL); // 使用快速轮询
              }
              return;
            }
          } catch (statusCheckError) {
            // 二次状态确认出错，记录但不阻止流程
            console.warn('[ExcelImport] 二次状态确认失败，但仍将继续:', statusCheckError);
          }
          
          // 设置确认状态防止重复请求
          isConfirmingCompletionRef.current = true;
          setIsConfirmingCompletion(true);
          
          // 进入数据加载阶段
          setDataLoadingPhase(0);
          setDataLoadingProgress(5); // 刚开始，显示少量进度
          
          // 添加视觉反馈
          setAnalysisProgress(prev => ({
            ...prev,
            status: DATA_LOADING_PHASES[0],
          }));
          
          // 提供用户反馈
          toast({
            title: "分析已完成",
            description: "正在准备加载结果数据...",
            duration: 2000
          });
          
          // 如果状态刚变化，添加极短延迟
          const confirmDelay = isStateJustChanged ? COMPLETION_CONFIRM_DELAY : 0;
          
          setTimeout(async () => {
            if (!isMounted) return;
            
            // 智能重试计数器
            let resultRetryCount = 0;
            const maxResultRetries = MAX_GET_RESULT_RETRIES;
            
            const attemptGetResult = async (): Promise<void> => {
              // 在每次异步操作前检查当前状态
              if (!isMounted || !isPollingProgress || !importSessionId) {
                console.log('[ExcelImport] 组件已卸载或轮询已停止，放弃获取结果');
                return;
              }
              
              try {
                console.log(`[ExcelImport] 尝试获取分析结果 (尝试 ${resultRetryCount + 1}/${maxResultRetries})`);
                
                // 更新为获取数据阶段
                setDataLoadingPhase(1);
                setDataLoadingProgress(25); // 第一阶段，25%
                setAnalysisProgress(prev => ({
                  ...prev,
                  status: DATA_LOADING_PHASES[1]
                }));
                
                // 获取分析最终完整结果
            const fullResultPayload: ExcelAnalysisResultPayload = await excelImportService.getAnalysisResult(importSessionId);
            if (!isMounted) return;
                
                // 更新为处理数据阶段
                setDataLoadingPhase(2);
                setDataLoadingProgress(65);
                setAnalysisProgress(prev => ({
                  ...prev,
                  status: DATA_LOADING_PHASES[2]
                }));
                
                // 短暂延迟处理结果以便显示进度
                setTimeout(() => {
                  if (!isMounted) return;
                  
                  // 更新为准备显示结果阶段
                  setDataLoadingPhase(3);
                  setDataLoadingProgress(85);
                  setAnalysisProgress(prev => ({
                    ...prev,
                    status: DATA_LOADING_PHASES[3]
                  }));
                  
                  setTimeout(() => {
                    if (!isMounted) return;
                    
                    // 成功获取结果，停止轮询并重置计数器
                    console.log(`[ExcelImport] 成功获取完整分析结果`, fullResultPayload);
                    stopPollingImmediately(); // 使用新的辅助函数停止轮询
                    isConfirmingCompletionRef.current = false;
                    setIsConfirmingCompletion(false);
                    pollCountRef.current = 0;
                    
                    // 最终进度
                    setDataLoadingProgress(100);
                    
                    // 保存会话状态并进入确认阶段
                    saveImportSession(importSessionId, 'confirm');
                    
                    // 处理分析结果
            setAnalysisStats(fullResultPayload.analysis_result || { total: 0, new: 0, update: 0, identical: 0, error: 0 });
            const conflicts = fullResultPayload.conflict_records || [];
                    
                    // 根据是否有冲突决定后续流程
            if (conflicts.length === 0) {
              confirmImportWithoutConflicts(importSessionId || "");
                    } else {
                      setConflictRecords(conflicts.map(record => ({ 
                        ...record, 
                        action: record.conflict_type === 'identical' ? ConflictResolutionAction.SKIP : ConflictResolutionAction.UPDATE 
                      })));
              setCurrentStep('confirm');
                      toast({ 
                        title: "Excel文件分析完成", 
                        description: `检测到 ${conflicts.length} 条记录需要处理。`, 
                        duration: 10000 
                      });
                    }
                  }, 300); // 显示"准备显示结果"阶段的短延迟
                }, 500); // 显示"处理结果数据"阶段的短延迟
                
              } catch (getResultError: any) {
            if (!isMounted) return;
                
                // 处理获取最终结果时的错误
                if (getResultError.name === 'AnalysisNotCompleteError') {
                  // 尽管状态为analyzed，但后端仍未准备好结果
                  resultRetryCount++;
                  console.warn(`[ExcelImport] 后端返回结果尚未准备好，稍后重试 (${resultRetryCount}/${maxResultRetries})`);
                  
                  // 更新重试进度
                  setDataLoadingProgress(Math.min(20 + resultRetryCount * 5, 60)); // 重试进度，最多到60%
                  
                  if (resultRetryCount < maxResultRetries) {
                    // 简单的重试策略 - 固定短间隔
                    const retryDelay = 300;
                    
                    if (resultRetryCount % 3 === 0) {
                      toast({
                        title: "正在处理数据",
                        description: "服务器正在准备分析结果... (尝试 " + (resultRetryCount + 1) + "/" + maxResultRetries + ")",
                        duration: 2000
                      });
                    }
                    
                    // 更新状态显示重试次数
                    setAnalysisProgress(prev => ({
                      ...prev,
                      status: `正在获取分析结果 (尝试 ${resultRetryCount + 1}/${maxResultRetries})`
                    }));
                    
                    // 设置下一次重试
                    setTimeout(attemptGetResult, retryDelay);
                  } else {
                    // 重试次数用尽，回退到轮询模式
                    console.error(`[ExcelImport] 结果获取重试次数已用尽，回退到轮询模式`);
                    isConfirmingCompletionRef.current = false;
                    setIsConfirmingCompletion(false);
                    setDataLoadingPhase(-1); // 重置数据加载阶段
                    
                    // 检查是否仍在轮询，仅在需要时恢复轮询
                    if (isPollingProgress && isMounted) {
                      progressPollingRef.current = setTimeout(poll, NORMAL_POLL_INTERVAL);
                    }
                  }
                } else {
                  // 其他错误处理
                  isConfirmingCompletionRef.current = false;
                  setIsConfirmingCompletion(false);
                  setDataLoadingPhase(-1); // 重置数据加载阶段
                  
                  // 检查会话状态错误
                  if (getResultError.name === 'SessionNotFoundError' || getResultError.message?.includes('会话不存在') || getResultError.message?.includes('会话已过期')) {
                    console.error(`[ExcelImport] 会话不存在或已过期: ${importSessionId}`);
                    stopPollingImmediately(); // 立即停止轮询
                    toast({ 
                      title: "获取分析结果失败", 
                      description: getResultError.message || "会话不存在或已过期", 
                      variant: "destructive" 
                    });
                    resetForm({ showToast: false });
                  } else if (getResultError.name === 'SessionCancelledError' || getResultError.message?.includes('会话已取消')) {
                    console.error(`[ExcelImport] 会话已被取消: ${importSessionId}`);
                    stopPollingImmediately(); // 立即停止轮询
                    toast({ 
                      title: "会话已取消", 
                      description: getResultError.message || "导入会话已被取消", 
                      variant: "default" 
                    });
                    resetForm({ showToast: false });
                  } else {
                    // 处理其他未预期的错误
                    console.error(`[ExcelImport] 获取分析结果未知错误:`, getResultError);
                    stopPollingImmediately(); // 立即停止轮询
                    toast({ 
                      title: "获取分析结果失败", 
                      description: getResultError.message || "无法加载分析详情", 
                      variant: "destructive" 
                    });
                    resetForm({ showToast: false });
                  }
                }
              }
            };
            
            // 开始首次尝试获取结果
            attemptGetResult();
            
          }, confirmDelay);
          
          // 在等待确认期间不执行其他操作
                  return;
                }
        
        // 如果未进入结果获取阶段，继续轮询
        if (!isConfirmingCompletionRef.current) {
          pollCountRef.current++;
          
          // 确认当前轮询仍然是活跃状态，防止多余的轮询请求
          if (!isPollingProgress || !isMounted) {
            console.log(`[ExcelImport] 轮询已停止，不再安排下一次轮询`);
            return;
          }
          
          // 简单明确的轮询策略:
          // 1. 进度100%时使用快速轮询(200ms)
          // 2. 其他情况使用正常轮询(1000ms)
          let nextPollDelay = NORMAL_POLL_INTERVAL;
          
          // 当进度为100%时启用快速轮询检测状态变化
          if (progressData.analyzed_percentage >= 100) {
            // 重要: 当进度已达100%时，使用快速轮询频率检测状态变化
            nextPollDelay = FAST_POLL_INTERVAL;
            
            // 如果进度100%持续了一段时间但状态仍是analyzing，添加用户提示
            if (progressData.status === 'analyzing' && 
                pollCountRef.current > 10 && 
                pollCountRef.current % 5 === 0) {
              toast({
                title: "正在最终处理",
                description: "文件分析进度已达100%，等待系统完成最终处理...",
                duration: 3000
              });
            }
          } 
          // 其他进度区间的正常轮询
          else if (progressData.analyzed_percentage < 30) {
            nextPollDelay = 800;
          } else if (progressData.analyzed_percentage < 80) {
            nextPollDelay = 1000;
          } else {
            nextPollDelay = 600;
          }
          
          // 保存当前进度以便下次轮询比较
        lastProgressRef.current = progressData.analyzed_percentage;
          
          // 最终检查再安排下一次轮询
          if (isPollingProgress && isMounted) {
            progressPollingRef.current = setTimeout(poll, nextPollDelay);
          } else {
            console.log(`[ExcelImport] 轮询已被停止，不再安排下一次轮询`);
          }
        }
      } catch (error: any) {
        if (!isMounted) return;
        
        // 轮询过程异常处理
        if (error.name === 'SessionExpiredError' || error.message?.includes('会话已过期')) {
          console.error(`[ExcelImport] 轮询时发现会话已过期:`, error);
          stopPollingImmediately(); // 立即停止轮询
          toast({ 
            title: "会话已过期", 
            description: error.message || "导入会话已失效，请重新操作。", 
            variant: "destructive" 
          });
          resetForm({showToast: false}); 
          return; // 会话已过期，停止轮询
        }
        
        // 检查是否是会话取消的错误
        if (error.name === 'SessionCancelledError' || error.message?.includes('会话已取消')) {
          console.error(`[ExcelImport] 轮询时发现会话已取消:`, error);
          stopPollingImmediately(); // 立即停止轮询
          toast({ 
            title: "会话已取消", 
            description: error.message || "导入会话已被取消或终止。", 
            variant: "default" 
          });
          resetForm({showToast: false});
          return; // 会话已取消，停止轮询
        }
        
        // 其他错误进行有限次数的重试
          pollCountRef.current++;
        console.warn(`[ExcelImport] 轮询分析进度错误 (尝试 ${pollCountRef.current}/8):`, error);
        
        // 确认当前轮询仍然是活跃状态
        if (pollCountRef.current < 8 && isPollingProgress && isMounted) {
          // 渐进式增加重试间隔
          const retryDelay = Math.min(1500 * pollCountRef.current, 10000);
          progressPollingRef.current = setTimeout(poll, retryDelay);
          
          // 尝试次数较多时显示提示
          if (pollCountRef.current > 3) {
            toast({
              title: "获取进度信息延迟",
              description: "系统正在尝试恢复连接，请稍候...",
              duration: retryDelay - 500
            });
          }
          } else {
          // 重试次数耗尽，终止轮询
          stopPollingImmediately(); // 使用新的辅助函数停止轮询
          toast({ 
            title: "分析进度获取中断", 
            description: error.message || "无法持续获取导入分析进度。", 
            variant: "destructive" 
          });
        }
      }
    };
    
    // 初始化轮询
    if (isPollingProgress) {
      pollCountRef.current = 0;
      lastProgressRef.current = -1;
      isConfirmingCompletionRef.current = false;
      setIsConfirmingCompletion(false);
      poll(); 
    }
    
    // 清理函数
    return () => {
      console.log(`[ExcelImport] 轮询useEffect清理函数执行，设置isMounted=false和清除计时器`);
      isMounted = false;
      if (progressPollingRef.current) {
        clearTimeout(progressPollingRef.current);
        progressPollingRef.current = null;
      }
    };
  }, [
    importSessionId, 
    currentStep, 
    isPollingProgress, 
    fileDetails, 
    saveImportSession, 
    toast, 
    resetForm, 
    confirmImportWithoutConflicts, 
    selectedFile, 
  ]);

  // useEffect for filtering conflict records based on filterType
  useEffect(() => {
    if (filterType === 'all') { // No !filterType check needed if 'all' is the default/base
      setFilteredConflictRecords(conflictRecords);
        } else {
      setFilteredConflictRecords(
        conflictRecords.filter(record => record.conflict_type === filterType)
      );
    }
  }, [conflictRecords, filterType]);

  const handleFileUpload = async () => {
    if (!selectedFile) {
        toast({
        title: "未选择文件", 
        description: "请先选择一个Excel文件。" 
        });
        return;
      }
      
    // 新增: 检查上次重置时间，如果刚刚取消了会话，等待一小段时间
    // @ts-ignore - 添加全局变量跟踪最后重置时间
    const lastResetTime = window.lastResetTime || 0;
    const timeSinceLastReset = Date.now() - lastResetTime;
    if (timeSinceLastReset < 2000) { // 2秒内
      toast({
        title: "请稍候",
        description: "正在清理前一个会话状态，请等待几秒后再试。",
        duration: 3000
      });
      return;
    }
    
    // 新增: 在上传前检查会话状态
    try {
      const activeSessionResponse = await excelImportService.getActiveImportSession();
      if (activeSessionResponse && activeSessionResponse.has_active_session && activeSessionResponse.session_info) {
        // 如果有活跃会话，检查其状态
        const sessionInfo = activeSessionResponse.session_info;
        
        // 如果会话已取消但仍被视为活跃，尝试强制再次取消
        if (sessionInfo.status === 'cancelled') {
          console.log('[ExcelImport] 检测到已取消但仍活跃的会话，尝试强制清理:', sessionInfo.session_id);
          try {
            await excelImportService.cancelImport(sessionInfo.session_id);
            // 添加短暂延迟，让后端有时间处理
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 会话应该已清理，继续上传
            console.log('[ExcelImport] 已取消的会话已重新清理，继续上传');
          } catch (e) {
            console.warn('[ExcelImport] 清理已取消会话时发生错误，可能会影响上传:', e);
            // 继续尝试上传，让系统返回具体错误
          }
        } else if (sessionInfo.status !== 'cancelled' && sessionInfo.status !== 'error' && sessionInfo.status !== 'imported') {
          // 真正的活跃会话，显示错误并停止
          toast({
            title: "会话冲突",
            description: `系统中已有活跃的导入会话 (${sessionInfo.file_name || '未知文件'})，请先处理该会话。`,
            variant: "destructive"
          });
          return;
        }
      }
    } catch (e) {
      // 获取会话状态出错，但不阻止上传，让系统返回具体错误
      console.warn('[ExcelImport] 检查会话状态时发生错误:', e);
      }
      
    // 重置之前的分析/导入状态，以防用户重复上传不同文件而未刷新
    setImportSessionId(null);
    setFileDetails(null);
    setAnalysisProgress({ analyzed_records: 0, total_records: 0, analyzed_percentage: 0, status: '' });
    setIsPollingProgress(false);
    setAnalysisStats(null);
    setConflictRecords([]);
    setImportLogDetails(null);
    setActiveSessionToProcess(null);
    setIsSessionOverrideActive(false);
    
    // 添加重置新增状态的代码
    setDataLoadingPhase(-1); // 确保数据加载阶段状态被重置
    setDataLoadingProgress(0); // 重置数据加载进度
    setIsConfirmingCompletion(false); // 重置确认状态
    isConfirmingCompletionRef.current = false; // 重置引用类型状态
    previousStatusRef.current = null; // 重置前一状态引用
    
    // currentStep 会在下面设置为 'upload'
    
    setIsUploading(true);
    setUploadProgress(0);
    setCurrentStep('upload');
    setFileDetails({ name: selectedFile.name, totalRecords: 0 }); // 初始显示文件名，总记录数待API返回
    
    console.log('[ExcelImport] Starting file upload and analysis for:', selectedFile.name);
    
    try {
      const startupInfo: ExcelAnalysisStartupInfoData = await excelImportService.analyzeExcelFile(
        selectedFile,
        {}, // No specific import options for now
        (progress) => {
          // Update XHR upload progress (0-100% for the file transmission itself)
          // The analysis polling will handle progress divulgação backend analysis.
          if (currentStep === 'upload') { // Only update if still in pure upload phase
            setUploadProgress(progress);
          }
        }
      );
      
      console.log('[ExcelImport] analyzeExcelFile service call successful, startupInfo:', startupInfo);

      // 后端已接受文件并创建会话，准备开始轮询分析进度
      setImportSessionId(startupInfo.import_session_id);
      // fileDetails 已经初步设置，totalRecords会由轮询更新，但可以用startupInfo的覆盖一下
      setFileDetails(prev => ({ 
        name: startupInfo.file_name || selectedFile.name, 
        totalRecords: startupInfo.total_records || prev?.totalRecords || 0 
      }));
      saveImportSession(startupInfo.import_session_id, 'analyze'); // Save to session storage
      
      // الانتقال إلى مرحلة التحليل وبدء الاقتراع
      setCurrentStep('analyze'); 
      setIsPollingProgress(true); // This will trigger the polling useEffect
      setIsUploading(false); // XHR Upload part is done, now it's backend analysis
      setUploadProgress(100); // Visually mark XHR upload as complete

              toast({
        title: "文件上传成功",
        description: `"${startupInfo.file_name || selectedFile.name}"已开始在服务器端分析。`,
      });

    } catch (error: any) {
      console.error("[ExcelImport] File upload or analysis initiation failed:", error);
      let errorTitle = "文件处理失败";
      let errorMessage = "启动Excel文件分析时发生未知错误。";

      if (error instanceof Error) {
        errorMessage = error.message;
        if (error.name === 'ActiveSessionExistsError') {
          errorTitle = "导入操作被阻止";
          // description: error.message (后端返回的错误信息，包含了谁创建了会话等)
          // 主页面ImportPage的useEffect也会检测到这个，并显示更详细的Alert
          // 这里只做简单toast提示
        } else if (error.message.includes('用户未认证')) {
            errorTitle = "认证失败";
        }
      }

      toast({ title: errorTitle, description: errorMessage, variant: "destructive" });
      
      // Reset UI to initial state on failure
      setCurrentStep('select');
      setIsUploading(false);
      setSelectedFile(null);
      setImportSessionId(null);
      setFileDetails(null);
      setIsPollingProgress(false); // Ensure polling is stopped
      if(fileInputRef.current) fileInputRef.current.value = "";
    } 
    // No finally block for setIsUploading(false) here because successful upload leads to 'analyze' step
  };

  // Function to update a single conflict record's resolution action
  // CHANGE: [2024-07-28] updateConflictAction now accepts unique identifiers instead of index
  const updateConflictAction = useCallback((commissionNumber: string, rowNum: number, newAction: ConflictResolutionAction) => {
    setConflictRecords(prevConflicts => 
      prevConflicts.map(conflict => 
        (conflict.commission_number === commissionNumber && conflict.row === rowNum) ? 
        { ...conflict, action: newAction } : 
        conflict
      )
    );
    // Optional: toast for single action change if desired, but can be noisy
    // toast({ description: `记录 ${commissionNumber} (行 ${rowNum}) 的处理方式已更新为 ${newAction}` });
  }, [setConflictRecords]); // Dependency is only setConflictRecords as it uses prevConflicts

  // Function to update all (or filtered) conflict records' resolution action
  // This function already operates on the full list or filters internally, so its signature is okay.
  const updateAllConflictsAction = useCallback((action: ConflictResolutionAction, conflictTypeFilter?: 'new' | 'update' | 'identical') => {
    setConflictRecords(prevConflicts =>
      prevConflicts.map(conflict => {
        if (conflictTypeFilter && conflict.conflict_type !== conflictTypeFilter) {
          return conflict;
        }
        if (conflict.conflict_type === 'new') return conflict; // 'new' records action should not be changed from UI here
        return { ...conflict, action: action };
      })
    );
      toast({
      title: "批量操作完成",
      description: `所有符合条件的 ${conflictTypeFilter ? conflictTypeFilter + '类型' : '可见'} 冲突记录已设置为 "${action === ConflictResolutionAction.SKIP ? '跳过' : '更新'}"。`,
      duration: 2000
    });
  }, [setConflictRecords, toast]);

  // Function to handle conflict resolution submission
  const handleConflictResolution = useCallback(async () => {
    if (!importSessionId || !analysisStats) {
      toast({ title: "错误", description: "会话信息丢失，无法提交解决方案。", variant: "destructive" });
      return;
    }
    if (conflictRecords.length === 0) {
        // 如果没有冲突记录（理论上此时不应该调用此函数，而是confirmImportWithoutConflicts）
        // 但作为安全措施，如果被调用且无冲突，则执行无冲突导入
        console.warn("[ExcelImport] handleConflictResolution called with no conflict records. Attempting direct confirm.");
        confirmImportWithoutConflicts(importSessionId);
        return;
    }

    const resolutionsToSubmit: ConflictResolution[] = conflictRecords.map(conflict => ({
      commission_number: conflict.commission_number,
      action: conflict.action, // action is part of ConflictRecordWithAction
      // 如果后端API需要 conflict_detail_id，则需要从 conflict 对象中获取并传递
      // conflict_detail_id: conflict.id, // Assuming ConflictRecordWithAction has an 'id' from ImportConflictDetail
    }));

    console.log('[ExcelImport] Submitting conflict resolutions:', resolutionsToSubmit);
    setIsSubmittingConfirm(true);
    setCurrentStep('confirm'); // Ensure step is 'confirm' or an 'importing' visual step

    try {
      const result: ImportConfirmResultData = await excelImportService.confirmImport(importSessionId, resolutionsToSubmit);
      console.log('[ExcelImport] Conflict resolution import successful:', result);

      setImportLogDetails(result);
      setCurrentStep('completed');
      setShowConflictModal(false); // Close the modal on success
      clearSessionStorage(); // Clear session storage
      
      toast({
        title: "导入成功",
        description: `成功处理 ${result.success_records + result.user_manual_skipped_records + result.system_skipped_records} 条记录，导入 ${result.success_records} 条 (新建 ${result.created_count}, 更新 ${result.updated_count})。`,
        variant: "default",
        duration: 7000,
        action: result.import_log_id ? (
            <Button variant="outline" size="sm" asChild>
                <Link href={`/records/import-history/${result.import_log_id}`}>查看导入日志</Link>
            </Button>
        ) : undefined,
      });

    } catch (error: any) {
      console.error("[ExcelImport] Error during conflict resolution import:", error);
      toast({
        title: "导入失败",
        description: error.message || "提交冲突解决方案并导入时发生未知错误。",
        variant: "destructive",
        duration: 7000,
      });
      // 不重置currentStep，允许用户重试或取消
    } finally {
      setIsSubmittingConfirm(false);
    }
  }, [
    importSessionId, 
    analysisStats, 
    conflictRecords, 
    toast, 
    clearSessionStorage,
    confirmImportWithoutConflicts, // Added as it's called inside
    // Add setters
    setIsSubmittingConfirm, setCurrentStep, setImportLogDetails, setShowConflictModal
  ]);

  // Main UI rendering function based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 'select':
        return (
          <div className="space-y-6 py-1">
            <Alert variant="default" className="bg-sky-50 border-sky-200 text-sky-700">
              <Info className="h-5 w-5 text-sky-600" />
              <AlertTitle className="font-semibold text-sky-800">操作指引：开始导入</AlertTitle>
              <AlertDescription className="text-sky-700/90">
                请选择一个有效的Excel文件 (.xlsx 或 .xls) 。系统将首先对文件进行预分析以检测与现有数据的潜在冲突，之后您可以对这些冲突进行处理并最终确认导入操作。
              </AlertDescription>
            </Alert>
            <div className="space-y-2">
              <Label htmlFor="excel-file-input" className="text-md font-medium text-gray-800">1. 选择您的Excel文件</Label>
              <div 
                className={`relative group border-2 border-dashed rounded-xl p-6 text-center transition-all duration-150 ease-in-out min-h-[180px] flex flex-col justify-center items-center 
                            ${isSessionOverrideActive || isUploading ? 'border-gray-300 bg-gray-100 cursor-not-allowed opacity-70' : 'border-gray-300 hover:border-primary hover:shadow-md'}`}
                onClick={() => !(isSessionOverrideActive || isUploading) && fileInputRef.current?.click()}
                role="button" aria-disabled={isSessionOverrideActive || isUploading} tabIndex={isSessionOverrideActive || isUploading ? -1 : 0}
              >
                <Input id="excel-file-input" ref={fileInputRef} type="file" accept=".xlsx,.xls,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" onChange={handleFileChange} className="sr-only" disabled={isSessionOverrideActive || isUploading} />
                {isUploading ? (
                    <div className="flex flex-col items-center">
                        <Clock className="h-12 w-12 text-primary mb-3 animate-spin" />
                        <p className="font-semibold text-md text-primary">文件正在上传中...</p>
                        <p className="text-sm text-muted-foreground">({uploadProgress}%)</p>
                    </div>
                ) : isSessionOverrideActive && activeSessionToProcess ? (
                    <div className="flex flex-col items-center">
                        <AlertTriangle className="h-12 w-12 text-amber-500 mb-3" />
                        <p className="font-semibold text-md text-amber-700">系统当前有活跃导入</p>
                        <p className="text-sm text-amber-600 mt-1">
                            文件: <span className="font-bold">{activeSessionToProcess.file_name || 'N/A'}</span> (状态: {activeSessionToProcess.status})<br/>
                            请先处理或在主页面取消该会话。
                        </p>
                    </div>
                ) : selectedFile ? (
                  <div className="flex flex-col items-center">
                    <FileSpreadsheet className="h-12 w-12 text-green-600 mb-3" />
                    <p className="font-semibold text-md text-gray-700 truncate max-w-xs sm:max-w-md md:max-w-lg" title={selectedFile.name}>{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">({(selectedFile.size / 1024).toFixed(1)} KB)</p>
                    <Button variant="ghost" size="sm" className="text-xs mt-2 text-red-600 hover:text-red-700 hover:bg-red-50 h-auto py-1 px-2" onClick={(e) => { e.stopPropagation(); setSelectedFile(null); if(fileInputRef.current) fileInputRef.current.value=""; }}> <RotateCcw className="mr-1 h-3 w-3"/> 清除选择</Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <DownloadCloud className="h-12 w-12 text-gray-400 group-hover:text-primary mb-3 transition-colors duration-150" />
                    <p className="font-semibold text-md text-gray-700 group-hover:text-primary transition-colors">点击此处或拖拽文件上传</p>
                    <p className="text-sm text-muted-foreground mt-1">支持 .xlsx, .xls 格式的文件</p>
                  </div>
                )}
              </div>
            </div>
            <Button type="button" onClick={handleFileUpload} disabled={!selectedFile || isUploading || isSessionOverrideActive} className="w-full py-3 text-md font-semibold" size="lg">
              {isUploading ? <><Clock className="mr-2 h-5 w-5 animate-spin" />上传并处理中...</> : isSessionOverrideActive ? <><AlertCircle className="mr-2 h-5 w-5" />系统正忙，请稍候</> : <><Upload className="mr-2 h-5 w-5" />上传文件并启动分析</>}
            </Button>
            {isSessionOverrideActive && !isUploading && (
                 <p className="text-xs text-center text-amber-700 bg-amber-50 p-2 rounded-md border border-amber-200">提示: 当前有一个导入会话由其他操作占用或由其他用户发起。您需要等待其完成后才能开始新的导入，或在"导入页"取消该会话（如果权限允许）。</p>
            )}
          </div>
        );
        
      case 'upload':
      case 'analyze':
        return (
          <div className="space-y-6 py-4">
            <Alert variant="default" className={`p-4 rounded-lg border shadow-sm ${currentStep === 'upload' ? 'bg-blue-50 border-blue-300 text-blue-800' : 'bg-amber-50 border-amber-300 text-amber-800'}`}>
              <div className="flex items-center">
                <div className={`flex-shrink-0 p-3 rounded-full mr-4 ${currentStep === 'upload' ? 'bg-blue-100' : 'bg-amber-100'}`}>
                  {currentStep === 'upload' ? <FileUp className="h-7 w-7 text-blue-700" /> : <Info className={`h-7 w-7 text-amber-700 ${isPollingProgress ? 'animate-pulse' : ''}`} /> }
                </div>
                <div className="flex-1">
                  <AlertTitle className="font-bold text-lg">
                    {currentStep === 'upload' ? '文件上传中' : (
                      dataLoadingPhase >= 0 ? 
                        `分析结果数据加载中 (${DATA_LOADING_PHASES[dataLoadingPhase]})` : 
                        (analysisProgress.status === 'analyzed' || analysisProgress.analyzed_percentage >= 100 ? '数据分析已完成' : '服务器正在分析数据')
                    )}
                  </AlertTitle>
                  <AlertDescription className="mt-1 text-sm">
                    {fileDetails?.name && <span className="block font-medium" title={fileDetails.name}>文件: {fileDetails.name} ({fileDetails.totalRecords > 0 ? `${fileDetails.totalRecords} 条记录` : selectedFile ? `${(selectedFile.size/1024).toFixed(1)}KB` : ''})</span>}
                    {currentStep === 'upload' && '您的文件正在安全上传到服务器。上传完成后将自动开始数据分析过程。'}
                    {currentStep === 'analyze' && (
                      dataLoadingPhase >= 0 ? 
                        `正在从服务器加载分析后的数据，请稍候...` : 
                        '系统正在处理您的数据并识别潜在冲突，请耐心等候。此过程可能需要数分钟，具体取决于文件大小和数据复杂度。'
                    )}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
            
            <div className="space-y-2 px-1">
              <div className="flex items-baseline justify-between text-sm mb-1">
                <span className="font-medium text-gray-700">
                  {currentStep === 'upload' ? '上传进度' : (
                    dataLoadingPhase >= 0 ? 
                      '数据加载进度' : 
                      (analysisProgress.status === 'analyzed' || analysisProgress.analyzed_percentage >= 100 ? '分析结果' : '分析进度')
                  )}:
                </span>
                <span className={`font-semibold text-xl ${currentStep === 'upload' ? 'text-blue-600' : (dataLoadingPhase >= 0 ? 'text-purple-600' : 'text-amber-600')}`}>
                  {currentStep === 'upload' ? `${uploadProgress}%` : (
                    dataLoadingPhase >= 0 ? 
                      `${dataLoadingProgress}%` : 
                      `${analysisProgress.analyzed_percentage.toFixed(0)}%`
                  )}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700 overflow-hidden shadow-inner">
                <div 
                  className={`h-4 rounded-full transition-all duration-300 ease-out flex items-center justify-center text-white text-xs font-medium 
                    ${currentStep === 'upload' ? 'bg-blue-500' : (dataLoadingPhase >= 0 ? 'bg-purple-500' : 'bg-amber-500')} 
                    ${(isPollingProgress && currentStep ==='analyze' && dataLoadingPhase < 0 && analysisProgress.analyzed_percentage < 100) ? 'animate-pulse' : ''}
                    ${(dataLoadingPhase >= 0) ? 'animate-pulse' : ''}`}
                  style={{ width: `${currentStep === 'upload' ? uploadProgress : (dataLoadingPhase >= 0 ? dataLoadingProgress : analysisProgress.analyzed_percentage)}%` }}
                >
                  {(currentStep === 'upload' ? uploadProgress : (dataLoadingPhase >= 0 ? dataLoadingProgress : analysisProgress.analyzed_percentage)) > 10 ? 
                    `${(currentStep === 'upload' ? uploadProgress : (dataLoadingPhase >= 0 ? dataLoadingProgress : analysisProgress.analyzed_percentage)).toFixed(0)}%` : ''}
                </div>
              </div>
              
              {currentStep === 'analyze' && dataLoadingPhase < 0 && analysisProgress.total_records > 0 && (
                <p className="text-xs text-muted-foreground text-right mt-1.5">
                  已扫描 {analysisProgress.analyzed_records} / {analysisProgress.total_records} 条记录
                </p>
              )}
              
              {currentStep === 'analyze' && dataLoadingPhase >= 0 && (
                <p className="text-xs text-purple-600 text-right mt-1.5">
                  正在加载阶段 {dataLoadingPhase + 1}/4: {DATA_LOADING_PHASES[dataLoadingPhase]}
                </p>
              )}
            </div>
            
            {/* 其他提示内容 */}
            {(currentStep === 'upload' || (currentStep === 'analyze' && analysisProgress.status !== 'analyzed' && analysisProgress.analyzed_percentage < 100 && dataLoadingPhase < 0)) && (
                 <div className="text-center text-sm text-muted-foreground mt-10 flex items-center justify-center">
                    <Clock className="inline-block mr-2 h-4 w-4 animate-spin" /> 
                    <span>处理进行中，请勿关闭或刷新此页面...</span>
                 </div>
            )}
            
            {/* 数据加载阶段特殊提示 */}
            {currentStep === 'analyze' && dataLoadingPhase >= 0 && (
                <div className="text-center text-sm text-purple-600 mt-10 flex items-center justify-center">
                    <Clock className="inline-block mr-2 h-4 w-4 animate-spin" /> 
                    <span>正在加载分析结果数据，请稍候...</span>
                </div>
            )}
            
            {currentStep === 'analyze' && (
                <div className="flex justify-center mt-10">
                    <Button 
                        variant="destructive"
                        onClick={() => resetForm({showToast: true, toastMessage: "用户已取消分析操作并重置导入。"})} 
                        disabled={(analysisProgress.status === 'analyzed' && analysisProgress.analyzed_percentage >= 100 && dataLoadingPhase >= 0) || !importSessionId }
                        className="shadow-md hover:shadow-lg transition-shadow px-6 py-3"
                    >
                        <RotateCcw className="mr-2 h-4 w-4"/> 强制取消并重置
                    </Button>
                </div>
            )}
          </div>
        );
        
      case 'confirm':
        return (
          <div className="space-y-6 py-4">
            <Alert variant="default" className="p-4 rounded-lg bg-green-50 border border-green-300 text-green-800 shadow-sm">
              <CheckCircle2 className="h-6 w-6 mr-3 flex-shrink-0 text-green-600" />
              <div>
                <AlertTitle className="font-bold text-lg">Excel文件分析完成</AlertTitle>
                <AlertDescription className="text-sm">
                  系统已完成对 <span className="font-semibold">{fileDetails?.name || '您上传的Excel文件'}</span> 的分析。请查看下面的摘要并处理潜在的数据冲突（如有）。
                </AlertDescription>
              </div>
            </Alert>
            {analysisStats && (
              <div className="pt-2">
                <h4 className="text-md font-semibold mb-3 text-gray-800">分析结果摘要:</h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3 text-center">
                  <Card className="p-3 shadow"><p className="text-sm text-muted-foreground">总记录</p><p className="text-2xl font-bold text-gray-700">{analysisStats.total || 0}</p></Card>
                  <Card className="p-3 bg-green-50 shadow"><p className="text-sm text-green-700">新记录</p><p className="text-2xl font-bold text-green-700">{analysisStats.new || 0}</p></Card>
                  <Card className="p-3 bg-yellow-50 shadow"><p className="text-sm text-yellow-700">可更新</p><p className="text-2xl font-bold text-yellow-700">{analysisStats.update || 0}</p></Card>
                  <Card className="p-3 bg-blue-50 shadow"><p className="text-sm text-blue-700">无差异</p><p className="text-2xl font-bold text-blue-700">{analysisStats.identical || 0}</p></Card>
                  <Card className="p-3 bg-red-50 shadow"><p className="text-sm text-red-700">错误/跳过</p><p className="text-2xl font-bold text-red-700">{analysisStats.error || 0}</p></Card>
                </div>
              </div>
            )}
            {conflictRecords.length > 0 ? (
              <div className="mt-6 space-y-4">
                <Alert variant="default" className="border-amber-400 bg-amber-50 text-amber-800 shadow-sm">
                    <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0"/>
                    <div className="ml-3 flex-1">
                        <AlertTitle className="font-semibold">发现冲突记录，请处理</AlertTitle>
                        <AlertDescription className="mt-1">
                            检测到 <span className="font-bold">{conflictRecords.length}</span> 条记录与现有数据存在差异或为新记录。请点击下方按钮查看并选择处理方式。
                            对于标记为"新记录"和"无差异"的记录，系统通常会自动处理，但您仍可审查。
                        </AlertDescription>
                    </div>
                </Alert>
                <Button onClick={() => setShowConflictModal(true)} className="w-full py-3 text-md font-semibold" size="lg">
                  <Filter className="mr-2 h-5 w-5" /> 处理 {conflictRecords.length} 条冲突记录
                </Button>
              </div>
            ) : (
              <Alert variant="default" className="border-green-400 bg-green-50 text-green-800 shadow-sm">
                <CheckCircle2 className="h-5 w-5 text-green-600 flex-shrink-0"/>
                 <div className="ml-3 flex-1">
                    <AlertTitle className="font-semibold">无冲突记录</AlertTitle>
                    <AlertDescription className="mt-1">
                        分析完成，所有记录均为新记录或与现有数据完全一致。您可以直接确认导入。
                    </AlertDescription>
                 </div>
              </Alert>
            )}
            <Separator className="my-8" />
            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="outline" onClick={() => resetForm({showToast: true, toastMessage: "用户已取消导入确认操作。"})} className="flex-1 py-3 text-md" disabled={isSubmittingConfirm}>
                <RotateCcw className="mr-2 h-4 w-4"/> 取消并重新选择文件
              </Button>
              <Button 
                onClick={ (conflictRecords.length === 0 && analysisStats && analysisStats.total > 0) ? 
                            () => confirmImportWithoutConflicts(importSessionId || '') : 
                            handleConflictResolution
                        }
                className="flex-1 py-6 text-lg font-semibold bg-green-600 hover:bg-green-700 shadow-md hover:shadow-lg transition-all"
                disabled={isSubmittingConfirm || !importSessionId || (conflictRecords.length > 0 && !showConflictModal) } 
                size="lg"
              >
                {isSubmittingConfirm ? <><Clock className="mr-2 h-5 w-5 animate-spin"/>正在导入数据...</> : <><ArrowRight className="mr-2 h-5 w-5"/>{conflictRecords.length > 0 ? '应用解决方案并导入' : '全部确认并开始导入'}</>}
              </Button>
            </div>
            {conflictRecords.length > 0 && !showConflictModal && (
                <p className="text-xs text-center text-muted-foreground mt-3">提示：请先点击"处理冲突记录"按钮解决所有冲突，或确认默认操作，然后再进行导入。</p>
            )}
             {analysisStats && analysisStats.total === 0 && (
                <p className="text-xs text-center text-muted-foreground mt-3">提示：Excel文件中没有可导入的数据记录。</p>
            )}
          </div>
        );
        
      case 'complete':
      case 'completed': 
        return (
          <div className="space-y-8 py-4 text-center">
            <CheckCircle2 className="h-24 w-24 text-green-500 mx-auto " /> 
            <h3 className="font-bold text-3xl text-gray-800">导入成功完成!</h3>
            {importLogDetails ? (
              <Card className="text-left p-5 sm:p-6 mt-2 max-w-lg mx-auto shadow-xl rounded-lg">
                <CardHeader className="p-0 pb-4 mb-4 border-b text-center">
                    <CardTitle className="text-xl font-semibold text-gray-700">导入结果摘要</CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-2.5">
                    <div className="flex justify-between items-center"><span>总共处理Excel记录:</span> <span className="font-bold text-lg text-primary">{importLogDetails.total_records}</span></div>
                    <Separator/>
                    <div className="flex justify-between items-center"><span>成功导入 (新建/更新):</span> <span className="font-bold text-lg text-green-600">{importLogDetails.success_records}</span></div>
                    <div className="pl-5 text-xs text-gray-600">
                        <div className="flex justify-between"><span>&bull; 新建记录:</span> <span className="font-medium">{importLogDetails.created_count}</span></div>
                        <div className="flex justify-between"><span>&bull; 更新记录:</span> <span className="font-medium">{importLogDetails.updated_count}</span></div>
                    </div>
                    {(importLogDetails.user_manual_skipped_records > 0 || importLogDetails.system_skipped_records > 0) && <Separator className="my-2"/>}
                    {importLogDetails.user_manual_skipped_records > 0 && <div className="flex justify-between items-center"><span>用户手动跳过:</span> <span className="font-medium text-amber-600">{importLogDetails.user_manual_skipped_records}</span></div>}
                    {importLogDetails.system_skipped_records > 0 && <div className="flex justify-between items-center"><span>系统跳过 (无差异):</span> <span className="font-medium text-blue-600">{importLogDetails.system_skipped_records}</span></div>}
                    {importLogDetails.failed_records > 0 && 
                        <>
                            <Separator className="my-2"/>
                            <div className="flex justify-between items-center pt-1.5 text-red-600"><span>导入失败记录:</span> <span className="font-bold text-lg">{importLogDetails.failed_records}</span></div>
                        </>}
                </CardContent>
              </Card>
            ): (
                <Alert variant="destructive" className="max-w-md mx-auto">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>错误</AlertTitle>
                    <AlertDescription>无法加载导入结果的详细摘要信息。</AlertDescription>
                </Alert>
            )}
            <div className="flex flex-col sm:flex-row gap-4 pt-8 max-w-lg mx-auto">
              <Button variant="outline" onClick={() => resetForm({showToast: false})} className="flex-1 py-3 text-md sm:py-6 sm:text-base">
                <Upload className="mr-2 h-5 w-5"/> 导入新的Excel文件
              </Button>
              {importLogDetails?.import_log_id && (
                <Button asChild className="flex-1 py-3 text-md sm:py-6 sm:text-base bg-primary hover:bg-primary/90">
                  <Link href={`/records/import-history/${importLogDetails.import_log_id}`}> 
                    <FileText className="mr-2 h-5 w-5"/> 查看本次导入日志
                  </Link>
                </Button>
              )}
            </div>
          </div>
        );
      default:
        return (
            <div className="text-center p-8 min-h-[200px] flex flex-col justify-center items-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-muted-foreground">正在加载导入组件或步骤 ({currentStep}) 未知...</p>
            </div>
        );
    }
  };
  
  // Render the conflict dialog modal
  const renderConflictDialog = () => {
      if (!showConflictModal) return null;
  
  return (
        <ConflictResolutionModal
          open={showConflictModal}
          conflicts={filteredConflictRecords}
          analysisResult={analysisStats}
          onClose={() => setShowConflictModal(false)}
          onResolve={handleConflictResolution}
          isProcessing={isSubmittingConfirm}
          updateAction={updateConflictAction}
          updateAllActions={updateAllConflictsAction}
          currentFilter={filterType}
          onFilterChange={setFilterType}
        />
      );
  };

  // Main component render
  return (
      <Card className="overflow-hidden">
        {showTitle && (
          <CardHeader>
          <CardTitle>Excel导入 (两阶段流程)</CardTitle>
          <CardDescription>上传Excel文件，系统将分析数据冲突，您可以确认处理方案后完成导入。</CardDescription>
          </CardHeader>
        )}
      <CardContent className="p-4 sm:p-6">
        {/* Progress Steps Indicator */}
        <div className="mb-6">
            <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((stepNum, index, arr) => {
              let stepName = "";
              let isActive = false;
              let isCompleted = false;
              switch (stepNum) {
                case 1: 
                  stepName = "选择文件"; 
                  isActive = currentStep === 'select'; 
                  isCompleted = ['upload', 'analyze', 'confirm', 'complete', 'completed'].includes(currentStep); 
                  break;
                case 2: 
                  stepName = "上传文件"; 
                  isActive = currentStep === 'upload'; 
                  isCompleted = ['analyze', 'confirm', 'complete', 'completed'].includes(currentStep); 
                  break;
                case 3: 
                  stepName = "数据分析"; 
                  isActive = currentStep === 'analyze'; 
                  isCompleted = ['confirm', 'complete', 'completed'].includes(currentStep); 
                  break;
                case 4: 
                  stepName = "处理冲突"; 
                  isActive = currentStep === 'confirm'; 
                  isCompleted = ['complete', 'completed'].includes(currentStep); 
                  break;
                case 5: 
                  stepName = "完成导入"; 
                  isActive = currentStep === 'complete' || currentStep === 'completed'; 
                  isCompleted = currentStep === 'complete' || currentStep === 'completed'; 
                  break;
              }
              const displayStepNum = isCompleted && stepNum !== 5 ? <CheckCircle2 size={16}/> : stepNum;
              return (
                <React.Fragment key={stepNum}>
                  <div className="flex flex-col items-center">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${isActive ? 'bg-primary border-primary text-white shadow-lg' : isCompleted ? 'bg-green-500 border-green-500 text-white' : 'bg-gray-100 border-gray-300 text-gray-500'} transition-all duration-300`}>
                      {displayStepNum}
              </div>
                    <p className={`mt-1.5 text-xs ${(isActive || isCompleted) ? 'text-primary font-semibold' : 'text-muted-foreground'}`}>{stepName}</p>
              </div>
                  {index < arr.length - 1 && (
                    <div className={`flex-1 h-1 mx-2 rounded ${isCompleted ? 'bg-primary' : 'bg-gray-200'} transition-colors`}></div>
                  )}
                </React.Fragment>
              );
            })}
              </div>
          </div>
          
        <div className="min-h-[300px]">
            {renderStep()}
          </div>
        </CardContent>
      {renderConflictDialog()}
    </Card>
  );

};

export default ExcelImportWithConflictResolution;

```
