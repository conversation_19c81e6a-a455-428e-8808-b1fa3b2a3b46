# Refactoring Plan: Replace PyPDF2 with PyMuPDF

## 1. Goal (目标)

Refactor the codebase to replace all usages of the `PyPDF2` library with the `PyMuPDF` (imported as `fitz`) library. This aims to consolidate the PDF handling libraries used in the project, leverage PyMuPDF's potentially better performance and richer feature set, and simplify dependencies.

(将代码库中所有使用 `PyPDF2` 库的地方重构为使用 `PyMuPDF`（以 `fitz` 导入）。目标是整合项目中使用的 PDF 处理库，利用 PyMuPDF 可能更好的性能和更丰富的功能集，并简化依赖关系。)

## 2. Rationale/Benefits (理由/益处)

* **Unified Library**: Use a single, powerful library (`PyMuPDF`) for most PDF operations. (统一使用单一、强大的库处理大多数 PDF 操作。)
* **Performance**: `PyMuPDF` is generally considered faster than `PyPDF2`. (PyMuPDF 通常被认为比 PyPDF2 更快。)
* **Features**: `PyMuPDF` offers a wide range of features, potentially simplifying some existing logic. (PyMuPDF 提供广泛的功能，可能简化现有逻辑。)
* **Dependency Simplification**: Eventually remove `PyPDF2` from `requirements.txt`. (最终移除 PyPDF2 依赖。)
* **Maintainability**: Align with the general direction as `PyMuPDF` is actively developed and `PyPDF2`'s maintainer suggests moving towards `pypdf`. (与 PyMuPDF 积极开发的方向保持一致，并考虑到 PyPDF2 维护者建议迁移到 pypdf。)

## 3. Affected Areas (影响范围)

The refactoring will primarily affect the following modules and functionalities:

* `archive_processing/services/upload_service.py`: PDF validation logic during file upload. (文件上传时的 PDF 验证逻辑。)
* `archive_processing/utils/pdf_utils.py`: Functions related to reading, writing, splitting, or merging PDFs (if any using PyPDF2). (与读取、写入、分割、合并 PDF 相关的功能（如果使用了 PyPDF2）。)
* `archive_processing/utils/processing_report_utils.py`: Potentially functions for getting page counts or other metadata. (可能涉及获取页数或其他元数据的功能。)
* `test_suite/`: Unit and integration tests that currently use or mock `PyPDF2`. (当前使用或模拟 PyPDF2 的单元测试和集成测试。)

## 4. High-Level Steps (高层步骤)

1. **Analyze `PyPDF2` Usage**: Thoroughly identify all places where `PyPDF2` (`PdfReader`, `PdfWriter`) is used. (彻底识别所有使用 PyPDF2 的地方。)
2. **Replace Validation**: Refactor `upload_service.py` to use `fitz.open()` for PDF validation. (重构 `upload_service.py` 使用 `fitz.open()` 进行验证。)
3. **Replace Read/Write/Metadata**: Refactor `pdf_utils.py` and `processing_report_utils.py` to use `fitz` equivalents for reading pages, getting page counts, writing PDFs, etc. (重构 `pdf_utils.py` 和 `processing_report_utils.py` 使用 fitz 等效方法。)
4. **Update Tests**: Modify relevant tests in `test_suite/` to mock `fitz` instead of `PyPDF2` and validate the new logic. (修改 `test_suite/` 中的相关测试，模拟 fitz 而不是 PyPDF2。)
5. **Final Testing**: Perform integration testing to ensure PDF handling still works correctly end-to-end. (执行集成测试确保端到端 PDF 处理仍然正确。)
6. **Remove Dependency**: Once confident, remove `PyPDF2` from `requirements.txt`. (确认无误后，从 `requirements.txt` 中移除 PyPDF2。)

## 5. Potential Challenges (潜在挑战)

* **API Differences**: `PyMuPDF`'s API differs from `PyPDF2`. Need careful mapping of functionality. (PyMuPDF API 与 PyPDF2 不同，需要仔细映射功能。)
* **Edge Cases**: Ensure `PyMuPDF` handles all edge cases (e.g., corrupted files, specific PDF versions) the same way or better than `PyPDF2` currently does. (确保 PyMuPDF 能同样或更好地处理所有边界情况。)
* **Testing**: Refactoring test mocks and ensuring adequate test coverage for the new implementation. (重构测试模拟并确保新实现的测试覆盖率。)
