# 操作文档：实现集中式权限管理系统

## 📋 变更摘要

**目的**：实现集中式权限管理系统，便于在不同环境中快速切换API权限设置  
**范围**：所有API视图的权限管理  
**关联需求**：简化演示环境配置，减少代码冗余

## 🔧 操作步骤

### 📊 OP-001：分析当前权限实现方式

**前置条件**：项目中当前使用硬编码的方式设置临时权限
**操作**：分析现有视图类中的权限设置，发现大量视图使用相同模式的临时权限设置（`permission_classes = [AllowAny]`）
**后置条件**：理解当前权限设置方式及存在的问题

### ✏️ OP-002：设计集中式权限管理系统

**前置条件**：明确系统目标
**操作**：设计基于环境变量/设置文件的集中权限管理系统，支持debug、demo、prod三种模式
**后置条件**：确定系统的架构和各组件职责

### ✏️ OP-003：创建权限管理模块

**前置条件**：完成系统设计
**操作**：

1. 创建`archive_flow_manager/permissions.py`文件
2. 实现权限模式判断函数
3. 实现权限装饰器
4. 创建权限感知的API视图基类
**后置条件**：权限管理模块创建完成

### ✏️ OP-004：修改设置文件

**前置条件**：权限管理模块创建完成
**操作**：

1. 修改`archive_flow_manager/settings.py`，添加`PERMISSION_MODE`设置
**后置条件**：配置文件添加权限模式设置

### ✏️ OP-005：更新视图权限设置

**前置条件**：权限管理模块和配置完成
**操作**：

1. 修改`archive_processing/views.py`，使用权限装饰器替代硬编码权限
2. 修改`archive_records/views.py`，使用权限装饰器替代硬编码权限
3. 修改`report_issuing/views.py`，添加权限装饰器
**后置条件**：所有视图使用集中权限管理系统

### ✏️ OP-006：添加辅助工具和文档

**前置条件**：权限系统实现完成
**操作**：

1. 创建`dev_tools/scripts/set_permission_mode.py`帮助脚本
2. 创建`AgentReadme/guides/permission_management.md`使用指南
**后置条件**：添加完整的使用指南和辅助工具

## 📝 变更详情

### CH-001：创建权限管理模块

**文件**：`archive_flow_manager/permissions.py`
**新增**：

```python
"""
权限管理模块 - 提供集中式权限配置

该模块提供了各种权限配置，可根据不同环境（开发、演示、生产）切换，
便于管理整个项目的API权限设置。
"""

from django.conf import settings
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView

# 权限模式枚举
DEBUG_MODE = 'debug'   # 调试模式：所有API允许匿名访问
DEMO_MODE = 'demo'     # 演示模式：部分API允许匿名访问
PROD_MODE = 'prod'     # 生产模式：严格权限控制

# 当前权限模式
# 可通过环境变量或设置文件配置
CURRENT_MODE = getattr(settings, 'PERMISSION_MODE', PROD_MODE)

# 是否处于调试模式下（AllowAny权限）
def is_debug_mode():
    """
    检查当前是否处于调试模式
    
    返回:
        bool: 是否处于调试模式
    """
    return CURRENT_MODE == DEBUG_MODE

# 是否处于演示模式下（部分API允许匿名访问）
def is_demo_mode():
    """
    检查当前是否处于演示模式
    
    返回:
        bool: 是否处于演示模式
    """
    return CURRENT_MODE == DEMO_MODE

# 根据当前模式获取权限类
def get_permission_classes():
    """
    根据当前模式返回合适的权限类
    
    返回:
        list: 权限类列表
    """
    if is_debug_mode():
        return [AllowAny]
    return [IsAuthenticated]  # 默认返回标准权限

# 用于替换视图中权限设置的装饰器
def permission_mode_aware(view_class):
    """
    装饰器：使视图根据当前模式自动调整权限
    
    参数:
        view_class: 要装饰的视图类
        
    返回:
        class: 调整了权限设置的视图类
    """
    view_class.permission_classes = get_permission_classes()
    return view_class

# API视图基类，自动根据当前模式设置权限
class PermissionModeAPIView(APIView):
    """
    API视图基类，自动根据当前模式设置权限
    
    继承此类的视图将根据当前权限模式自动调整权限设置
    """
    @classmethod
    def as_view(cls, **initkwargs):
        view = super().as_view(**initkwargs)
        if is_debug_mode():
            view.permission_classes = [AllowAny]
        return view
```

**理由**：创建集中管理权限的核心模块，提供各种权限配置功能
**潜在影响**：简化权限管理，减少代码冗余

### CH-002：添加权限模式设置

**文件**：`archive_flow_manager/settings.py`
**变更**：

```python
# --- 权限模式设置 ---
# 可选值: 'debug' (所有API允许匿名访问), 'demo' (部分API允许匿名访问), 'prod' (严格权限控制)
# 根据环境变量设置权限模式，如果环境变量未设置，则默认为'prod'
PERMISSION_MODE = os.environ.get('PERMISSION_MODE', 'prod')
```

**理由**：在配置文件中添加权限模式设置，支持环境变量配置
**潜在影响**：允许通过环境变量或配置文件切换权限模式

### CH-003：更新档案处理视图权限

**文件**：`archive_processing/views.py`
**变更前**：

```python
class PDFUploadView(APIView):
    # ------------------ TEMPORARY CHANGE FOR DEMO ------------------
    # 临时修改：允许任何人访问此接口以进行演示
    # TODO: 演示完成后，务必将权限改回 IsAuthenticated 或更严格的权限！
    permission_classes = [AllowAny]
    # ---------------- END TEMPORARY CHANGE FOR DEMO ----------------
```

**变更后**：

```python
@permission_mode_aware
class PDFUploadView(APIView):
    """
    用于上传PDF文件进行处理的API
    POST请求用于上传新的PDF文件
    
    权限说明：
    - 生产环境：要求用户认证
    - 调试模式：允许匿名访问（通过PERMISSION_MODE环境变量设置）
    """
```

**理由**：使用装饰器替代硬编码权限，便于集中管理
**潜在影响**：简化视图类代码，允许通过配置切换权限

### CH-004：更新档案记录视图权限

**文件**：`archive_records/views.py`
**变更前**：

```python
class ExcelImportView(APIView):
    """Excel文件导入API视图"""
    # ------------------ TEMPORARY CHANGE FOR DEMO ------------------
    # 临时修改：允许任何人访问此接口以进行演示
    # TODO: 演示完成后，务必将权限改回 IsAuthenticated 或更严格的权限！
    permission_classes = [AllowAny]
    # ---------------- END TEMPORARY CHANGE FOR DEMO ----------------
```

**变更后**：

```python
@permission_mode_aware
class ExcelImportView(APIView):
    """
    Excel文件导入API视图
    
    权限说明：
    - 生产环境：要求用户认证
    - 调试模式：允许匿名访问（通过PERMISSION_MODE环境变量设置）
    """
```

**理由**：使用装饰器替代硬编码权限，便于集中管理
**潜在影响**：简化视图类代码，允许通过配置切换权限

### CH-005：更新报告发放视图权限

**文件**：`report_issuing/views.py`
**变更前**：无集中式权限管理
**变更后**：

```python
# 导入权限管理工具
from archive_flow_manager.permissions import permission_mode_aware, is_debug_mode

@permission_mode_aware
class IssueFormViewSet(viewsets.ModelViewSet):
    """
    发放单视图集
    
    权限说明：
    - 生产环境：要求用户认证和发放单创建者权限
    - 调试模式：仅要求匿名访问权限（通过PERMISSION_MODE环境变量设置）
    """
```

**理由**：添加权限装饰器，实现集中权限管理
**潜在影响**：简化视图类代码，允许通过配置切换权限

### CH-006：创建权限管理使用指南

**文件**：`AgentReadme/guides/permission_management.md`
**新增**：

```markdown
# 权限管理系统使用指南

本文档介绍了项目中的集中式权限管理系统，该系统允许根据不同环境（开发、演示、生产）自动切换API权限设置。

## 设计目标

集中权限管理系统旨在解决以下问题：

1. **减少重复代码**：不再需要在每个视图中单独设置权限
2. **环境切换简便**：只需修改一个环境变量即可切换权限模式
3. **安全性保障**：确保权限控制在不同环境中保持一致
4. **调试与演示便利**：无需手动修改代码就可切换到调试或演示模式

...（完整内容略）
```

**理由**：提供完整的使用指南，便于团队成员使用
**潜在影响**：提高团队对权限系统的理解和使用

### CH-007：创建权限模式切换脚本

**文件**：`dev_tools/scripts/set_permission_mode.py`
**新增**：

```python
#!/usr/bin/env python
"""
权限模式切换脚本

使用方法:
    python set_permission_mode.py [mode]
    
参数:
    mode: 权限模式，可选值为 debug, demo, prod。如果不提供，则显示当前模式

示例:
    python set_permission_mode.py debug  # 设置为调试模式
    python set_permission_mode.py prod   # 设置为生产模式
    python set_permission_mode.py        # 显示当前模式
"""

...（完整内容略）
```

**理由**：提供便捷的命令行工具，便于快速切换权限模式
**潜在影响**：简化权限模式切换过程，提高开发效率

## ✅ 验证结果

**方法**：

1. 设置不同的权限模式，检查API访问权限变化
2. 测试系统在不同模式下的行为

**结果**：

1. 调试模式(debug)：所有API允许匿名访问
2. 生产模式(prod)：API恢复严格权限控制

**问题**：

1. 演示模式(demo)目前与生产模式行为相同，后续可增强
2. 需要添加更多单元测试覆盖权限系统

**解决方案**：

1. 后续迭代中，为演示模式添加更细粒度的权限控制
2. 增加单元测试，确保权限系统在各种场景下正常工作
