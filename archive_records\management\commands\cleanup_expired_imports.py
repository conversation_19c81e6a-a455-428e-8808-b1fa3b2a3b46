"""
Django管理命令，用于清理过期的Excel导入会话。
"""
import logging
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from archive_records.services.import_session_manager import ImportSessionManager

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Cleans up expired Excel import sessions from the system.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate the cleanup process without actually modifying data.',
        )
        parser.add_argument(
            '-v',
            '--verbosity',
            type=int,
            default=1,
            help='Verbosity level; 0=minimal output, 1=normal output, 2=verbose output, 3=very verbose output'
        )

    def handle(self, *args, **options):
        """执行命令的主要逻辑"""
        dry_run = options['dry_run']
        verbosity = options['verbosity']

        if verbosity > 0:
            self.stdout.write(self.style.SUCCESS('Starting cleanup of expired Excel import sessions...'))
        
        if dry_run:
            if verbosity > 0:
                self.stdout.write(self.style.WARNING('Dry run mode activated. No data will be changed.'))
        
        session_manager = ImportSessionManager()
        
        try:
            # cleanup_expired_sessions 内部现在不直接删除，而是标记为错误并触发异步清理
            # 这里调用它的目的主要是确保过期检查逻辑被执行。
            # 真正的文件清理和状态更新由Celery任务或其调用的_cleanup_session_resources处理。
            # 因此，这个管理命令主要是触发这个检查和标记过程。
            # 如果 cleanup_expired_sessions 返回了清理的数量，我们可以使用它。
            
            # 假设 cleanup_expired_sessions 返回的是被标记/处理的会话数量
            # 或者我们可以直接查询有多少会话符合过期条件，然后在 dry-run 中报告这个数字

            # 为了简单起见，如果 session_manager.cleanup_expired_sessions() 能够适应 dry_run 并返回信息，那是最好的。
            # 否则，我们需要在命令中模拟。
            # 基于当前对该方法的理解，它会直接修改状态。我们需要一个dry_run的适配。
            # 暂时，我们假设它不能很好地支持dry_run，所以管理命令的dry_run只是不调用它。

            if not dry_run:
                # CHANGE: [2025-05-28] cleanup_expired_sessions 方法已被移除
                # 过期会话的状态更新现在通过 _ensure_session_status_is_current 在需要时自动处理
                # 资源清理由定期的 process_finalized_sessions_task Celery任务处理
                # 此管理命令保留用于手动触发状态检查，但不再直接调用清理方法
                
                # 手动触发系统活跃会话检查，这会间接调用 _ensure_session_status_is_current
                # 从而更新过期会话的状态
                active_session = session_manager.get_system_active_session()
                
                # 查询当前有多少会话处于各种状态，用于报告
                from archive_records.models import ImportSession, ImportSessionStatus
                total_sessions = ImportSession.objects.count()
                finalized_sessions = ImportSession.objects.filter(status=ImportSessionStatus.FINALIZED).count()
                error_sessions = ImportSession.objects.filter(status=ImportSessionStatus.ERROR).count()
                
                if verbosity > 0:
                    self.stdout.write(self.style.SUCCESS(f'状态检查完成。'))
                    self.stdout.write(f'总会话数: {total_sessions}, FINALIZED: {finalized_sessions}, ERROR: {error_sessions}')
                    if active_session:
                        self.stdout.write(f'当前活跃会话: {active_session.session_id} (状态: {active_session.status})')
                    else:
                        self.stdout.write('当前无活跃会话')
                        
                if verbosity > 1:
                    logger.info(f'[ManagementCommand] cleanup_expired_imports: 状态检查完成。总会话: {total_sessions}')
            else:
                # Dry run: 模拟查找过期会话的数量
                # 这需要 ImportSessionManager 提供一个只读的查询方法，或者我们在这里复制查询逻辑
                # from archive_records.models import ImportSession, ImportSessionStatus # 移到顶部
                # now = timezone.now()
                # terminal_states = [
                #     ImportSessionStatus.CANCELLED, 
                #     ImportSessionStatus.ERROR, 
                #     ImportSessionStatus.IMPORT_COMPLETE
                # ]
                # expired_sessions_query = ImportSession.objects.filter(
                #     expires_at__lt=now
                # ).exclude(
                #     status__in=terminal_states
                # )
                # potential_to_clean_count = expired_sessions_query.count()
                # self.stdout.write(self.style.NOTICE(f'Dry run: Found {potential_to_clean_count} session(s) that would be processed for cleanup.'))
                # logger.info(f'[ManagementCommand] cleanup_expired_imports (dry-run): Found {potential_to_clean_count} sessions for cleanup.')
                # 上述dry-run逻辑比较复杂，暂时简化，提示用户dry-run不执行实际调用
                self.stdout.write(self.style.NOTICE('Dry run: Actual cleanup_expired_sessions() call skipped.'))
                self.stdout.write(self.style.NOTICE('In a real run, session_manager.cleanup_expired_sessions() would be invoked here.'))

            if verbosity > 0:
                self.stdout.write(self.style.SUCCESS('Cleanup process finished.'))

        except Exception as e:
            logger.error(f"[ManagementCommand] Error during cleanup_expired_imports: {str(e)}", exc_info=True)
            self.stderr.write(self.style.ERROR(f'An error occurred: {str(e)}'))
            raise CommandError(f'Failed to cleanup expired sessions: {str(e)}') from e

# CHANGE: [2025-05-16] Created Django management command for cleaning up expired import sessions.
# Ensure the directory structure archive_records/management/commands/ exists
# and __init__.py files are present in archive_records/management/ and archive_records/management/commands/. 