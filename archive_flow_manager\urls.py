"""
URL configuration for archive_flow_manager project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)

# CHANGE: [2025-05-10] 调整API路径策略，保持统一使用/api前缀
# 修改后的Nginx配置不再去掉/api前缀，因此Django需要匹配带/api前缀的路径
# 这样的设计更符合REST API的最佳实践，保持了路径的一致性

urlpatterns = [
    path('admin/', admin.site.urls),

    path('api/report-issuing/', include('report_issuing.urls')),
    path('api/archive-records/', include('archive_records.urls')),
    path('api/archive-processing/', include('archive_processing.urls')),
    # dj-rest-auth认证端点 - 符合文档要求的标准实现
    path('api/auth/', include('dj_rest_auth.urls')),
    path('api/auth/registration/', include('dj_rest_auth.registration.urls')),
    
    # 保留标准SimpleJWT端点作为备用
    path('api/auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    
    # API接口使用统一的/api前缀，这是RESTful API的标准做法
    # Nginx现在直接转发带/api前缀的请求，不再进行路径重写
    
    path('swagger<format>/', get_schema_view(
        openapi.Info(
            title="报告发放系统 API",
            default_version='v1',
            description="档案报告发放系统API文档",
            contact=openapi.Contact(email="<EMAIL>"),
        ),
        public=True,
        permission_classes=[permissions.IsAuthenticated],
    ).without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', get_schema_view(
        openapi.Info(
            title="报告发放系统 API",
            default_version='v1',
            description="档案报告发放系统API文档",
            contact=openapi.Contact(email="<EMAIL>"),
        ),
        public=True,
        permission_classes=[permissions.IsAuthenticated],
    ).with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', get_schema_view(
        openapi.Info(
            title="报告发放系统 API",
            default_version='v1',
            description="档案报告发放系统API文档",
            contact=openapi.Contact(email="<EMAIL>"),
        ),
        public=True,
        permission_classes=[permissions.IsAuthenticated],
    ).with_ui('redoc', cache_timeout=0), name='schema-redoc'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)