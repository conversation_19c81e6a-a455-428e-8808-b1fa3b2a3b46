#!/usr/bin/env python3
"""
OCR 微服务测试脚本
"""
import os
import sys
import time
import requests
from PIL import Image, ImageDraw, ImageFont
import io

# 测试配置
OCR_SERVICE_URL = "http://localhost:8001"
TEST_TIMEOUT = 30.0


def create_test_image(text: str = "测试文本 Test Text 123") -> Image.Image:
    """创建测试图像"""
    # 创建白色背景图像
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 绘制文本
    draw.text((10, 30), text, fill='black', font=font)
    
    return img


def test_health_check():
    """测试健康检查端点"""
    print("🔍 测试健康检查端点...")
    
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/health", timeout=5.0)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康检查通过")
            print(f"   状态: {health_data.get('status')}")
            print(f"   OCR引擎就绪: {health_data.get('ocr_engine_ready')}")
            print(f"   运行时间: {health_data.get('uptime'):.2f}秒")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def test_basic_ocr():
    """测试基础 OCR 功能"""
    print("\n🔍 测试基础 OCR 功能...")
    
    try:
        # 创建测试图像
        test_image = create_test_image("Hello World 你好世界")
        
        # 转换为字节流
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 发送请求
        files = {'file': ('test.png', img_buffer, 'image/png')}
        data = {'mode': 'basic'}
        
        start_time = time.time()
        response = requests.post(
            f"{OCR_SERVICE_URL}/ocr",
            files=files,
            data=data,
            timeout=TEST_TIMEOUT
        )
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                text = result.get('text', '')
                print(f"✅ 基础 OCR 成功")
                print(f"   识别文本: '{text}'")
                print(f"   处理时间: {processing_time:.2f}秒")
                print(f"   服务处理时间: {result.get('processing_time', 0):.2f}秒")
                return True
            else:
                print(f"❌ OCR 处理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 基础 OCR 测试异常: {e}")
        return False


def test_enhanced_ocr():
    """测试增强 OCR 功能"""
    print("\n🔍 测试增强 OCR 功能...")
    
    try:
        # 创建测试图像
        test_image = create_test_image("Enhanced OCR Test 增强识别测试")
        
        # 转换为字节流
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 发送请求
        files = {'file': ('test.png', img_buffer, 'image/png')}
        data = {
            'mode': 'enhanced',
            'max_attempts': 3
        }
        
        start_time = time.time()
        response = requests.post(
            f"{OCR_SERVICE_URL}/ocr",
            files=files,
            data=data,
            timeout=TEST_TIMEOUT
        )
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                text_results = result.get('text_results', [])
                print(f"✅ 增强 OCR 成功")
                print(f"   结果数量: {len(text_results)}")
                for i, text_result in enumerate(text_results):
                    text = text_result.get('text', '')
                    method = text_result.get('method', '')
                    print(f"   结果{i+1} ({method}): '{text}'")
                print(f"   总处理时间: {processing_time:.2f}秒")
                return True
            else:
                print(f"❌ 增强 OCR 处理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 增强 OCR 测试异常: {e}")
        return False


def test_metrics():
    """测试指标端点"""
    print("\n🔍 测试指标端点...")
    
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/metrics", timeout=5.0)
        
        if response.status_code == 200:
            metrics = response.json()
            print(f"✅ 指标获取成功")
            print(f"   总请求数: {metrics.get('total_requests')}")
            print(f"   成功请求数: {metrics.get('successful_requests')}")
            print(f"   失败请求数: {metrics.get('failed_requests')}")
            print(f"   平均处理时间: {metrics.get('average_processing_time'):.3f}秒")
            return True
        else:
            print(f"❌ 指标获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 指标测试异常: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    try:
        # 测试无效文件
        files = {'file': ('test.txt', b'invalid image data', 'text/plain')}
        data = {'mode': 'basic'}
        
        response = requests.post(
            f"{OCR_SERVICE_URL}/ocr",
            files=files,
            data=data,
            timeout=TEST_TIMEOUT
        )
        
        if response.status_code == 400:
            print(f"✅ 错误处理正常: 正确拒绝无效文件")
            return True
        else:
            print(f"⚠️ 错误处理异常: 期望400，实际{response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始 OCR 微服务测试")
    print("=" * 60)
    
    tests = [
        ("健康检查", test_health_check),
        ("基础 OCR", test_basic_ocr),
        ("增强 OCR", test_enhanced_ocr),
        ("指标端点", test_metrics),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 间隔1秒
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OCR 微服务运行正常")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查服务状态")
        return 1


if __name__ == "__main__":
    sys.exit(main())
