"""
Excel导入会话管理ViewSet模块

CHANGE: [2025-06-04] 创建专门的Excel导入ViewSet文件
- 统一管理所有Excel导入相关功能
- 替代原有的多个独立APIView
- 保持完整的业务逻辑和错误处理
"""

import os
import logging
import tempfile
import uuid
import json
import threading
from datetime import datetime, timedelta

from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User
from django.core.cache import cache

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import (
    ImportSession,
    ImportSessionStatus,
    SessionOperation,
    ImportConflictDetail,
)
from ..serializers import (
    ExcelImportSerializer, 
    ImportLogSerializer,
    ImportSessionAnalysisResultSerializer,
)
from ..services import ExcelImportService
from ..services.import_session_manager import (
    ImportSessionManager, 
    ConflictResolution
)

logger = logging.getLogger(__name__)


class ExcelImportSessionViewSet(viewsets.ModelViewSet):
    """
    Excel导入会话管理ViewSet
    
    管理新的两阶段Excel导入流程，包括：
    - 导入会话管理（分析、确认、冲突处理）
    - 会话状态管理（心跳、取消、暂停等）
    
    替代原有的独立APIView：
    - ExcelImportAnalysisView -> analyze action  
    - ExcelImportConfirmView -> confirm action
    - ExcelImportAnalysisResultView -> analysis_result action
    - GetActiveImportSessionView -> active_session action
    - BeginActiveConflictProcessingView -> begin_conflict_processing action
    - ExcelImportCancelView -> cancel action
    - SessionHeartbeatView -> heartbeat action
    - AcknowledgeImportResultsView -> acknowledge action
    - PendActiveConflictProcessingView -> pend_conflict_processing action
    """
    
    queryset = ImportSession.objects.all().order_by("-created_at")
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["file_name", "status"]
    ordering_fields = ["created_at", "updated_at", "status"]
    ordering = ["-created_at"]
    
    def get_serializer_class(self):
        """根据操作类型选择序列化器"""
        if self.action in ['import_excel', 'analyze']:
            return ExcelImportSerializer
        # 其他action可以返回通用序列化器或None
        return ExcelImportSerializer
    
    def get_queryset(self):
        """过滤查询集"""
        queryset = super().get_queryset()
        
        # 根据状态过滤
        status_filter = self.request.query_params.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset
    
    def perform_create(self, serializer):
        """创建会话时设置创建者"""
        serializer.save(created_by=self.request.user)
    
    # ==========================================
    # Excel导入业务action方法 - 新的两阶段导入流程
    # ==========================================

    @action(detail=False, methods=['post'], parser_classes=[MultiPartParser, FormParser], url_path='analyze')
    def analyze(self, request):
        """
        开始Excel导入分析
        
        POST /api/archive-records/excel-import/analyze/
        
        迁移自: ExcelImportAnalysisView.post()
        """
        logger.info(f"[ViewSet.Analyze] 用户 {request.user.username} 请求Excel导入分析")
        
        excel_file = request.FILES.get('file')
        sheet_name = request.data.get('sheet_name', 0)
        
        if not excel_file:
            return Response(
                {'success': False, 'error': '未提供Excel文件'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not excel_file.name.endswith(('.xlsx', '.xls')):
            return Response(
                {'success': False, 'error': '文件必须是Excel格式(.xlsx或.xls)'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            session_manager = ImportSessionManager()
            logger.info(f"[ViewSet.Analyze] 用户 {request.user.username} 请求创建导入会话，文件名: {excel_file.name}")
            
            # CHANGE: create_session现在返回字典，包含success和session字段
            create_result = session_manager.create_session(
                uploaded_file=excel_file, 
                user_id=request.user.id, 
                sheet_name=sheet_name
            )
            
            # 检查session是否成功创建
            if not create_result.get('success', False):
                # 如果创建失败，返回错误信息
                error_msg = create_result.get('error', '创建会话失败')
                error_type = create_result.get('error_type', 'general_error')
                existing_session_id = create_result.get('existing_session_id')
                
                logger.warning(f"[ViewSet.Analyze] 会话创建失败: {error_msg}")
                
                status_code = status.HTTP_409_CONFLICT if error_type == 'system_has_active_session' else status.HTTP_400_BAD_REQUEST
                
                return Response(
                    {'success': False, 'error': error_msg, 'errorType': error_type, 'existing_session_id': existing_session_id},
                    status=status_code
                )
            
            # 会话创建成功，从返回字典中获取session实例
            import_session_orm = create_result.get('session')
            
            # 从ORM实例获取session_id
            session_id_str = str(import_session_orm.session_id)
            logger.info(f"[ViewSet.Analyze] 导入会话创建成功，ID: {session_id_str}, 初始状态: {import_session_orm.status}")
            
            # 后台分析线程
            def run_analysis_in_background(s_id_for_thread: str):
                current_session_id = s_id_for_thread # 避免闭包问题
                try:
                    logger.info(f"[ViewSet.AnalyzeBG] 后台线程开始分析Excel文件，会话ID: {current_session_id}")
                    # analyze_session 内部会获取 ImportSession 实例
                    # 创建新的manager实例以确保线程隔离性，虽然在这个场景下可能不是严格必须
                    thread_session_manager = ImportSessionManager()
                    thread_session_manager.analyze_session(current_session_id)
                    logger.info(f"[ViewSet.AnalyzeBG] 后台分析顺利完成，会话ID: {current_session_id}")
                except Exception as e_bg:
                    logger.error(f"[ViewSet.AnalyzeBG] 后台分析线程失败，会话ID: {current_session_id}, 错误: {str(e_bg)}", exc_info=True)
                    # 尝试将会话标记为错误状态
                    try:
                        with transaction.atomic():
                            # 重新获取会话以确保是最新的，并锁定
                            failed_session = ImportSession.objects.select_for_update().get(session_id=uuid.UUID(current_session_id))
                            # CHANGE: [2025-06-04] 检查会话是否已是终态，避免重复标记为ERROR
                            terminal_statuses = [
                                ImportSessionStatus.CANCELLED,
                                ImportSessionStatus.ERROR,
                                ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
                                ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
                                ImportSessionStatus.FINALIZED,
                            ]
                            if failed_session.status not in terminal_statuses:
                                old_status_bg_err = failed_session.status
                                failed_session.status = ImportSessionStatus.ERROR
                                failed_session.error_message = f"后台分析线程错误: {str(e_bg)[:500]}"
                                failed_session.save(update_fields=['status', 'error_message', 'updated_at'])
                                SessionOperation.objects.create(
                                    session=failed_session,
                                    operation_type='error_in_background_analysis',
                                    user=failed_session.created_by, # 或尝试从请求上下文中获取，但线程中较难
                                    old_status=old_status_bg_err,
                                    new_status=ImportSessionStatus.ERROR,
                                    details={'error': str(e_bg)[:500], 'source': 'background_thread'}
                                )
                                logger.info(f"[ViewSet.AnalyzeBG] 会话 {current_session_id} 已在后台标记为ERROR")
                            else:
                                logger.info(f"[ViewSet.AnalyzeBG] 会话 {current_session_id} 状态已是终态 ({failed_session.status})，未再次标记为ERROR。")
                    except ImportSession.DoesNotExist:
                        logger.error(f"[ViewSet.AnalyzeBG] 尝试标记错误状态时，会话 {current_session_id} 未找到。")
                    except Exception as e_mark_error:
                        logger.error(f"[ViewSet.AnalyzeBG] 标记会话 {current_session_id} 为错误时再次失败: {e_mark_error}", exc_info=True)
            
            analysis_thread = threading.Thread(target=run_analysis_in_background, args=(session_id_str,))
            analysis_thread.daemon = True
            analysis_thread.start()
            logger.info(f"[ViewSet.Analyze] 已启动后台分析线程，会话ID: {session_id_str}")
            
            # 返回给前端的信息
            return Response({
                'success': True,
                'data': {
                    'import_session_id': session_id_str,
                    'file_name': import_session_orm.file_name, # 从ORM实例获取
                    'total_records': import_session_orm.record_count,  # 初始为0，分析完成后更新
                    'status': import_session_orm.status, # 初始状态 (应该是UPLOAD)
                    'message': '文件已上传，正在后台准备分析'
                }
            })
        
        except ValueError as ve: # 通常是由于已存在活跃会话
            logger.warning(f"[ViewSet.Analyze] 创建会话失败: {str(ve)}", exc_info=True) # 添加exc_info
            return Response(
                {'success': False, 'error': str(ve), 'errorType': 'active_session_exists'},
                status=status.HTTP_409_CONFLICT
            )
        except Exception as e_main:
            logger.error(f"[ViewSet.Analyze] Excel导入会话创建主流程失败: {str(e_main)}", exc_info=True)
            return Response(
                {'success': False, 'error': f'创建Excel导入会话时出错: {str(e_main)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='confirm')
    def confirm(self, request):
        """
        确认Excel导入
        
        POST /api/archive-records/excel-import/confirm/
        
        迁移自: ExcelImportConfirmView.post()
        """
        logger.info(f"[ViewSet.Confirm] 用户 {request.user.username} 请求确认Excel导入")
        logger.info(f"[ViewSet.Confirm] Raw request.data: {request.data}") # 打印原始请求数据
        
        session_id = request.data.get('import_session_id')
        expected_session_id = request.data.get('expected_session_id')  # CHANGE: [2025-05-28] 新增期望会话ID校验
        resolutions = request.data.get('resolutions', [])
        # 新增：从请求中获取分析阶段和用户决策阶段的统计数据
        analysis_stats_from_request = request.data.get('analysis_stats') 
        user_decision_stats_from_request = request.data.get('user_decision_stats')
        
        logger.info(f"[ViewSet.Confirm] 收到确认导入请求，会话ID: {session_id}, 期望会话ID: {expected_session_id}, 解决方案数量: {len(resolutions)}")
        if analysis_stats_from_request:
            logger.info(f"[ViewSet.Confirm] 接收到的分析统计: {analysis_stats_from_request}")
        else:
            logger.warning(f"[ViewSet.Confirm] 请求中未包含 analysis_stats 参数。")
        if user_decision_stats_from_request:
            logger.info(f"[ViewSet.Confirm] 接收到的用户决策统计: {user_decision_stats_from_request}")
        else:
            logger.warning(f"[ViewSet.Confirm] 请求中未包含 user_decision_stats 参数。")

        if not session_id:
            logger.warning("[ViewSet.Confirm] 确认导入缺少会话ID参数")
            return Response(
                {'success': False, 'error': '未提供导入会话ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # CHANGE: [2025-05-28] 实现严格的会话状态校验
        try:
            session_manager = ImportSessionManager()
            
            # 获取系统当前唯一活跃会话
            current_active_session = session_manager.get_system_active_session()
            
            if not current_active_session:
                logger.warning(f"[ViewSet.Confirm] 系统当前无活跃会话，但用户尝试确认会话 {session_id}")
                return Response({
                    'success': False, 
                    'error': '系统当前没有活跃的导入会话，请刷新页面重新开始。',
                    'error_type': 'no_active_session'
                }, status=status.HTTP_409_CONFLICT)
            
            # 校验会话ID匹配
            if str(current_active_session.session_id) != str(session_id):
                logger.warning(f"[ViewSet.Confirm] 会话ID不匹配。请求的: {session_id}, 当前活跃的: {current_active_session.session_id}")
                return Response({
                    'success': False,
                    'error': f'会话状态已变更，请刷新页面。当前活跃会话: {current_active_session.session_id}',
                    'error_type': 'session_id_mismatch',
                    'current_active_session_id': str(current_active_session.session_id)
                }, status=status.HTTP_409_CONFLICT)
            
            # 校验期望会话ID（如果提供）
            if expected_session_id and str(expected_session_id) != str(current_active_session.session_id):
                logger.warning(f"[ViewSet.Confirm] 期望会话ID不匹配。期望的: {expected_session_id}, 当前活跃的: {current_active_session.session_id}")
                return Response({
                    'success': False,
                    'error': '会话状态已发生变化，请刷新页面获取最新状态。',
                    'error_type': 'expected_session_mismatch',
                    'current_active_session_id': str(current_active_session.session_id)
                }, status=status.HTTP_409_CONFLICT)
            
            # 验证会话状态是否适合确认导入
            # CHANGE: [2025-06-01] 根据文档要求，确认导入只能在CONFLICT_RESOLUTION_IN_PROGRESS状态下进行
            if current_active_session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
                logger.warning(f"[ViewSet.Confirm] 会话 {session_id} 状态不适合确认导入: {current_active_session.status}")
                return Response({
                    'success': False,
                    'error': f'会话当前状态为 {current_active_session.get_status_display()}，只有在"冲突处理中"状态下才能执行导入确认。',
                    'error_type': 'invalid_session_state',
                    'current_status': current_active_session.status
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证用户权限
            if current_active_session.created_by != request.user and current_active_session.processing_user != request.user:
                logger.warning(f"[ViewSet.Confirm] 用户 {request.user.username} 无权确认会话 {session_id}")
                return Response({
                    'success': False,
                    'error': '您没有权限操作此导入会话。',
                    'error_type': 'permission_denied'
                }, status=status.HTTP_403_FORBIDDEN)
            
        except Exception as e:
            logger.error(f"[ViewSet.Confirm] 会话状态校验失败: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'error': f'会话状态校验时发生错误: {str(e)}',
                'error_type': 'session_validation_error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 验证resolution的action值 (这部分逻辑保持不变)
        valid_actions = [ConflictResolution.UPDATE, ConflictResolution.SKIP, ConflictResolution.CREATE]
        for resolution in resolutions:
            if 'action' in resolution and resolution['action'] not in valid_actions:
                logger.warning(f"[ViewSet.Confirm] 无效的处理动作: {resolution.get('action')}")
                return Response(
                    {'success': False, 'error': f'无效的处理动作: {resolution.get("action")}，有效值为: {", ".join(valid_actions)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if 'action' in resolution and resolution['action'] == ConflictResolution.CREATE:
                 logger.warning(f"[ViewSet.Confirm] 注意: 用户为委托编号 {resolution.get('commission_number')} 选择了CREATE操作，但系统将以smart_update策略处理。")

        try:
            # 先延长会话时间 (extend_session 已重构为直接操作ORM)
            if not session_manager.extend_session(session_id):
                logger.warning(f"[ViewSet.Confirm] 尝试确认导入前，延长会话 {session_id} 失败 (可能不存在或非活跃)")
                return Response(
                    {'success': False, 'error': '导入会话不存在、已过期或非活跃，无法继续导入。请重新开始。' , 'errorType': 'session_extend_failed'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            logger.info(f"[ViewSet.Confirm] 会话 {session_id} 已成功延长。")

            # CHANGE: [2025-05-18] 调用已修改为异步触发的 confirm_import
            # 原: import_results_dict = session_manager.confirm_import(...)
            task_submission_response = session_manager.confirm_import(
                session_id=session_id,
                resolutions=resolutions,
                user=request.user,
                analysis_stats_dict=analysis_stats_from_request, # 传递分析统计
                user_decision_stats_dict=user_decision_stats_from_request # 传递用户决策统计
            )
            
            logger.info(f"[ViewSet.Confirm] 导入任务提交结果 for session {session_id}: {task_submission_response}")
            
            # 检查任务提交是否成功
            if task_submission_response.get('success'):
                # 异步任务已提交，返回 202 Accepted 或 200 OK 告知客户端任务已接受
                return Response({
                    'success': True,
                    'message': '导入确认任务已成功提交到后台处理。',
                    'data': task_submission_response.get('data') # 包含 session_id, task_id, current_status
                }, status=status.HTTP_202_ACCEPTED) # 使用 202 表示请求已接受，正在异步处理
            else:
                # 如果任务提交本身失败 (例如，参数验证失败，会话状态不对等)
                # session_manager.confirm_import 应该返回包含 error 和 error_type 的字典
                error_detail = task_submission_response.get('error', '提交导入任务失败，但未提供具体错误信息。')
                error_type = task_submission_response.get('error_type', 'task_submission_failed')
                logger.error(f"[ViewSet.Confirm] 提交导入任务失败 for session {session_id}: {error_detail} (Type: {error_type})")
                # 根据错误类型决定HTTP状态码，通常是客户端错误
                response_status = status.HTTP_400_BAD_REQUEST
                if error_type == 'session_not_found':
                    response_status = status.HTTP_404_NOT_FOUND
                elif error_type == 'invalid_session_state':
                    response_status = status.HTTP_409_CONFLICT # 状态冲突
                
                return Response({
                    'success': False, 
                    'error': error_detail,
                    'errorType': error_type
                }, status=response_status)
            # END CHANGE
            
        except ValueError as ve: # 特定捕获ValueError，通常是业务逻辑错误（如状态不对）
            logger.warning(f"[ViewSet.Confirm] Excel导入确认参数错误或状态不符 for session {session_id}: {str(ve)}", exc_info=True)
            return Response(
                {'success': False, 'error': str(ve), 'errorType': 'invalid_state_or_parameters'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"[ViewSet.Confirm] Excel导入确认失败 for session {session_id}: {str(e)}", exc_info=True)
            return Response(
                {'success': False, 'error': f'确认导入时发生意外错误: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @swagger_auto_schema(
        operation_summary="获取指定Excel导入会话的分析结果",
        operation_description="获取指定Excel导入会话的分析结果，包括统计数据和所有冲突记录的详细信息。",
        responses={
            200: openapi.Response(
                description="成功返回分析结果", 
                schema=ImportSessionAnalysisResultSerializer,
            ),
            404: openapi.Response(description="会话未找到或无权访问"),
        },
        tags=['Excel Import Session Management']
    )
    @action(detail=True, methods=['get'], url_path='analysis-result')
    def analysis_result(self, request, pk=None):
        """
        获取Excel导入分析结果

        GET /api/archive-records/excel-import/{session_id}/analysis-result/

        REFACTORED: [2025-06-19] 此方法现在使用DRF序列化器返回结果，
        自动处理字段名到camelCase的转换。
        """
        session_id = pk
        logger.info(f"[ViewSet.AnalysisResult] 用户 {request.user.id} 请求会话 {session_id} 的分析结果")

        if not session_id:
            return Response(
                {'success': False, 'error': '未提供会话ID'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # CHANGE: [2025-06-20] 修复prefetch字段名，确保正确加载冲突详情和字段差异
            session_with_details = ImportSession.objects.prefetch_related(
                'conflict_details__field_differences'
            ).select_related('created_by').get(session_id=session_id)

            serializer = ImportSessionAnalysisResultSerializer(session_with_details)
            # CHANGE: [2025-06-20] 确保使用CamelCaseJSONRenderer
            return Response(serializer.data)
        except ImportSession.DoesNotExist:
            logger.warning(f"[ViewSet.AnalysisResult] 用户 {request.user.id} 尝试访问不存在的会话 {session_id}")
            return Response({"error": "会话未找到"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(
                f"[ViewSet.AnalysisResult] 获取会话 {session_id} 结果时发生未知错误: {str(e)}",
                exc_info=True
            )
            return Response(
                {'success': False, 'error': f"获取分析结果时发生服务器内部错误: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'], url_path='active-session')
    def active_session(self, request):
        """
        获取活跃导入会话
        
        GET /api/archive-records/excel-import/active-session/
        
        迁移自: GetActiveImportSessionView.get()
        """
        current_user = request.user
        logger.info(f"[ViewSet.ActiveSession] 用户 {current_user.username} 查询系统活动导入会话...")
        try:
            session_manager = ImportSessionManager()
            # _ensure_session_status_is_current WILL be called inside get_system_active_session if it needs to update expiry-based status, but NOT for heartbeat timeout.
            active_session = session_manager.get_system_active_session() 
            
            if not active_session:
                logger.info(f"[ViewSet.ActiveSession] 系统当前没有活动的导入会话。")
                return Response({
                    'success': True,
                    'data': {'has_active_session': False, 'message': '当前系统没有活动的导入会话。'}
                })
            
            session_info_dict = active_session.to_dict()
            
            # CHANGE: [2025-06-02] GetActiveImportSessionView 将不再主动修改会话状态以处理心跳超时。
            # 这个逻辑将移至 BeginActiveConflictProcessingView。
            # 这里只如实返回会话信息，前端UI根据此信息决定按钮状态和提示。
            logger.info(f"[ViewSet.ActiveSession] 返回系统活动会话 {active_session.session_id} (状态: {active_session.status}) 给用户 {current_user.username}.")

            return Response({
                'success': True,
                'data': {
                    'has_active_session': True,
                    'session_info': session_info_dict,
                    # 'can_takeover' is removed as takeover logic is now part of BeginActive...
                    'message': '成功获取活动会话信息。'
                }
            })
            
        except Exception as e:
            logger.error(f"[ViewSet.ActiveSession] 获取系统活跃导入会话失败: {str(e)}", exc_info=True)
            return Response(
                {'success': False, 'error': f'获取活跃导入会话时出错: {str(e)}', 'error_type': 'server_error'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @swagger_auto_schema(
        operation_summary="开始/继续/接管冲突处理",
        operation_description=(
            "允许用户开始处理状态为 STARTED 或 PENDING 的会话；"
            "或继续处理状态为 IN_PROGRESS 且处理者是自己的会话（会更新心跳）；"
            "或接管状态为 IN_PROGRESS 但原处理者已心跳超时的会话。"
            "所有操作均为原子性。"
        ),
        responses={
            200: openapi.Response(description="操作成功，可以开始/继续处理冲突", examples={"application/json": {"success": True, "message": "...", "session_data": "{...}"}}),
            400: openapi.Response(description="无效的会话ID或会话状态不允许此操作"),
            403: openapi.Response(description="禁止操作（例如，尝试操作他人未超时的会话）"),
            404: openapi.Response(description="会话未找到"),
            409: openapi.Response(description="状态冲突（例如，操作时会话状态已被改变）")
        },
        tags=['Excel Import Session Management']
    )
    @action(detail=True, methods=['post'], url_path='begin-conflict-processing')
    def begin_conflict_processing(self, request, pk=None):
        """
        开始/继续/接管冲突处理
        
        POST /api/archive-records/excel-import/{id}/begin-conflict-processing/
        
        迁移自: BeginActiveConflictProcessingView.post()
        """
        session_id = pk  # 在ViewSet中，pk就是URL中的id参数
        logger.info(f"[ViewSet.BeginConflict] 用户 {request.user.username} 尝试开始/继续/接管冲突处理，会话ID: {session_id}")
        
        # The session_id from the URL (due to <uuid:session_id> path converter) is already a UUID object.
        # No need to convert it again or catch ValueError for format, as Django handles that at URL routing.

        try:
            with transaction.atomic():
                session = ImportSession.objects.select_for_update().get(session_id=session_id)

                if not session.is_active():
                    logger.warning(f"[ViewSet.BeginConflict] 用户 {request.user.username} 尝试操作非活跃会话 {session_id}, 状态: {session.status}")
                    return Response({"success": False, "error": "会话已非活跃，无法操作。", "current_status": session.status}, status=status.HTTP_400_BAD_REQUEST)

                current_status = session.status
                current_processing_user = session.processing_user
                request_user = request.user
                
                # 允许操作的状态
                actionable_conflict_statuses = [
                    ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
                    ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
                    ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS
                ]

                if current_status not in actionable_conflict_statuses:
                    logger.warning(f"[ViewSet.BeginConflict] 会话 {session_id} 状态 {current_status} 不允许冲突处理操作，用户: {request_user.username}")
                    return Response({
                        "success": False, 
                        "error": f"会话当前状态为'{session.get_status_display()}'，无法进行冲突处理操作。",
                        "current_status": current_status
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 情况一: 无人处理 (STARTED or PENDING)
                if current_status in [ImportSessionStatus.CONFLICT_RESOLUTION_STARTED, ImportSessionStatus.CONFLICT_RESOLUTION_PENDING]:
                    if current_processing_user is not None:
                        # 理论上不应发生，但作为防御性编程
                        logger.error(f"[ViewSet.BeginConflict] 会话 {session_id} 状态为 {current_status} 但意外地有处理用户 {current_processing_user.username}。正在纠正。")
                        # session.processing_user = None # 会在下面被覆盖
                    
                    session.status = ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS
                    session.processing_user = request_user
                    session.last_heartbeat_at = timezone.now()
                    session.last_activity = timezone.now()
                    session.save()
                    SessionOperation.objects.create(session=session, operation_type='begin_conflict_processing', user=request_user, old_status=current_status, new_status=session.status, details={"message": f"User {request_user.username} started/resumed conflict processing."})
                    logger.info(f"[ViewSet.BeginConflict] 用户 {request_user.username} 将会话 {session_id} 从 {current_status} 转为 IN_PROGRESS")
                    return Response({"success": True, "message": "已成功开始处理冲突。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)

                # 情况二和三: 会话已在 IN_PROGRESS
                elif current_status == ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
                    if current_processing_user == request_user:
                        # 用户是本人，更新心跳即可
                        session.last_heartbeat_at = timezone.now()
                        session.last_activity = timezone.now()
                        session.save(update_fields=['last_heartbeat_at', 'last_activity', 'updated_at'])
                        logger.info(f"[ViewSet.BeginConflict] 用户 {request_user.username} (当前处理者) 继续处理会话 {session_id} (心跳已更新)")
                        return Response({"success": True, "message": "您可以继续处理冲突。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)
                    else:
                        # 其他人正在处理，检查是否超时
                        heartbeat_timeout_minutes = getattr(settings, "SESSION_HEARTBEAT_TIMEOUT_MINUTES", 2)
                        is_timed_out = False
                        if session.last_heartbeat_at:
                            if (timezone.now() - session.last_heartbeat_at) > timedelta(minutes=heartbeat_timeout_minutes):
                                is_timed_out = True
                        else:
                            # 没有心跳记录，但有处理用户，视为可以尝试接管（或根据业务规则判定为立即超时）
                            is_timed_out = True 
                            logger.warning(f"[ViewSet.BeginConflict] 会话 {session_id} 由 {current_processing_user.username if current_processing_user else 'Unknown'} 处理中但无心跳记录。视为超时，用户 {request_user.username} 可接管")

                        if is_timed_out:
                            logger.info(f"[ViewSet.BeginConflict] 会话 {session_id} 由 {current_processing_user.username if current_processing_user else 'Other'} 处理中但已超时。用户 {request_user.username} 正在接管")
                            original_user_username = current_processing_user.username if current_processing_user else "Unknown"
                            
                            # 步骤 1: 记录超时并转为 PENDING (可选但清晰)
                            # session.status = ImportSessionStatus.CONFLICT_RESOLUTION_PENDING
                            # session.error_message = session.error_message or f"原处理用户 {original_user_username} 心跳超时。"
                            # session.save(update_fields=['status', 'error_message', 'updated_at'])
                            # SessionOperation.objects.create(session=session, operation_type='timeout_to_pending', old_status=ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS, new_status=ImportSessionStatus.CONFLICT_RESOLUTION_PENDING, details={"timed_out_user": original_user_username})

                            # 步骤 2: 新用户接管并转为 IN_PROGRESS
                            session.status = ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS # 保持或重新设置为 IN_PROGRESS
                            session.processing_user = request_user
                            session.last_heartbeat_at = timezone.now()
                            session.last_activity = timezone.now()
                            # error_message 字段不应在此处被新用户接管的消息覆盖，保留原超时信息或分析信息
                            session.save()
                            SessionOperation.objects.create(session=session, operation_type='takeover_conflict_processing', user=request_user, old_status=ImportSessionStatus.CONFLICT_RESOLUTION_PENDING, new_status=session.status, details={"message": f"User {request_user.username} took over from {original_user_username} due to timeout."})
                            return Response({"success": True, "message": f"已成功接管来自用户 {original_user_username} 的超时会话。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)
                        else:
                            logger.warning(f"[ViewSet.BeginConflict] 用户 {request_user.username} 尝试接管会话 {session_id} (当前处理者: {current_processing_user.username})，但对方未超时")
                            return Response({
                                "success": False, 
                                "error": f"会话当前由用户 '{current_processing_user.username}' 处理中，且对方在线。",
                                "current_status": current_status,
                                "processing_user": current_processing_user.username
                            }, status=status.HTTP_409_CONFLICT) # 409 Conflict is appropriate here
                else: # Should not be reached if actionable_conflict_statuses is comprehensive
                    logger.error(f"[ViewSet.BeginConflict] 未处理的会话状态: {current_status}，会话: {session_id}")
                    return Response({"success": False, "error": "未知的会话状态，无法操作。", "current_status": current_status}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except ImportSession.DoesNotExist:
            logger.warning(f"[ViewSet.BeginConflict] 会话 {session_id} 不存在，用户: {request.user.username}")
            return Response({"success": False, "error": "导入会话未找到。"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.exception(f"[ViewSet.BeginConflict] 开始/继续/接管冲突处理失败，会话: {session_id}，用户: {request.user.username}，错误: {e}")
            return Response({"success": False, "error": "服务器内部错误，无法处理请求。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], url_path='cancel')
    def cancel(self, request, pk=None):
        """
        取消Excel导入会话
        
        POST /api/archive-records/excel-import/{id}/cancel/
        
        迁移自: ExcelImportCancelView.post()
        """
        session_id = pk  # 从URL参数获取session_id
        expected_session_id = request.data.get('expected_session_id')  # CHANGE: [2025-05-28] 新增期望会话ID校验
        reason = request.data.get('reason', '用户手动取消') # 允许前端传递取消原因
        logger.info(f"[ViewSet.Cancel] 用户 {request.user.username} 请求取消导入会话，ID: {session_id}, 期望ID: {expected_session_id}, 原因: {reason}")
        
        if not session_id:
            logger.warning("[ViewSet.Cancel] 取消导入请求缺少会话ID参数")
            return Response(
                {'success': False, 'error': '未提供导入会话ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            session_manager = ImportSessionManager()
            
            # CHANGE: [2025-05-28] 实现严格的会话状态校验
            # 获取系统当前唯一活跃会话
            current_active_session = session_manager.get_system_active_session()
            
            if not current_active_session:
                logger.warning(f"[ViewSet.Cancel] 系统当前无活跃会话，但用户尝试取消会话 {session_id}")
                return Response({
                    'success': False, 
                    'error': '系统当前没有活跃的导入会话，可能已经完成或取消。',
                    'error_type': 'no_active_session'
                }, status=status.HTTP_409_CONFLICT)
            
            # 校验会话ID匹配
            if str(current_active_session.session_id) != str(session_id):
                logger.warning(f"[ViewSet.Cancel] 会话ID不匹配。请求的: {session_id}, 当前活跃的: {current_active_session.session_id}")
                return Response({
                    'success': False,
                    'error': f'会话状态已变更，请刷新页面。当前活跃会话: {current_active_session.session_id}',
                    'error_type': 'session_id_mismatch',
                    'current_active_session_id': str(current_active_session.session_id)
                }, status=status.HTTP_409_CONFLICT)
            
            # 校验期望会话ID（如果提供）
            if expected_session_id and str(expected_session_id) != str(current_active_session.session_id):
                logger.warning(f"[ViewSet.Cancel] 期望会话ID不匹配。期望的: {expected_session_id}, 当前活跃的: {current_active_session.session_id}")
                return Response({
                    'success': False,
                    'error': '会话状态已发生变化，请刷新页面获取最新状态。',
                    'error_type': 'expected_session_mismatch',
                    'current_active_session_id': str(current_active_session.session_id)
                }, status=status.HTTP_409_CONFLICT)

            # 业务权限检查 - 简化为任何登录用户都可以取消任何会话
            # CHANGE: [权限简化] 移除了复杂的业务权限控制，但保留技术安全检查（会话状态、并发控制等）
            # 未来如需要可重新引入: 只有创建者、当前处理者或管理员可以取消
            can_cancel = True  # 简化的业务权限检查

            # 如果会话已经是终态，则不应再取消（技术安全检查）
            terminal_states = [ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY, ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS, ImportSessionStatus.CANCELLED, ImportSessionStatus.ERROR, ImportSessionStatus.FINALIZED]
            if current_active_session.status in terminal_states:
                logger.warning(f"[ViewSet.Cancel] 尝试取消一个已经处于终态的会话: {session_id}, 状态: {current_active_session.status}")
                
                # CHANGE: [2024-07-27] 已取消会话或错误会话也返回成功，允许UI正常清理状态
                if current_active_session.status == ImportSessionStatus.CANCELLED:
                    return Response({
                        'success': True, 
                        'data': {
                            'message': f'会话已处于" {current_active_session.get_status_display()} "状态，无需取消',
                            'session_id': str(session_id),
                            'status': ImportSessionStatus.CANCELLED
                        }
                    })
                    
                return Response({
                    'success': False, 
                    'error': f'会话已处于" {current_active_session.get_status_display()} "状态，无需取消', 
                    'errorType': 'session_already_terminated'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 核心操作：清理会话资源并标记为已取消
            # CHANGE: [2025-05-28] 使用重构后的取消逻辑，传递session对象而非ID
            session_manager._cleanup_session_resources(
                session=current_active_session,  # 传递session对象
                cancelled_by=request.user,
                reason=reason
            )
            
            # CHANGE: [2024-07-27] 添加强制会话缓存清理，确保会话真正被清理
            # 强制从活跃会话列表中移除
            try:
                active_key = "excel_active_sessions"
                if hasattr(cache, 'get') and hasattr(cache, 'set'):
                    active_sessions = cache.get(active_key) or {}
                    if session_id in active_sessions:
                        del active_sessions[session_id]
                        cache.set(active_key, active_sessions)
                        logger.info(f"[ViewSet.Cancel] 从活跃会话缓存中移除了会话: {session_id}")
            except Exception as e_cache:
                logger.warning(f"[ViewSet.Cancel] 清理会话缓存时出错: {e_cache}")
            
            logger.info(f"[ViewSet.Cancel] 导入会话已成功标记为取消并清理资源: {session_id}")
            return Response({
                'success': True, 
                'data': {
                    'message': '导入会话已成功取消',
                    'session_id': str(session_id),
                    'status': ImportSessionStatus.FINALIZED  # CHANGE: [2025-05-28] 返回最终状态
                }
            })
                    
        except Exception as e:
            logger.error(f"[ViewSet.Cancel] 取消导入会话 {session_id} 时出错: {str(e)}", exc_info=True)
            return Response(
                {'success': False, 'error': f'取消导入会话时出错: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'], url_path='heartbeat')
    def heartbeat(self, request, pk=None):
        """
        记录导入会话心跳
        
        POST /api/archive-records/excel-import/{id}/heartbeat/
        
        迁移自: SessionHeartbeatView.post()
        """
        session_id = pk  # 在ViewSet中，pk就是URL中的id参数
        logger.info(f"[ViewSet.Heartbeat] 用户 {request.user.username} 发送心跳，会话ID: {session_id}")
        
        manager = ImportSessionManager()
        success, message = manager.record_heartbeat(session_id=session_id, user=request.user)

        if success:
            return Response({"success": True, "message": message}, status=status.HTTP_200_OK)
        else:
            # 根据消息内容判断更具体的错误状态码
            if "会话不存在" in message or "已不存在" in message:
                status_code = status.HTTP_404_NOT_FOUND
            elif "不是当前会话的处理者" in message or "处理者已改变" in message:
                status_code = status.HTTP_403_FORBIDDEN
            elif "非活跃" in message: # 例如 "会话非活跃，无法记录心跳。" 或 "会话已变为非活跃状态"
                 status_code = status.HTTP_400_BAD_REQUEST # 或者 409 Conflict 如果认为状态冲突更合适
            else:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR # 默认为服务器内部错误
            
            logger.warning(f"[ViewSet.Heartbeat] 心跳API调用失败 for session {session_id}, user {request.user.username}: {message}")
            return Response({"success": False, "error": message}, status=status_code)

    @action(detail=True, methods=['post'], url_path='acknowledge')
    def acknowledge(self, request, pk=None):
        """
        用户确认导入结果
        
        POST /api/archive-records/excel-import/{id}/acknowledge/
        
        迁移自: AcknowledgeImportResultsView.post()
        """
        session_id = pk  # 在ViewSet中，pk就是URL中的id参数
        logger.info(f"[ViewSet.Acknowledge] 用户 {request.user.username} 请求确认识读会话 {session_id} 的结果。")
        
        manager = ImportSessionManager()
        # CHANGE: [2025-05-30] 修正 acknowledge_session_results 调用参数
        # 首先，根据 session_id 获取 ImportSession 对象
        try:
            session_obj = ImportSession.objects.get(session_id=session_id)
        except ImportSession.DoesNotExist:
            logger.warning(f"[ViewSet.Acknowledge] 会话 {session_id} 不存在，无法确认结果。")
            return Response({
                "success": False, 
                "error": "指定的导入会话不存在。",
                "current_status": None
            }, status=status.HTTP_404_NOT_FOUND)

        success, message, current_status_val = manager.acknowledge_session_results(session=session_obj, user=request.user)

        if success:
            logger.info(f"[ViewSet.Acknowledge] 会话 {session_id} 结果确认成功。消息: {message}")
            return Response({
                "success": True, 
                "data": {
                    "message": message,
                    "session_id": str(session_id),
                    "new_status": current_status_val.value if current_status_val else None
                }
            }, status=status.HTTP_200_OK)
        else:
            logger.warning(f"[ViewSet.Acknowledge] 会话 {session_id} 结果确认失败。消息: {message}, 当前状态: {current_status_val}")
            status_code = status.HTTP_400_BAD_REQUEST
            if message == "指定的导入会话不存在。":
                status_code = status.HTTP_404_NOT_FOUND
            elif '并非"导入完成"状态' in message: # 使用单引号包裹，避免内部双引号冲突
                 status_code = status.HTTP_409_CONFLICT # 状态冲突
            
            return Response({
                "success": False, 
                "error": message,
                "current_status": current_status_val.value if current_status_val else None
            }, status=status_code)

    @swagger_auto_schema(
        operation_summary="暂停/搁置当前导入会话中的冲突处理",
        operation_description="当用户正在处理冲突（会话处于'冲突处理中'状态）并决定暂时中断（例如关闭处理界面但未提交）时调用。成功后，会话状态将变为'冲突处理暂停'，处理用户将被清空，允许其他用户或同一用户后续继续处理。",
        responses={
            200: openapi.Response(
                description="成功暂停冲突处理",
                examples={"application/json": {"success": True, "message": "冲突处理已暂停。", "session_data": "{...}"}}
            ),
            400: openapi.Response(description="无效的会话ID或会话状态不符/非当前处理用户操作"),
            403: openapi.Response(description="用户无权限执行此操作"),
            404: openapi.Response(description="会话未找到")
        },
        tags=['Excel Import Session Management']
    )
    @action(detail=True, methods=['post'], url_path='pend-conflict-processing')
    def pend_conflict_processing(self, request, pk=None):
        """
        暂停/搁置当前导入会话中的冲突处理
        
        POST /api/archive-records/excel-import/{id}/pend-conflict-processing/
        
        迁移自: PendActiveConflictProcessingView.post()
        """
        session_id = pk  # 在ViewSet中，pk就是URL中的id参数
        logger.info(f"[ViewSet.PendConflict] 用户 {request.user.username} 尝试暂停冲突处理，会话ID: {session_id}")
        
        try:
            with transaction.atomic():
                session = ImportSession.objects.select_for_update().get(session_id=session_id)

                if not session.is_active(): # is_active 也会检查 CANCELLED, FINALIZED 等状态
                    logger.warning(f"[ViewSet.PendConflict] 用户 {request.user.username} 尝试暂停非活跃会话 {session_id} 的冲突处理，状态: {session.status}")
                    return Response({"success": False, "error": "会话已非活跃，无法操作。"}, status=status.HTTP_400_BAD_REQUEST)

                if session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
                    logger.warning(f"[ViewSet.PendConflict] 会话 {session_id} 状态为 {session.status}，不是 CONFLICT_RESOLUTION_IN_PROGRESS，无法暂停冲突处理")
                    # 即使状态不是IN_PROGRESS，如果用户是processing_user，也应允许其"放弃"处理权，并转为PENDING
                    # 但如果根本没有processing_user，或者状态完全不对，则拒绝
                    if not session.processing_user or session.processing_user != request.user:
                         return Response({
                            "success": False, 
                            "error": f"会话当前状态为'{session.get_status_display()}'，并非'处理中'，无法暂停。或您不是当前处理用户。"
                        }, status=status.HTTP_400_BAD_REQUEST)
                
                # 只有当前处理用户才能暂停自己的操作
                if session.processing_user != request.user:
                    logger.warning(f"[ViewSet.PendConflict] 用户 {request.user.username} 不是会话 {session_id} 的当前处理用户 ({session.processing_user.username if session.processing_user else 'None'})，无法暂停")
                    return Response({
                        "success": False, 
                        "error": "您不是此会话的当前处理用户，无法暂停。"
                    }, status=status.HTTP_403_FORBIDDEN)

                old_status_val = session.status
                session.status = ImportSessionStatus.CONFLICT_RESOLUTION_PENDING
                session.processing_user = None # 清空处理用户
                session.last_activity = timezone.now() # 更新活动时间
                # last_heartbeat_at 不需要在此处清除，因为它反映的是上一个IN_PROGRESS状态的活动
                session.save()

                SessionOperation.objects.create(
                    session=session,
                    operation_type='status_change', # 或者更具体的如 'pend_conflict_processing'
                    user=request.user,
                    old_status=old_status_val,
                    new_status=session.status,
                    details={
                        "message": f"User {request.user.username} pended active conflict processing.",
                        "action": "pend_active_conflict_processing"
                    }
                )
                logger.info(f"[ViewSet.PendConflict] 用户 {request.user.username} 成功暂停会话 {session_id} 的冲突处理。状态变更为 {session.status}")
                return Response({
                    "success": True, 
                    "message": "冲突处理已暂停。", 
                    "session_data": session.to_dict()
                }, status=status.HTTP_200_OK)

        except ImportSession.DoesNotExist:
            logger.warning(f"[ViewSet.PendConflict] 会话 {session_id} 不存在，无法暂停冲突处理")
            return Response({"success": False, "error": "导入会话未找到。"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.exception(f"[ViewSet.PendConflict] 暂停会话 {session_id} 冲突处理时出错: {e}")
            return Response({"success": False, "error": "服务器内部错误，无法暂停冲突处理。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

