# Operation Document: Refine Error Handling in _safe_bulk_create for Excel Import

## 📋 Change Summary

**Purpose**: To further address the "You can't execute queries until the end of the 'atomic' block." error during Excel imports by ensuring `django.db.Error` exceptions from within the `_safe_bulk_create` method's individual record creation fallback are correctly propagated.
**Scope**: Modified the `_safe_bulk_create` method in `archive_records/services/excel_import.py`.
**Associated**: Continuation of debugging the 500 error reported by the user, specifically ensuring all paths leading to broken transactions are covered.

## 🔧 Operation Steps

### 📊 OP-001: Analyze Remaining Path for Transaction Error

**Precondition**: Previous fixes addressed `django.db.Error` handling in the main batch processing loop of `_process_dataframe`, but the error persists.
**Operation**:
    1. Re-examined the browser console log showing the persistent 500 error with the same transaction message.
    2. Hypothesized that the `_safe_bulk_create` method, when falling back to individual record creation after a `UNIQUE constraint` failure, might not be correctly re-throwing `django.db.Error` if an individual `create()` call fails with a database error.
    3. Reviewed the `_safe_bulk_create` method's logic and confirmed that its `except Exception as individual_error:` block would catch a `django.db.Error` from an individual `create()` call and then `continue`, rather than re-throwing the database error. This would break the transaction without the caller (`_process_dataframe`) being aware until a later database operation.
**Postcondition**: Identified a loophole in `_safe_bulk_create`'s error handling that could lead to the observed transaction error.

### ✏️ OP-002: Modify Exception Handling in `_safe_bulk_create`

**Precondition**: The `_safe_bulk_create` method's fallback for individual record creation has a generic exception handler that doesn't re-throw database errors.
**Operation**:
    1. Modified the `try-except` block within the `for record in records:` loop inside `_safe_bulk_create`.
    2. Changed `except Exception as individual_error:` to be more specific:
        - Added `except django.db.Error as db_err_individual:`: This block now logs the specific database error (with `exc_info=True`) and then re-throws (`raise`) the `db_err_individual`.
        - Retained `except Exception as individual_error:` for other non-DB errors, which logs the error (with `exc_info=True`) and continues to the next record (maintaining the original behavior for non-DB errors in this specific fallback path).
**Postcondition**: `django.db.Error` exceptions occurring during the individual creation fallback in `_safe_bulk_create` are now re-thrown. This allows the error to propagate to the `transaction.atomic()` block in `_process_dataframe`, ensuring the batch transaction is correctly rolled back, and preventing the "can't execute queries" error from that specific path.

## 📝 Change Details

### CH-001: Refactor Exception Handling in `_safe_bulk_create` Fallback

**File**: `archive_records/services/excel_import.py`
**Method**: `_safe_bulk_create`
**Before** (relevant part):

```python
# ...
                        try:
                            # ... individual ArchiveRecord.objects.create(...) ...
                        except Exception as individual_error: # Catches all, including DB errors
                            logger.error(f"单条记录创建失败: {str(individual_error)}")
                            continue # Problem: DB error breaks transaction but loop continues
# ...
```

**After** (relevant part):

```python
# ...
                        try:
                            # ... individual ArchiveRecord.objects.create(...) ...
                        except django.db.Error as db_err_individual: # Specific DB error
                            logger.error(f"单条记录创建失败 (数据库错误): {str(db_err_individual)}，记录数据可能为: {record.__dict__ if hasattr(record, '__dict__') else record}", exc_info=True)
                            raise # Re-throw DB error to propagate
                        except Exception as individual_error: # Other non-DB errors
                            logger.error(f"单条记录创建失败 (非数据库错误): {str(individual_error)}，记录数据可能为: {record.__dict__ if hasattr(record, '__dict__') else record}", exc_info=True)
                            continue # Continue with the next record for non-DB errors
# ...
```

**Rationale**: Ensures that if a database error occurs during the fallback mechanism of creating records one by one within `_safe_bulk_create`, the error is propagated. This allows the calling `transaction.atomic()` block in `_process_dataframe` to correctly recognize the failure, roll back the transaction for that batch, and prevent the "You can't execute queries until the end of the 'atomic' block." error when subsequent operations like `import_log.save()` are attempted within the (now correctly rolled-back or never-committed) transaction.

## ✅ Verification Results

**Method**: Code review and logical deduction. The change ensures a previously unhandled path for `django.db.Error` now correctly leads to transaction rollback.
**Results**: This change, in conjunction with previous modifications, should comprehensively prevent the specific transaction error during Excel imports by ensuring all identified database error paths within batch atomic transactions lead to proper rollback and error propagation.
**Problems**: None anticipated for this specific refinement.
**Solutions**: N/A.
