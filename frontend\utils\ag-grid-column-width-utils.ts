import { GridApi } from 'ag-grid-enterprise';

/**
 * 计算字符宽度的工具函数
 * @param char 要计算宽度的字符
 * @returns 字符的像素宽度
 */
function getCharWidth(char: string): number {
  const charCode = char.charCodeAt(0);
  
  // 中文字符（CJK统一汉字、CJK扩展等）
  if ((charCode >= 0x4E00 && charCode <= 0x9FFF) ||  // CJK统一汉字
      (charCode >= 0x3400 && charCode <= 0x4DBF) ||  // CJK扩展A
      (charCode >= 0x20000 && charCode <= 0x2A6DF) || // CJK扩展B
      (charCode >= 0x2A700 && charCode <= 0x2B73F)) { // CJK扩展C
    return 16; // 中文字符
  }
  // 全角符号、全角数字等
  else if (charCode >= 0xFF00 && charCode <= 0xFFEF) {
    return 16; // 全角字符
  }
  // 数字
  else if (char >= '0' && char <= '9') {
    return 8; // 数字字符
  }
  // 符号和标点
  else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(char)) {
    return 6; // 符号字符通常较窄
  }
  // 英文字母
  else if (/[a-zA-Z]+/.test(char)) {
    return 8; // 英文字符
  }
  // 其他所有字符
  else {
    return 10; // 默认宽度，确保足够宽
  }
}

/**
 * 计算文本宽度的工具函数
 * @param text 要计算宽度的文本
 * @returns 文本的像素宽度
 */
export function calculateTextWidth(text: string): number {
  let width = 0;
  for (let i = 0; i < text.length; i++) {
    width += getCharWidth(text.charAt(i));
  }
  return width;
}

/**
 * 根据表头名称估算列的初始宽度，结合更精确的文本宽度计算。
 * @param headerName - 列的表头字符串。
 * @param options - 可选参数对象。
 * @param options.padding - 总的额外内边距（用于图标和两侧间隙）。
 * @param options.minWidth - 计算得出的最小宽度。
 * @param options.isSpecialCase - 是否为特殊情况（如日期、状态列，可能需要额外最小宽度）。
 * @param options.specialCaseMinWidth - 特殊情况下的最小宽度。
 * @returns 计算得出的列宽度（像素）。
 */
export const getHeaderColumnWidth = (
  headerName: string,
  options: {
    padding?: number;
    minWidth?: number;
    isSpecialCase?: boolean;
    specialCaseMinWidth?: number;
  } = {}
): number => {
  const {
    padding = 70,                       // 默认：总内边距为70 (左右各约35px，用于图标和间距)
    minWidth = 90,                      // 默认：列的最小宽度
    isSpecialCase = false,
    specialCaseMinWidth = 140,          // 默认：日期或状态列的建议最小宽度
  } = options;

  if (!headerName) {
    return minWidth;
  }

  // 使用本文件中的 calculateTextWidth 获取纯文本宽度
  const totalTextPixelWidth = calculateTextWidth(headerName);

  let calculatedWidth = totalTextPixelWidth + padding;

  if (isSpecialCase) {
    calculatedWidth = Math.max(calculatedWidth, specialCaseMinWidth);
  }
  
  return Math.max(calculatedWidth, minWidth);
};

// CHANGE: [2025-05-13] 移除不再需要的 sizeColumnsToHeaderWidth 函数
/*
export function sizeColumnsToHeaderWidth<T>(api: GridApi<T>, extraSpace: number = 50): void {
  // 获取所有列
  const allColumnIds = api.getColumns()?.map(column => column.getColId());
  if (!allColumnIds || allColumnIds.length === 0) return;

  // 为每一列设置宽度
  allColumnIds.forEach(columnId => {
    const column = api.getColumnDef(columnId);
    if (!column) return;

    // 获取表头文本
    const headerName = column.headerName || columnId;

    // 计算文本宽度
    const textWidth = calculateTextWidth(headerName);

    // 设置列宽（文本宽度 + 额外空间）
    const newWidth = textWidth + extraSpace;

    // 应用新宽度，但尊重minWidth设置
    const minWidth = column.minWidth || 80;
    const finalWidth = Math.max(newWidth, minWidth);

    // 使用API方法设置列宽
    api.setColumnWidths([{
      key: columnId,
      newWidth: finalWidth
    }]);
  });
}
*/ 