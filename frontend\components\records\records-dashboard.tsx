"use client"

import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  BarChart3,
  FileText,
  FolderArchive,
  Upload,
  CheckCircle2,
  AlertCircle,
  Clock,
  AlertTriangle,
} from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"

// 模拟数据 - 实际应用中应从API获取
const dashboardData = {
  importBatches: 128,
  archiveEntries: 5842,
  importErrors: 347,
  archivedEntries: 4921,
  distributionRatio: 78, // 完成报告发放的档案占归档条目的比例 (%)
  archiveRatio: 84, // 归档条目占导入条目比例 (%)
  recentActivities: [
    { id: 1, type: "import", user: "张三", time: "2023-04-29 14:32", count: 156, status: "completed" },
    { id: 2, type: "correction", user: "李四", time: "2023-04-29 11:15", count: 3, status: "completed" },
    { id: 3, type: "archive", user: "王五", time: "2023-04-28 16:45", count: 78, status: "completed" },
    { id: 4, type: "distribution", user: "赵六", time: "2023-04-28 10:20", count: 42, status: "pending" },
    { id: 5, type: "import", user: "钱七", time: "2023-04-27 09:05", count: 213, status: "error" },
  ],
}

export function RecordsDashboard() {
  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "import":
        return <Upload className="h-4 w-4" />
      case "correction":
        return <AlertTriangle className="h-4 w-4" />
      case "archive":
        return <FolderArchive className="h-4 w-4" />
      case "distribution":
        return <FileText className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  // 获取活动状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "pending":
        return <Clock className="h-4 w-4 text-amber-500" />
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  // 获取活动类型中文名
  const getActivityTypeName = (type: string) => {
    switch (type) {
      case "import":
        return "导入"
      case "correction":
        return "更正"
      case "archive":
        return "归档"
      case "distribution":
        return "发放"
      default:
        return type
    }
  }

  return (
    <div className="space-y-6">
      {/* 顶部统计卡片 - 已重新排序 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">台账导入批次数</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.importBatches}</div>
            <p className="text-xs text-muted-foreground">总计导入批次</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">导入异常</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.importErrors}</div>
            <p className="text-xs text-muted-foreground">需要处理的异常记录</p>
            <Button variant="link" size="sm" className="px-0 mt-2" asChild>
              <Link href="/records/errors">处理异常</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">档案条目数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.archiveEntries}</div>
            <p className="text-xs text-muted-foreground">系统中的总档案条目</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">归档条目数</CardTitle>
            <FolderArchive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.archivedEntries}</div>
            <p className="text-xs text-muted-foreground">已完成归档的条目</p>
          </CardContent>
        </Card>
      </div>

      {/* 比例指标卡片 - 已交换位置并更新标题 */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>归档条目占导入条目比例</CardTitle>
            <CardDescription>归档条目占总导入条目的百分比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">进度</span>
              <span className="text-sm font-medium">{dashboardData.archiveRatio}%</span>
            </div>
            <Progress value={dashboardData.archiveRatio} className="h-2" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>完成报告发放的档案占归档条目的比例</CardTitle>
            <CardDescription>已完成报告发放的档案占归档条目的百分比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">进度</span>
              <span className="text-sm font-medium">{dashboardData.distributionRatio}%</span>
            </div>
            <Progress value={dashboardData.distributionRatio} className="h-2" />
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 - 移除了字段统计，最近活动现在占据整行 */}
      <Card>
        <CardHeader>
          <CardTitle>最近活动</CardTitle>
          <CardDescription>最近的台账相关活动</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {dashboardData.recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4">
                <div className="bg-muted p-2 rounded-full">{getActivityIcon(activity.type)}</div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {activity.user} 进行了{getActivityTypeName(activity.type)}操作
                    </p>
                    {getStatusIcon(activity.status)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {activity.time} · {activity.count} 条记录
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 快速访问卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-lg font-medium">台账管理</CardTitle>
            <FileText className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">查看和管理档案台账记录</p>
            <Button asChild className="w-full">
              <Link href="/records/ledger">进入台账管理</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-lg font-medium">台账导入</CardTitle>
            <Upload className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">导入新的档案台账数据</p>
            <Button asChild className="w-full">
              <Link href="/records/import">导入台账</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-lg font-medium">数据统计</CardTitle>
            <BarChart3 className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-muted-foreground">查看详细的台账数据统计</p>
            <Button asChild className="w-full">
              <Link href="/statistics">查看统计</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
