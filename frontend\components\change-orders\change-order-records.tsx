"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, FileText, Download, Printer, Eye } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface ChangeOrderRecordsProps {
  changeOrder: any
}

export function ChangeOrderRecords({ changeOrder }: ChangeOrderRecordsProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // 获取更改单中的记录
  const records = changeOrder.records || []

  // 筛选记录
  const filteredRecords = records.filter(
    (record: any) =>
      searchQuery === "" ||
      record.unifiedNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.projectName?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="bg-gray-50 border-b pb-3">
        <CardTitle className="text-lg font-medium text-gray-800">档案列表</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="搜索统一编号或标题..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                档案条目: {records.length} 项
              </Badge>
            </div>
          </div>

          {filteredRecords.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              <FileText className="mx-auto h-12 w-12 opacity-30" />
              <p className="mt-2">尚未添加任何档案记录</p>
              <p className="text-sm text-gray-500 mt-1">请在"更改内容"标签页中选择档案记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse min-w-[1200px]">
                <thead>
                  <tr className="bg-gray-50">
                    {/* 固定在左侧的列 */}
                    <th className="sticky left-0 z-10 bg-gray-50 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      统一编号
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      标题
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      项目名称
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      样品编号
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      委托单位
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      委托日期
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      委托人
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      归档状态
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      更改状态
                    </th>
                    {/* 固定在右侧的列 */}
                    <th className="sticky right-0 z-10 bg-gray-50 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRecords.map((record: any) => {
                    // 检查记录是否有更改
                    const hasChanges = changeOrder.changes?.some((change: any) => change.recordId === record.id)

                    return (
                      <tr key={record.id} className="hover:bg-gray-50">
                        {/* 固定在左侧的列 */}
                        <td className="sticky left-0 z-10 bg-white px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b group-hover:bg-gray-50">
                          {record.unifiedNumber}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">{record.title}</td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">
                          {record.projectName}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">
                          {record.sampleNumber}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">
                          {record.clientCompany}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">
                          {record.commissionDate}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap border-b">
                          {record.commissioner}
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap border-b">
                          <Badge
                            variant="outline"
                            className={
                              record.status === "已归档"
                                ? "bg-green-50 text-green-700 border-green-200"
                                : "bg-yellow-50 text-yellow-700 border-yellow-200"
                            }
                          >
                            {record.status || "未知"}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap border-b">
                          <Badge
                            variant="outline"
                            className={
                              hasChanges
                                ? "bg-blue-50 text-blue-700 border-blue-200"
                                : "bg-gray-50 text-gray-700 border-gray-200"
                            }
                          >
                            {hasChanges ? "有更改" : "无更改"}
                          </Badge>
                        </td>
                        {/* 固定在右侧的列 */}
                        <td className="sticky right-0 z-10 bg-white px-4 py-3 text-sm whitespace-nowrap border-b group-hover:bg-gray-50">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                操作
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/records/detail/${record.id}`} className="flex items-center gap-2">
                                  <Eye className="h-4 w-4" />
                                  查看档案
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center gap-2">
                                <Download className="h-4 w-4" />
                                下载档案
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center gap-2">
                                <Printer className="h-4 w-4" />
                                打印档案
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
