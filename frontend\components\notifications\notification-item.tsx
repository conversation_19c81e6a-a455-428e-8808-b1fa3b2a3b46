"use client"
import { cn } from "@/lib/utils"
import { AlertCircle, CheckCircle, Info, XCircle } from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"

interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  read: boolean
  createdAt: string
  link?: string
}

interface NotificationItemProps {
  notification: Notification
  onMarkAsRead: (id: string) => void
}

export function NotificationItem({ notification, onMarkAsRead }: NotificationItemProps) {
  const getIcon = () => {
    switch (notification.type) {
      case "info":
        return <Info className="h-5 w-5 text-blue-500" />
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 格式化时间为"x分钟前"、"x小时前"等
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
    } catch (error) {
      return dateString
    }
  }

  const handleClick = () => {
    if (!notification.read) {
      onMarkAsRead(notification.id)
    }
  }

  const content = (
    <div
      className={cn(
        "flex items-start gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",
        !notification.read && "bg-blue-50 dark:bg-blue-900/20",
      )}
      onClick={handleClick}
    >
      <div className="flex-shrink-0 mt-1">{getIcon()}</div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h5 className={cn("text-sm font-medium", !notification.read && "font-semibold")}>{notification.title}</h5>
          <span className="text-xs text-muted-foreground">{formatTime(notification.createdAt)}</span>
        </div>
        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{notification.message}</p>
      </div>
      {!notification.read && <div className="flex-shrink-0 w-2 h-2 rounded-full bg-blue-500 mt-2" aria-hidden="true" />}
    </div>
  )

  if (notification.link) {
    return <Link href={notification.link}>{content}</Link>
  }

  return content
}
