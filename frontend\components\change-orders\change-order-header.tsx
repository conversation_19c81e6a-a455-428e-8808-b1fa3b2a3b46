"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Lock, CheckCircle2, Archive, Printer, Trash, FileText, Send } from "lucide-react"

interface ChangeOrderHeaderProps {
  changeOrder: any
  isNewOrder: boolean
  lastSavedAt: string | null
  isSaving: boolean
  isProcessing: boolean
  canAutoSave: () => boolean
  onSaveAsDraft: () => void
  onLock: () => void
  onUnlock: () => void
  onConfirm: () => void
  onRevertToDraft: () => void
  onArchive: () => void
  onPrint: () => void
  onDelete: () => void
}

export function ChangeOrderHeader({
  changeOrder,
  isNewOrder,
  lastSavedAt,
  isSaving,
  isProcessing,
  canAutoSave,
  onSaveAsDraft,
  onLock,
  onUnlock,
  onConfirm,
  onRevertToDraft,
  onArchive,
  onPrint,
  onDelete,
}: ChangeOrderHeaderProps) {
  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "creating":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            正在创建
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            草稿
          </Badge>
        )
      case "locked":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            已锁定
          </Badge>
        )
      case "confirmed":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            已确认
          </Badge>
        )
      case "archived":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            已归档
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="text-lg font-medium">状态:</div>
            <div className="text-lg">{getStatusBadge(changeOrder.status)}</div>
            {changeOrder.status === "draft" && lastSavedAt && (
              <div className="text-sm text-muted-foreground flex items-center ml-2">
                <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                上次保存: {lastSavedAt}
              </div>
            )}
            {isSaving && (
              <div className="text-sm text-muted-foreground flex items-center ml-2">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-900 mr-1"></div>
                正在保存...
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {/* 创建状态可以手动保存为草稿 */}
            {changeOrder.status === "creating" && canAutoSave() && (
              <Button onClick={onSaveAsDraft} disabled={isProcessing || isSaving}>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                保存为草稿
              </Button>
            )}

            {/* 草稿状态可以锁定或删除 */}
            {changeOrder.status === "draft" && true && (
              <>
                <Button onClick={onLock} disabled={isProcessing || isSaving}>
                  <Lock className="mr-2 h-4 w-4" />
                  锁定
                </Button>
                <Button variant="destructive" onClick={onDelete} disabled={isProcessing || isSaving}>
                  <Trash className="mr-2 h-4 w-4" />
                  删除更改单
                </Button>
              </>
            )}

            {/* 锁定状态可以解锁或确认 */}
            {changeOrder.status === "locked" && (
              <div className="flex gap-2">
                {true && (
                  <Button
                    onClick={onUnlock}
                    disabled={isProcessing}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Lock className="h-5 w-5" />
                    解锁
                  </Button>
                )}
                {true && (
                  <Button
                    onClick={onConfirm}
                    disabled={isProcessing}
                    variant="default"
                    className="bg-black text-white hover:bg-gray-800 flex items-center gap-2"
                  >
                    <CheckCircle2 className="h-5 w-5" />
                    确认
                  </Button>
                )}
              </div>
            )}

            {/* 确认状态可以归档 */}
            {changeOrder.status === "confirmed" && (
              <div className="flex gap-2">
                <Button
                  onClick={onRevertToDraft}
                  disabled={isProcessing}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <FileText className="h-5 w-5" />
                  转为草稿
                </Button>
                <Button
                  onClick={onArchive}
                  disabled={isProcessing}
                  variant="default"
                  className="bg-black text-white hover:bg-gray-800 flex items-center gap-2"
                >
                  <Archive className="h-5 w-5" />
                  归档
                </Button>
                <Button onClick={onPrint} disabled={isProcessing} variant="outline" className="flex items-center gap-2">
                  <Printer className="h-5 w-5" />
                  打印确认单
                </Button>
              </div>
            )}

            {/* 创建状态不需要任何按钮 */}
            {changeOrder.status === "creating" && !canAutoSave() && (
              <div className="text-sm text-muted-foreground">填写必要信息后将自动保存为草稿</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
