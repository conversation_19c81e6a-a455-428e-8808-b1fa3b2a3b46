# 项目愿景与路线图

## 业务目标与核心流程

**目标**: 开发一个档案流管理系统，支持用户上传 PDF 文件并指定预归档物理盒号，后台自动处理（分割、提取统一编号），与现有档案台账系统关联，最终完成物理归档。

**核心流程 (v3 - 加入严格预检查)**:

1. **上传**: 用户通过 `PDFUploadView` 上传 PDF，指定盒号 (`assigned_box_number`)。
2. **保存与任务创建**: `UploadService` 验证保存文件，创建 `UploadedFile` 和 `ProcessingTask` 记录。
3. **触发独立任务**: `TaskService` 将 `task_id` 提交给 Celery 队列。
4. **Celery 并行处理**: Worker 独立执行 `process_pdf_task(task_id)`。
5. **信息提取**: 任务内部调用 `PdfProcessingService` 获取分割点和统一编号。
6. **严格预检查**: 检查编号识别完整性 和 数据库记录存在性。失败则中止。
7. **物理分割与归档** (预检查通过时): 计算页面范围，写入临时文件，调用 `FileStorageService` 归档。
8. **关联与状态更新** (预检查通过时): 调用 `record_update_service` 更新数据库记录状态、URL、盒号。
9. **状态查询与重试**: 用户通过 API 查询任务状态/结果，并可重试失败任务。

## 核心架构决策

* **前后端分离**: (隐含，通过 API 交互)
* **异步任务处理**: 使用 Celery 处理耗时的 PDF 操作，分离上传请求和后台处理。
* **任务并行化**: 每个上传的 PDF 触发一个独立的 Celery 任务，依靠 Celery Worker 实现任务间的并行处理。
* **服务化拆分**: 将核心逻辑拆分为独立的服务（如 `PdfProcessingService`, `FileStorageService`, `RecordUpdateService` 等）和工具模块 (`utils/`)。
* **严格预检查**: 在执行文件操作前强制进行数据完整性校验，确保数据有效性。

## 高层路线图与阶段重点

*(注意: 此处为高层规划，具体任务和迭代计划见 `ai_dev_checkpoint.md`)*

**已完成主要阶段:**

* **Phase 1: 基础搭建与核心模型**
  * 项目结构与配置
  * 核心模型定义 (`UploadedFile`, `ProcessingTask`)
  * 基础上传与任务创建流程
* **Phase 2: PDF 处理器重构** (已完成 ≈ 98%)
  * 完成核心逻辑的服务化和工具化拆分 (`PdfProcessingService`, `utils` 等)
  * 实现严格预检查逻辑
  * 完成文件归档与记录更新基础服务
  * 完善测试覆盖与环境配置
  * (参考 `completed_feature_plans/feature_plan_pdf_refactoring.md` 获取详细历史)

**当前阶段 (活跃):**

* **Phase 3: API 与用户交互完善**
  * **战略重点**: 提供完整的用户交互 API，使用户能够有效管理上传、监控进度、获取结果并处理失败情况。
  * **主要目标**:
    * 完善文件上传 API (`PDFUploadView`)
    * 实现任务状态查询 API (`TaskStatusView`)
    * 实现处理结果查询/下载 API (`ProcessingResultView`)
    * 实现任务重试 API (`RetryProcessingView`)
    * (详细计划见 `active_feature_plans/` 下对应计划，或 `ai_dev_checkpoint.md` 中的当前任务)

**未来规划 (待定/后续阶段):**

* **Phase 4: 权限系统与高级功能**
  * 实现精细化的用户权限管理
  * 提供下载管理 API
  * Excel 数据导入/导出功能完善
  * 报告发放模块 (`report_issuing`) 功能集成与完善
* **Phase 5: 性能优化与运维**
  * 大型 PDF 处理效率提升 (如考虑 Celery Chord 实现 PDF 内部并行化)
  * Celery 配置与监控优化
  * 错误处理机制审视与加固
  * 备份与恢复策略
* **Phase 6: 前端集成 (初步)**
  * 与前端框架（如 AGgrid）进行初步对接，展示台账数据

## ⚠️ 已知主要风险/挑战 (宏观)

* **PDF 处理健壮性**: 对各种异常 PDF 格式的处理能力和准确率需持续验证。
* **测试覆盖广度**: 需持续扩展测试，覆盖更多边缘场景。
* **性能瓶颈**: 处理大量或大型 PDF 文件可能存在的性能问题。
* **Celery 复杂度**: (若实施 Chord 等高级特性) 可能引入的维护和调试复杂性。
