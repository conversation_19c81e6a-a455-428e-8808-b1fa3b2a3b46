"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter } from "lucide-react"
import { useState } from "react"
import { DateRangePicker } from "../../ui/date-range-picker"

export function ReportsFilter() {
  const [searchTerm, setSearchTerm] = useState("")
  const [status, setStatus] = useState("")

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索发放单号、项目名称..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="creating">创建中</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="locked">已锁定</SelectItem>
            <SelectItem value="issued">已发放</SelectItem>
            <SelectItem value="deleted">已删除</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
          <span className="sr-only">高级筛选</span>
        </Button>
      </div>
      <div className="flex flex-col sm:flex-row gap-4">
        <DateRangePicker />
        <Select>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="领取部门" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部部门</SelectItem>
            <SelectItem value="dept1">技术部</SelectItem>
            <SelectItem value="dept2">工程部</SelectItem>
            <SelectItem value="dept3">质量部</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
