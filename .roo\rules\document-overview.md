---
description: 用户Prompt示例"刚才讨论的内容更新一下文档吧。" 或 "同步一下项目状态。"当接收到一般性的文档更新请求时，此规则指导 AI 回忆项目关键文档及其核心职责，以便向用户提出更具体的澄清问题。
globs: 
alwaysApply: false
---
# Rules：文档概览与定位 (document-overview)

描述：(*当接收到一般性的文档更新请求时，此规则指导 AI 回忆项目关键文档及其核心职责，以便主动提议具体的更新操作，或在提议被否定时向用户寻求澄清。同时也提醒理解各文档作为 AI 状态记录的重要性*)

用户指令Prompt示例 (可能触发此规则辅助判断): (*"刚才讨论的内容更新一下文档吧。" 或 "同步一下项目状态。"*)

@rules document-overview

当用户发出模糊的文档更新指令时，AI 代理应遵循以下流程：

1. **回顾文档列表**: 参考以下项目关键文档列表及其核心职责：
    * **@AgentReadme/AgentReadme.md**: 项目入口，定义文档结构和协作流程。(*通常不由 AI 更新*)
    * **@AgentReadme/planning_and_requirements/project_vision_and_roadmap.md**: 项目愿景、高层目标和长期路线图。(*低频更新，通常由用户要求进行变更*)
    * **@AgentReadme/planning_and_requirements/project_requirements.md**: 详细的功能和业务需求。(*需求变更时更新，通常由用户要求进行变更*)
    * **@AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md**: 核心计划文档，包含区域一至六，跟踪任务、历史、风险等。(*各区域更新频率不同*)
    * **@AgentReadme/planning_and_requirements/ai_dev_checkpoint.md**: 迭代工作日志，记录最新进展、问题和微观下一步计划。**(高频更新，AI 的短期记忆/状态检查点)**
    * **@AgentReadme/active_feature_plans/**: 当前活跃特性的详细计划目录。(*低频更新，通常由用户要求进行变更*)
    * **@AgentReadme/guides/testing_guide.md** (及其他指南): 特定流程的操作指南。(*低频更新*)
    * **@AgentReadme/testing_logs/**: 测试与 Debug 过程记录目录。(*测试/Debug 时更新*)

2. **结合上下文优先提议**:
    * 基于当前对话上下文和正在处理的任务，**主动提议**对最可能相关的 1-2 个文档进行**具体的、合理的**更新操作。
    * 提议应清晰说明**要更新哪个文件**以及**建议更新的内容**。
    * **特别注意**: 如果提议的更新涉及到 @AgentReadme/planning_and_requirements/ai_dev_checkpoint.md，应遵循 `@commit-and-update-docs` 流程中定义的步骤，确保文档更新和代码提交的协调一致。

3. **获取用户确认**:
    * 等待用户对提议的确认。
    * 如果用户同意，则继续执行提议的更新。

4. **处理用户否定或不确定**:
    * 如果用户否定了提议或表示不确定，AI 可以进一步询问以澄清："好的，那么您具体希望更新哪个方面的内容？是关于任务状态 (@...detailed_work_plan_and_log.md)，迭代进展记录 (@...ai_dev_checkpoint.md - AI 的状态检查点)，还是其他的规划文件？"
