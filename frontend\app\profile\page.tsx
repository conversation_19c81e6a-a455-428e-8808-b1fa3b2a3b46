"use client"

import type React from "react"

import { useState } from "react"
import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Save, Key } from "lucide-react"
// TODO: Replace with NextAuth imports


export default function ProfilePage() {
    // TODO: Replace with useSession() hook
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)

  const [profile, setProfile] = useState({
    name: "用户",
    email: "<EMAIL>",
    phone: "",
    department: "",
    position: "",
  })

  const [passwords, setPasswords] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  const handleProfileChange = (field: string, value: string) => {
    setProfile((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handlePasswordChange = (field: string, value: string) => {
    setPasswords((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsUpdating(true)
    try {
      // TODO: Implement profile update with NextAuth
      await new Promise((resolve) => setTimeout(resolve, 1000))
      toast({
        title: "更新成功",
        description: "个人资料已更新",
      })
    } catch (error) {
      toast({
        title: "更新失败",
        description: "更新个人资料时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault()
    if (passwords.newPassword !== passwords.confirmPassword) {
      toast({
        title: "密码不匹配",
        description: "新密码和确认密码不一致",
        variant: "destructive",
      })
      return
    }
    
    setIsChangingPassword(true)
    try {
      // TODO: Implement password change with NextAuth
      await new Promise((resolve) => setTimeout(resolve, 1000))
      toast({
        title: "更改成功",
        description: "密码已更改",
      })
      setPasswords({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      })
    } catch (error) {
      toast({
        title: "更改失败",
        description: "更改密码时发生错误，请确认当前密码是否正确",
        variant: "destructive",
      })
    } finally {
      setIsChangingPassword(false)
    }
  }

  return (
    <div className="space-y-6">
      <PageTitle title="个人资料" subtitle="查看和更新您的个人信息" />

      <div className="grid gap-6 md:grid-cols-5">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>个人信息</CardTitle>
            <CardDescription>您的基本信息和头像</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            <Avatar className="h-32 w-32">
              {/* TODO: Replace with NextAuth session.user.image */}
              <AvatarImage src="https://placeholder.svg?key=yfxsp" alt="用户头像" />
              <AvatarFallback>用</AvatarFallback>
            </Avatar>
            <div className="text-center">
              {/* TODO: Replace with NextAuth session.user.name */}
              <h3 className="text-lg font-medium">{profile.name}</h3>
              <p className="text-sm text-muted-foreground">角色未知</p>
            </div>
            <Button variant="outline" size="sm">
              更换头像
            </Button>
          </CardContent>
        </Card>

        <Card className="md:col-span-3">
          <Tabs defaultValue="profile">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>账户设置</CardTitle>
                <TabsList>
                  <TabsTrigger value="profile">个人资料</TabsTrigger>
                  <TabsTrigger value="password">修改密码</TabsTrigger>
                </TabsList>
              </div>
              <CardDescription>管理您的账户信息和安全设置</CardDescription>
            </CardHeader>
            <CardContent>
              <TabsContent value="profile" className="space-y-4">
                <form onSubmit={handleUpdateProfile}>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="name">姓名</Label>
                      <Input
                        id="name"
                        value={profile.name}
                        onChange={(e) => handleProfileChange("name", e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="email">邮箱</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => handleProfileChange("email", e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="phone">手机号码</Label>
                      <Input
                        id="phone"
                        value={profile.phone}
                        onChange={(e) => handleProfileChange("phone", e.target.value)}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="department">部门</Label>
                        <Input
                          id="department"
                          value={profile.department}
                          onChange={(e) => handleProfileChange("department", e.target.value)}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="position">职位</Label>
                        <Input
                          id="position"
                          value={profile.position}
                          onChange={(e) => handleProfileChange("position", e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mt-6 flex justify-end">
                    <Button type="submit" disabled={isUpdating}>
                      {isUpdating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          更新中...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          保存更改
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>
              <TabsContent value="password" className="space-y-4">
                <form onSubmit={handleChangePassword}>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="current-password">当前密码</Label>
                      <Input
                        id="current-password"
                        type="password"
                        value={passwords.currentPassword}
                        onChange={(e) => handlePasswordChange("currentPassword", e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="new-password">新密码</Label>
                      <Input
                        id="new-password"
                        type="password"
                        value={passwords.newPassword}
                        onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="confirm-password">确认新密码</Label>
                      <Input
                        id="confirm-password"
                        type="password"
                        value={passwords.confirmPassword}
                        onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="mt-6 flex justify-end">
                    <Button type="submit" disabled={isChangingPassword}>
                      {isChangingPassword ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          更改中...
                        </>
                      ) : (
                        <>
                          <Key className="mr-2 h-4 w-4" />
                          更改密码
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>登录历史</CardTitle>
          <CardDescription>您最近的登录记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { time: "2023-12-20 10:30", ip: "***********", location: "北京", device: "Chrome / Windows" },
              { time: "2023-12-18 09:20", ip: "***********", location: "北京", device: "Chrome / Windows" },
              { time: "2023-12-15 14:45", ip: "***********", location: "北京", device: "Safari / iOS" },
            ].map((login, index) => (
              <div key={index} className="flex items-center justify-between border-b pb-3 last:border-0 last:pb-0">
                <div>
                  <p className="text-sm font-medium">{login.time}</p>
                  <p className="text-xs text-muted-foreground">
                    IP: {login.ip} · 位置: {login.location}
                  </p>
                </div>
                <div className="text-sm text-muted-foreground">{login.device}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
