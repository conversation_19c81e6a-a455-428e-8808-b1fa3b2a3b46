from .base_handler import BaseStateHandler, InvalidTransitionError
from report_issuing.states import IssueFormState
import logging

logger = logging.getLogger(__name__)

class IssuedHandler(BaseStateHandler):
    """处理已归档状态下的发放单操作。"""

    def soft_delete(self, reason: str):
        """
        在已归档状态下，允许软删除发放单，将其状态转换为 DELETED。
        
        Args:
            reason (str): 软删除的原因。
        """
        logger.info(f"软删除已归档的发放单 {self.form.id}，原因: {reason}。状态将从 ISSUED 变为 DELETED。")
        # TODO: [P1] 用户将在此处补充具体的软删除逻辑
        # 1. 检查是否有权限执行软删除
        # 2. 调用数据服务更新状态为 DELETED，并记录删除原因和操作人
        # 3. 注意：此操作不应恢复已扣减的档案发放数量，仅作废发放单本身
        # 4. 调用审计服务
        pass 