#!/usr/bin/env python3
"""
简化的任务路由测试脚本

只测试任务路由配置，不访问数据库
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_flow_manager.celery import app

def test_task_routing():
    """测试任务路由配置"""
    print("=" * 60)
    print("任务路由配置测试")
    print("=" * 60)
    
    # 测试任务列表（使用完整的模块路径）
    test_tasks = [
        # PDF处理任务
        'archive_processing.tasks.core_tasks.process_pdf_serial_task',
        'archive_processing.tasks.core_tasks.process_pdf_parallel_task',
        'archive_processing.tasks.core_tasks.process_pdf_ocr_task',
        'archive_processing.tasks.core_tasks.aggregate_pdf_ocr_results_task',
        'archive_processing.tasks.core_tasks.process_pdf_with_ocr_results_task',
        'archive_processing.tasks.core_tasks.process_pdf_three_phase_coordinator_task',
        
        # 清理任务
        'archive_processing.tasks.core_tasks.cleanup_expired_files_task',
        'archive_processing.tasks.cleanup.periodic_cleanup_deleted_files',
        'archive_processing.tasks.cleanup.cleanup_stuck_tasks',
        
        # 其他任务
        'archive_records.tasks.process_finalized_sessions_task',
        'some.unknown.task',  # 测试未知任务
    ]
    
    print("\n📋 任务路由测试结果:")
    print("-" * 60)
    
    pdf_tasks = []
    default_tasks = []
    
    for task_name in test_tasks:
        try:
            # 获取任务的路由信息
            route = app.conf.task_routes.get(task_name)
            
            # 如果没有直接匹配，检查模式匹配
            if not route:
                for pattern, route_config in app.conf.task_routes.items():
                    if '*' in pattern:
                        # 简单的通配符匹配
                        pattern_prefix = pattern.replace('*', '')
                        if task_name.startswith(pattern_prefix):
                            route = route_config
                            break
            
            if route:
                queue = route.get('queue', 'default')
                print(f"✅ {task_name}")
                print(f"   -> 队列: {queue}")
                
                if queue == 'pdf_processing':
                    pdf_tasks.append(task_name)
                else:
                    default_tasks.append(task_name)
            else:
                print(f"⚠️  {task_name}")
                print(f"   -> 队列: default (默认)")
                default_tasks.append(task_name)
                
        except Exception as e:
            print(f"❌ {task_name}")
            print(f"   -> 错误: {e}")
    
    print("\n" + "=" * 60)
    print("📊 路由统计:")
    print("-" * 60)
    print(f"📁 pdf_processing 队列: {len(pdf_tasks)} 个任务")
    for task in pdf_tasks:
        print(f"   - {task}")
    
    print(f"\n📁 default 队列: {len(default_tasks)} 个任务")
    for task in default_tasks:
        print(f"   - {task}")
    
    print("\n" + "=" * 60)

def test_manual_task_dispatch():
    """测试手动任务分发"""
    print("\n🚀 手动任务分发测试:")
    print("-" * 60)
    
    try:
        # 尝试导入PDF处理任务
        from archive_processing.tasks.core_tasks import process_pdf_serial_task
        print("✅ 成功导入 process_pdf_serial_task")
        
        # 检查任务的队列配置
        task_name = process_pdf_serial_task.name
        print(f"📝 任务名称: {task_name}")
        
        # 获取路由配置
        route = app.conf.task_routes.get(task_name)
        if route:
            queue = route.get('queue', 'default')
            print(f"📋 配置的队列: {queue}")
        else:
            print("⚠️ 未找到路由配置，将使用默认队列")
        
        # 模拟任务分发（不实际执行）
        print("\n🔧 模拟任务分发:")
        print("   任务将被发送到 pdf_processing 队列")
        print("   PDF worker 应该能够接收到这个任务")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def check_worker_queues():
    """检查worker队列配置"""
    print("\n🔧 Worker队列配置检查:")
    print("-" * 60)
    
    try:
        # 检查活跃的worker
        inspect = app.control.inspect()
        active_queues = inspect.active_queues()
        
        if active_queues:
            print("✅ 发现活跃的worker队列:")
            for worker_name, queues in active_queues.items():
                print(f"  🔧 {worker_name}:")
                for queue in queues:
                    queue_name = queue.get('name', 'Unknown')
                    print(f"     - 队列: {queue_name}")
                    
                    # 检查是否是PDF处理队列
                    if queue_name == 'pdf_processing':
                        print("       ✅ 这是PDF处理专用队列")
                    elif queue_name == 'default':
                        print("       ✅ 这是默认队列")
        else:
            print("❌ 没有发现活跃的worker")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始简化的任务路由测试...\n")
    
    # 测试任务路由配置
    test_task_routing()
    
    # 检查worker队列
    check_worker_queues()
    
    # 测试手动任务分发
    test_manual_task_dispatch()
    
    print("\n💡 结论:")
    print("如果看到 '✅ 任务路由配置正确' 和 '✅ PDF处理专用队列'，")
    print("说明PDF任务应该能够正确路由到PDF worker。")
    print("\n🔍 如果PDF worker仍然没有收到任务，请检查:")
    print("1. 是否有实际的PDF处理任务被触发")
    print("2. 前端是否正确调用了PDF处理API")
    print("3. ProcessingTask表中是否有pending状态的记录")

if __name__ == '__main__':
    main()
