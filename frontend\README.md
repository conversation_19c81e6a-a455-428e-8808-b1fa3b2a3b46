# 档案管理系统 - 前端

一个基于 Next.js 的现代化档案管理系统前端应用，提供直观的用户界面和丰富的功能模块。

## 🚀 技术栈

- **框架**: Next.js 14+ (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: 自定义组件库
- **状态管理**: React Context + Custom Hooks
- **HTTP客户端**: Fetch API + 自定义封装
- **表单处理**: React Hook Form
- **文件上传**: 原生 File API
- **认证**: JWT Token
- **开发工具**: ESLint, Prettier

## 📁 项目结构

```文件组织结构
frontend/
├── app/                          # Next.js App Router 页面
│   ├── api/                      # API路由
│   ├── archive/                  # 档案管理页面
│   ├── records/                  # 记录管理页面
│   ├── reports/                  # 报告管理页面
│   ├── change-orders/            # 变更单管理页面
│   ├── login/                    # 登录页面
│   ├── dashboard/                # 仪表板
│   ├── users/                    # 用户管理
│   ├── settings/                 # 系统设置
│   └── layout.tsx                # 根布局
├── components/                   # 可复用组件
│   ├── domain/                   # 业务领域组件
│   │   ├── records/              # 记录相关组件
│   │   ├── archive/              # 档案相关组件
│   │   └── reports/              # 报告相关组件
│   ├── shared/                   # 跨领域共享组件
│   └── ui/                       # 基础UI组件
├── hooks/                        # 自定义Hooks
│   ├── domain/                   # 业务领域Hooks
│   ├── shared/                   # 跨领域共享Hooks
│   └── ui/                       # UI相关Hooks
├── services/                     # 服务层
│   ├── domain/                   # 业务领域服务
│   │   ├── records/              # 记录相关服务
│   │   ├── auth/                 # 认证相关服务
│   │   └── reports/              # 报告相关服务
│   ├── shared/                   # 跨领域基础服务
│   ├── infrastructure/           # 基础设施服务
│   └── index.ts                  # 统一导出
├── contexts/                     # React Context
│   ├── domain/                   # 业务领域状态
│   ├── shared/                   # 全局共享状态
│   └── ui/                       # UI状态管理
├── utils/                        # 工具函数
├── styles/                       # 样式文件
├── config/                       # 配置文件
└── public/                       # 静态资源
```

## 🛠️ 开发环境设置

### 前置要求

- Node.js 18.0+
- npm 或 yarn
- 后端API服务运行在 `http://localhost:8000`

### 安装依赖

```bash
cd frontend
pnpm install
```

### 环境配置

创建 `.env.local` 文件：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# 应用配置
NEXT_PUBLIC_APP_NAME=档案管理系统
NEXT_PUBLIC_APP_VERSION=1.0.0

# 开发模式配置
NODE_ENV=development
```

### 启动开发服务器

```bash
pnpm run dev
```

访问 `http://localhost:3000` 查看应用。

## 📋 主要功能模块

### 🗂️ 档案管理 (Archive)

- 档案列表查看和搜索
- 档案详情查看
- PDF文档导入和处理
- 档案分类和标签管理

### 📊 记录管理 (Records)

- Excel批量导入记录
- 记录列表和详情查看
- 导入历史追踪
- 冲突解决和数据验证

### 📈 报告管理 (Reports)

- 报告生成和查看
- 报告模板管理
- 数据导出功能

### 📝 变更单管理 (Change Orders)

- 变更单创建和审批
- 变更流程跟踪
- 变更历史记录

### 👥 用户管理 (Users)

- 用户账户管理
- 权限和角色分配
- 用户活动日志

## 🔧 开发指南

### 代码规范

项目采用现代前端架构模式，遵循以下原则：

1. **分层架构**: domain/shared/ui 三层组织
2. **关注点分离**: 组件、服务、状态管理分离
3. **类型安全**: 全面使用 TypeScript
4. **代码复用**: 通过 hooks 和 services 实现逻辑复用

### 组件开发

```typescript
// 示例：业务组件
// components/domain/records/RecordsList.tsx
import { useRecords } from '@/hooks/domain/records';
import { recordsService } from '@/services';

export function RecordsList() {
  const { records, loading } = useRecords();
  
  return (
    <div className="records-list">
      {/* 组件内容 */}
    </div>
  );
}
```

### 服务调用

```typescript
// 示例：使用服务
import { excelImportService, type ExcelImportOptions } from '@/services';

const handleImport = async (file: File) => {
  const options: ExcelImportOptions = { sheet_name: 0 };
  const result = await excelImportService.analyzeExcelFile(file, options);
  // 处理结果
};
```

### 状态管理

```typescript
// 示例：使用Context
import { useAuth } from '@/contexts/domain/auth';

export function UserProfile() {
  const { user, logout } = useAuth();
  
  return (
    <div>
      <span>欢迎, {user?.username}</span>
      <button onClick={logout}>退出</button>
    </div>
  );
}
```

## 🧪 测试

```bash
# 运行单元测试
pnpm run test

# 运行测试覆盖率
pnpm run test:coverage

# 运行E2E测试
pnpm run test:e2e
```

## 📦 构建和部署

### 开发构建

```bash
pnpm run build
```

### 生产部署

```bash
# 构建生产版本
pnpm run build

# 启动生产服务器
pnpm start
```

### Docker部署

```bash
# 构建Docker镜像
docker build -t archive-frontend .

# 运行容器
docker run -p 3000:3000 archive-frontend
```

## 🔍 调试指南

### 开发工具

- **React Developer Tools**: 调试React组件
- **Network Tab**: 监控API请求
- **Console**: 查看日志输出

### 常见问题

1. **API连接失败**
   - 检查后端服务是否运行
   - 验证API_BASE_URL配置

2. **认证问题**
   - 检查JWT Token是否有效
   - 确认登录状态

3. **文件上传失败**
   - 检查文件大小限制
   - 验证文件格式支持

## 📚 相关文档

- [Next.js 官方文档](https://nextjs.org/docs)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [React Hook Form 文档](https://react-hook-form.com/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
