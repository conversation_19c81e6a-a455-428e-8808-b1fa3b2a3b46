# 项目函数汇总


## `archive_processing/dto/result_dtos.py`

*   **`__str__(self)`** (方法, 行: 13)
    ```
    无文档字符串
    ```

## `archive_processing/models.py`

*   **`__str__(self)`** (方法, 行: 33)
    ```
    无文档字符串
    ```
*   **`__str__(self)`** (方法, 行: 76)
    ```
    无文档字符串
    ```

## `archive_processing/serializers.py`

*   **`validate_file(self, value)`** (方法, 行: 16)
    ```
    验证上传的文件是否有效 (委托给 UploadService)。
    ```
*   **`validate_assigned_box_number(self, value)`** (方法, 行: 32)
    ```
    验证分配的档案盒号格式 (如果需要特定格式)。
    ```

## `archive_processing/services/file_storage_service.py`

*   **`generate_archive_filename(unified_number, original_path, timestamp)`** (函数, 行: 14)
    ```
    生成归档文件的规范化文件名。

优先使用统一编号，如果提供的话。否则，可以基于原始文件名或时间戳。

Args:
    unified_number (str, optional): 文件的统一编号。
    original_path (str, optional): 原始文件的路径，用于提取文件名和扩展名。
    timestamp (int, optional): 文件处理的时间戳。
    
Returns:
    str: 生成的文件名。
    ```
*   **`generate_archive_storage_path(unified_number)`** (函数, 行: 61)
    ```
    根据统一编号生成存档路径

Args:
    unified_number: 统一编号
    
Returns:
    存档路径
    ```
*   **`get_archive_storage_path(unified_number)`** (函数, 行: 118)
    ```
    获取档案存储路径，按年月组织

Args:
    unified_number: 统一编号用于生成子目录
    
Returns:
    完整存储路径
    ```
*   **`generate_file_hash(file_path)`** (函数, 行: 155)
    ```
    生成文件哈希值，用于唯一标识和验证

Args:
    file_path: 文件路径
    
Returns:
    SHA-256哈希值
    ```
*   **`ensure_directory_exists(directory_path)`** (函数, 行: 179)
    ```
    确保目录存在，如果不存在则创建

Args:
    directory_path: 目录路径
    
Returns:
    bool: 目录是否存在或创建成功
    ```
*   **`get_temp_directory()`** (函数, 行: 196)
    ```
    获取临时目录，用于处理过程中的临时文件存储

Returns:
    Path: 临时目录路径
    ```
*   **`backup_archive_file(file_path, backup_type)`** (函数, 行: 221)
    ```
    备份档案文件到按时间组织的备份目录

Args:
    file_path: 要备份的文件路径
    backup_type: 备份周期类型，可选值："daily", "weekly", "monthly"
    
Returns:
    Dict[str, Any]: 包含备份信息的字典，失败时返回None
    ```
*   **`verify_file_integrity(file_path, original_hash)`** (函数, 行: 305)
    ```
    验证文件完整性，通过哈希值比较

Args:
    file_path: 文件路径
    original_hash: 原始哈希值，如果不提供则认为文件完整
    
Returns:
    bool: 文件是否完整
    ```
*   **`get_backup_locations(file_path, backup_type, days_to_check)`** (函数, 行: 335)
    ```
    获取文件的备份位置列表，或根据备份类型获取最近的备份目录列表

Args:
    file_path: 文件路径（可选），如果提供将搜索此文件的备份
    backup_type: 备份类型（daily, weekly, monthly）
    days_to_check: 检查最近几天的备份
    
Returns:
    List[str]: 备份路径列表
    ```

## `archive_processing/services/pdf_processing_service.py`

*   **`__init__(self, config: Optional[Dict])`** (方法, 行: 28)
    ```
    初始化服务，加载配置。
    ```
*   **`process_pdf_for_splitting_info(self, pdf_path: str, target_text: str) -> ProcessingResultDto`** (方法, 行: 47)
    ```
    处理 PDF，查找分割点并识别关联的统一编号。
返回包含处理结果的 DTO，但不执行物理文件分割。

Args:
    pdf_path: 要处理的 PDF 文件路径。
    target_text: 用于查找分割点的目标文本。

Returns:
    ProcessingResultDto: 包含处理结果的数据对象。
    ```
*   **`_find_split_points(self, doc: fitz.Document, target_text: str) -> Tuple[...]`** (方法, 行: 149)
    ```
    查找PDF中包含目标文本的页面，并聚合识别结果和统计。

Args:
    doc: PDF文档对象
    target_text: 目标文本

Returns:
    元组: (all_split_points, all_unified_numbers, all_recognition_stats, 
          total_processed_pages, total_matched_pages)
    ```
*   **`_process_page_batch(self, doc: fitz.Document, start_page: int, end_page: int, processed_target: str) -> Tuple[...]`** (方法, 行: 216)
    ```
    处理一批PDF页面，返回批次结果（分割点、编号、统计）和处理统计。

Args:
    doc: PDF文档对象
    start_page: 开始页码
    end_page: 结束页码
    processed_target: 处理后的目标文本

Returns:
    元组: (batch_split_points, batch_unified_numbers, batch_recognition_stats, 
          processed_in_batch, matched_in_batch)
    ```
*   **`_enhance_unified_number_recognition(self, page_num: int, original_ocr: str, original_image: Image.Image, target_text: str) -> Tuple[...]`** (方法, 行: 333)
    ```
    增强统一编号识别，返回最佳编号和统计增量。
    ```
*   **`_get_page_crop(self, doc: fitz.Document, page_num: int) -> Optional[PDFPageImage]`** (方法, 行: 384)
    ```
    获取PDF页面的裁剪区域图像 (基本不变)
    ```
*   **`_perform_deep_detection(self, image: Image.Image, target_text: str) -> bool`** (方法, 行: 418)
    ```
    执行深度检测 (基本不变，调用 ocr_utils)
    ```

## `archive_processing/services/progress_tracking_service.py`

*   **`create(cls, task_id: str, total_steps: int, message: str, timeout: int) -> Dict[...]`** (方法, 行: 29)
    ```
    创建进度追踪器

Args:
    task_id: 任务ID
    total_steps: 总步骤数
    message: 初始进度消息
    timeout: 缓存超时时间(秒)
    
Returns:
    进度追踪器实例
    ```
*   **`update(cls, task_id: str, completed_steps: int, message: Optional[str], status: Optional[str], timeout: int) -> Optional[...]`** (方法, 行: 65)
    ```
    更新任务进度

Args:
    task_id: 任务ID
    completed_steps: 已完成步骤数
    message: 进度消息
    status: 任务状态
    timeout: 缓存超时时间(秒)
    
Returns:
    更新后的进度信息，如果任务不存在则返回None
    ```
*   **`get(cls, task_id: str) -> Optional[...]`** (方法, 行: 134)
    ```
    获取任务进度

Args:
    task_id: 任务ID
    
Returns:
    进度信息，如果任务不存在则返回None
    ```
*   **`complete(cls, task_id: str, message: str, result_data: Optional[Dict]) -> Optional[...]`** (方法, 行: 156)
    ```
    将任务标记为已完成

Args:
    task_id: 任务ID
    message: 完成消息
    result_data: 结果数据
    
Returns:
    更新后的进度信息
    ```
*   **`fail(cls, task_id: str, error_message: str, error_details: Optional[Dict]) -> Optional[...]`** (方法, 行: 191)
    ```
    将任务标记为失败

Args:
    task_id: 任务ID
    error_message: 错误消息
    error_details: 错误详情
    
Returns:
    更新后的进度信息
    ```
*   **`delete(cls, task_id: str) -> bool`** (方法, 行: 226)
    ```
    删除任务进度

Args:
    task_id: 任务ID
    
Returns:
    是否成功删除
    ```
*   **`_get_cache_key(cls, task_id: str) -> str`** (方法, 行: 247)
    ```
    生成缓存键名
    ```
*   **`create_progress_tracker(task_id, total_steps)`** (函数, 行: 253)
    ```
    创建进度追踪器（向后兼容接口）

Args:
    task_id: 任务ID
    total_steps: 总步骤数
    
Returns:
    进度追踪器实例
    ```
*   **`update_progress(task_id, completed_steps, message, status)`** (函数, 行: 267)
    ```
    更新任务进度（向后兼容接口）

Args:
    task_id: 任务ID
    completed_steps: 已完成步骤数
    message: 进度消息
    status: 任务状态
    
Returns:
    更新后的进度信息
    ```
*   **`get_task_progress(task_id)`** (函数, 行: 283)
    ```
    获取任务进度（向后兼容接口）

Args:
    task_id: 任务ID
    
Returns:
    进度信息
    ```
*   **`send_email_notification(cls, user_id: int, subject: str, message: str) -> Dict[...]`** (方法, 行: 300)
    ```
    发送邮件通知

Args:
    user_id: 用户ID
    subject: 邮件主题
    message: 邮件内容
    
Returns:
    发送结果
    ```
*   **`send_completion_notification(cls, user_id: int, task_id: str, task_info: Dict[...]) -> Dict[...]`** (方法, 行: 341)
    ```
    发送任务完成通知

Args:
    user_id: 用户ID
    task_id: 任务ID
    task_info: 任务信息
    
Returns:
    发送结果
    ```
*   **`send_in_app_notification(cls, user_id: int, notification_type: str, message: str, data: Optional[Dict]) -> Dict[...]`** (方法, 行: 372)
    ```
    发送站内消息通知

Args:
    user_id: 用户ID
    notification_type: 通知类型
    message: 通知内容
    data: 附加数据
    
Returns:
    发送结果
    ```
*   **`send_completion_notification(user_id, task_info)`** (函数, 行: 400)
    ```
    发送完成通知（向后兼容接口）

Args:
    user_id: 用户ID
    task_info: 任务信息
    
Returns:
    发送结果
    ```
*   **`send_task_notification(user_id, notification_type, message, data)`** (函数, 行: 415)
    ```
    发送任务通知（向后兼容接口）

Args:
    user_id: 用户ID
    notification_type: 通知类型
    message: 通知内容
    data: 附加数据
    
Returns:
    发送结果
    ```

## `archive_processing/services/record_update_service.py`

*   **`generate_file_url(file_path: Optional[str]) -> Optional[str]`** (函数, 行: 15)
    ```
    根据文件路径生成可访问的URL (示例)

Args:
    file_path (Optional[str]): 文件的绝对或相对路径。

Returns:
    Optional[str]: 生成的URL或None。
    ```
*   **`update_archive_record(unified_number: str, file_path: str, user_id: int, assigned_box_number: Optional[str])`** (函数, 行: 73)
    ```
    更新单个档案记录的信息（状态、URL、盒号等）。

Args:
    unified_number (str): 统一编号。
    file_path (str): 处理后文件的最终存储路径。
    user_id (int): 执行操作的用户ID。
    assigned_box_number (Optional[str]): 本次任务分配的盒号。

Returns:
    dict: 包含更新结果的字典，例如:
          {'success': True, 'record_id': ..., 'archive_url': ..., 'assigned_box_number': ...}
          {'success': False, 'status': 'not_found', 'error': ...}
          {'success': False, 'error': ...}
    ```
*   **`check_records_exist(unified_numbers: List[str]) -> List[str]`** (函数, 行: 151)
    ```
    检查提供的统一编号列表中，哪些在 ArchiveRecord 表中不存在。

Args:
    unified_numbers (List[str]): 需要检查的统一编号列表。

Returns:
    List[str]: 不存在于数据库中的统一编号列表。
              如果所有编号都存在，则返回空列表。
    ```

## `archive_processing/services/security_service.py`

*   **`create_secure_access_token(file_path: str, user_id: int, expires_in: int) -> str`** (函数, 行: 12)
    ```
    创建安全访问令牌，用于控制文件访问权限

Args:
    file_path: 文件路径
    user_id: 用户ID
    expires_in: 有效期(秒)，默认24小时
    
Returns:
    安全访问令牌
    ```
*   **`verify_access_token(token: str, file_path: Optional[str]) -> Tuple[...]`** (函数, 行: 58)
    ```
    验证访问令牌的有效性

Args:
    token: 访问令牌
    file_path: 文件路径（可选，用于额外验证）
    
Returns:
    Tuple[bool, Dict]: (是否有效, 令牌数据)
    ```
*   **`check_file_access_permission(file_path: str, user_id: int) -> bool`** (函数, 行: 110)
    ```
    检查用户是否有权限访问文件

Args:
    file_path: 文件路径
    user_id: 用户ID
    
Returns:
    bool: 是否有权限
    ```
*   **`generate_download_url(file_path: str, user_id: int, expires_in: int) -> str`** (函数, 行: 145)
    ```
    生成带有访问令牌的下载URL

Args:
    file_path: 文件路径
    user_id: 用户ID
    expires_in: 有效期(秒)，默认1小时
    
Returns:
    下载URL
    ```

## `archive_processing/services/task_service.py`

*   **`create_task(uploaded_file: UploadedFile, task_type: str, params: dict, user_id: int) -> ProcessingTask`** (函数, 行: 18)
    ```
    创建新的处理任务并将其放入队列。

Args:
    uploaded_file: 关联的 UploadedFile 实例。
    task_type: 任务类型 (例如 'pdf_processing')。
    params: 任务需要的额外参数 (例如，分割依据文本)。
    user_id: 发起任务的用户ID (可选)。

Returns:
    ProcessingTask: 创建的任务记录实例。

Raises:
    Exception: 如果任务创建或入队失败。
    ```
*   **`get_task_status(task_id: uuid.UUID)`** (函数, 行: 71)
    ```
    获取指定任务的状态和进度。

Args:
    task_id: 要查询的任务ID。

Returns:
    dict: 包含任务状态、进度等信息的字典，如果找不到任务则返回 None。
    ```

## `archive_processing/services/upload_service.py`

*   **`validate_file(file_obj: DjangoUploadedFile)`** (函数, 行: 37)
    ```
    验证上传的文件类型和大小。

Args:
    file_obj: Django的UploadedFile对象。

Raises:
    ValidationError: 如果文件验证失败。
    ```
*   **`validate_pdf_content(file_path: str)`** (函数, 行: 89)
    ```
    验证已保存的文件是否为有效的 PDF 文档。

Args:
    file_path: 已保存的文件路径。

Raises:
    ValidationError: 如果文件不是有效的 PDF 文档。
    ```
*   **`calculate_file_hash(file_path: str) -> str`** (函数, 行: 118)
    ```
    计算文件的SHA256哈希值。

Args:
    file_path: 文件路径。
    
Returns:
    str: 文件的SHA256哈希值（16进制字符串）。
    
Raises:
    IOError: 如果无法读取文件。
    ```
*   **`save_uploaded_file(file_obj: DjangoUploadedFile, assigned_box_number: str, user_id: int) -> UploadedFile`** (函数, 行: 143)
    ```
    保存上传的文件到临时存储，并在数据库中创建记录。

Args:
    file_obj: Django的UploadedFile对象。
    assigned_box_number: 用户提供的档案盒号。
    user_id: 上传用户的ID (可选)。

Returns:
    UploadedFile: 创建的数据库记录实例。

Raises:
    ValidationError: 如果文件验证失败。
    IOError: 如果文件保存过程中发生错误。
    ```

## `archive_processing/tasks.py`

*   **`process_pdf_task(self, task_id)`** (方法, 行: 30)
    ```
    Celery 任务：异步处理上传的 PDF 文件，并关联档案记录 (Refactored)
    ```
*   **`cleanup_expired_files_task()`** (函数, 行: 408)
    ```
    清理过期临时文件的定期任务
    ```

## `archive_processing/utils/image_utils.py`

*   **`prepare_standard_image_for_tesseract(image: Image.Image) -> Image.Image`** (函数, 行: 22)
    ```
    为 Tesseract 准备标准的单个预处理图像 (灰度化 + Otsu二值化)

Args:
    image: 原始 PIL 图像对象
    
Returns:
    处理后的 PIL 图像对象 (二值化)
    ```
*   **`prepare_standard_image_for_paddle(image: Image.Image) -> Image.Image`** (函数, 行: 44)
    ```
    为 PaddleOCR 准备标准的单个预处理图像 (确保是 RGB)

Args:
    image: 原始 PIL 图像对象
    
Returns:
    处理后的 PIL 图像对象 (RGB)
    ```
*   **`get_enhanced_images_for_paddle(image: Image.Image) -> List[...]`** (函数, 行: 59)
    ```
    创建多个增强版本的 RGB 图像，用于 PaddleOCR 识别尝试

Args:
    image: 原始 RGB PIL 图像对象
        
Returns:
    处理后的 RGB PIL 图像列表
    ```
*   **`get_enhanced_images_for_tesseract(image: Image.Image) -> List[...]`** (函数, 行: 101)
    ```
    创建多个增强版本的灰度/二值化图像，用于 Tesseract 识别尝试

Args:
    image: 原始 PIL 图像对象
        
Returns:
    处理后的灰度/二值化 PIL 图像列表
    ```
*   **`image_hash(image: Image.Image) -> str`** (函数, 行: 156)
    ```
    生成图像的哈希值用于缓存

Args:
    image: 原始 PIL 图像对象
    
Returns:
    图像的哈希值字符串
    ```

## `archive_processing/utils/ocr_utils.py`

*   **`init_paddle_ocr(use_paddle: bool) -> Optional[PaddleOCR]`** (函数, 行: 44)
    ```
    初始化PaddleOCR引擎，如果可用且被请求。

Args:
    use_paddle: 是否尝试初始化PaddleOCR。

Returns:
    初始化的 PaddleOCR 引擎实例，如果不可用或初始化失败则返回 None。
    ```
*   **`run_tesseract_basic(image: Image.Image, config: str) -> str`** (函数, 行: 75)
    ```
    在单个预处理图像上运行 Tesseract OCR。

Args:
    image: 经过 Tesseract 优化的预处理图像 (PIL.Image 对象)。
    config: Tesseract 配置字符串。如果为 None，使用默认配置。

Returns:
    识别出的文本字符串，失败则返回空字符串。
    ```
*   **`run_tesseract_enhanced(images: List[...], configs: List[str]) -> List[str]`** (函数, 行: 100)
    ```
    在多个图像版本上运行 Tesseract OCR (用于增强识别)。

Args:
    images: 预处理后的图像列表 (PIL.Image 对象)。
    configs: 与图像列表对应的 Tesseract 配置字符串列表。
             如果为 None，将使用默认配置。

Returns:
    每个图像识别出的文本列表 (失败则为空字符串)。
    ```
*   **`run_paddle_basic(image: Image.Image, paddle_engine: PaddleOCR) -> str`** (函数, 行: 135)
    ```
    在单个预处理图像上运行 PaddleOCR。

Args:
    image: 经过 PaddleOCR 优化的预处理图像 (通常是 RGB)。
    paddle_engine: 已初始化的 PaddleOCR 引擎实例。

Returns:
    识别出的文本字符串，失败则返回空字符串。
    ```
*   **`run_paddle_enhanced(images: List[...], paddle_engine: PaddleOCR) -> List[str]`** (函数, 行: 169)
    ```
    在多个图像版本上运行 PaddleOCR (用于增强识别)。

Args:
    images: 预处理后的图像列表 (通常是 RGB)。
    paddle_engine: 已初始化的 PaddleOCR 引擎实例。

Returns:
    每个图像识别出的文本列表 (失败则为空字符串)。
    ```
*   **`perform_basic_ocr(image: Image.Image, engine: str, ocr_cache: Dict[...], enable_cache: bool, paddle_engine: Optional[PaddleOCR], use_paddle: bool, config: Optional[str], page_num: Optional[int]) -> str`** (函数, 行: 222)
    ```
    执行基础OCR，包括缓存检查、引擎选择和回退逻辑。

Args:
    image: 原始 PIL 图像。
    engine: OCR 引擎选择 ("auto", "paddle", "tesseract")。
    ocr_cache: OCR 结果缓存字典。
    enable_cache: 是否启用缓存。
    paddle_engine: 已初始化的 PaddleOCR 引擎 (如果 use_paddle=True)。
    use_paddle: 是否允许使用 PaddleOCR。
    config: Tesseract 配置字符串 (可选)。
    page_num: 页码 (用于日志和缓存键，可选)。

Returns:
    识别出的文本字符串。
    ```
*   **`perform_enhanced_ocr(image: Image.Image, use_paddle: bool, paddle_engine: Optional[PaddleOCR], target_text: Optional[str], extract_number: bool, max_attempts: int, collect_all_results: bool, tesseract_configs: Optional[...]) -> Union[...]`** (函数, 行: 318)
    ```
    执行增强OCR，尝试多种图像处理和OCR配置以提高识别率。

Args:
    image: 原始 PIL 图像。
    use_paddle: 是否优先使用 PaddleOCR。
    paddle_engine: 已初始化的 PaddleOCR 引擎实例。
    target_text: 用于提前退出的目标文本 (可选)。
    extract_number: 是否尝试提取统一编号。
    max_attempts: 最大尝试次数。
    collect_all_results: 是否收集所有结果 (如果为False，找到target_text即返回True)。
    tesseract_configs: Tesseract 的配置列表 (可选)。

Returns:
    如果 collect_all_results=False 且找到 target_text，返回 True。
    如果 extract_number=True，返回 UnifiedNumberResult 列表。
    否则返回 False。
    ```

## `archive_processing/utils/pdf_processor_usefull.py`

*   **`_detect_cpu_type()`** (函数, 行: 57)
    ```
    检测CPU类型，返回CPU平台类型

MOVED: [2024-05-14] 移动到 archive_processing/utils/system_utils.py #AFM-32
    ```
*   **`_format_time(seconds)`** (函数, 行: 66)
    ```
    格式化时间为易读形式

MOVED: [2024-05-14] 移动到 archive_processing/utils/system_utils.py #AFM-32
    ```
*   **`__init__(self, dpi: int, crop_ratio: float, batch_size: int, case_sensitive: bool, ignore_punctuation: bool, use_paddle_ocr: bool, cpu_optimization: Optional[str], enable_cache: bool)`** (方法, 行: 89)
    ```
    (已废弃) 初始化PDF处理器。
    ```
*   **`process_pdf(self, pdf_path: str, target_text: str) -> List[...]`** (方法, 行: 124)
    ```
    (已废弃) 处理PDF并执行物理分割。请改用 PdfProcessingService + 调用方编排。
    ```
*   **`_split_pdf(self, pdf_path: str, split_points: List[int], unified_numbers: Dict[...]) -> List[...]`** (方法, 行: 202)
    ```
    (已废弃) PDF物理分割逻辑已被移出。请使用 pdf_utils.create_temp_pdf_for_single_archive 配合调用方逻辑。
    ```
*   **`get_stats(self) -> dict`** (方法, 行: 213)
    ```
    (保留，但数据源已变化) 获取处理统计信息。
    ```
*   **`split_pdf(pdf_path: str, target_text: str, use_paddle_ocr: bool, cpu_optimization: Optional[str], enable_cache: bool, **kwargs) -> List[str]`** (函数, 行: 239)
    ```
    分割PDF文件

Args:
    pdf_path: PDF文件路径
    target_text: 目标文本
    use_paddle_ocr: 是否使用PaddleOCR引擎(如果可用)
    cpu_optimization: CPU优化模式("intel", "amd"或None自动检测)
    enable_cache: 是否启用OCR缓存提高性能
    **kwargs: 其他参数设置

Returns:
    分割后的PDF文件路径列表
    ```
*   **`debug_pdf_processing(pdf_path: str, target_text: str, output_dir: str, **kwargs) -> Dict`** (函数, 行: 265)
    ```
    (已废弃) 调试PDF处理功能。请使用新的服务和工具自行实现调试逻辑。
    ```
*   **`benchmark_pdf_processing(pdf_path: str, repeat: int) -> Dict`** (函数, 行: 275)
    ```
    (可能需要重构) 基准测试PDF处理性能。

注意：此函数目前基于旧的 PDFProcessor 模拟处理速度，
可能需要重构以使用 PdfProcessingService 进行更真实的基准测试。
    ```
*   **`generate_archive_filename(unified_number, original_path, timestamp)`** (函数, 行: 392)
    ```
    生成归档文件的规范化文件名。

优先使用统一编号，如果提供的话。否则，可以基于原始文件名或时间戳。

Args:
    unified_number (str, optional): 文件的统一编号。
    original_path (str, optional): 原始文件的路径，用于提取文件名和扩展名。
    timestamp (datetime, optional): 文件处理的时间戳。
    
Returns:
    str: 生成的文件名。
    ```
*   **`get_archive_storage_path(unified_number)`** (函数, 行: 412)
    ```
    获取档案存储路径

Args:
    unified_number: 统一编号用于生成子目录
    
Returns:
    完整存储路径
    ```
*   **`generate_file_hash(file_path)`** (函数, 行: 428)
    ```
    生成文件哈希值

Args:
    file_path: 文件路径
    
Returns:
    SHA-256哈希值
    ```
*   **`create_secure_access_token(file_path, user_id, expires_in)`** (函数, 行: 448)
    ```
    创建安全访问令牌

Args:
    file_path: 文件路径
    user_id: 用户ID
    expires_in: 有效期(秒)
    
Returns:
    安全访问令牌
    ```
*   **`verify_access_token(token, file_path)`** (函数, 行: 466)
    ```
    验证访问令牌

Args:
    token: 访问令牌
    file_path: 文件路径
    
Returns:
    是否有效
    ```
*   **`backup_archive_file(file_path, backup_type)`** (函数, 行: 488)
    ```
    备份档案文件

Args:
    file_path: 文件路径
    backup_type: 备份类型
    
Returns:
    备份文件路径
    ```
*   **`verify_file_integrity(file_path, original_hash)`** (函数, 行: 505)
    ```
    验证文件完整性

Args:
    file_path: 文件路径
    original_hash: 原始哈希值
    
Returns:
    是否完整
    ```
*   **`create_progress_tracker(task_id, total_steps)`** (函数, 行: 526)
    ```
    创建进度追踪器

Args:
    task_id: 任务ID
    total_steps: 总步骤数
    
Returns:
    进度追踪器实例
    ```
*   **`update_progress(task_id, completed_steps, message, status)`** (函数, 行: 543)
    ```
    更新任务进度

Args:
    task_id: 任务ID
    completed_steps: 已完成步骤数
    message: 进度消息
    status: 任务状态
    
Returns:
    更新后的进度信息
    ```
*   **`get_task_progress(task_id)`** (函数, 行: 562)
    ```
    获取任务进度

Args:
    task_id: 任务ID
    
Returns:
    进度信息
    ```
*   **`send_completion_notification(user_id, task_info)`** (函数, 行: 578)
    ```
    发送完成通知

Args:
    user_id: 用户ID
    task_info: 任务信息
    
Returns:
    发送结果
    ```
*   **`generate_archive_storage_path(unified_number)`** (函数, 行: 598)
    ```
    根据统一编号生成存档路径

Args:
    unified_number: 统一编号
    
Returns:
    存档路径
    ```
*   **`send_task_notification(user_id, notification_type, message, data)`** (函数, 行: 664)
    ```
    发送任务通知

Args:
    user_id: 用户ID
    notification_type: 通知类型
    message: 通知内容
    data: 附加数据
    
Returns:
    发送结果
    ```

## `archive_processing/utils/pdf_utils.py`

*   **`calculate_part_ranges(split_points: List[int], total_pages: int) -> List[PDFPartRange]`** (函数, 行: 10)
    ```
    根据分割点（从0开始的页码）计算每个部分的页面范围（包含起始和结束页）。

Args:
    split_points: 分割点页码列表 (0-based)。例如 [10, 25] 表示在第10页之后和第25页之后分割。
                  分割发生在 *指定页之后*，所以第10页属于第一部分。
    total_pages: PDF的总页数。

Returns:
    一个包含 PDFPartRange 对象的列表，每个对象代表一个部分的起始和结束页码 (0-based, inclusive)。
    ```
*   **`create_temp_pdf_for_single_archive(original_pdf_path: str, start_page: int, end_page: int, output_path: str) -> bool`** (函数, 行: 79)
    ```
    为单个逻辑档案创建临时的 PDF 文件，包含原始 PDF 的指定页面范围。

Args:
    original_pdf_path: 原始 PDF 文件的路径。
    start_page: 起始页码 (0-based, inclusive)。
    end_page: 结束页码 (0-based, inclusive)。
    output_path: 输出 PDF 文件的路径。

Returns:
    True 如果成功写入，False 如果发生错误。
    ```

## `archive_processing/utils/processing_report_utils.py`

*   **`create_result_summary(input_pdf_path: str, result_dto: ProcessingResultDto, archived_files: List[...], output_dir: str, status_update: Optional[Dict]) -> Optional[str]`** (函数, 行: 13)
    ```
    创建处理结果摘要文件。

Args:
    input_pdf_path: 输入的原始PDF文件路径。
    result_dto: PdfProcessingService 返回的处理结果对象。
    archived_files: 包含最终归档文件信息 (路径, 统一编号) 的元组列表。
                    如果某部分未成功归档，路径可能为 None。
    output_dir: 摘要文件输出目录。
    status_update: 档案状态更新结果字典 (可选)。

Returns:
    成功创建的摘要文件路径，如果发生错误则返回 None。
    ```

## `archive_processing/utils/system_utils.py`

*   **`detect_cpu_type()`** (函数, 行: 17)
    ```
    检测CPU类型，返回CPU平台类型

Returns:
    str: CPU类型 - "intel", "amd" 或 "unknown"
    ```
*   **`format_time(seconds)`** (函数, 行: 56)
    ```
    格式化时间为易读形式

Args:
    seconds: 秒数
    
Returns:
    str: 格式化后的时间字符串
    ```

## `archive_processing/utils/text_utils.py`

*   **`preprocess_text(text: str, case_sensitive: bool, ignore_punctuation: bool) -> str`** (函数, 行: 9)
    ```
    预处理文本用于匹配

Args:
    text: 原始文本
    case_sensitive: 是否区分大小写
    ignore_punctuation: 是否忽略标点符号
    
Returns:
    处理后的文本
    ```
*   **`normalize_unified_number(number: str) -> str`** (函数, 行: 31)
    ```
    专门负责规范化编号格式

Args:
    number: 原始编号字符串
    
Returns:
    规范化后的编号字符串
    ```
*   **`extract_unified_number(ocr_text: str, target_text: str) -> Optional[str]`** (函数, 行: 83)
    ```
    从文本中提取可能的统一编号

Args:
    ocr_text: OCR识别出的原始文本
    target_text: 用于定位编号区域的目标文本，例如"代合同"
    
Returns:
    提取并规范化后的统一编号，如果未找到则返回None
    ```
*   **`remove_parentheses_content(text: str) -> str`** (函数, 行: 128)
    ```
    移除文本中的括号及其包含的内容
    ```
*   **`is_text_match(text: str, target: str, match_keywords: str, min_keyword_count: int) -> Tuple[...]`** (函数, 行: 135)
    ```
    检查文本是否匹配目标或包含足够的关键字

Args:
    text: 待检查文本
    target: 目标文本（用于精确匹配，例如 "代合同"）
    match_keywords: 用于模糊匹配的关键字字符串
    min_keyword_count: 模糊匹配所需的最少关键字数量
    
Returns:
    Tuple[bool, str]: (是否匹配, 匹配类型)
    匹配类型: "exact" 完全匹配, "fuzzy" 模糊匹配, "none" 不匹配
    ```
*   **`choose_best_number(results: List[UnifiedNumberResult]) -> Optional[str]`** (函数, 行: 169)
    ```
    从多个OCR结果中选择最可靠的统一编号。

Args:
    results: UnifiedNumberResult 对象的列表，包含识别到的编号和方法名。
    
Returns:
    最可靠的统一编号字符串，如果无法确定则返回 None。
    ```

## `archive_processing/views.py`

*   **`post(self, request, *args, **kwargs)`** (方法, 行: 36)
    ```
    处理POST请求，接收文件和目标档案盒号。
    ```
*   **`get(self, request, task_id, *args, **kwargs)`** (方法, 行: 149)
    ```
    无文档字符串
    ```
*   **`post(self, request, task_id, *args, **kwargs)`** (方法, 行: 160)
    ```
    无文档字符串
    ```
*   **`get(self, request, task_id, *args, **kwargs)`** (方法, 行: 170)
    ```
    无文档字符串
    ```

## `archive_records/admin.py`

*   **`get_queryset(self, request)`** (方法, 行: 30)
    ```
    无文档字符串
    ```
*   **`change_source_display(self, obj)`** (方法, 行: 33)
    ```
    无文档字符串
    ```
*   **`change_reason_short(self, obj)`** (方法, 行: 37)
    ```
    无文档字符串
    ```
*   **`changed_by_name(self, obj)`** (方法, 行: 43)
    ```
    无文档字符串
    ```
*   **`actions_column(self, obj)`** (方法, 行: 50)
    ```
    显示操作链接
    ```
*   **`get_queryset(self, request)`** (方法, 行: 84)
    ```
    无文档字符串
    ```
*   **`record_display(self, obj)`** (方法, 行: 87)
    ```
    显示记录标识，带链接
    ```
*   **`change_type_display(self, obj)`** (方法, 行: 99)
    ```
    带颜色的变更类型显示
    ```
*   **`changed_by_name(self, obj)`** (方法, 行: 115)
    ```
    显示变更人信息
    ```
*   **`rollback_info(self, obj)`** (方法, 行: 123)
    ```
    显示回滚信息
    ```
*   **`actions_column(self, obj)`** (方法, 行: 130)
    ```
    显示操作链接
    ```
*   **`get_queryset(self, request)`** (方法, 行: 151)
    ```
    无文档字符串
    ```
*   **`record_info(self, obj)`** (方法, 行: 158)
    ```
    显示记录信息
    ```
*   **`version_info(self, obj)`** (方法, 行: 170)
    ```
    显示版本信息
    ```
*   **`change_action_display(self, obj)`** (方法, 行: 177)
    ```
    带颜色的变更动作显示
    ```
*   **`field_importance_display(self, obj)`** (方法, 行: 193)
    ```
    带标记的字段重要性显示
    ```
*   **`old_value_display(self, obj)`** (方法, 行: 206)
    ```
    旧值显示，截断长文本
    ```
*   **`new_value_display(self, obj)`** (方法, 行: 215)
    ```
    新值显示，截断长文本
    ```
*   **`view_details(self, obj)`** (方法, 行: 235)
    ```
    查看详情链接
    ```
*   **`has_add_permission(self, request, obj)`** (方法, 行: 243)
    ```
    无文档字符串
    ```
*   **`has_change_permission(self, request, obj)`** (方法, 行: 246)
    ```
    无文档字符串
    ```
*   **`has_delete_permission(self, request, obj)`** (方法, 行: 249)
    ```
    无文档字符串
    ```
*   **`view_version_history(self, request, queryset)`** (方法, 行: 262)
    ```
    查看选中记录的版本历史
    ```

## `archive_records/models.py`

*   **`__str__(self)`** (方法, 行: 296)
    ```
    无文档字符串
    ```
*   **`save(self, *args, **kwargs)`** (方法, 行: 299)
    ```
    无文档字符串
    ```
*   **`get_first_issue_record(self)`** (方法, 行: 305)
    ```
    获取第一次发放记录
    ```
*   **`get_second_issue_record(self)`** (方法, 行: 313)
    ```
    获取第二次发放记录
    ```
*   **`get_all_issue_records(self)`** (方法, 行: 321)
    ```
    获取所有活跃的发放记录
    ```
*   **`__str__(self)`** (方法, 行: 396)
    ```
    无文档字符串
    ```
*   **`__str__(self)`** (方法, 行: 440)
    ```
    无文档字符串
    ```
*   **`get_record_changes_count(self)`** (方法, 行: 443)
    ```
    获取记录变更数量
    ```
*   **`get_affected_records(self)`** (方法, 行: 447)
    ```
    获取影响的记录列表
    ```
*   **`get_critical_changes_count(self)`** (方法, 行: 451)
    ```
    获取关键字段变更数量
    ```
*   **`get_stats(self)`** (方法, 行: 458)
    ```
    获取批次统计信息
    ```
*   **`update_summary(self)`** (方法, 行: 474)
    ```
    更新变更摘要
    ```
*   **`__str__(self)`** (方法, 行: 530)
    ```
    无文档字符串
    ```
*   **`get_changed_by_details(self)`** (方法, 行: 535)
    ```
    获取变更人的详细信息
    ```
*   **`get_change_time(self)`** (方法, 行: 552)
    ```
    获取变更时间
    ```
*   **`get_critical_changes(self)`** (方法, 行: 556)
    ```
    获取关键字段变更列表
    ```
*   **`get_important_changes(self)`** (方法, 行: 560)
    ```
    获取重要字段变更列表
    ```
*   **`has_critical_changes(self)`** (方法, 行: 565)
    ```
    是否包含关键字段变更
    ```
*   **`has_important_changes(self)`** (方法, 行: 570)
    ```
    是否包含重要字段变更
    ```
*   **`summary_text(self)`** (方法, 行: 575)
    ```
    获取变更摘要文本
    ```
*   **`__str__(self)`** (方法, 行: 632)
    ```
    无文档字符串
    ```
*   **`get_change_description(self)`** (方法, 行: 635)
    ```
    获取变更描述文本
    ```
*   **`save(self, *args, **kwargs)`** (方法, 行: 646)
    ```
    无文档字符串
    ```
*   **`create_record_change_log(self, record, batch, version_number, change_type, record_before, changes, is_rollback, rollback_source_version)`** (方法, 行: 659)
    ```
    创建记录级变更日志

Args:
    record: 档案记录实例
    batch: 变更批次
    version_number: 版本号
    change_type: 变更类型 ('create', 'update', 'delete', 'rollback')
    record_before: 变更前的状态 (可选)
    changes: 变更字段列表 (可选)
    is_rollback: 是否为回滚操作
    rollback_source_version: 回滚源版本号

Returns:
    RecordChangeLog: 创建的变更日志实例
    ```

## `archive_records/serializers.py`

*   **`get_status_display(self, obj)`** (方法, 行: 31)
    ```
    无文档字符串
    ```
*   **`validate_file(self, value)`** (方法, 行: 112)
    ```
    验证文件是否为有效的Excel文件
    ```
*   **`validate_sheet_name(self, value)`** (方法, 行: 118)
    ```
    验证工作表名称
    ```
*   **`get_change_type_display(self, obj)`** (方法, 行: 147)
    ```
    无文档字符串
    ```
*   **`get_batch_info(self, obj)`** (方法, 行: 150)
    ```
    无文档字符串
    ```
*   **`get_rollback_info(self, obj)`** (方法, 行: 161)
    ```
    获取回滚相关信息
    ```

## `archive_records/services/archive_status_service.py`

*   **`update_archive_status(unified_numbers: dict) -> dict`** (函数, 行: 12)
    ```
    更新档案记录的归档状态

Args:
    unified_numbers: 字典，key为页码，value为统一编号
    
Returns:
    dict: 更新结果统计
    ```

## `archive_records/services/excel_import.py`

*   **`__init__(self, field_mapping)`** (方法, 行: 133)
    ```
    初始化导入服务

Args:
    field_mapping (dict, optional): 自定义Excel列名到模型字段的映射。
                                   如未提供，使用默认映射。
    ```
*   **`_process_excel_data(self, df)`** (方法, 行: 143)
    ```
    处理Excel数据，识别并设置正确的表头
    ```
*   **`import_from_file(self, file_path, user, sheet_name, duplicate_strategy)`** (方法, 行: 157)
    ```
    从Excel文件导入数据

Args:
    file_path: Excel文件路径
    user: 操作用户
    sheet_name: 工作表名称或索引
    duplicate_strategy: 重复数据处理策略，可选值:
        - 'smart_update': 智能更新(默认)，仅更新有变化的非空字段
        - 'overwrite': 覆盖，完全替换现有记录
        - 'skip': 跳过，忽略已存在的记录
        - 'append': 追加，总是创建新记录(需修改唯一性约束)

Returns:
    ImportLog: 导入日志对象
    ```
*   **`_process_dataframe(self, df, import_log, duplicate_strategy)`** (方法, 行: 279)
    ```
    处理DataFrame并导入数据

Args:
    df (DataFrame): pandas DataFrame对象
    import_log (ImportLog): 导入日志对象
    duplicate_strategy (str): 重复数据处理策略，可选值:
        - 'smart_update': 智能更新(默认)，仅更新有变化的非空字段
        - 'overwrite': 覆盖，完全替换现有记录
        - 'skip': 跳过，忽略已存在的记录
        - 'append': 追加，总是创建新记录(需修改唯一性约束)

Returns:
    tuple: (成功记录数, 错误记录列表, 跳过记录列表, 创建记录数, 更新记录数, 未改变记录数)
    ```
*   **`_map_dataframe_columns(self, df)`** (方法, 行: 691)
    ```
    将DataFrame的列名映射为模型字段名，并进行基本的数据清洗

Args:
    df (DataFrame): 原始DataFrame
    
Returns:
    DataFrame: 处理后的DataFrame
    ```
*   **`_process_row(self, row)`** (方法, 行: 722)
    ```
    处理单行数据，进行类型转换和数据清洗
    ```
*   **`_parse_numeric_with_format(self, value, field)`** (方法, 行: 806)
    ```
    解析数值字段，并确保按照固定格式存储

Args:
    value: 输入值
    field: 字段名
    
Returns:
    解析后的数值，按照固定格式的字符串
    ```
*   **`_calculate_file_hash(self, file_path)`** (方法, 行: 870)
    ```
    计算文件的MD5哈希值

Args:
    file_path: 文件路径
    
Returns:
    str: MD5哈希值
    ```
*   **`_generate_batch_number(self)`** (方法, 行: 886)
    ```
    生成唯一的批次号

Returns:
    str: 批次号
    ```
*   **`_get_field_importance(self, field_name)`** (方法, 行: 897)
    ```
    获取字段的重要性级别
    ```
*   **`_get_field_label(self, field_name)`** (方法, 行: 901)
    ```
    获取字段的中文标签
    ```
*   **`_get_next_version_number(self, record)`** (方法, 行: 909)
    ```
    获取记录的下一个版本号
    ```
*   **`create_record_change_log(self, record, batch, version_number, change_type, record_before, field_changes, is_rollback, rollback_source_version)`** (方法, 行: 919)
    ```
    创建记录级变更日志

Args:
    record: 档案记录实例
    batch: 变更批次
    version_number: 版本号
    change_type: 变更类型 ('create', 'update', 'delete', 'rollback')
    record_before: 变更前的状态 (可选)
    field_changes: 变更字段字典 {field_name: new_value} (可选)
    is_rollback: 是否为回滚操作
    rollback_source_version: 回滚源版本号

Returns:
    RecordChangeLog: 创建的变更日志实例
    ```
*   **`_clean_json_data(self, data_list)`** (方法, 行: 1000)
    ```
    清理JSON数据中的不可序列化对象
    ```
*   **`_create_safe_serializable_data(self, data)`** (方法, 行: 1020)
    ```
    创建完全可序列化的数据副本
    ```
*   **`_serialize_for_json(self, data)`** (方法, 行: 1059)
    ```
    将数据转换为JSON可序列化的格式

Args:
    data: 任何数据对象
    
Returns:
    可JSON序列化的数据
    ```
*   **`_find_existing_record(self, commission_number)`** (方法, 行: 1081)
    ```
    根据委托编号查找现有记录

Args:
    commission_number: 委托编号
    
Returns:
    ArchiveRecord或None: 找到的记录或None
    ```
*   **`_safe_bulk_create(self, records, max_retries)`** (方法, 行: 1116)
    ```
    安全的批量创建方法，包含重试机制
    ```

## `archive_records/views.py`

*   **`get_serializer_class(self)`** (方法, 行: 63)
    ```
    根据操作类型选择序列化器
    ```
*   **`get_queryset(self)`** (方法, 行: 69)
    ```
    自定义查询集
    ```
*   **`imported_records(self, request, pk)`** (方法, 行: 91)
    ```
    获取特定导入批次的记录
    ```
*   **`post(self, request, *args, **kwargs)`** (方法, 行: 113)
    ```
    处理Excel导入请求
    ```
*   **`get_queryset(self)`** (方法, 行: 166)
    ```
    支持按导入批次、记录ID等过滤
    ```
*   **`get(self, request, record_id, format)`** (方法, 行: 198)
    ```
    获取特定记录的版本历史
    ```
*   **`_get_version_relationships(self, versions)`** (方法, 行: 263)
    ```
    获取版本之间的关系，用于构建版本关系图
    ```
*   **`get(self, request, record_id, format)`** (方法, 行: 294)
    ```
    比较记录的两个版本
    ```
*   **`_compare_versions(self, v1_record, v2_record)`** (方法, 行: 340)
    ```
    比较两个版本记录的差异

Args:
    v1_record: 第一个版本记录
    v2_record: 第二个版本记录
    
Returns:
    list: 差异字段列表
    ```
*   **`post(self, request, record_id, version_number, format)`** (方法, 行: 414)
    ```
    回滚到指定版本
    ```
*   **`get_field_label(self, field_name)`** (方法, 行: 643)
    ```
    获取字段的人类可读标签
    ```

## `dev_tools/management/commands/excel_import_cmd.py`

*   **`add_arguments(self, parser)`** (方法, 行: 52)
    ```
    无文档字符串
    ```
*   **`handle(self, *args, **options)`** (方法, 行: 58)
    ```
    无文档字符串
    ```
*   **`display_imported_records(self, limit)`** (方法, 行: 139)
    ```
    以表格形式显示导入的记录
    ```

## `dev_tools/scripts/version_management_test.py`

*   **`__init__(self)`** (方法, 行: 32)
    ```
    无文档字符串
    ```
*   **`create_test_file(self, data, filename)`** (方法, 行: 49)
    ```
    创建测试Excel文件
    ```
*   **`test_initial_import(self)`** (方法, 行: 57)
    ```
    测试首次导入
    ```
*   **`test_update_import(self, records)`** (方法, 行: 168)
    ```
    测试更新导入
    ```
*   **`test_commission_number_update(self)`** (方法, 行: 340)
    ```
    测试委托编号更新同步统一编号
    ```
*   **`test_rollback(self)`** (方法, 行: 438)
    ```
    测试版本回滚
    ```
*   **`test_version_compare(self)`** (方法, 行: 548)
    ```
    测试版本比较功能
    ```
*   **`test_force_create_strategy(self)`** (方法, 行: 607)
    ```
    专门测试force_create策略 - 即使记录已存在也创建新记录
    ```
*   **`run_all_tests(self, skip_force_create)`** (方法, 行: 656)
    ```
    运行所有测试（跳过强制创建模式测试）

Args:
    skip_force_create: 是否跳过强制创建模式测试
    ```

## `report_issuing/models.py`

*   **`__str__(self)`** (方法, 行: 81)
    ```
    无文档字符串
    ```
*   **`can_edit(self)`** (方法, 行: 84)
    ```
    检查发放单是否可编辑
    ```
*   **`can_lock(self)`** (方法, 行: 88)
    ```
    检查发放单是否可锁定
    ```
*   **`can_unlock(self)`** (方法, 行: 92)
    ```
    检查发放单是否可解锁
    ```
*   **`can_confirm(self)`** (方法, 行: 96)
    ```
    检查发放单是否可确认
    ```
*   **`can_archive(self)`** (方法, 行: 100)
    ```
    检查发放单是否可归档
    ```
*   **`can_delete(self)`** (方法, 行: 104)
    ```
    检查发放单是否可删除
    ```
*   **`__str__(self)`** (方法, 行: 161)
    ```
    无文档字符串
    ```
*   **`__str__(self)`** (方法, 行: 253)
    ```
    无文档字符串
    ```
*   **`__str__(self)`** (方法, 行: 307)
    ```
    无文档字符串
    ```

## `report_issuing/permissions.py`

*   **`has_object_permission(self, request, view, obj)`** (方法, 行: 7)
    ```
    无文档字符串
    ```

## `report_issuing/serializers.py`

*   **`get_issuer_name(self, obj)`** (方法, 行: 73)
    ```
    无文档字符串
    ```
*   **`get_issue_type_display(self, obj)`** (方法, 行: 76)
    ```
    无文档字符串
    ```
*   **`to_representation(self, instance)`** (方法, 行: 102)
    ```
    统一处理所有日期时间格式
    ```

## `report_issuing/services/issue_form_service.py`

*   **`__init__(self, user)`** (方法, 行: 64)
    ```
    初始化服务实例

Args:
    user: 当前操作用户，用于权限检查和操作记录
    ```
*   **`_generate_issue_form_number(self)`** (方法, 行: 73)
    ```
    生成唯一的发放单编号

格式：ISSUE-{年月日}-{3位序号}
    ```
*   **`_check_permission(self, action, issue_form)`** (方法, 行: 103)
    ```
    检查用户权限

Args:
    action: 要执行的操作，如 'create', 'edit', 'lock', 等
    issue_form: 要操作的发放单对象
    
Raises:
    PermissionDenied: 如果用户没有权限执行该操作
    ```
*   **`_validate_status_transition(self, issue_form, target_status)`** (方法, 行: 117)
    ```
    验证状态转换是否合法

Args:
    issue_form: 发放单对象
    target_status: 目标状态
    
Raises:
    InvalidStatusTransition: 如果状态转换不合法
    ```
*   **`_get_copies_for_issue_type(self, issue_type, archive_record)`** (方法, 行: 144)
    ```
    根据发放类型确定发放份数

Args:
    issue_type: 发放类型，'single' 或 'all_remaining'
    archive_record: 档案记录对象
    
Returns:
    int: 应该发放的份数
    ```
*   **`_validate_archive_record_for_issue(self, archive_record, issue_type)`** (方法, 行: 176)
    ```
    验证档案记录是否可以添加到指定类型的发放单

Args:
    archive_record: 档案记录对象
    issue_type: 发放类型，'single' 或 'all_remaining'
    
Raises:
    IssueFormError: 如果档案记录不能添加到该类型的发放单
    ```
*   **`create_issue_form(self, data)`** (方法, 行: 217)
    ```
    创建新的发放单

Args:
    data: 包含发放单信息的字典
    
Returns:
    IssueForm: 创建的发放单对象
    ```
*   **`update_issue_form(self, form_id, data)`** (方法, 行: 247)
    ```
    更新发放单基本信息

Args:
    form_id: 发放单ID
    data: 要更新的字段和值
    
Returns:
    IssueForm: 更新后的发放单对象
    
Raises:
    IssueFormError: 如果发放单不存在或不能更新
    ```
*   **`add_form_items(self, form_id, items_data)`** (方法, 行: 286)
    ```
    批量添加发放单条目

Args:
    form_id: 发放单ID
    items_data: 档案记录ID列表或包含完整信息的字典列表
    
Returns:
    List[IssueFormItem]: 添加的条目列表
    ```
*   **`update_form_item(self, item_id, data)`** (方法, 行: 375)
    ```
    更新发放单条目

Args:
    item_id: 条目ID
    data: 要更新的数据，如receiver和notes
    
Returns:
    IssueFormItem: 更新后的条目对象
    ```
*   **`remove_form_items(self, form_id, item_ids)`** (方法, 行: 414)
    ```
    移除发放单条目

Args:
    form_id: 发放单ID
    item_ids: 要移除的条目ID列表
    
Returns:
    int: 移除的条目数量
    ```
*   **`lock_issue_form(self, form_id)`** (方法, 行: 448)
    ```
    锁定发放单

Args:
    form_id: 发放单ID
    
Returns:
    IssueForm: 更新后的发放单对象
    ```
*   **`unlock_issue_form(self, form_id)`** (方法, 行: 479)
    ```
    解锁发放单（恢复到草稿状态）

Args:
    form_id: 发放单ID
    
Returns:
    IssueForm: 更新后的发放单对象
    
Raises:
    IssueFormError: 如果发放单不存在或不能解锁
    PermissionDenied: 如果用户不是发放单的创建者
    ```
*   **`confirm_issue_form(self, form_id)`** (方法, 行: 513)
    ```
    确认发放单（标记为已确认并打印）

Args:
    form_id: 发放单ID
    
Returns:
    IssueForm: 更新后的发放单对象
    ```
*   **`archive_issue_form(self, form_id, confirmation_file)`** (方法, 行: 540)
    ```
    归档发放单并生成发放记录

Args:
    form_id: 发放单ID
    confirmation_file: 确认单文件（可选）
    
Returns:
    IssueForm: 更新后的发放单对象
    ```
*   **`delete_issue_form(self, form_id, reason)`** (方法, 行: 577)
    ```
    删除发放单

对于未归档的发放单，执行物理删除
对于已归档的发放单，执行软删除并处理关联的发放记录

Args:
    form_id: 发放单ID
    reason: 删除原因
    
Returns:
    bool: 是否成功删除
    ```
*   **`get_issue_form(self, form_id)`** (方法, 行: 622)
    ```
    获取发放单详情

Args:
    form_id: 发放单ID
    
Returns:
    IssueForm: 发放单对象
    ```
*   **`list_issue_forms(self, filters, page, page_size)`** (方法, 行: 637)
    ```
    列出发放单

Args:
    filters: 过滤条件
    page: 页码
    page_size: 每页记录数
    
Returns:
    dict: 包含分页结果的字典
    ```
*   **`get_available_archive_records(self, issue_type, filters)`** (方法, 行: 694)
    ```
    获取可用于发放的档案记录（从前端界面批量选择角度考虑的发放方式，单份发放或全部/剩余发放）
台账记录形式的发放类型由 _determine_actual_issue_type 函数处理

Args:
    issue_type: 发放类型 ('single' 或 'all_remaining')
    filters: 额外的过滤条件
    
Returns:
    QuerySet: 符合条件的档案记录查询集
    ```
*   **`check_archive_records_status(self, archive_record_ids, issue_type)`** (方法, 行: 710)
    ```
    检查多个档案记录是否可以添加到指定类型的发放单（从前端界面批量选择角度考虑的发放方式，单份发放或全部/剩余发放）
台账记录形式的发放类型由 _determine_actual_issue_type 函数处理

Args:
    archive_record_ids: 档案记录ID列表
    issue_type: 发放类型，'single' 或 'all_remaining'
    
Returns:
    dict: 每个档案记录ID的检查结果，键为ID，值为(bool, str)元组，
         表示是否可发放及原因
    ```

## `report_issuing/services/issue_record_service.py`

*   **`__init__(self, user)`** (方法, 行: 37)
    ```
    初始化服务实例

Args:
    user: 当前操作用户，用于权限检查和操作记录
    ```
*   **`create_issue_record(self, archive_record_id, data)`** (方法, 行: 46)
    ```
    创建发放记录

Args:
    archive_record_id: 档案记录ID
    data: 包含发放记录信息的字典
    
Returns:
    IssueRecord: 创建的发放记录
    ```
*   **`update_issue_record(self, record_id, data, reason)`** (方法, 行: 121)
    ```
    更新发放记录

更新策略：创建新记录而不是直接修改原记录，以保证历史的完整

Args:
    record_id: 发放记录ID
    data: 要更新的数据
    reason: 更新原因
    
Returns:
    IssueRecord: 更新后的新记录
    ```
*   **`delete_issue_record(self, record_id, reason)`** (方法, 行: 245)
    ```
    删除发放记录（软删除）

Args:
    record_id: 发放记录ID
    reason: 删除原因
    
Returns:
    bool: 是否成功删除
    ```
*   **`get_issue_record_history(self, record_id)`** (方法, 行: 294)
    ```
    获取发放记录的历史

Args:
    record_id: 发放记录ID
    
Returns:
    QuerySet: 历史记录查询集
    ```
*   **`get_archive_record_issue_status(self, archive_record_id)`** (方法, 行: 311)
    ```
    获取档案记录的发放状态

Args:
    archive_record_id: 档案记录ID
    
Returns:
    dict: 发放状态信息
    ```
*   **`handle_form_deleted_records(self, issue_form, reason)`** (方法, 行: 362)
    ```
    处理发放单删除时关联的发放记录

Args:
    issue_form: 发放单对象
    reason: 删除原因
    ```
*   **`create_form_issue_records(self, issue_form)`** (方法, 行: 451)
    ```
    为发放单的所有条目创建发放记录

Args:
    issue_form: 发放单对象
    
Returns:
    bool: 是否成功创建所有记录
    ```
*   **`_determine_actual_issue_type(self, archive_record, form_issue_type)`** (方法, 行: 536)
    ```
    根据发放单类型和档案记录状态确定实际的发放类型

Args:
    archive_record: 档案记录对象
    form_issue_type: 发放单类型，'single' 或 'all_remaining'
    
Returns:
    str: 实际的发放类型，'first' 或 'second'
    ```
*   **`get_available_archive_records_for_issue(self, issue_type, filters)`** (方法, 行: 564)
    ```
    获取可用于发放的档案记录

Args:
    issue_type: 发放类型 ('single' 或 'all_remaining')
    filters: 额外的过滤条件，如工程名称、委托单位等
    
Returns:
    QuerySet: 符合条件的档案记录查询集
    ```

## `report_issuing/services/permission_service.py`

*   **`__init__(self, user)`** (方法, 行: 17)
    ```
    初始化权限服务

Args:
    user: 当前操作用户
    ```
*   **`has_model_permission(self, model_name, action)`** (方法, 行: 26)
    ```
    检查用户是否具有对指定模型的权限

Args:
    model_name: 模型名称，如 'issueform'
    action: 操作类型，如 'add', 'change', 'delete', 'view'
    
Returns:
    bool: 是否具有权限
    ```
*   **`has_group_permission(self, group_name)`** (方法, 行: 46)
    ```
    检查用户是否属于指定用户组

Args:
    group_name: 用户组名称
    
Returns:
    bool: 是否属于该用户组
    ```
*   **`can_archive(self)`** (方法, 行: 64)
    ```
    检查用户是否有归档权限

Returns:
    bool: 是否有归档权限
    ```
*   **`is_creator_or_admin(self, obj)`** (方法, 行: 73)
    ```
    检查用户是否为对象创建者或管理员

Args:
    obj: 需要检查的对象，必须有 issuer 属性
    
Returns:
    bool: 是否为创建者或管理员
    ```
*   **`check_issue_form_permission(self, action, issue_form)`** (方法, 行: 92)
    ```
    检查用户对发放单的权限

Args:
    action: 要执行的操作，如 'create', 'edit', 'lock', 'confirm', 'archive', 'delete'
    issue_form: 要操作的发放单对象
    
Raises:
    PermissionDenied: 如果用户没有权限执行该操作
    ValueError: 如果参数不正确
    ```

## `report_issuing/views.py`

*   **`get_queryset(self)`** (方法, 行: 29)
    ```
    自定义查询集，可根据权限过滤
    ```
*   **`perform_create(self, serializer)`** (方法, 行: 35)
    ```
    创建发放单
    ```
*   **`add_items(self, request, pk)`** (方法, 行: 46)
    ```
    添加发放单条目
    ```
*   **`remove_items(self, request, pk)`** (方法, 行: 70)
    ```
    移除发放单条目
    ```
*   **`lock(self, request, pk)`** (方法, 行: 81)
    ```
    锁定发放单
    ```
*   **`unlock(self, request, pk)`** (方法, 行: 92)
    ```
    解锁发放单
    ```
*   **`confirm(self, request, pk)`** (方法, 行: 103)
    ```
    确认发放单
    ```
*   **`archive(self, request, pk)`** (方法, 行: 114)
    ```
    归档发放单
    ```
*   **`delete_form(self, request, pk)`** (方法, 行: 126)
    ```
    删除发放单
    ```
*   **`available_records(self, request)`** (方法, 行: 137)
    ```
    获取可用于发放的档案记录
    ```
*   **`get_queryset(self)`** (方法, 行: 168)
    ```
    自定义查询集，默认只返回活跃记录
    ```
*   **`create(self, request, *args, **kwargs)`** (方法, 行: 179)
    ```
    创建发放记录
    ```
*   **`update(self, request, *args, **kwargs)`** (方法, 行: 190)
    ```
    更新发放记录
    ```
*   **`delete_record(self, request, pk)`** (方法, 行: 203)
    ```
    删除发放记录
    ```
*   **`history(self, request, pk)`** (方法, 行: 214)
    ```
    获取发放记录历史
    ```
*   **`archive_status(self, request)`** (方法, 行: 225)
    ```
    获取档案记录的发放状态
    ```

## `test_suite/dependencies/test_tesseract.py`

*   **`test_tesseract_installation()`** (函数, 行: 36)
    ```
    测试Tesseract OCR是否正确安装和配置
    ```

## `test_suite/functional/test_pdf_split_high_speed.py`

*   **`main()`** (函数, 行: 78)
    ```
    无文档字符串
    ```

## `test_suite/integration/archive_processing/test_archive_processing_upload.py`

*   **`setUp(self)`** (方法, 行: 28)
    ```
    测试环境初始化
    ```
*   **`test_pdf_upload_success(self, mock_save_file, mock_create_task, mock_delay)`** (方法, 行: 57)
    ```
    测试成功的PDF上传流程
    ```
*   **`test_pdf_upload_missing_file(self)`** (方法, 行: 111)
    ```
    测试上传请求缺少文件
    ```
*   **`test_pdf_upload_missing_assigned_box_number(self)`** (方法, 行: 123)
    ```
    测试上传请求缺少目标档案盒号
    ```
*   **`test_pdf_upload_save_validation_error(self, mock_save_file)`** (方法, 行: 147)
    ```
    测试 UploadService 抛出 ValidationError
    ```
*   **`test_pdf_upload_save_io_error(self, mock_save_file)`** (方法, 行: 163)
    ```
    测试 UploadService 抛出 IOError
    ```
*   **`test_pdf_upload_task_creation_error(self, mock_save_file, mock_create_task, mock_delay)`** (方法, 行: 181)
    ```
    测试 TaskService 创建任务失败
    ```
*   **`test_pdf_upload_unexpected_error(self, mock_save_file)`** (方法, 行: 201)
    ```
    测试 UploadService 抛出未知错误
    ```

## `test_suite/integration/archive_records/test_excel_import_version_history.py`

*   **`setUp(self)`** (方法, 行: 21)
    ```
    测试前准备
    ```
*   **`test_full_workflow(self)`** (方法, 行: 37)
    ```
    测试完整工作流：导入 -> 更新 -> 查看历史 -> 比较版本 -> 回滚
    ```

## `test_suite/integration/report_issuing/test_api_issue_form.py`

*   **`setUp(self)`** (方法, 行: 20)
    ```
    设置测试环境
    ```
*   **`test_list_issue_forms(self)`** (方法, 行: 66)
    ```
    测试获取发放单列表
    ```
*   **`test_create_issue_form(self)`** (方法, 行: 75)
    ```
    测试创建发放单
    ```
*   **`test_add_items(self)`** (方法, 行: 95)
    ```
    测试添加发放单条目
    ```
*   **`test_remove_items(self)`** (方法, 行: 137)
    ```
    测试移除发放单条目
    ```
*   **`test_lock_and_unlock(self)`** (方法, 行: 154)
    ```
    测试锁定和解锁发放单
    ```
*   **`test_confirm_and_archive(self)`** (方法, 行: 178)
    ```
    测试确认和归档发放单
    ```
*   **`test_delete_form(self)`** (方法, 行: 224)
    ```
    测试删除发放单
    ```
*   **`test_available_records(self)`** (方法, 行: 268)
    ```
    测试获取可用于发放的档案记录
    ```

## `test_suite/integration/report_issuing/test_api_issue_record.py`

*   **`setUp(self)`** (方法, 行: 21)
    ```
    设置测试环境
    ```
*   **`test_list_issue_records(self)`** (方法, 行: 77)
    ```
    测试获取发放记录列表
    ```
*   **`test_create_issue_record(self)`** (方法, 行: 86)
    ```
    测试创建发放记录
    ```
*   **`test_update_issue_record(self)`** (方法, 行: 121)
    ```
    测试更新发放记录
    ```
*   **`test_delete_record(self)`** (方法, 行: 150)
    ```
    测试删除发放记录
    ```
*   **`test_get_history(self)`** (方法, 行: 173)
    ```
    测试获取发放记录历史
    ```
*   **`test_archive_status(self)`** (方法, 行: 192)
    ```
    测试获取档案记录的发放状态
    ```

## `test_suite/integration/test_excel_import_and_pdf_processing_workflow.py`

*   **`setUp(self)`** (方法, 行: 53)
    ```
    无文档字符串
    ```
*   **`tearDown(self)`** (方法, 行: 72)
    ```
    无文档字符串
    ```
*   **`test_excel_import_and_pdf_processing(self)`** (方法, 行: 80)
    ```
    测试Excel导入和PDF处理的完整流程
    ```

## `test_suite/unit/archive_processing/test_pdf_file_operations.py`

*   **`setUp(self)`** (方法, 行: 19)
    ```
    设置测试环境
    ```
*   **`tearDown(self)`** (方法, 行: 47)
    ```
    清理测试环境
    ```
*   **`test_generate_archive_storage_path(self)`** (方法, 行: 66)
    ```
    测试归档存储路径生成功能
    ```
*   **`test_generate_archive_filename(self)`** (方法, 行: 87)
    ```
    测试归档文件名生成功能
    ```
*   **`test_pdf_split_path_handling(self, mock_split_pdf)`** (方法, 行: 102)
    ```
    测试PDF分割的路径处理
    ```
*   **`test_path_construction(self)`** (方法, 行: 121)
    ```
    直接测试路径构造逻辑
    ```

## `test_suite/unit/archive_processing/test_record_update_service.py`

*   **`setUp(self)`** (方法, 行: 17)
    ```
    设置测试环境
    ```
*   **`tearDown(self)`** (方法, 行: 35)
    ```
    清理测试环境
    ```
*   **`test_generate_file_url_with_media_root_path(self)`** (方法, 行: 46)
    ```
    测试处理在 MEDIA_ROOT 下的文件路径
    ```
*   **`test_generate_file_url_with_relative_path(self)`** (方法, 行: 53)
    ```
    测试处理相对路径
    ```
*   **`test_generate_file_url_with_none_path(self)`** (方法, 行: 61)
    ```
    测试处理 None 路径
    ```
*   **`test_generate_file_url_with_empty_path(self)`** (方法, 行: 66)
    ```
    测试处理空路径
    ```
*   **`test_generate_file_url_with_non_media_path(self)`** (方法, 行: 71)
    ```
    测试处理不在 MEDIA_ROOT 下的路径
    ```
*   **`setUp(self)`** (方法, 行: 92)
    ```
    设置测试环境
    ```
*   **`test_update_archive_record_success(self, mock_generate_url)`** (方法, 行: 118)
    ```
    测试成功更新档案记录
    ```
*   **`test_update_archive_record_without_box_number(self, mock_generate_url)`** (方法, 行: 146)
    ```
    测试不提供盒号的情况
    ```
*   **`test_update_archive_record_record_not_found(self)`** (方法, 行: 168)
    ```
    测试记录未找到的情况
    ```
*   **`test_update_archive_record_missing_unified_number(self)`** (方法, 行: 183)
    ```
    测试缺少统一编号的情况
    ```
*   **`test_update_archive_record_missing_file_path(self)`** (方法, 行: 198)
    ```
    测试缺少文件路径的情况
    ```
*   **`test_update_archive_record_user_not_found(self, mock_generate_url)`** (方法, 行: 214)
    ```
    测试用户不存在的情况
    ```
*   **`test_update_archive_record_db_error(self, mock_save, mock_generate_url)`** (方法, 行: 240)
    ```
    测试数据库错误的情况
    ```

## `test_suite/unit/archive_processing/test_upload_service.py`

*   **`setUp(self)`** (方法, 行: 21)
    ```
    测试前的准备工作
    ```
*   **`tearDown(self)`** (方法, 行: 71)
    ```
    测试后的清理工作
    ```
*   **`test_validate_file_with_valid_file(self)`** (方法, 行: 79)
    ```
    测试验证有效文件
    ```
*   **`test_validate_file_with_invalid_extension(self)`** (方法, 行: 88)
    ```
    测试验证扩展名无效的文件
    ```
*   **`test_validate_file_with_large_file(self)`** (方法, 行: 95)
    ```
    测试验证超过大小限制的文件
    ```
*   **`test_validate_file_with_empty_file(self)`** (方法, 行: 113)
    ```
    测试验证空文件对象
    ```
*   **`test_validate_pdf_content_with_valid_pdf(self, mock_pdfreader)`** (方法, 行: 122)
    ```
    测试验证有效PDF内容
    ```
*   **`test_validate_pdf_content_with_invalid_pdf(self, mock_pdfreader)`** (方法, 行: 142)
    ```
    测试验证无效PDF内容
    ```
*   **`test_validate_pdf_content_without_pypdf2(self)`** (方法, 行: 153)
    ```
    测试在没有 PyPDF2 的情况下验证 PDF 内容
    ```
*   **`test_calculate_file_hash(self)`** (方法, 行: 162)
    ```
    测试文件哈希计算功能
    ```
*   **`test_calculate_file_hash_with_nonexistent_file(self)`** (方法, 行: 184)
    ```
    测试计算不存在文件的哈希值
    ```
*   **`test_save_uploaded_file(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_create, mock_hash, mock_validate)`** (方法, 行: 199)
    ```
    测试保存上传文件功能
    ```
*   **`test_save_uploaded_file_with_pdf_validation(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_create, mock_hash, mock_validate_pdf, mock_validate)`** (方法, 行: 263)
    ```
    测试保存上传文件时进行 PDF 验证
    ```
*   **`test_save_uploaded_file_with_invalid_pdf(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_remove, mock_create, mock_validate_pdf, mock_validate)`** (方法, 行: 305)
    ```
    测试保存无效 PDF 文件
    ```
*   **`test_save_uploaded_file_with_nonexistent_user(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_user_get, mock_create, mock_hash, mock_validate)`** (方法, 行: 339)
    ```
    测试保存文件时用户不存在的情况
    ```
*   **`test_save_uploaded_file_with_db_error(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_remove, mock_create, mock_hash, mock_validate)`** (方法, 行: 390)
    ```
    测试保存文件时数据库错误的情况
    ```

## `test_suite/unit/archive_records/base_version_test.py`

*   **`setUp(self)`** (方法, 行: 13)
    ```
    无文档字符串
    ```
*   **`_create_initial_version(self, record)`** (方法, 行: 27)
    ```
    创建初始版本记录

Args:
    record: 档案记录实例
    
Returns:
    RecordChangeLog: 创建的版本记录
    ```
*   **`_create_update_version(self, record, version_number, changes, reason)`** (方法, 行: 74)
    ```
    创建更新版本记录

Args:
    record: 档案记录实例
    version_number: 版本号
    changes: 字段变更字典 {字段名: 新值}
    reason: 变更原因
    
Returns:
    RecordChangeLog: 创建的版本记录
    ```

## `test_suite/unit/archive_records/test_services.py`

*   **`setUp(self)`** (方法, 行: 20)
    ```
    无文档字符串
    ```
*   **`tearDown(self)`** (方法, 行: 28)
    ```
    清理测试环境，删除临时文件
    ```
*   **`_create_test_file(self, data, file_name)`** (方法, 行: 37)
    ```
    创建用于测试的Excel文件（仅用于测试）
    ```
*   **`test_real_file_import(self)`** (方法, 行: 54)
    ```
    测试实际Excel文件导入
    ```
*   **`test_error_handling(self)`** (方法, 行: 118)
    ```
    测试错误处理
    ```
*   **`test_unified_number_generation(self)`** (方法, 行: 123)
    ```
    测试导入时自动生成统一编号
    ```
*   **`test_smart_update_strategy(self)`** (方法, 行: 142)
    ```
    测试智能更新策略
    ```
*   **`test_skip_strategy(self)`** (方法, 行: 193)
    ```
    测试跳过策略
    ```
*   **`test_debug_full_import_process(self)`** (方法, 行: 197)
    ```
    调试完整导入过程
    ```
*   **`setUp(self)`** (方法, 行: 267)
    ```
    无文档字符串
    ```
*   **`test_update_archive_status(self)`** (方法, 行: 279)
    ```
    测试更新档案状态
    ```
*   **`test_empty_unified_codes(self)`** (方法, 行: 305)
    ```
    测试空的统一编号列表
    ```

## `test_suite/unit/archive_records/test_version_compare.py`

*   **`setUp(self)`** (方法, 行: 16)
    ```
    无文档字符串
    ```
*   **`test_version_compare_api(self)`** (方法, 行: 54)
    ```
    测试版本比较API
    ```
*   **`test_consecutive_versions_compare(self)`** (方法, 行: 100)
    ```
    测试相邻版本比较
    ```

## `test_suite/unit/archive_records/test_version_history.py`

*   **`setUp(self)`** (方法, 行: 21)
    ```
    无文档字符串
    ```
*   **`test_record_initial_version(self)`** (方法, 行: 36)
    ```
    测试记录初始创建时的版本记录
    ```
*   **`test_record_update_version(self)`** (方法, 行: 87)
    ```
    测试记录更新时的版本记录
    ```
*   **`test_api_record_history(self)`** (方法, 行: 112)
    ```
    测试记录历史API
    ```
*   **`test_api_version_compare(self)`** (方法, 行: 133)
    ```
    测试版本比较API
    ```
*   **`test_api_version_rollback(self)`** (方法, 行: 148)
    ```
    测试版本回滚API
    ```
*   **`test_excel_import_versioning(self)`** (方法, 行: 178)
    ```
    测试Excel导入时的版本记录
    ```
*   **`test_version_numbering(self)`** (方法, 行: 197)
    ```
    测试版本号自动递增功能
    ```
*   **`test_unified_number_sync(self)`** (方法, 行: 264)
    ```
    测试统一编号始终与委托编号同步
    ```
*   **`setUp(self)`** (方法, 行: 338)
    ```
    测试前准备
    ```
*   **`test_complete_workflow(self)`** (方法, 行: 357)
    ```
    测试完整工作流：创建->更新->查看历史->比较->回滚
    ```

## `test_suite/unit/archive_records/test_version_rollback.py`

*   **`setUp(self)`** (方法, 行: 17)
    ```
    无文档字符串
    ```
*   **`test_rollback_to_previous_version(self)`** (方法, 行: 56)
    ```
    测试回滚到上一个版本
    ```
*   **`test_rollback_with_commission_number_change(self)`** (方法, 行: 77)
    ```
    测试回滚委托编号同时更新统一编号
    ```
*   **`test_rollback_creates_proper_version(self)`** (方法, 行: 107)
    ```
    测试回滚操作创建正确的版本记录
    ```

## `test_suite/unit/archive_records/test_views.py`

*   **`setUp(self)`** (方法, 行: 14)
    ```
    无文档字符串
    ```
*   **`test_excel_import(self)`** (方法, 行: 21)
    ```
    测试Excel文件导入
    ```

## `test_suite/unit/report_issuing/test_issue_form_service.py`

*   **`setUp(self)`** (方法, 行: 20)
    ```
    设置测试环境
    ```
*   **`test_create_issue_form(self)`** (方法, 行: 52)
    ```
    测试创建发放单
    ```
*   **`test_add_form_items(self)`** (方法, 行: 78)
    ```
    测试添加发放单条目
    ```
*   **`test_add_form_items_with_different_formats(self)`** (方法, 行: 111)
    ```
    测试使用不同格式添加发放单条目
    ```
*   **`test_add_form_items_error_handling(self)`** (方法, 行: 139)
    ```
    测试添加发放单条目的错误处理
    ```
*   **`test_remove_form_items(self)`** (方法, 行: 156)
    ```
    测试移除发放单条目
    ```
*   **`test_status_transitions(self)`** (方法, 行: 182)
    ```
    测试状态转换
    ```
*   **`test_delete_issue_form(self)`** (方法, 行: 237)
    ```
    测试删除发放单
    ```
*   **`test_get_available_archive_records(self)`** (方法, 行: 290)
    ```
    测试获取可用于发放的档案记录
    ```

## `test_suite/unit/report_issuing/test_issue_record_service.py`

*   **`setUp(self)`** (方法, 行: 19)
    ```
    设置测试环境
    ```
*   **`test_create_issue_record(self)`** (方法, 行: 59)
    ```
    测试创建发放记录
    ```
*   **`test_create_duplicate_record(self)`** (方法, 行: 90)
    ```
    测试创建重复发放记录的错误处理
    ```
*   **`test_update_issue_record(self)`** (方法, 行: 106)
    ```
    测试更新发放记录
    ```
*   **`test_delete_issue_record(self)`** (方法, 行: 146)
    ```
    测试删除发放记录
    ```
*   **`test_handle_form_deleted_records(self)`** (方法, 行: 176)
    ```
    测试处理发放单删除时关联的发放记录
    ```
*   **`test_get_archive_record_issue_status(self)`** (方法, 行: 238)
    ```
    测试获取档案记录的发放状态
    ```
*   **`test_determine_actual_issue_type(self)`** (方法, 行: 292)
    ```
    测试确定实际发放类型
    ```

## `test_suite/utils/test_helpers.py`

*   **`get_test_file_path(category: str, filename: str) -> str`** (函数, 行: 4)
    ```
    获取测试文件路径
    ```

## `test_suite/utils/test_settings.py`

*   **`apply_test_settings()`** (函数, 行: 23)
    ```
    将测试配置应用到Django设置对象
应在测试setup函数中调用
    ```
*   **`cleanup_test_files()`** (函数, 行: 33)
    ```
    测试完成后清理临时文件
应在测试tearDown函数中调用
    ```