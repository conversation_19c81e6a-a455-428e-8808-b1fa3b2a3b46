# 操作文档：大型PDF处理效率提升计划

## 📋 变更摘要

**目的**: 提高大型PDF文件的处理效率，缩短处理时间
**范围**: PDF处理架构、数据模型和任务协调机制
**关联问题**: #AFM-32 大型PDF处理效率提升

## 🔧 操作步骤

### 📊 OP-001: 分析当前PDF处理流程与性能瓶颈

**前置条件**: 已有基础PDF处理流程能正常工作，且任务并发处理机制已修复
**操作**:

1. 分析当前PDF处理流程中的主要时间消耗点
2. 识别可并行化的处理环节
3. 评估现有架构的可扩展性
4. 确定并行处理架构的设计方向
**后置条件**: 明确系统瓶颈和并行化策略

### ✏️ OP-002: 设计SubTask数据模型

**前置条件**: 已完成处理流程分析，确定了需要跟踪的子任务状态和参数
**操作**:

1. 设计`SubTask`数据模型，包括必要的字段:
   - `subtask_id`: 子任务唯一标识
   - `parent_task`: 与主任务的外键关联
   - `start_page`和`end_page`: 页面范围
   - `status`: 子任务处理状态
   - `result_data`: 处理结果
   - `error_message`: 错误信息
   - 时间戳字段
2. 确保数据模型设计支持状态查询和子任务重试
3. 设计数据库索引以提高查询效率
**后置条件**: 子任务数据模型设计完成，准备实施数据库迁移

### ✏️ OP-003: 实现Celery Chord架构

**前置条件**: 已确定使用Celery Chord进行子任务协调
**操作**:

1. 创建子任务处理函数`process_pdf_subtask`
2. 修改主任务函数`process_pdf_task`，实现任务拆分逻辑
3. 使用Celery Chord组织子任务并设置回调函数
4. 实现回调函数处理子任务结果汇总
**后置条件**: 并行处理基础架构实现完成

### ✏️ OP-004: 开发任务拆分策略

**前置条件**: 并行处理架构已搭建
**操作**:

1. 实现基于页面数量的自动拆分算法
2. 开发基于内容特征（如统一编号）的智能拆分算法
3. 增加拆分策略配置选项（用户可选或系统自动判断）
**后置条件**: 任务拆分策略实现完成

### ✏️ OP-005: 实现子任务结果合并机制

**前置条件**: 子任务处理和数据模型已实现
**操作**:

1. 开发子任务处理结果验证逻辑
2. 实现子任务处理报告合并功能
3. 开发统一的主任务成功/失败判定逻辑
**后置条件**: 子任务结果合并机制实现完成

### 🧪 OP-006: 测试并行处理效果

**前置条件**: 并行处理架构已实现完成
**操作**:

1. 设计性能测试方案（不同页数、不同拆分策略）
2. 收集性能测试数据，与串行处理对比
3. 测试边缘情况（如单页PDF、超大PDF）
**后置条件**: 确认并行处理方案有效性和性能提升情况

## 📝 实施计划详情

### 子任务数据模型设计

```python
class SubTask(models.Model):
    """PDF分割子任务"""
    subtask_id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    parent_task = models.ForeignKey('ProcessingTask', on_delete=models.CASCADE, related_name='subtasks')
    start_page = models.IntegerField(help_text="子任务处理的起始页码")
    end_page = models.IntegerField(help_text="子任务处理的结束页码")
    part_index = models.IntegerField(help_text="在原文档中的部分索引")
    status = models.CharField(
        max_length=40, 
        choices=[
            ('pending', '待处理'),
            ('processing', '处理中'),
            ('completed', '已完成'),
            ('failed', '失败'),
            ('retrying', '重试中')
        ], 
        default='pending'
    )
    unified_number = models.CharField(max_length=100, blank=True, null=True, help_text="识别出的统一编号")
    result_data = models.JSONField(null=True, blank=True, help_text="处理结果数据")
    error_message = models.TextField(blank=True, null=True, help_text="错误信息")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "处理子任务"
        verbose_name_plural = verbose_name
        ordering = ['part_index']
        indexes = [
            models.Index(fields=['parent_task', 'status']),
        ]
```

### Celery Chord实现思路

```python
@shared_task(bind=True)
def process_pdf_task(self, task_id):
    """主任务：PDF处理协调器"""
    with transaction.atomic():
        task = ProcessingTask.objects.select_for_update().select_related('file').get(task_id=task_id)
        
        if task.status not in ['queued', 'failed']:
            return {'success': True, 'message': f'Task {task_id} already processed or in progress.'}
            
        task.status = 'processing'
        task.save(update_fields=['status'])
    
    # 获取PDF信息和拆分策略
    pdf_path = task.file.file_path
    pdf_service = PdfProcessingService()
    
    # 进行文档分析获取分割点
    result_dto = pdf_service.process_pdf_for_splitting_info(pdf_path, target_text)
    
    # 根据分割点创建子任务
    part_ranges = pdf_utils.calculate_part_ranges(result_dto.split_points, pdf_info)
    
    # 创建子任务
    subtask_ids = []
    for i, part_range in enumerate(part_ranges):
        start_page, end_page = part_range
        unified_number = result_dto.unified_numbers[i] if i < len(result_dto.unified_numbers) else None
        
        subtask = SubTask.objects.create(
            parent_task=task,
            start_page=start_page,
            end_page=end_page,
            part_index=i,
            unified_number=unified_number
        )
        subtask_ids.append(subtask.subtask_id)
    
    # 创建Celery Chord
    subtasks = [process_pdf_subtask.s(str(subtask_id)) for subtask_id in subtask_ids]
    callback = finalize_pdf_processing.s(task_id=task_id)
    
    chord(subtasks)(callback)
    
    return {'success': True, 'message': 'Task split into subtasks', 'subtask_count': len(subtask_ids)}

@shared_task(bind=True)
def process_pdf_subtask(self, subtask_id):
    """子任务：处理PDF的特定页面范围"""
    with transaction.atomic():
        subtask = SubTask.objects.select_for_update().get(subtask_id=subtask_id)
        
        if subtask.status != 'pending':
            return {'success': True, 'message': f'Subtask {subtask_id} already processed or in progress.'}
            
        subtask.status = 'processing'
        subtask.save(update_fields=['status'])
    
    try:
        parent_task = subtask.parent_task
        pdf_path = parent_task.file.file_path
        
        # 提取指定页面范围并处理
        temp_output_path = pdf_utils.create_temp_pdf_for_single_archive(
            pdf_path, 
            subtask.start_page, 
            subtask.end_page,
            subtask.unified_number
        )
        
        # 归档处理后的文件
        final_path = FileStorageService.archive_single_archive_pdf(
            temp_output_path, 
            subtask.unified_number, 
            parent_task.params.get('assigned_box_number')
        )
        
        # 更新数据库记录
        if subtask.unified_number:
            record_update_service.update_archive_record(
                subtask.unified_number,
                final_path,
                parent_task.params.get('assigned_box_number')
            )
        
        # 更新子任务状态
        subtask.status = 'completed'
        subtask.result_data = {
            'final_path': final_path,
            'unified_number': subtask.unified_number
        }
        subtask.save()
        
        return {
            'success': True, 
            'subtask_id': subtask_id,
            'unified_number': subtask.unified_number,
            'final_path': final_path
        }
        
    except Exception as e:
        logger.error(f"子任务处理失败: {subtask_id}, 错误: {str(e)}", exc_info=True)
        subtask.status = 'failed'
        subtask.error_message = str(e)
        subtask.save()
        
        return {
            'success': False, 
            'subtask_id': subtask_id,
            'error': str(e)
        }

@shared_task(bind=True)
def finalize_pdf_processing(self, subtask_results, task_id):
    """回调函数：汇总子任务结果"""
    with transaction.atomic():
        task = ProcessingTask.objects.select_for_update().get(task_id=task_id)
        
        # 生成汇总报告
        success_count = sum(1 for result in subtask_results if result.get('success', False))
        total_count = len(subtask_results)
        
        # 创建处理结果摘要
        summary = processing_report_utils.create_result_summary(
            task_id=task_id,
            success_count=success_count,
            total_count=total_count,
            details=subtask_results
        )
        
        # 更新主任务状态
        if success_count == total_count:
            task.status = 'completed'
        elif success_count > 0:
            task.status = 'completed_with_errors'
        else:
            task.status = 'failed'
            
        task.result_data = {
            'summary': summary,
            'subtask_results': subtask_results
        }
        task.save()
        
        return {
            'success': True,
            'task_id': task_id,
            'status': task.status,
            'success_count': success_count,
            'total_count': total_count
        }
```

## ✅ 预期成果

**性能提升目标**:
- 大型PDF（>50页）处理时间减少50%以上
- 系统整体吞吐量提高30%以上
- 处理超大PDF（>200页）的能力增强

**功能增强**:
- 更细粒度的任务状态跟踪
- 支持单个部分失败时其他部分仍能正常处理
- 为未来更复杂的任务调度和处理流程奠定基础

**系统优化**:
- 更高效地利用服务器资源
- 改进错误隔离能力，提高整体系统稳定性
- 为后续"错误处理优化"任务提供基础设施支持 