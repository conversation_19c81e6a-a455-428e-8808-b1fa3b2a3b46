# Operation Document: Apply `fetchWithRetry` to Multiple Service Methods

## 📋 Change Summary

**Purpose**: To enhance the robustness of API calls in `frontend/services/excel-import-service.ts` by applying the `fetchWithRetry` helper function to several GET and one idempotent POST methods.
**Scope**: `frontend/services/excel-import-service.ts`
**Associated**: Corresponds to task "一.2 会话管理与API优化" -> "请求优化" -> "[~] 完善错误处理与重试机制" in `remaining_excel_import_refactor_plan.md`.

## 🔧 Operation Steps

### 📊 OP-001: Identify Target Methods

**Precondition**: `fetchWithRetry` helper function is available in the service file.
**Operation**: Identified the following methods as suitable candidates for `fetchWithRetry` due to their nature (GET requests for data retrieval, or idempotent POST requests sensitive to network issues):
    - `getAnalysisProgress`
    - `getAnalysisResult`
    - `getExcelImportBatchList`
    - `getExcelImportBatchDetails`
    - `sendHeartbeat`
**Postcondition**: Target methods selected.

### ✏️ OP-002: Apply `fetchWithRetry`

**Precondition**: Target methods identified.
**Operation**: Modified each of the selected methods to replace direct `fetch` calls with `fetchWithRetry`. Ensured that the original `options` (method, headers, body) were correctly passed to `fetchWithRetry`.
**Postcondition**: `fetchWithRetry` has been applied to the selected service methods.

## 📝 Change Details

### CH-001: Modify `getAnalysisProgress`

**File**: `frontend/services/excel-import-service.ts`
**Change**: Replaced `fetch` with `fetchWithRetry`.
**Rationale**: `getAnalysisProgress` is a GET request; retrying on temporary network/server issues improves reliability.

### CH-002: Modify `getAnalysisResult`

**File**: `frontend/services/excel-import-service.ts`
**Change**: Replaced `fetch` with `fetchWithRetry`.
**Rationale**: `getAnalysisResult` is a GET request; retrying improves reliability.

### CH-003: Modify `getExcelImportBatchList`

**File**: `frontend/services/excel-import-service.ts`
**Change**: Replaced `fetch` with `fetchWithRetry`.
**Rationale**: `getExcelImportBatchList` is a GET request; retrying improves reliability.

### CH-004: Modify `getExcelImportBatchDetails`

**File**: `frontend/services/excel-import-service.ts`
**Change**: Replaced `fetch` with `fetchWithRetry`.
**Rationale**: `getExcelImportBatchDetails` is a GET request; retrying improves reliability.

### CH-005: Modify `sendHeartbeat`

**File**: `frontend/services/excel-import-service.ts`
**Change**: Replaced `fetch` with `fetchWithRetry`.
**Rationale**: `sendHeartbeat` is a POST request but is idempotent and crucial for session management. Retrying on temporary failures is beneficial.

## ✅ Verification Results

**Method**: Code modification and review.
**Results**: `fetchWithRetry` has been successfully integrated into the specified methods.
**Problems**: None directly from this change. The `fetchWithRetry` function itself could be moved to a more generic utils module in the future if used across multiple services.
**Solutions**: For now, its placement within `excel-import-service.ts` is acceptable as it's currently only used there.
