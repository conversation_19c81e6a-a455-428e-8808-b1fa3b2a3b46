# Operation Document: Add Field Length Validation for Excel Import

## 📋 Change Summary

**Purpose**: 解决导入Excel文件过程中出现的`value too long for type character varying(200)`错误，通过在数据库插入前添加字段长度验证。
**Scope**: 修改了`archive_records/services/excel_import.py`中的`_process_row`方法。
**Associated**: 用户报告的错误："value too long for type character varying(200)"，数据库字段长度超限问题。

## 🔧 Operation Steps

### 📊 OP-001: 分析新错误日志和根本原因

**Precondition**: 虽然之前的事务错误处理修复已正确实现，但用户仍然报告了字段长度相关的数据库错误。
**Operation**:
    1. 分析服务器日志，确认错误是由于某个字符串字段值超过了数据库列的最大长度200字符导致。
    2. 确认现有的事务错误处理逻辑已正确工作：DataError被正确捕获并立即传播，没有尝试在已损坏的事务中重试。
    3. 识别需要添加数据验证逻辑，在尝试插入到数据库之前检查字段值是否符合数据库列长度限制。
**Postcondition**: 确定需要在`_process_row`方法中添加字段长度验证，提早检测并报告超长字段，而不是让其导致数据库错误。

### ✏️ OP-002: 实现字段长度验证

**Precondition**: `_process_row`方法负责处理和验证每行数据，但缺少字段长度验证。
**Operation**:
    1. 添加`string_field_limits`字典，定义各个字符串字段的最大长度限制，与数据库模型一致。
    2. 在字符串字段处理逻辑中添加长度验证：
       - 检查字段值长度是否超过限制
       - 如果超长，找出字段的中文名称用于错误提示
       - 记录详细的警告日志
       - 抛出包含具体信息的ValueError，指明哪个字段超长以及最大允许长度
    3. 提供字段值截断的代码注释选项，如果业务上允许的话可以启用
**Postcondition**: `_process_row`方法现在能够在早期检测字段长度超限问题，并提供详细的错误信息，避免数据库DataError。

## 📝 Change Details

### CH-001: 添加字段长度验证逻辑

**File**: `archive_records/services/excel_import.py`
**Method**: `_process_row`
**Before**:

```python
# 处理字符串字段时没有长度验证
# 字符串字段处理
str_value = str(value).strip() if value is not None else None

# 特殊处理委托编号
if field == "commission_number" and str_value:
    # 规范化委托编号 - 去除前后空格
    str_value = str_value.strip()

if field in required_fields.values() and not str_value:
    cn_field = en_to_cn.get(field)
    if cn_field:
        raise ValueError(f"必填字段不能为空: {cn_field}")
processed_data[field] = str_value
```

**After**:

```python
# 字段长度限制 - 根据数据库模型定义添加
string_field_limits = {
    "commission_number": 100,
    "sample_number": 100,
    # ... 其他字段限制 ...
    "payment_status": 50,
    "price_adjustment_status": 50,
}

# 字符串字段处理
str_value = str(value).strip() if value is not None else None

# 特殊处理委托编号
if field == "commission_number" and str_value:
    # 规范化委托编号 - 去除前后空格
    str_value = str_value.strip()

# 检查字符串字段长度是否超过限制
if str_value and field in string_field_limits:
    max_length = string_field_limits[field]
    if len(str_value) > max_length:
        # 获取字段的中文名称用于错误提示
        field_cn = None
        for cn, en in self.field_mapping.items():
            if en == field:
                field_cn = cn
                break
        cn_display = f"{field_cn}({field})" if field_cn else field
        
        # 记录超长字段详情用于日志
        logger.warning(f"字段值超过最大长度: {cn_display}，最大长度: {max_length}，实际长度: {len(str_value)}，值: '{str_value[:50]}...'")
        
        # 根据业务需求，可以选择截断或抛出错误
        # 选项1: 截断过长的值（如果业务上允许）
        # str_value = str_value[:max_length]
        # logger.warning(f"已截断字段 {cn_display} 的值至 {max_length} 字符")
        
        # 选项2: 抛出错误（如果需要用户干预）
        truncated_value = str_value[:47] + "..." if len(str_value) > 50 else str_value
        raise ValueError(f"字段 {cn_display} 的值超过最大长度 {max_length}：'{truncated_value}'")

if field in required_fields.values() and not str_value:
    cn_field = en_to_cn.get(field)
    if cn_field:
        raise ValueError(f"必填字段不能为空: {cn_field}")
processed_data[field] = str_value
```

**Rationale**: 在数据插入数据库前添加字段长度验证，可以更早地检测到问题，并提供更具描述性的错误信息，而不是让用户面对难以理解的数据库错误。这样用户可以立即知道哪个字段超出了长度限制，以及它应该多长。我们提供了两种处理选项：抛出错误（默认）或自动截断过长的值（注释掉，可根据业务需求启用）。

## ✅ Verification Results

**Method**: 代码审查和对错误处理逻辑的分析。
**Results**:

1. 修改代码现在会在试图插入超长字段之前检测到问题
2. 错误消息现在会包含更多有用信息：字段名称（中英文）、最大允许长度、超长值的示例
3. 用户会收到更友好的错误消息，而不是晦涩的数据库错误
**Problems**: 如果将来有新增的字符串字段，需要更新`string_field_limits`字典。
**Solutions**: 考虑将来从数据库模型定义中自动生成字段限制，而不是硬编码在代码中。
