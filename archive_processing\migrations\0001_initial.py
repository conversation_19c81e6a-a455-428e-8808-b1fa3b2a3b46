# Generated by Django 5.1.8 on 2025-05-16 12:33

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='文件ID')),
                ('original_name', models.CharField(max_length=255, verbose_name='原始文件名')),
                ('saved_path', models.CharField(max_length=1024, verbose_name='保存路径')),
                ('file_size', models.BigIntegerField(verbose_name='文件大小')),
                ('assigned_box_number', models.CharField(blank=True, db_index=True, max_length=100, null=True, verbose_name='分配盒号')),
                ('upload_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('file_hash', models.CharField(blank=True, db_index=True, max_length=64, null=True, verbose_name='文件SHA256哈希')),
                ('uploader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='上传者')),
            ],
            options={
                'verbose_name': '上传的文件',
                'verbose_name_plural': '上传的文件',
                'ordering': ['-upload_time'],
            },
        ),
        migrations.CreateModel(
            name='ProcessingTask',
            fields=[
                ('task_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='任务ID')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('queued', '已入队'), ('processing', '处理中'), ('processed_pending_lookup', '处理完成待查找'), ('looking_up', '查找关联中'), ('lookup_successful_pending_boxing', '查找成功待分配盒号'), ('lookup_partially_failed', '部分查找失败'), ('lookup_failed', '查找失败'), ('assigning_box', '分配盒号中'), ('completed', '已完成'), ('failed', '处理失败'), ('retrying_lookup', '重试查找中')], db_index=True, default='pending', max_length=40, verbose_name='任务状态')),
                ('progress', models.IntegerField(default=0, verbose_name='处理进度')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('result_data', models.JSONField(blank=True, null=True, verbose_name='结果数据(文件-编号映射)')),
                ('task_type', models.CharField(db_index=True, default='pdf_processing', max_length=50, verbose_name='任务类型')),
                ('processing_params', models.JSONField(blank=True, null=True, verbose_name='处理参数')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='重试次数')),
                ('file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='archive_processing.uploadedfile', verbose_name='关联文件')),
            ],
            options={
                'verbose_name': '处理任务',
                'verbose_name_plural': '处理任务',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PDFChunkTask',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('pdf_path', models.CharField(help_text='原始PDF文件路径', max_length=512)),
                ('start_page', models.IntegerField(help_text='起始页码（包含）')),
                ('end_page', models.IntegerField(help_text='结束页码（包含）')),
                ('chunk_index', models.IntegerField(help_text='块索引号，用于排序')),
                ('status', models.CharField(choices=[('pending', '等待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '处理失败')], default='pending', max_length=32)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('result_data', models.JSONField(blank=True, help_text='子任务处理结果数据', null=True)),
                ('error', models.TextField(blank=True, help_text='处理错误信息', null=True)),
                ('parent_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chunks', to='archive_processing.processingtask')),
            ],
            options={
                'ordering': ['chunk_index'],
                'indexes': [models.Index(fields=['parent_task', 'status'], name='archive_pro_parent__ca7b22_idx'), models.Index(fields=['status', 'created_at'], name='archive_pro_status_a8b52a_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['upload_time'], name='archive_pro_upload__44f247_idx'),
        ),
        migrations.AddIndex(
            model_name='processingtask',
            index=models.Index(fields=['created_at'], name='archive_pro_created_affe79_idx'),
        ),
        migrations.AddIndex(
            model_name='processingtask',
            index=models.Index(fields=['file', 'task_type'], name='archive_pro_file_id_7524b2_idx'),
        ),
    ]
