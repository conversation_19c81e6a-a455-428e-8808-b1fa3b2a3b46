"use client"

import React, { useState, useMemo, use<PERSON><PERSON>back, useRef, useEffect } from "react"
import { Search, Minus, Plus, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
// CHANGE: [2025-06-17] 引入自动列宽计算工具，替换固定列宽
import { getHeaderColumnWidth } from "@/utils/ag-grid-column-width-utils"

import { AgGridReact } from 'ag-grid-react'
import {
  ColDef,
  ICellRendererParams,
  SelectionChangedEvent,
  GridApi,
} from 'ag-grid-enterprise'
import agGridConfig from '@/lib/ag-grid-config'
import { DateRangeFilter } from "@/components/common/DateRangeFilter"

import {
  useReportListService,
  mapLedgerRecordToSelectedRecord,
  type SelectedRecord,
  type ReportListProps,
  type IssuableArchive,
  type RowSelectedEvent,
} from "@/hooks/domain/issue/useReportListService"
import { CustomHeaderCheckbox } from './CustomHeaderCheckbox'

// 自定义单元格渲染器 - 份数控制
const CopiesControlRenderer: React.FC<ICellRendererParams> = (params) => {
  const { data } = params
  const { handleCopiesChange, canAdjustCopies, isBatchProcessing } = params.context

  const value = data.issueCopies || 1
  const adjustable = canAdjustCopies(data)

  if (!adjustable) {
    return (
      <div className="text-center">
        {value}
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-2 justify-center">
      <Button
        variant="outline"
        size="icon"
        className="h-6 w-6"
        onClick={() => {
          handleCopiesChange(data.id, 1)
        }}
        disabled={(value || 1) <= 1 || isBatchProcessing}
      >
        <Minus className="h-3 w-3" />
      </Button>
      <span className="w-8 text-center">{value}</span>
      <Button
        variant="outline"
        size="icon"
        className="h-6 w-6"
        onClick={() => {
          handleCopiesChange(data.id, data.remainingCopies)
        }}
        disabled={(value || 1) >= data.remainingCopies || isBatchProcessing}
      >
        <Plus className="h-3 w-3" />
      </Button>
    </div>
  )
}

// 自定义单元格渲染器 - 状态标签
const StatusRenderer: React.FC<ICellRendererParams> = (params) => {
  const { getReportStatus, getLedgerRecordStatus } = params.context;

  if (!params.data) {
    return null;
  }

  // 根据数据类型选择合适的状态计算函数
  let distributionStatus: string;
  if (params.data.distributionCount !== undefined) {
    // 已选档案数据，使用getReportStatus
    distributionStatus = getReportStatus(params.data);
  } else {
    // 台账数据，使用getLedgerRecordStatus
    distributionStatus = getLedgerRecordStatus(params.data);
  }

  const isHistoricalOneCopy = (data: any): boolean => {
    if (!data) return false;
    if (data.firstDistribution) {
      return data.firstDistribution.copies === 1;
    }
    if (typeof data.firstIssueCopies !== 'undefined') {
      return data.firstIssueCopies === 1;
    }
    return false;
  };

  const showOneCopyBadge = isHistoricalOneCopy(params.data) && distributionStatus === "首发";

  return (
    <div className="flex items-center justify-center gap-1 h-full w-full">
      <Badge
        variant="outline"
        className={cn(
          "text-xs px-1.5 py-0.5",
          distributionStatus === "未发放" && "bg-gray-50 text-gray-700 border-gray-200",
          distributionStatus === "首发" && "bg-blue-50 text-blue-700 border-blue-200",
          distributionStatus === "二发" && "bg-green-50 text-green-700 border-green-200",
          distributionStatus === "未知状态" && "bg-red-50 text-red-700 border-red-200"
        )}
      >
        {distributionStatus}
      </Badge>

      {distributionStatus !== "未发放" && (
        <Badge
          variant="outline"
          className={cn(
            "text-xs px-1.5 py-0.5",
            showOneCopyBadge
              ? "bg-yellow-50 text-yellow-700 border-yellow-200"
              : "bg-green-50 text-green-700 border-green-200"
          )}
        >
          {showOneCopyBadge ? "1份" : "全部"}
        </Badge>
      )}
    </div>
  );
};

// 自定义单元格渲染器 - 操作按钮
const ActionsRenderer: React.FC<ICellRendererParams> = (params) => {
  const { removeRecord, isBatchProcessing } = params.context

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => removeRecord(params.data.id)}
      disabled={isBatchProcessing}
    >
      移除
    </Button>
  )
}

export function ReportList(props: ReportListProps) {
  const {
    selectedRecords,
    isEditable,
    setSelectedRecords,
  } = props

  const [reportsActiveTab, setReportsActiveTab] = useState("selected")
  const [isBatchProcessing, setIsBatchProcessing] = useState(false)
  const [batchTotalCopiesDialog, setBatchTotalCopiesDialog] = useState(false)
  const [batchTotalCopiesValue, setBatchTotalCopiesValue] = useState("")
  const [batchOperationType, setBatchOperationType] = useState<"issue" | "total">("issue")

  // 内部业务逻辑 - 移除记录
  const removeRecord = useCallback((id: string) => {
    const newRecords = selectedRecords.filter((record) => record.id !== id)
    setSelectedRecords(newRecords)
  }, [selectedRecords, setSelectedRecords])

  // 内部业务逻辑 - 修改份数
  const handleCopiesChange = useCallback((id: string, newCopies: number) => {
    const newRecords = selectedRecords.map(record => {
      if (record.id === id) {
        return { ...record, issueCopies: newCopies }
      }
      return record
    })
    setSelectedRecords(newRecords)
  }, [selectedRecords, setSelectedRecords])

  // 内部业务逻辑 - 批量修改份数
  const handleBatchCopiesChange = useCallback((recordIds: string[], type: "one" | "max") => {
    const newRecords = selectedRecords.map(record => {
      if (recordIds.includes(record.id)) {
        const newCopies = type === "one" ? 1 : record.remainingCopies
        return { ...record, issueCopies: newCopies }
      }
      return record
    })
    setSelectedRecords(newRecords)
  }, [selectedRecords, setSelectedRecords])

  // 内部业务逻辑 - 获取报告状态
  const getReportStatus = useCallback((record: SelectedRecord): string => {
    if (record.distributionCount === 0) {
      return "未发放"
    } else if (record.firstDistribution && !record.secondDistribution) {
      return "首发"
    } else if (record.secondDistribution) {
      return "二发"
    }
    return "未知状态"
  }, [])

  // 使用 ref 直接引用已选档案表格的 API，避免 DOM 查询
  const selectedRecordsGridRef = useRef<any>(null)

  const reportListServiceOptions = useMemo(() => ({
    onError: (error: Error) => {
      console.error('数据加载失败:', error)
    },
    onDataLoad: (count: number) => {
      console.log(`加载了 ${count} 条记录`)
    }
  }), [])

  const {
    searchQuery,
    setSearchQuery,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    datasource,
    onGridReady,
    getRowId,
    gridApi,
    selectCurrentPage,
    deselectCurrentPage,
    getCurrentPageSelectionStats,
    updateTotalCopies,
  } = useReportListService(reportListServiceOptions)

  // 自定义header checkbox现在有了选择功能

  // 修复服务器端行模型选择API错误：避免使用getSelectedNodes()，改用node.isSelected()
  const onSelectionChanged = useCallback((event: SelectionChangedEvent) => {
    const api = event.api

    // 对于服务器端行模型，我们需要使用不同的方法来处理选择
    // 我们将基于当前页面的选择状态来更新 selectedRecords
    setSelectedRecords((currentSelectedRecords: SelectedRecord[]) => {
      // 获取当前页面上所有行的数据和选择状态
      const currentPageRecords: { data: any; selected: boolean }[] = []

      api.forEachNode(node => {
        if (node.data) {
          currentPageRecords.push({
            data: node.data,
            selected: node.isSelected() || false
          })
        }
      })

      // 创建当前页面行ID的集合
      const currentPageRowIds = new Set(currentPageRecords.map(r => r.data.unifiedNumber))

      // 1. 保留不在当前页的已选记录
      const preservedRecords = currentSelectedRecords.filter(
        (record: SelectedRecord) => !currentPageRowIds.has(record.id)
      )

      // 2. 处理当前页面的选择状态
      const currentPageSelectedRecords = currentPageRecords
        .filter(r => r.selected)
        .map(r => mapLedgerRecordToSelectedRecord(r.data))
        .filter((record: SelectedRecord) => !preservedRecords.some((p: SelectedRecord) => p.id === record.id)) // 避免重复

      // 3. 合并保留的记录和当前页面新选中的记录
      return [...preservedRecords, ...currentPageSelectedRecords]
    })
  }, [setSelectedRecords])

  // 双向同步：当selectedRecords变化时，同步台账表勾选状态
  // 这解决了用户在已选档案中移除记录时，台账表勾选状态不更新的问题
  useEffect(() => {
    if (gridApi) {
      const selectedIds = new Set(selectedRecords.map(r => r.id));
      
      // 遍历所有台账表节点，同步勾选状态
      gridApi.forEachNode(node => {
        if (node.data && node.data.unifiedNumber) {
          const shouldBeSelected = selectedIds.has(node.data.unifiedNumber);
          // 只有当前勾选状态与目标状态不一致时才更新
          if (node.isSelected() !== shouldBeSelected) {
            // 第二个参数false避免触发onSelectionChanged事件，防止无限循环
            node.setSelected(shouldBeSelected, false);
          }
        }
      });
    }
  }, [selectedRecords, gridApi])

  const totalCopies = selectedRecords.reduce((sum: number, record: SelectedRecord) => sum + (record.issueCopies || 1), 0)

  const canAdjustCopies = (record: any) => {
    if (record.distributionCount > 0) return false
    return true
  }

  const getLedgerRecordStatus = useCallback((record: any): string => {
    if (!record || typeof record.firstIssueCopies === 'undefined' || typeof record.secondIssueCopies === 'undefined') {
      return '未知状态';
    }
    if (record.secondIssueCopies > 0) {
      return '二发';
    }
    if (record.firstIssueCopies > 0) {
      return '首发';
    }
    return '未发放';
  }, []);

  // 批量设置发放份数
  const handleBatchSetIssueCopies = (type: "one" | "max") => {
    if (isBatchProcessing) return

    setIsBatchProcessing(true)
    try {
      // 使用 ref 直接访问 AG Grid API，更可靠
      const gridApi = selectedRecordsGridRef.current
      if (gridApi) {
        const selectedNodes = gridApi.getSelectedNodes()
        const selectedIds = selectedNodes.map((node: any) => node.data.id)

        if (selectedIds.length > 0) {
          handleBatchCopiesChange(selectedIds, type)
        } else {
          console.warn("请先勾选要操作的记录")
          // 可以在这里添加更好的用户提示，比如 toast 通知
        }
      } else {
        console.error("无法访问表格API")
      }
    } catch (error) {
      console.error("批量操作失败:", error)
    } finally {
      setIsBatchProcessing(false)
    }
  }

  // 批量设置总份数
  const handleBatchSetTotalCopies = useCallback(async () => {
    if (isBatchProcessing || !batchTotalCopiesValue) return

    const newTotal = parseInt(batchTotalCopiesValue)
    if (isNaN(newTotal) || newTotal <= 0) {
      console.warn('总份数必须是正整数')
      return
    }

    setIsBatchProcessing(true)
    try {
      const gridApi = selectedRecordsGridRef.current
      if (gridApi) {
        const selectedNodes = gridApi.getSelectedNodes()
        const selectedRecordsData = selectedNodes.map((node: any) => node.data)

        if (selectedRecordsData.length > 0) {
          // 验证所有选中记录的总份数是否可以设置
          const invalidRecords = selectedRecordsData.filter((record: any) => {
            const distributedCopies = (record.distributionCount || 0)
            return newTotal < distributedCopies
          })

          if (invalidRecords.length > 0) {
            console.warn(`有 ${invalidRecords.length} 条记录的新总份数小于已发放份数，无法设置`)
            return
          }

          // 调用批量更新API
          const archiveIds = selectedRecordsData.map((record: any) => record.id)
          const result = await updateTotalCopies(archiveIds, newTotal)

          if (result.success) {
            console.log('✅ 批量更新总份数成功')
            // 更新本地状态
            setSelectedRecords(prev => prev.map((record: SelectedRecord) => {
              if (archiveIds.includes(record.id)) {
                return { 
                  ...record, 
                  totalCopies: newTotal, 
                  remainingCopies: newTotal - (record.distributionCount || 0) 
                }
              }
              return record
            }))
            
            // 关闭弹窗
            setBatchTotalCopiesDialog(false)
            setBatchTotalCopiesValue("")
          } else {
            console.error('❌ [EDIT] 已选档案总份数更新失败:', result.errors)
            // 失败时恢复本地状态
            setSelectedRecords(prev => prev.map((record: SelectedRecord) => {
              if (archiveIds.includes(record.id)) {
                return { 
                  ...record, 
                  totalCopies: prev.find(r => r.id === record.id)?.totalCopies || 0,
                  remainingCopies: prev.find(r => r.id === record.id)?.remainingCopies || 0
                }
              }
              return record
            }))
          }
        } else {
          console.warn("请先勾选要操作的记录")
        }
      } else {
        console.error("无法访问表格API")
      }
    } catch (error) {
      console.error("批量操作失败:", error)
    } finally {
      setIsBatchProcessing(false)
    }
  }, [batchTotalCopiesValue, updateTotalCopies, setSelectedRecords])

  // 已选档案表格的行选择配置：根据批量操作类型动态调整选择逻辑
  const selectedRecordsRowSelection = useMemo(() => {
    return {
      mode: 'multiRow' as const,
      isRowSelectable: (rowNode: any) => {
        if (!rowNode.data) return false
        
        // 根据批量操作类型决定选择逻辑
        if (batchOperationType === 'issue') {
          // 发放份数：只有可以调整份数的记录才能被勾选（原始逻辑）
          return canAdjustCopies(rowNode.data)
        } else {
          // 总份数：所有记录都可以被勾选
          return true
        }
      },
    };
  }, [canAdjustCopies, batchOperationType]);

  // 已选档案表格的选择列配置：固定在左侧
  const selectedRecordsSelectionColumnDef = useMemo(() => ({
    width: 50,
    pinned: 'left' as const,
    suppressHeaderMenuButton: true,
    resizable: false,
  }), []);

  // 已选档案表总份数编辑事件处理
  const handleSelectedTotalCopiesEdit = useCallback(async (params: any) => {
    const { data, newValue, oldValue } = params
    
    // 验证新值
    if (!newValue || newValue <= 0 || !Number.isInteger(Number(newValue))) {
      console.warn('总份数必须是正整数')
      // 恢复原值
      params.node.setDataValue(params.colDef.field, oldValue)
      return
    }
    
    const archiveId = data.id
    const newTotal = Number(newValue)
    
    console.log('🔄 [EDIT] 已选档案总份数编辑', { archiveId, oldValue, newValue: newTotal })
    
    try {
      // 1. 先更新本地状态，提供即时反馈
      setSelectedRecords(prev => prev.map(record => 
        record.id === archiveId 
          ? { ...record, totalCopies: newTotal, remainingCopies: newTotal - (record.distributionCount || 0) }
          : record
      ))
      
      // 2. 调用Hook层的事务协调函数更新后台
      const result = await updateTotalCopies(archiveId, newTotal)
      
      if (result.success) {
        console.log('✅ [EDIT] 已选档案总份数更新成功，台账表将自动刷新')
        // 成功时不需要额外操作，updateTotalCopies已经会刷新台账表
      } else {
        console.error('❌ [EDIT] 已选档案总份数更新失败:', result.errors)
        // 失败时恢复本地状态
        setSelectedRecords(prev => prev.map(record => 
          record.id === archiveId 
            ? { ...record, totalCopies: oldValue, remainingCopies: oldValue - (record.distributionCount || 0) }
            : record
        ))
      }
    } catch (error) {
      console.error('❌ [EDIT] 已选档案总份数更新异常:', error)
      // 异常时恢复本地状态
      setSelectedRecords(prev => prev.map(record => 
        record.id === archiveId 
          ? { ...record, totalCopies: oldValue, remainingCopies: oldValue - (record.distributionCount || 0) }
          : record
      ))
    }
  }, [updateTotalCopies, setSelectedRecords])

  // CHANGE: [2025-06-17] 已选档案表格列定义：使用自动列宽计算替换固定宽度
  const columnDefs = useMemo<ColDef[]>(() => [
    { headerName: "统一编号", field: "id", width: getHeaderColumnWidth("统一编号"), pinned: 'left' },
    { headerName: "样品编号", field: "sampleNumber", width: getHeaderColumnWidth("样品编号") },
    { headerName: "委托单位", field: "clientCompany", width: getHeaderColumnWidth("委托单位") },
    { headerName: "委托人", field: "clientContact", width: getHeaderColumnWidth("委托人") },
    { headerName: "委托日期", field: "clientDate", width: getHeaderColumnWidth("委托日期", { isSpecialCase: true, specialCaseMinWidth: 120 }) },
    { headerName: "工程编号", field: "projectNumber", width: getHeaderColumnWidth("工程编号") },
    { headerName: "工程名称", field: "projectName", width: getHeaderColumnWidth("工程名称") },
    { headerName: "工程部位", field: "projectLocation", width: getHeaderColumnWidth("工程部位") },
    { 
      headerName: "总份数", 
      field: "totalCopies", 
      width: getHeaderColumnWidth("总份数", { minWidth: 80 }), 
      pinned: 'left',
      filter: 'agNumberColumnFilter',
      editable: true,
      cellEditor: 'agNumberCellEditor',
      cellEditorParams: {
        min: 1,
        max: 999,
        precision: 0
      },
      onCellValueChanged: handleSelectedTotalCopiesEdit
    },
    { headerName: "可发放份数", field: "remainingCopies", width: getHeaderColumnWidth("可发放份数"), filter: 'agNumberColumnFilter' },
    {
      headerName: "发放份数",
      width: getHeaderColumnWidth("发放份数"),
      pinned: 'left',
      cellRenderer: CopiesControlRenderer,
      filter: false,
      sortable: false
    },
    {
      headerName: "状态",
      field: "status",
      width: getHeaderColumnWidth("状态", { isSpecialCase: true, specialCaseMinWidth: 120 }),
      pinned: 'left',
      cellRenderer: StatusRenderer,
      cellStyle: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
      filter: false,
      sortable: false
    },
    { headerName: "第一次发放时间", field: "firstDistribution.time", width: getHeaderColumnWidth("第一次发放时间", { isSpecialCase: true, specialCaseMinWidth: 160 }), valueGetter: params => params.data.firstDistribution?.time || "-" },
    { headerName: "发放人", field: "firstDistribution.distributor", width: getHeaderColumnWidth("发放人"), valueGetter: params => params.data.firstDistribution?.distributor || "-" },
    { headerName: "领取人", field: "firstDistribution.recipient", width: getHeaderColumnWidth("领取人"), valueGetter: params => params.data.firstDistribution?.recipient || "-" },
    { headerName: "领取人电话", field: "firstDistribution.recipientPhone", width: getHeaderColumnWidth("领取人电话"), valueGetter: params => params.data.firstDistribution?.recipientPhone || "-" },
    { headerName: "第二次发放时间", field: "secondDistribution.time", width: getHeaderColumnWidth("第二次发放时间", { isSpecialCase: true, specialCaseMinWidth: 160 }), valueGetter: params => params.data.secondDistribution?.time || "-" },
    { headerName: "发放人(2)", field: "secondDistribution.distributor", width: getHeaderColumnWidth("发放人(2)"), valueGetter: params => params.data.secondDistribution?.distributor || "-" },
    { headerName: "领取人(2)", field: "secondDistribution.recipient", width: getHeaderColumnWidth("领取人(2)"), valueGetter: params => params.data.secondDistribution?.recipient || "-" },
    { headerName: "领取人电话(2)", field: "secondDistribution.recipientPhone", width: getHeaderColumnWidth("领取人电话(2)"), valueGetter: params => params.data.secondDistribution?.recipientPhone || "-" },
    {
      headerName: "操作",
      field: "actions",
      width: getHeaderColumnWidth("操作", { minWidth: 80 }),
      pinned: 'right',
      cellRenderer: ActionsRenderer,
      sortable: false,
      filter: false,
      resizable: false
    }
  ], [])

  // 提前定义 isRowSelectable，因为在 recordsColumnDefs 中需要使用
  const isRowSelectable = useCallback((params: any) => {
    if (!params.data) return false;
    const firstCopies = params.data.firstIssueCopies || 0;
    const secondCopies = params.data.secondIssueCopies || 0;
    const remainingCopies = params.data.totalIssueCopies - (firstCopies + secondCopies);
    return remainingCopies > 0;
  }, []);

  // CHANGE: [2025-06-17] 台账表格列定义：使用自动列宽计算替换固定宽度，并修复字段名为camelCase
  const recordsColumnDefs = useMemo<ColDef[]>(() => [
    {
      headerName: "统一编号", field: "unifiedNumber", width: getHeaderColumnWidth("统一编号"), pinned: 'left', filter: 'agTextColumnFilter',
    },
    { headerName: "样品编号", field: "sampleNumber", width: getHeaderColumnWidth("样品编号"), filter: 'agTextColumnFilter' },
    { headerName: "委托单位", field: "clientUnit", width: getHeaderColumnWidth("委托单位"), filter: 'agTextColumnFilter' },
    { headerName: "委托人", field: "clientName", width: getHeaderColumnWidth("委托人"), filter: 'agTextColumnFilter' },
    {
      headerName: "委托日期", field: "commissionDatetime", width: getHeaderColumnWidth("委托日期", { isSpecialCase: true, specialCaseMinWidth: 120 }),
      valueFormatter: params => params.value ? new Date(params.value).toLocaleDateString() : '-',
      filter: 'agDateColumnFilter',
      filterParams: {
        debounceMs: 500,
        suppressAndOrCondition: true,
      },
    },
    { headerName: "工程编号", field: "projectNumber", width: getHeaderColumnWidth("工程编号"), filter: 'agTextColumnFilter' },
    { headerName: "工程名称", field: "projectName", width: getHeaderColumnWidth("工程名称"), filter: 'agTextColumnFilter' },
    { headerName: "工程部位", field: "projectLocation", width: getHeaderColumnWidth("工程部位"), filter: 'agTextColumnFilter' },
    { 
      headerName: "总份数", 
      field: "totalIssueCopies", 
      width: getHeaderColumnWidth("总份数", { minWidth: 80 }), 
      pinned: 'left',
      filter: 'agNumberColumnFilter'
    },
    {
      headerName: "可发放份数",
      field: "remainingCopies",
      valueGetter: params => {
        if (!params.data) return 0;
        const firstCopies = params.data.firstIssueCopies || 0;
        const secondCopies = params.data.secondIssueCopies || 0;
        return params.data.totalIssueCopies - (firstCopies + secondCopies);
      },
      width: getHeaderColumnWidth("可发放份数"),
      pinned: 'left',
      filter: 'agNumberColumnFilter'
    },
    {
      headerName: "状态",
      field: "status",
      width: getHeaderColumnWidth("状态", { isSpecialCase: true, specialCaseMinWidth: 120 }),
      pinned: 'left',
      cellRenderer: StatusRenderer,
      cellStyle: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
      filter: false,
      sortable: false
    },
    { headerName: "第一次发放时间", field: "firstIssueDateTime", width: getHeaderColumnWidth("第一次发放时间", { isSpecialCase: true, specialCaseMinWidth: 160 }), valueGetter: params => params.data.firstIssueDateTime || "-" },
    { headerName: "发放人", field: "firstIssuePerson", width: getHeaderColumnWidth("发放人"), valueGetter: params => params.data.firstIssuePerson || "-" },
    { headerName: "领取人", field: "firstReceiverName", width: getHeaderColumnWidth("领取人"), valueGetter: params => params.data.firstReceiverName || "-" },
    { headerName: "领取人电话", field: "firstReceiverPhone", width: getHeaderColumnWidth("领取人电话"), valueGetter: params => params.data.firstReceiverPhone || "-" },
    { headerName: "第二次发放时间", field: "secondIssueDateTime", width: getHeaderColumnWidth("第二次发放时间", { isSpecialCase: true, specialCaseMinWidth: 160 }), valueGetter: params => params.data.secondIssueDateTime || "-" },
    { headerName: "发放人(2)", field: "secondIssuePerson", width: getHeaderColumnWidth("发放人(2)"), valueGetter: params => params.data.secondIssuePerson || "-" },
    { headerName: "领取人(2)", field: "secondReceiverName", width: getHeaderColumnWidth("领取人(2)"), valueGetter: params => params.data.secondReceiverName || "-" },
    { headerName: "领取人电话(2)", field: "secondReceiverPhone", width: getHeaderColumnWidth("领取人电话(2)"), valueGetter: params => params.data.secondReceiverPhone || "-" },
  ], [])

  const defaultColDef = useMemo(() => ({
    ...agGridConfig.defaultColDef,
    floatingFilter: true,
    resizable: true,
    sortable: true,
    filter: true,
  }), [])



  return (
    <div className="h-full w-full overflow-hidden">
      <Card className="h-full shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="p-2.5 h-full flex flex-col">
          <div className="flex items-center justify-between mb-1.5">
            <h3 className="text-lg font-medium">
              报告清单 <span className="text-red-500">*</span>
            </h3>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                已选: {selectedRecords.length} 项
              </Badge>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                总份数: {totalCopies} 份
              </Badge>
            </div>
          </div>

          <div className="relative flex-1 min-h-0">
            <Tabs
              value={reportsActiveTab}
              onValueChange={setReportsActiveTab}
              className="h-full"
            >
              <TabsList className="grid w-full grid-cols-2 mb-2">
                <TabsTrigger value="selected">已选档案</TabsTrigger>
                <TabsTrigger value="records">台账表</TabsTrigger>
              </TabsList>

              {/* 已选档案表 - 通过display控制显隐，避免重建表格实例 */}
              <div
                className="absolute inset-0 top-10 overflow-hidden border rounded-md"
                style={{ 
                  margin: 0,
                  display: reportsActiveTab === "selected" ? "block" : "none"
                }}
              >
                {selectedRecords.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <p className="text-gray-500">暂无选择的档案，请在台账表中选择</p>
                      <Button variant="outline" className="mt-4" onClick={() => setReportsActiveTab("records")}>
                        浏览台账表
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex flex-col">
                    <div className="p-2 border-b bg-gray-50 flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              disabled={selectedRecords.length === 0 || isBatchProcessing}
                            >
                              {batchOperationType === "issue" ? "发放份数" : "总份数"}
                              <ChevronDown className="ml-1 h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="start">
                            <DropdownMenuItem
                              onClick={() => setBatchOperationType("issue")}
                            >
                              发放份数
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setBatchOperationType("total")}
                            >
                              总份数
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {batchOperationType === "issue" ? (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleBatchSetIssueCopies("one")}
                              disabled={isBatchProcessing}
                            >
                              选中设为1份
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleBatchSetIssueCopies("max")}
                              disabled={isBatchProcessing}
                            >
                              选中设为最大份数
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setBatchTotalCopiesDialog(true)}
                            disabled={isBatchProcessing}
                          >
                            选中设置总份数
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className="flex-1 min-h-0" style={{ height: "100%" }} data-testid="selected-records-grid">
                      <AgGridReact
                        rowData={selectedRecords}
                        columnDefs={columnDefs}
                        defaultColDef={defaultColDef}

                        rowModelType="clientSide"
                        pagination={agGridConfig.clientSideDefaults.pagination}
                        paginationPageSize={agGridConfig.clientSideDefaults.paginationPageSize}

                        // 编辑配置
                        editType="fullRow"
                        stopEditingWhenCellsLoseFocus={true}
                        undoRedoCellEditing={true}

                        // 使用原生行选择替代自定义复选框
                        rowSelection={selectedRecordsRowSelection}
                        selectionColumnDef={selectedRecordsSelectionColumnDef}
                        onGridReady={(params) => {
                          selectedRecordsGridRef.current = params.api
                        }}

                        theme={agGridConfig.themes.quartzCustom}

                        context={{
                          isBatchProcessing,
                          removeRecord,
                          handleCopiesChange,
                          canAdjustCopies,
                          getReportStatus,
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* 台账表 - 通过display控制显隐，避免重建表格实例 */}
              <div
                className="absolute inset-0 top-10 overflow-hidden border rounded-md"
                style={{ 
                  margin: 0,
                  display: reportsActiveTab === "records" ? "block" : "none"
                }}
              >
                <div className="h-full flex flex-col">
                  <div className="p-3 border-b bg-gray-50 flex flex-col gap-2">
                    <div className="flex flex-col sm:flex-row gap-2 items-center">
                      <div className="flex-1 relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                        <Input
                          placeholder="通过上方表头筛选，或在此搜索统一编号..."
                          className="pl-9 h-9"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                      

                      
                      <DateRangeFilter
                        initialStartDate={startDate}
                        initialEndDate={endDate}
                        onApplyFilter={(start: string, end: string) => {
                          setStartDate(start)
                          setEndDate(end)
                        }}
                      />
                    </div>
                    

                  </div>

                  <div className="flex-1 min-h-0" style={{ height: "100%" }}>
                    <AgGridReact
                      columnDefs={recordsColumnDefs}
                      defaultColDef={defaultColDef}

                      rowModelType="serverSide"
                      serverSideDatasource={datasource}
                      pagination={true}
                      paginationPageSize={500}
                      paginationPageSizeSelector={false}
                      cacheBlockSize={500}

                      // ✅ 配置官方选择列，只替换header组件
                      selectionColumnDef={{
                        width: 50,
                        pinned: 'left',
                        suppressHeaderMenuButton: true,
                        resizable: false,
                        headerComponent: CustomHeaderCheckbox,
                        headerComponentParams: {
                          selectCurrentPage,
                          deselectCurrentPage,
                          getCurrentPageSelectionStats,
                          isRowSelectable
                        }
                      }}

                      // 更新为新的、结构化的行选择配置 (v32.2+)
                      // 禁用默认的header checkbox，使用自定义header组件
                      rowSelection={{
                        mode: 'multiRow',
                        headerCheckbox: false,            // 🔧 禁用默认header checkbox，使用自定义的
                        enableSelectionWithoutKeys: true, // 允许单击选择，替代了 rowMultiSelectWithClick
                        enableClickSelection: true,      // 允许单击选择，替代了 suppressRowClickSelection={false}
                        isRowSelectable: isRowSelectable,
                      }}

                      getRowId={getRowId}

                      onGridReady={onGridReady}
                      onSelectionChanged={onSelectionChanged}

                      theme={agGridConfig.themes.quartzCustom}

                      context={{
                        isEditable,
                        getLedgerRecordStatus,
                      }}

                      suppressColumnVirtualisation={agGridConfig.performanceConfig.suppressColumnVirtualisation}
                      suppressRowVirtualisation={agGridConfig.performanceConfig.suppressRowVirtualisation}
                      rowHeight={agGridConfig.performanceConfig.rowHeight}
                      headerHeight={agGridConfig.performanceConfig.headerHeight}
                      rowBuffer={agGridConfig.performanceConfig.rowBuffer}
                      suppressRowHoverHighlight={agGridConfig.performanceConfig.suppressRowHoverHighlight}
                      debounceVerticalScrollbar={agGridConfig.performanceConfig.debounceVerticalScrollbar}
                      skipHeaderOnAutoSize={agGridConfig.performanceConfig.skipHeaderOnAutoSize}
                      suppressColumnMoveAnimation={agGridConfig.performanceConfig.suppressColumnMoveAnimation}
                      suppressAnimationFrame={agGridConfig.performanceConfig.suppressAnimationFrame}
                      suppressRowTransform={agGridConfig.performanceConfig.suppressRowTransform}
                      suppressCellFocus={agGridConfig.performanceConfig.suppressCellFocus}
                      enableCellTextSelection={agGridConfig.performanceConfig.enableCellTextSelection}

                      domLayout="normal"
                    />
                  </div>
                </div>
              </div>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* 批量设置总份数弹窗 */}
      <Dialog open={batchTotalCopiesDialog} onOpenChange={setBatchTotalCopiesDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>批量设置总份数</DialogTitle>
            <DialogDescription>
              请输入要设置的总份数。注意：总份数不能小于已发放份数。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="totalCopies" className="text-right">
                总份数
              </Label>
              <Input
                id="totalCopies"
                type="number"
                min="1"
                max="999"
                className="col-span-3"
                value={batchTotalCopiesValue}
                onChange={(e) => setBatchTotalCopiesValue(e.target.value)}
                placeholder="请输入总份数"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setBatchTotalCopiesDialog(false)
                setBatchTotalCopiesValue("")
              }}
            >
              取消
            </Button>
            <Button
              type="button"
              onClick={handleBatchSetTotalCopies}
              disabled={isBatchProcessing || !batchTotalCopiesValue}
            >
              {isBatchProcessing ? "处理中..." : "确定"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
