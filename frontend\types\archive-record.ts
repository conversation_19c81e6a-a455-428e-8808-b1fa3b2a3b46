/**
 * 档案记录相关类型定义
 * 
 * 注意：所有字段名使用camelCase格式，对应后端snake_case字段
 * 通过djangorestframework-camel-case自动转换
 *
 * CHANGE: [2025-06-19] 以-`models.py`为准，全面更新字段
 */

// 档案记录主接口
export interface ArchiveRecord {
  // 1. 基础标识信息
  id: number;
  sampleNumber: string;
  accountFromExcel?: string | null;
  commissionNumber: string;
  unifiedNumber?: string | null;
  reportNumber?: string | null;
  provinceUnifiedNumber?: string | null;
  stationCode?: string | null;
  organizationCode?: string | null;

  // 2. 项目与委托信息
  projectNumber?: string | null;
  projectName: string;
  subProject?: string | null;
  projectLocation?: string | null;
  projectAddress?: string | null;
  clientUnit: string;
  clientName?: string | null;
  commissionDatetime?: string | null;

  // 3. 试验与结果信息
  testStartDatetime?: string | null;
  testEndDatetime?: string | null;
  testPerson1?: string | null;
  testPerson2?: string | null;
  dataEntryPerson?: string | null;
  testResult?: string | null;
  conclusion?: string | null;
  testParameters?: string | null;
  unqualifiedParameters?: string | null;
  checkPerson?: string | null;
  approvePerson?: string | null;
  testMethod?: string | null;
  testStandard?: string | null;

  // 4. 档案生命周期信息
  archiveStatus?: string | null;
  changeCount?: number | null;
  currentStatus?: string | null;
  processingStatus?: string | null;
  archiveBoxNumber?: string | null;
  archiveUrl?: string | null;
  attachmentsFromExcel?: string | null;
  storageDatetime?: string | null;
  storagePerson?: string | null;
  outboundDatetime?: string | null;
  outboundPerson?: string | null;
  archiveDatetime?: string | null;
  archivePerson?: string | null;
  archiveNote?: string | null;

  // 5. 报告管理信息
  reportIssueStatus?: string | null;
  firstIssueCopies?: number | null;
  firstIssueDatetime?: string | null;
  firstIssuePerson?: string | null;
  firstReceiverName?: string | null;
  firstReceiverUnit?: string | null;
  firstReceiverPhone?: string | null;
  secondIssueCopies?: number | null;
  secondIssueDatetime?: string | null;
  secondIssuePerson?: string | null;
  secondReceiverName?: string | null;
  secondReceiverUnit?: string | null;
  secondReceiverPhone?: string | null;
  totalIssueCopies?: number | null;

  // 6. 样品信息
  groupNumber?: string | null;
  sampleName?: string | null;
  assignedPerson?: string | null;
  componentCount?: number | null;
  testPointCount?: number | null;
  unqualifiedPointCount?: number | null;
  sampleRetentionDatetime?: string | null;
  sampleRemainingTime?: number | null;
  sampleType?: string | null;
  sampleCount?: number | null;
  samplingLocation?: string | null;
  samplingDatetime?: string | null;
  samplingPerson?: string | null;
  samplingMethod?: string | null;

  // 7. 财务信息
  paymentStatus?: string | null;
  priceAdjustmentStatus?: string | null;
  standardPrice?: number | null;
  discountPrice?: number | null;
  actualPrice?: number | null;
  chargeAmount?: number | null;
  chargeStatus?: string | null;
  chargeDatetime?: string | null;

  // 8. 系统元数据
  importUser?: number | null;
  importUserName?: string;
  importDate?: string;
  batchNumber?: string | null;
  sourceSystem?: string | null;
  createdAt?: string;
  updatedAt?: string;
}

// 档案记录列表项（简化版）
export interface ArchiveRecordListItem {
  id: number;
  sampleNumber: string;
  commissionNumber: string;
  unifiedNumber?: string;
  projectName: string;
  clientUnit: string;
  archiveStatus?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 档案记录查询参数
export interface ArchiveRecordQueryParams {
  page?: number;
  pageSize?: number;              // page_size
  sampleNumber?: string;          // sample_number
  commissionNumber?: string;      // commission_number
  unifiedNumber?: string;         // unified_number
  reportNumber?: string;          // report_number
  projectName?: string;           // project_name
  clientUnit?: string;            // client_unit
  archiveStatus?: string;         // archive_status
  batchNumber?: string;           // batch_number
  search?: string;
  ordering?: string;
  commissionDatetimeGte?: string; // commission_datetime__gte
  commissionDatetimeLte?: string; // commission_datetime__lte
}

// 档案状态类型
export type ArchiveStatus = 
  | 'pending'          // 待处理
  | 'stored'           // 已入库
  | 'archived'         // 已归档
  | 'issued'           // 已发放
  | 'completed';       // 已完成

// 发放状态类型
export type ReportIssueStatus = 
  | 'not_issued'       // 未发放
  | 'first_issued'     // 已一次发放
  | 'second_issued'    // 已二次发放
  | 'completed';       // 发放完成

// 档案记录分页响应
export interface ArchiveRecordPaginatedResponse {
  count: number;
  next?: string;
  previous?: string;
  results: ArchiveRecord[];
} 