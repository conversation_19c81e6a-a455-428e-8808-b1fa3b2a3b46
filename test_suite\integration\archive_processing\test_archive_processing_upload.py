import uuid
import os
from unittest.mock import patch, MagicMock, ANY

from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework import status
from rest_framework.test import APITestCase

from archive_processing.models import UploadedFile, ProcessingTask

# CHANGE: [2024-03-28] 添加针对 PDF 上传流程的 API 测试 #AFM-6
# CHANGE: [2024-03-29] 更新测试以匹配字段重命名和API响应格式 #AFM-16
# CHANGE: [2024-03-29] 将实例属性定义移至 setUp 方法 #AFM-28
# CHANGE: [2024-04-14] 合并重复的测试类并修复集成测试
# TODO: [P1] 增加对 UploadService 和 TaskService 的独立单元测试
# TODO: [P2] 完善 TaskService 失败时返回 207 状态的测试场景
# TODO: [P2] 增加对文件内容本身的验证测试（如果 UploadService.validate_file 实现）
# TODO: [P2 - 延后] 添加认证/权限集成测试

class PDFUploadAPITests(APITestCase):
    """
    测试 PDFUploadView API 端点。
    合并了原 PDFUploadViewIntegrationTest 的逻辑。
    """
    def setUp(self):
        """测试环境初始化"""
        # 创建测试用户并进行身份验证。
        self.test_user = User.objects.create_user(username='testuser', password='password123')
        # Use force_authenticate for consistency and potentially better isolation
        self.client.force_authenticate(user=self.test_user)
        # self.client.login(username='testuser', password='password123') # Keep old login as comment if needed

        # 定义上传URL。
        self.upload_url = reverse('archive_processing:pdf-upload')

        # 创建一个模拟的PDF文件。
        self.pdf_content = b'%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj 2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj 3 0 obj<</Type/Page/MediaBox[0 0 3 3]/Parent 2 0 R/Resources<<>>>>endobj\nxref\n0 4\n0000000000 65535 f\n0000000010 00000 n\n0000000053 00000 n\n0000000102 00000 n\ntrailer<</Size 4/Root 1 0 R>>\nstartxref\n149\n%%EOF\n'
        self.mock_pdf = SimpleUploadedFile("test_document.pdf", self.pdf_content, content_type="application/pdf")
        # 使用新的字段名
        self.valid_assigned_box_number = "BOX-TEST-001"

        # 创建一个用于模拟服务返回的 UploadedFile 实例
        self.mock_uploaded_file_instance = MagicMock(spec=UploadedFile)
        self.mock_uploaded_file_instance.file_id = 'mock-uuid-1234'
        self.mock_uploaded_file_instance.original_name = self.mock_pdf.name
        self.mock_uploaded_file_instance.file_size = len(self.pdf_content)
        self.mock_uploaded_file_instance.assigned_box_number = self.valid_assigned_box_number
        self.mock_uploaded_file_instance.uploader = self.test_user
        # ... add other relevant attributes if needed

    @patch('archive_processing.views.dispatch_pdf_processing')
    @patch('archive_processing.views.TaskService.create_task')
    @patch('archive_processing.views.UploadService.save_uploaded_file')
    def test_pdf_upload_success(self, mock_save_file, mock_create_task, mock_dispatch):
        """测试成功的PDF上传流程"""
        # 配置 Mocks 返回成功的模拟对象。
        mock_save_file.return_value = self.mock_uploaded_file_instance

        # 返回一个带有可序列化 task_id 和 status 的模拟任务对象
        mock_task = MagicMock(spec=ProcessingTask)
        mock_task.task_id = uuid.uuid4() # 使用真实 UUID
        mock_task.status = 'queued'
        mock_create_task.return_value = mock_task

        # 准备请求数据。
        data = {
            'file': self.mock_pdf,
            'assigned_box_number': self.valid_assigned_box_number
        }

        # 发送POST请求。
        response = self.client.post(self.upload_url, data, format='multipart')

        # 断言成功的状态码和响应内容。
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['file_id'], str(self.mock_uploaded_file_instance.file_id))
        self.assertEqual(response.data['data']['task_id'], str(mock_task.task_id))
        self.assertEqual(response.data['data']['task_status'], 'queued')
        self.assertIn('文件上传成功', response.data['data']['message'])

        # 断言服务方法被正确调用。
        mock_save_file.assert_called_once_with(
            file_obj=ANY, 
            assigned_box_number=self.valid_assigned_box_number,
            user_id=self.test_user.id 
        )
        
        # 可选: 对实际文件对象的属性进行额外检查
        actual_call_args, actual_call_kwargs = mock_save_file.call_args
        actual_file_obj = actual_call_kwargs.get('file_obj')
        self.assertIsNotNone(actual_file_obj)
        self.assertEqual(actual_file_obj.name, self.mock_pdf.name)
        self.assertEqual(actual_file_obj.size, self.mock_pdf.size)
        self.assertEqual(actual_file_obj.content_type, self.mock_pdf.content_type)

        # 验证 TaskService.create_task 被正确调用
        mock_create_task.assert_called_once_with(
            uploaded_file=self.mock_uploaded_file_instance,
            params={'assigned_box_number': self.valid_assigned_box_number},
            user_id=self.test_user.id
        )

        # 验证 dispatch_pdf_processing 被正确调用（传入 UUID 对象）
        mock_dispatch.assert_called_once_with(mock_task.task_id)

    def test_pdf_upload_missing_file(self):
        """测试上传请求缺少文件"""
        data = {
            'assigned_box_number': self.valid_assigned_box_number
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)
        self.assertIn('field_errors', response.data['error'])
        self.assertIn('file', response.data['error']['field_errors'])

    def test_pdf_upload_missing_assigned_box_number(self):
        """测试上传请求缺少目标档案盒号"""
        data = {
            'file': self.mock_pdf
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)
        self.assertIn('field_errors', response.data['error'])
        self.assertIn('assigned_box_number', response.data['error']['field_errors'])

    # Note: Authentication test deferred as requested
    # def test_pdf_upload_not_authenticated(self):
    #     """测试未认证用户上传"""
    #     self.client.logout()
    #     data = {
    #         'file': self.mock_pdf,
    #         'assigned_box_number': self.valid_assigned_box_number
    #     }
    #     response = self.client.post(self.upload_url, data, format='multipart')
    #     self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @patch('archive_processing.views.UploadService.save_uploaded_file')
    def test_pdf_upload_save_validation_error(self, mock_save_file):
        """测试 UploadService 抛出 ValidationError"""
        validation_error_msg = ["文件类型无效"]
        mock_save_file.side_effect = DjangoValidationError(validation_error_msg)
        data = {
            'file': self.mock_pdf,
            'assigned_box_number': self.valid_assigned_box_number
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)
        expected_error_str = f"文件上传处理失败: {validation_error_msg}"
        self.assertEqual(response.data['error']['message'], expected_error_str)

    @patch('archive_processing.views.UploadService.save_uploaded_file')
    def test_pdf_upload_save_io_error(self, mock_save_file):
        """测试 UploadService 抛出 IOError"""
        io_error_msg = "磁盘空间不足"
        mock_save_file.side_effect = IOError(io_error_msg)
        data = {
            'file': self.mock_pdf,
            'assigned_box_number': self.valid_assigned_box_number
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)
        expected_error_str = f"文件上传处理失败: {io_error_msg}"
        self.assertEqual(response.data['error']['message'], expected_error_str)

    @patch('archive_processing.views.dispatch_pdf_processing')
    @patch('archive_processing.views.TaskService.create_task')
    @patch('archive_processing.views.UploadService.save_uploaded_file')
    def test_pdf_upload_task_creation_error(self, mock_save_file, mock_create_task, mock_dispatch):
        """测试 TaskService 创建任务失败"""
        mock_save_file.return_value = self.mock_uploaded_file_instance
        mock_create_task.side_effect = Exception("无法连接到消息队列")

        data = {
            'file': self.mock_pdf,
            'assigned_box_number': self.valid_assigned_box_number
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_207_MULTI_STATUS)
        self.assertTrue(response.data['success']) # 标记为成功，但有警告
        self.assertIn('data', response.data)
        self.assertIn("文件上传成功，但自动处理任务启动失败", response.data['data']['message'])
        self.assertIn("无法启动处理任务", response.data['warning'])
        self.assertEqual(response.data['data']['file_id'], str(self.mock_uploaded_file_instance.file_id))
        # dispatch_pdf_processing 应该未被调用
        mock_dispatch.assert_not_called()

    @patch('archive_processing.views.UploadService.save_uploaded_file')
    def test_pdf_upload_unexpected_error(self, mock_save_file):
        """测试 UploadService 抛出未知错误"""
        mock_save_file.side_effect = Exception("未知数据库错误")
        data = {
            'file': self.mock_pdf,
            'assigned_box_number': self.valid_assigned_box_number
        }
        response = self.client.post(self.upload_url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)
        self.assertIn("服务器内部错误", response.data['error']['message'])

# Removed the duplicated PDFUploadViewIntegrationTest class 