# CMA章模板配置说明

## 目录作用

此目录用于存放CMA章识别模板图片，系统会自动加载这些模板用于报告分割功能。

## 模板要求

### 1. 支持的图片格式
- PNG格式（推荐）
- JPG/JPEG格式

### 2. 图片质量要求
- 分辨率：建议200-300 DPI
- 尺寸：CMA章清晰可见，建议包含一定边距
- 颜色：彩色图片，确保红色印章清晰

### 3. 模板命名
- 建议使用有意义的文件名，如：`cma_seal_template_01.png`
- 可以放置多个模板文件，系统会自动加载所有支持格式的图片

## 配置方式

### 方式1：文件夹扫描（推荐）
将模板图片直接放在此目录下，系统会自动扫描并加载。

### 方式2：Settings配置
在`settings.py`中添加：
```python
# CMA章模板路径列表
MA_SEAL_TEMPLATE_PATHS = [
    '/path/to/template1.png',
    '/path/to/template2.png',
]
```

## 识别参数调整

可以在`settings.py`中调整识别参数：

```python
# CMA章识别参数配置
REPORT_RECOGNITION_DPI = 200  # 页面渲染DPI
MA_SEAL_COLOR_THRESHOLD = 30  # 红色筛选阈值
MA_SEAL_MIN_CONTOUR_AREA = 500  # 最小轮廓面积
MA_SEAL_MATCH_THRESHOLD = 10  # 特征匹配阈值
MA_SEAL_USE_SIFT = True  # 是否使用SIFT算法
MA_SEAL_MAX_FEATURES = 500  # 最大特征点数
```

## 模板制作建议

1. **获取样本**：从实际报告PDF中截取CMA章部分
2. **去噪处理**：清理背景噪声，保持印章清晰
3. **适当裁剪**：保留印章周围适当边距
4. **多样性**：收集不同角度、尺寸的CMA章样本

## 故障排除

### 识别率低
- 增加更多样本模板
- 调整`MA_SEAL_MATCH_THRESHOLD`参数
- 检查模板图片质量

### 误识别
- 提高`MA_SEAL_MATCH_THRESHOLD`参数
- 优化模板图片质量
- 调整`MA_SEAL_MIN_CONTOUR_AREA`参数

## 日志查看

系统启动时会在日志中显示：
```
ReportRecognitionService 初始化完成，模板路径: /path/to/templates/ma_seal
总共加载了 X 个MA章模板
```

检查此日志确认模板是否正确加载。 