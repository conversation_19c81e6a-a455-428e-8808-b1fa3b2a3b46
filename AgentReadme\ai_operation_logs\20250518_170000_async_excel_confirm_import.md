# Operation Document: 将Excel确认导入重构为异步Celery任务

## 📋 Change Summary

**Purpose**: 解决因Excel确认导入接口 (`/api/archive-records/confirm-import/`) 处理时间过长导致的 504 Gateway Time-out 错误。通过将核心导入逻辑异步化，提高API响应速度和系统稳定性。
**Scope**:

- `archive_records/models.py`: 添加新的导入会话状态。
- `archive_records/tasks.py`: 创建新的Celery任务处理实际导入。
- `archive_records/services/import_session_manager.py`: 修改 `confirm_import` 方法以提交Celery任务。
- `archive_records/views.py`: 修改 `ExcelImportConfirmView` 以适应异步流程。
**Associated**: 用户报告的 504 Gateway Time-out 错误，以及前端解析HTML错误页面为JSON失败的问题。

## 🔧 Operation Steps

### 📊 OP-001: 分析问题与设计异步方案

**Precondition**: 用户反馈确认导入接口超时 (504)。
**Operation**:

1. 分析确认导入流程，识别出其包含文件读写、大量数据处理和数据库交互，是潜在的耗时操作。
2. 确定将核心导入逻辑（原 `ImportSessionManager._execute_import_with_resolutions` 方法及其调用的服务）移至后台异步处理是必要的。
3. 选择Celery作为异步任务队列，因为它已在项目其他部分使用。
4. 设计异步流程：
    a.  API接收请求后，快速验证，更新会话状态为"排队等待导入"。
    b.  将导入参数（会话ID，用户决策，用户ID）提交给Celery任务。
    c.  API立即返回"任务已提交"的响应 (HTTP 202)。
    d.  Celery worker执行实际导入，更新会话最终状态和结果。
    e.  前端通过轮询接口获取任务的最终状态。
**Postcondition**: 异步化方案设计完成。

### ✏️ OP-002: 实现异步化修改

**Precondition**: 异步方案已设计。
**Operation**:

1. **模型更新 (`archive_records/models.py`)**:
    - 在 `ImportSessionStatus` 枚举中添加新状态 `IMPORT_QUEUED = "queued", "排队等待导入"`。
2. **创建Celery任务 (`archive_records/tasks.py`)**:
    - 定义新的共享任务 `process_excel_import_confirmation_task(self, session_id: str, resolutions_json: str, user_id: int)`。
    - 任务逻辑：
        - 获取 `User` 和 `ImportSession` 实例。
        - 验证会话状态是否为 `IMPORT_QUEUED` 或 `IMPORT_START`。
        - 更新会话状态为 `IMPORT_IN_PROGRESS`。
        - 反序列化 `resolutions_json`。
        - **关键**：从 `session` 和反序列化后的 `resolutions` 中正确构建 `conflict_records_data` 和 `resolution_map`，以匹配 `_execute_import_with_resolutions` 方法的参数需求。
        - 实例化 `ImportSessionManager` 并调用其 `_execute_import_with_resolutions` 方法。
        - 根据返回的 `ImportLog` 实例，更新 `ImportSession` 的最终状态 (`IMPORT_COMPLETE` 或 `ERROR`)、关联的 `import_log` 字段及错误信息。
        - 处理任务执行过程中的各种异常（如 `OperationalError` 进行重试，其他错误则标记会话为 `ERROR`）。
3. **修改服务层 (`archive_records/services/import_session_manager.py`)**:
    - 重构 `confirm_import` 方法：
        - 移除原有的同步执行导入逻辑。
        - 添加参数验证（会话状态、用户决策的action值）。
        - 更新 `ImportSession` 状态为 `IMPORT_QUEUED`。
        - 将 `resolutions` (List[Dict]) 序列化为JSON字符串。
        - 调用 `process_excel_import_confirmation_task.delay(...)` 提交任务到Celery。
        - 返回一个包含 `{success: True, data: {message: ..., session_id: ..., task_id: ..., current_status: ...}}` 的字典。
        - 如果提交任务前发生错误（如序列化失败、参数验证失败），则返回 `{success: False, error: ..., error_type: ...}`。
4. **修改视图层 (`archive_records/views.py`)**:
    - 调整 `ExcelImportConfirmView` 的 `post` 方法：
        - 在调用 `session_manager.confirm_import(...)` 后，检查返回结果字典中的 `success` 字段。
        - 如果成功，返回 HTTP 202 (Accepted) 响应，并将 `task_submission_response.get('data')` 作为响应体中的 `data` 部分。
        - 如果失败，根据 `task_submission_response` 中的 `error` 和 `error_type` 构造合适的错误响应（如 HTTP 400, 404, 409）。
**Postcondition**: 后端代码已完成异步化重构。

## 📝 Change Details

### CH-001: 添加 `IMPORT_QUEUED` 状态到 `ImportSessionStatus`

**File**: `archive_records/models.py`
**Change**: 在 `ImportSessionStatus` 枚举中加入 `IMPORT_QUEUED = "queued", "排队等待导入"`。

### CH-002: 创建 `process_excel_import_confirmation_task` Celery 任务

**File**: `archive_records/tasks.py`
**Change**: 新增 `@shared_task`，负责实际的导入确认逻辑。关键在于正确获取和准备参数给 `ImportSessionManager._execute_import_with_resolutions`。

### CH-003: 修改 `ImportSessionManager.confirm_import`

**File**: `archive_records/services/import_session_manager.py`
**Change**: 此方法不再执行导入，而是改为验证参数、更新会话状态为排队，并将任务分发给Celery。返回任务提交状态。

### CH-004: 修改 `ExcelImportConfirmView`

**File**: `archive_records/views.py`
**Change**: 视图现在处理来自 `ImportSessionManager.confirm_import` 的异步提交结果，并相应地返回 HTTP 202 或错误状态码。

**Rationale**: 将耗时的导入操作移至后台Celery任务处理，可以显著减少API请求的响应时间，从而避免网关超时。同时，系统对用户更友好，因为用户可以更快地得到操作反馈。
**Potential Impact**:

- 前端需要适配新的异步流程，通过轮询状态接口获取最终导入结果。
- 需要确保Celery worker配置正确并有足够资源处理任务。
- 增加了系统的复杂性，但提高了可伸缩性和响应性。

## ✅ Verification Results

**Method**: 代码审查和逻辑推断。
**Results**: 该重构方案能有效解决504超时问题。
**Problems**: 需要前端配合修改。需要全面测试异步流程。
**Solutions**: 明确告知用户前端修改的必要性，并建议进行完整的端到端测试。
