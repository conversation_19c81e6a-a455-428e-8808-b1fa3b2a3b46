"""
单元测试：archive_processing.services.file_storage_service.FileStorageService

此测试套件专注于 FileStorageService 中各个方法的单元测试，
特别是文件归档逻辑 (archive_single_archive_pdf)。
测试将使用 mock 来隔离对文件系统和 Django settings 的依赖。
"""

import unittest
import os
import shutil
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

# 确保能导入目标模块
from archive_processing.services.file_storage_service import FileStorageService

class TestFileStorageServiceUnit(unittest.TestCase):
    """FileStorageService 单元测试类"""

    def setUp(self):
        """测试初始化，创建临时文件/目录等（如果需要）"""
        # 创建一个临时的源文件供测试使用
        self.temp_dir_obj = tempfile.TemporaryDirectory()
        self.temp_dir = self.temp_dir_obj.name
        self.source_file_path = os.path.join(self.temp_dir, "test_temp_file.pdf")
        with open(self.source_file_path, "w") as f:
            f.write("dummy pdf content")
        self.unified_number = "TESTPREFIX-001"
        self.original_pdf_path = "/path/to/original.pdf"

    def tearDown(self):
        """测试清理，删除临时文件/目录"""
        self.temp_dir_obj.cleanup()

    # --- 测试 archive_single_archive_pdf --- 

    @patch('shutil.move')
    @patch('os.path.exists')
    @patch('archive_processing.services.file_storage_service.FileStorageService.get_archive_storage_path')
    @patch('archive_processing.services.file_storage_service.FileStorageService.generate_archive_filename')
    def test_archive_single_archive_pdf_success(self,
                                               mock_generate_filename,
                                               mock_get_storage_path,
                                               mock_path_exists,
                                               mock_shutil_move):
        """测试 archive_single_archive_pdf 成功归档文件"""
        # 模拟依赖项
        expected_filename = "TESTPREFIX-001.pdf"
        expected_dir = "/mock/archive/base/TESTPREFIX"
        expected_final_path = os.path.join(expected_dir, expected_filename)

        mock_generate_filename.return_value = expected_filename
        mock_get_storage_path.return_value = expected_dir
        mock_path_exists.return_value = False # 模拟目标文件不存在
        mock_shutil_move.return_value = None # move 成功不返回特殊值

        # 调用被测方法
        result = FileStorageService.archive_single_archive_pdf(
            temp_path=self.source_file_path,
            unified_number=self.unified_number,
            original_pdf_path=self.original_pdf_path
        )

        # 断言
        self.assertTrue(result['success'])
        self.assertEqual(result['final_path'], expected_final_path)
        mock_generate_filename.assert_called_once_with(
            unified_number=self.unified_number,
            original_path=self.original_pdf_path
        )
        mock_get_storage_path.assert_called_once_with(unified_number=self.unified_number)
        mock_path_exists.assert_called_once_with(expected_final_path)
        mock_shutil_move.assert_called_once_with(self.source_file_path, expected_final_path)

    @patch('shutil.move')
    @patch('os.path.isfile') # Mock isfile for this test
    def test_archive_temp_file_not_found(self, mock_isfile, mock_shutil_move):
        """测试当临时文件不存在时归档失败"""
        # 模拟 isfile 返回 False
        mock_isfile.return_value = False

        # 调用被测方法
        result = FileStorageService.archive_single_archive_pdf(
            temp_path="/non/existent/temp.pdf",
            unified_number=self.unified_number
        )

        # 断言
        self.assertFalse(result['success'])
        self.assertIn("临时文件路径无效或文件不存在", result['error'])
        mock_isfile.assert_called_once_with("/non/existent/temp.pdf")
        mock_shutil_move.assert_not_called() # 确认未尝试移动文件

    @patch('shutil.move')
    @patch('os.path.exists')
    @patch('archive_processing.services.file_storage_service.FileStorageService.get_archive_storage_path')
    @patch('archive_processing.services.file_storage_service.FileStorageService.generate_archive_filename')
    @patch('archive_processing.services.file_storage_service.logger') # Mock logger to check warning
    def test_archive_target_exists_overwrite_warning(self,
                                                    mock_logger,
                                                    mock_generate_filename,
                                                    mock_get_storage_path,
                                                    mock_path_exists,
                                                    mock_shutil_move):
        """测试当目标文件已存在时，记录警告并成功覆盖"""
        # 模拟依赖项
        expected_filename = "TESTPREFIX-001.pdf"
        expected_dir = "/mock/archive/base/TESTPREFIX"
        expected_final_path = os.path.join(expected_dir, expected_filename)

        mock_generate_filename.return_value = expected_filename
        mock_get_storage_path.return_value = expected_dir
        mock_path_exists.return_value = True # 模拟目标文件存在
        mock_shutil_move.return_value = None

        # 调用被测方法
        result = FileStorageService.archive_single_archive_pdf(
            temp_path=self.source_file_path,
            unified_number=self.unified_number
        )

        # 断言
        self.assertTrue(result['success'])
        self.assertEqual(result['final_path'], expected_final_path)
        mock_path_exists.assert_called_once_with(expected_final_path)
        mock_logger.warning.assert_called_once_with(
            f"归档目标文件已存在，将被覆盖: '{expected_final_path}'"
        )
        mock_shutil_move.assert_called_once_with(self.source_file_path, expected_final_path) # 确认仍然移动了

    @patch('shutil.move')
    @patch('os.path.exists')
    @patch('archive_processing.services.file_storage_service.FileStorageService.get_archive_storage_path')
    @patch('archive_processing.services.file_storage_service.FileStorageService.generate_archive_filename')
    def test_archive_move_os_error(self,
                                    mock_generate_filename,
                                    mock_get_storage_path,
                                    mock_path_exists,
                                    mock_shutil_move):
        """测试当 shutil.move 抛出 OSError 时归档失败"""
        # 模拟依赖项
        expected_filename = "TESTPREFIX-001.pdf"
        expected_dir = "/mock/archive/base/TESTPREFIX"
        expected_final_path = os.path.join(expected_dir, expected_filename)
        mock_error = OSError("磁盘空间不足")

        mock_generate_filename.return_value = expected_filename
        mock_get_storage_path.return_value = expected_dir
        mock_path_exists.return_value = False
        mock_shutil_move.side_effect = mock_error # 模拟抛出异常

        # 调用被测方法
        result = FileStorageService.archive_single_archive_pdf(
            temp_path=self.source_file_path,
            unified_number=self.unified_number
        )

        # 断言
        self.assertFalse(result['success'])
        self.assertIn("归档文件移动/IO操作失败", result['error'])
        self.assertIn(str(mock_error), result['error'])
        mock_shutil_move.assert_called_once_with(self.source_file_path, expected_final_path)

    def test_archive_missing_unified_number(self):
        """测试当 unified_number 缺失时归档失败"""
        # 调用被测方法 (使用 None)
        result_none = FileStorageService.archive_single_archive_pdf(
            temp_path=self.source_file_path,
            unified_number=None
        )
        # 调用被测方法 (使用空字符串)
        result_empty = FileStorageService.archive_single_archive_pdf(
            temp_path=self.source_file_path,
            unified_number=""
        )

        # 断言
        self.assertFalse(result_none['success'])
        self.assertIn("必须提供统一编号", result_none['error'])
        self.assertFalse(result_empty['success'])
        self.assertIn("必须提供统一编号", result_empty['error'])

    # TODO: 添加更多测试用例，覆盖失败场景：
    # - test_archive_path_generation_fails (如果可能，例如 mock get_archive_storage_path 返回 None)

    # --- (可选) 测试其他辅助方法 --- 
    # def test_generate_archive_filename_with_unified_number(self):
    #     ...
    # def test_get_archive_storage_path_structure(self):
    #     ...
    # def test_get_temp_directory(self):
    #     ...

if __name__ == '__main__':
    unittest.main() 