"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import {
  generateTempId,
  isTempId,
  associateTempIdWithSession,
  validateTempIdBelongsToSession,
  removeTempIdFromSession,
  saveTemporaryData,
  getTemporaryData,
  removeTemporaryData,
} from "@/utils/id-utils"
import { fetchChangeOrder, saveChangeOrderAsDraft, updateChangeOrder } from "@/services/change-order-service"

export type ChangeOrderStatus = "creating" | "draft" | "locked" | "confirmed" | "archived"

export interface ChangeOrderRecord {
  id: string
  unifiedNumber: string
  title: string
  projectName: string
  status: string
  fields: Array<{
    name: string
    label: string
    value: string
  }>
  [key: string]: any
}

export interface ChangeOrderChange {
  recordId: string
  field: string
  fieldLabel: string
  oldValue: string
  newValue: string
}

export interface ChangeOrderHistoryEntry {
  id: number
  action: string
  status: string
  timestamp: string
  user: string
  reason: string
}

export interface ChangeOrder {
  id: string
  status: ChangeOrderStatus
  reason: string
  details: string
  createdAt: string
  createdBy: string
  lockedAt?: string | null
  lockedBy?: string | null
  confirmedAt?: string | null
  confirmedBy?: string | null
  archivedAt?: string | null
  archivedBy?: string | null
  records: ChangeOrderRecord[]
  changes: ChangeOrderChange[]
  history: ChangeOrderHistoryEntry[]
  [key: string]: any
}

interface UseChangeOrderOptions {
  id: string
}

export function useChangeOrder({ id }: UseChangeOrderOptions) {
  const router = useRouter()
  const { toast } = useToast()

  // 状态
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isNewOrder, setIsNewOrder] = useState(false)
  const [changeOrder, setChangeOrder] = useState<ChangeOrder | null>(null)
  const [editedChanges, setEditedChanges] = useState<ChangeOrderChange[]>([])
  const [isSaving, setIsSaving] = useState(false)
  const [lastSavedAt, setLastSavedAt] = useState<string | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [editMode, setEditMode] = useState(true)
  const [urlUpdatedToPermanent, setUrlUpdatedToPermanent] = useState(false)

  // Refs
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null)
  const previousChangeOrderRef = useRef<ChangeOrder | null>(null)
  const previousEditedChangesRef = useRef<ChangeOrderChange[]>([])
  const statusTransitionRef = useRef<boolean>(false)
  const initializationCompletedRef = useRef<boolean>(false)

  // 初始化加载逻辑
  useEffect(() => {
    // 防止重复初始化
    if (initializationCompletedRef.current) {
      return
    }

    const initializeChangeOrder = async () => {
      setIsLoading(true)

      try {
        // 情况1: 如果是"new"，创建新的更改单（使用临时ID）
        if (id === "new") {
          // 生成临时ID - 这个ID只在前端使用，不会传给后端
          const tempId = generateTempId("change-order")

          // 创建新的更改单
          const newChangeOrder: ChangeOrder = {
            id: tempId, // 临时ID
            status: "creating", // 初始状态为"创建中"
            reason: "",
            details: "",
            createdAt: new Date().toISOString(),
            createdBy: "当前用户", // 实际应用中应从认证上下文获取
            records: [], // 确保初始化为空数组
            changes: [],
            history: [
              {
                id: 1,
                action: "create",
                status: "creating",
                timestamp: new Date().toLocaleString(),
                user: "当前用户",
                reason: "新建更改单",
              },
            ],
          }

          setChangeOrder(newChangeOrder)
          setEditedChanges([])
          setIsNewOrder(true)
          previousChangeOrderRef.current = JSON.parse(JSON.stringify(newChangeOrder))
          previousEditedChangesRef.current = []

          // 关联临时ID与当前会话 - 用于验证用户是否有权访问此临时ID
          associateTempIdWithSession(tempId, "change-order")

          // 保存到localStorage - 临时存储，用于恢复编辑状态
          saveTemporaryData(tempId, "change-order", newChangeOrder)

          // 更新URL，但不添加到历史记录中 - 用户看到的是临时ID的URL
          router.replace(`/change-orders/detail/${tempId}`)

          // 标记初始化完成
          initializationCompletedRef.current = true
        }
        // 情况2: 如果是临时ID - 说明用户正在创建新的更改单，但尚未保存为草稿
        else if (isTempId(id)) {
          // 验证临时ID是否属于当前会话 - 安全检查
          if (!validateTempIdBelongsToSession(id, "change-order")) {
            toast({
              title: "访问受限",
              description: "您无权访问此更改单",
              variant: "destructive",
            })
            router.replace("/change-orders")
            return
          }

          // 从localStorage获取临时更改单数据
          const savedData = getTemporaryData(id, "change-order")

          if (savedData) {
            // 确保records字段存在
            if (!savedData.records) {
              savedData.records = []
            }

            // 如果找到了临时数据，恢复编辑状态
            setChangeOrder(savedData)
            setEditedChanges(savedData.changes || [])
            setIsNewOrder(true)
            previousChangeOrderRef.current = JSON.parse(JSON.stringify(savedData))
            previousEditedChangesRef.current = JSON.parse(JSON.stringify(savedData.changes || []))
          } else {
            // 如果没有找到临时数据，创建新的（这种情况不应该经常发生）
            const newChangeOrder: ChangeOrder = {
              id: id,
              status: "creating",
              reason: "",
              details: "",
              createdAt: new Date().toISOString(),
              createdBy: "当前用户",
              records: [], // 确保初始化为空数组
              changes: [],
              history: [
                {
                  id: 1,
                  action: "create",
                  status: "creating",
                  timestamp: new Date().toLocaleString(),
                  user: "当前用户",
                  reason: "新建更改单",
                },
              ],
            }

            setChangeOrder(newChangeOrder)
            setEditedChanges([])
            setIsNewOrder(true)
            previousChangeOrderRef.current = JSON.parse(JSON.stringify(newChangeOrder))
            previousEditedChangesRef.current = []

            // 保存到localStorage
            saveTemporaryData(id, "change-order", newChangeOrder)
          }

          // 标记初始化完成
          initializationCompletedRef.current = true
        }
        // 情况3: 否则是永久ID - 说明是已保存的更改单，从后端获取数据
        else {
          try {
            // 尝试从API获取更改单数据 - 使用永久ID
            const data = await fetchChangeOrder(id)

            // 确保数据中有records字段，如果没有则添加空数组
            if (!data.records) {
              data.records = []
            }

            setChangeOrder({
              ...data,
              history: (data as any).history || [],
              status: data.status as ChangeOrderStatus
            })
            setEditedChanges(data.changes || [])
            setIsNewOrder(false) // 不是新建的更改单
            previousChangeOrderRef.current = JSON.parse(JSON.stringify(data))
            previousEditedChangesRef.current = JSON.parse(JSON.stringify(data.changes || []))

            // 检查用户是否有权限编辑 - 只有草稿状态且用户有权限时才可编辑
            setEditMode(data.status === "draft")

            // 标记URL已经是永久ID
            setUrlUpdatedToPermanent(true)

            // 标记初始化完成
            initializationCompletedRef.current = true
          } catch (error) {
            console.error("Error fetching change order:", error)

            // 如果API返回404，说明更改单不存在
            if ((error as any)?.status === 404) {
              toast({
                title: "未找到更改单",
                description: "您尝试访问的更改单不存在",
                variant: "destructive",
              })
              router.replace("/change-orders")
            } else {
              // 其他错误
              toast({
                title: "加载失败",
                description: "无法加载更改单数据，请重试",
                variant: "destructive",
              })
            }
          }
        }
      } catch (error) {
        console.error("Error initializing change order:", error)
        toast({
          title: "加载失败",
          description: "法加载更改单数据，请重试",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    initializeChangeOrder()
  }, [id, router, toast])

  // 检查是否有未保存的更改
  useEffect(() => {
    if (!changeOrder || isLoading || statusTransitionRef.current) {
      // 如果正在进行状态转换，跳过检查
      if (statusTransitionRef.current) {
        console.log("跳过未保存更改检查：正在进行状态转换")
      }
      return
    }

    // 检查基本信息是否有变化
    const basicInfoChanged =
      previousChangeOrderRef.current?.reason !== changeOrder.reason ||
      previousChangeOrderRef.current?.details !== changeOrder.details

    // 检查记录列表是否有变化
    const recordsChanged =
      JSON.stringify(previousChangeOrderRef.current?.records || []) !== JSON.stringify(changeOrder.records || [])

    // 检查更改项是否有变化
    const changesChanged = JSON.stringify(previousEditedChangesRef.current) !== JSON.stringify(editedChanges)

    // 如果有任何变化，设置未保存状态
    if (basicInfoChanged || recordsChanged || changesChanged) {
      console.log("检测到未保存的更改:", {
        basicInfoChanged,
        recordsChanged,
        changesChanged,
      })
      setHasUnsavedChanges(true)
    } else {
      setHasUnsavedChanges(false)
    }
  }, [changeOrder, editedChanges, isLoading])

  // 检查是否满足自动保存条件
  const canAutoSave = (changes = editedChanges) => {
    if (!changeOrder) {
      console.log("自动保存条件不满足：无更改单数据")
      return false
    }

    // 必须有更改原因
    if (!changeOrder.reason?.trim()) {
      console.log("自动保存条件不满足：缺少更改原因")
      return false
    }

    // 确保changes是数组
    const changesArray = Array.isArray(changes) ? changes : []

    // 检查是否有实际的更改内容（而不是检查记录是否为空）
    if (!changesArray || changesArray.length === 0) {
      console.log("自动保存条件不满足：缺少更改内容", {
        editedChanges: changesArray,
        editedChangesLength: changesArray?.length,
      })
      return false
    }

    // 检查是否有任何记录的任何字段发生了实际变化
    const hasActualChanges = changesArray.some((change) => change.oldValue !== change.newValue)
    if (!hasActualChanges) {
      console.log("自动保存条件不满足：没有实际的值变化")
      return false
    }

    console.log("自动保存条件已满足，可以保存为草稿", {
      reason: changeOrder.reason,
      editedChangesCount: changesArray.length,
      editedChanges: changesArray,
    })
    return true
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current)
        autoSaveTimerRef.current = null
      }
    }
  }, [])

  // 自动保存为草稿 - 这是关键函数，处理从临时ID到永久ID的转换
  const handleAutoSaveAsDraft = async (changesOverride?: ChangeOrderChange[], skipCheck?: boolean) => {
    // 检查是否可以保存
    if (isSaving || isProcessing || !changeOrder || statusTransitionRef.current) {
      console.log(
        "无法保存：",
        isSaving
          ? "正在保存"
          : isProcessing
            ? "正在处理"
            : !changeOrder
              ? "无更改单数据"
              : statusTransitionRef.current
                ? "正在进行状态转换"
                : "条件不满足",
      )
      return
    }

    // 使用传入的更改数组或当前状态
    const changesToUse = Array.isArray(changesOverride)
      ? changesOverride
      : Array.isArray(editedChanges)
        ? editedChanges
        : []

    // 强制检查条件，如果不满足则不保存（除非skipCheck为true）
    if (!skipCheck && !canAutoSave(changesToUse)) {
      console.log("自动保存条件不满足，取消保存")
      return
    }

    // 标记正在进行状态转换
    statusTransitionRef.current = true
    console.log("开始自动保存为草稿...", { changesToUse })
    setIsSaving(true)

    try {
      // 准备要保存的数据 - 注意：不包含临时ID
      // 临时ID是前端概念，不传给后端
      const dataToSave = {
        // 不包含id字段，让后端生成永久ID
        reason: changeOrder.reason,
        details: changeOrder.details,
        changes: changesToUse, // 使用传入的更改数组
        records: changeOrder.records || [], // 确保records存在
        status: "draft",
        history: [...(changeOrder.history || []), {
          id: (changeOrder.history?.length || 0) + 1,
          action: "create",
          status: "draft",
          timestamp: new Date().toLocaleString(),
          user: "当前用户",
          reason: "自动保存为草稿",
        }],
        createdAt: changeOrder.createdAt,
        createdBy: changeOrder.createdBy,
        // 添加原始ID用于调试
        originalId: changeOrder.id,
      }

      console.log("调用API保存更改单...", dataToSave)
      // 调用API创建新的更改单 - 后端会生成永久ID\
      const result = await saveChangeOrderAsDraft(dataToSave)

      // 获取后端生成的永久ID
      const permanentId = result.id

      console.log(`临时ID ${changeOrder.id} 已转换为永久ID ${permanentId}`)

      // 更新状态 - 使用永久ID替换临时ID
      const updatedChangeOrder = {
        ...changeOrder,
        id: permanentId, // 替换为永久ID
        status: "draft" as ChangeOrderStatus,
        history: dataToSave.history,
      }

      setChangeOrder(updatedChangeOrder as ChangeOrder)
      setIsNewOrder(false)
      setLastSavedAt(new Date().toLocaleString())
      setHasUnsavedChanges(false)

      // 更新引用值 - 使用changesToUse而不是editedChanges
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(updatedChangeOrder))
      previousEditedChangesRef.current = JSON.parse(JSON.stringify(changesToUse))

      // 无感知URL更新 - 使用History API修改URL，不触发页面刷新
      console.log("更新URL从", window.location.pathname, "到", `/change-orders/detail/${permanentId}`)
      window.history.replaceState(
        {}, // 状态对象
        "", // 标题
        `/change-orders/detail/${permanentId}`, // 新URL
      )
      setUrlUpdatedToPermanent(true)

      // 清理临时数据
      removeTemporaryData(changeOrder.id, "change-order")
      removeTempIdFromSession(changeOrder.id, "change-order")

      toast({
        title: "自动保存成功",
        description: `更改单已保存为草稿 (${permanentId})`,
      })
    } catch (error) {
      console.error("Failed to auto save as draft:", error)
      toast({
        title: "保存失败",
        description: "无法保存更改单，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
      // 状态转换完成 - 立即重置，不使用setTimeout
      statusTransitionRef.current = false
    }
  }

  // 自动更新草稿 - 使用永久ID更新已有的草稿
  const handleAutoUpdate = async (changesOverride?: ChangeOrderChange[], skipCheck?: boolean) => {
    if (isSaving || isProcessing || !changeOrder || statusTransitionRef.current) {
      console.log(
        "无法更新：",
        isSaving
          ? "正在保存"
          : isProcessing
            ? "正在处理"
            : !changeOrder
              ? "无更改单数据"
              : statusTransitionRef.current
                ? "正在进行状态转换"
                : "条件不满足",
      )
      return
    }

    // 使用传入的更改数组或当前状态
    const changesToUse = Array.isArray(changesOverride)
      ? changesOverride
      : Array.isArray(editedChanges)
        ? editedChanges
        : []

    // 强制检查条件，如果不满足则不更新（除非skipCheck为true）
    if (!skipCheck && !canAutoSave(changesToUse)) {
      console.log("自动更新条件不满足，取消更新")
      return
    }

    // 标记正在进行状态转换
    statusTransitionRef.current = true
    console.log("开始自动更新草稿...", { changesToUse })
    setIsSaving(true)

    try {
      // 准备要保存的数据 - 这里使用的是永久ID
      const dataToSave = {
        ...changeOrder,
        changes: changesToUse, // 使用传入的更改数组
        records: changeOrder.records || [], // 确保records存在
        history: [...(changeOrder.history || []), {
        id: (changeOrder.history?.length || 0) + 1,
        action: "update",
        status: "draft",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "自动更新草稿",
        }],
      }

      console.log("调用API更新更改单...", dataToSave)
      // 更新现有更改单 - 使用永久ID
      const result = await updateChangeOrder(changeOrder.id, dataToSave)

      // 更新本地状态
      const updatedChangeOrder = {
        ...changeOrder,
        history: dataToSave.history,
      }

      setChangeOrder(updatedChangeOrder)
      setLastSavedAt(new Date().toLocaleString())
      setHasUnsavedChanges(false)

      // 更新引用值 - 使用changesToUse而不是editedChanges
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(updatedChangeOrder))
      previousEditedChangesRef.current = JSON.parse(JSON.stringify(changesToUse))

      toast({
        title: "自动更新成功",
        description: `更改单已自动更新`,
      })
    } catch (error) {
      console.error("Failed to auto update:", error)
      toast({
        title: "更新失败",
        description: "无法更新更改单，请重试",
      })
    } finally {
      setIsSaving(false)
      // 状态转换完成 - 立即重置，不使用setTimeout
      statusTransitionRef.current = false
    }
  }

  // 处理更改值的编辑
  const handleChangeEdit = (change: ChangeOrderChange, field: string, value: string) => {
    if (!editMode || statusTransitionRef.current) return

    console.log("handleChangeEdit 被调用:", { change, field, value, currentEditedChanges: editedChanges })

    // 检查值是否真的发生了变化
    const oldValue = change.oldValue || ""
    const currentValue =
      editedChanges.find((c) => c.recordId === change.recordId && c.field === change.field)?.newValue || oldValue

    if (value === currentValue) {
      console.log("值未发生变化，不进行更新:", {
        recordId: change.recordId,
        field: change.field,
        oldValue,
        currentValue,
        newValue: value,
      })
      return
    }

    // 查找更改项在数组中的索引
    const index = editedChanges.findIndex((c) => c.recordId === change.recordId && c.field === change.field)

    // 创建新的更改数组
    let newChanges: ChangeOrderChange[] = []

    if (index !== -1) {
      // 如果找到了更改项，直接更新它的 newValue 字段
      newChanges = [...editedChanges]
      newChanges[index].newValue = value
      console.log("更新现有更改项:", { index, updatedChange: newChanges[index] })
    } else {
      // 如果没有找到更改项，创建一个新的并添加到数组中
      const newChange = {
        recordId: change.recordId,
        field: change.field,
        fieldLabel: change.fieldLabel,
        oldValue: change.oldValue || "",
        newValue: value,
      }
      newChanges = [...editedChanges, newChange]
      console.log("添加新的更改项:", { newChange })
    }

    console.log("更新前的editedChanges:", editedChanges)
    console.log("即将设置的newChanges:", newChanges)

    // 更新状态 - 使用函数式更新确保基于最新状态
    setEditedChanges(newChanges)

    // 明确设置为有未保存更改
    setHasUnsavedChanges(true)

    // 保存到localStorage，确保状态更新
    if (isNewOrder && changeOrder) {
      saveTemporaryData(changeOrder.id, "change-order", {
        ...changeOrder,
        changes: newChanges,
        records: changeOrder.records || [], // 确保records存在
      })
    }

    // 触发自动保存（失焦保存）
    if (changeOrder && changeOrder.status === "creating" && isNewOrder) {
      console.log("失焦触发自动保存为草稿")
      // 延迟执行，确保状态已更新
      setTimeout(() => {
        handleAutoSaveAsDraft(newChanges)
      }, 100)
    } else if (changeOrder && changeOrder.status === "draft" && editMode) {
      console.log("失焦触发自动更新")
      setTimeout(() => {
        handleAutoUpdate(newChanges)
      }, 100)
    }
  }

  // 处理基本信息变更
  const handleBasicInfoChange = (field: string, value: any, shouldSave = false) => {
    console.log(`基本信息变更: ${field} = `, value, shouldSave ? "(失焦)" : "")

    // 如果正在进行状态转换，跳过更新
    if (statusTransitionRef.current) {
      console.log("跳过更新：正在进行状态转换")
      return
    }

    // 检查值是否真的发生了变化（与之前保存的值比较）
    const valueChanged = previousChangeOrderRef.current && previousChangeOrderRef.current[field] !== value

    // 更新状态
    setChangeOrder((prev) => {
      if (!prev) return prev

      // 检查值是否真的发生了变化 - 在这里进行检查，使用prev而不是changeOrder
      if (prev[field] === value) {
        console.log(`${field}值未发生变化，跳过更新:`, { value })
        return prev // 返回原状态，不更新
      }

      const updated = { ...prev, [field]: value }

      // 保存到localStorage
      if (isNewOrder) {
        saveTemporaryData(prev.id, "change-order", {
          ...prev,
          [field]: value,
          changes: editedChanges,
        })
      }

      return updated
    })

    // 只有在值真的发生变化时才设置为有未保存更改
    if (valueChanged) {
      setHasUnsavedChanges(true)
    }

    // 只在失焦且值发生变化时触发自动保存
    if (shouldSave && !statusTransitionRef.current && valueChanged) {
      // 触发自动保存（失焦保存）
      if (changeOrder?.status === "creating" && isNewOrder) {
        console.log("基本信息失焦触发自动保存为草稿")
        // 延迟执行，确保状态已更新，并传递当前的editedChanges
        setTimeout(() => {
          handleAutoSaveAsDraft(editedChanges)
        }, 100)
      } else if (changeOrder?.status === "draft" && editMode) {
        console.log("基本信息失焦触发自动更新")
        setTimeout(() => {
          handleAutoUpdate(editedChanges)
        }, 100)
      }
    }
  }

  // 锁定更改单
  const handleLockChangeOrder = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      // 添加历史记录
      const newHistoryEntry = {
        id: (changeOrder.history?.length || 0) + 1,
        action: "lock",
        status: "locked",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "锁定更改单",
      }

      // 准备要保存的数据
      const dataToSave = {
        ...changeOrder,
        status: "locked" as ChangeOrderStatus,
        lockedAt: new Date().toISOString(),
        lockedBy: "当前用户",
        history: [...(changeOrder.history || []), newHistoryEntry],
      }

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 更新本地状态
      setChangeOrder(dataToSave as ChangeOrder)
      setEditMode(false)
      setHasUnsavedChanges(false)
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(dataToSave))

      toast({
        title: "锁定成功",
        description: "更改单已成功锁定",
      })
    } catch (error) {
      console.error("Failed to lock change order:", error)
      toast({
        title: "锁定失败",
        description: "无法锁定更改单，请重试",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 解锁更改单
  const handleUnlockChangeOrder = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      // 添加历史记录
      const newHistoryEntry = {
        id: (changeOrder.history?.length || 0) + 1,
        action: "unlock",
        status: "draft",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "解锁更改单",
      }

      // 准备要保存的数据
      const dataToSave = {
        ...changeOrder,
        status: "draft" as ChangeOrderStatus,
        lockedAt: null,
        lockedBy: null,
        history: [...(changeOrder.history || []), newHistoryEntry],
      }

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 更新本地状态
      setChangeOrder(dataToSave as ChangeOrder)
      setEditMode(true)
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(dataToSave))

      toast({
        title: "解锁成功",
        description: "更改单已成功解锁",
      })
    } catch (error) {
      console.error("Failed to unlock change order:", error)
      toast({
        title: "解锁失败",
        description: "无法解锁更改单，请重试",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 确认更改单
  const handleConfirmChangeOrder = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      // 添加历史记录
      const newHistoryEntry = {
        id: (changeOrder.history?.length || 0) + 1,
        action: "confirm",
        status: "confirmed",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "确认更改单",
      }

      // 准备要保存的数据
      const dataToSave = {
        ...changeOrder,
        status: "confirmed" as ChangeOrderStatus,
        confirmedAt: new Date().toISOString(),
        confirmedBy: "当前用户",
        history: [...(changeOrder.history || []), newHistoryEntry],
      }

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 更新本地状态
      setChangeOrder(dataToSave as ChangeOrder)
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(dataToSave))

      toast({
        title: "确认成功",
        description: "更改单已成功确认",
      })
    } catch (error) {
      console.error("Failed to confirm change order:", error)
      toast({
        title: "确认失败",
        description: "无法确认更改单，请重试",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 归档更改单
  const handleArchiveChangeOrder = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      // 添加历史记录
      const newHistoryEntry = {
        id: (changeOrder.history?.length || 0) + 1,
        action: "archive",
        status: "archived",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "归档更改单",
      }

      // 准备要保存的数据
      const dataToSave = {
        ...changeOrder,
        status: "archived" as ChangeOrderStatus,
        archivedAt: new Date().toISOString(),
        archivedBy: "当前用户",
        history: [...(changeOrder.history || []), newHistoryEntry],
      }

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 更新本地状态
      setChangeOrder(dataToSave as ChangeOrder)
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(dataToSave))

      toast({
        title: "归档成功",
        description: "更改单已成功归档",
      })
    } catch (error) {
      console.error("Failed to archive change order:", error)
      toast({
        title: "归档失败",
        description: "无法归档更改单，请重试",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 转为草稿
  const handleRevertToDraft = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      // 添加历史记录
      const newHistoryEntry = {
        id: (changeOrder.history?.length || 0) + 1,
        action: "revert_to_draft",
        status: "draft",
        timestamp: new Date().toLocaleString(),
        user: "当前用户",
        reason: "转为草稿",
      }

      // 准备要保存的数据
      const dataToSave = {
        ...changeOrder,
        status: "draft" as ChangeOrderStatus,
        confirmedAt: null,
        confirmedBy: null,
        history: [...(changeOrder.history || []), newHistoryEntry],
      }

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 更新本地状态
      setChangeOrder(dataToSave as ChangeOrder)
      setEditMode(true)
      previousChangeOrderRef.current = JSON.parse(JSON.stringify(dataToSave))

      toast({
        title: "操作成功",
        description: "更改单已转为草稿",
      })
    } catch (error) {
      console.error("Failed to revert to draft:", error)
      toast({
        title: "操作失败",
        description: "无法转为草稿，请重试",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 删除更改单
  const handleDeleteChangeOrder = async () => {
    if (!changeOrder) return

    setIsProcessing(true)

    try {
      if (isNewOrder) {
        // 如果是临时更改单，只需清除本地数据
        removeTemporaryData(changeOrder.id, "change-order")
        removeTempIdFromSession(changeOrder.id, "change-order")
      } else {
        // 模拟处理延迟
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }

      toast({
        title: "删除成功",
        description: "更改单已成功删除",
      })

      // 重定向到更改单列表页
      router.push("/change-orders")
    } catch (error) {
      console.error("Failed to delete change order:", error)
      toast({
        title: "删除失败",
        description: "无法删除更改单，请重试",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return {
    // 状态
    isLoading,
    isProcessing,
    isNewOrder,
    changeOrder,
    editedChanges,
    isSaving,
    lastSavedAt,
    hasUnsavedChanges,
    editMode,
    urlUpdatedToPermanent,

    // 方法
    setChangeOrder, // 添加这一行，暴露 setChangeOrder 函数
    setEditedChanges,
    setIsProcessing,
    canAutoSave,
    handleAutoSaveAsDraft,
    handleAutoUpdate,
    handleChangeEdit,
    handleBasicInfoChange,
    handleLockChangeOrder,
    handleUnlockChangeOrder,
    handleConfirmChangeOrder,
    handleArchiveChangeOrder,
    handleRevertToDraft,
    handleDeleteChangeOrder,
  }
}
