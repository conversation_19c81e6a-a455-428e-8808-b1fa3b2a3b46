import logging
from typing import Dict, Any
from archive_records.models import ArchiveRecord

logger = logging.getLogger(__name__)

class IssueRuleService:
    """
    领域工具服务：提供无状态的、可重用的纯业务计算或验证逻辑。
    这是一个"服务于服务的服务"，被其他业务服务调用。
    """
    def __init__(self, user_id: int):
        self.user_id = user_id
        # 此处可以注入其他服务，例如数据服务，以获取计算所需的数据
        # self.archive_record_service = ArchiveRecordService()
        # self.issue_record_service = IssueRecordService()

    def calculate_issue_quantity(self, archive_identifier: str) -> dict:
        """
        计算档案项的发放数量和选项。
        
        Args:
            archive_identifier: 档案标识符（委托编号）
            
        Returns:
            dict: 包含 issue_type 等发放信息
        """
        type_info = self._determine_issue_type(archive_identifier)
        
        # 如果无法发放（例如，记录不存在或已发放完毕），则直接返回
        if not type_info['can_issue']:
            quantity_info = {
                'available_options': [], 
                'suggested_copies': 0, 
                'remaining_copies': 0, 
                'quantity_rule': type_info.get('reason', '发放已完成，无可发放数量')
            }
        # 如果是第一次发放
        elif type_info['issue_type'] == 'first':
            total = type_info['total_copies']
            # 补发场景
            if type_info['scenario'] == 'missing_first_补发':
                quantity_info = {'available_options': [1], 'suggested_copies': 1, 'remaining_copies': 1, 'quantity_rule': '补发第一次，固定1份'}
            # 正常第一次发放场景
            else:
                options = [1]
                if total > 1:
                    options.append(total)
                quantity_info = {'available_options': options, 'suggested_copies': 1, 'remaining_copies': total, 'quantity_rule': '第一次发放：1份或全部'}
        # 如果是第二次发放
        elif type_info['issue_type'] == 'second':
            remaining = type_info['total_copies'] - type_info['first_copies']
            quantity_info = {'available_options': [remaining], 'suggested_copies': remaining, 'remaining_copies': remaining, 'quantity_rule': '第二次发放：剩余全部'}
        
        else: # 备用，处理未知情况
            quantity_info = {'available_options': [], 'suggested_copies': 0, 'remaining_copies': 0, 'quantity_rule': '未知发放状态'}

        quantity_info['validation'] = {
            'business_rules': {
                'first_issue_constraint': '第一次发放只能是1份或全部',
                'second_issue_constraint': '第二次发放必须是剩余全部',
                'min_total_copies': 1, # 理论上总份数至少为1
                '补发_constraint': '补发第一次时只能是1份'
            },
            'scenario_description': self._get_scenario_description(type_info.get('scenario', 'unknown'))
        }
        
        return {**type_info, **quantity_info}

    def _determine_issue_type(self, archive_identifier: str) -> dict:
        """
        确定档案项的发放类型和基础状态。
        现在从数据库获取真实数据。
        
        Args:
            archive_identifier: 档案标识符（委托编号）
            
        Returns:
            dict: 包含发放类型和详细状态信息
        """
        try:
            # 使用委托编号查询而不是主键ID
            record = ArchiveRecord.objects.get(commission_number=archive_identifier)
        except ArchiveRecord.DoesNotExist:
            return {'can_issue': False, 'reason': f"档案记录 委托编号 {archive_identifier} 不存在。"}

        total_copies = record.total_issue_copies or 0
        first_copies = record.first_issue_copies or 0
        second_copies = record.second_issue_copies or 0

        has_first_issue = first_copies > 0
        has_second_issue = second_copies > 0
        
        # 场景1：两次都已发放
        if has_first_issue and has_second_issue:
            return {'issue_type': 'completed', 'can_issue': False, 'scenario': 'both_completed', 'total_copies': total_copies, 'first_copies': first_copies, 'second_copies': second_copies}
        
        # 场景2：只进行了第一次发放
        elif has_first_issue:
            # 第一次就发完了全部
            if first_copies >= total_copies:
                return {'issue_type': 'completed', 'can_issue': False, 'scenario': 'first_all_completed', 'total_copies': total_copies, 'first_copies': first_copies, 'second_copies': second_copies}
            # 第一次发了部分，等待第二次
            else:
                return {'issue_type': 'second', 'can_issue': True, 'scenario': 'first_partial_awaiting_second', 'total_copies': total_copies, 'first_copies': first_copies, 'second_copies': second_copies}
        
        # 场景3：只进行了第二次发放（数据异常情况），视为补发第一次
        elif has_second_issue:
            return {'issue_type': 'first', 'can_issue': True, 'scenario': 'missing_first_补发', 'total_copies': total_copies, 'first_copies': first_copies, 'second_copies': second_copies}
        
        # 场景4：从未发放过
        else:
            if total_copies > 0:
                return {'issue_type': 'first', 'can_issue': True, 'scenario': 'initial_state', 'total_copies': total_copies, 'first_copies': first_copies, 'second_copies': second_copies}
            else:
                return {'issue_type': 'not_issuable', 'can_issue': False, 'scenario': 'no_copies_to_issue', 'reason': '档案总份数为0，无法发放'}

    def _get_scenario_description(self, scenario: str) -> str:
        """获取场景描述"""
        descriptions = {
            'both_completed': '第一次和第二次发放都已完成',
            'first_all_completed': '第一次发放了全部，无需第二次',
            'first_partial_awaiting_second': '第一次发放了部分，等待第二次发放',
            'missing_first_补发': '缺失第一次发放记录，需要补发',
            'initial_state': '初始状态，准备进行第一次发放',
            'no_copies_to_issue': '档案总份数为0，无法发放',
            'unknown': '未知场景'
        }
        return descriptions.get(scenario, '未知场景') 