"""
发放单API测试模块

注意：为简化测试，用户权限处理已被简化为使用超级用户权限。
在实际部署时，应实现完整的权限检查机制。
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction

from archive_records.models import ArchiveRecord
from report_issuing.models import IssueForm, IssueFormItem, IssueRecord


class IssueFormAPITest(TestCase):
    def setUp(self):
        """设置测试环境"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='api_test_user', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 将用户设置为超级用户以绕过权限检查
        self.user.is_superuser = True
        self.user.save()
        
        # 设置API客户端
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 创建测试档案记录
        self.archive_record = ArchiveRecord.objects.create(
            sample_number='API001',
            report_number='API-R001',
            commission_number='API-C001',
            project_name='API测试项目',
            client_unit='API测试单位',
            commission_datetime=timezone.now(),
            archive_status='archived'
        )
        
        # 创建基本发放单用于测试
        self.issue_form = IssueForm.objects.create(
            issue_number='ISSUE-20230101-001',
            issue_date=timezone.now(),
            receiver_name='API测试领取人',
            receiver_unit='API测试单位',
            receiver_phone='12345678901',
            status='draft',
            issuer=self.user
        )
        
        # 添加条目
        self.form_item = IssueFormItem.objects.create(
            issue_form=self.issue_form,
            archive_record=self.archive_record,
            copies=1
        )
    
    def test_list_issue_forms(self):
        """测试获取发放单列表"""
        url = reverse('issueform-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['issue_number'], 'ISSUE-20230101-001')
    
    def test_create_issue_form(self):
        """测试创建发放单"""
        url = reverse('issueform-list')
        data = {
            'issue_date': timezone.now().isoformat(),
            'receiver_name': 'API创建领取人',
            'receiver_unit': 'API创建单位',
            'receiver_phone': '98765432109',
            'notes': 'API创建备注'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['receiver_name'], 'API创建领取人')
        self.assertEqual(response.data['status'], 'draft')
        
        # 验证数据库中已创建
        self.assertEqual(IssueForm.objects.count(), 2)
    
    def test_add_items(self):
        """测试添加发放单条目"""
        url = reverse('issueform-add-items', args=[self.issue_form.issue_number])
        
        # 创建另一个档案记录用于添加
        new_record = ArchiveRecord.objects.create(
            sample_number='API002',
            report_number='API-R002',
            commission_number='API-C002',
            project_name='API测试项目2',
            archive_status='archived'
        )
        
        data = {
            'items': [
                {
                    'archive_record': new_record.id,
                    'receiver_name': '条目专属领取人',
                    'receiver_unit': '条目专属单位',
                    'receiver_phone': '12345678901'
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['added'], 1)
        
        # 验证条目已添加
        self.assertEqual(self.issue_form.items.count(), 2)
        new_item = self.issue_form.items.get(archive_record=new_record)
        
        # 验证领取人信息已正确设置
        self.assertEqual(new_item.receiver_name, '条目专属领取人')
        self.assertEqual(new_item.receiver_unit, '条目专属单位')
        self.assertEqual(new_item.receiver_phone, '12345678901')
        
        # 对于single类型，系统应该自动计算份数为1
        self.assertEqual(new_item.copies, 1)
    
    def test_remove_items(self):
        """测试移除发放单条目"""
        url = reverse('issueform-remove-items', args=[self.issue_form.issue_number])
        
        data = {
            'item_ids': [self.form_item.id]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['removed'], 1)
        
        # 验证条目已移除
        self.assertEqual(self.issue_form.items.count(), 0)
    
    def test_lock_and_unlock(self):
        """测试锁定和解锁发放单"""
        # 测试锁定
        lock_url = reverse('issueform-lock', args=[self.issue_form.issue_number])
        lock_response = self.client.post(lock_url)
        
        self.assertEqual(lock_response.status_code, status.HTTP_200_OK)
        self.assertEqual(lock_response.data['status'], 'locked')
        
        # 验证数据库中状态已更新
        self.issue_form.refresh_from_db()
        self.assertEqual(self.issue_form.status, 'locked')
        
        # 测试解锁
        unlock_url = reverse('issueform-unlock', args=[self.issue_form.issue_number])
        unlock_response = self.client.post(unlock_url)
        
        self.assertEqual(unlock_response.status_code, status.HTTP_200_OK)
        self.assertEqual(unlock_response.data['status'], 'draft')
        
        # 验证数据库中状态已更新
        self.issue_form.refresh_from_db()
        self.assertEqual(self.issue_form.status, 'draft')
    
    def test_confirm_and_archive(self):
        """测试确认和归档发放单"""
        # 确保已有条目
        if not IssueFormItem.objects.filter(issue_form=self.issue_form).exists():
            IssueFormItem.objects.create(
                issue_form=self.issue_form,
                archive_record=self.archive_record,
                copies=1
            )
        
        # 先锁定发放单
        self.issue_form.status = 'locked'
        self.issue_form.save()
        
# CHANGE: [2025-06-07] 更新API测试以适应简化的生命周期
        # 注意：简化后的生命周期中没有独立的confirm和archive操作
        # 直接测试发放操作（从锁定到已发放）
        issue_url = reverse('issueform-issue', args=[self.issue_form.issue_number])
        issue_response = self.client.post(issue_url)
        
        self.assertEqual(issue_response.status_code, status.HTTP_200_OK)
        self.assertEqual(issue_response.data['status'], 'issued')
        
        # 验证数据库中状态已更新，并且创建了发放记录
        self.issue_form.refresh_from_db()
        self.assertEqual(self.issue_form.status, 'issued')
        
        # 验证创建了发放记录
        record = IssueRecord.objects.filter(
            archive_record=self.archive_record,
            issue_form=self.issue_form
        ).first()
        self.assertIsNotNone(record)
        self.assertEqual(record.issue_type, 'first')
        self.assertTrue(record.is_active)
        self.assertFalse(record.is_deleted)
    
    def test_delete_form(self):
        """测试删除发放单"""
        # 创建一个新的发放单用于测试删除
        new_form = IssueForm.objects.create(
            issue_number='ISSUE-20230101-002',
            issue_date=timezone.now(),
            receiver_name='测试删除',
            receiver_unit='测试单位',
            status='draft',
            issuer=self.user
        )
        
        # 测试删除未归档发放单
        delete_url = reverse('issueform-delete-form', args=[new_form.issue_number])
        data = {'reason': '测试删除原因'}
        response = self.client.post(delete_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        # 验证已经物理删除
        self.assertEqual(IssueForm.objects.filter(id=new_form.id).count(), 0)
        
        # 测试删除已归档发放单
        # 先将原有发放单归档
# CHANGE: [2025-06-07] 更新删除测试的状态检查
        if self.issue_form.status != 'issued':
            self.issue_form.status = 'locked'
            self.issue_form.save()
            self.client.post(reverse('issueform-confirm', args=[self.issue_form.issue_number]))
            self.client.post(reverse('issueform-archive', args=[self.issue_form.issue_number]))
        
        # 删除已归档发放单
        delete_url = reverse('issueform-delete-form', args=[self.issue_form.issue_number])
        data = {'reason': '测试删除已归档发放单'}
        response = self.client.post(delete_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证已软删除
        self.issue_form.refresh_from_db()
        self.assertTrue(self.issue_form.is_deleted)
        self.assertEqual(self.issue_form.status, 'deleted')
        self.assertEqual(self.issue_form.deletion_reason, '测试删除已归档发放单')
    
    def test_available_records(self):
        """测试获取可用于发放的档案记录"""
        url = reverse('issueform-available-records')
        response = self.client.get(url, {'issue_type': 'single'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue('results' in response.data)
        
        # 验证返回了正确的档案记录
        found = False
        for record in response.data['results']:
            if record['sample_number'] == 'API001':
                found = True
                break
        self.assertTrue(found, "未找到预期的档案记录")
        
        # 测试过滤条件
        response = self.client.get(url, {
            'issue_type': 'single',
            'project_name': 'API测试项目'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['project_name'], 'API测试项目')
        
        # 为档案创建发放记录后，应该不再返回
        IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False,
            created_by=self.user
        )
        
        response = self.client.get(url, {'issue_type': 'single'})
        
        # 验证不再返回已有发放记录的档案
        for record in response.data['results']:
            self.assertNotEqual(record['sample_number'], 'API001')
