import pytest
from unittest.mock import patch, MagicMock
from django.contrib.auth.models import User
from archive_processing.models import ProcessingTask, UploadedFile
from archive_processing.tasks.core_tasks import process_pdf_task
from archive_processing.services.pdf_processing_service import PdfProcessingService
import sys
from collections import namedtuple

pytestmark = pytest.mark.django_db(transaction=True)

@pytest.fixture
def test_user(db):
    """创建一个测试用户实例"""
    user, _ = User.objects.get_or_create(
        username='testuser',
        defaults={'password': 'testpassword', 'email': '<EMAIL>'}
    )
    return user

@pytest.fixture
def uploaded_file(db, test_user):
    """创建一个 UploadedFile 实例用于测试"""
    return UploadedFile.objects.create(
        saved_path="/fake/path/to/uploaded_doc.pdf",
        original_name="uploaded_doc.pdf",
        uploader=test_user,
        assigned_box_number="BOX-001",
        file_size=102400
    )

@pytest.fixture
def processing_task(db, uploaded_file):
    """创建一个 ProcessingTask 实例用于测试"""
    return ProcessingTask.objects.create(
        file=uploaded_file,
        status='queued',
        processing_params={'assigned_box_number': uploaded_file.assigned_box_number}
    )

class TestSimpleTaskIntegration:
    """简化版的任务测试类"""
    
    def test_simple_fixture_setup(self, processing_task):
        """简单测试，只验证 fixture 设置是否工作"""
        # 仅验证 fixture 是否正确创建
        assert processing_task is not None
        assert processing_task.status == 'queued'
        assert processing_task.file is not None
        
    @patch('archive_processing.tasks.PdfProcessingService')
    @patch('archive_processing.services.record_update_service.check_records_exist')
    @patch('archive_processing.utils.pdf_utils.calculate_part_ranges')
    @patch('archive_processing.utils.processing_report_utils.create_result_summary')
    def test_minimal_process_task(self, mock_create_summary, mock_calc_ranges, mock_check_records, mock_pdf_service_class, processing_task):
        """最小化的任务处理测试，只验证基本流程"""
        # 设置 mock
        mock_pdf_service = MagicMock()
        mock_pdf_service_class.return_value = mock_pdf_service
        
        # 设置 PDF 处理服务返回具体的失败结果
        mock_result = MagicMock()
        mock_result.success = False
        mock_result.error_message = "模拟错误：测试环境"
        mock_result.unified_numbers = {0: 'NUM-PART1', 5: 'NUM-PART2'}
        
        # 为 split_points 和 stats 提供具体值，避免 TypeError
        mock_result.split_points = [5]  # 实际列表而不是 MagicMock
        
        # 创建一个带有必要属性的 stats 对象
        mock_stats = MagicMock()
        mock_stats.total_pages = 10  # 实际整数而不是 MagicMock
        mock_result.stats = mock_stats
        
        mock_pdf_service.process_pdf_for_splitting_info.return_value = mock_result
        
        # 记录检查服务返回空列表（表示预检查成功）
        mock_check_records.return_value = []
        
        # 创建一个带有 start_page 和 end_page 属性的 PartRange 命名元组
        PartRange = namedtuple('PartRange', ['start_page', 'end_page'])
        part_ranges = [
            PartRange(start_page=0, end_page=5),
            PartRange(start_page=5, end_page=10)
        ]
        
        # 模拟 calculate_part_ranges 返回命名元组列表
        mock_calc_ranges.return_value = part_ranges
        
        # 模拟报告创建
        mock_create_summary.return_value = {
            'success': True,
            'file_path': '/fake/path/to/report.txt',
            'overall_status': 'failed'
        }
        
        # 告诉系统这是测试环境
        sys.modules['pytest'] = MagicMock()
        
        # 调用任务
        result = process_pdf_task(processing_task.task_id)
        
        # 最基本断言：验证服务被调用和任务状态被更新
        mock_pdf_service.process_pdf_for_splitting_info.assert_called_once()
        
        # 刷新任务记录状态
        processing_task.refresh_from_db()
        
        # 验证任务状态（应该是失败，因为模拟结果设置为失败）
        assert processing_task.status == 'failed' 