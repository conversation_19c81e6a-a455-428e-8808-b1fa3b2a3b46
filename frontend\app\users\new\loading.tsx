import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { PageTitle } from "@/components/page-title"

export default function NewUserLoading() {
  return (
    <div className="space-y-6">
      <PageTitle title="新建用户" subtitle="创建新的系统用户并分配权限" />

      <Card>
        <CardContent className="pt-6">
          <div className="space-y-8">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-5 w-1/3" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              ))}
            </div>

            <Skeleton className="h-20 w-full" />

            <Skeleton className="h-32 w-full" />

            <div className="flex justify-end space-x-4">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
