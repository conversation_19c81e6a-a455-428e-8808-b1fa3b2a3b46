"use client"

import React, { useState, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { useActiveSessionNotification } from '@/contexts/domain/records/import/ExcelImportSessionGuard';

interface ActiveSessionNotificationModalProps {
  children: ReactNode;
}

export function ActiveSessionNotificationModal({ children }: ActiveSessionNotificationModalProps) {
  // 使用Hook统一管理的状态，但保留模态框UI渲染
  const { isModalVisible, activeSessionData, hideActiveSessionModal } = useActiveSessionNotification();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  // 初始化时设置不检查状态，因为Hook会统一管理
  useEffect(() => {
    setIsCheckingSession(false);
  }, []);

  const renderContent = () => {
    if (isCheckingSession) {
      return (
        <Card className="p-6">
          <div className="flex flex-col items-center justify-center space-y-3">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-gray-500">检查正在进行的导入会话...</p>
          </div>
        </Card>
      );
    }
    
    return <>{children}</>;
  };

  const renderActiveSessionModal = () => {
    if (!isModalVisible || !activeSessionData) return null;

    return (
      <Dialog open={isModalVisible} onOpenChange={() => hideActiveSessionModal()}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-blue-600 mr-2" />
              系统存在活跃的导入会话
            </DialogTitle>
            <DialogDescription>
              系统当前有一个正在进行或待处理的导入会话，需要您确认如何处理。
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-800 mb-3">会话详细信息</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                <div><span className="font-medium">会话ID:</span> {activeSessionData.sessionId}</div>
                <div><span className="font-medium">当前状态:</span> <span className="font-semibold">{activeSessionData.status}</span></div>
                <div><span className="font-medium">文件名:</span> {activeSessionData.fileName || '-'}</div>
                <div><span className="font-medium">记录数:</span> {activeSessionData.recordCount || 0} 条</div>
                <div>
                  <span className="font-medium">创建者:</span> {activeSessionData.createdBy?.username || "未知用户"}
                  {session?.user?.id && activeSessionData.createdBy?.id && 
                   String(activeSessionData.createdBy.id) === String(session.user.id) && 
                    <span className="text-green-600 font-bold">（本人）</span>
                  }
                </div>
                <div><span className="font-medium">创建时间:</span> {activeSessionData.createdAt ? new Date(activeSessionData.createdAt).toLocaleString() : '未知'}</div>
                {activeSessionData.expiresAt && (
                  <div className="col-span-full">
                    <span className="font-medium">过期时间:</span> 
                    {(() => {
                      const expiresDate = new Date(activeSessionData.expiresAt!);
                      const nowDate = new Date();
                      if (nowDate > expiresDate) {
                        return <span className="text-red-600 font-semibold">已过期</span>;
                      }
                      return `约 ${Math.max(0, Math.floor((expiresDate.getTime() - nowDate.getTime()) / (60 * 1000)))} 分钟后`;
                    })()}
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-amber-50 p-3 rounded border border-amber-200">
              <p className="text-sm font-medium text-amber-800 mb-1">为什么必须先处理此导入?</p>
              <p className="text-sm text-amber-700">Excel导入是系统级操作，同一时间只允许一个导入会话，以避免数据冲突和资源竞争。</p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="default" 
              onClick={() => {
                console.log('ImportPage: 用户确认了解活跃会话状态');
                hideActiveSessionModal();
                toast({
                  title: "会话状态已确认",
                  description: "您可以继续其他操作，或等待其他用户完成当前会话。",
                  duration: 3000,
                });
              }}
              className="w-full"
            >
              我知道了
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <>
      {renderContent()}
      {renderActiveSessionModal()}
    </>
  );
} 