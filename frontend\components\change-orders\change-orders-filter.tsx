"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { ChevronDown, ChevronUp, Search } from "lucide-react"

interface SearchParams {
  keyword: string
  dateRange: { from: Date | undefined; to: Date | undefined }
  type: string
  creator: string
}

interface ChangeOrdersFilterProps {
  searchParams: SearchParams
  setSearchParams: (params: SearchParams) => void
}

export default function ChangeOrdersFilter({ searchParams, setSearchParams }: ChangeOrdersFilterProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [keyword, setKeyword] = useState(searchParams.keyword)
  const [dateRange, setDateRange] = useState(searchParams.dateRange)
  const [type, setType] = useState(searchParams.type)
  const [creator, setCreator] = useState(searchParams.creator)

  const handleSearch = () => {
    setSearchParams({
      keyword,
      dateRange,
      type,
      creator,
    })
  }

  const handleReset = () => {
    setKeyword("")
    setDateRange({ from: undefined, to: undefined })
    setType("")
    setCreator("")
    setSearchParams({
      keyword: "",
      dateRange: { from: undefined, to: undefined },
      type: "",
      creator: "",
    })
  }

  return (
    <div className="rounded-lg border p-4 space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[240px]">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="搜索更改单号、档案编号或标题"
              className="pl-8"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
            />
          </div>
        </div>
        <DateRangePicker
          onChange={(range) => setDateRange(range)}
          className="min-w-[240px]"
        />
        <Button onClick={handleSearch}>搜索</Button>
        <Button variant="outline" onClick={handleReset}>
          重置
        </Button>
        <Button variant="ghost" onClick={() => setShowAdvanced(!showAdvanced)} className="flex items-center gap-1">
          {showAdvanced ? (
            <>
              收起高级筛选
              <ChevronUp className="h-4 w-4" />
            </>
          ) : (
            <>
              展开高级筛选
              <ChevronDown className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>

      {showAdvanced && (
        <div className="flex flex-wrap gap-4 pt-2">
          <div className="w-[240px]">
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue placeholder="更改类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="字段更正">字段更正</SelectItem>
                <SelectItem value="内容补充">内容补充</SelectItem>
                <SelectItem value="批量更新">批量更新</SelectItem>
                <SelectItem value="其他">其他</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-[240px]">
            <Select value={creator} onValueChange={setCreator}>
              <SelectTrigger>
                <SelectValue placeholder="创建人" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部创建人</SelectItem>
                <SelectItem value="张三">张三</SelectItem>
                <SelectItem value="李四">李四</SelectItem>
                <SelectItem value="王五">王五</SelectItem>
                <SelectItem value="赵六">赵六</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  )
}
