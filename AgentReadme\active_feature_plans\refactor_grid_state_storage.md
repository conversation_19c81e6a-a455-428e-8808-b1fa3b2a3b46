# Feature Plan: Refactor Grid State Local Storage

**Status**: Proposed

## 1. 目标与动机 (Goal & Motivation)

当前，`frontend/app/records/ledger/page.tsx` (档案台账页面) 直接使用浏览器原生的 `localStorage` API 来持久化存储 AG Grid 的列状态（显示/隐藏、顺序、宽度等）。虽然功能可用，但这导致了以下问题：

* **逻辑分散**: 本地存储的操作逻辑散布在页面组件代码中。
* **缺乏封装**: 未利用项目中已有的 `localStorageService` (`frontend/services/local-storage-service.ts`) 提供的通用封装（如 `window` 检查）。
* **可维护性**: 如果未来需要修改存储键名、增加错误处理或支持其他表格的状态存储，直接修改页面组件会比较困难。

**本次重构的目标是**:

* 将 AG Grid 列状态的本地存储逻辑**集中管理**。
* 利用或扩展现有的**服务层模式**来处理本地存储。
* 提高 `RecordsLedgerPage` 组件的**代码清晰度**和**可维护性**。

## 2. 方案设计 (Design)

我们提议创建一个新的、专门的服务来处理 AG Grid 状态的本地存储，而不是直接修改通用的 `localStorageService`，以保持职责分离。

**新服务**: `gridStateService`

* **位置**: `frontend/services/grid-state-service.ts`
* **职责**: 封装获取、保存和移除特定 AG Grid 实例状态到 `localStorage` 的逻辑。
* **内部依赖**: 该服务将内部调用 `localStorageService` 来执行实际的 `localStorage` 操作，并负责处理 JSON 的序列化和反序列化。

**服务接口 (Interface)**:

```typescript
// 定义 gridId 类型，允许传入字符串作为表格的唯一标识
type GridId = string;

interface GridStateService {
  /**
   * 根据 gridId 获取存储的列状态。
   * @param gridId 表格的唯一标识符 (e.g., 'recordsLedger')
   * @returns 解析后的状态对象，如果未找到或解析失败则返回 null。
   */
  getGridColumnState(gridId: GridId): any | null;

  /**
   * 将指定 gridId 的列状态保存到 localStorage。
   * @param gridId 表格的唯一标识符。
   * @param state 要保存的状态对象。
   */
  saveGridColumnState(gridId: GridId, state: any): void;

  /**
   * 根据 gridId 移除存储的列状态。
   * @param gridId 表格的唯一标识符。
   */
  removeGridColumnState(gridId: GridId): void;
}
```

**存储键名 (Storage Key)**:

服务内部将使用类似 `gridColumnState_${gridId}` 的模式来生成 `localStorage` 中的键名，确保不同表格的状态不会冲突。例如，对于档案台账，键名将是 `gridColumnState_recordsLedger`。

## 3. 实施步骤 (Implementation Steps)

1. **创建服务文件**:
    * 在 `frontend/services/` 目录下创建新文件 `grid-state-service.ts`。
2. **实现服务逻辑**:
    * 在 `grid-state-service.ts` 中导入 `localStorageService`。
    * 实现 `getGridColumnState`, `saveGridColumnState`, `removeGridColumnState` 三个函数。
    * 在函数内部：
        * 构造存储键名 (e.g., `const key = \`gridColumnState_${gridId}\`;`)。
        * 调用 `localStorageService.getItem/setItem/removeItem`。
        * 处理 `JSON.stringify` 和 `JSON.parse`（包括错误处理）。
3. **重构 `RecordsLedgerPage`**:
    * 打开 `frontend/app/records/ledger/page.tsx`。
    * **导入**: `import { gridStateService } from '@/services/grid-state-service';` (假设服务以单例对象导出)。
    * **修改 `useEffect` (加载状态)**:
        * 替换 `localStorage.getItem('recordsGridColumnState')` 为 `gridStateService.getGridColumnState('recordsLedger')`。
        * 移除内部的 `JSON.parse` 逻辑，因为服务已处理。
    * **修改 `saveColumnStateToLocalStorage` (保存状态)**:
        * 替换 `localStorage.setItem('recordsGridColumnState', JSON.stringify(columnState))` 为 `gridStateService.saveGridColumnState('recordsLedger', columnState)`。
        * 移除内部的 `JSON.stringify` 逻辑。
    * **修改 `clearSavedColumnState` (重置状态)**:
        * 替换 `localStorage.removeItem('recordsGridColumnState')` 为 `gridStateService.removeGridColumnState('recordsLedger')`。
4. **测试**:
    * 验证档案台账页面的列状态保存、加载和重置功能是否仍然正常工作。

## 4. 预期收益 (Expected Benefits)

* **代码整洁**: `RecordsLedgerPage` 组件不再包含直接的 `localStorage` 操作和 JSON 处理逻辑。
* **逻辑集中**: 所有与 Grid 状态存储相关的逻辑都集中在 `gridStateService` 中。
* **可维护性**: 未来修改存储逻辑或键名只需修改服务文件。
* **可扩展性**: 可以轻松地为项目中的其他 AG Grid 表格（如果需要）添加状态持久化，只需调用服务时传入不同的 `gridId` 即可。
* **安全性**: 利用了 `localStorageService` 中已有的 `window` 存在性检查。

## 5. 风险与考虑 (Risks & Considerations)

* 风险较低，主要是代码移动和封装。
* 需要确保 `gridStateService` 的实现正确处理了 JSON 解析错误等边缘情况。
* 可以考虑 `gridStateService` 是否应该导出为单例对象还是类。对于简单的本地存储操作，导出单例对象通常足够。

## 6. 后续 (Next Steps)

* 评审此计划。
* 获得批准后，按照实施步骤进行编码。
