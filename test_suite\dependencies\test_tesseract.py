"""
Tesseract OCR依赖测试模块

此模块用于验证Tesseract OCR的安装和配置是否正确。测试包括：
1. 检查Tesseract可执行文件是否存在
2. 检查语言数据文件是否存在
3. 测试基本OCR功能，确认能正确识别英文文本
4. 测试中文OCR功能，确认能正确识别中文文本

这是系统依赖测试的一部分，用于确保PDF分割功能所需的OCR组件正常工作。
可以通过以下方式运行：
- 单独运行: python tests/dependencies/test_tesseract.py
- 作为依赖测试的一部分: python tests/dependencies/test_all_dependencies.py

测试成功会返回True，失败返回False并输出错误信息。
"""

import os
import sys
import pytesseract
from PIL import Image, ImageDraw

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')
import django
django.setup()

from django.conf import settings

# 定义测试输出目录
TEST_OUTPUT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'output'))

def test_tesseract_installation():
    """测试Tesseract OCR是否正确安装和配置"""
    print("=" * 50)
    print("测试Tesseract OCR安装")
    print("=" * 50)
    
    # 打印配置信息
    tesseract_cmd = settings.TESSERACT_CMD_PATH
    tessdata_dir = settings.TESSDATA_PREFIX
    
    print(f"Tesseract 路径: {tesseract_cmd}")
    print(f"TESSDATA_PREFIX: {tessdata_dir}")
    
    # 检查文件是否存在
    if not os.path.exists(tesseract_cmd):
        print(f"错误: Tesseract 可执行文件不存在: {tesseract_cmd}")
        return False
    else:
        print(f"Tesseract 可执行文件存在 ✓")
    
    if not os.path.exists(tessdata_dir):
        print(f"错误: TESSDATA 目录不存在: {tessdata_dir}")
        return False
    else:
        print(f"TESSDATA 目录存在 ✓")
        
        # 检查语言文件
        eng_path = os.path.join(tessdata_dir, 'eng.traineddata')
        chi_path = os.path.join(tessdata_dir, 'chi_sim.traineddata')
        
        if os.path.exists(eng_path):
            print(f"英语语言文件存在 ✓")
        else:
            print(f"警告: 英语语言文件不存在: {eng_path}")
            
        if os.path.exists(chi_path):
            print(f"中文语言文件存在 ✓")
        else:
            print(f"警告: 中文语言文件不存在: {chi_path}")
    
    # 设置环境变量和命令路径
    os.environ['TESSDATA_PREFIX'] = tessdata_dir
    pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
    
    # 测试OCR功能
    try:
        # 创建测试目录
        os.makedirs(TEST_OUTPUT_DIR, exist_ok=True)
        
        # 创建测试图像
        img = Image.new('RGB', (300, 100), color='white')
        d = ImageDraw.Draw(img)
        d.text((10, 10), "Hello World! 测试中文", fill='black')
        test_image_path = os.path.join(TEST_OUTPUT_DIR, "tesseract_test_image.png")
        img.save(test_image_path)
        print(f"已创建测试图像: {test_image_path}")
        
        # 执行OCR
        text = pytesseract.image_to_string(Image.open(test_image_path))
        print(f"OCR识别结果: {text}")
        
        if "Hello World" in text:
            print("基本OCR测试通过 ✓")
        else:
            print("警告: OCR可能未正确识别英文")
            
        # 测试中文识别
        try:
            text_chi = pytesseract.image_to_string(Image.open(test_image_path), lang='chi_sim')
            print(f"中文OCR识别结果: {text_chi}")
            
            if "测试中文" in text_chi:
                print("中文OCR测试通过 ✓")
            else:
                print("警告: OCR可能未正确识别中文")
                
        except Exception as e:
            print(f"中文OCR测试失败: {e}")
            return False
            
        return True
            
    except Exception as e:
        print(f"OCR测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_tesseract_installation()
    if success:
        print("\n✅ Tesseract OCR 测试通过!")
    else:
        print("\n❌ Tesseract OCR 测试失败!")
        sys.exit(1)
