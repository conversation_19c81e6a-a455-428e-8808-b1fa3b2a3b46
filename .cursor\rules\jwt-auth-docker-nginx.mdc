---
description: prompt示例： "认证功能", "权限问题", "用户登录", "API安全", "JWT配置"
globs: 
alwaysApply: false
---
# JWT认证及Docker-Nginx环境规则

**作用**: 指导所有涉及认证、授权和权限的开发工作, 确保与现有JWT实现和部署环境兼容。

**触发指令示例**: "认证功能", "权限问题", "用户登录", "API安全", "JWT配置"

## 系统架构概述

本项目采用以下架构部署:

- **前端**: Next.js应用
- **后端**: Django REST Framework
- **部署方式**: Docker Compose
- **反向代理**: Nginx
- **认证方式**: JWT (JSON Web Token)

## JWT认证实现详情

### 1. 认证流程

1. **用户登录**:
   - 前端通过`/api/auth/login`发送凭据
   - 后端验证成功后返回JWT token
   - 前端将token存储在Cookie中

2. **API请求授权**:
   - 前端在请求头添加`Authorization: Bearer <token>`
   - Nginx转发请求到Django后端
   - Django使用JWT验证token并识别用户

3. **会话维护**:
   - 访问令牌有效期为1天
   - 刷新令牌有效期为7天
   - 前端通过`/api/auth/refresh`接口刷新token

### 2. 关键配置详情

#### Django设置 (settings.py)

```python
# JWT配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'AUTH_HEADER_TYPES': ('Bearer',),
}
```

#### 认证API端点

```tips
/api/auth/login - 登录获取JWT令牌
/api/auth/refresh - 刷新JWT令牌
/api/auth/me - 获取当前用户信息
```

#### 前端认证上下文 (auth-context.tsx)

- 管理token存储和刷新
- 提供登录/注销功能
- 处理权限检查

## Docker与Nginx配置要点

### 1. Docker Compose部署

- 项目使用Docker Compose管理多个容器服务
- `web`服务运行Django后端
- `nginx`服务处理请求路由和静态文件
- 环境变量通过Docker Compose传递给容器

### 2. Nginx配置关键点

- 请求路径保留`/api`前缀
- Nginx负责将API请求转发到Django容器
- 静态文件和媒体文件由Nginx直接提供
- WebSocket连接也通过Nginx代理

### 3. 请求流处理

```tips
客户端 → Nginx → Django (JWT认证) → 业务逻辑处理 → 响应
```

## 安全考虑事项

1. **跨域配置**:
   - CORS设置已配置允许前端访问
   - 确保新API端点正确处理CORS

2. **令牌安全**:
   - JWT签名密钥保护是关键
   - 生产环境应使用环境变量传递密钥
   - 不应在代码或容器中硬编码SECRET_KEY

3. **Nginx安全头**:
   - 生产环境应添加安全HTTP头
   - 考虑添加XSS保护头
   - 应配置适当的内容安全策略

## 开发指南

### 添加新的权限控制API

1. **必须确认JWT兼容性**:
   - 使用`permission_classes = [IsAuthenticated]`保护视图
   - 确保从`request.user`正确获取用户信息
   - 测试时验证带JWT的请求能正确识别用户

2. **权限粒度控制**:
   - 使用DRF的权限类进行控制
   - 视图级权限: `permission_classes`
   - 对象级权限: 覆盖`get_queryset()`或实现自定义权限类

### Docker环境下的调试

1. **查看Nginx日志**:
   - 对于401/403错误, 检查Nginx日志
   - 验证请求头是否正确传递

2. **容器内测试**:
   - 使用`docker-compose exec web python manage.py shell`进入Django环境
   - 可直接测试认证逻辑

3. **注意容器网络**:
   - 服务间通信使用Docker网络中的服务名
   - Redis和其他服务应通过服务名而非localhost访问

## 常见问题及解决方案

1. **Token无效或过期**:
   - 确认前端正确使用refresh机制
   - 验证系统时间同步
   - 检查日志中的JWT解码错误

2. **权限错误**:
   - 确认用户具有所需权限
   - 验证权限检查逻辑
   - 确认JWT解码后user对象正确

3. **Nginx代理问题**:
   - 确认/api路径前缀保留
   - 验证Nginx转发规则
   - 检查请求头传递配置

