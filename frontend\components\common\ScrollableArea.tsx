import React from 'react';
import { cn } from '@/lib/utils'; // Assuming you have a cn utility for classnames

interface ScrollableAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const ScrollableArea: React.FC<ScrollableAreaProps> = ({ children, className, ...props }) => {
  return (
    <div
      className={cn(
        "flex-1 overflow-y-auto min-h-0", // Core scrolling classes
        className // Allow merging additional classes
      )}
      {...props} // Pass down other div props like id, etc.
    >
      {children}
    </div>
  );
};

export default ScrollableArea; 