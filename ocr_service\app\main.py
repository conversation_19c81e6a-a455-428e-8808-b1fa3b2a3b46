"""
OCR 微服务主应用
"""
import time
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any
import psutil
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

# 标准的相对导入
from .config import settings
from .logging_config import setup_logging, get_logger
from .models import OCRRequest, OCRResponse, HealthStatus, MetricsResponse, OCRMode
from .ocr_engine import ocr_engine
from .image_processor import image_processor
from .metrics import metrics_manager
from .middleware import RequestLimitMiddleware, RequestTrackingMiddleware

# 设置日志
setup_logging()
logger = get_logger(__name__)

# 应用启动时间
app_start_time = time.time()

# 活跃请求计数器
active_requests = 0


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("OCR 服务启动中", version=settings.app_version)
    
    # 初始化 OCR 引擎
    success = await ocr_engine.initialize()
    if not success:
        logger.error("OCR 引擎初始化失败，服务将无法正常工作")
    
    # 启动指标收集
    if settings.enable_metrics:
        metrics_manager.start()
    
    logger.info("OCR 服务启动完成", 
               ocr_ready=ocr_engine.is_ready(),
               metrics_enabled=settings.enable_metrics)
    
    yield
    
    # 关闭时清理
    logger.info("OCR 服务关闭中")
    
    if settings.enable_metrics:
        metrics_manager.stop()
    
    logger.info("OCR 服务已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="高性能 OCR 微服务，基于 PaddleOCR",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(RequestLimitMiddleware, max_concurrent=settings.max_concurrent_requests)
app.add_middleware(RequestTrackingMiddleware)


async def get_request_data(request: OCRRequest = None) -> OCRRequest:
    """获取请求数据"""
    return request or OCRRequest()


@app.get("/", response_model=Dict[str, Any])
async def root():
    """根端点"""
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "ocr_ready": ocr_engine.is_ready(),
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=HealthStatus)
async def health_check():
    """健康检查端点"""
    global active_requests
    
    # 获取内存使用情况
    process = psutil.Process()
    memory_info = process.memory_info()
    
    uptime = time.time() - app_start_time
    
    status = HealthStatus(
        status="healthy" if ocr_engine.is_ready() else "unhealthy",
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        version=settings.app_version,
        uptime=uptime,
        ocr_engine_ready=ocr_engine.is_ready(),
        memory_usage={
            "rss": memory_info.rss,
            "vms": memory_info.vms,
            "percent": process.memory_percent()
        },
        active_requests=active_requests
    )
    
    return status


@app.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """获取服务指标"""
    if not settings.enable_metrics:
        raise HTTPException(status_code=404, detail="指标收集未启用")
    
    stats = metrics_manager.get_stats()
    
    # 获取内存使用情况
    process = psutil.Process()
    memory_info = process.memory_info()
    
    return MetricsResponse(
        total_requests=stats["total_requests"],
        successful_requests=stats["successful_requests"],
        failed_requests=stats["failed_requests"],
        average_processing_time=stats["average_processing_time"],
        active_requests=active_requests,
        uptime=time.time() - app_start_time,
        memory_usage={
            "rss": memory_info.rss,
            "vms": memory_info.vms,
            "percent": process.memory_percent()
        }
    )


@app.post("/ocr", response_model=OCRResponse)
async def perform_ocr(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    request_data: OCRRequest = Depends(get_request_data)
):
    """执行 OCR 识别"""
    global active_requests
    active_requests += 1
    
    start_time = time.time()
    
    try:
        # 检查 OCR 引擎状态
        if not ocr_engine.is_ready():
            raise HTTPException(status_code=503, detail="OCR 引擎未就绪")
        
        # 读取图像数据
        image_data = await file.read()
        
        # 验证图像
        is_valid, error_msg = image_processor.validate_image(image_data)
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 加载图像
        image = image_processor.load_image(image_data)
        
        logger.info("开始 OCR 处理", 
                   mode=request_data.mode,
                   image_size=image.size,
                   file_size=len(image_data))
        
        # 根据模式执行 OCR
        if request_data.mode == OCRMode.BASIC:
            result = await _perform_basic_ocr(image, request_data)
        else:
            result = await _perform_enhanced_ocr(image, request_data)
        
        processing_time = time.time() - start_time
        result.processing_time = processing_time
        result.image_size = {"width": image.size[0], "height": image.size[1]}
        
        # 记录指标
        if settings.enable_metrics:
            background_tasks.add_task(
                metrics_manager.record_request, 
                success=result.success, 
                processing_time=processing_time
            )
        
        logger.info("OCR 处理完成", 
                   mode=request_data.mode,
                   success=result.success,
                   processing_time=processing_time)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("OCR 处理失败", error=str(e), exc_info=True)
        
        # 记录失败指标
        if settings.enable_metrics:
            background_tasks.add_task(
                metrics_manager.record_request, 
                success=False, 
                processing_time=time.time() - start_time
            )
        
        raise HTTPException(status_code=500, detail=f"OCR 处理失败: {str(e)}")
    
    finally:
        active_requests -= 1


async def _perform_basic_ocr(image, request_data: OCRRequest) -> OCRResponse:
    """执行基础 OCR"""
    # 直接使用原始图像，保持与原业务逻辑一致
    text = await ocr_engine.recognize_basic(image)
    
    return OCRResponse(
        success=bool(text),
        mode=OCRMode.BASIC,
        text=text,
        processing_time=0.0  # 将在外层设置
    )


async def _perform_enhanced_ocr(image, request_data: OCRRequest) -> OCRResponse:
    """执行增强 OCR"""
    # 生成图像变体
    enhanced_images = image_processor.get_enhanced_images(image, request_data.max_attempts)

    # 执行增强识别
    text_results = await ocr_engine.recognize_enhanced(enhanced_images)
    
    # 检查是否找到目标文本
    found_target = False
    if request_data.target_text:
        for result in text_results:
            if request_data.target_text in result.text:
                found_target = True
                if not request_data.collect_all_results:
                    break
    
    return OCRResponse(
        success=len(text_results) > 0,
        mode=OCRMode.ENHANCED,
        text_results=text_results,
        found_target=found_target if request_data.target_text else None,
        processing_time=0.0  # 将在外层设置
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers,
        log_level=settings.log_level.lower()
    )
