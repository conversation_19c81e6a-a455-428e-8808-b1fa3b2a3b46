from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
from archive_records.models import ArchiveRecord


class IssueForm(models.Model):
    """
    发放单模型

    集中管理一批待发放报告的文档，支持第一次和第二次发放类型。
    发放单生命周期：创建（草稿状态）→ 锁定（用户自确认）→ 发放并归档
    """
    # 基础信息
    # issue_number 现在由 save 方法自动生成，且在admin中不可编辑
    issue_number = models.CharField(max_length=50, unique=True, editable=False, blank=True, verbose_name='发放单编号')
    issue_date = models.DateTimeField(verbose_name='发放日期')
    
    # 领取方信息
    receiver_name = models.CharField(max_length=100, verbose_name='领取人姓名')
    receiver_unit = models.CharField(max_length=200, verbose_name='领取单位')
    receiver_phone = models.CharField(max_length=50, verbose_name='领取人电话')
    
    # 状态管理 - 简化为三个核心状态
    status = models.CharField(
        choices=[
            ('draft', '草稿'),      # 创建和编辑阶段
            ('locked', '锁定'),     # 用户自确认，锁定编辑
            ('issued', '已发放'),   # 发放并归档，生成发放记录
            ('deleted', '已删除'),   # 已删除，软删除，不可编辑
        ], 
        default='draft', 
        max_length=20, 
        verbose_name='状态'
    )
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    
    # 操作人信息
    issuer = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.PROTECT, 
        related_name='issued_forms', 
        verbose_name='发放人'
    )
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="deleted_issue_forms",
        verbose_name="删除人"
    )
    
    # 文件信息（签字确认单，不需要上传，前端直接生成，后端不需要保存了，可删除字段）
    confirmation_file = models.FileField(
        blank=True, 
        null=True, 
        upload_to='issue_confirmations/%Y/%m/', 
        verbose_name='签字确认单'
    )
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="删除时间")
    
    # 其他信息
    notes = models.TextField(null=True, blank=True, verbose_name='备注说明')
    deletion_reason = models.TextField(blank=True, verbose_name="删除原因")
    
    class Meta:
        verbose_name = "发放单"
        verbose_name_plural = "发放单"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['issue_number']),
            models.Index(fields=['status']),
            models.Index(fields=['issue_date']),
            models.Index(fields=['is_deleted']),
        ]
        
    def __str__(self):
        return f"{self.issue_number} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        """
        重写save方法，在首次创建时自动生成唯一的发放单编号。
        """
        # 检查是否为新创建的实例 (self.pk is None)
        if not self.pk:
            # 循环以确保生成的编号是唯一的
            while True:
                # 生成规则: "ISSUE" + YYYYMMDD + "-" + 4位随机十六进制字符
                today_str = timezone.now().strftime('%Y%m%d')
                random_part = uuid.uuid4().hex[:4].upper()
                new_issue_number = f"ISSUE-{today_str}-{random_part}"
                
                # 检查数据库中是否已存在该编号
                if not IssueForm.objects.filter(issue_number=new_issue_number).exists():
                    self.issue_number = new_issue_number
                    break
        
        super().save(*args, **kwargs)
    
    def can_edit(self):
        """检查发放单是否可编辑"""
        return self.status == 'draft'
    
    def can_lock(self):
        """检查发放单是否可锁定"""
        return self.status == 'draft'
    
    def can_unlock(self):
        """检查发放单是否可解锁回到草稿状态"""
        return self.status == 'locked'
    
    def can_issue(self):
        """检查发放单是否可发放"""
        return self.status == 'locked'
    
    def can_hard_delete(self):
        """检查发放单是否可硬删除"""
        return self.status == 'draft'
    
    def can_soft_delete(self):
        """检查发放单是否可软删除"""
        return self.status == 'issued' and not self.is_deleted
    
    def get_first_issue_items(self):
        """获取第一次发放的项目"""
        return self.items.filter(first=True)
    
    def get_second_issue_items(self):
        """获取第二次发放的项目"""
        return self.items.filter(second=True)
    
    def get_issue_summary(self):
        """获取发放汇总信息"""
        first_items = self.get_first_issue_items()
        second_items = self.get_second_issue_items()
        
        return {
            'first_issue': {
                'count': first_items.count(),
                'total_copies': sum(item.copies for item in first_items)
            },
            'second_issue': {
                'count': second_items.count(),
                'total_copies': sum(item.copies for item in second_items)
            }
        }
    
    def to_dict(self):
        """将模型实例转换为字典，用于API响应"""
        return {
            'id': self.id,
            'issue_number': self.issue_number,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'receiver_name': self.receiver_name,
            'receiver_unit': self.receiver_unit,
            'receiver_phone': self.receiver_phone,
            'status': self.status,
            'status_display': self.get_status_display(),
            'issuer_id': self.issuer_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'notes': self.notes
        }
    
    def delete(self, *args, **kwargs):
        """删除发放单时清理ArchiveRecord的相关字段"""
        # 清理第一次发放记录
        for item in self.get_first_issue_items():
            self._clear_archive_first_issue(item.archive_record)
        
        # 清理第二次发放记录
        for item in self.get_second_issue_items():
            self._clear_archive_second_issue(item.archive_record)
        
        super().delete(*args, **kwargs)
    
    def _clear_archive_first_issue(self, archive_record):
        """清理档案的第一次发放记录"""
        archive_record.first_issue_copies = None
        archive_record.first_issue_datetime = None
        archive_record.first_issue_person = None
        archive_record.first_receiver_name = None
        archive_record.first_receiver_unit = None
        archive_record.first_receiver_phone = None
        archive_record.save(update_fields=[
            'first_issue_copies', 'first_issue_datetime', 'first_issue_person',
            'first_receiver_name', 'first_receiver_unit', 'first_receiver_phone',
            'updated_at'
        ])
    
    def _clear_archive_second_issue(self, archive_record):
        """清理档案的第二次发放记录"""
        archive_record.second_issue_copies = None
        archive_record.second_issue_datetime = None
        archive_record.second_issue_person = None
        archive_record.second_receiver_name = None
        archive_record.second_receiver_unit = None
        archive_record.second_receiver_phone = None
        archive_record.save(update_fields=[
            'second_issue_copies', 'second_issue_datetime', 'second_issue_person',
            'second_receiver_name', 'second_receiver_unit', 'second_receiver_phone',
            'updated_at'
        ])


class IssueFormItem(models.Model):
    """
    发放单条目模型 - 增强版
    
    发放单中的每个具体档案项，关联到具体的档案记录。
    通过first和second字段明确标识发放次序，确保数据一致性。
    """
    # 关联关系
    issue_form = models.ForeignKey(
        IssueForm, 
        on_delete=models.CASCADE, 
        related_name='items', 
        verbose_name='发放单'
    )
    archive_record = models.ForeignKey(
        'archive_records.ArchiveRecord', 
        on_delete=models.PROTECT,  # 防止误删档案记录
        related_name='issue_items', 
        verbose_name='档案记录'
    )
    
    # 发放信息
    copies = models.PositiveIntegerField(verbose_name='发放份数')
    
    # 发放次序字段
    first = models.BooleanField(default=False, verbose_name="是否为第一次发放")
    second = models.BooleanField(default=False, verbose_name="是否为第二次发放")
    
    # 领取方信息 - 可覆盖发放单默认值
    receiver_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='领取人姓名')
    receiver_unit = models.CharField(max_length=200, blank=True, null=True, verbose_name='领取单位')
    receiver_phone = models.CharField(max_length=50, blank=True, null=True, verbose_name='领取人电话')
    
    notes = models.TextField(null=True, blank=True, verbose_name="备注")
    
    class Meta:
        verbose_name = "发放单条目"
        verbose_name_plural = "发放单条目"
        db_table = 'report_issuing_issueformitem'
        constraints = [
            # 确保每个档案在同一发放单中只能有一条记录
            models.UniqueConstraint(
                fields=['issue_form', 'archive_record'],
                name='unique_issue_form_archive'
            ),
            # 确保first和second不能同时为True
            models.CheckConstraint(
                check=~(models.Q(first=True) & models.Q(second=True)),
                name='not_both_first_and_second'
            ),
            # 确保first和second至少有一个为True
            models.CheckConstraint(
                check=models.Q(first=True) | models.Q(second=True),
                name='must_be_first_or_second'
            )
        ]
        indexes = [
            models.Index(fields=['issue_form', 'archive_record']),
            models.Index(fields=['archive_record', 'first']),
            models.Index(fields=['archive_record', 'second']),
            models.Index(fields=['issue_form', 'first']),
            models.Index(fields=['issue_form', 'second']),
        ]
    
    def clean(self):
        """模型验证"""
        from django.core.exceptions import ValidationError
        super().clean()
        
        # 验证first和second字段的逻辑
        if self.first and self.second:
            raise ValidationError("不能同时标记为第一次发放和第二次发放")
        
        if not self.first and not self.second:
            raise ValidationError("必须标记为第一次发放或第二次发放")
        
        # 验证发放次序的业务逻辑
        if self.archive_record_id:
            self._validate_issue_sequence()
    
    def _validate_issue_sequence(self):
        """验证发放次序"""
        from django.core.exceptions import ValidationError
        archive = self.archive_record
        
        if self.first:
            # 第一次发放验证
            if archive.first_issue_copies and archive.first_issue_copies > 0:
                # 检查是否已有其他有效的第一次发放记录
                existing_first = IssueFormItem.objects.filter(
                    archive_record=archive,
                    first=True,
                    issue_form__is_deleted=False
                ).exclude(pk=self.pk).exists()
                
                if existing_first:
                    raise ValidationError("该档案已有第一次发放记录")
        
        elif self.second:
            # 第二次发放验证
            if archive.second_issue_copies and archive.second_issue_copies > 0:
                # 检查是否已有其他有效的第二次发放记录
                existing_second = IssueFormItem.objects.filter(
                    archive_record=archive,
                    second=True,
                    issue_form__is_deleted=False
                ).exclude(pk=self.pk).exists()
                
                if existing_second:
                    raise ValidationError("该档案已有第二次发放记录")
            
            # 验证是否有第一次发放记录
            has_first_issue = (
                archive.first_issue_copies and archive.first_issue_copies > 0 and
                IssueFormItem.objects.filter(
                    archive_record=archive,
                    first=True,
                    issue_form__is_deleted=False
                ).exists()
            )
            
            if not has_first_issue:
                raise ValidationError("第二次发放前必须有第一次发放记录")
    
    def save(self, *args, **kwargs):
        """保存时同步更新ArchiveRecord"""
        self.clean()
        super().save(*args, **kwargs)
        
        # 同步更新ArchiveRecord的发放字段
        self._sync_archive_record()
    
    def _sync_archive_record(self):
        """同步更新ArchiveRecord的发放相关字段"""
        archive = self.archive_record
        issue_form = self.issue_form
        
        if self.first:
            # 更新第一次发放信息
            archive.first_issue_copies = self.copies
            archive.first_issue_datetime = issue_form.issue_date
            archive.first_issue_person = issue_form.issuer.get_full_name() if issue_form.issuer else ''
            archive.first_receiver_name = issue_form.receiver_name
            archive.first_receiver_unit = issue_form.receiver_unit
            archive.first_receiver_phone = issue_form.receiver_phone
        
        elif self.second:
            # 更新第二次发放信息
            archive.second_issue_copies = self.copies
            archive.second_issue_datetime = issue_form.issue_date
            archive.second_issue_person = issue_form.issuer.get_full_name() if issue_form.issuer else ''
            archive.second_receiver_name = issue_form.receiver_name
            archive.second_receiver_unit = issue_form.receiver_unit
            archive.second_receiver_phone = issue_form.receiver_phone
        
        archive.save(update_fields=[
            'first_issue_copies', 'first_issue_datetime', 'first_issue_person',
            'first_receiver_name', 'first_receiver_unit', 'first_receiver_phone',
            'second_issue_copies', 'second_issue_datetime', 'second_issue_person',
            'second_receiver_name', 'second_receiver_unit', 'second_receiver_phone',
            'updated_at'
        ])
    
    @property
    def issue_type_display(self):
        """返回发放类型显示名称"""
        if self.first:
            return '第一次发放'
        elif self.second:
            return '第二次发放'
        return '未知类型'
    
    @property
    def issue_sequence(self):
        """返回发放次序"""
        if self.first:
            return 'first'
        elif self.second:
            return 'second'
        return None
        
    def __str__(self):
        issue_type = "第一次" if self.first else "第二次"
        return f"{self.issue_form.issue_number} - {self.archive_record.unified_number} ({issue_type}发放{self.copies}份)"


class IssueRecord(models.Model):
    """
    报告发放记录模型
    
    记录档案报告的发放历史，包括发放时间、发放人、领取人等信息。
    支持两种发放次数类型：
    - 第一次发放(first)：档案的首次发放
    - 第二次发放(second)：档案的二次发放
    """
    # 基本关联
    archive_record = models.ForeignKey(
        'archive_records.ArchiveRecord', 
        on_delete=models.CASCADE,
        related_name="issue_records",
        verbose_name="档案记录"
    )
    
    # 发放信息
    issue_type = models.CharField(
        max_length=20,
        choices=[('first', '第一次发放'), ('second', '第二次发放')],
        default='first',
        verbose_name="发放次数类型"
    )
    issue_date = models.DateTimeField(verbose_name="发放时间")
    issuer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="issued_records",
        verbose_name="发放人"
    )
    
    # 完整的领取方信息
    receiver_name = models.CharField(max_length=100, verbose_name="领取人姓名")
    receiver_unit = models.CharField(max_length=200, verbose_name="领取单位")
    receiver_phone = models.CharField(max_length=50, blank=True, null=True, verbose_name="领取人电话")
    
    # 发放份数
    copies = models.PositiveIntegerField(default=1, verbose_name="发放份数")
    
    # 关联发放单
    issue_form = models.ForeignKey(
        IssueForm,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="issue_records",
        verbose_name="关联发放单"
    )
    
    # 状态标记
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_issue_records",
        verbose_name="创建人"
    )
    notes = models.TextField(null=True, blank=True, verbose_name="备注")
    
    class Meta:
        verbose_name = "发放记录"
        verbose_name_plural = "发放记录"
        indexes = [
            models.Index(fields=['archive_record', 'issue_type', 'is_active']),
            models.Index(fields=['issue_form']),
            models.Index(fields=['is_deleted']),
        ]
        
    def __str__(self):
        return f"{self.archive_record.report_number} - {self.get_issue_type_display()} ({self.issue_date.strftime('%Y-%m-%d')})"


class IssueRecordHistory(models.Model):
    """
    发放记录历史模型
    
    记录发放记录的所有变更历史，包括创建、更新、删除等操作。
    """
    # 基本关联
    issue_record = models.ForeignKey(
        IssueRecord,
        on_delete=models.CASCADE,
        related_name="history_records",
        verbose_name="发放记录"
    )
    
    # 业务操作信息
    action = models.CharField(
        max_length=30,
        choices=[
            ('archive_table_modify', '总台账表修改'),
            ('issue_form_create', '发放单创建'),
            ('issue_form_delete', '发放单删除')
        ],
        verbose_name="业务动作"
    )
    
    # 变更数据
    before_data = models.JSONField(null=True, blank=True, verbose_name="修改前数据")
    after_data = models.JSONField(null=True, blank=True, verbose_name="修改后数据")
    
    # 操作元数据
    operation_time = models.DateTimeField(auto_now_add=True, verbose_name="操作时间")
    operator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name="操作人"
    )
    operation_reason = models.TextField(blank=True, verbose_name="操作原因")
    
    class Meta:
        verbose_name = "发放记录历史"
        verbose_name_plural = "发放记录历史"
        ordering = ['-operation_time']
        indexes = [
            models.Index(fields=['issue_record', 'operation_time']),
            models.Index(fields=['operator', 'operation_time']),
        ]
        
    def __str__(self):
        return f"{self.issue_record} - {self.get_action_display()} ({self.operation_time.strftime('%Y-%m-%d %H:%M')})"
