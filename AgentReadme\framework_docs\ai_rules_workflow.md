# AI 规则交互工作流 (ai_rules_workflow.md)

**目的**: 本文档使用 Mermaid 流程图可视化 AI 代理在处理不同用户输入和任务时，核心规则之间是如何交互和协作的。这有助于理解 AI 的决策逻辑和工作流程。

**注意**: 此图是简化表示，重点突出主要规则的触发和流转。实际交互可能更复杂。

```mermaid
graph TD
    Start((User Input)) --> CheckInputType{Input Type?};

    CheckInputType -- Vague Update Request? --> DocOverviewRule["@document-overview"];
    DocOverviewRule --> ClarifyOrPropose{Clarify Intent or Propose Update};
    ClarifyOrPropose -- Propose Updates --> ExecuteUpdate[Execute Proposed Update];
    ClarifyOrPropose -- Clarified Task --> ReadmeWorkflowRule["@readme-driven-workflow"];

    CheckInputType -- Clear Task Request? --> ReadmeWorkflowRule;
    ReadmeWorkflowRule -- Get Context & Locate Task --> DevWorkflowRule["@dev-workflow-standards (Execute 7 Steps)"];
    DevWorkflowRule --> OutputResult((Output / Result));

    CheckInputType -- Request Commit Msg? --> CommitUpdateDocsRule["@commit-and-update-docs"];
    CommitUpdateDocsRule --> AnalyzeStatus["步骤① 分析现状与已完成工作"];
    AnalyzeStatus --> ProposeNextStep["步骤② 提议下一步计划"];
    ProposeNextStep --> ConfirmNextStep["步骤③ 获取用户对下一步计划的确认"];
    ConfirmNextStep --> ProposeAllUpdates["步骤④ 提议所有文档更新(检查点、计划状态等)"];
    ProposeAllUpdates --> ConfirmAllUpdates["步骤⑤ 获取用户对所有文档更新的确认"];
    ConfirmAllUpdates --> ExecuteUpdates["步骤⑥ 执行文档更新"];
    ExecuteUpdates --> GenerateCommitMsg["步骤⑦ 基于更新后的检查点生成Commit Message"];
    GenerateCommitMsg --> OutputResult;

    CheckInputType -- Request Plan Change? --> HandlePlanChangeRule["@handle-plan-change"];
    HandlePlanChangeRule --> ConfirmChange["步骤① Confirm Change Request"];
    ConfirmChange --> ProposeCheckpointUpdate2["步骤② Propose Checkpoint Update \(Next Step\)"];
    ProposeCheckpointUpdate2 --> AskPlanImpact["步骤③ Ask re: Core Plan Impact?"];
    AskPlanImpact -- Yes --> AnalyzePlanImpact["步骤④-A Analyze Core Plan Impact"];
    AnalyzePlanImpact --> ProposePlanChanges["步骤④-B Propose Core Plan Changes"];
    AskPlanImpact -- No --> GetUserConfirmation2["步骤⑤ Present Changes & Get User Confirmation"];
    ProposePlanChanges --> GetUserConfirmation2;
    GetUserConfirmation2 -- Confirmed --> OutputResult;

    %% Styling %%
    style DocOverviewRule fill:#f9f,stroke:#333,stroke-width:2px
    style CommitUpdateDocsRule fill:#f9f,stroke:#333,stroke-width:2px
    style HandlePlanChangeRule fill:#f9f,stroke:#333,stroke-width:2px
    style ReadmeWorkflowRule fill:#ccf,stroke:#333,stroke-width:2px
    style DevWorkflowRule fill:#ccf,stroke:#333,stroke-width:2px
```

## 图例说明

- **圆角矩形 `(( ))`**: 流程的开始或结束。
- **菱形 `{ }`**: 决策点，根据输入类型或条件分流。
- **矩形 `[ ]`**: 处理步骤或具体操作。
- **带反斜杠的矩形 `[/ /]`**: 表示一个检查点或约束条件。
- **紫色矩形 `[\`@rule-name\`]`**: 代表交互式或特定场景触发的规则。
- **蓝色矩形 `[\`@rule-name\`]`**: 代表核心信息获取或开发执行流程的规则。
- **红色矩形 `[/ /]`**: 代表约束性检查规则。

## 关键流程解释

1. **输入判断**: 根据用户输入是模糊更新、清晰任务、请求Commit还是请求计划变更，进入不同分支。

2. **模糊处理 (`@document-overview`)**: 尝试澄清意图，提议并执行适当的更新。

3. **任务执行流程**: 清晰的任务请求会先通过 `@readme-driven-workflow` 获取上下文，然后由 `@dev-workflow-standards` 按 7 步法执行。

4. **Commit流程 (`@commit-and-update-docs`)**: 按照文档驱动的原则，首先分析现状并识别已完成的工作，然后提议并确认下一步计划，接着提议并确认所有文档更新，执行更新后，才基于更新后的检查点生成 Commit Message。这确保了一旦提交完成，所有状态文档已准备就绪，可以直接驱动下一步工作。

5. **计划变更流程 (`@handle-plan-change`)**: 确认变更 -> 提议 Checkpoint 更新 -> 分析对核心计划影响 -> 提议核心计划更新 -> 汇总确认。

## 规则与文档关系简介

本项目使用多个规则文件来指导 AI 行为，每个规则各司其职：

1. **[@readme-driven-workflow](./.cursor/rules/readme-driven-workflow.mdc)** - 信息获取规则，确保 AI 在执行任务前正确理解项目上下文和相关文档。

2. **[@dev-workflow-standards](./.cursor/rules/dev-workflow-standards.mdc)** - 核心开发流程规则，定义了 7 步标准开发流程，适用于所有开发任务。推荐设置为 always 模式。

3. **[@document-overview](./.cursor/rules/document-overview.mdc)** - 处理模糊更新请求，帮助澄清用户意图或提出更新建议。

4. **[@commit-and-update-docs](./.cursor/rules/commit-and-update-docs.mdc)** - 处理提交信息生成和文档更新，确保在生成 Commit Message 前，先更新并确认所有项目状态文档。

5. **[@handle-plan-change](./.cursor/rules/handle-plan-change.mdc)** - 处理计划变更请求，评估对整体计划的影响并提出相应更新。

## AgentReadme 核心内容导航

要全面理解本项目的工作流程、规划和资源，请参考 AgentReadme 目录下的核心文档和目录：

**核心流程与规划文档:**

- **AI 规则交互**: 本文档 ([AgentReadme/framework_docs/ai_rules_workflow.md](./ai_rules_workflow.md)) 可视化了各 AI 规则如何协同工作。
- **项目入口与协作流程**: 查阅 [AgentReadme/AgentReadme.md](../AgentReadme.md) 获取 AgentReadme 整体结构和协作方式。
- **高层目标与愿景**: 参考 [AgentReadme/planning_and_requirements/project_vision_and_roadmap.md](../planning_and_requirements/project_vision_and_roadmap.md)。
- **详细需求**: 参考 [AgentReadme/planning_and_requirements/project_requirements.md](../planning_and_requirements/project_requirements.md)。
- **详细计划与历史记录**: 参考 [AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md](../planning_and_requirements/detailed_work_plan_and_log.md)。
- **迭代开发检查点**: 参考 [AgentReadme/planning_and_requirements/ai_dev_checkpoint.md](../planning_and_requirements/ai_dev_checkpoint.md)。

**辅助资源与历史记录:**

- **函数摘要**: [AgentReadme/function_map/function_summary.md](../function_map/function_summary.md) 提供了代码库中主要函数的概览 (由脚本生成)。
- **开发指南**: [AgentReadme/guides/](../guides/) 目录包含各种开发实践指南 (例如 [testing_guide.md](../guides/testing_guide.md)，**未来可能包含代码风格、部署流程等指南**)。
- **进行中/已完成的特性计划**: 分别查阅 [AgentReadme/active_feature_plans/](../active_feature_plans/) 和 [AgentReadme/completed_feature_plans/](../completed_feature_plans/) 目录以获取特定功能的详细规划文档。
- **AI 操作日志**: [AgentReadme/ai_operation_logs/](../ai_operation_logs/) 记录了 AI 执行重要操作的详细日志。
- **测试日志**: [AgentReadme/testing_logs/](../testing_logs/) 用于存放相关的测试记录。
