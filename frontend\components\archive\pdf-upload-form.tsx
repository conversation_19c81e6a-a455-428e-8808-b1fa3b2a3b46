"use client"

import type React from "react"
import {
  Upload,
  FileText,
  AlertCircle,
  X,
  Trash2,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import type { UploadableFileWithProgress } from "@/hooks/domain/archive/usePdfUploader"
import { CircularProgress } from "@/components/ui/circular-progress"

interface PdfUploadFormProps {
    files: UploadableFileWithProgress[];
    useParallel: boolean;
    setUseParallel: (value: boolean) => void;
    chunkSize: string;
    setChunkSize: (value: string) => void;
    batchSize: string;
    setBatchSize: (value: string) => void;
    fileInputRef: React.RefObject<HTMLInputElement | null>;
    handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSelectFileClick: () => void;
    handleBoxNumberChange: (id: string, value: string) => void;
    handleRemoveFile: (id: string) => void;
    handleClearAll: () => void;
    handleSubmit: (e: React.FormEvent) => void;
    isUploading: boolean;
    isReadyToUpload: boolean;
    incompleteFiles: UploadableFileWithProgress[];
}

export function PdfUploadForm({
    files,
    useParallel,
    setUseParallel,
    chunkSize,
    setChunkSize,
    batchSize,
    setBatchSize,
    fileInputRef,
    handleFileChange,
    handleSelectFileClick,
    handleBoxNumberChange,
    handleRemoveFile,
    handleClearAll,
    handleSubmit,
    isUploading,
    isReadyToUpload,
    incompleteFiles
}: PdfUploadFormProps) {

  return (
    <Card className="border-transparent shadow-none">
      <form onSubmit={handleSubmit}>
        <CardContent className="pt-6">
          <Alert className="py-3">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>注意</AlertTitle>
            <AlertDescription>
              系统将自动处理PDF文件，识别其中的统一编号，拆分出档案PDF文件、报告PDF文件，并关联到台账记录。请确保PDF文件清晰可读，且台账中已存在对应的记录。
            </AlertDescription>
          </Alert>
          
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <h3 className="text-lg font-medium tracking-tight">
                选择文件 (可多选) 并输入档案盒号
              </h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Input
                    id="pdf-file"
                    type="file"
                    accept=".pdf"
                    multiple
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSelectFileClick}
                    disabled={isUploading}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    选择文件
                  </Button>
                </div>

                {files.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <div className="flex justify-between items-center">
                      <h4 className="text-sm font-medium">
                        文件上传列表 ({files.length})
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClearAll}
                        type="button"
                        disabled={isUploading}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        清空列表
                      </Button>
                    </div>
                    <div className="rounded-md border max-h-60 overflow-y-auto">
                      <ul className="divide-y divide-border">
                        {files.map((item) => (
                          <li
                            key={item.id}
                            className="flex items-start justify-between p-3 gap-3"
                          >
                            <FileText className="h-6 w-6 text-muted-foreground flex-shrink-0 mt-1" />
                            <div className="flex-1 overflow-hidden px-1">
                              <p className="text-sm font-medium truncate">
                                {item.file.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {(item.file.size / 1024 / 1024).toFixed(2)}{" "}
                                MB
                              </p>
                              <Input
                                type="text"
                                placeholder="输入物理档案盒号"
                                value={item.boxNumber}
                                onChange={(e) =>
                                  handleBoxNumberChange(item.id, e.target.value)
                                }
                                disabled={
                                  item.status !== "pending" || isUploading
                                }
                                className={cn(
                                  "mt-2 h-8 text-sm my-1",
                                  incompleteFiles.some(
                                    (f) => f.id === item.id
                                  ) && "border-red-500"
                                )}
                                required
                              />
                            </div>
                            <div className="flex flex-col items-end gap-2 flex-shrink-0 ml-2">
                              <div className="flex items-center justify-center flex-shrink-0 h-8 w-[70px]">
                                {item.status === "pending" && (
                                  <span className="text-xs text-muted-foreground">
                                    待上传
                                  </span>
                                )}
                                {item.status === "uploading" && (
                                  <CircularProgress progress={item.progress || 0} size={28} strokeWidth={3} />
                                )}
                                {item.status === 'processing' && (
                                    <Loader2 className="h-5 w-5 animate-spin text-green-200" />
                                )}
                                {item.status === "success" && (
                                  <CheckCircle className="h-5 w-5 text-green-500" />
                                )}
                                {item.status === "error" && (
                                  <div
                                    className="flex items-center gap-1 text-red-500 cursor-pointer"
                                    title={item.error}
                                  >
                                    <XCircle className="h-5 w-5" />
                                  </div>
                                )}
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 self-center"
                                onClick={() => handleRemoveFile(item.id)}
                                type="button"
                                disabled={isUploading}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-4 rounded-lg border bg-muted/20 p-4">
              <div className="space-y-1.5">
                <h4 className="font-semibold">高级处理选项</h4>
                <p className="text-xs text-muted-foreground">
                  调整以下参数可优化大文件的处理性能和速度。通常无需修改。
                </p>
              </div>

              <div className="space-y-2 pt-2">
                <Label className="text-sm font-medium">处理模式</Label>
                <div className="flex items-center space-x-4">
                  <span
                    className={`text-sm font-medium ${
                      !useParallel
                        ? "text-foreground"
                        : "text-muted-foreground"
                    }`}
                  >
                    串行处理
                  </span>
                  <Switch
                    variant="functional"
                    checked={useParallel}
                    onCheckedChange={setUseParallel}
                    aria-label="处理模式切换"
                    disabled={isUploading}
                  />
                  <span
                    className={`text-sm font-medium ${
                      useParallel ? "text-foreground" : "text-muted-foreground"
                    }`}
                  >
                    并行处理
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
                <div className="space-y-2">
                  <Label htmlFor="batch-size" className="text-sm">
                    单次加载页数
                  </Label>
                  <Input
                    id="batch-size"
                    type="number"
                    value={batchSize}
                    onChange={(e) => setBatchSize(e.target.value)}
                    placeholder="例如: 5"
                    disabled={isUploading}
                  />
                  <p className="text-xs text-muted-foreground">
                    每个处理任务加载进内存的页数。
                  </p>
                </div>

                {useParallel && (
                  <div className="space-y-2">
                    <Label htmlFor="chunk-size" className="text-sm">
                      页面分块大小
                    </Label>
                    <Input
                      id="chunk-size"
                      type="number"
                      value={chunkSize}
                      onChange={(e) => setChunkSize(e.target.value)}
                      placeholder="例如: 20"
                      disabled={isUploading || !useParallel}
                    />
                    <p className="text-xs text-muted-foreground">
                      每个并行任务处理的页面数。
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            type="submit"
            disabled={isUploading || files.length === 0 || !isReadyToUpload}
            className="w-full h-12 text-lg"
          >
            <Upload className="mr-2 h-5 w-5" />
            {isUploading
              ? `上传中... (${
                  files.filter((f) => f.status === "uploading").length
                }个文件)`
              : `上传并处理 (${
                  files.filter((f) => f.status === "pending").length
                }个文件)`}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
