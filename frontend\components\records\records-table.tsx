"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, FileText, History, Pencil, FileEdit, Edit2, Check, X, ChevronLeft, ChevronRight } from "lucide-react"
import React, { useState, useMemo } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { Input } from "@/components/ui/input"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useArchiveRecords } from "@/hooks/domain/records/use-archive-records"
import type { ArchiveRecord, ArchiveStatus, ReportIssueStatus } from "@/types/archive-record"

// ==================== 组件Props ====================

interface RecordsTableProps {
  enableSelection?: boolean;
  enableInlineEdit?: boolean;
  onSelectionChange?: (selectedIds: number[]) => void;
  onRecordUpdate?: (record: ArchiveRecord) => void;
  initialFilters?: {
    archiveStatus?: ArchiveStatus;
    batchNumber?: string;
    search?: string;
  };
}

// ==================== 主组件 ====================

export function RecordsTable({
  enableSelection = true,
  enableInlineEdit = false,
  onSelectionChange,
  onRecordUpdate,
  initialFilters = {},
}: RecordsTableProps) {
  const router = useRouter()
  const { toast } = useToast()

  // 使用档案记录Hook
  const {
    records,
    totalCount,
    isLoading,
    isError,
    currentPage,
    pageSize,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    selectedIds,
    queryParams,
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected,
    goToPage,
    setPageSize,
    search,
    filter,
    sort,
    updateRecord,
    batchUpdateStatus,
  } = useArchiveRecords({
    initialParams: {
      pageSize: 20,
      ...initialFilters,
    },
  });

  // 本地状态
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingRecord, setEditingRecord] = useState<number | null>(null)
  const [editedData, setEditedData] = useState<Partial<ArchiveRecord>>({})
  const [showCorrectionDialog, setShowCorrectionDialog] = useState(false)
  const [correctionReason, setCorrectionReason] = useState("")

  // 监听选中状态变化
  React.useEffect(() => {
    onSelectionChange?.(selectedIds);
  }, [selectedIds, onSelectionChange]);

  // ==================== 事件处理 ====================

  // 开启更正操作模式
  const startCorrection = () => {
    setShowCorrectionDialog(true)
  }

  // 确认开启更正操作
  const confirmCorrection = () => {
    if (!correctionReason.trim()) {
      toast({
        title: "无法开启更正",
        description: "请填写更正原因",
        variant: "destructive",
      })
      return
    }

    setIsEditMode(true)
    setShowCorrectionDialog(false)

    toast({
      title: "更正模式已开启",
      description: "您现在可以直接编辑台账数据",
    })
  }

  // 开始编辑某条记录
  const startEditing = (recordId: number) => {
    const record = records.find((r) => r.id === recordId)
    if (record) {
      setEditingRecord(recordId)
      setEditedData({
        projectName: record.projectName,
        unifiedNumber: record.unifiedNumber,
        archiveBoxNumber: record.archiveBoxNumber || "",
      })
    }
  }

  // 取消编辑
  const cancelEditing = () => {
    setEditingRecord(null)
    setEditedData({})
  }

  // 保存编辑
  const saveEditing = async () => {
    if (!editingRecord) return

    try {
      await updateRecord(editingRecord, {
        ...editedData,
        // 可以添加更正原因到备注中
        archiveNote: `${editedData.archiveNote || ''}\n[更正] ${correctionReason}`.trim(),
      });

      onRecordUpdate?.(records.find(r => r.id === editingRecord)!);
      setEditingRecord(null)
      setEditedData({})
    } catch (error) {
      // Hook中已经处理了错误提示
    }
  }

  // 处理输入变化
  const handleInputChange = (field: keyof ArchiveRecord, value: string) => {
    setEditedData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // 退出更正模式
  const exitCorrectionMode = () => {
    setIsEditMode(false)
    setEditingRecord(null)
    setEditedData({})
    setCorrectionReason("")
    
    toast({
      title: "已退出更正模式",
      description: "表格恢复为只读状态",
    })
  }

  // 处理全选
  const handleSelectAll = () => {
    if (selectedIds.length === records.length) {
      clearSelection();
    } else {
      selectAll();
    }
  }

  // 批量变更单操作
  const handleBatchChangeOrder = () => {
    if (selectedIds.length === 0) {
      toast({
        title: "请选择记录",
        description: "请先选择要创建变更单的记录",
        variant: "destructive",
      })
      return
    }

    router.push(`/change-orders/new?records=${selectedIds.join(',')}`)
  }

  // ==================== 状态显示函数 ====================

  const getStatusBadge = (status: ArchiveStatus | undefined) => {
    if (!status) return <Badge variant="secondary">未知</Badge>;
    switch (status) {
      case "archived":
        return <Badge variant="default">已归档</Badge>
      case "pending":
        return <Badge variant="secondary">待归档</Badge>
      case "stored":
        return <Badge variant="outline">已入库</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  }

  const getIssueStatusBadge = (status: ReportIssueStatus | undefined) => {
    if (!status) return <Badge variant="secondary">未知</Badge>;
    switch (status) {
      case 'not_issued':
        return <Badge variant="secondary">未发放</Badge>
      case 'second_issued':
        return <Badge className="bg-blue-100 text-blue-800">二次发放</Badge>
      case 'completed':
        return <Badge className="bg-purple-100 text-purple-800">发放完成</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // ==================== 渲染函数 ====================

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-sm text-muted-foreground">加载中...</div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-sm text-destructive">加载失败，请重试</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 表格操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {enableSelection && selectedIds.length > 0 && (
            <>
              <span className="text-sm text-muted-foreground">
                已选择 {selectedIds.length} 项
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBatchChangeOrder}
              >
                创建变更单
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => batchUpdateStatus(selectedIds, 'archived')}
              >
                批量归档
              </Button>
            </>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {enableInlineEdit && (
            <>
              {!isEditMode ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={startCorrection}
                >
                  <Edit2 className="h-4 w-4 mr-2" />
                  开启更正
                </Button>
              ) : (
                <div className="flex items-center space-x-2">
                  <Badge variant="destructive">更正模式</Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exitCorrectionMode}
                  >
                    <X className="h-4 w-4 mr-2" />
                    退出更正
                  </Button>
                </div>
              )}
            </>
          )}
          
          <div className="text-sm text-muted-foreground">
            共 {totalCount} 条记录
          </div>
        </div>
      </div>

      {/* 表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {enableSelection && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedIds.length === records.length && records.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
              )}
              <TableHead 
                className="cursor-pointer"
                onClick={() => sort('sampleNumber')}
              >
                样品编号
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => sort('unifiedNumber')}
              >
                统一编号
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => sort('projectName')}
              >
                项目名称
              </TableHead>
              <TableHead>归档状态</TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => sort('archiveDatetime')}
              >
                归档日期
              </TableHead>
              <TableHead>档案盒号</TableHead>
              <TableHead>发放状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {records.map((record) => (
              <TableRow key={record.id}>
                {enableSelection && (
                  <TableCell>
                    <Checkbox
                      checked={isSelected(record.id)}
                      onCheckedChange={(checked) => 
                        toggleSelection(record.id)
                      }
                      aria-label={`选择记录 ${record.sampleNumber}`}
                    />
                  </TableCell>
                )}
                <TableCell className="font-medium">
                  {record.sampleNumber}
                </TableCell>
                <TableCell>
                  {editingRecord === record.id ? (
                    <Input
                      value={editedData.unifiedNumber || ""}
                      onChange={(e) => handleInputChange("unifiedNumber", e.target.value)}
                      className="h-8"
                    />
                  ) : (
                    record.unifiedNumber
                  )}
                </TableCell>
                <TableCell>
                  {editingRecord === record.id ? (
                    <Input
                      value={editedData.projectName || ""}
                      onChange={(e) => handleInputChange("projectName", e.target.value)}
                      className="h-8"
                    />
                  ) : (
                    record.projectName
                  )}
                </TableCell>
                <TableCell>
                  {getStatusBadge(record.archiveStatus as ArchiveStatus | undefined)}
                </TableCell>
                <TableCell>
                  {record.archiveDatetime ? 
                    new Date(record.archiveDatetime).toLocaleDateString() : 
                    '-'
                  }
                </TableCell>
                <TableCell>
                  {editingRecord === record.id ? (
                    <Input
                      value={editedData.archiveBoxNumber || ""}
                      onChange={(e) => handleInputChange("archiveBoxNumber", e.target.value)}
                      className="h-8"
                    />
                  ) : (
                    record.archiveBoxNumber || '-'
                  )}
                </TableCell>
                <TableCell>
                  {getIssueStatusBadge(record.reportIssueStatus as ReportIssueStatus | undefined)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {editingRecord === record.id ? (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={saveEditing}
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={cancelEditing}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </>
                    ) : (
                      <>
                        {isEditMode && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => startEditing(record.id)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/records/detail/${record.id}`}>
                                <FileText className="mr-2 h-4 w-4" />
                                查看详情
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/records/history/${record.id}`}>
                                <History className="mr-2 h-4 w-4" />
                                查看历史
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/change-orders/new?records=${record.id}`}>
                                <FileEdit className="mr-2 h-4 w-4" />
                                创建变更单
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalCount)} 条，
          共 {totalCount} 条记录
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(currentPage - 1)}
            disabled={!hasPreviousPage}
          >
            <ChevronLeft className="h-4 w-4" />
            上一页
          </Button>
          
          <div className="flex items-center space-x-1">
            <span className="text-sm">第</span>
            <Input
              type="number"
              value={currentPage}
              onChange={(e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= totalPages) {
                  goToPage(page);
                }
              }}
              className="w-16 h-8 text-center"
            />
            <span className="text-sm">页，共 {totalPages} 页</span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(currentPage + 1)}
            disabled={!hasNextPage}
          >
            下一页
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 更正确认对话框 */}
      <AlertDialog open={showCorrectionDialog} onOpenChange={setShowCorrectionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>开启数据更正模式</AlertDialogTitle>
            <AlertDialogDescription>
              开启更正模式后，您可以直接在表格中编辑数据。所有变更都会记录在案。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="correction-reason">更正原因</Label>
              <Textarea
                id="correction-reason"
                placeholder="请说明进行数据更正的原因..."
                value={correctionReason}
                onChange={(e) => setCorrectionReason(e.target.value)}
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCorrectionDialog(false)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmCorrection}>
              确认开启
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
