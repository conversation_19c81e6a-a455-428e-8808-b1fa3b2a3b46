"use client"

/**
 * 警告：此组件已被多处引用，包括：
 * - frontend/app/statistics/page.tsx
 * - frontend/components/reports/reports-filter.tsx
 * - frontend/components/records/records-filter.tsx
 * - frontend/components/common/advanced-search.tsx
 * - frontend/components/change-orders/change-orders-filter.tsx
 * - frontend/components/archive/pdf-import-ledger.tsx
 * 
 * 但此组件的实现与 records/ledger/page.tsx 中的日期选择器实现不同。
 * 建议在未来重构为更统一的日期选择器实现，以保持代码一致性。
 * 具体建议：
 * 1. 基于 records/ledger/page.tsx 中的实现创建通用组件
 * 2. 使用 ClientOnly 包装所有日期显示，避免水合错误
 * 3. 确保API统一，便于各处引用
 */

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { addDays, format } from "date-fns"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import ClientOnly from "@/components/common/ClientOnly"

interface DateRangePickerProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  onChange?: (date: { from: Date; to: Date | undefined }) => void
}

export function DateRangePicker({ className, onChange }: DateRangePickerProps) {
  // 直接在组件内初始化日期状态
  const [date, setDate] = React.useState<{
    from: Date | null
    to: Date | undefined
  }>({
    // 不需要设置初始值，使用 ClientOnly 组件处理渲染
    from: null,
    to: undefined,
  })

  // 移除用于避免水合错误的 useEffect
  // React.useEffect(() => {
  //   // 只在客户端运行
  //   const today = new Date()
  //   setDate({
  //     from: today,
  //     to: addDays(today, 7),
  //   })
  // }, [])

  const [isOpen, setIsOpen] = React.useState(false)

  // 初始化日期的函数，在组件挂载后由 ClientOnly 组件调用
  const initializeDates = React.useCallback(() => {
    const today = new Date()
    const newDate = {
      from: today,
      to: addDays(today, 7),
    }
    setDate(newDate)
    onChange?.(newDate)
  }, [onChange])

  // 预设日期范围 - 使用useCallback确保函数引用稳定
  const handleSelectPreset = React.useCallback((preset: string) => {
    // 确保在客户端运行
    if (typeof window === 'undefined') return

    const today = new Date()
    let newDate: { from: Date; to: Date | undefined }
    
    switch (preset) {
      case "today":
        newDate = { from: today, to: today }
        break
      case "yesterday": {
        const yesterday = addDays(today, -1)
        newDate = { from: yesterday, to: yesterday }
        break
      }
      case "last7days":
        newDate = { from: addDays(today, -6), to: today }
        break
      case "last30days":
        newDate = { from: addDays(today, -29), to: today }
        break
      case "thisMonth": {
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        newDate = { from: firstDayOfMonth, to: lastDayOfMonth }
        break
      }
      case "lastMonth": {
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
        newDate = { from: firstDayOfLastMonth, to: lastDayOfLastMonth }
        break
      }
      default:
        return
    }
    
    setDate(newDate)
    onChange?.(newDate)
  }, [onChange])

  // 使用 ClientOnly 的 useEffect 来初始化日期
  React.useEffect(() => {
    initializeDates()
  }, [initializeDates])

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <ClientOnly>
              {date.from ? (
                date.to ? (
                  <>
                    {format(date.from, "yyyy-MM-dd")} - {format(date.to, "yyyy-MM-dd")}
                  </>
                ) : (
                  format(date.from, "yyyy-MM-dd")
                )
              ) : (
                <span>选择日期范围</span>
              )}
            </ClientOnly>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col sm:flex-row gap-2 p-3 border-b">
            <Select onValueChange={handleSelectPreset} defaultValue="custom">
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="选择日期范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="custom">自定义</SelectItem>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="yesterday">昨天</SelectItem>
                <SelectItem value="last7days">最近7天</SelectItem>
                <SelectItem value="last30days">最近30天</SelectItem>
                <SelectItem value="thisMonth">本月</SelectItem>
                <SelectItem value="lastMonth">上月</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button variant="outline" className="w-full" onClick={() => setIsOpen(false)}>
                取消
              </Button>
              <Button className="w-full" onClick={() => setIsOpen(false)}>
                应用
              </Button>
            </div>
          </div>
          <ClientOnly>
            <Calendar
              // 使用autoFocus代替已弃用的initialFocus
              autoFocus
              mode="range"
              // 只在from存在时设置defaultMonth
              defaultMonth={date.from || undefined}
              // 确保selected的类型正确
              selected={{
                from: date.from || undefined,
                to: date.to
              }}
              onSelect={(newDate) => {
                if (newDate) {
                  const dateRange = newDate as { from: Date; to: Date | undefined }
                  setDate(dateRange)
                  onChange?.(dateRange)
                }
              }}
              numberOfMonths={2}
            />
          </ClientOnly>
        </PopoverContent>
      </Popover>
    </div>
  )
}
