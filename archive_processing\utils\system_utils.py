"""
系统工具模块

此模块包含与系统和通用格式化相关的函数：
1. CPU类型检测
2. 时间格式化

从pdf_processor_usefull.py中提取和重构，以提高代码组织性和可维护性。
"""
import logging
import platform
import subprocess
import re

logger = logging.getLogger(__name__)

def detect_cpu_type():
    """
    检测CPU类型，返回CPU平台类型
    
    Returns:
        str: CPU类型 - "intel", "amd" 或 "unknown"
    """
    try:
        cpu_info = ""
        if platform.system() == "Windows":
            try:
                cpu_info = subprocess.check_output("wmic cpu get name", shell=True).decode()
            except Exception as e:
                logger.warning(f"Windows环境下获取CPU信息失败: {e}")
        elif platform.system() == "Linux":
            try:
                with open("/proc/cpuinfo", "r") as f:
                    cpu_info = f.read()
            except Exception as e:
                logger.warning(f"Linux环境下获取CPU信息失败: {e}")
        elif platform.system() == "Darwin":  # macOS
            try:
                cpu_info = subprocess.check_output("sysctl -n machdep.cpu.brand_string", shell=True).decode()
            except Exception as e:
                logger.warning(f"macOS环境下获取CPU信息失败: {e}")
        
        is_intel = "intel" in cpu_info.lower()
        is_amd = "amd" in cpu_info.lower()
        
        if is_intel:
            return "intel"
        elif is_amd:
            return "amd"
        else:
            return "unknown"
    except Exception as e:
        logger.warning(f"CPU类型检测失败: {e}")
        return "unknown"  # 无法检测时默认返回未知

def format_time(seconds):
    """
    格式化时间为易读形式
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化后的时间字符串
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}毫秒"
    elif seconds < 60:
        return f"{seconds:.2f}秒"
    else:
        minutes = int(seconds // 60)
        seconds = seconds % 60
        return f"{minutes}分{seconds:.2f}秒" 