"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, ClipboardCheck } from "lucide-react"
import Link from "next/link"

export function QuickAccessMenu() {
  const quickAccessItems = [
    {
      title: "报告发放台账",
      description: "查看和管理所有报告发放单",
      icon: ClipboardCheck,
      href: "/reports/management",
    },
    {
      title: "新建报告发放单",
      description: "创建新的报告发放单",
      icon: FileText,
      href: "/reports/detail/new",
    },
  ]

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>快速访问</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {quickAccessItems.map((item) => (
          <Button key={item.title} variant="outline" className="w-full justify-start h-auto py-3" asChild>
            <Link href={item.href} className="flex items-start gap-3">
              <item.icon className="h-5 w-5 text-muted-foreground" />
              <div className="text-left">
                <div className="font-medium">{item.title}</div>
                <div className="text-xs text-muted-foreground mt-1">{item.description}</div>
              </div>
            </Link>
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}
