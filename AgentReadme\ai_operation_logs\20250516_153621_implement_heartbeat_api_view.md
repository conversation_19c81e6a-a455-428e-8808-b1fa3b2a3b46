# Operation Document: Implement/Verify Session Heartbeat API

## 📋 Change Summary

**Purpose**: 实现并验证会话心跳API (`SessionHeartbeatView`)，使其能够调用 `ImportSessionManager.record_heartbeat` 服务方法，并根据服务层返回的结果给出正确的HTTP响应。
**Scope**: `archive_records/views.py` 中的 `SessionHeartbeatView`，并确认 `archive_records/services/import_session_manager.py` 中的 `record_heartbeat` 方法及 `archive_records/urls.py` 中的路由配置。
**Associated**: 对应《Excel导入功能的会话管理需求与解决方案.md》文档中的新增任务1.3.A。

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing Code for Heartbeat API

**Precondition**: 需求文档已定义心跳API的要求。
**Operation**:

1. 检查 `archive_records/views.py` 是否已存在 `SessionHeartbeatView`。
2. 检查 `ImportSessionManager` (位于 `archive_records/services/import_session_manager.py`) 是否已存在 `record_heartbeat` 服务方法。
3. 检查 `archive_records/urls.py` 是否已配置心跳API的路由。
**Postcondition**: 确认现有代码情况：`SessionHeartbeatView` 骨架存在，`record_heartbeat` 方法已完整实现，URL路由已配置。主要任务是完善视图的 `post` 方法。

### ✏️ OP-002: Enhance `SessionHeartbeatView.post` Method

**Precondition**: 分析完成，确认 `record_heartbeat` 服务方法可用。
**Operation**:

1. 在 `SessionHeartbeatView.post` 方法中，实例化 `ImportSessionManager`。
2. 调用 `manager.record_heartbeat(session_id=session_id, user=request.user)`。
3. 根据服务方法返回的 `(success, message)` 元组：
    * 若 `success` 为 `True`，返回 `HTTP_200_OK` 及成功消息。
    * 若 `success` 为 `False`，根据 `message` 内容判断并返回更具体的HTTP状态码（如404, 403, 400, 500）。
4. 添加适当的日志记录。
**Postcondition**: `SessionHeartbeatView.post` 方法已能正确处理心跳请求并返回响应。

### 🧪 OP-003: Verify Changes (Conceptual)

**Precondition**: 代码修改已应用。
**Operation**: 审阅生成的代码 diff，确保其符合预期修改。后续需要通过API测试和单元测试进行验证。
**Postcondition**: 初步确认心跳API的视图逻辑已正确实现。

## 📝 Change Details

### CH-001: Enhance `SessionHeartbeatView.post`

**File**: `archive_records/views.py`
**Before**:

```python
# class SessionHeartbeatView(APIView):
#     """处理导入会话心跳的API视图。"""
#     permission_classes = [IsAuthenticated]
# 
#     def post(self, request, session_id: uuid.UUID, *args, **kwargs):
#         # 旧的或不完整的实现
```

**After**: (CHANGE: [2025-05-16] 完善心跳API视图逻辑)

```python
class SessionHeartbeatView(APIView):
    """处理导入会话心跳的API视图。"""
    permission_classes = [IsAuthenticated]

    def post(self, request, session_id: uuid.UUID, *args, **kwargs):
        """
        记录指定导入会话的心跳。
        """
        manager = ImportSessionManager()
        success, message = manager.record_heartbeat(session_id=session_id, user=request.user)

        if success:
            return Response({"success": True, "message": message}, status=status.HTTP_200_OK)
        else:
            # 根据消息内容判断更具体的错误状态码
            if "会话不存在" in message or "已不存在" in message:
                status_code = status.HTTP_404_NOT_FOUND
            elif "不是当前会话的处理者" in message or "处理者已改变" in message:
                status_code = status.HTTP_403_FORBIDDEN
            elif "非活跃" in message: 
                 status_code = status.HTTP_400_BAD_REQUEST 
            else:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            
            logger.warning(f"心跳API调用失败 for session {session_id}, user {request.user.username}: {message}")
            return Response({"success": False, "error": message}, status=status_code)
```

**Rationale**: 完善视图以正确调用已有的健壮的服务层方法，并提供规范的API响应。
**Potential Impact**: 心跳API现在功能完整，可以被前端调用。

## ✅ Verification Results

**Method**: 代码审查。后续需要API测试。
**Results**: `SessionHeartbeatView` 已完善。`ImportSessionManager.record_heartbeat` 和URL路由已预先存在且符合要求。
**Problems**: 暂无。
**Solutions**: 暂无。
