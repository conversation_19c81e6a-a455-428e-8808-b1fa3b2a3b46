import NextAuth, { DefaultSession, DefaultUser } from "next-auth";
import { JWT, DefaultJWT } from "next-auth/jwt";

// Define the structure of the user object coming from your backend
export interface BackendUser {
  pk: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

// Extend the built-in JWT type
declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    userId?: string;
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpires?: number;
    backendUser?: BackendUser;
    error?: string; // For propagating errors like RefreshAccessTokenError
  }
}

// Extend the built-in Session type
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id?: string; // Add id to user object in session
    } & DefaultSession["user"]; // Keep existing properties like name, email, image
    accessToken?: string;
    error?: string; // For propagating errors to the client
    // You can also add other properties from the JWT to the session if needed
    // backendUser?: BackendUser;
  }

  // Extend the built-in User type (this is what's passed to jwt callback from authorize)
  interface User extends DefaultUser {
    id: string; // Ensure id is always a string here as NextAuth expects
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpires?: number;
    backendUser?: BackendUser;
  }
} 