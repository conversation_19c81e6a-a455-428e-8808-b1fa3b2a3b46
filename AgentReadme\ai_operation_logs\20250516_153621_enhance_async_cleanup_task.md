# Operation Document: Enhance Async Cleanup Task for Cache Clearing

## 📋 Change Summary

**Purpose**: 完善Celery异步任务 `cleanup_cancelled_session_task`，在原有的文件清理逻辑基础上，增加对相关Django缓存条目的清理。
**Scope**: `archive_records/tasks.py` 文件中的 `cleanup_cancelled_session_task` 函数。
**Associated**: 对应《Excel导入功能的会话管理需求与解决方案.md》文档中的任务1.3完善部分。

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing `cleanup_cancelled_session_task`

**Precondition**: `cleanup_cancelled_session_task` 已存在，主要负责文件清理。
**Operation**: 审查现有任务逻辑，确定缓存清理代码的合适插入点。
**Postcondition**: 明确缓存清理应在文件清理之后、更新 `cleaned_up_at` 之前执行。

### ✏️ OP-002: Implement Cache Clearing Logic

**Precondition**: 分析完成。
**Operation**:

1. 在 `tasks.py` 中导入 `from django.core.cache import cache`。
2. 在 `cleanup_cancelled_session_task` 函数内，文件清理逻辑执行完毕后：
    * 记录开始清理缓存的日志。
    * 构造与当前 `session_id` 相关的已知缓存键列表（例如 `excel_import_conflicts:data:<session_id>` 和 `excel_import_conflicts:records:<session_id>`）。
    * 使用 `cache.delete_many()` 尝试批量删除这些明确构造的键。
    * 记录尝试删除的键和操作结果。
    * 添加对潜在 `Exception` 的捕获和日志记录，确保缓存清理失败不影响任务主体流程（如更新 `cleaned_up_at`）。
**Postcondition**: `cleanup_cancelled_session_task` 现在包含缓存清理步骤。

### 🧪 OP-003: Verify Changes (Conceptual)

**Precondition**: 代码修改已应用。
**Operation**: 审阅代码，确保缓存清理逻辑正确，错误处理得当，并且日志记录充分。
**Postcondition**: 初步确认缓存清理逻辑已正确集成。

## 📝 Change Details

### CH-001: Add Cache Clearing to `cleanup_cancelled_session_task`

**File**: `archive_records/tasks.py`
**Before**:

```python
# ... (任务定义和文件清理逻辑) ...
            # 文件清理代码块结束
            
            session.cleaned_up_at = timezone.now()
            # ... (后续更新和日志记录)
```

**After**: (CHANGE: [2025-05-16] 在异步清理任务中添加缓存清理)

```python
# ... (任务定义和文件清理逻辑) ...
            else:
                logger.info(f"Celery任务：会话 {session_id} 没有文件路径记录，无需删除文件。")

            # CHANGE: [2025-05-16] 添加缓存清理逻辑
            logger.info(f"Celery任务：开始为会话 {session_id} 清理相关缓存。")
            cache_keys_to_delete = []
            cache_conflict_data_key = f'excel_import_conflicts:data:{session_id}'
            cache_conflict_records_key = f'excel_import_conflicts:records:{session_id}'
            cache_keys_to_delete.extend([cache_conflict_data_key, cache_conflict_records_key])
            
            deleted_count = 0
            try:
                if hasattr(cache, 'delete_pattern'):
                    logger.info(f"尝试使用通用模式清理缓存 (如果后端支持)。") # 通用模式键暂不直接使用
                    cache.delete_many(cache_keys_to_delete) # 主要删除已知键
                    deleted_count = len(cache_keys_to_delete) # 假设delete_many不返回数量
                    logger.info(f"已尝试删除 {len(cache_keys_to_delete)} 个明确构造的缓存键: {cache_keys_to_delete}")
                else:
                    for key in cache_keys_to_delete:
                        if cache.delete(key):
                            deleted_count += 1
                    logger.info(f"已明确删除 {deleted_count} 个缓存键: {cache_keys_to_delete}")
                logger.info(f"Celery任务：会话 {session_id} 的缓存清理尝试完成。")
            except Exception as e_cache:
                logger.error(f"Celery任务：为会话 {session_id} 清理缓存时发生错误: {e_cache}", exc_info=True)
            # END CHANGE

            session.cleaned_up_at = timezone.now()
            # ... (后续更新和日志记录)
```

**Rationale**: 增强异步清理任务，确保在会话取消后，相关的缓存数据也能被清理，防止残留数据占用内存或导致潜在的不一致。
**Potential Impact**: 改进了资源清理的彻底性。依赖于Django缓存配置的正确性。

## ✅ Verification Results

**Method**: 代码审查。
**Results**: 缓存清理逻辑已添加到异步任务中。
**Problems**: 关于 `cache.delete_pattern` 的使用，已调整为更通用的 `cache.delete_many` 或循环 `cache.delete`，因为前者并非Django标准API。
**Solutions**: 已在代码中采用更兼容的缓存删除方式。
