"""
档案记录API视图模块

此模块包含与档案记录相关的API视图。
"""

import os
import logging
import tempfile
from django.db.models import Q
from django.conf import settings
from rest_framework import viewsets, status, filters, generics, mixins, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction
from django.utils import timezone
import uuid
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from django.utils.timezone import now
from django.shortcuts import get_object_or_404
import json  # 添加json导入，用于解析筛选和排序参数
from django.core.cache import cache
import pandas as pd
from django.http import HttpResponse, JsonResponse
from django.core.paginator import Paginator

# 导入权限管理工具
from archive_flow_manager.permissions import permission_mode_aware, is_debug_mode

from ..models import (
    ArchiveRecord,
    ImportLog,
    ChangeLogBatch,
    RecordChangeLog,
    FieldChangeLog,
    ChangeOrder,
    ChangeOrderItem,
    ChangeOrderAttachment,
    ImportSession,
    ImportSessionStatus,
    SessionOperation,
    ImportConflictDetail,
)
from ..serializers import (
    ArchiveRecordSerializer,
    ArchiveRecordListSerializer,
    ImportLogSerializer,
    ExcelImportSerializer,
    ChangeLogBatchSerializer,
    RecordChangeLogSerializer,
    ChangeOrderSerializer,
    ChangeOrderCreateSerializer,
    ChangeOrderItemCreateSerializer,
    ChangeOrderActionSerializer,
    ChangeOrderAttachmentSerializer,
    ChangeOrderItemSerializer,
)
from ..services import ExcelImportService, ChangeOrderService
from ..permissions import IsChangeOrderOwnerOrAdmin, CanManageChangeOrder
from ..services.import_session_manager import ImportSessionManager, ConflictResolution, SESSION_EXPIRATION_MINUTES, CONCLUSIVE_SESSION_STATUSES
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

logger = logging.getLogger(__name__)


class StandardResultsSetPagination(PageNumberPagination):
    """标准分页器"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class LimitOffsetPaginationWithMaxLimit(PageNumberPagination):
    """支持limit/offset分页的分页器，适配AG Grid"""

    page_size = 25
    page_size_query_param = "limit"
    max_page_size = 1000
    offset_query_param = "offset"

    def paginate_queryset(self, queryset, request, view=None):
        """重写分页方法以支持offset参数"""
        limit = request.query_params.get(self.page_size_query_param)
        offset = request.query_params.get(self.offset_query_param)

        if limit:
            try:
                self.page_size = int(limit)
                # 确保不超过最大页大小
                if self.page_size > self.max_page_size:
                    self.page_size = self.max_page_size
            except (ValueError, TypeError):
                pass  # 使用默认页大小

        if offset:
            try:
                offset_val = int(offset)
                # 计算页码 (page number)
                page_number = (offset_val // self.page_size) + 1
                request.query_params._mutable = True
                request.query_params["page"] = str(page_number)
                request.query_params._mutable = False
            except (ValueError, TypeError, ZeroDivisionError):
                pass  # 忽略无效的offset

        return super().paginate_queryset(queryset, request, view)


class ArchiveRecordViewSet(viewsets.ModelViewSet):
    """档案记录ViewSet"""

    queryset = ArchiveRecord.objects.all().order_by("-created_at")
    serializer_class = ArchiveRecordSerializer
    pagination_class = LimitOffsetPaginationWithMaxLimit  # CHANGE: [2025-06-04] 改为AG Grid兼容的分页器
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "sample_number",
        "report_number",
        "project_name",
        "client_unit",
        "archive_status",
        "batch_number",
    ]
    search_fields = [
        "sample_number",
        "report_number",
        "project_name",
        "client_unit",
        "client_name",
        "project_address",
    ]
    ordering_fields = [
        "created_at",
        "storage_datetime",
        "archive_datetime",
        "first_issue_datetime",
        "second_issue_datetime",
        "project_name",
    ]

    def get_serializer_class(self):
        """根据操作类型选择序列化器"""
        # CHANGE: [2025-06-04] list操作也使用完整序列化器，确保前端AG Grid获取所有字段数据
        # if self.action == "list":
        #     return ArchiveRecordListSerializer
        return ArchiveRecordSerializer

    def get_queryset(self):
        """自定义查询集 - 支持AG Grid筛选"""
        queryset = super().get_queryset()

        # CHANGE: [2025-06-04] 应用AG Grid特定的筛选逻辑
        queryset = apply_ag_grid_filters(queryset, self.request)

        # 保留原有的筛选逻辑
        # 按导入批次过滤
        batch_number = self.request.query_params.get("batch_number")
        if batch_number:
            queryset = queryset.filter(batch_number=batch_number)

        # 按类别过滤
        category_id = self.request.query_params.get("category", None)
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # 按状态过滤
        status = self.request.query_params.get("status", None)
        if status:
            queryset = queryset.filter(archive_status=status)

        return queryset

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user, updated_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    def get_permissions(self):
        """根据不同操作设置权限"""
        if self.action in ["submit"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["approve", "reject"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["execute"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["cancel", "destroy"]:
            permission_classes = [IsAuthenticated, CanManageChangeOrder]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]


class ImportLogViewSet(viewsets.ReadOnlyModelViewSet):
    """导入日志ViewSet - 只读"""

    serializer_class = ImportLogSerializer
    queryset = ImportLog.objects.all().order_by("-created_at")
    pagination_class = StandardResultsSetPagination
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ["status", "batch_number"]
    search_fields = ["file_name", "batch_number"]

    @action(detail=True, methods=["get"])
    def imported_records(self, request, pk=None):
        """获取特定导入批次的记录"""
        import_log = self.get_object()

        # 获取此次导入的记录
        records = ArchiveRecord.objects.filter(
            batch_number=import_log.batch_number
        ).order_by("-created_at")

        # 分页
        page = self.paginate_queryset(records)
        if page is not None:
            serializer = ArchiveRecordListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ArchiveRecordListSerializer(records, many=True)
        return Response(serializer.data)


# class ExcelImportView(APIView):
#     """
#     Excel文件导入API视图

#     要求用户认证，符合生产环境标准
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.import_excel action
#     """

#     parser_classes = (MultiPartParser, FormParser)
#     permission_classes = [IsAuthenticated]  # 始终要求认证

#     def post(self, request, *args, **kwargs):
#         """处理Excel导入请求"""
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] ExcelImportView.post 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.import_excel")
        
#         serializer = ExcelImportSerializer(data=request.data)

#         if not serializer.is_valid():
#             return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#         excel_file = serializer.validated_data["file"]
#         sheet_name = serializer.validated_data.get("sheet_name", 0)
#         duplicate_strategy = "smart_update"  # 使用系统默认值，避免前端控制

#         # 创建临时文件
#         with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_file:
#             temp_file_path = temp_file.name
#             for chunk in excel_file.chunks():
#                 temp_file.write(chunk)

#         try:
#             # 执行导入
#             import_service = ExcelImportService()
#             import_log = import_service.import_from_file(
#                 temp_file_path,
#                 user=request.user,
#                 sheet_name=sheet_name,
#                 duplicate_strategy=duplicate_strategy,
#             )

#             # 返回导入结果，使用标准的 {success: true, data: {...}} 结构
#             response_data = {
#                 "message": f"导入完成: 共{import_log.total_records}条记录, "
#                 f"成功{import_log.success_records}条 "
#                 f"(新建{import_log.created_count}条, 更新{import_log.updated_count}条, "
#                 f"完全相同{import_log.unchanged_count}条, 手动跳过{import_log.user_manual_skipped_records}条), "
#                 f"总跳过{import_log.total_skipped_records}条(完全相同+手动跳过), "
#                 f"失败{import_log.failed_records}条",
#                 "import_log": ImportLogSerializer(import_log).data,
#             }
#             return Response(
#                 {"success": True, "data": response_data}, status=status.HTTP_200_OK
#             )

#         except Exception as e:
#             logger.error(f"导入过程出错: {str(e)}", exc_info=True)
#             return Response(
#                 {"error": f"导入失败: {str(e)}"},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             )
#         finally:
#             # 删除临时文件
#             if os.path.exists(temp_file_path):
#                 os.unlink(temp_file_path)


class ChangeLogViewSet(viewsets.ReadOnlyModelViewSet):
    """变更日志查询视图集"""

    queryset = ChangeLogBatch.objects.all()
    serializer_class = ChangeLogBatchSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """支持按导入批次、记录ID等过滤"""
        queryset = super().get_queryset()

        # 按导入批次过滤
        import_log_id = self.request.query_params.get("import_log")
        if import_log_id:
            queryset = queryset.filter(import_log_id=import_log_id)

        # 按记录ID过滤（需要子查询）
        record_id = self.request.query_params.get("record_id")
        if record_id:
            record_changes = RecordChangeLog.objects.filter(
                record_id=record_id
            ).values_list("batch_id", flat=True)
            queryset = queryset.filter(id__in=record_changes)

        # 按时间范围过滤
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        if start_date:
            queryset = queryset.filter(changed_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(changed_at__lte=end_date)

        return queryset


class RecordHistoryView(APIView):
    """
    档案记录版本历史查询视图
    提供特定记录的所有历史版本信息
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, record_id, format=None):
        """获取特定记录的版本历史"""
        try:
            # 查询记录是否存在
            record = ArchiveRecord.objects.get(pk=record_id)

            # 是否返回简要信息
            brief = request.query_params.get("brief", "false").lower() == "true"

            # 获取所有版本历史
            versions = RecordChangeLog.objects.filter(record=record).order_by(
                "version_number"
            )

            version_info = []
            for v in versions:
                version_info.append(
                    {
                        "id": v.id,
                        "version": v.version_number,
                        "type": v.get_change_type_display(),
                        "time": v.batch.changed_at.strftime(
                            "%Y-%m-%d %H:%M:%S"
                        ),  # 格式化时间
                        "user": (
                            v.batch.changed_by.username
                            if v.batch.changed_by
                            else "未知"
                        ),
                    }
                )

            if brief:
                # 简要模式：只返回版本基本信息
                version_data = versions.values(
                    "id",
                    "version_number",
                    "change_type",
                    "changed_fields_count",
                    "is_rollback",
                    "rollback_source_version",
                    "batch__changed_at",
                    "batch__changed_by__username",
                    "batch__change_source",
                    "batch__change_reason",
                )

                return Response(
                    {
                        "record_id": record_id,
                        "versions_count": versions.count(),
                        "versions": version_data,
                        "version_relationships": self._get_version_relationships(
                            versions
                        ),
                    }
                )
            else:
                # 完整模式：返回所有版本详细信息
                serializer = RecordChangeLogSerializer(versions, many=True)

                # 包含当前记录信息
                record_serializer = ArchiveRecordSerializer(record)

                return Response(
                    {
                        "record": record_serializer.data,
                        "versions": serializer.data,
                        "versions_count": versions.count(),
                        "version_relationships": self._get_version_relationships(
                            versions
                        ),
                    }
                )

        except ArchiveRecord.DoesNotExist:
            return Response(
                {"error": f"记录不存在: {record_id}"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"获取记录历史失败: {str(e)}")
            return Response(
                {"error": f"获取记录历史失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _get_version_relationships(self, versions):
        """获取版本之间的关系，用于构建版本关系图"""
        relationships = []

        # 添加顺序关系（版本链）
        for i in range(len(versions) - 1):
            relationships.append(
                {
                    "type": "sequence",
                    "from_version": versions[i].version_number,
                    "to_version": versions[i + 1].version_number,
                }
            )

        # 添加回滚关系
        rollback_versions = versions.filter(is_rollback=True)
        for version in rollback_versions:
            relationships.append(
                {
                    "type": "rollback",
                    "from_version": version.version_number,
                    "to_version": version.rollback_source_version,
                    "description": f"回滚到版本{version.rollback_source_version}",
                }
            )

        return relationships


class RecordVersionCompareView(APIView):
    """
    记录版本比较视图
    比较同一记录的两个不同版本
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, record_id, format=None):
        """比较记录的两个版本"""
        try:
            # 获取要比较的版本号
            version1 = request.query_params.get("v1")
            version2 = request.query_params.get("v2")

            if not (version1 and version2):
                return Response(
                    {"error": "缺少要比较的版本参数 v1 和 v2"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # 查询两个版本的变更记录
            v1_record = RecordChangeLog.objects.get(
                record_id=record_id, version_number=int(version1)
            )

            v2_record = RecordChangeLog.objects.get(
                record_id=record_id, version_number=int(version2)
            )

            # 比较两个版本的差异
            differences = self._compare_versions(v1_record, v2_record)

            return Response(
                {
                    "record_id": record_id,
                    "version1": int(version1),
                    "version2": int(version2),
                    "differences": differences,
                }
            )

        except RecordChangeLog.DoesNotExist:
            return Response(
                {"error": f"指定的版本不存在"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"比较版本失败: {str(e)}")
            return Response(
                {"error": f"比较版本失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _compare_versions(self, v1_record, v2_record):
        """比较两个版本记录的差异

        Args:
            v1_record: 第一个版本记录
            v2_record: 第二个版本记录

        Returns:
            list: 差异字段列表
        """
        differences = []

        # 使用record_after进行比较，因为它包含完整的记录状态
        v1_state = v1_record.record_after or {}
        v2_state = v2_record.record_after or {}

        # 获取所有字段列表
        all_fields = set(v1_state.keys()) | set(v2_state.keys())

        # 忽略的字段列表 - 通常是内部字段或元数据字段
        ignored_fields = [
            "created_at",
            "updated_at",
            "import_date",
            "batch_number",
            "change_count",
        ]

        for field in all_fields:
            # 跳过忽略的字段
            if field in ignored_fields:
                continue

            v1_value = v1_state.get(field)
            v2_value = v2_state.get(field)

            # 兼容性处理：处理None和空字符串
            if v1_value == "":
                v1_value = None
            if v2_value == "":
                v2_value = None

            # 跳过值相同的字段
            if v1_value == v2_value:
                continue

            # 找出字段的中文名称
            field_label = field
            field_objects = (
                FieldChangeLog.objects.filter(field_name=field)
                .values_list("field_label", flat=True)
                .distinct()
            )

            if field_objects.exists():
                field_label = field_objects[0]

            # 判断字段重要性
            importance = "normal"
            field_importance = (
                FieldChangeLog.objects.filter(field_name=field)
                .values_list("field_importance", flat=True)
                .distinct()
            )

            if field_importance.exists():
                importance = field_importance[0]

            differences.append(
                {
                    "field": field,
                    "field_label": field_label,
                    "importance": importance,
                    "v1_value": v1_value,
                    "v2_value": v2_value,
                }
            )

        # 按字段重要性排序
        importance_order = {"critical": 0, "important": 1, "normal": 2}
        return sorted(
            differences, key=lambda x: importance_order.get(x["importance"], 3)
        )


class RecordVersionRollbackView(APIView):
    """记录版本回滚视图"""

    permission_classes = [IsAuthenticated]

    def post(self, request, record_id, version_number, format=None):
        """回滚到指定版本"""
        # 添加完整的异常处理
        try:
            # 查找记录和目标版本
            record = ArchiveRecord.objects.get(id=record_id)
            target_version = RecordChangeLog.objects.get(
                record=record, version_number=version_number
            )

            # 查找当前最新版本
            current_version = (
                RecordChangeLog.objects.filter(record=record)
                .order_by("-version_number")
                .first()
            )

            # 确保不是回滚到当前版本
            if current_version.version_number == version_number:
                return Response(
                    {"message": "无需回滚，当前已是指定版本", "changes_count": 0}
                )

            # 创建变更批次
            change_batch = ChangeLogBatch.objects.create(
                batch_id=uuid.uuid4(),
                change_source="manual_rollback",
                change_reason=f"回滚到版本 {version_number}",
                changed_by=request.user,
            )

            # 记录变更前状态
            record_before = {}
            for field in record._meta.fields:
                if not field.primary_key and field.name != "id":
                    value = getattr(record, field.name)
                    record_before[field.name] = (
                        str(value) if value is not None else None
                    )

            # 获取目标状态
            rollback_data = target_version.record_after
            changes = []

            # 获取字段变更
            for field, value in rollback_data.items():
                if hasattr(record, field):
                    # 获取字段类型信息
                    field_obj = record._meta.get_field(field)

                    # 处理外键字段
                    if field_obj.is_relation and field_obj.many_to_one:
                        current_value = getattr(record, field)
                        current_value_str = (
                            str(current_value.id) if current_value else "None"
                        )

                        # 如果值与当前值相同，跳过
                        if current_value_str == value:
                            continue

                        # 处理空值
                        if value is None or value == "None" or value == "":
                            if current_value is not None:
                                changes.append(
                                    {
                                        "field": field,
                                        "field_label": self.get_field_label(field),
                                        "old_value": current_value,
                                        "new_value": None,
                                        "importance": "normal",
                                    }
                                )
                                setattr(record, field, None)
                        else:
                            # 特殊处理用户外键字段
                            if (
                                field == "import_user"
                                or field.endswith("_by")
                                or field == "changed_by"
                            ):
                                try:
                                    # 尝试通过ID查找用户
                                    if isinstance(value, str) and value.isdigit():
                                        user_obj = User.objects.get(id=int(value))
                                    elif isinstance(value, int):
                                        user_obj = User.objects.get(id=value)
                                    else:
                                        # 尝试通过用户名查找
                                        user_obj = User.objects.get(username=value)

                                    if current_value != user_obj:
                                        changes.append(
                                            {
                                                "field": field,
                                                "field_label": self.get_field_label(
                                                    field
                                                ),
                                                "old_value": current_value,
                                                "new_value": user_obj,
                                                "importance": "normal",
                                            }
                                        )
                                        setattr(record, field, user_obj)
                                except User.DoesNotExist:
                                    logger.warning(
                                        f"回滚用户外键字段 {field} 失败: 找不到用户 {value}"
                                    )
                                except Exception as e:
                                    logger.warning(
                                        f"回滚用户外键字段 {field} 失败: {str(e)}"
                                    )
                            else:
                                # 处理其他外键关系
                                try:
                                    related_model = field_obj.related_model
                                    related_pk = value
                                    # 尝试转换为整数ID
                                    if isinstance(value, str) and value.isdigit():
                                        related_pk = int(value)

                                    related_obj = related_model.objects.get(
                                        pk=related_pk
                                    )

                                    if current_value != related_obj:
                                        changes.append(
                                            {
                                                "field": field,
                                                "field_label": self.get_field_label(
                                                    field
                                                ),
                                                "old_value": current_value,
                                                "new_value": related_obj,
                                                "importance": "normal",
                                            }
                                        )
                                        setattr(record, field, related_obj)
                                except Exception as e:
                                    # 记录错误但继续处理其他字段
                                    logger.warning(
                                        f"回滚外键字段 {field} 失败: {str(e)}"
                                    )
                    else:
                        # 处理普通字段
                        current_value = getattr(record, field)
                        current_value_str = (
                            str(current_value) if current_value is not None else None
                        )

                        # 如果值相同，跳过
                        if current_value_str == value:
                            continue

                        # 转换值类型
                        try:
                            field_type = field_obj.get_internal_type()
                            if field_type in ("IntegerField", "PositiveIntegerField"):
                                typed_value = (
                                    int(value) if value and value != "None" else None
                                )
                            elif field_type in ("FloatField", "DecimalField"):
                                typed_value = (
                                    float(value) if value and value != "None" else None
                                )
                            elif field_type == "BooleanField":
                                typed_value = (
                                    bool(value) if value and value != "None" else False
                                )
                            elif field_type.endswith(
                                "DateField"
                            ) or field_type.endswith("DateTimeField"):
                                if value and value != "None":
                                    typed_value = (
                                        timezone.make_aware(
                                            datetime.fromisoformat(value)
                                        )
                                        if "T" in value
                                        else datetime.fromisoformat(value)
                                    )
                                else:
                                    typed_value = None
                            else:
                                typed_value = value

                            changes.append(
                                {
                                    "field": field,
                                    "field_label": self.get_field_label(field),
                                    "old_value": current_value,
                                    "new_value": typed_value,
                                    "importance": "normal",
                                }
                            )
                            setattr(record, field, typed_value)
                        except Exception as e:
                            logger.warning(f"回滚字段 {field} 失败: {str(e)}")

            # 如果有变更，保存记录
            if changes:
                record.updated_at = timezone.now()
                record.save()

                # 记录变更后的完整状态
                record_after = {}
                for field in record._meta.fields:
                    if not field.primary_key and field.name != "id":
                        value = getattr(record, field.name)
                        record_after[field.name] = (
                            str(value) if value is not None else None
                        )

                # 获取下一个版本号
                next_version = (
                    RecordChangeLog.objects.filter(record=record)
                    .order_by("-version_number")
                    .first()
                    .version_number
                    + 1
                )

                # 创建记录级变更日志，使用回滚专有类型和标记
                record_change = RecordChangeLog.objects.create(
                    batch=change_batch,
                    record=record,
                    version_number=next_version,
                    change_type="rollback",  # 使用回滚类型
                    record_before=record_before,
                    record_after=record_after,
                    changed_fields_count=len(changes),
                    is_rollback=True,  # 标记为回滚操作
                    rollback_source_version=version_number,  # 记录回滚源版本
                )

                # 记录字段变更
                field_logs = []
                for change in changes:
                    field_logs.append(
                        FieldChangeLog(
                            record_change=record_change,
                            field_name=change["field"],
                            field_label=change["field_label"],
                            old_value=(
                                str(change["old_value"])
                                if change["old_value"] is not None
                                else None
                            ),
                            new_value=(
                                str(change["new_value"])
                                if change["new_value"] is not None
                                else None
                            ),
                            field_importance=change.get("importance", "normal"),
                        )
                    )

                if field_logs:
                    FieldChangeLog.objects.bulk_create(field_logs)

                return Response(
                    {
                        "message": f"已成功回滚到版本 {version_number}",
                        "changes_count": len(changes),
                        "new_version": next_version,
                        "is_rollback": True,
                        "rollback_source": version_number,
                    }
                )
            else:
                return Response(
                    {"message": "回滚未执行，没有检测到变更", "changes_count": 0}
                )

        except RecordChangeLog.DoesNotExist:
            return Response(
                {"error": f"指定的版本不存在: {version_number}"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ArchiveRecord.DoesNotExist:
            return Response(
                {"error": f"记录不存在: {record_id}"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"回滚操作失败: {str(e)}", exc_info=True)
            return Response(
                {"error": f"回滚失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_field_label(self, field_name):
        """
        获取字段的人类可读标签
        
        CHANGE: [2025-01-15] 已废弃，字段显示名称现在由前端统一处理
        保留方法以兼容现有代码
        """
        # 直接返回字段名，前端会处理显示
        return field_name


def apply_ag_grid_filters(queryset, request):
    """
    应用AG Grid特定的筛选和排序

    Args:
        queryset: 要筛选的查询集
        request: HTTP请求对象，包含筛选和排序参数

    Returns:
        Django QuerySet: 应用筛选和排序后的查询集
    """
    # 1. 首先应用日期范围筛选(委托日期) - 效率最高的筛选条件
    date_filters = {
        "commission_datetime__gte": request.query_params.get(
            "commission_datetime__gte"
        ),
        "commission_datetime__lte": request.query_params.get(
            "commission_datetime__lte"
        ),
    }
    date_kwargs = {k: v for k, v in date_filters.items() if v}
    if date_kwargs:
        logger.debug(f"应用日期筛选: {date_kwargs}")
        queryset = queryset.filter(**date_kwargs)

    # 2. 再应用列筛选 - 精确或范围筛选，效率次之
    filter_model = None
    filter_model_json = request.query_params.get("filter_model")
    if filter_model_json:
        try:
            filter_model = json.loads(filter_model_json)
            logger.debug(f"成功解析filter_model参数: {filter_model}")
            queryset = _apply_filter_model(queryset, filter_model)
        except json.JSONDecodeError as e:
            logger.error(f"解析filter_model参数时出错: {e}")

    # 3. 最后应用快速筛选(AND组合模糊搜索) - 处理最复杂，放在最后
    q = request.query_params.get("q")
    if q and q.strip():
        # 将搜索字符串按空格分割为多个词
        search_terms = [term.strip() for term in q.split() if term.strip()]
        logger.debug(f"应用AND组合快速筛选: 搜索词={search_terms}")

        if search_terms:
            # 搜索字段列表 - 增加了unified_number和report_number提高覆盖面
            search_fields = [
                "sample_number",
                "commission_number",
                "project_name",
                "client_unit",
                "project_address",
                "unified_number",
                "report_number",
            ]

            # 对每个搜索词创建一个Q对象(各字段OR组合)，然后各词之间AND组合
            combined_q = Q()
            for term in search_terms:
                term_q = Q()  # 当前词的Q对象
                for field in search_fields:
                    term_q |= Q(**{f"{field}__icontains": term})

                # 与总Q对象使用AND组合 - 确保每个词都必须匹配
                combined_q &= term_q

            # 应用AND组合模糊搜索条件
            queryset = queryset.filter(combined_q)
            logger.debug(f"应用快速筛选后的记录数: {queryset.count()}")

    return queryset


def _apply_filter_model(queryset, filter_model):
    """
    根据筛选模型应用筛选

    Args:
        queryset: 要筛选的查询集
        filter_model: AG Grid筛选模型对象

    Returns:
        已筛选的查询集
    """
    if not filter_model or not isinstance(filter_model, dict):
        logger.warning(f"无效的筛选模型格式: {filter_model}")
        return queryset

    # 遍历筛选模型中的每个字段
    for field, condition in filter_model.items():
        # 忽略空条件或非字典条件
        if not condition or not isinstance(condition, dict):
            logger.warning(f"忽略无效的筛选条件 - 字段: {field}, 条件: {condition}")
            continue

        try:
            # 获取筛选类型
            filter_type = condition.get("filterType")
            logger.debug(
                f"处理筛选字段 {field}, 类型: {filter_type}, 条件: {condition}"
            )

            # 文本筛选
            if filter_type == "text":
                queryset = _apply_text_filter(queryset, field, condition)

            # 数字筛选
            elif filter_type == "number":
                queryset = _apply_number_filter(queryset, field, condition)

            # 日期筛选
            elif filter_type == "date":
                queryset = _apply_date_filter(queryset, field, condition)

            # 集合筛选（如下拉列表选择）
            elif filter_type == "set":
                queryset = _apply_set_filter(queryset, field, condition)

            # 其他未知类型
            else:
                logger.warning(f"未支持的筛选类型: {filter_type}, 字段: {field}")

        except Exception as e:
            # 记录错误但继续处理其他筛选条件
            logger.error(f"应用筛选条件时出错 - 字段: {field}, 错误: {str(e)}")
            continue

    return queryset


def _apply_text_filter(queryset, field, condition):
    """应用文本筛选"""
    filter_value = condition.get("filter")
    condition_type = condition.get("type")

    if not filter_value:
        return queryset

    # 文本筛选类型映射
    text_filter_map = {
        "contains": f"{field}__icontains",
        "equals": f"{field}__exact",
        "startsWith": f"{field}__istartswith",
        "endsWith": f"{field}__iendswith",
        # 默认使用包含查询
        None: f"{field}__icontains",
    }

    filter_key = text_filter_map.get(condition_type, text_filter_map[None])
    filter_kwargs = {filter_key: filter_value}

    logger.debug(f"应用文本筛选: {filter_kwargs}")
    return queryset.filter(**filter_kwargs)


def _apply_number_filter(queryset, field, condition):
    """应用数字筛选"""
    filter_value = condition.get("filter")
    condition_type = condition.get("type")
    filter_to = condition.get("filterTo")

    if filter_value is None:
        return queryset

    # 数字筛选类型映射
    number_filter_map = {
        "equals": field,
        "greaterThan": f"{field}__gt",
        "lessThan": f"{field}__lt",
        "greaterThanOrEqual": f"{field}__gte",
        "lessThanOrEqual": f"{field}__lte",
        "notEqual": f"{field}__ne",
    }

    # 处理范围查询特殊情况
    if condition_type == "inRange" and filter_to is not None:
        range_filter = {f"{field}__gte": filter_value, f"{field}__lte": filter_to}
        logger.debug(f"应用数字范围筛选: {range_filter}")
        return queryset.filter(**range_filter)
    elif condition_type in number_filter_map:
        filter_key = number_filter_map[condition_type]
        filter_kwargs = {filter_key: filter_value}
        logger.debug(f"应用数字筛选: {filter_kwargs}")
        return queryset.filter(**filter_kwargs)

    return queryset


def _apply_date_filter(queryset, field, condition):
    """应用日期筛选"""
    date_from = condition.get("dateFrom")
    date_to = condition.get("dateTo")
    condition_type = condition.get("type")

    logger.debug(f"处理日期筛选: 类型={condition_type}, 从={date_from}, 到={date_to}")

    # 处理相等查询特殊情况
    if condition_type == "equals" and date_from:
        return queryset.filter(**{field: date_from})

    # 处理日期范围
    date_filters = {}
    if date_from:
        date_filters[f"{field}__gte"] = date_from
    if date_to:
        date_filters[f"{field}__lte"] = date_to

    if date_filters:
        logger.debug(f"应用日期范围筛选: {date_filters}")
        return queryset.filter(**date_filters)

    return queryset


def _apply_set_filter(queryset, field, condition):
    """应用集合筛选（多选）"""
    values = condition.get("values")
    if not values or not isinstance(values, list) or len(values) == 0:
        return queryset

    logger.debug(f"应用集合筛选: 字段={field}, 值={values}")
    return queryset.filter(**{f"{field}__in": values})


class ArchiveRecordListView(generics.ListAPIView):
    """
    获取档案台账记录列表的 API 视图

    要求用户认证，符合生产环境标准
    """

    queryset = ArchiveRecord.objects.all().order_by("-updated_at")  # 按更新时间降序排序
    serializer_class = ArchiveRecordSerializer  # 使用包含所有字段的序列化器
    pagination_class = (
        LimitOffsetPaginationWithMaxLimit  # 使用支持limit/offset的分页器，适配AG Grid
    )
    permission_classes = [IsAuthenticated]  # 始终要求认证
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]  # 添加过滤、搜索、排序
    filterset_fields = [  # 可过滤字段
        "sample_number",
        "commission_number",
        "unified_number",
        "report_number",
        "project_name",
        "client_unit",
        "archive_status",
        "batch_number",
    ]
    search_fields = [  # 可搜索字段
        "sample_number",
        "commission_number",
        "unified_number",
        "report_number",
        "project_name",
        "client_unit",
        "project_address",
        "archive_box_number",
    ]
    ordering_fields = [  # 可排序字段
        "id",
        "updated_at",
        "created_at",
        "commission_datetime",
        "storage_datetime",
        "archive_datetime",
        "project_name",
        "client_unit",
    ]
    ordering = ["-updated_at"]  # 默认排序

    def get_queryset(self):
        """
        重写get_queryset方法，应用AG Grid特定的筛选和排序
        """
        queryset = super().get_queryset()

        # 应用AG Grid筛选和排序
        queryset = apply_ag_grid_filters(queryset, self.request)

        return queryset

    def list(self, request, *args, **kwargs):
        """
        重写list方法，添加对AG Grid特定参数的处理
        """
        # 从AG Grid的前端请求中读取filter_model、filter和sort参数
        filter_model = request.query_params.get("filter_model")
        filter_param = request.query_params.get("filter")
        sort_model = request.query_params.get("sort")
        ordering = request.query_params.get("ordering")

        # 记录请求参数，用于调试
        logger.debug(
            f"AG Grid请求参数 - filter_model: {filter_model}, filter: {filter_param}, sort: {sort_model}"
        )
        logger.debug(f"排序参数 - ordering: {ordering}")
        logger.debug(f"所有请求参数: {dict(request.query_params)}")

        # 调用父类方法获取数据
        return super().list(request, *args, **kwargs)


class ChangeOrderViewSet(viewsets.ModelViewSet):
    """更改单视图集

    提供更改单的增删改查功能，以及提交、审批、拒绝、执行和取消等操作。
    """

    queryset = ChangeOrder.objects.all()
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["order_number", "title", "description"]
    ordering_fields = ["created_at", "updated_at", "status", "priority"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """根据不同操作返回对应的序列化器"""
        if self.action == "create":
            return ChangeOrderCreateSerializer
        return ChangeOrderSerializer

    def get_permissions(self):
        """根据不同操作设置权限"""
        if self.action in ["submit"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["approve", "reject"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["execute"]:
            permission_classes = [IsAuthenticated]
        elif self.action in ["cancel", "destroy"]:
            permission_classes = [IsAuthenticated, CanManageChangeOrder]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """根据查询参数过滤更改单"""
        queryset = ChangeOrder.objects.all()

        # 根据状态过滤
        status = self.request.query_params.get("status", None)
        if status:
            queryset = queryset.filter(status=status)

        # 根据优先级过滤
        priority = self.request.query_params.get("priority", None)
        if priority:
            queryset = queryset.filter(priority=priority)

        # 根据创建者过滤
        created_by = self.request.query_params.get("created_by", None)
        if created_by:
            queryset = queryset.filter(created_by_id=created_by)

        # 根据时间范围过滤
        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)
        if start_date and end_date:
            queryset = queryset.filter(created_at__range=[start_date, end_date])
        elif start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        elif end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        # 关键词搜索
        keyword = self.request.query_params.get("keyword", None)
        if keyword:
            queryset = queryset.filter(
                Q(title__icontains=keyword)
                | Q(description__icontains=keyword)
                | Q(order_number__icontains=keyword)
            )

        return queryset

    def perform_create(self, serializer):
        """创建更改单时设置创建者"""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """更新更改单时设置更新者"""
        serializer.save(updated_by=self.request.user)

    @action(detail=True, methods=["post"])
    def submit(self, request, pk=None):
        """提交更改单

        将更改单状态从草稿变更为待审批
        """
        change_order = self.get_object()
        service = ChangeOrderService()

        try:
            service.submit_change_order(change_order, request.user)
            return Response(
                {
                    "success": True,
                    "message": "更改单已成功提交",
                    "data": ChangeOrderSerializer(change_order).data,
                }
            )
        except Exception as e:
            return Response(
                {"success": False, "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def approve(self, request, pk=None):
        """审批更改单

        将更改单状态从待审批变更为已审批
        """
        change_order = self.get_object()
        service = ChangeOrderService()

        try:
            service.approve_change_order(change_order, request.user)
            return Response(
                {
                    "success": True,
                    "message": "更改单已成功审批",
                    "data": ChangeOrderSerializer(change_order).data,
                }
            )
        except Exception as e:
            return Response(
                {"success": False, "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def reject(self, request, pk=None):
        """拒绝更改单

        将更改单状态从待审批变更为已拒绝
        """
        change_order = self.get_object()
        service = ChangeOrderService()

        serializer = ChangeOrderActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        reason = serializer.validated_data.get("reason", "")

        try:
            service.reject_change_order(change_order, request.user, reason)
            return Response(
                {
                    "success": True,
                    "message": "更改单已拒绝",
                    "data": ChangeOrderSerializer(change_order).data,
                }
            )
        except Exception as e:
            return Response(
                {"success": False, "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def execute(self, request, pk=None):
        """执行更改单

        将更改单状态从已审批变更为已完成，同时执行档案记录的实际更改
        """
        change_order = self.get_object()
        service = ChangeOrderService()

        try:
            service.execute_change_order(change_order, request.user)
            return Response(
                {
                    "success": True,
                    "message": "更改单已成功执行",
                    "data": ChangeOrderSerializer(change_order).data,
                }
            )
        except Exception as e:
            return Response(
                {"success": False, "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def cancel(self, request, pk=None):
        """取消更改单

        将更改单状态变更为已取消
        """
        change_order = self.get_object()
        service = ChangeOrderService()

        serializer = ChangeOrderActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        reason = serializer.validated_data.get("reason", "")

        try:
            service.cancel_change_order(change_order, request.user, reason)
            return Response(
                {
                    "success": True,
                    "message": "更改单已取消",
                    "data": ChangeOrderSerializer(change_order).data,
                }
            )
        except Exception as e:
            return Response(
                {"success": False, "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ChangeOrderItemViewSet(viewsets.ModelViewSet):
    """更改单条目视图集

    提供更改单条目的增删改查功能
    """

    queryset = ChangeOrderItem.objects.all()
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["id"]
    ordering = ["id"]

    def get_serializer_class(self):
        """根据不同操作返回对应的序列化器"""
        if self.action == "create":
            return ChangeOrderItemCreateSerializer
        return ChangeOrderItemSerializer

    def get_permissions(self):
        """设置权限"""
        return [IsAuthenticated()]

    def get_queryset(self):
        """根据查询参数过滤更改单条目"""
        queryset = ChangeOrderItem.objects.all()

        # 根据更改单ID过滤
        change_order_id = self.request.query_params.get("change_order", None)
        if change_order_id:
            queryset = queryset.filter(change_order_id=change_order_id)

        # 根据档案记录ID过滤
        archive_record_id = self.request.query_params.get("archive_record", None)
        if archive_record_id:
            queryset = queryset.filter(archive_record_id=archive_record_id)

        return queryset


class ChangeOrderAttachmentViewSet(viewsets.ModelViewSet):
    """更改单附件视图集

    提供更改单附件的增删改查功能
    """

    queryset = ChangeOrderAttachment.objects.all()
    serializer_class = ChangeOrderAttachmentSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["upload_time"]
    ordering = ["-upload_time"]

    def get_permissions(self):
        """设置权限"""
        return [IsAuthenticated()]

    def get_queryset(self):
        """根据查询参数过滤更改单附件"""
        queryset = ChangeOrderAttachment.objects.all()

        # 根据更改单ID过滤
        change_order_id = self.request.query_params.get("change_order", None)
        if change_order_id:
            queryset = queryset.filter(change_order_id=change_order_id)

        return queryset

    def perform_create(self, serializer):
        """创建附件时设置上传者"""
        serializer.save(uploaded_by=self.request.user)


# class ExcelImportAnalysisView(APIView):
#     """
#     Excel导入冲突分析视图
    
#     此视图处理Excel导入的第一阶段：接收Excel文件，创建导入会话，
#     立即返回会话ID供前端轮询进度，同时在后台启动分析过程。
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.analyze action
#     """
#     permission_classes = [IsAuthenticated]
    
#     def post(self, request):
#         """
#         接收上传的Excel文件，创建导入会话，并在后台启动分析
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] ExcelImportAnalysisView.post 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.analyze")
        
#         excel_file = request.FILES.get('file')
#         sheet_name = request.data.get('sheet_name', 0)
        
#         if not excel_file:
#             return Response(
#                 {'success': False, 'error': '未提供Excel文件'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         if not excel_file.name.endswith(('.xlsx', '.xls')):
#             return Response(
#                 {'success': False, 'error': '文件必须是Excel格式(.xlsx或.xls)'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         try:
#             session_manager = ImportSessionManager()
#             logger.info(f"[API.AnalyzeView] 用户 {request.user.username} 请求创建导入会话，文件名: {excel_file.name}")
            
#             # CHANGE: create_session现在返回字典，包含success和session字段
#             create_result = session_manager.create_session(
#                 uploaded_file=excel_file, 
#                 user_id=request.user.id, 
#                 sheet_name=sheet_name
#             )
            
#             # 检查session是否成功创建
#             if not create_result.get('success', False):
#                 # 如果创建失败，返回错误信息
#                 error_msg = create_result.get('error', '创建会话失败')
#                 error_type = create_result.get('error_type', 'general_error')
#                 existing_session_id = create_result.get('existing_session_id')
                
#                 logger.warning(f"[API.AnalyzeView] 会话创建失败: {error_msg}")
                
#                 status_code = status.HTTP_409_CONFLICT if error_type == 'system_has_active_session' else status.HTTP_400_BAD_REQUEST
                
#                 return Response(
#                     {'success': False, 'error': error_msg, 'errorType': error_type, 'existing_session_id': existing_session_id},
#                     status=status_code
#                 )
            
#             # 会话创建成功，从返回字典中获取session实例
#             import_session_orm = create_result.get('session')
            
#             # 从ORM实例获取session_id
#             session_id_str = str(import_session_orm.session_id)
#             logger.info(f"[API.AnalyzeView] 导入会话创建成功，ID: {session_id_str}, 初始状态: {import_session_orm.status}")
            
#             # 后台分析线程
#             import threading
#             def run_analysis_in_background(s_id_for_thread: str):
#                 current_session_id = s_id_for_thread # 避免闭包问题
#                 try:
#                     logger.info(f"[API.AnalyzeBG] 后台线程开始分析Excel文件，会话ID: {current_session_id}")
#                     # analyze_session 内部会获取 ImportSession 实例
#                     # 创建新的manager实例以确保线程隔离性，虽然在这个场景下可能不是严格必须
#                     thread_session_manager = ImportSessionManager()
#                     thread_session_manager.analyze_session(current_session_id)
#                     logger.info(f"[API.AnalyzeBG] 后台分析顺利完成，会话ID: {current_session_id}")
#                 except Exception as e_bg:
#                     logger.error(f"[API.AnalyzeBG] 后台分析线程失败，会话ID: {current_session_id}, 错误: {str(e_bg)}", exc_info=True)
#                     # 尝试将会话标记为错误状态
#                     try:
#                         with transaction.atomic():
#                             # 重新获取会话以确保是最新的，并锁定
#                             failed_session = ImportSession.objects.select_for_update().get(session_id=uuid.UUID(current_session_id))
#                             # CHANGE: [2025-05-30] 使用CONCLUSIVE_SESSION_STATUSES替代硬编码的状态列表
#                             if failed_session.status not in CONCLUSIVE_SESSION_STATUSES:
#                                 old_status_bg_err = failed_session.status
#                                 failed_session.status = ImportSessionStatus.ERROR
#                                 failed_session.error_message = f"后台分析线程错误: {str(e_bg)[:500]}"
#                                 failed_session.save(update_fields=['status', 'error_message', 'updated_at'])
#                                 SessionOperation.objects.create(
#                                     session=failed_session,
#                                     operation_type='error_in_background_analysis',
#                                     user=failed_session.created_by, # 或尝试从请求上下文中获取，但线程中较难
#                                     old_status=old_status_bg_err,
#                                     new_status=ImportSessionStatus.ERROR,
#                                     details={'error': str(e_bg)[:500], 'source': 'background_thread'}
#                                 )
#                                 logger.info(f"[API.AnalyzeBG] 会话 {current_session_id} 已在后台标记为ERROR")
#                             else:
#                                 logger.info(f"[API.AnalyzeBG] 会话 {current_session_id} 状态已是终态 ({failed_session.status})，未再次标记为ERROR。")
#                     except ImportSession.DoesNotExist:
#                         logger.error(f"[API.AnalyzeBG] 尝试标记错误状态时，会话 {current_session_id} 未找到。")
#                     except Exception as e_mark_error:
#                         logger.error(f"[API.AnalyzeBG] 标记会话 {current_session_id} 为错误时再次失败: {e_mark_error}", exc_info=True)
            
#             analysis_thread = threading.Thread(target=run_analysis_in_background, args=(session_id_str,))
#             analysis_thread.daemon = True
#             analysis_thread.start()
#             logger.info(f"[API.AnalyzeView] 已启动后台分析线程，会话ID: {session_id_str}")
            
#             # 返回给前端的信息
#             return Response({
#                 'success': True,
#                 'data': {
#                     'import_session_id': session_id_str,
#                     'file_name': import_session_orm.file_name, # 从ORM实例获取
#                     'total_records': import_session_orm.record_count,  # 初始为0，分析完成后更新
#                     'status': import_session_orm.status, # 初始状态 (应该是UPLOAD)
#                     'message': '文件已上传，正在后台准备分析'
#                 }
#             })
        
#         except ValueError as ve: # 通常是由于已存在活跃会话
#             logger.warning(f"[API.AnalyzeView] 创建会话失败: {str(ve)}", exc_info=True) # 添加exc_info
#             return Response(
#                 {'success': False, 'error': str(ve), 'errorType': 'active_session_exists'},
#                 status=status.HTTP_409_CONFLICT
#             )
#         except Exception as e_main:
#             logger.error(f"[API.AnalyzeView] Excel导入会话创建主流程失败: {str(e_main)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': f'创建Excel导入会话时出错: {str(e_main)}'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class ExcelImportConfirmView(APIView):
#     """
#     Excel导入确认视图
#     此视图处理Excel导入的第二阶段：根据用户决策执行导入，处理冲突并返回导入结果。
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.confirm action
#     """
#     permission_classes = [IsAuthenticated]
    
#     def post(self, request):
#         """
#         确认导入Excel文件，根据用户决策处理冲突
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] ExcelImportConfirmView.post 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.confirm")
        
#         logger.info(f"[API.Confirm] Raw request.data: {request.data}") # 打印原始请求数据
#         session_id = request.data.get('import_session_id')
#         expected_session_id = request.data.get('expected_session_id')  # CHANGE: [2025-05-28] 新增期望会话ID校验
#         resolutions = request.data.get('resolutions', [])
#         # 新增：从请求中获取分析阶段和用户决策阶段的统计数据
#         analysis_stats_from_request = request.data.get('analysis_stats') 
#         user_decision_stats_from_request = request.data.get('user_decision_stats')
        
#         logger.info(f"[API.Confirm] 收到确认导入请求，会话ID: {session_id}, 期望会话ID: {expected_session_id}, 解决方案数量: {len(resolutions)}")
#         if analysis_stats_from_request:
#             logger.info(f"[API.Confirm] 接收到的分析统计: {analysis_stats_from_request}")
#         else:
#             logger.warning(f"[API.Confirm] 请求中未包含 analysis_stats 参数。")
#         if user_decision_stats_from_request:
#             logger.info(f"[API.Confirm] 接收到的用户决策统计: {user_decision_stats_from_request}")
#         else:
#             logger.warning(f"[API.Confirm] 请求中未包含 user_decision_stats 参数。")

#         if not session_id:
#             logger.warning("[API.Confirm] 确认导入缺少会话ID参数")
#             return Response(
#                 {'success': False, 'error': '未提供导入会话ID'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         # CHANGE: [2025-05-28] 实现严格的会话状态校验
#         try:
#             session_manager = ImportSessionManager()
            
#             # 获取系统当前唯一活跃会话
#             current_active_session = session_manager.get_system_active_session()
            
#             if not current_active_session:
#                 logger.warning(f"[API.Confirm] 系统当前无活跃会话，但用户尝试确认会话 {session_id}")
#                 return Response({
#                     'success': False, 
#                     'error': '系统当前没有活跃的导入会话，请刷新页面重新开始。',
#                     'error_type': 'no_active_session'
#                 }, status=status.HTTP_409_CONFLICT)
            
#             # 校验会话ID匹配
#             if str(current_active_session.session_id) != str(session_id):
#                 logger.warning(f"[API.Confirm] 会话ID不匹配。请求的: {session_id}, 当前活跃的: {current_active_session.session_id}")
#                 return Response({
#                     'success': False,
#                     'error': f'会话状态已变更，请刷新页面。当前活跃会话: {current_active_session.session_id}',
#                     'error_type': 'session_id_mismatch',
#                     'current_active_session_id': str(current_active_session.session_id)
#                 }, status=status.HTTP_409_CONFLICT)
            
#             # 校验期望会话ID（如果提供）
#             if expected_session_id and str(expected_session_id) != str(current_active_session.session_id):
#                 logger.warning(f"[API.Confirm] 期望会话ID不匹配。期望的: {expected_session_id}, 当前活跃的: {current_active_session.session_id}")
#                 return Response({
#                     'success': False,
#                     'error': '会话状态已发生变化，请刷新页面获取最新状态。',
#                     'error_type': 'expected_session_mismatch',
#                     'current_active_session_id': str(current_active_session.session_id)
#                 }, status=status.HTTP_409_CONFLICT)
            
#             # 验证会话状态是否适合确认导入
#             # CHANGE: [2025-06-01] 根据文档要求，确认导入只能在CONFLICT_RESOLUTION_IN_PROGRESS状态下进行
#             if current_active_session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
#                 logger.warning(f"[API.Confirm] 会话 {session_id} 状态不适合确认导入: {current_active_session.status}")
#                 return Response({
#                     'success': False,
#                     'error': f'会话当前状态为 {current_active_session.get_status_display()}，只有在"冲突处理中"状态下才能执行导入确认。',
#                     'error_type': 'invalid_session_state',
#                     'current_status': current_active_session.status
#                 }, status=status.HTTP_400_BAD_REQUEST)
            
#             # 验证用户权限
#             if current_active_session.created_by != request.user and current_active_session.processing_user != request.user:
#                 logger.warning(f"[API.Confirm] 用户 {request.user.username} 无权确认会话 {session_id}")
#                 return Response({
#                     'success': False,
#                     'error': '您没有权限操作此导入会话。',
#                     'error_type': 'permission_denied'
#                 }, status=status.HTTP_403_FORBIDDEN)
            
#         except Exception as e:
#             logger.error(f"[API.Confirm] 会话状态校验失败: {str(e)}", exc_info=True)
#             return Response({
#                 'success': False,
#                 'error': f'会话状态校验时发生错误: {str(e)}',
#                 'error_type': 'session_validation_error'
#             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
#         # 验证resolution的action值 (这部分逻辑保持不变)
#         valid_actions = [ConflictResolution.UPDATE, ConflictResolution.SKIP, ConflictResolution.CREATE]
#         for resolution in resolutions:
#             if 'action' in resolution and resolution['action'] not in valid_actions:
#                 logger.warning(f"[API.Confirm] 无效的处理动作: {resolution.get('action')}")
#                 return Response(
#                     {'success': False, 'error': f'无效的处理动作: {resolution.get("action")}，有效值为: {", ".join(valid_actions)}'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
#             if 'action' in resolution and resolution['action'] == ConflictResolution.CREATE:
#                  logger.warning(f"[API.Confirm] 注意: 用户为委托编号 {resolution.get('commission_number')} 选择了CREATE操作，但系统将以smart_update策略处理。")

#         try:
#             # 先延长会话时间 (extend_session 已重构为直接操作ORM)
#             if not session_manager.extend_session(session_id):
#                 logger.warning(f"[API.Confirm] 尝试确认导入前，延长会话 {session_id} 失败 (可能不存在或非活跃)")
#                 return Response(
#                     {'success': False, 'error': '导入会话不存在、已过期或非活跃，无法继续导入。请重新开始。' , 'errorType': 'session_extend_failed'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
#             logger.info(f"[API.Confirm] 会话 {session_id} 已成功延长。")

#             # CHANGE: [2025-05-18] 调用已修改为异步触发的 confirm_import
#             # 原: import_results_dict = session_manager.confirm_import(...)
#             task_submission_response = session_manager.confirm_import(
#                 session_id=session_id,
#                 resolutions=resolutions,
#                 user=request.user,
#                 analysis_stats_dict=analysis_stats_from_request, # 传递分析统计
#                 user_decision_stats_dict=user_decision_stats_from_request # 传递用户决策统计
#             )
            
#             logger.info(f"[API.Confirm] 导入任务提交结果 for session {session_id}: {task_submission_response}")
            
#             # 检查任务提交是否成功
#             if task_submission_response.get('success'):
#                 # 异步任务已提交，返回 202 Accepted 或 200 OK 告知客户端任务已接受
#                 return Response({
#                     'success': True,
#                     'message': '导入确认任务已成功提交到后台处理。',
#                     'data': task_submission_response.get('data') # 包含 session_id, task_id, current_status
#                 }, status=status.HTTP_202_ACCEPTED) # 使用 202 表示请求已接受，正在异步处理
#             else:
#                 # 如果任务提交本身失败 (例如，参数验证失败，会话状态不对等)
#                 # session_manager.confirm_import 应该返回包含 error 和 error_type 的字典
#                 error_detail = task_submission_response.get('error', '提交导入任务失败，但未提供具体错误信息。')
#                 error_type = task_submission_response.get('error_type', 'task_submission_failed')
#                 logger.error(f"[API.Confirm] 提交导入任务失败 for session {session_id}: {error_detail} (Type: {error_type})")
#                 # 根据错误类型决定HTTP状态码，通常是客户端错误
#                 response_status = status.HTTP_400_BAD_REQUEST
#                 if error_type == 'session_not_found':
#                     response_status = status.HTTP_404_NOT_FOUND
#                 elif error_type == 'invalid_session_state':
#                     response_status = status.HTTP_409_CONFLICT # 状态冲突
                
#                 return Response({
#                     'success': False, 
#                     'error': error_detail,
#                     'errorType': error_type
#                 }, status=response_status)
#             # END CHANGE
            
#         except ValueError as ve: # 特定捕获ValueError，通常是业务逻辑错误（如状态不对）
#             logger.warning(f"[API.Confirm] Excel导入确认参数错误或状态不符 for session {session_id}: {str(ve)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': str(ve), 'errorType': 'invalid_state_or_parameters'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
#         except Exception as e:
#             logger.error(f"[API.Confirm] Excel导入确认失败 for session {session_id}: {str(e)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': f'确认导入时发生意外错误: {str(e)}'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class ExcelImportAnalysisResultView(APIView):
#     """
#     Excel导入分析结果查询视图
#     提供获取完整分析结果的功能，包括冲突记录和统计信息
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.analysis_result action
#     """
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request):
#         """
#         获取导入分析结果
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] ExcelImportAnalysisResultView.get 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.analysis_result")
        
#         session_id = request.query_params.get('import_session_id')
#         logger.info(f"[API.Result] 收到分析结果查询请求，会话ID: {session_id}")
        
#         if not session_id:
#             logger.warning("[API.Result] 分析结果查询缺少会话ID参数")
#             return Response(
#                 {'success': False, 'error': '未提供导入会话ID'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         try:
#             # CHANGE: [2024-07-27]不再需要 session_manager.get_session() 来获取会话，直接查询
#             try:
#                 db_session = ImportSession.objects.get(session_id=session_id)
#             except ImportSession.DoesNotExist:
#                 logger.warning(f"[API.Result] 会话不存在: {session_id}")
#                 return Response(
#                     {'success': False, 'error': '导入会话不存在或已过期', 'errorType': 'session_not_found_or_expired'},
#                     status=status.HTTP_404_NOT_FOUND
#                 )

#             # CHANGE: [2025-06-02] 修正状态检查逻辑，只允许在分析和冲突处理阶段获取分析结果
#             # 移除导入阶段状态，因为一旦进入导入阶段，用户就不应该再查看分析结果了
#             valid_result_statuses = [
#                 ImportSessionStatus.ANALYSIS_COMPLETE,
#                 ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
#                 ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,
#                 ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
#                 ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED,
#                 # 移除了导入阶段的状态：IMPORT_QUEUED, IMPORT_START, IMPORT_IN_PROGRESS, 
#                 # IMPORT_COMPLETED_SUCCESSFULLY, IMPORT_COMPLETED_WITH_ERRORS
#                 # 这些状态下应该查看导入结果，而不是分析结果
#             ]

#             if db_session.status not in valid_result_statuses:
#                 logger.warning(f"[API.Result] 会话状态不允许获取分析结果: {db_session.status} for session {session_id}")
#                 return Response(
#                     {
#                         'success': False, 
#                         'error': f'导入会话当前状态为 {db_session.get_status_display()}，无法获取分析结果。只有在分析完成后才能获取结果。', 
#                         'status': db_session.status
#                     },
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             if not db_session.is_active() and db_session.status not in valid_result_statuses:
#                  logger.warning(f"[API.Result] 会话 {session_id} 非活跃且状态不允许获取结果（状态：{db_session.status}）。")
#                  # 如果会话已过期但分析已完成，也许仍允许查看结果？取决于业务需求。
#                  # 当前严格要求会话活跃或至少状态符合要求。
#                  if db_session.expires_at and timezone.now() > db_session.expires_at:
#                      error_msg = '导入会话已过期'
#                  else:
#                      error_msg = f'导入会话当前状态为 {db_session.get_status_display()}，无法获取分析结果。'
#                  return Response(
#                     {'success': False, 'error': error_msg, 'errorType': 'session_inactive_for_results', 'status': db_session.status},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # CHANGE: [2025-06-02] 从数据库获取冲突详情
#             # 修复：返回所有需要用户确认的记录类型，不仅仅是update类型
#             # 包含：new（新增）、update（更新）、error（错误）类型，排除identical（完全相同）类型
#             conflict_details_queryset = ImportConflictDetail.objects.filter(
#                 session=db_session,
#                 conflict_type__in=["new", "update", "error"]  # 返回所有需要用户确认的记录类型
#             ).order_by('excel_row_number')

#             conflict_records_list = [cd.to_dict() for cd in conflict_details_queryset]
#             logger.info(f"[API.Result] 从数据库为会话 {session_id} 获取了 {len(conflict_records_list)} 条需要确认的记录（包含new、update、error类型）。")
            
#             analysis_stats = {}
#             # CHANGE: [2025-06-04] 从专门的analysis_stats字段读取统计数据 #issue_session_cancel_overwrite
#             if db_session.analysis_stats:
#                 analysis_stats = db_session.analysis_stats
#                 if not isinstance(analysis_stats, dict):
#                     analysis_stats = {}
#             # TEMPORARY DISABLED: [2025-06-04] 向后兼容代码已注释，出问题时可重新启用
#             # elif db_session.error_message: # 兼容旧数据：如果analysis_stats为空但error_message包含JSON
#             #     try:
#             #         analysis_stats = json.loads(db_session.error_message)
#             #         if not isinstance(analysis_stats, dict):
#             #             analysis_stats = {}
#             #     except json.JSONDecodeError:
#             #         logger.warning(f"[API.Result] 会话 {session_id} 的error_message不是有效JSON，跳过统计数据解析")
#             #         analysis_stats = {}
            
#             # 保留所有统计信息，包括identical和new的计数，确保前端显示正确的总数
#             default_stat_keys = ["total", "new", "identical", "update", "error"]
#             final_stats = {key: analysis_stats.get(key, 0) for key in default_stat_keys}

#             expires_at_iso = db_session.expires_at.isoformat() if db_session.expires_at else None
            
#             response_data = {
#                 'success': True,
#                 'data': {
#                     'import_session_id': str(db_session.session_id),
#                     'analysis_result': final_stats,
#                     'conflict_records': conflict_records_list,
#                     'file_name': db_session.file_name,
#                     'total_records': db_session.record_count,
#                     'status': db_session.status,
#                     'expires_at': expires_at_iso
#                 }
#             }
            
#             logger.info(f"[API.Result] 返回分析结果给会话 {session_id}, 冲突记录数: {len(conflict_records_list)}, 统计: {final_stats}")
#             return Response(response_data)
            
#         except Exception as e:
#             logger.error(f"[API.Result] 获取导入分析结果失败 for session {session_id}: {str(e)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': f'获取分析结果时发生意外错误: {str(e)}'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class GetActiveImportSessionView(APIView):
#     """
#     获取活跃导入会话视图
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.active_session action
#     """
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request):
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] GetActiveImportSessionView.get 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.active_session")
        
#         current_user = request.user
#         logger.info(f"[API.GetActive] 用户 {current_user.username} 查询系统活动导入会话...")
#         try:
#             session_manager = ImportSessionManager()
#             # _ensure_session_status_is_current WILL be called inside get_system_active_session if it needs to update expiry-based status, but NOT for heartbeat timeout.
#             active_session = session_manager.get_system_active_session() 
            
#             if not active_session:
#                 logger.info(f"[API.GetActive] 系统当前没有活动的导入会话。")
#                 return Response({
#                     'success': True,
#                     'data': {'has_active_session': False, 'message': '当前系统没有活动的导入会话。'}
#                 })
            
#             session_info_dict = active_session.to_dict()
            
#             # CHANGE: [2025-06-02] GetActiveImportSessionView 将不再主动修改会话状态以处理心跳超时。
#             # 这个逻辑将移至 BeginActiveConflictProcessingView。
#             # 这里只如实返回会话信息，前端UI根据此信息决定按钮状态和提示。
#             logger.info(f"[API.GetActive] 返回系统活动会话 {active_session.session_id} (状态: {active_session.status}) 给用户 {current_user.username}.")

#             return Response({
#                 'success': True,
#                 'data': {
#                     'has_active_session': True,
#                     'session_info': session_info_dict,
#                     # 'can_takeover' is removed as takeover logic is now part of BeginActive...
#                     'message': '成功获取活动会话信息。'
#                 }
#             })
            
#         except Exception as e:
#             logger.error(f"[API.GetActive] 获取系统活跃导入会话失败: {str(e)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': f'获取活跃导入会话时出错: {str(e)}', 'error_type': 'server_error'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class BeginActiveConflictProcessingView(APIView):
#     """
#     开始/继续/接管冲突处理视图
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.begin_conflict_processing action
#     """
#     permission_classes = [IsAuthenticated]
    
#     @swagger_auto_schema(
#         operation_summary="开始/继续/接管冲突处理",
#         operation_description=(
#             "允许用户开始处理状态为 STARTED 或 PENDING 的会话；"
#             "或继续处理状态为 IN_PROGRESS 且处理者是自己的会话（会更新心跳）；"
#             "或接管状态为 IN_PROGRESS 但原处理者已心跳超时的会话。"
#             "所有操作均为原子性。"
#         ),
#         manual_parameters=[
#             openapi.Parameter(
#                 name='session_id', 
#                 in_=openapi.IN_PATH, 
#                 description="要操作的会话ID。", 
#                 required=True, 
#                 type=openapi.TYPE_STRING,
#                 format=openapi.FORMAT_UUID
#             )
#         ],
#         responses={
#             200: openapi.Response(description="操作成功，可以开始/继续处理冲突", examples={"application/json": {"success": True, "message": "...", "session_data": "{...}"}}),
#             400: openapi.Response(description="无效的会话ID或会话状态不允许此操作"),
#             403: openapi.Response(description="禁止操作（例如，尝试操作他人未超时的会话）"),
#             404: openapi.Response(description="会话未找到"),
#             409: openapi.Response(description="状态冲突（例如，操作时会话状态已被改变）")
#         },
#         tags=['Excel Import Session Management']
#     )
#     def post(self, request, session_id, *args, **kwargs):
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] BeginActiveConflictProcessingView.post 被调用 - 用户: {request.user.username}，会话: {session_id}。此视图已废弃，请使用 ExcelImportSessionViewSet.begin_conflict_processing")
        
#         logger.info(f"User {request.user.username} attempting to begin/continue/takeover conflict processing for session {session_id}.")
#         # The session_id from the URL (due to <uuid:session_id> path converter) is already a UUID object.
#         # No need to convert it again or catch ValueError for format, as Django handles that at URL routing.

#         try:
#             with transaction.atomic():
#                 session = ImportSession.objects.select_for_update().get(session_id=session_id)

#                 if not session.is_active():
#                     logger.warning(f"User {request.user.username} attempt on inactive session {session_id}, status: {session.status}")
#                     return Response({"success": False, "error": "会话已非活跃，无法操作。", "current_status": session.status}, status=status.HTTP_400_BAD_REQUEST)

#                 current_status = session.status
#                 current_processing_user = session.processing_user
#                 request_user = request.user
                
#                 # 允许操作的状态
#                 actionable_conflict_statuses = [
#                     ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
#                     ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
#                     ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS
#                 ]

#                 if current_status not in actionable_conflict_statuses:
#                     logger.warning(f"Session {session_id} in status {current_status} is not actionable for conflict processing by user {request_user.username}.")
#                     return Response({
#                         "success": False, 
#                         "error": f"会话当前状态为'{session.get_status_display()}'，无法进行冲突处理操作。",
#                         "current_status": current_status
#                     }, status=status.HTTP_400_BAD_REQUEST)

#                 # 情况一: 无人处理 (STARTED or PENDING)
#                 if current_status in [ImportSessionStatus.CONFLICT_RESOLUTION_STARTED, ImportSessionStatus.CONFLICT_RESOLUTION_PENDING]:
#                     if current_processing_user is not None:
#                         # 理论上不应发生，但作为防御性编程
#                         logger.error(f"Session {session_id} is {current_status} but unexpectedly has processing_user {current_processing_user.username}. Correcting.")
#                         # session.processing_user = None # 会在下面被覆盖
                    
#                     session.status = ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS
#                     session.processing_user = request_user
#                     session.last_heartbeat_at = timezone.now()
#                     session.last_activity = timezone.now()
#                     session.save()
#                     SessionOperation.objects.create(session=session, operation_type='begin_conflict_processing', user=request_user, old_status=current_status, new_status=session.status, details={"message": f"User {request_user.username} started/resumed conflict processing."})
#                     logger.info(f"User {request_user.username} took session {session_id} from {current_status} to IN_PROGRESS.")
#                     return Response({"success": True, "message": "已成功开始处理冲突。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)

#                 # 情况二和三: 会话已在 IN_PROGRESS
#                 elif current_status == ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
#                     if current_processing_user == request_user:
#                         # 用户是本人，更新心跳即可
#                         session.last_heartbeat_at = timezone.now()
#                         session.last_activity = timezone.now()
#                         session.save(update_fields=['last_heartbeat_at', 'last_activity', 'updated_at'])
#                         logger.info(f"User {request_user.username} (current processor) continues processing session {session_id} (heartbeat updated). ")
#                         return Response({"success": True, "message": "您可以继续处理冲突。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)
#                     else:
#                         # 其他人正在处理，检查是否超时
#                         heartbeat_timeout_minutes = getattr(settings, "SESSION_HEARTBEAT_TIMEOUT_MINUTES", 2)
#                         is_timed_out = False
#                         if session.last_heartbeat_at:
#                             if (timezone.now() - session.last_heartbeat_at) > timedelta(minutes=heartbeat_timeout_minutes):
#                                 is_timed_out = True
#                         else:
#                             # 没有心跳记录，但有处理用户，视为可以尝试接管（或根据业务规则判定为立即超时）
#                             is_timed_out = True 
#                             logger.warning(f"Session {session_id} is IN_PROGRESS by {current_processing_user.username if current_processing_user else 'Unknown'} but has no heartbeat record. Assuming timed out for user {request_user.username}.")

#                         if is_timed_out:
#                             logger.info(f"Session {session_id} was IN_PROGRESS by {current_processing_user.username if current_processing_user else 'Other'}, but timed out. User {request_user.username} is taking over.")
#                             original_user_username = current_processing_user.username if current_processing_user else "Unknown"
                            
#                             # 步骤 1: 记录超时并转为 PENDING (可选但清晰)
#                             # session.status = ImportSessionStatus.CONFLICT_RESOLUTION_PENDING
#                             # session.error_message = session.error_message or f"原处理用户 {original_user_username} 心跳超时。"
#                             # session.save(update_fields=['status', 'error_message', 'updated_at'])
#                             # SessionOperation.objects.create(session=session, operation_type='timeout_to_pending', old_status=ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS, new_status=ImportSessionStatus.CONFLICT_RESOLUTION_PENDING, details={"timed_out_user": original_user_username})

#                             # 步骤 2: 新用户接管并转为 IN_PROGRESS
#                             session.status = ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS # 保持或重新设置为 IN_PROGRESS
#                             session.processing_user = request_user
#                             session.last_heartbeat_at = timezone.now()
#                             session.last_activity = timezone.now()
#                             # error_message 字段不应在此处被新用户接管的消息覆盖，保留原超时信息或分析信息
#                             session.save()
#                             SessionOperation.objects.create(session=session, operation_type='takeover_conflict_processing', user=request_user, old_status=ImportSessionStatus.CONFLICT_RESOLUTION_PENDING, new_status=session.status, details={"message": f"User {request_user.username} took over from {original_user_username} due to timeout."})
#                             return Response({"success": True, "message": f"已成功接管来自用户 {original_user_username} 的超时会话。", "session_data": session.to_dict()}, status=status.HTTP_200_OK)
#                         else:
#                             logger.warning(f"User {request_user.username} attempt to take over session {session_id} from {current_processing_user.username}, but not timed out.")
#                             return Response({
#                                 "success": False, 
#                                 "error": f"会话当前由用户 '{current_processing_user.username}' 处理中，且对方在线。",
#                                 "current_status": current_status,
#                                 "processing_user": current_processing_user.username
#                             }, status=status.HTTP_409_CONFLICT) # 409 Conflict is appropriate here
#                 else: # Should not be reached if actionable_conflict_statuses is comprehensive
#                     logger.error(f"[BeginActiveConflictProcessingView] Unhandled session status: {current_status} for session {session_id}")
#                     return Response({"success": False, "error": "未知的会话状态，无法操作。", "current_status": current_status}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#         except ImportSession.DoesNotExist:
#             logger.warning(f"Session {session_id} not found for conflict processing attempt by {request.user.username}.")
#             return Response({"success": False, "error": "导入会话未找到。"}, status=status.HTTP_404_NOT_FOUND)
#         except Exception as e:
#             logger.exception(f"Error during begin/continue/takeover conflict processing for session {session_id} by user {request.user.username}: {e}")
#             return Response({"success": False, "error": "服务器内部错误，无法处理请求。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class ExcelImportCancelView(APIView):
#     """
#     取消Excel导入会话视图
#     允许用户取消正在进行的导入会话，并清理相关资源。
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.cancel action
#     """
#     permission_classes = [IsAuthenticated]
    
#     def post(self, request):
#         """
#         取消正在进行的导入会话
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] ExcelImportCancelView.post 被调用 - 用户: {request.user.username}。此视图已废弃，请使用 ExcelImportSessionViewSet.cancel")
        
#         session_id = request.data.get('import_session_id')
#         expected_session_id = request.data.get('expected_session_id')  # CHANGE: [2025-05-28] 新增期望会话ID校验
#         reason = request.data.get('reason', '用户手动取消') # 允许前端传递取消原因
#         logger.info(f"[API.Cancel] 用户 {request.user.username} 请求取消导入会话，ID: {session_id}, 期望ID: {expected_session_id}, 原因: {reason}")
        
#         if not session_id:
#             logger.warning("[API.Cancel] 取消导入请求缺少会话ID参数")
#             return Response(
#                 {'success': False, 'error': '未提供导入会话ID'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
        
#         try:
#             session_manager = ImportSessionManager()
            
#             # CHANGE: [2025-05-28] 实现严格的会话状态校验
#             # 获取系统当前唯一活跃会话
#             current_active_session = session_manager.get_system_active_session()
            
#             if not current_active_session:
#                 logger.warning(f"[API.Cancel] 系统当前无活跃会话，但用户尝试取消会话 {session_id}")
#                 return Response({
#                     'success': False, 
#                     'error': '系统当前没有活跃的导入会话，可能已经完成或取消。',
#                     'error_type': 'no_active_session'
#                 }, status=status.HTTP_409_CONFLICT)
            
#             # 校验会话ID匹配
#             if str(current_active_session.session_id) != str(session_id):
#                 logger.warning(f"[API.Cancel] 会话ID不匹配。请求的: {session_id}, 当前活跃的: {current_active_session.session_id}")
#                 return Response({
#                     'success': False,
#                     'error': f'会话状态已变更，请刷新页面。当前活跃会话: {current_active_session.session_id}',
#                     'error_type': 'session_id_mismatch',
#                     'current_active_session_id': str(current_active_session.session_id)
#                 }, status=status.HTTP_409_CONFLICT)
            
#             # 校验期望会话ID（如果提供）
#             if expected_session_id and str(expected_session_id) != str(current_active_session.session_id):
#                 logger.warning(f"[API.Cancel] 期望会话ID不匹配。期望的: {expected_session_id}, 当前活跃的: {current_active_session.session_id}")
#                 return Response({
#                     'success': False,
#                     'error': '会话状态已发生变化，请刷新页面获取最新状态。',
#                     'error_type': 'expected_session_mismatch',
#                     'current_active_session_id': str(current_active_session.session_id)
#                 }, status=status.HTTP_409_CONFLICT)

#             # 业务权限检查 - 简化为任何登录用户都可以取消任何会话
#             # CHANGE: [权限简化] 移除了复杂的业务权限控制，但保留技术安全检查（会话状态、并发控制等）
#             # 未来如需要可重新引入: 只有创建者、当前处理者或管理员可以取消
#             can_cancel = True  # 简化的业务权限检查

#             # 如果会话已经是终态，则不应再取消（技术安全检查）
#             terminal_states = [ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY, ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS, ImportSessionStatus.CANCELLED, ImportSessionStatus.ERROR, ImportSessionStatus.FINALIZED]
#             if current_active_session.status in terminal_states:
#                 logger.warning(f"[API.Cancel] 尝试取消一个已经处于终态的会话: {session_id}, 状态: {current_active_session.status}")
                
#                 # CHANGE: [2024-07-27] 已取消会话或错误会话也返回成功，允许UI正常清理状态
#                 if current_active_session.status == ImportSessionStatus.CANCELLED:
#                     return Response({
#                         'success': True, 
#                         'data': {
#                             'message': f'会话已处于" {current_active_session.get_status_display()} "状态，无需取消',
#                             'session_id': str(session_id),
#                             'status': ImportSessionStatus.CANCELLED
#                         }
#                     })
                    
#                 return Response({
#                     'success': False, 
#                     'error': f'会话已处于" {current_active_session.get_status_display()} "状态，无需取消', 
#                     'errorType': 'session_already_terminated'
#                 }, status=status.HTTP_400_BAD_REQUEST)

#             # 核心操作：清理会话资源并标记为已取消
#             # CHANGE: [2025-05-28] 使用重构后的取消逻辑，传递session对象而非ID
#             session_manager._cleanup_session_resources(
#                 session=current_active_session,  # 传递session对象
#                 cancelled_by=request.user,
#                 reason=reason
#             )
            
#             # CHANGE: [2024-07-27] 添加强制会话缓存清理，确保会话真正被清理
#             # 强制从活跃会话列表中移除
#             try:
#                 active_key = "excel_active_sessions"
#                 if hasattr(cache, 'get') and hasattr(cache, 'set'):
#                     active_sessions = cache.get(active_key) or {}
#                     if session_id in active_sessions:
#                         del active_sessions[session_id]
#                         cache.set(active_key, active_sessions)
#                         logger.info(f"[API.Cancel] 从活跃会话缓存中移除了会话: {session_id}")
#             except Exception as e_cache:
#                 logger.warning(f"[API.Cancel] 清理会话缓存时出错: {e_cache}")
            
#             logger.info(f"[API.Cancel] 导入会话已成功标记为取消并清理资源: {session_id}")
#             return Response({
#                 'success': True, 
#                 'data': {
#                     'message': '导入会话已成功取消',
#                     'session_id': str(session_id),
#                     'status': ImportSessionStatus.FINALIZED  # CHANGE: [2025-05-28] 返回最终状态
#                 }
#             })
                    
#         except Exception as e:
#             logger.error(f"[API.Cancel] 取消导入会话 {session_id} 时出错: {str(e)}", exc_info=True)
#             return Response(
#                 {'success': False, 'error': f'取消导入会话时出错: {str(e)}'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class SessionHeartbeatView(APIView):
#     """
#     处理导入会话心跳的API视图
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.heartbeat action
#     """
#     permission_classes = [IsAuthenticated]

#     def post(self, request, session_id: uuid.UUID, *args, **kwargs):
#         """
#         记录指定导入会话的心跳。
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] SessionHeartbeatView.post 被调用 - 用户: {request.user.username}，会话: {session_id}。此视图已废弃，请使用 ExcelImportSessionViewSet.heartbeat")
        
#         manager = ImportSessionManager()
#         success, message = manager.record_heartbeat(session_id=session_id, user=request.user)

#         if success:
#             return Response({"success": True, "message": message}, status=status.HTTP_200_OK)
#         else:
#             # 根据消息内容判断更具体的错误状态码
#             if "会话不存在" in message or "已不存在" in message:
#                 status_code = status.HTTP_404_NOT_FOUND
#             elif "不是当前会话的处理者" in message or "处理者已改变" in message:
#                 status_code = status.HTTP_403_FORBIDDEN
#             elif "非活跃" in message: # 例如 "会话非活跃，无法记录心跳。" 或 "会话已变为非活跃状态"
#                  status_code = status.HTTP_400_BAD_REQUEST # 或者 409 Conflict 如果认为状态冲突更合适
#             else:
#                 status_code = status.HTTP_500_INTERNAL_SERVER_ERROR # 默认为服务器内部错误
            
#             logger.warning(f"心跳API调用失败 for session {session_id}, user {request.user.username}: {message}")
#             return Response({"success": False, "error": message}, status=status_code)


# class AcknowledgeImportResultsView(APIView):
#     """
#     用户确认导入结果API视图
    
#     DEPRECATED: [2025-06-04] 此视图已废弃，请使用 ExcelImportSessionViewSet.acknowledge action
#     """
#     permission_classes = [IsAuthenticated]

#     def post(self, request, session_id: uuid.UUID, *args, **kwargs):
#         """
#         用户确认指定导入会话的结果已被查看。
#         这会将会话状态更新为 FINALIZED。
#         """
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] AcknowledgeImportResultsView.post 被调用 - 用户: {request.user.username}，会话: {session_id}。此视图已废弃，请使用 ExcelImportSessionViewSet.acknowledge")
        
#         logger.info(f"[API.AcknowledgeResults] 用户 {request.user.username} 请求确认识读会话 {session_id} 的结果。")
        
#         manager = ImportSessionManager()
#         # CHANGE: [2025-05-30] 修正 acknowledge_session_results 调用参数
#         # 首先，根据 session_id 获取 ImportSession 对象
#         try:
#             session_obj = ImportSession.objects.get(session_id=session_id)
#         except ImportSession.DoesNotExist:
#             logger.warning(f"[API.AcknowledgeResults] 会话 {session_id} 不存在，无法确认结果。")
#             return Response({
#                 "success": False, 
#                 "error": "指定的导入会话不存在。",
#                 "current_status": None
#             }, status=status.HTTP_404_NOT_FOUND)

#         success, message, current_status_val = manager.acknowledge_session_results(session=session_obj, user=request.user)

#         if success:
#             logger.info(f"[API.AcknowledgeResults] 会话 {session_id} 结果确认成功。消息: {message}")
#             return Response({
#                 "success": True, 
#                 "data": {
#                     "message": message,
#                     "session_id": str(session_id),
#                     "new_status": current_status_val.value if current_status_val else None
#                 }
#             }, status=status.HTTP_200_OK)
#         else:
#             logger.warning(f"[API.AcknowledgeResults] 会话 {session_id} 结果确认失败。消息: {message}, 当前状态: {current_status_val}")
#             status_code = status.HTTP_400_BAD_REQUEST
#             if message == "指定的导入会话不存在。":
#                 status_code = status.HTTP_404_NOT_FOUND
#             elif '并非"导入完成"状态' in message: # 使用单引号包裹，避免内部双引号冲突
#                  status_code = status.HTTP_409_CONFLICT # 状态冲突
            
#             return Response({
#                 "success": False, 
#                 "error": message,
#                 "current_status": current_status_val.value if current_status_val else None
#             }, status=status_code)


# class PendActiveConflictProcessingView(APIView):
#     permission_classes = [IsAuthenticated]
    
#     @swagger_auto_schema(
#         operation_summary="暂停/搁置当前导入会话中的冲突处理",
#         operation_description="当用户正在处理冲突（会话处于'冲突处理中'状态）并决定暂时中断（例如关闭处理界面但未提交）时调用。成功后，会话状态将变为'冲突处理暂停'，处理用户将被清空，允许其他用户或同一用户后续继续处理。",
#         manual_parameters=[
#             openapi.Parameter(
#                 name='session_id', 
#                 in_=openapi.IN_PATH, 
#                 description="要暂停冲突处理的会话ID。", 
#                 required=True, 
#                 type=openapi.TYPE_STRING,
#                 format=openapi.FORMAT_UUID
#             )
#         ],
#         responses={
#             200: openapi.Response(
#                 description="成功暂停冲突处理",
#                 examples={"application/json": {"success": True, "message": "冲突处理已暂停。", "session_data": "{...}"}}
#             ),
#             400: openapi.Response(description="无效的会话ID或会话状态不符/非当前处理用户操作"),
#             403: openapi.Response(description="用户无权限执行此操作"),
#             404: openapi.Response(description="会话未找到")
#         },
#         tags=['Excel Import Session Management']
#     )
#     def post(self, request, session_id, *args, **kwargs):
#         # CHANGE: [2025-06-04] 添加废弃警告
#         logger.warning(f"[DEPRECATED] PendActiveConflictProcessingView.post 被调用 - 用户: {request.user.username}，会话: {session_id}。此视图已废弃，请使用 ExcelImportSessionViewSet.pend_conflict_processing")
        
#         logger.info(f"User {request.user.username} attempting to pend active conflict processing for session {session_id}.")
#         try:
#             # CHANGE: [2025-06-02] 修复UUID类型转换错误 - 检查session_id类型后再转换
#             if isinstance(session_id, uuid.UUID):
#                 session_uuid = session_id
#             else:
#                 session_uuid = uuid.UUID(session_id)
#         except ValueError:
#             logger.warning(f"Invalid session_id format: {session_id} for pend active conflict processing attempt.")
#             return Response({"success": False, "error": "无效的会话ID格式。"}, status=status.HTTP_400_BAD_REQUEST)

#         try:
#             with transaction.atomic():
#                 session = ImportSession.objects.select_for_update().get(session_id=session_uuid)

#                 if not session.is_active(): # is_active 也会检查 CANCELLED, FINALIZED 等状态
#                     logger.warning(f"User {request.user.username} attempt to pend conflict processing on inactive session {session_id}, status: {session.status}")
#                     return Response({"success": False, "error": "会话已非活跃，无法操作。"}, status=status.HTTP_400_BAD_REQUEST)

#                 if session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
#                     logger.warning(f"Session {session_id} is in status {session.status}, not CONFLICT_RESOLUTION_IN_PROGRESS, for pending conflict processing.")
#                     # 即使状态不是IN_PROGRESS，如果用户是processing_user，也应允许其"放弃"处理权，并转为PENDING
#                     # 但如果根本没有processing_user，或者状态完全不对，则拒绝
#                     if not session.processing_user or session.processing_user != request.user:
#                          return Response({
#                             "success": False, 
#                             "error": f"会话当前状态为'{session.get_status_display()}'，并非'处理中'，无法暂停。或您不是当前处理用户。"
#                         }, status=status.HTTP_400_BAD_REQUEST)
                
#                 # 只有当前处理用户才能暂停自己的操作
#                 if session.processing_user != request.user:
#                     logger.warning(f"User {request.user.username} is not the current processing user ({session.processing_user.username if session.processing_user else 'None'}) for session {session_id}. Cannot pend.")
#                     return Response({
#                         "success": False, 
#                         "error": "您不是此会话的当前处理用户，无法暂停。"
#                     }, status=status.HTTP_403_FORBIDDEN)

#                 old_status_val = session.status
#                 session.status = ImportSessionStatus.CONFLICT_RESOLUTION_PENDING
#                 session.processing_user = None # 清空处理用户
#                 session.last_activity = timezone.now() # 更新活动时间
#                 # last_heartbeat_at 不需要在此处清除，因为它反映的是上一个IN_PROGRESS状态的活动
#                 session.save()

#                 SessionOperation.objects.create(
#                     session=session,
#                     operation_type='status_change', # 或者更具体的如 'pend_conflict_processing'
#                     user=request.user,
#                     old_status=old_status_val,
#                     new_status=session.status,
#                     details={
#                         "message": f"User {request.user.username} pended active conflict processing.",
#                         "action": "pend_active_conflict_processing"
#                     }
#                 )
#                 logger.info(f"User {request.user.username} successfully pended active conflict processing for session {session_id}. Status changed to {session.status}.")
#                 return Response({
#                     "success": True, 
#                     "message": "冲突处理已暂停。", 
#                     "session_data": session.to_dict()
#                 }, status=status.HTTP_200_OK)

#         except ImportSession.DoesNotExist:
#             logger.warning(f"Session {session_id} not found for pending conflict processing.")
#             return Response({"success": False, "error": "导入会话未找到。"}, status=status.HTTP_404_NOT_FOUND)
#         except Exception as e:
#             logger.exception(f"Error during pending active conflict processing for session {session_id}: {e}")
#             return Response({"success": False, "error": "服务器内部错误，无法暂停冲突处理。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# ==========================================
# 保留的原有APIView类 (待废弃)
# TODO: 在确认ExcelImportSessionViewSet工作正常后移除这些类
# ==========================================
