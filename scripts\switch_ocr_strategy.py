#!/usr/bin/env python3
"""
OCR 策略切换工具
用于在方案1和方案2之间切换
"""
import os
import sys
import argparse

def switch_strategy(strategy: str):
    """切换 OCR 处理策略"""
    
    ocr_utils_path = "archive_processing/utils/ocr_utils.py"
    
    if not os.path.exists(ocr_utils_path):
        print(f"❌ 找不到文件: {ocr_utils_path}")
        return False
    
    # 读取文件内容
    with open(ocr_utils_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if strategy == "1":
        # 切换到方案1: 微服务内图像处理
        new_value = "True"
        strategy_name = "方案1 - 微服务内图像处理"
        description = "现代化架构，网络效率高，微服务自包含"
    elif strategy == "2":
        # 切换到方案2: 主应用图像处理
        new_value = "False"
        strategy_name = "方案2 - 主应用图像处理"
        description = "保守策略，零风险，保持现有逻辑"
    else:
        print("❌ 无效的策略选择，请选择 1 或 2")
        return False
    
    # 替换配置值
    old_line = None
    new_content = []
    
    for line in content.split('\n'):
        if line.strip().startswith('USE_MICROSERVICE_IMAGE_PROCESSING ='):
            old_line = line.strip()
            new_line = f"USE_MICROSERVICE_IMAGE_PROCESSING = {new_value}  # 当前策略: {strategy_name}"
            new_content.append(new_line)
        else:
            new_content.append(line)
    
    if old_line is None:
        print("❌ 找不到配置项 USE_MICROSERVICE_IMAGE_PROCESSING")
        return False
    
    # 写回文件
    with open(ocr_utils_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_content))
    
    print(f"✅ 成功切换到 {strategy_name}")
    print(f"   描述: {description}")
    print(f"   配置: USE_MICROSERVICE_IMAGE_PROCESSING = {new_value}")
    print(f"   文件: {ocr_utils_path}")
    
    return True


def show_current_strategy():
    """显示当前策略"""
    ocr_utils_path = "archive_processing/utils/ocr_utils.py"
    
    if not os.path.exists(ocr_utils_path):
        print(f"❌ 找不到文件: {ocr_utils_path}")
        return
    
    with open(ocr_utils_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    for line in content.split('\n'):
        if line.strip().startswith('USE_MICROSERVICE_IMAGE_PROCESSING ='):
            if 'True' in line:
                print("📋 当前策略: 方案1 - 微服务内图像处理")
                print("   特点: 现代化架构，网络效率高，微服务自包含")
            else:
                print("📋 当前策略: 方案2 - 主应用图像处理")
                print("   特点: 保守策略，零风险，保持现有逻辑")
            print(f"   配置: {line.strip()}")
            return
    
    print("❌ 找不到配置项")


def main():
    parser = argparse.ArgumentParser(description="OCR 策略切换工具")
    parser.add_argument('action', choices=['switch', 'show'], 
                       help='操作类型: switch=切换策略, show=显示当前策略')
    parser.add_argument('--strategy', choices=['1', '2'], 
                       help='策略选择: 1=微服务内处理, 2=主应用处理')
    
    args = parser.parse_args()
    
    if args.action == 'show':
        show_current_strategy()
    elif args.action == 'switch':
        if not args.strategy:
            print("❌ 切换策略时必须指定 --strategy 参数")
            print("   使用 --strategy 1 切换到方案1")
            print("   使用 --strategy 2 切换到方案2")
            return 1
        
        if switch_strategy(args.strategy):
            print("\n🔄 建议重启服务以使配置生效:")
            print("   docker-compose restart web")
            return 0
        else:
            return 1
    
    return 0


if __name__ == "__main__":
    print("🔧 OCR 策略切换工具")
    print("=" * 50)
    
    if len(sys.argv) == 1:
        print("用法示例:")
        print("  python scripts/switch_ocr_strategy.py show")
        print("  python scripts/switch_ocr_strategy.py switch --strategy 1")
        print("  python scripts/switch_ocr_strategy.py switch --strategy 2")
        print("")
        print("策略说明:")
        print("  方案1: 微服务内图像处理 (现代化，网络效率高)")
        print("  方案2: 主应用图像处理 (保守，零风险)")
        print("")
        show_current_strategy()
        sys.exit(0)
    
    sys.exit(main())
