#!/usr/bin/env python3
"""
测试Celery队列路由配置

这个脚本用于验证任务是否被正确路由到指定的队列。
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_flow_manager.celery import app
from celery import current_app

def test_task_routing():
    """测试任务路由配置"""
    print("=" * 60)
    print("Celery 任务路由测试")
    print("=" * 60)
    
    # 测试任务列表
    test_tasks = [
        # PDF处理任务
        'archive_processing.tasks.process_pdf_serial_task',
        'archive_processing.tasks.process_pdf_parallel_task',
        'archive_processing.tasks.process_pdf_ocr_task',
        'archive_processing.tasks.aggregate_pdf_ocr_results_task',
        'archive_processing.tasks.process_pdf_with_ocr_results_task',
        'archive_processing.tasks.process_pdf_three_phase_coordinator_task',
        
        # 清理任务（现在路由到默认队列）
        'archive_processing.tasks.cleanup_expired_files_task',
        'archive_processing.tasks.periodic_cleanup_deleted_files',
        'archive_processing.tasks.cleanup_stuck_tasks',
        
        # 默认任务
        'archive_records.tasks.process_finalized_sessions_task',
        'some.unknown.task',  # 测试未知任务
    ]
    
    print("\n📋 任务路由测试结果:")
    print("-" * 60)
    
    for task_name in test_tasks:
        try:
            # 获取任务的路由信息
            route = app.conf.task_routes.get(task_name)
            
            # 如果没有直接匹配，检查模式匹配
            if not route:
                for pattern, route_config in app.conf.task_routes.items():
                    if '*' in pattern:
                        # 简单的通配符匹配
                        pattern_prefix = pattern.replace('*', '')
                        if task_name.startswith(pattern_prefix):
                            route = route_config
                            break
            
            if route:
                queue = route.get('queue', 'default')
                print(f"✅ {task_name}")
                print(f"   -> 队列: {queue}")
            else:
                print(f"⚠️  {task_name}")
                print(f"   -> 队列: default (默认)")
                
        except Exception as e:
            print(f"❌ {task_name}")
            print(f"   -> 错误: {e}")
    
    print("\n" + "=" * 60)

def test_queue_configuration():
    """测试队列配置"""
    print("\n📊 队列配置信息:")
    print("-" * 60)
    
    # 显示配置的队列
    queues = app.conf.task_queues
    if queues:
        for queue in queues:
            print(f"📁 队列: {queue.name}")
            if hasattr(queue, 'exchange'):
                print(f"   交换机: {queue.exchange.name if queue.exchange else 'default'}")
            if hasattr(queue, 'routing_key'):
                print(f"   路由键: {queue.routing_key}")
    else:
        print("⚠️  未找到队列配置")
    
    print(f"\n🔧 默认队列: {app.conf.task_default_queue}")
    print(f"🔧 自动创建队列: {app.conf.task_create_missing_queues}")

def test_broker_connection():
    """测试Broker连接"""
    print("\n🔗 Broker连接测试:")
    print("-" * 60)
    
    try:
        # 尝试连接到broker
        with app.connection() as conn:
            conn.ensure_connection(max_retries=3)
        print("✅ Broker连接成功")
        print(f"   Broker URL: {app.conf.broker_url}")
        print(f"   Result Backend: {app.conf.result_backend}")
    except Exception as e:
        print(f"❌ Broker连接失败: {e}")
        print("💡 提示: 请确保Redis服务正在运行")
        print("   docker run -d --name redis -p 6379:6379 redis:latest")

def show_routing_summary():
    """显示路由配置摘要"""
    print("\n📋 路由配置摘要:")
    print("-" * 60)
    
    routes = app.conf.task_routes
    
    # 按队列分组显示
    queue_tasks = {}
    for task_pattern, route_config in routes.items():
        queue = route_config.get('queue', 'default')
        if queue not in queue_tasks:
            queue_tasks[queue] = []
        queue_tasks[queue].append(task_pattern)
    
    for queue, tasks in queue_tasks.items():
        print(f"\n📁 {queue} 队列:")
        for task in tasks:
            print(f"   - {task}")
    
    print(f"\n📁 default 队列:")
    print("   - 所有未明确路由的任务")

def main():
    """主函数"""
    print("🚀 开始Celery配置测试...\n")
    
    # 测试broker连接
    test_broker_connection()
    
    # 测试队列配置
    test_queue_configuration()
    
    # 测试任务路由
    test_task_routing()
    
    # 显示路由摘要
    show_routing_summary()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    print("\n💡 下一步:")
    print("1. 启动Redis: docker run -d --name redis -p 6379:6379 redis:latest")
    print("2. 启动workers: bash scripts/start_workers.sh start all")
    print("3. 监控状态: python scripts/celery_monitor.py status")
    print("=" * 60)

if __name__ == '__main__':
    main()
