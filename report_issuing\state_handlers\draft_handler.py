from .base_handler import BaseStateHandler, InvalidTransitionError
from report_issuing.states import IssueFormState
from report_issuing.services.state_specific.draft_state_service import DraftStateService
from report_issuing.services.data_services.issue_form_data_service import IssueFormDataService
import logging

logger = logging.getLogger(__name__)

class DraftHandler(BaseStateHandler):
    """
    草稿状态的协调员。
    负责接收指令，并将其委托给专门的业务服务（DraftStateService）来执行。
    同时，它还负责处理状态转换（如锁定）的最终决策。
    """
    def __init__(self, form, user_id):
        super().__init__(form, user_id)
        # 注入草稿状态的专属业务服务
        self.draft_service = DraftStateService(user_id)
        # 注入数据服务实例
        self.data_service = IssueFormDataService()

    def update_draft(self, form_data: dict, items_data: list = None):
        """
        委托：以事务方式更新发放单，并替换所有条目。
        """
        logger.info(f"DraftHandler: 委托以事务方式（替换模式）更新发放单 {self.form.id}。")
        return self.draft_service.update_draft(
            form_id=self.form.id,
            form_data=form_data,
            items_data=items_data
        )

    def lock(self):
        """
        决策：将状态转换为 LOCKED。
        这是状态流转操作，由 Handler 自己决策和执行。
        """
        logger.info(f"DraftHandler: 决策锁定发放单 {self.form.id}。")

        # 1. 实现锁定前的最终检查逻辑
        # 使用数据服务获取完整的表单对象，以访问其关联的条目
        form_instance = self.data_service.get_form_by_id(self.form.id)
        if form_instance.items.count() == 0:
            raise InvalidTransitionError("发放单为空，无法锁定。")
        
        logger.debug(f"发放单 {self.form.id} 通过锁定前检查。")

        # 2. 调用数据服务，直接更新状态字段
        self.data_service.update_form_status(
            form_id=self.form.id, 
            new_status=IssueFormState.LOCKED.value
        )
        
        logger.info(f"状态已从 DRAFT 转换为 LOCKED for form {self.form.id}。")
        
        # 返回更新后的发放单对象
        return self.data_service.get_form_by_id(self.form.id)

    def hard_delete(self):
        """
        委托：硬删除发放单。
        """
        logger.info(f"DraftHandler: 委托硬删除发放单 {self.form.id}。")
        self.draft_service.perform_hard_delete(form_id=self.form.id) 