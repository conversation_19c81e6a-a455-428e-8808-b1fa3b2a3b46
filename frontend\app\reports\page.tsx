"use client"

import { PageTitle } from "@/components/page-title"
import { ReportsStatistics } from "@/components/domain/reports/reports-statistics"
import { ReportsTrendChart } from "@/components/domain/reports/reports-trend-chart"
import { StatusDistributionCard } from "@/components/domain/reports/status-distribution-card"
import { RecentDistributionActivities } from "@/components/domain/reports/recent-distribution-activities"
import { PendingDistributions } from "@/components/domain/reports/pending-distributions"
import { NotesBoard } from "@/components/domain/reports/notes-board"
import { QuickAccessMenu } from "@/components/domain/reports/quick-access-menu"
import { DistributionWorkflow } from "@/components/domain/reports/distribution-workflow"

export default function ReportsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <PageTitle title="报告发放" subtitle="报告发放业务管理与监控" />
      </div>

      <ReportsStatistics />

      <StatusDistributionCard />

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        <QuickAccessMenu />
        <div className="lg:col-span-2">
          <ReportsTrendChart />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        <NotesBoard />
        <div className="lg:col-span-2">
          <PendingDistributions />
        </div>
      </div>

      <RecentDistributionActivities />

      <DistributionWorkflow />
    </div>
  )
}
