"""
Excel数据冲突分析服务

此模块用于分析Excel导入数据与现有数据库记录间的冲突，
支持冲突检测、差异比较和冲突记录生成。
"""

import os
import pandas as pd
import numpy as np
import logging
import uuid
import json
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple, Any, Union
from dataclasses import dataclass, field, asdict
import time
from contextlib import contextmanager
from django.db.models import Q
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone
from django.conf import settings
import datetime as dt

from ..models import ArchiveRecord, ImportSession, ImportSessionStatus, ImportConflictDetail, SessionOperation, ImportFieldDifference
from ..utils.datetime_utils import DateTimeComparisonUtil
from ..field_mappings import get_field_display_name
from archive_records.utils.datetime_utils import TimezoneHandler

# 配置日志
logger = logging.getLogger(__name__)


# 添加性能跟踪器
@contextmanager
def performance_tracker(operation_name, logger=None):
    """
    性能追踪上下文管理器，用于记录操作执行时间
    
    Args:
        operation_name: 操作名称
        logger: 日志记录器
    """
    start_time = time.time()
    if logger:
        logger.info(f"开始 {operation_name}")
    
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        if logger:
            logger.info(f"完成 {operation_name}, 耗时: {duration:.2f}秒")
        else:
            print(f"完成 {operation_name}, 耗时: {duration:.2f}秒")


class ExcelConflictAnalyzer:
    """Excel导入冲突分析器，用于分析Excel文件与现有数据的冲突"""
    
    # 系统/自动管理字段，不参与冲突比较
    SYSTEM_FIELDS = {
        "id", "pk", "batch_number", "source_system", "created_at", "updated_at", 
        "import_user_id", "last_modified_by_id"
    }
    
    # 字段比较中特殊处理的字段
    DATE_FIELDS = {
        "commission_datetime", "test_start_datetime", "test_end_datetime", 
        "created_at", "updated_at", "archive_datetime", "outbound_datetime",
        "storage_datetime", "first_issue_datetime", "sample_retention_datetime"
    }
    
    # 数值字段
    NUMERIC_FIELDS = {
        "component_count", "test_point_count", "unqualified_point_count",
        "standard_price", "discount_price", "actual_price",
        "first_issue_copies", "sample_remaining_time", "group_number"
    }
    
    def __init__(self):
        """
        初始化冲突分析器
        """
        # CHANGE: [2025-06-04] 完全移除字段标签映射逻辑，简化构造函数
        pass
    
    def analyze_dataframe(
        self, 
        df: pd.DataFrame, 
        field_mapping: Optional[Dict[str, str]] = None,
        import_session: Optional[ImportSession] = None,
        header_offset: int = 1,
        progress_update_interval: int = 10
    ) -> Tuple[List[Dict], Dict[str, int]]:
        """
        分析DataFrame中的冲突记录，并将详细冲突信息持久化到数据库。
        
        REFACTORED: [2025-06-19] 此方法现在直接将冲突持久化到数据库，
        并返回一个统计字典，不再返回冲突记录列表。
        """
        logger.info(f"[Analyzer] 开始分析Excel数据冲突，总行数: {len(df)}")
        
        if not import_session:
            logger.error("[Analyzer] analyze_dataframe方法必须接收一个有效的ImportSession实例。")
            return [], {"total": len(df), "new": 0, "identical": 0, "update": 0, "error": len(df) if df is not None else 0}
        
        # 首先将状态设置为ANALYSIS_START
        import_session.progress = 5.0
        import_session.status = ImportSessionStatus.ANALYSIS_START
        import_session.save(update_fields=['progress', 'status', 'updated_at'])
        logger.info(f"[Analyzer] 会话 {import_session.session_id} 设置初始进度: 5%, 状态: ANALYSIS_START")

        # 然后转换到ANALYSIS_IN_PROGRESS状态
        import_session.status = ImportSessionStatus.ANALYSIS_IN_PROGRESS
        import_session.save(update_fields=['status', 'updated_at'])
        logger.info(f"[Analyzer] 会话 {import_session.session_id} 状态更新为: ANALYSIS_IN_PROGRESS")
        
        stats = {
            "total": len(df),
            "new": 0,
            "identical": 0,
            "update": 0,
            "error": 0
        }
        
        import_session.record_count = len(df)
        import_session.save(update_fields=['record_count', 'updated_at'])
            
        commission_numbers = []
        start_time = datetime.now()
        last_progress_update_time = start_time
        
        logger.info("[Analyzer] 开始从DataFrame中提取委托编号")
        
        import_session.progress = 8.0
        import_session.save(update_fields=['progress', 'updated_at'])
        logger.info(f"[Analyzer] 会话 {import_session.session_id} 委托编号提取开始，设置进度: 8%")
        
        for idx, row in df.iterrows():
            commission_number = self._get_commission_number(row, field_mapping)
            if commission_number:
                commission_numbers.append(commission_number)
                
            if idx % 100 == 0:
                current_time = datetime.now()
                if (current_time - last_progress_update_time).total_seconds() >= 0.5:
                    progress_percentage = 8.0 + (idx / len(df)) * 2.0
                    import_session.progress = min(10.0, round(progress_percentage, 1))
                    import_session.save(update_fields=['progress', 'updated_at'])
                    last_progress_update_time = current_time
                    logger.info(f"[Analyzer] 会话 {import_session.session_id} 委托编号提取进度: {idx}/{len(df)} ({import_session.progress}%)")
        
        commission_numbers = list(set(commission_numbers))
        logger.info(f"[Analyzer] 从Excel中提取了 {len(commission_numbers)} 个唯一委托编号")
        
        import_session.progress = 10.0
        import_session.save(update_fields=['progress', 'updated_at'])
        logger.info(f"[Analyzer] 会话 {import_session.session_id} 委托编号提取完成，设置进度: 10%")
        
        existing_records_map = {}
        query_success = False
        try:
            with performance_tracker("[Analyzer] 查询现有记录", logger):
                if commission_numbers:
                    batch_size = 200
                    total_batches = (len(commission_numbers) + batch_size - 1) // batch_size
                    logger.info(f"[Analyzer] 开始批量查询数据库中的现有记录，总批次: {total_batches}")
                    batch_query_errors = 0
                    for batch_idx in range(total_batches):
                        try:
                            batch_start = batch_idx * batch_size
                            batch_end = min((batch_idx + 1) * batch_size, len(commission_numbers))
                            batch_commission_numbers = commission_numbers[batch_start:batch_end]
                            batch_records_qs = ArchiveRecord.objects.filter(commission_number__in=batch_commission_numbers)
                            
                            batch_progress = (batch_idx + 1) / total_batches
                            progress_percentage = 10.0 + batch_progress * 10.0
                            import_session.progress = min(20.0, round(progress_percentage, 1))
                            import_session.save(update_fields=['progress', 'updated_at'])
                            logger.info(f"[Analyzer] 会话 {import_session.session_id} 批量查询进度: 批次 {batch_idx+1}/{total_batches} ({import_session.progress}%)")
                            
                            for record_from_db in batch_records_qs:
                                commission_num_norm = self._normalize_commission_number(record_from_db.commission_number)
                                existing_records_map[commission_num_norm] = record_from_db
                            time.sleep(0.05)
                        except Exception as e_batch_query:
                            batch_query_errors += 1
                            logger.error(f"[Analyzer] 批次 {batch_idx+1}/{total_batches} 查询出错: {str(e_batch_query)}")
                            batch_progress = (batch_idx + 1) / total_batches
                            progress_percentage = 10.0 + batch_progress * 10.0
                            import_session.progress = min(20.0, round(progress_percentage, 1))
                            import_session.save(update_fields=['progress', 'updated_at'])
                    if batch_query_errors < total_batches or total_batches == 0:
                        query_success = True
                    logger.info(f"[Analyzer] 在数据库中找到 {len(existing_records_map)} 条匹配的记录")
                    try:
                        # 进行大小写不敏感和宽松匹配，找出可能的匹配
                        if len(existing_records_map) < len(commission_numbers):
                            logger.info("开始进行宽松匹配以查找更多可能的匹配")
                            
                            # 创建现有记录的大小写不敏感字典
                            case_insensitive_records = {
                                commission.lower(): record
                                for commission, record in existing_records_map.items()
                                if commission
                            }
                            
                            # 查找未匹配的委托编号
                            unmatched_commissions = [
                                comm for comm in commission_numbers
                                if comm and comm not in existing_records_map
                            ]
                            
                            # 尝试大小写不敏感匹配
                            for comm in unmatched_commissions:
                                if comm.lower() in case_insensitive_records:
                                    # 找到大小写不敏感匹配
                                    existing_records_map[comm] = case_insensitive_records[comm.lower()]
                            
                            logger.info(f"宽松匹配后，共找到 {len(existing_records_map)} 条匹配的记录")
                    except Exception as e:
                        logger.error(f"宽松匹配过程出错: {str(e)}")
        except Exception as e_query_all:
            logger.error(f"[Analyzer] 整个批量查询过程失败: {str(e_query_all)}")

        import_session.progress = 25.0
        import_session.save(update_fields=['progress', 'updated_at'])
        logger.info(f"[Analyzer] 会话 {import_session.session_id} 批量查询和宽松匹配完成，设置进度: 25%, 找到匹配记录: {len(existing_records_map)}")
        
        total_records_df = len(df)
        analyzed_count = 0
        last_progress_update_time = datetime.now()
        conflicts_to_create = []
        field_diffs_to_create = []
        row_analysis_errors = 0
        
        logger.info(f"[Analyzer] 开始分析每行记录并持久化冲突详情，总行数: {total_records_df}")
        
        with performance_tracker("[Analyzer] 逐行分析冲突", logger):
            for idx, row in df.iterrows():
                commission_number = self._get_commission_number(row, field_mapping)
                excel_row_num = idx + header_offset + 1

                if not commission_number:
                    stats["error"] += 1
                    logger.warning(f"Excel行 {excel_row_num}: 无法找到委托编号，跳过此行。")
                    continue

                record = existing_records_map.get(commission_number)
                
                if record:
                    # 记录存在，比较字段差异
                    differences = self._compare_fields(row, record, field_mapping)
                    
                    if differences:
                        # 有差异 -> update
                        conflict_type = "update"
                        stats["update"] += 1
                    else:
                        # 无差异 -> identical
                        conflict_type = "identical"
                        stats["identical"] += 1
                    
                    conflict_detail = ImportConflictDetail(
                        session=import_session,
                        commission_number=commission_number,
                        excel_row_number=excel_row_num,
                        existing_record_pk=record.pk,
                        conflict_type=conflict_type
                    )
                    conflicts_to_create.append(conflict_detail)

                    for diff in differences:
                        diff.conflict_detail = conflict_detail # 关联回主对象
                        field_diffs_to_create.append(diff)

                else:
                    # 记录不存在 -> new
                    stats["new"] += 1
                    conflict_detail = ImportConflictDetail(
                        session=import_session,
                        commission_number=commission_number,
                        excel_row_number=excel_row_num,
                        existing_record_pk=None,
                        conflict_type="new"
                    )
                    conflicts_to_create.append(conflict_detail)

                # 更新进度
                if idx % progress_update_interval == 0:
                    progress = 20.0 + (idx / total_records_df) * 70.0
                    import_session.progress = min(90.0, round(progress, 1))
                    import_session.save(update_fields=['progress', 'updated_at'])

        # 批量将冲突写入数据库
        with performance_tracker("[Analyzer] 批量保存冲突到数据库", logger):
            try:
                with transaction.atomic():
                    # CHANGE: [2025-06-20] 修复批量创建外键关联问题
                    # 1. 先批量创建主冲突记录
                    created_conflicts = ImportConflictDetail.objects.bulk_create(
                        conflicts_to_create, 
                        batch_size=500
                    )
                    logger.info(f"[Analyzer] 成功批量创建 {len(created_conflicts)} 条冲突记录")
                    
                    # 2. 重新查询刚创建的冲突记录以获取数据库分配的ID
                    # 使用session和commission_number的组合来匹配
                    conflict_lookup = {}
                    created_conflicts_qs = ImportConflictDetail.objects.filter(
                        session=import_session
                    ).select_related('session')
                    
                    for conflict in created_conflicts_qs:
                        key = f"{conflict.commission_number}-{conflict.excel_row_number}"
                        conflict_lookup[key] = conflict
                    
                    # 3. 更新字段差异记录的外键关联
                    valid_field_diffs = []
                    for diff in field_diffs_to_create:
                        # 通过原始的conflict_detail对象获取匹配键
                        original_conflict = diff.conflict_detail
                        key = f"{original_conflict.commission_number}-{original_conflict.excel_row_number}"
                        
                        if key in conflict_lookup:
                            # 更新为数据库中的实际对象
                            diff.conflict_detail = conflict_lookup[key]
                            valid_field_diffs.append(diff)
                        else:
                            logger.warning(f"[Analyzer] 找不到匹配的冲突记录: {key}")
                    
                    # 4. 批量创建字段差异记录
                    if valid_field_diffs:
                        created_diffs = ImportFieldDifference.objects.bulk_create(
                            valid_field_diffs, 
                            batch_size=500
                        )
                        logger.info(f"[Analyzer] 成功批量创建 {len(created_diffs)} 条字段差异记录")
                    else:
                        logger.warning("[Analyzer] 没有有效的字段差异记录需要创建")
                    
            except Exception as e:
                logger.error(f"[Analyzer] 批量保存冲突时发生严重错误: {e}", exc_info=True)
                stats['error'] += len(conflicts_to_create) # 假设所有待创建的都失败了
                # 在这里可以添加回滚或错误处理逻辑
        
        import_session.conflict_count = stats["update"] + stats["new"]
        import_session.progress = 95.0
        import_session.save(update_fields=['conflict_count', 'progress', 'updated_at'])
        
        logger.info(f"[Analyzer] 冲突分析完成. 统计: {stats}")
        # 返回空列表和统计数据，因为冲突已直接写入DB
        return [], stats
    
    def _normalize_commission_number(self, commission_number: Any) -> str:
        """
        规范化委托编号以确保一致比较
        
        Args:
            commission_number: 原始委托编号
            
        Returns:
            规范化后的委托编号
        """
        if commission_number is None:
            return ""
            
        # 转为字符串
        comm_str = str(commission_number)
        
        # 去除前后空格
        return comm_str.strip()
    
    def _get_commission_number(
        self, 
        row: pd.Series, 
        field_mapping: Optional[Dict[str, str]] = None
    ) -> Optional[str]:
        """
        从行数据中获取委托编号
        
        Args:
            row: DataFrame行
            field_mapping: 字段映射
        
        Returns:
            委托编号或None
        """
        # 尝试直接通过字段名获取
        if "commission_number" in row:
            commission_number = row["commission_number"]
        # 尝试通过中文字段名获取
        elif "委托编号" in row:
            commission_number = row["委托编号"]
        # 如果提供了字段映射，尝试通过映射获取
        elif field_mapping:
            for excel_col, model_field in field_mapping.items():
                if model_field == "commission_number" and excel_col in row:
                    commission_number = row[excel_col]
                    break
            else:
                return None
        else:
            return None
        
        # 对委托编号进行规范化处理
        if commission_number is not None:
            # 转为字符串并去除前后空格
            return str(commission_number).strip()
        
        return None
    
    def _compare_fields(
        self, 
        row: pd.Series, 
        record: ArchiveRecord,
        field_mapping: Optional[Dict[str, str]] = None
    ) -> List[ImportFieldDifference]:
        """
        比较一行数据和一个记录实例之间的字段差异。
        
        现在此方法返回 ImportFieldDifference 模型实例列表 (未保存到数据库)。
        """
        differences = []
        fields_to_compare = self._get_fields_to_compare(row, record, field_mapping)

        for field_name, excel_col_name in fields_to_compare.items():
            excel_value = row.get(excel_col_name)
            db_value = getattr(record, field_name, None)

            if not self._values_equal(field_name, excel_value, db_value):
                # 创建ImportFieldDifference实例，但不关联conflict_detail，
                # 因为它将在上层批量创建时被关联。
                diff = ImportFieldDifference(
                    field_name=field_name,
                    existing_value=self._format_for_json(db_value),
                    imported_value=self._format_for_json(excel_value)
                )
                differences.append(diff)
        
        return differences
    
    def _get_fields_to_compare(
        self, 
        row: pd.Series, 
        record: ArchiveRecord,
        field_mapping: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """
        获取需要比较的字段映射
        
        Args:
            row: DataFrame行
            record: 现有记录对象
            field_mapping: 字段映射
        
        Returns:
            模型字段到Excel字段的映射字典 {model_field: excel_field}
        """
        result = {}
        
        # 如果提供了字段映射，使用它进行映射
        if field_mapping:
            for excel_field, model_field in field_mapping.items():
                if excel_field in row.index and hasattr(record, model_field):
                    result[model_field] = excel_field
            return result
        
        # CHANGE: [2025-06-04] 完全移除DEFAULT_FIELD_LABELS反向映射，只保留直接匹配
        # 没有提供映射，只使用直接匹配同名字段
        model_fields = {f.name for f in record._meta.fields}
        
        # 直接匹配同名字段
        for field in model_fields:
            if field in row.index:
                result[field] = field
        
        return result
    
    def _values_equal(self, field_name: str, value1: Any, value2: Any) -> bool:
        """
        比较两个值是否相等，根据字段类型采用不同的比较策略
        
        Args:
            value1: Excel值
            value2: 数据库值
        """
        # 处理空值情况
        if pd.isna(value1) and pd.isna(value2):
            return True
        if pd.isna(value1) or pd.isna(value2):
            return False
            
        # 日期字段特殊处理
        if field_name in self.DATE_FIELDS:
            # CHANGE: [2025-06-20] 移除双重处理问题，直接使用TimezoneHandler的比较方法
            # TimezoneHandler.compare_datetime_values会自行处理Excel时间解析
            logger.info(f"[分析阶段-时间比较] 字段: {field_name}")
            logger.info(f"[分析阶段-时间比较] Excel值: {value1}, 数据库值: {value2}")
            
            result = TimezoneHandler.compare_datetime_values(value1, value2, field_name)
            logger.info(f"[分析阶段-时间比较] 比较结果: {'相等' if result else '不相等'}")
            return result
        
        # 数值字段特殊处理
        if field_name in self.NUMERIC_FIELDS:
            return self._compare_numbers(value1, value2)
            
        # 字符串比较，忽略首尾空格
        if isinstance(value1, str) and isinstance(value2, str):
            return value1.strip() == value2.strip()
            
        # 默认比较
        return value1 == value2
    
    def _compare_numbers(self, num1: Any, num2: Any) -> bool:
        """
        比较数值是否相等
        
        Args:
            num1: 第一个数值
            num2: 第二个数值
        
        Returns:
            两个数值是否相等
        """
        try:
            # 尝试转换为浮点数
            float1 = float(num1) if num1 is not None else 0
            float2 = float(num2) if num2 is not None else 0
            
            # 允许小额差异
            return abs(float1 - float2) < 0.001
        except (ValueError, TypeError):
            # 无法转换为数值，直接比较
            return str(num1) == str(num2)

    def _format_for_json(self, value: Any) -> Any:
        """格式化值以便安全地存入JSONField"""
        if isinstance(value, (datetime, dt.date)):
            return value.isoformat()
        if isinstance(value, (np.int64, np.int32)):
            return int(value)
        if isinstance(value, (np.float64, np.float32)):
            return float(value)
        # CHANGE: [2025-06-20] 修复Decimal类型JSON序列化问题
        from decimal import Decimal
        if isinstance(value, Decimal):
            return float(value)
        if pd.isna(value):
            return None
        return value 