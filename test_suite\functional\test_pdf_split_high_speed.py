#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PDF高速分割与信息提取功能测试 (使用新服务架构)

此脚本用于测试 archive_processing.services.PdfProcessingService 及相关工具函数
的功能正确性和基本性能。

它会处理指定的PDF文件，提取分割信息和统一编号，模拟物理分割到临时目录，
并生成一个包含处理结果和统计信息的摘要文件。

# 命令行参数:
python test_suite/functional/test_pdf_split_high_speed.py

# 实际运行命令:
python test_suite/functional/test_pdf_split_high_speed.py

"""

import os
import sys
import time
import logging
import tempfile # 用于创建临时目录
from typing import List, Optional, Dict, Tuple

# 更可靠地添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)
print(f"添加项目路径: {project_root}")

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')
print(f"设置 Django 设置模块: archive_flow_manager.settings")

# 尝试设置 Django
try:
    import django
    django.setup()
    print("Django 环境设置成功")
except Exception as e:
    print(f"Django 环境设置失败: {e}")
    print(f"当前 sys.path: {sys.path}")
    sys.exit(1)

# 设置 KMP_DUPLICATE_LIB_OK
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 导入处理函数和相关类
try:
    # 导入新的服务和工具
    from archive_processing.services.pdf_processing_service import PdfProcessingService
    from archive_processing.dto.processing_dtos import ProcessingResultDto # 导入 DTO
    from archive_processing.utils import pdf_utils, processing_report_utils, system_utils
    from archive_processing.utils.ocr_utils import init_paddle_ocr
    print("成功导入新的 PDF 处理服务和工具模块")
except ImportError as e:
    print(f"导入新的 PDF 处理模块失败: {e}")
    sys.exit(1)

# 设置日志
# 将根日志级别设置为 INFO，避免过多无关日志
# 然后为我们关心的特定模块启用 DEBUG 级别
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# 获取并设置特定模块的日志级别为 DEBUG
# 这将捕获 ocr_utils, pdf_processing_service 等所有相关模块的日志
logging.getLogger('archive_processing').setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)
logger.info("已为 'archive_processing' 模块启用 DEBUG 级日志记录。")

# --- 测试配置 ---
# 固定测试文件路径
TEST_PDF_PATH = os.path.abspath(os.path.join(project_root, 'test_suite', 'test_files', 'pdf', 'test_excel_import_and_pdf_processing.pdf'))
# 固定目标文本 (根据需要修改)
TARGET_TEXT = "代合同"
# 固定输出目录 (用于存放摘要文件和临时分割文件)
OUTPUT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'output', 'functional_test')) # 调整目录名
# --- 结束配置 ---

# 移除旧的本地 create_summary_report 函数

def main():
    # 定义传递给 processor 的配置参数
    processor_config = {
        'use_paddle_ocr': True, # 默认使用 Paddle (如果可用)
        'dpi': 150, 
        'crop_ratio': 0.2,
        'batch_size': 100,
        'case_sensitive': False,
        'ignore_punctuation': True,
        'enable_cache': True
    }

    logger.info("=" * 60)
    logger.info(f"开始 PDF 处理功能测试 (使用新服务)")
    logger.info(f"测试文件: {TEST_PDF_PATH}")
    logger.info(f"目标文本: '{TARGET_TEXT}'")
    logger.info(f"使用PaddleOCR: {'是' if processor_config.get('use_paddle_ocr') else '否'}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info("-" * 60)
    
    # 检查测试文件是否存在
    if not os.path.exists(TEST_PDF_PATH):
        logger.error(f"错误: 测试文件不存在 {TEST_PDF_PATH}")
        sys.exit(1)

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # --- 执行处理 --- 
    pdf_service = PdfProcessingService(config=processor_config)
    archived_files: List[Tuple[Optional[str], Optional[str]]] = []
    summary_path: Optional[str] = None
    result_dto: Optional[ProcessingResultDto] = None
    temp_dir_obj = None # 用于临时文件
    
    try:
        start_time = time.time()
        
        # 1. 调用服务获取信息
        logger.info("调用 PdfProcessingService 获取信息...")
        result_dto = pdf_service.process_pdf_for_splitting_info(
            pdf_path=TEST_PDF_PATH,
            target_text=TARGET_TEXT
        )
        info_extraction_time = time.time() - start_time
        logger.info(f"信息提取完成，耗时: {system_utils.format_time(info_extraction_time)}")
        
        assert result_dto is not None, "处理服务返回了 None"
        assert result_dto.success, f"信息提取失败: {result_dto.error_message}"
        assert result_dto.stats is not None, "返回结果缺少统计信息"
        assert result_dto.stats.total_pages > 0, "未能正确获取总页数"
        # 根据测试文件预期添加断言 (需要根据实际测试文件调整)
        logger.info(f"测试文件预期: 找到分割点和统一编号")
        assert len(result_dto.split_points) > 0, "断言失败：未能找到任何分割点"
        assert len(result_dto.unified_numbers) > 0, "断言失败：未能识别任何统一编号"
        logger.info(f"信息提取成功，找到 {len(result_dto.split_points)} 个分割点，识别 {len(result_dto.unified_numbers)} 个编号。")

        # 2. 模拟物理分割与归档
        if result_dto.success:
            logger.info("开始模拟物理分割和归档...")
            # 使用 context manager 确保临时目录被清理
            with tempfile.TemporaryDirectory(prefix="pdf_split_test_", dir=OUTPUT_DIR) as temp_dir:
                temp_dir_obj = temp_dir # Store path for logging cleanup
                logger.info(f"创建临时目录: {temp_dir}")
                
                part_ranges = pdf_utils.calculate_part_ranges(
                    split_points=result_dto.split_points, 
                    total_pages=result_dto.stats.total_pages
                )
                logger.info(f"计算得到 {len(part_ranges)} 个文件部分范围。")
                
                for i, part_range in enumerate(part_ranges):
                    unified_number = result_dto.unified_numbers.get(part_range.start_page)
                    # 模拟最终归档路径（实际应由 FileStorageService 生成）
                    # 文件名包含页码和可能的编号，便于调试
                    temp_output_filename = f"part_{i+1}_pages_{part_range.start_page+1}-{part_range.end_page+1}{'_num_' + unified_number if unified_number else ''}.pdf"
                    temp_output_path = os.path.join(temp_dir, temp_output_filename)
                    
                    logger.debug(f"尝试写入部分 {i+1}: 页 {part_range.start_page+1}-{part_range.end_page+1} 到 {temp_output_path}")
                    write_success = pdf_utils.create_temp_pdf_for_single_archive(
                        original_pdf_path=TEST_PDF_PATH,
                        start_page=part_range.start_page + 1,
                        end_page=part_range.end_page + 1,
                        output_path=temp_output_path
                    )
                    
                    if write_success:
                         logger.info(f"成功写入临时文件: {temp_output_filename}")
                         # 模拟归档成功，实际应调用 FileStorageService.archive_single_archive_pdf
                         # 注意：这里我们将临时路径添加到列表，因为文件会在with语句结束时被删除
                         # 在真实场景中，这里应该是归档后的最终路径
                         archived_files.append((temp_output_path, unified_number))
                    else:
                         logger.error(f"写入部分 {i+1} 到临时文件失败！")
                         archived_files.append((None, unified_number)) # 记录写入失败
                
                splitting_time = time.time() - start_time - info_extraction_time
                logger.info(f"模拟分割完成，耗时: {system_utils.format_time(splitting_time)}")
                assert len(archived_files) == len(part_ranges), "归档文件列表长度与计算的范围不匹配"
                # 检查是否有写入失败的情况
                write_failures = sum(1 for path, _ in archived_files if path is None)
                assert write_failures == 0, f"检测到 {write_failures} 个文件部分写入失败"

            # 临时目录在此处已被自动清理
            temp_dir_obj = None 
        else:
             logger.warning("信息提取未成功，跳过物理分割模拟。")

        # 3. 生成摘要报告
        logger.info("生成摘要报告...")
        # 注意：archived_files 包含的是临时文件路径，这些文件可能已被删除
        # create_result_summary 函数需要能够处理路径不存在的情况
        summary_path = processing_report_utils.create_result_summary(
            input_pdf_path=TEST_PDF_PATH,
            result_dto=result_dto, # 传递 DTO
            archived_files=archived_files, # 传递包含临时路径的结果
            output_dir=OUTPUT_DIR,
            status_update=None # 暂时不测试数据库更新
        )
        assert summary_path is not None and os.path.exists(summary_path), "未能成功创建摘要文件"
        logger.info(f"摘要报告已生成: {summary_path}")

        total_time = time.time() - start_time
        logger.info("-" * 60)
        logger.info(f"测试成功完成！总耗时: {system_utils.format_time(total_time)}")
        logger.info("=" * 60)

    except AssertionError as ae:
        logger.error(f"测试断言失败: {ae}", exc_info=True)
        logger.info("=" * 60)
        sys.exit(1) # 测试失败退出
    except Exception as e:
        logger.error(f"测试执行过程中发生意外错误: {e}", exc_info=True)
        logger.info("=" * 60)
        sys.exit(1) # 测试失败退出
    # finally block removed as tempfile.TemporaryDirectory handles cleanup

def test_official_paddle_image():
    """
    一个独立的测试用例，用于下载官方示例图片并使用我们项目中的OCR工具进行识别。
    这能验证我们的 OCR 工具链本身对于一个已知能成功的图片是否工作正常。
    """
    print("\n" + "="*60)
    print("🚀 开始运行【官方示例图片】专项测试...")
    print("="*60)

    # 1. 初始化我们项目中的 PaddleOCR 引擎
    print("1. 正在使用 ocr_utils.init_paddle_ocr() 初始化引擎...")
    paddle_engine = init_paddle_ocr()
    if not paddle_engine:
        print("❗ 引擎初始化失败，测试中止。")
        return

    print("✅ 引擎初始化成功。")

    # 2. 下载官方图片
    try:
        import requests
        from PIL import Image
        import io

        image_url = "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
        print(f"2. 正在从以下 URL 下载图片:\n   {image_url}")
        
        response = requests.get(image_url, timeout=20)
        response.raise_for_status()  # 如果下载失败则抛出异常
        
        image = Image.open(io.BytesIO(response.content))
        print("✅ 图片下载并加载成功。")

    except Exception as e:
        print(f"❗ 图片下载失败: {e}")
        return

    # 3. 使用微服务进行OCR识别
    print("3. 正在使用微服务进行OCR识别...")
    if paddle_engine:
        extracted_text = paddle_engine.recognize_basic(image)
    else:
        extracted_text = "OCR引擎未初始化"

    # 4. 打印结果
    print("\n" + "-"*60)
    print("✅ 识别完成！结果如下：")
    print(f"【识别出的文本】: {extracted_text}")
    print("-"*60)

if __name__ == "__main__":
    # setup_django_env()  # 专项测试不依赖Django环境，可以注释掉
    main()              # 注释掉主测试流程，以进行独立测试
    
    # 只运行官方图片专项测试
    test_official_paddle_image()
