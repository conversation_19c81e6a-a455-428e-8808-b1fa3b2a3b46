"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Package, ArrowUpRight, ArrowDownRight, TrendingUp, BarChart3 } from "lucide-react"

export function ArchiveBoxStatistics() {
  const [period, setPeriod] = useState("daily")

  // 模拟每日档案盒数据
  const dailyBoxData = [
    { id: "ARC-2023-001", count: 15, trend: "up", percentage: 12 },
    { id: "ARC-2023-002", count: 12, trend: "up", percentage: 8 },
    { id: "ARC-2023-003", count: 8, trend: "down", percentage: 5 },
    { id: "ARC-2023-004", count: 7, trend: "up", percentage: 3 },
  ]

  // 模拟每月档案盒数据
  const monthlyBoxData = [
    { id: "ARC-2023-001", count: 156, trend: "up", percentage: 15 },
    { id: "ARC-2023-002", count: 142, trend: "up", percentage: 12 },
    { id: "ARC-2023-003", count: 98, trend: "down", percentage: 8 },
    { id: "ARC-2023-004", count: 91, trend: "up", percentage: 7 },
  ]

  return (
    <div>
      <Tabs value={period} onValueChange={setPeriod}>
        <TabsList className="mb-4">
          <TabsTrigger value="daily">每日档案盒统计</TabsTrigger>
          <TabsTrigger value="monthly">每月档案盒统计</TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">今日档案盒数</p>
                    <p className="text-2xl font-bold">15</p>
                  </div>
                  <Package className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">平均文件数/盒</p>
                    <p className="text-2xl font-bold">28.4</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">较昨日变化</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold text-green-600">+3</p>
                      <ArrowUpRight className="ml-1 h-5 w-5 text-green-600" />
                    </div>
                  </div>
                  <TrendingUp className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">盒容量利用率</p>
                    <p className="text-2xl font-bold text-blue-600">85.2%</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">85%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>档案盒号</TableHead>
                  <TableHead>归档文件数</TableHead>
                  <TableHead>变化趋势</TableHead>
                  <TableHead>占比</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dailyBoxData.map((box) => (
                  <TableRow key={box.id}>
                    <TableCell className="font-medium">{box.id}</TableCell>
                    <TableCell>{box.count}</TableCell>
                    <TableCell>
                      {box.trend === "up" ? (
                        <Badge className="bg-green-100 text-green-800">
                          <ArrowUpRight className="mr-1 h-3 w-3" />
                          上升
                        </Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800">
                          <ArrowDownRight className="mr-1 h-3 w-3" />
                          下降
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>{box.percentage}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <Button variant="outline" size="sm">
              查看完整统计
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="monthly" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">本月档案盒数</p>
                    <p className="text-2xl font-bold">42</p>
                  </div>
                  <Package className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">平均文件数/盒</p>
                    <p className="text-2xl font-bold">30.6</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">较上月变化</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold text-green-600">+8</p>
                      <ArrowUpRight className="ml-1 h-5 w-5 text-green-600" />
                    </div>
                  </div>
                  <TrendingUp className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">盒容量利用率</p>
                    <p className="text-2xl font-bold text-blue-600">86.4%</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">86%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>档案盒号</TableHead>
                  <TableHead>归档文件数</TableHead>
                  <TableHead>变化趋势</TableHead>
                  <TableHead>占比</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {monthlyBoxData.map((box) => (
                  <TableRow key={box.id}>
                    <TableCell className="font-medium">{box.id}</TableCell>
                    <TableCell>{box.count}</TableCell>
                    <TableCell>
                      {box.trend === "up" ? (
                        <Badge className="bg-green-100 text-green-800">
                          <ArrowUpRight className="mr-1 h-3 w-3" />
                          上升
                        </Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800">
                          <ArrowDownRight className="mr-1 h-3 w-3" />
                          下降
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>{box.percentage}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <Button variant="outline" size="sm">
              查看完整统计
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
