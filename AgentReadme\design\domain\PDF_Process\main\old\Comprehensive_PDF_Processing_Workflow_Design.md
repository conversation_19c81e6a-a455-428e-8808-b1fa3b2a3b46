# 综合PDF处理与归档工作流设计文档 (V6 - Final)

## 1. 概述与核心原则

本文档是对现有 `PDF_Processing_Frontend_and_API_Design.md` 的补充和最终细化，旨在定义一个**健壮、可追踪、且数据一致**的端到端PDF处理工作流。它整合了多轮讨论的最终共识，是后续所有开发工作的核心指导蓝图。

### 1.1 核心原则

- **数据原子性**: 最终的业务核心数据 (`ArchiveRecord`) 必须在所有处理步骤都成功完成后，以原子操作的形式一次性写入数据库。在此之前，业务数据表中不应存在"半成品"或"中间状态"的记录。
- **进度可追踪**: 尽管业务数据是最后才写入，但处理的全过程必须是可监控的。系统应能清晰地展示每个独立文件在各个处理阶段的实时状态。
- **职责单一与解耦**: 每个服务和任务都有其明确且单一的职责。文件处理、状态更新、流程调度等逻辑应相互解耦，便于维护和独立重试。

## 2. 系统总体工作流 (最终方案)

我们采纳**"任务作为状态追踪器，最后原子化入库"**的设计方案。

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API as PDFUploadView
    participant UploadSvc as UploadService
    participant TaskSvc as TaskService
    participant Dispatcher as dispatch_pdf_processing
    participant Celery
    participant DB as 数据库

    User->>Frontend: 1. 上传PDF (含参数 use_parallel, chunk_size等)
    Frontend->>API: 2. POST /api/upload-pdf/
    API->>UploadSvc: 3. 保存文件，创建 UploadedFile 记录
    UploadSvc-->>API: 4. 返回 UploadedFile 实例
    API->>TaskSvc: 5. 创建 ProcessingTask (关联UploadedFile, 存入processing_params, 状态: queued)
    TaskSvc-->>API: 6. 返回 ProcessingTask 实例
    API->>Dispatcher: 7. 根据 use_parallel 参数分发任务
    Dispatcher->>Celery: 8. process_pdf_serial_task.delay(task_id)
    API-->>Frontend: 9. 返回成功响应 (含task_id)

    Note over Celery,DB: == 第一阶段：档案拆分 ==
    Celery->>DB: 10. WorkerA 获取任务，更新 ProcessingTask 状态为 'processing'
    Celery->>Celery: 11. 执行PDF拆分与文件归档 (不操作ArchiveRecord)
    Celery->>DB: 12. 任务完成，更新 ProcessingTask 状态为 'completed'，并将结果写入 result_data

    Note over Celery,DB: == 阶段切换调度 ==
    Celery->>DB: 13. (定时任务) trigger_report_splitting_task 查找状态为 'completed' 的 ProcessingTask
    Celery->>DB: 14. 遍历 result_data, 为每个拆分出的档案创建 ReportSplittingTask (状态: pending)
    Celery->>DB: 15. 更新 ProcessingTask 状态为 'phase_2_triggered'

    Note over Celery,DB: == 第二阶段：报告分割 ==
    Celery->>DB: 16. WorkerB 获取 ReportSplittingTask, 更新其状态为 'processing'
    Celery->>Celery: 17. 执行报告分割与文件归档
    Celery->>DB: 18. 任务完成，更新 ReportSplittingTask 状态为 'completed'

    Note over Celery,DB: == 最终入库调度 (原子操作) ==
    Celery->>DB: 19. (定时任务) finalize_records_task 查找所有子任务都完成的 ProcessingTask
    Celery->>DB: 20. **加锁**并更新 ProcessingTask 状态为 'finalizing' (防止并发)
    Celery->>DB: 21. **在一个事务中**, 批量创建所有 ArchiveRecord，并关联所有外键和URL
    Celery->>DB: 22. 更新 ProcessingTask 状态为 'finalized'
```

## 3. 详细设计与实施计划

### 3.1 数据层 (Data Layer)

**目标**: 建立支持上述工作流的数据库模型。

- **任务 3.1.1: `ArchiveRecord` 模型 (`archive_records/models.py`)**
  - **字段**:
    - `id`: 主键
    - `source_file`: `ForeignKey` to `UploadedFile`, `null=True`, `blank=True`. **最终关联**。
    - `archive_url`: `URLField`, 存储第一阶段产出的档案PDF链接。
    - `report_url`: `URLField`, 存储第二阶段产出的报告PDF链接。
    - `archive_status`: `CharField`, `null=True`, `db_index=True`。**此字段不再使用枚举**，以兼容Excel导入等多种数据源可能带来的不同状态字符串。PDF处理流程只在最终入库时将其设置为 `'archived'`。
    - 其他业务字段...

- **任务 3.1.2: `UploadedFile` 模型 (`archive_processing/models.py`)**
  - **字段**:
    - `file_id`: 主键 (UUID)
    - `archive_box_number`: `CharField`, 存储用户上传时指定的盒号。
    - **新增 `status` 字段**: `CharField` with `choices` (`active`, `deleted`, 预留`superseded`)，`default='active'`，用于软删除和状态管理。
    - 其他文件元信息...

- **任务 3.1.3: `ProcessingTask` 模型 (`archive_processing/models.py`)**
  - **`status` 字段 `choices` 扩展**:
    - `'queued'`: 已入队
    - `'processing'`: (第一阶段)档案拆分中
    - `'completed'`: (第一阶段)档案拆分完成
    - `'phase_2_triggered'`: (切换)已创建报告拆分任务
    - `'finalizing'`: (第三阶段)最终入库中
    - `'finalized'`: (第三阶段)最终入库完成
    - `'failed'`: 失败
  - **`result_data` 字段**: `JSONField`, 用于存储第一阶段成功拆分出的 `[{'unified_number': ..., 'archive_path': ...}]` 列表。

- **任务 3.1.4: `ReportSplittingTask` 模型 (`archive_processing/models.py`)**
  - **字段**:
    - `id`: 主键
    - `processing_task`: `ForeignKey` to `ProcessingTask` (建立主任务和子任务的关联)。
    - `unified_number`: `CharField`, 标识此任务对应的档案。
    - `source_pdf_path`: `CharField`, 第一阶段产出的档案PDF路径。
    - `status`: `CharField` (`pending`, `processing`, `completed`, `failed`)。
    - `output_pdf_path`: `CharField`, 第二阶段产出的报告PDF路径。
    - `error_message`: `TextField`。

### 3.2 服务与任务层 (Service & Task Layer)

- **任务 3.2.1: 清理 `record_update_service.py`**
  - **动作**: 移除或完全重构 `update_archive_record` 函数。它的新职责将是在**最终入库**时被调用，以原子方式创建记录。
  - **新函数签名 (建议)**: `create_finalized_records_from_task(processing_task: ProcessingTask)`

- **任务 3.2.2: 重构 `tasks.py` - 第一阶段 (`process_pdf_serial_task`)**
  - **核心职责**: 只负责文件处理。
  - **输入**: `task_id`。
  - **逻辑**:
    1. 获取 `ProcessingTask` 和 `UploadedFile`。
    2. 调用 `PdfProcessingService` 进行文件拆分和物理归档。
    3. **不与 `ArchiveRecord` 交互**。
    4. 成功后，将结果列表存入 `ProcessingTask.result_data`，并更新 `ProcessingTask.status` 为 `'completed'`。

- **任务 3.2.3: 新建 `tasks.py` - 阶段切换任务 (`trigger_report_splitting_task`)**
  - **核心职责**: 承上启下。
  - **触发方式**: Celery Beat 定时任务。
  - **逻辑**:
    1. 查询状态为 `'completed'` 的 `ProcessingTask`。
    2. 遍历其 `result_data`。
    3. 为每个条目创建 `ReportSplittingTask` 记录。
    4. 更新 `ProcessingTask.status` 为 `'phase_2_triggered'`。

- **任务 3.2.4: 新建 `tasks.py` - 第二阶段任务 (`process_report_splitting_task`)**
  - **核心职责**: 执行报告分割。
  - **输入**: `report_splitting_task_id`。
  - **逻辑**:
    1. 获取 `ReportSplittingTask`。
    2. 调用新的 `SealSplittingService` (或类似服务) 执行处理。
    3. 成功后，更新**自身**状态为 `'completed'`，并保存 `output_pdf_path`。

- **任务 3.2.5: 新建 `tasks.py` - 最终入库任务 (`finalize_records_task`)**
  - **核心职责**: 原子化写入最终业务数据。
  - **触发方式**: Celery Beat 定时任务。
  - **逻辑**:
    1. 查找所有子 `ReportSplittingTask` 都已完成的 `ProcessingTask`。
    2. **实现并发控制**: 使用 `select_for_update()` 和 `status='finalizing'` 状态锁。
    3. 调用重构后的 `record_update_service.create_finalized_records_from_task()`。
    4. 在一个数据库事务中，批量创建所有 `ArchiveRecord`，并完成所有字段的填充和外键关联。
    5. 更新 `ProcessingTask.status` 为 `'finalized'`。

- **任务 3.2.6 (新增): 创建后台文件清理任务 (`cleanup_abandoned_files_task`)**
  - **核心职责**: 定期或事件触发地清理与状态为 `deleted` 的 `UploadedFile` 记录相关联的所有物理文件。
  - **触发**: 由 `re-upload` API 成功后异步触发。
  - **逻辑**: 以 `UploadedFile` 为起点，追溯并删除其原始上传文件、所有第一阶段和第二阶段产出的文件。

### 3.3 API 与前端层 (API & Frontend Layer)

- **任务 3.3.1: 修复在位编辑 API (`PATCH /api/archive-records/{record_id}/`)**
  - **实现智能分发**:
    - 当检测到请求修改的是 `archiveBoxNumber` 时，通过 `record.source_file` 找到并更新 `UploadedFile.archive_box_number`。
    - 其他字段的编辑按原逻辑执行。

- **任务 3.3.2: 开发新的监控 API**
  - **`GET /api/uploaded-files/`**: 获取上传文件列表，作为Master行数据源。**默认仅查询 `status='active'` 的记录**。API内部会关联查询对应的`ProcessingTask`以获取实时状态和进度。
  - **`GET /api/processing-tasks/{task_id}/report-splitting-tasks/`**: 获取指定主任务下的所有第二阶段子任务列表 (Detail行数据)。
  - **`PATCH /api/uploaded-files/{file_id}/`**: 用于编辑档案盒号。
  - **`POST /api/tasks/{task_id}/retry/`**: 统一的智能重试API。后端根据任务ID判断其类型（第一阶段或第二阶段）并触发相应的重试流程。
  - **`POST /api/uploaded-files/{old_file_id}/re-upload/`**: **重新上传API**。用于修正一个处理失败的文件。后端将旧文件状态置为 `deleted` 并触发清理。

- **任务 3.3.3: 开发监控前端页面**
  - 适配新的API，使用Master-Detail表格展示两阶段的处理进度。

---
*文档版本 V6 (Final), 基于深度讨论后的最终方案制定*  
