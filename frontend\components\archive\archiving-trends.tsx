"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

export function ArchivingTrends() {
  const [metric, setMetric] = useState("volume")

  return (
    <div>
      <Tabs value={metric} onValueChange={setMetric}>
        <TabsList className="mb-4">
          <TabsTrigger value="volume">归档量</TabsTrigger>
          <TabsTrigger value="processing-time">处理时间</TabsTrigger>
          <TabsTrigger value="success-rate">成功率</TabsTrigger>
        </TabsList>
        <TabsContent value="volume" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">归档量趋势图表</p>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="processing-time" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">处理时间趋势图表</p>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="success-rate" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">成功率趋势图表</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
