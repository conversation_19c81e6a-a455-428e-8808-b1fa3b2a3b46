"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Lock, Unlock, CheckCircle2, Calendar, User, Edit, FileText, Trash, Send, Archive, Printer } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface ReportDistributionHeaderProps {
  report: any
  isNewReport: boolean
  lastSavedAt: string | null
  isSaving: boolean
  isProcessing: boolean
  canAutoSave: () => boolean
  onSaveAsDraft: () => void
  onLock: () => void
  onUnlock: () => void
  onSubmit: () => void
  onPrint: () => void
  onDelete: () => void
}

export function ReportDistributionHeader({
  report,
  isNewReport,
  lastSavedAt,
  isSaving,
  isProcessing,
  canAutoSave,
  onSaveAsDraft,
  onLock,
  onUnlock,
  onSubmit,
  onPrint,
  onDelete,
}: ReportDistributionHeaderProps) {
  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "creating":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            正在创建
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            草稿
          </Badge>
        )
      case "locked":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            已锁定
          </Badge>
        )
      case "issued":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            已发放
          </Badge>
        )
      case "deleted":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            已删除
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card>
      <CardContent className="py-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* 左侧：状态显示 */}
          <div className="flex items-center gap-2">
            <div className="text-lg font-medium">状态:</div>
            <div className="text-lg">{getStatusBadge(report.status)}</div>
            {report.status === "draft" && lastSavedAt && (
              <div className="text-sm text-muted-foreground flex items-center ml-2">
                <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                上次保存: {lastSavedAt}
              </div>
            )}
            {isSaving && (
              <div className="text-sm text-muted-foreground flex items-center ml-2">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-900 mr-1"></div>
                正在保存...
              </div>
            )}
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex flex-wrap gap-2">
            {/* 创建状态可以手动保存为草稿 */}
            {report.status === "creating" && canAutoSave() && (
              <Button onClick={onSaveAsDraft} disabled={isProcessing || isSaving}>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                保存为草稿
              </Button>
            )}

            {/* 草稿状态可以锁定或删除 */}
            {report.status === "draft" && (
              <>
                <Button onClick={onLock} disabled={isProcessing || isSaving}>
                  <Lock className="mr-2 h-4 w-4" />
                  锁定
                </Button>
                <Button variant="destructive" onClick={onDelete} disabled={isProcessing || isSaving}>
                  <Trash className="mr-2 h-4 w-4" />
                  删除发放单
                </Button>
              </>
            )}

            {/* 锁定状态可以解锁、发放或打印 */}
            {report.status === "locked" && (
              <div className="flex gap-2">
                {true && (
                  <Button
                    onClick={onUnlock}
                    disabled={isProcessing}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Lock className="h-5 w-5" />
                    解锁
                  </Button>
                )}
                {true && (
                  <Button
                    onClick={onSubmit}
                    disabled={isProcessing}
                    variant="default"
                    className="bg-black text-white hover:bg-gray-800 flex items-center gap-2"
                  >
                    <Send className="h-5 w-5" />
                    发放
                  </Button>
                )}
                <Button onClick={onPrint} disabled={isProcessing} variant="outline" className="flex items-center gap-2">
                  <Printer className="h-5 w-5" />
                  打印确认单
                </Button>
              </div>
            )}

            {/* 已发放状态可以打印或软删除 */}
            {report.status === "issued" && (
              <div className="flex gap-2">
                <Button onClick={onPrint} disabled={isProcessing} variant="outline" className="flex items-center gap-2">
                  <Printer className="h-5 w-5" />
                  打印确认单
                </Button>
                <Button onClick={onDelete} disabled={isProcessing} variant="destructive" className="flex items-center gap-2">
                  <Trash className="h-5 w-5" />
                  软删除
                </Button>
              </div>
            )}

            {/* 已删除状态只显示信息，无操作 */}
            {report.status === "deleted" && (
              <div className="text-sm text-muted-foreground text-red-600">
                该发放单已被软删除，仅供查看
              </div>
            )}

            {/* 创建状态显示提示信息 */}
            {report.status === "creating" && (
              <div className="text-sm text-muted-foreground">填写必要信息后将自动保存为草稿</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
