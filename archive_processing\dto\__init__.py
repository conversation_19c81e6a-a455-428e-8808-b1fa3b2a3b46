# CHANGE: [2024-03-29] 重构 DTO，按领域拆分到各个文件 #AFM-29

# 按照领域从不同模块导入 DTO 类
from .pdf_dtos import PDFPageImage, PDFPartRange
from .result_dtos import UnifiedNumberResult, ProcessResult
from .processing_dtos import ProcessingStats, SplitPartInfo

# 定义导出的所有类，保持简洁的导入路径
__all__ = [
    # PDF 相关
    'PDFPageImage', 
    'PDFPartRange',
    
    # 结果相关
    'UnifiedNumberResult',
    'ProcessResult',
    
    # 处理和统计相关
    'ProcessingStats',
    'SplitPartInfo',
] 