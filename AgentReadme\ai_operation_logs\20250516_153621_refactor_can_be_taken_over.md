# Operation Document: Refactor ImportSession.can_be_taken_over Method

## 📋 Change Summary

**Purpose**: 重构 `ImportSession` 模型中的 `can_be_taken_over` 方法，以实现更健壮和准确的会话接管逻辑，优先基于心跳超时，并包含首次心跳宽限期和后备的活动超时机制。
**Scope**: `archive_records/models.py` 文件中的 `ImportSession.can_be_taken_over` 方法。
**Associated**: 对应《Excel导入功能的会话管理需求与解决方案.md》文档中的任务1.1后半部分。

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing Implementation and Requirements

**Precondition**: `ImportSession` 模型存在，`can_be_taken_over` 方法的旧有实现存在，需求文档已明确新的逻辑。
**Operation**: 仔细阅读需求文档中关于 `can_be_taken_over` 的新逻辑要求，分析现有模型代码，识别需要修改的部分。
**Postcondition**: 完全理解了修改方案和涉及的超时参数。

### ✏️ OP-002: Modify `can_be_taken_over` Method

**Precondition**: 分析完成，已确定修改方案。
**Operation**:

1. 修改 `ImportSession.can_be_taken_over` 方法的文档字符串，更新为中文并解释新逻辑。
2. 从 `django.conf.settings` 中获取 `SESSION_HEARTBEAT_TIMEOUT_MINUTES`、`SESSION_FIRST_HEARTBEAT_GRACE_PERIOD_SECONDS` 和 `SESSION_ACTIVITY_BACKUP_TIMEOUT_MINUTES` 超时配置，并提供默认值。
3. 实现以下核心逻辑：
    * 检查会话是否激活 (`is_active()`)。
    * 禁止用户接管自己正在处理的会话。
    * 如果会话有处理者 (`processing_user`):
        * 若状态为 `CONFLICT_RESOLUTION`：
            * 有 `last_heartbeat_at`：基于心跳超时判断。
            * 无 `last_heartbeat_at`：基于 `last_activity` 和首次心跳宽限期判断。
        * 对于其他有处理者的状态，或作为心跳机制的后备：基于 `last_activity` 和后备活动超时判断。
    * 如果会话状态为 `ANALYSIS_COMPLETE` 且无处理者，则允许接管。
    * 默认返回 `False`。
4. 在方法内部添加详细的中文日志记录 (`logger.debug`, `logger.info`, `logger.warning`) 以便追踪判断逻辑。
**Postcondition**: `can_be_taken_over` 方法已按新逻辑重构完成。

### 🧪 OP-003: Verify Changes (Conceptual)

**Precondition**: 代码修改已应用。
**Operation**: 审阅生成的代码 diff，确保其符合预期修改。在后续步骤中，将通过单元测试和集成测试进行实际验证。
**Postcondition**: 初步确认代码修改符合设计。

## 📝 Change Details

### CH-001: Refactor `ImportSession.can_be_taken_over`

**File**: `archive_records/models.py`
**Before**:

```python
# ... (旧的 can_be_taken_over 方法实现) ...
    def can_be_taken_over(self, current_user: Optional[User] = None) -> bool:
        """
        检查会话是否可被接管（即，独占处理权是否可以被解除或转移）。
        主要适用场景：
        1. 会话处于 ANALYSIS_COMPLETE 状态，此时没有处理者，允许第一个用户接管以进入 CONFLICT_RESOLUTION。
        2. 会话处于 CONFLICT_RESOLUTION 状态，当前有处理者，此时基于心跳判断原处理者是否失联。
        其他"共享"阶段，不适用"接管"单一处理者的概念。
        """
        if not self.is_active(): # is_active 检查过期和终态 (CANCELLED, ERROR, IMPORT_COMPLETE)
            return False

        if current_user and self.processing_user == current_user:
            # 用户不能接管自己正在处理的会话
            return False
        
        # ... (旧的逻辑细节) ...
        return False
```

**After**: (CHANGE: [2025-05-16] 重构会话接管逻辑)

```python
# ... (新的 can_be_taken_over 方法实现，如此前edit_file工具调用中所示) ...
    def can_be_taken_over(self, current_user: Optional[User] = None) -> bool:
        """
        检查会话是否可被接管。
        新逻辑：
        1. 优先基于心跳超时判断（尤其在CONFLICT_RESOLUTION状态）。
        2. 为首次心跳提供一个较短的宽限期（基于last_activity）。
        3. 为没有心跳记录或非强制心跳状态的情况，提供一个后备的、更长的基于last_activity的超时。
        4. 特殊处理ANALYSIS_COMPLETE状态下无处理者时允许接管。
        """
        if not self.is_active():
            logger.debug(f"会话 {self.session_id} 非激活状态，不可接管。")
            return False

        if current_user and self.processing_user == current_user:
            # 用户不能接管自己正在处理的会话
            logger.debug(f"用户 {current_user.username} 尝试接管自己正在处理的会话 {self.session_id}，不允许。")
            return False

        # 从Django settings获取超时配置
        heartbeat_timeout_minutes = getattr(settings, "SESSION_HEARTBEAT_TIMEOUT_MINUTES", 2)
        first_heartbeat_grace_seconds = getattr(settings, "SESSION_FIRST_HEARTBEAT_GRACE_PERIOD_SECONDS", 60)
        activity_backup_timeout_minutes = getattr(settings, "SESSION_ACTIVITY_BACKUP_TIMEOUT_MINUTES", 30)

        now = timezone.now()

        # 场景1: 会话有处理者 (self.processing_user is not None)
        if self.processing_user:
            # 子场景 1.1: 会话处于需要强制心跳的独占状态 (例如 CONFLICT_RESOLUTION)
            if self.status == ImportSessionStatus.CONFLICT_RESOLUTION:
                if self.last_heartbeat_at:
                    heartbeat_inactive_threshold = now - datetime.timedelta(minutes=heartbeat_timeout_minutes)
                    if self.last_heartbeat_at < heartbeat_inactive_threshold:
                        logger.info(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 因心跳超时 ({heartbeat_timeout_minutes}分钟) 可被接管。最后心跳: {self.last_heartbeat_at}")
                        return True  # 心跳超时，可接管
                    else:
                        logger.debug(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 心跳正常，不可接管。最后心跳: {self.last_heartbeat_at}")
                        return False # 心跳未超时，不可接管
                else:
                    # 没有心跳记录，但状态是CONFLICT_RESOLUTION且有处理者。
                    # 使用首次心跳宽限期（基于last_activity）。
                    first_heartbeat_grace_threshold = now - datetime.timedelta(seconds=first_heartbeat_grace_seconds)
                    if self.last_activity < first_heartbeat_grace_threshold:
                        logger.warning(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 无心跳记录，但超过首次心跳宽限期 ({first_heartbeat_grace_seconds}秒)，可被接管。最后活动: {self.last_activity}")
                        return True # 超过首次心跳宽限期，可接管
                    else:
                        logger.info(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 无心跳记录，但在首次心跳宽限期内，暂不可接管。最后活动: {self.last_activity}")
                        return False # 在首次心跳宽限期内，不可接管
            
            # 子场景 1.2: 后备的活动超时逻辑
            activity_inactive_threshold = now - datetime.timedelta(minutes=activity_backup_timeout_minutes)
            if self.last_activity < activity_inactive_threshold:
                logger.warning(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 因活动超时 ({activity_backup_timeout_minutes}分钟) 可被接管 (后备逻辑)。最后活动: {self.last_activity}")
                return True 
            else:
                logger.debug(f"会话 {self.session_id} (处理者: {self.processing_user.username}, 状态: {self.status}) 活动时间未触发后备超时，不可接管。最后活动: {self.last_activity}")

        # 场景2: 会话分析完成，等待用户首次进入冲突处理 (此时会话应无processing_user)
        if self.status == ImportSessionStatus.ANALYSIS_COMPLETE and not self.processing_user:
            logger.info(f"会话 {self.session_id} 状态为 ANALYSIS_COMPLETE 且无处理者，允许接管。")
            return True

        logger.debug(f"会话 {self.session_id} (状态: {self.status}, 处理者: {self.processing_user}) 不满足任何显式接管条件，默认为不可接管。")
        return False
# ...
```

**Rationale**: 根据需求文档，重构方法以提高接管逻辑的准确性，优先使用心跳，并增加了后备机制和特定状态处理。
**Potential Impact**: 会话接管的行为将发生改变，依赖于新的超时逻辑和 `last_heartbeat_at` 字段。需要更新相关单元测试。依赖 Django settings 中定义新的超时参数。

## ✅ Verification Results

**Method**: 代码审查和后续的单元/集成测试。
**Results**: 代码已按设计修改。方法逻辑更清晰，并包含了详细的日志记录。
**Problems**: 暂无。
**Solutions**: 暂无。
