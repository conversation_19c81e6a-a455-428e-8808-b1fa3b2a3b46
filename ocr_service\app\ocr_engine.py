"""
OCR 引擎管理模块
"""
import time
import warnings
from typing import Optional, List, Dict, Any
import numpy as np
from PIL import Image
import structlog

from .config import settings
from .models import OCRTextResult, OCRNumberResult

# 抑制 PaddleOCR 警告
warnings.filterwarnings("ignore", category=UserWarning, module="paddle")

logger = structlog.get_logger(__name__)


class OCREngine:
    """OCR 引擎管理器"""
    
    def __init__(self):
        self._engine: Optional[Any] = None
        self._initialized = False
        self._init_time: Optional[float] = None
        
    async def initialize(self) -> bool:
        """初始化 OCR 引擎"""
        if self._initialized:
            return True
            
        try:
            logger.info("开始初始化 PaddleOCR 引擎", 
                       version=settings.ocr_version, 
                       language=settings.ocr_language)
            
            start_time = time.time()
            
            # 动态导入 PaddleOCR
            from paddleocr import PaddleOCR
            
            # 抑制 PaddleOCR 日志
            import logging
            paddle_logger = logging.getLogger("ppocr")
            paddle_logger.setLevel(logging.WARNING)
            
            # 初始化引擎
            self._engine = PaddleOCR(
                ocr_version=settings.ocr_version,
                lang=settings.ocr_language,
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            
            self._init_time = time.time() - start_time
            self._initialized = True
            
            logger.info("PaddleOCR 引擎初始化成功", 
                       init_time=self._init_time,
                       engine_ready=True)
            
            return True
            
        except ImportError as e:
            logger.error("PaddleOCR 导入失败", error=str(e))
            return False
        except Exception as e:
            logger.error("PaddleOCR 初始化失败", error=str(e), exc_info=True)
            return False
    
    def is_ready(self) -> bool:
        """检查引擎是否就绪"""
        return self._initialized and self._engine is not None
    
    def get_init_time(self) -> Optional[float]:
        """获取初始化时间"""
        return self._init_time
    
    async def recognize_basic(self, image: Image.Image) -> str:
        """基础 OCR 识别"""
        if not self.is_ready():
            raise RuntimeError("OCR 引擎未初始化")

        try:
            start_time = time.time()

            # 转换为 numpy 数组
            img_array = np.array(image)

            # 执行 OCR
            result = self._engine.predict(img_array)

            # 解析结果
            text = self._parse_ocr_result(result)

            processing_time = time.time() - start_time

            logger.debug("基础 OCR 识别完成",
                        text_length=len(text),
                        processing_time=processing_time)

            return text

        except Exception as e:
            logger.error("基础 OCR 识别失败", error=str(e), exc_info=True)
            return ""
    
    async def recognize_enhanced(self, images: List[Image.Image]) -> List[OCRTextResult]:
        """增强 OCR 识别"""
        if not self.is_ready():
            raise RuntimeError("OCR 引擎未初始化")

        results = []

        for i, image in enumerate(images):
            try:
                start_time = time.time()

                # 转换为 numpy 数组
                img_array = np.array(image)

                # 执行 OCR
                result = self._engine.predict(img_array)

                # 解析结果
                text = self._parse_ocr_result(result)
                processing_time = time.time() - start_time

                results.append(OCRTextResult(
                    text=text,
                    method=f"PaddleOCR增强_{i+1}",
                    processing_time=processing_time
                ))

                logger.debug("增强 OCR 识别完成",
                           attempt=i+1,
                           text_length=len(text),
                           processing_time=processing_time)

            except Exception as e:
                logger.error("增强 OCR 识别失败",
                           attempt=i+1,
                           error=str(e))

                results.append(OCRTextResult(
                    text="",
                    method=f"PaddleOCR增强_{i+1} (失败)",
                    processing_time=0.0
                ))

        return results
    
    def _parse_ocr_result(self, result: Any) -> str:
        """解析 OCR 结果"""
        try:
            if result and len(result) > 0 and result[0]:
                page_result = result[0]
                
                # 检查新版文档模式输出
                if isinstance(page_result, dict) and 'rec_texts' in page_result:
                    text_list = page_result.get('rec_texts', [])
                    return " ".join(text_list).strip()
                
                # # 兼容旧版格式
                # if isinstance(page_result, list):
                #     texts = []
                #     for item in page_result:
                #         if isinstance(item, list) and len(item) >= 2:
                #             texts.append(item[1][0] if isinstance(item[1], tuple) else str(item[1]))
                #     return " ".join(texts).strip()
            
            return ""
            
        except Exception as e:
            logger.error("解析 OCR 结果失败", error=str(e))
            return ""


# 全局 OCR 引擎实例
ocr_engine = OCREngine()
