import apiClient, { ApiResponse } from "@/lib/apiClient";
import { UploadProgress, UploadResult } from "./types";

export interface UploadableFile {
    id: string;
    file: File;
    boxNumber: string;
    status: "pending" | "uploading" | "processing" | "success" | "error";
    error?: string;
}

/**
 * 封装的上传服务
 * @param file - 要上传的文件
 * @param archiveBoxNumber - 档案盒号
 * @param onUploadProgress - 进度回调
 * @returns 
 */
export const uploadPdfService = async (
    file: File,
    archiveBoxNumber: string,
    useParallel: boolean,
    onUploadProgress: (progress: UploadProgress) => void
): Promise<ApiResponse<UploadResult['data']>> => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("archive_box_number", archiveBoxNumber);
    formData.append("use_parallel", String(useParallel));

    // apiClient.post 不会抛出异常, 它在出错时返回 { success: false, ... } 
    // 类型系统认为它返回 ApiResponse<UploadResult>, 但在运行时其实是 ApiResponse<UploadResult['data']>.
    // 我们在这里进行类型断言，以告知 TypeScript 运行时的真实情况。
    const response = await apiClient.post<UploadResult>(
        "/api/archive-processing/uploaded-files/",
        formData,
        {
            onUploadProgress: (progressEvent) => {
                if (progressEvent.total) {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    onUploadProgress({ percentCompleted });
                }
            },
            headers: { "Content-Type": "multipart/form-data" },
            timeout: 300000, // 5分钟超时
        }
    ) as unknown as ApiResponse<UploadResult['data']>;

    return response;
};