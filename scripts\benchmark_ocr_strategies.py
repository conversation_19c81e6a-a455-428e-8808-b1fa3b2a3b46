#!/usr/bin/env python3
"""
OCR 策略性能对比测试
"""
import os
import sys
import time
import statistics
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_processing.utils import ocr_utils, image_utils


def create_test_image(text: str, size: tuple = (800, 200)) -> Image.Image:
    """创建测试图像"""
    img = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 32)
    except:
        font = ImageFont.load_default()
    
    # 计算文本位置 (居中)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    draw.text((x, y), text, fill='black', font=font)
    return img


def benchmark_strategy(strategy_name: str, use_microservice: bool, test_images: list, iterations: int = 3):
    """测试特定策略的性能"""
    print(f"\n🔍 测试 {strategy_name}")
    print("-" * 40)
    
    # 临时修改配置
    original_config = ocr_utils.USE_MICROSERVICE_IMAGE_PROCESSING
    ocr_utils.USE_MICROSERVICE_IMAGE_PROCESSING = use_microservice
    
    try:
        # 初始化 OCR 引擎
        paddle_engine = ocr_utils.init_paddle_ocr(use_paddle=True)
        if paddle_engine is None:
            print("❌ OCR 引擎初始化失败")
            return None
        
        results = {
            'strategy': strategy_name,
            'total_times': [],
            'network_requests': [],
            'success_rate': 0,
            'avg_text_length': 0
        }
        
        successful_tests = 0
        total_text_length = 0
        
        for i in range(iterations):
            print(f"  迭代 {i+1}/{iterations}...")
            
            iteration_times = []
            iteration_requests = 0
            iteration_success = 0
            
            for j, test_image in enumerate(test_images):
                try:
                    start_time = time.time()
                    
                    if use_microservice:
                        # 方案1: 微服务内处理
                        text = ocr_utils.run_paddle_basic(test_image, paddle_engine)
                        iteration_requests += 1  # 1次网络请求
                    else:
                        # 方案2: 主应用预处理
                        enhanced_images = image_utils.get_enhanced_images_for_paddle(test_image)
                        texts = ocr_utils.run_paddle_enhanced(enhanced_images, paddle_engine)
                        text = texts[0] if texts else ""
                        iteration_requests += len(enhanced_images)  # 多次网络请求
                    
                    processing_time = time.time() - start_time
                    iteration_times.append(processing_time)
                    
                    if text and len(text.strip()) > 0:
                        iteration_success += 1
                        total_text_length += len(text)
                    
                    print(f"    图像{j+1}: {processing_time:.2f}s, 文本长度: {len(text)}")
                    
                except Exception as e:
                    print(f"    图像{j+1}: 失败 - {e}")
                    iteration_times.append(0)
            
            total_time = sum(iteration_times)
            results['total_times'].append(total_time)
            results['network_requests'].append(iteration_requests)
            successful_tests += iteration_success
            
            print(f"    总时间: {total_time:.2f}s, 网络请求: {iteration_requests}次")
        
        # 计算统计信息
        results['success_rate'] = successful_tests / (len(test_images) * iterations) * 100
        results['avg_text_length'] = total_text_length / max(successful_tests, 1)
        results['avg_total_time'] = statistics.mean(results['total_times'])
        results['avg_network_requests'] = statistics.mean(results['network_requests'])
        
        return results
        
    finally:
        # 恢复原始配置
        ocr_utils.USE_MICROSERVICE_IMAGE_PROCESSING = original_config


def print_comparison(results1, results2):
    """打印对比结果"""
    print("\n" + "=" * 60)
    print("📊 性能对比结果")
    print("=" * 60)
    
    print(f"{'指标':<20} {'方案1 (微服务)':<15} {'方案2 (主应用)':<15} {'优势':<10}")
    print("-" * 60)
    
    # 平均总时间
    time1 = results1['avg_total_time']
    time2 = results2['avg_total_time']
    winner = "方案1" if time1 < time2 else "方案2"
    improvement = abs(time2 - time1) / max(time1, time2) * 100
    print(f"{'平均总时间(s)':<20} {time1:<15.2f} {time2:<15.2f} {winner} (+{improvement:.1f}%)")
    
    # 网络请求数
    req1 = results1['avg_network_requests']
    req2 = results2['avg_network_requests']
    winner = "方案1" if req1 < req2 else "方案2"
    improvement = abs(req2 - req1) / max(req1, req2) * 100
    print(f"{'平均网络请求数':<20} {req1:<15.1f} {req2:<15.1f} {winner} (-{improvement:.1f}%)")
    
    # 成功率
    success1 = results1['success_rate']
    success2 = results2['success_rate']
    winner = "方案1" if success1 > success2 else "方案2" if success2 > success1 else "相同"
    print(f"{'成功率(%)':<20} {success1:<15.1f} {success2:<15.1f} {winner}")
    
    # 平均文本长度
    text1 = results1['avg_text_length']
    text2 = results2['avg_text_length']
    winner = "方案1" if text1 > text2 else "方案2" if text2 > text1 else "相同"
    print(f"{'平均文本长度':<20} {text1:<15.1f} {text2:<15.1f} {winner}")
    
    print("\n📋 建议:")
    if time1 < time2 and req1 < req2:
        print("🚀 方案1 在性能和网络效率方面都更优，建议使用")
    elif time2 < time1 and success2 >= success1:
        print("🛡️ 方案2 在处理时间和稳定性方面更优，建议使用")
    else:
        print("⚖️ 两种方案各有优势，根据具体需求选择:")
        print("   - 追求网络效率和现代化架构: 选择方案1")
        print("   - 追求稳定性和零风险: 选择方案2")


def main():
    print("🏁 OCR 策略性能对比测试")
    print("=" * 60)
    
    # 创建测试图像
    test_texts = [
        "Hello World 你好世界",
        "Performance Test 性能测试",
        "OCR Benchmark OCR基准测试",
        "Strategy Comparison 策略对比"
    ]
    
    print("📝 创建测试图像...")
    test_images = [create_test_image(text) for text in test_texts]
    print(f"   创建了 {len(test_images)} 张测试图像")
    
    # 测试方案1
    results1 = benchmark_strategy("方案1 - 微服务内图像处理", True, test_images)
    if results1 is None:
        print("❌ 方案1 测试失败")
        return 1
    
    # 测试方案2
    results2 = benchmark_strategy("方案2 - 主应用图像处理", False, test_images)
    if results2 is None:
        print("❌ 方案2 测试失败")
        return 1
    
    # 打印对比结果
    print_comparison(results1, results2)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
