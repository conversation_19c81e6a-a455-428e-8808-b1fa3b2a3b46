# 审计服务层设计文档

## 📋 文档信息

- **文档名称**: 审计服务层设计
- **版本**: v1.0
- **创建日期**: 2025-01-27
- **负责人**: 开发团队
- **相关文档**:
  - [发放单模块事务层设计文档](./transaction_service_layer_design.md)

## 🎯 设计目标

1. **封装审计复杂性**: 提供一个简洁的门面 (`Facade`)，封装 `ChangeLogBatch`, `RecordChangeLog`, `FieldChangeLog`, `IssueRecordHistory` 四个模型的复杂操作。
2. **保障事务一致性**: 确保审计日志的写入与业务操作在同一个数据库事务中，保证业务与审计的同步成功或失败。
3. **提供清晰的API**: 为其他服务层（主要是事务层）提供易于理解和调用的审计接口。
4. **支持核心业务场景**: 完美支持"发放并归档"和"软删除回滚"等复杂业务场景的审计需求。
5. **提高代码可维护性**: 将审计逻辑与业务逻辑解耦，使 `IssueTransactionService` 更专注于业务流程协调。

## 🏗️ 架构定位与模型关系

`AuditService` 作为审计功能的核心封装层，直接与底层的四个审计模型进行交互。

```mermaid
graph TD
    subgraph "上层调用者"
        ITS[IssueTransactionService]
    end

    subgraph "审计服务层 (本设计核心)"
        AS[AuditService]
        AS_API1["start_batch()"]
        AS_API2["log_creation()"]
        AS_API3["log_update()"]
        AS_API4["log_deletion()"]
        AS_API5["log_custom_event()"]
        AS_API6["complete_batch()"]
        
        AS --> AS_API1 & AS_API2 & AS_API3 & AS_API4 & AS_API5 & AS_API6
    end

    subgraph "数据模型 (用户提供)"
        M_CB[(ChangeLogBatch)]
        M_RCL[(RecordChangeLog)]
        M_FCL[(FieldChangeLog)]
        M_IRH[(IssueRecordHistory)]
    end

    ITS -->|调用| AS

    AS_API1 -->|创建| M_CB
    AS_API2 -->|创建| M_RCL & M_FCL
    AS_API3 -->|创建| M_RCL & M_FCL
    AS_API4 -->|创建| M_RCL
    AS_API5 -->|创建| M_IRH

    M_CB -.-> M_RCL
    M_RCL -.-> M_FCL

    style AS fill:#f3e5f5,stroke:#8e24aa,stroke-width:2px
```

## 核心接口 (API) 设计

```python
# report_issuing/services/audit_service.py

from django.contrib.auth.models import User
from django.db.models import Model
from typing import Dict, Any, Optional

class AuditService:
    """
    统一审计服务层
    封装了 ChangeLogBatch, RecordChangeLog, FieldChangeLog, IssueRecordHistory 的所有操作。
    """

    def start_batch(self, user: User, operation_type: str, description: Optional[str] = None) -> 'ChangeLogBatch':
        """
        开始一个审计批次，这是所有审计记录的入口。
        
        Args:
            user: 执行操作的用户。
            operation_type: 操作类型 (e.g., 'ISSUE_FORM_CREATE', 'ISSUE_FORM_ARCHIVE').
            description: 对本次操作的简短描述。
            
        Returns:
            一个已创建并保存的 ChangeLogBatch 实例。
        """
        # ... 实现细节 ...

    def log_creation(self, batch: 'ChangeLogBatch', instance: Model, details: Optional[Dict] = None):
        """
        记录一个新模型实例的创建。
        会自动记录该实例所有字段的初始值。
        
        Args:
            batch: 由 start_batch() 返回的批次实例。
            instance: 新创建的模型实例。
            details: 额外的上下文信息。
        """
        # ... 实现细节 ...

    def log_update(self, batch: 'ChangeLogBatch', instance: Model, old_values: Dict[str, Any], details: Optional[Dict] = None):
        """
        记录一个模型实例的更新。
        会自动比较新旧值的差异，只记录发生变化的字段。
        
        Args:
            batch: 批次实例。
            instance: 被更新的模型实例。
            old_values: 一个包含字段旧值的字典，{'field_name': old_value, ...}。
            details: 额外的上下文信息。
        """
        # ... 实现细节 ...

    def log_deletion(self, batch: 'ChangeLogBatch', instance_class: type, instance_pk: Any, representation: str, details: Optional[Dict] = None):
        """
        记录一个模型实例的删除。
        
        Args:
            batch: 批次实例。
            instance_class: 被删除实例的类 (e.g., IssueFormItem).
            instance_pk: 被删除实例的主键。
            representation: 被删除实例的字符串表示，用于日志可读性。
            details: 额外的上下文信息。
        """
        # ... 实现细节 ...
        
    def log_issue_record_history(self, batch: 'ChangeLogBatch', issue_record: 'IssueRecord', event_type: str):
        """
        为 IssueRecord 创建一个历史快照。
        
        Args:
            batch: 批次实例。
            issue_record: 要为其创建快照的 IssueRecord 实例。
            event_type: 触发快照的事件类型 (e.g., 'CREATED', 'CANCELLED').
        """
        # ... 实现细节 ...

    def complete_batch(self, batch: 'ChangeLogBatch', status: str = 'SUCCESS', outcome_description: Optional[str] = None):
        """
        完成并关闭一个审计批次。
        
        Args:
            batch: 批次实例。
            status: 最终操作结果 ('SUCCESS', 'FAILED', 'PARTIAL_SUCCESS').
            outcome_description: 对操作结果的描述。
        """
        # ... 实现细节 ...

```

## 📝 核心业务场景集成示例

这是 `AuditService` 如何在 `IssueTransactionService` 中被调用的示例，完美契合您提出的两个场景。

### 场景一：发放并归档

**目标**: 在一个批次内，原子地记录 `IssueForm` 状态变更、`IssueRecord` 创建以及 `ArchiveRecord` 更新。

```python
# 在 IssueTransactionService.issue_form_with_records 方法中

def issue_form_with_records(self, form_id: int, user: User) -> Dict:
    
    # 1. 开始审计批次
    audit_batch = self.audit_service.start_batch(
        user=user,
        operation_type='ISSUE_FORM_ARCHIVE',
        description=f"发放并归档发放单 #{form_id}"
    )
    
    try:
        with transaction.atomic():
            issue_form = self.issue_form_service.get_issue_form_by_id(form_id)
            old_form_values = {'status': issue_form.status}
            
            # ... 业务逻辑 ...

            # 循环处理每个发放条目
            for item in issue_form_items:
                # ...
                
                # 2. 记录 IssueRecord 的创建
                issue_record = self.issue_record_service.create_issue_record(...)
                self.audit_service.log_creation(batch=audit_batch, instance=issue_record)
                
                # 3. 记录 IssueRecord 的历史快照
                self.audit_service.log_issue_record_history(batch=audit_batch, issue_record=issue_record, event_type='CREATED')

                # 4. 记录 ArchiveRecord 的更新
                archive_record = item.archive_record
                old_archive_values = {'first_issue_copies': archive_record.first_issue_copies, ...}
                
                # ... 更新 archive_record ...
                self.archive_record_service.update_archive_record(...)
                self.audit_service.log_update(batch=audit_batch, instance=archive_record, old_values=old_archive_values)

            # 5. 记录 IssueForm 状态的更新
            issue_form.status = 'issued'
            self.issue_form_service.update_issue_form(...)
            self.audit_service.log_update(batch=audit_batch, instance=issue_form, old_values=old_form_values)

        # 6. 成功完成审计批次
        self.audit_service.complete_batch(batch=audit_batch, status='SUCCESS')
        
    except Exception as e:
        # 7. 失败时标记审计批次
        self.audit_service.complete_batch(batch=audit_batch, status='FAILED', outcome_description=str(e))
        raise e
        
    return result

```

### 场景二：软删除发放单

**目标**: 在一个批次内，记录所有因软删除而引发的补偿性操作（状态回滚）。

```python
# 在 IssueTransactionService 新增一个方法
def soft_delete_issue_form_with_reversal(self, form_id: int, user: User, reason: str) -> Dict:
    
    # 1. 开始审计批次
    audit_batch = self.audit_service.start_batch(
        user=user,
        operation_type='ISSUE_FORM_SOFT_DELETE',
        description=f"软删除发放单 #{form_id}，原因: {reason}"
    )

    try:
        with transaction.atomic():
            # 获取所有相关的 IssueRecord
            issue_records = self.issue_record_service.get_issue_records_by_form_id(form_id)
            
            for record in issue_records:
                # 2. 记录 IssueRecord 的状态变更（回滚）
                old_record_values = {'is_active': record.is_active}
                record.is_active = False
                self.issue_record_service.update_issue_record(...)
                self.audit_service.log_update(batch=audit_batch, instance=record, old_values=old_record_values)
                
                # 3. 为 IssueRecord 创建"已取消"的历史快照
                self.audit_service.log_issue_record_history(batch=audit_batch, issue_record=record, event_type='CANCELLED')

                # 4. 记录 ArchiveRecord 的状态变更（补偿）
                archive_record = record.archive_record
                old_archive_values = {'first_issue_copies': archive_record.first_issue_copies, ...}
                # ... 重新计算并更新 archive_record ...
                self.archive_record_service.update_archive_record(...)
                self.audit_service.log_update(batch=audit_batch, instance=archive_record, old_values=old_archive_values)

            # 5. 记录 IssueForm 自身的状态变更
            issue_form = self.issue_form_service.get_issue_form_by_id(form_id)
            old_form_values = {'is_deleted': issue_form.is_deleted, 'status': issue_form.status}
            self.issue_form_service.soft_delete_issue_form(...)
            self.audit_service.log_update(batch=audit_batch, instance=issue_form, old_values=old_form_values)

        # 6. 成功完成审计批次
        self.audit_service.complete_batch(batch=audit_batch, status='SUCCESS')

    except Exception as e:
        # 7. 失败时标记审计批次
        self.audit_service.complete_batch(batch=audit_batch, status='FAILED', outcome_description=str(e))
        raise e
        
    return result
```

## ✅ 设计优势

1. **高度解耦**: `IssueTransactionService` 不再关心审计模型的具体实现，只需调用 `AuditService` 的高级接口即可。
2. **事务安全**: 审计操作与业务操作绑定在同一个 `transaction.atomic()` 块中。
3. **逻辑清晰**: 每个业务操作对应一个审计批次，该批次内的所有日志都与此操作相关，极大地提高了日志的可追溯性和可读性。
4. **易于扩展**: 如果未来有新的审计需求（例如，记录IP地址），只需修改 `AuditService` 的内部实现，而无需改动大量的业务代码。

## 🚀 后续步骤

1. **确认设计**: 请您评审此设计文档。
2. **实现 `AuditService`**: 根据本文档实现 `audit_service.py`。
3. **实现依赖的数据服务**: 实现 `IssueFormService` 等。
4. **集成**: 将 `AuditService` 和数据服务集成到 `IssueTransactionService` 中。

---
**文档维护**: 本文档将作为 `AuditService` 实现的唯一标准。
