# Operation Document: Tasks Refactoring, Parallelization, and Validation

## 📋 Change Summary

**Purpose**: Refactor the PDF processing tasks for atomicity, implement a parallel processing model using Celery Chord, introduce a strict "all-or-nothing" validation mechanism before committing changes, fix page indexing issues, resolve reporting bugs, and improve overall code quality and performance.
**Scope**: `archive_processing/tasks.py`, `archive_processing/utils/pdf_utils.py`, `archive_processing/utils/processing_report_utils.py`, `archive_processing/models.py` (added `PDFChunkTask`), Celery configuration, related documentation.
**Associated**: #AFM-Refactor-Parallel, #AFM-32, #AFM-all-or-nothing, #AFM-fix-page-boundaries, #AFM-parallel-debug, #AFM-Report-Fix (assumed tags for tracking)

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing Task Implementation

**Precondition**: Existing `process_pdf_task` is a large, monolithic function with complex logic and potential for long-running transactions and race conditions.
**Operation**: Reviewed the existing `process_pdf_task`, identified areas for splitting into smaller functions, analyzed transaction boundaries, and pinpointed error handling weaknesses. Identified the need for both serial and parallel processing paths.
**Postcondition**: Clear understanding of the limitations of the existing implementation and requirements for the refactoring.

### ✏️ OP-002: Refactor Serial Task into Atomic Functions

**Precondition**: Analysis complete.
**Operation**:

1. Extracted distinct logical steps (get task, check status, validate file, check existence, prepare config, extract info, pre-check, process parts, generate report, update status) into separate, well-defined functions within `tasks.py`.
2. Refined transaction management: used `transaction.atomic()` only around critical database state changes (e.g., updating task status), ensuring long-running operations (PDF processing, file I/O) happen outside transactions.
3. Enhanced logging within each new function for better traceability.
4. Created `process_pdf_serial_task` to orchestrate these atomic functions for the serial execution path.
**Postcondition**: Serial processing logic is modular, transactions are optimized, and error tracing is improved.

### ✏️ OP-003: Design and Implement Parallel Processing Model (Celery Chord)

**Precondition**: Serial task refactored. Need for parallel processing identified.
**Operation**:

1. Defined a three-phase model:
    * Phase 1 (Parallel OCR): Split PDF into chunks, run OCR on each chunk in parallel (`process_pdf_ocr_task`).
    * Phase 2 (Aggregation): Collect results from all OCR tasks, merge information (handling cross-chunk items), perform pre-check (`aggregate_pdf_ocr_results_task`).
    * Phase 3 (Unified Processing): Based on aggregated results, perform file splitting, archiving, and database updates sequentially but based on the complete picture (`process_pdf_with_ocr_results_task`).
2. Created `PDFChunkTask` model to track subtask status and results.
3. Implemented `process_pdf_three_phase_coordinator_task` to initiate the workflow.
4. Implemented `process_pdf_ocr_task`, `aggregate_pdf_ocr_results_task`, and `process_pdf_with_ocr_results_task` as Celery tasks.
5. Used Celery `group`, `chain`, and `chord` primitives to link the phases correctly.
6. Implemented staggered task dispatch (`countdown=i * 2`) in the coordinator to reduce initial lock contention.
**Postcondition**: A functional three-phase parallel processing workflow using Celery Chord is implemented.

### ✏️ OP-004: Implement "All-or-Nothing" Validation (`validate_all_parts_processable`)

**Precondition**: Both serial and parallel processing paths exist. Need to ensure atomicity for the entire batch of files within a PDF.
**Operation**:

1. Created the `validate_all_parts_processable` function.
2. Logic: Takes a list of identified parts (unified_number, page_range), iterates through them, and for each part:
    * Validates input info.
    * Attempts to create a temporary split PDF file.
    * Validates that the final archive storage path can be determined and is likely writable (directory check).
    * If any part fails validation, the function returns `False` and lists the failed parts. Temporary files created during validation are cleaned up.
    * If all parts pass, returns `True` along with a list of validated parts (including their temporary file paths).
3. Integrated `validate_all_parts_processable` into `process_pdf_serial_task` after the initial database pre-check and before any final archiving/database updates.
4. Integrated `validate_all_parts_processable` into `process_pdf_with_ocr_results_task` (Phase 3 of parallel) before final archiving/database updates. Added compatibility logic to handle different page range key names (`page_range` vs `absolute_page_range`).
5. Modified both serial and parallel flows to halt processing and report failure if validation fails, ensuring no partial data is committed.
**Postcondition**: The "all-or-nothing" requirement is enforced. Processing only proceeds if all identified parts within a PDF are validated successfully.

### ✏️ OP-005: Fix Page Indexing Discrepancy

**Precondition**: Inconsistent page indexing observed between PyMuPDF (0-based) and PDF splitting logic (expecting 1-based).
**Operation**:

1. Located the call to `pdf_utils.create_temp_pdf_for_single_archive` within `tasks.py` (both serial and parallel paths/validation).
2. Modified the arguments passed for `start_page` and `end_page`, adding `+ 1` to convert the 0-based index (from `calculate_part_ranges`) to the 1-based index expected by the utility function.
3. Added comments clarifying the index conversion.
**Postcondition**: Page indexing is consistent, preventing off-by-one errors during PDF splitting.

### ✏️ OP-006: Resolve Report Generation Issues

**Precondition**: Report generation failed in the parallel path, specifically when `splitting_info_dto` was not available directly in the final processing task.
**Operation**:

1. Modified `generate_summary_report` function in `tasks.py`.
2. Added logic to check if `splitting_info_dto` is `None`.
3. If `None`, construct a simplified DTO-like dictionary using information available from `archived_files_list` and `status_update_results` (e.g., basic counts, list of unified numbers).
4. Passed this simplified or the original DTO to `processing_report_utils.create_result_summary`.
**Postcondition**: Summary reports can be generated reliably in both serial and parallel processing paths, even with variations in available intermediate data structures.

### ✏️ OP-007: Code Quality and Structure Improvements

**Precondition**: Codebase contains structural issues (incorrect indentation, complex try/except blocks) and suboptimal logging/error handling.
**Operation**:

1. Reviewed and corrected indentation issues, particularly within complex `try...except...finally` blocks in `tasks.py`.
2. Simplified exception handling where possible, ensuring specific exceptions are caught and logged appropriately.
3. Added the `check_task_status_consistency` helper function for debugging parallel task states.
4. Improved log messages throughout `tasks.py` to provide more context for debugging.
**Postcondition**: Code structure is improved, error handling is more robust, and debugging capabilities are enhanced.

### ✅ Verification Operations

**Method**: Code review, running existing unit/integration tests, manual testing of PDF processing via UI/API in Docker environment for both serial and parallel modes (using `use_parallel` flag). Tested with PDFs designed to fail validation (e.g., containing parts with non-existent DB records, parts failing splitting). Tested page boundary cases.
**Results**: Refactored serial task functions correctly. Parallel tasks execute through the three phases. "All-or-nothing" validation correctly prevents partial updates on validation failure. Page indexing fix prevents errors. Reports generate correctly. Code quality improvements pass linters.
**Problems**: Initial parallel runs showed high lock contention; addressed by staggering task starts. Report generation initially failed in parallel path; addressed by creating simplified DTO.
**Solutions**: Implemented task staggering. Modified report generation logic.

## 📝 Change Details

### CH-001: Atomic Function Extraction (Serial Task)

**File**: `archive_processing/tasks.py`
**Before**: Large `process_pdf_task` function.
**After**: Functions like `get_task_with_lock`, `check_task_status`, `validate_task_file`, `check_file_exists`, `prepare_processing_config`, `extract_splitting_info`, `perform_pre_check`, `handle_failed_pre_check`, `process_pdf_part`, `generate_summary_report`, `update_task_status`, `determine_final_status` created. `process_pdf_serial_task` orchestrates them.
**Rationale**: Improve modularity, testability, readability, and optimize transaction scope.
**Potential Impact**: Easier maintenance, reduced risk of long-running transactions.

### CH-002: Parallel Processing Implementation

**Files**: `archive_processing/tasks.py`, `archive_processing/models.py`
**Before**: Only serial processing available.
**After**: Added `PDFChunkTask` model. Added `process_pdf_parallel_task`, `process_pdf_three_phase_coordinator_task`, `process_pdf_ocr_task`, `aggregate_pdf_ocr_results_task`, `process_pdf_with_ocr_results_task`. Implemented Celery Chord logic.
**Rationale**: Improve performance for large PDFs by parallelizing the OCR step.
**Potential Impact**: Increased throughput for large files, increased system complexity, potential for new concurrency issues (mitigated by design and staggering).

### CH-003: "All-or-Nothing" Validation Implementation

**File**: `archive_processing/tasks.py`
**Before**: Processing proceeded part-by-part after pre-check, potentially leading to partial updates if later parts failed.
**After**: Added `validate_all_parts_processable` function. Integrated calls into `process_pdf_serial_task` and `process_pdf_with_ocr_results_task`. Processing halts if validation fails.
**Rationale**: Ensure transactional integrity for the entire PDF processing operation.
**Potential Impact**: Increased initial validation time, guarantees data consistency.

### CH-004: Page Index Fix

**File**: `archive_processing/tasks.py`
**Before**: `create_temp_pdf_for_single_archive` received 0-based indices, potentially causing incorrect page splits.
**After**: Page indices are incremented by 1 before being passed to `create_temp_pdf_for_single_archive`.
**Rationale**: Correct page splitting logic.
**Potential Impact**: Correct handling of PDF page ranges.

### CH-005: Report Generation Fix

**File**: `archive_processing/tasks.py`
**Before**: `generate_summary_report` assumed `splitting_info_dto` was always present.
**After**: Added logic to handle cases where `splitting_info_dto` is `None` by constructing a simplified DTO.
**Rationale**: Ensure reports are generated reliably in all execution paths.
**Potential Impact**: Robust report generation.

### CH-006: Added Consistency Check Helper

**File**: `archive_processing/tasks.py`
**Before**: Debugging parallel task states was difficult.
**After**: Added `check_task_status_consistency` function.
**Rationale**: Provide a tool for diagnosing parallel processing issues.
**Potential Impact**: Easier debugging.

## ✅ Verification Results

**Method**: As described in OP-007. Focused on verifying the "all-or-nothing" logic with specially crafted PDFs and observing task states in Celery Flower/logs. Verified page ranges in output PDFs.
**Results**: All major functionalities (serial processing, parallel processing, validation, page indexing, reporting) work as expected according to the requirements. The system correctly prevents partial processing when validation fails.
**Problems**: None during final verification rounds after implementing fixes (staggering, report DTO).
**Solutions**: N/A.
