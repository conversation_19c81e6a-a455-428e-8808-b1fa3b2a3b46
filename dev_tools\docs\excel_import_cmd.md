# 管理命令使用说明

## Excel档案导入命令

### 简介
`excel_import_cmd` 命令提供了一个便捷的方式，通过命令行将Excel文件中的档案记录批量导入到系统数据库中。此命令特别适用于系统初始化或批量数据迁移场景。

### 功能特点
- 支持批量导入Excel文件数据
- 自动创建导入用户（如果不存在）
- 提供详细的导入结果统计
- 以表格形式显示导入结果
- 错误日志记录和摘要显示
- 支持自定义用户名

### 使用方法

基本语法：
```bash
python manage.py excel_import_cmd <excel_file> [--username <username>] [--limit <num>]
```

参数说明：
- `excel_file`：Excel文件的路径（必需参数）
- `--username`：执行导入的用户名（可选参数，默认为'admin'）
- `--limit`：显示导入记录的数量（可选参数，默认为10条）

### 使用示例

1. 使用默认用户名导入：
```bash
python manage.py excel_import_cmd data.xlsx
```

2. 使用真实数据导入：
```bash
python manage.py excel_import_cmd archive_records/tests/test_files/real_data.xlsx
```

3. 使用自定义用户名导入：
```bash
python manage.py excel_import_cmd data.xlsx --username john_doe
```

4. 显示更多导入记录：
```bash
python manage.py excel_import_cmd data.xlsx --limit 20
```
```

### 2. 更新`excel_import_cmd.py`文件中的文档字符串

在文件顶部的文档字符串中更新命令名称：

```python
"""
Excel档案记录导入命令

此命令用于从Excel文件批量导入档案记录到数据库。支持以下功能：
1. 自动创建导入用户（如果不存在）
2. 导入Excel数据到档案记录系统
3. 以表格形式显示导入结果统计
4. 错误日志记录和摘要显示

用法:
    python manage.py excel_import_cmd <excel_file> [--username <username>] [--limit <num>]

参数:
    excel_file          Excel文件的路径（必需）
    --username          执行导入的用户名（可选，默认为'admin'）
    --limit             显示导入记录的数量（可选，默认为10条）

示例:
    # 使用默认用户名('admin')导入数据
    python manage.py excel_import_cmd data.xlsx
    
    # 使用测试数据导入
    python manage.py excel_import_cmd test_suite/test_files/excel/valid_data.xlsx

    # 使用指定用户名导入数据
    python manage.py excel_import_cmd data.xlsx --username john_doe
    
    # 显示更多导入记录
    python manage.py excel_import_cmd data.xlsx --limit 20

注意事项:
    1. Excel文件必须符合预定义的模板格式
    2. 如果指定的用户不存在，将自动创建该用户
    3. 导入过程会显示实时进度和错误信息
    
文件命名说明:
    此文件与业务逻辑中的 excel_import.py 服务类配合使用，是命令行接口。
"""
```
