#!/usr/bin/env python3
"""
测试OCR重复执行的准确率影响
验证同一图像多次OCR是否会产生不同结果
"""
import time
import sys
import requests
import io
from PIL import Image, ImageDraw, ImageFont

# 添加项目路径
sys.path.append('.')

def create_test_image(text: str, noise_level: int = 0) -> Image.Image:
    """创建测试图像，可添加噪声"""
    size = (600, 200)
    image = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(image)
    
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # 添加文本
    draw.text((50, 80), text, fill='black', font=font)
    
    # 添加噪声（模拟扫描质量问题）
    if noise_level > 0:
        import random
        for _ in range(noise_level * 100):
            x = random.randint(0, size[0]-1)
            y = random.randint(0, size[1]-1)
            draw.point((x, y), fill='gray')
    
    return image

def test_ocr_service(image: Image.Image, mode: str = "basic", repeat_count: int = 5):
    """测试OCR服务的重复性"""
    url = "http://localhost:8001/ocr"
    
    results = []
    
    for i in range(repeat_count):
        try:
            # 将图像转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # 准备请求
            files = {'file': ('test.png', img_buffer, 'image/png')}
            data = {'mode': mode, 'max_attempts': 4}
            
            # 发送请求
            response = requests.post(url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    if mode == "basic":
                        text = result.get('text', '')
                        results.append(text)
                    else:  # enhanced
                        text_results = result.get('text_results', [])
                        texts = [tr.get('text', '') for tr in text_results]
                        results.append(texts)
                else:
                    results.append(None)
            else:
                results.append(None)
                
        except Exception as e:
            print(f"第{i+1}次请求失败: {e}")
            results.append(None)
        
        # 短暂延迟
        time.sleep(0.1)
    
    return results

def analyze_repeatability(results, mode: str):
    """分析重复性"""
    print(f"\n📊 {mode.upper()} OCR 重复性分析:")
    print("-" * 50)
    
    valid_results = [r for r in results if r is not None]
    
    if not valid_results:
        print("❌ 没有有效结果")
        return False
    
    print(f"有效结果数: {len(valid_results)}/{len(results)}")
    
    if mode == "basic":
        # 基础OCR：比较文本
        unique_results = set(valid_results)
        print(f"唯一结果数: {len(unique_results)}")
        
        if len(unique_results) == 1:
            print("✅ 结果完全一致（确定性）")
            print(f"结果: '{list(unique_results)[0]}'")
            return True
        else:
            print("⚠️ 结果不一致（可能有随机性）")
            for i, result in enumerate(unique_results):
                print(f"  结果{i+1}: '{result}'")
            return False
    
    else:  # enhanced
        # 增强OCR：比较每个变体的结果
        print("增强OCR结果分析:")
        
        all_consistent = True
        for variant_idx in range(4):  # 假设4个变体
            variant_results = []
            for result_set in valid_results:
                if isinstance(result_set, list) and len(result_set) > variant_idx:
                    variant_results.append(result_set[variant_idx])
            
            if variant_results:
                unique_variant_results = set(variant_results)
                print(f"  变体{variant_idx+1}: {len(unique_variant_results)} 个唯一结果")
                
                if len(unique_variant_results) > 1:
                    all_consistent = False
                    print(f"    ⚠️ 不一致: {list(unique_variant_results)}")
                else:
                    print(f"    ✅ 一致: '{list(unique_variant_results)[0]}'")
        
        return all_consistent

def main():
    """主函数"""
    print("🔍 OCR重复执行准确率测试")
    print("=" * 60)
    
    # 检查OCR服务是否可用
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ OCR服务不可用，请先启动服务")
            return
    except:
        print("❌ 无法连接到OCR服务，请先启动服务")
        return
    
    # 测试场景
    test_cases = [
        {
            "name": "清晰文本",
            "text": "合同协议书\n统一编号：2025-001",
            "noise": 0
        },
        {
            "name": "轻微噪声",
            "text": "合同协议书\n统一编号：2025-001", 
            "noise": 1
        },
        {
            "name": "复杂文本",
            "text": "甲方：某某公司\n乙方：某某个人\n统一编号：CONTRACT-2025-001",
            "noise": 0
        }
    ]
    
    overall_consistent = True
    
    for case in test_cases:
        print(f"\n🧪 测试案例: {case['name']}")
        print("=" * 40)
        
        # 创建测试图像
        test_image = create_test_image(case['text'], case['noise'])
        
        # 测试基础OCR
        print("测试基础OCR重复性...")
        basic_results = test_ocr_service(test_image, "basic", 5)
        basic_consistent = analyze_repeatability(basic_results, "basic")
        
        # 测试增强OCR
        print("\n测试增强OCR重复性...")
        enhanced_results = test_ocr_service(test_image, "enhanced", 3)  # 减少次数，因为耗时更长
        enhanced_consistent = analyze_repeatability(enhanced_results, "enhanced")
        
        if not (basic_consistent and enhanced_consistent):
            overall_consistent = False
    
    # 总结
    print(f"\n🎯 总结:")
    print("=" * 40)
    
    if overall_consistent:
        print("✅ OCR结果完全确定性")
        print("📝 结论: 重复OCR不会提高准确率")
        print("💡 建议: 实现缓存功能，避免重复计算")
    else:
        print("⚠️ OCR结果存在随机性")
        print("📝 结论: 重复OCR可能提高准确率")
        print("💡 建议: 移除缓存功能，保留重复计算")
    
    return overall_consistent

if __name__ == "__main__":
    main()
