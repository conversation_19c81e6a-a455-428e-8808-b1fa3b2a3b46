"use client";

import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { AgGridReact } from 'ag-grid-react';
import {
  ColDef,
  GridOptions,
  ICellRendererParams,
  ValueFormatterParams,
  GridReadyEvent,
  GetDetailRowDataParams,
  FirstDataRenderedEvent,
  GridApi,
  GetRowIdParams,
  IDetailCellRendererParams,
  ValueGetterParams,
} from 'ag-grid-enterprise';
import agGridConfig from '@/lib/ag-grid-config';
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  ExternalLink,
  Loader2,
  Pencil,
  RefreshCw,
  UploadCloud,
} from "lucide-react";
import { getTasks, getTaskDetailRecords, retryTask, reuploadFile, updateArchiveBoxNumber } from '@/services/domain/archive/ledger/processing-task-service';
import { ProcessingTask, ArchiveRecord } from '@/services/domain/archive/ledger/types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProcessingTaskGridProps {
  status: 'processing' | 'completed' | 'failed' | 'all' | 'queued' | 'pending' | 'chunking' | 'processing_parallel' | 'aggregating' | 'failed_validation' | 'completed_without_report';
}

// --- 状态和操作UI组件 ---
const statusBadgeMap = {
    pending: <Badge variant="outline"><Clock className="mr-1 h-3 w-3" />待处理</Badge>,
    queued: <Badge variant="outline"><Clock className="mr-1 h-3 w-3" />待处理</Badge>,
    
    processing: <Badge variant="secondary"><Loader2 className="mr-1 h-3 w-3 animate-spin" />处理中</Badge>,
    chunking: <Badge variant="secondary"><Loader2 className="mr-1 h-3 w-3 animate-spin" />处理中(分块)</Badge>,
    processing_parallel: <Badge variant="secondary"><Loader2 className="mr-1 h-3 w-3 animate-spin" />处理中(并行)</Badge>,
    aggregating: <Badge variant="secondary"><Loader2 className="mr-1 h-3 w-3 animate-spin" />处理中(汇总)</Badge>,
    
    completed: <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle2 className="mr-1 h-3 w-3" />已完成</Badge>,
    completed_without_report: <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><AlertCircle className="mr-1 h-3 w-3" />已完成(报告未完全分离)</Badge>,
    
    failed: <Badge variant="destructive"><AlertCircle className="mr-1 h-3 w-3" />失败</Badge>,
    failed_validation: <Badge variant="destructive"><AlertCircle className="mr-1 h-3 w-3" />失败(验证)</Badge>,
};

const ProgressBarCellRenderer: React.FC<
  ICellRendererParams<ProcessingTask>
> = ({ data }) => {
  if (!data || typeof data.progress !== "number") {
    return null;
  }

  const { progress, status } = data;

  const isFailed = status === "failed" || status === "failed_validation";
  const isProcessing = [
    "processing",
    "chunking",
    "processing_parallel",
    "aggregating",
  ].includes(status);
  const isCompleted = status === "completed";
  const isCompletedWithoutReport = status === "completed_without_report";

  let barClass = "";
  if (isFailed) {
    barClass = "bg-red-500"; // 失败时为红色
  } else if (isProcessing) {
    barClass = "bg-green-300 animate-pulse"; // 处理中时为带动画的浅绿色
  } else if (isCompleted) {
    barClass = "bg-green-500"; // 完成时为深绿色
  } else if (isCompletedWithoutReport) {
    barClass = "bg-yellow-500"; // 部分完成时为黄色
  } else {
    // 待处理或排队中
    barClass = "bg-sky-400"; // 其他状态(如排队中)为蓝色
  }

  return (
    <div className="flex h-full w-full items-center">
      <div className="relative h-5 w-full overflow-hidden rounded-full bg-gray-200">
        <div
          className={`h-full rounded-full transition-all duration-300 ${barClass}`}
          style={{ width: `${progress}%` }}
        />
        <span className="absolute inset-0 flex items-center justify-center text-xs font-semibold text-white drop-shadow-[0_1px_1px_rgba(0,0,0,0.4)]">
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
};

const ActionsCellRenderer: React.FC<ICellRendererParams<ProcessingTask> & { refreshData: () => void }> = ({ data, refreshData }) => {
    const { toast } = useToast();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isReuploading, setIsReuploading] = useState(false);
    const [isRetrying, setIsRetrying] = useState(false);
    
    // 编辑功能相关的状态
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [newArchiveBoxNumber, setNewArchiveBoxNumber] = useState('');
    const [isSaving, setIsSaving] = useState(false);


    if (!data) return null;

    const handleRetry = async () => {
        setIsRetrying(true);
        try {
            await retryTask(data.taskId);
            toast({
                title: "操作成功",
                description: `任务 ${data.taskId} 已成功加入重试队列。`,
            });
            refreshData();
        } catch (error: any) {
            console.error(`Error retrying task ${data.taskId}:`, error);
            toast({
                title: "操作失败",
                description: `无法重试任务: ${error.message}`,
                variant: "destructive",
            });
        } finally {
            setIsRetrying(false);
        }
    };
    
    const handleTriggerReupload = () => {
      fileInputRef.current?.click();
    };

    const handleFileSelected = async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) {
        return;
      }

      setIsReuploading(true);
      try {
        await reuploadFile(data.fileId, file);
        toast({
            title: "操作成功",
            description: `文件 ${data.uploadedFile.originalName} 已被新文件替换，新的处理任务已创建。`,
        });
        refreshData();
      } catch (error: any) {
        console.error(`Error re-uploading for fileId ${data.fileId}:`, error);
        toast({
          title: "操作失败",
          description: `无法重新上传文件: ${error.message}`,
          variant: "destructive",
        });
      } finally {
        setIsReuploading(false);
        if(fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    };
    
    const handleOpenEditDialog = () => {
      setNewArchiveBoxNumber(data.uploadedFile.archiveBoxNumber || '');
      setIsEditDialogOpen(true);
    };

    const handleSaveChanges = async () => {
      setIsSaving(true);
      try {
        await updateArchiveBoxNumber(data.fileId, newArchiveBoxNumber);
        toast({
          title: "操作成功",
          description: `档案盒号已成功更新为 ${newArchiveBoxNumber}。`,
        });
        setIsEditDialogOpen(false);
        refreshData(); 
      } catch (error: any) {
        console.error(`Error updating archive box number for fileId ${data.fileId}:`, error);
        toast({
          title: "操作失败",
          description: `无法更新档案盒号: ${error.message}`,
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    };

    return (
        <div className="flex h-full items-center justify-center space-x-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileSelected}
              className="hidden"
              accept=".pdf" 
            />
            {(data.status === 'failed' || data.status === 'failed_validation' || data.status === 'completed_without_report') && (
              <span title="重试" onClick={handleRetry} className="cursor-pointer">
                {isRetrying ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 text-blue-500 hover:text-blue-700" />
                )}
              </span>
            )}
            {(data.status === 'failed' || data.status === 'failed_validation' || data.status === 'completed_without_report') && (
              <span title="重新上传 (替换)" onClick={handleTriggerReupload} className="cursor-pointer">
                {isReuploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <UploadCloud className="h-4 w-4 text-green-600 hover:text-green-800" />
                )}
              </span>
            )}
            {/* 编辑档案盒号按钮 */}
            {(data.status === 'completed' || data.status === 'completed_without_report') && (
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogTrigger asChild>
                  <span title="编辑档案盒号" onClick={handleOpenEditDialog} className="cursor-pointer">
                    <Pencil className="h-4 w-4 text-gray-600 hover:text-gray-900" />
                  </span>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>编辑档案盒号</DialogTitle>
                    <DialogDescription>
                      更新完成任务的档案盒号信息
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      正在为文件 <strong>{data.uploadedFile.originalName}</strong> 更新档案盒号。
                    </p>
                    <Input 
                      value={newArchiveBoxNumber}
                      onChange={(e) => setNewArchiveBoxNumber(e.target.value)}
                      placeholder="请输入新的档案盒号"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
                    <Button onClick={handleSaveChanges} disabled={isSaving}>
                      {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      保存
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}
        </div>
    );
};

const LinkCellRenderer: React.FC<ICellRendererParams> = ({ value }) => {
    if (!value) return null;
    return (
        <a href={value} target="_blank" rel="noopener noreferrer" className="flex items-center text-blue-500 hover:underline">
            查看 <ExternalLink className="ml-1 h-4 w-4" />
        </a>
    );
};

const ErrorMessageCellRenderer: React.FC<ICellRendererParams<ProcessingTask>> = ({
  value,
}) => {
  if (!value) return null;

  const maxLength = 40;
  const truncatedValue =
    value.length > maxLength
      ? `${value.substring(0, maxLength)}... (点击查看)`
      : value;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <span className="cursor-pointer text-red-500 hover:underline">
          {truncatedValue}
        </span>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>完整错误信息</DialogTitle>
          <DialogDescription>
            以下是任务处理过程中记录的详细技术错误信息，可用于问题排查。
          </DialogDescription>
        </DialogHeader>
        <div className="mt-2 max-h-[60vh] overflow-y-auto rounded-md bg-slate-50 p-4">
          <pre className="whitespace-pre-wrap break-words text-sm text-slate-700">
            {value}
          </pre>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// --- 主组件 ---
const ProcessingTaskGrid: React.FC<ProcessingTaskGridProps> = ({ status }) => {
  const [rowData, setRowData] = useState<ProcessingTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const gridApiRef = useRef<GridApi | null>(null);

  const getRowId = useMemo(() => {
    return (params: GetRowIdParams<ProcessingTask>) => params.data.taskId;
  }, []);

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api;
  };

  const fetchTasks = useCallback(async () => {
    setIsLoading(true);
    try {
      const tasks = await getTasks(status);
      setRowData(tasks);
    } catch (error: any) {
      console.error(`Error fetching ${status} tasks:`, error);
      toast({
        title: "数据加载失败",
        description: error.message || `无法加载状态为 "${status}" 的任务列表。`,
        variant: "destructive",
      });
      setRowData([]);
    } finally {
      setIsLoading(false);
    }
  }, [status, toast]);

  useEffect(() => {
    fetchTasks();

    // 只对"处理中"或"全部"标签页启用轮询
    if (status === 'processing' || status === 'all') {
      const intervalId = setInterval(() => {
        // 在后台静默刷新，不显示加载状态
        (async () => {
          try {
            const newTasks = await getTasks(status);
            const gridApi = gridApiRef.current;

            if (gridApi) {
              const rowsToRemove: ProcessingTask[] = [];
              const existingTaskIds = new Set<string>();
              const newTaskIds = new Set(newTasks.map(t => t.taskId));

              // 收集当前Grid中的所有任务ID，并找出要移除的任务
              gridApi.forEachNode(node => {
                if (node.data) {
                  existingTaskIds.add(node.data.taskId);
                  if (!newTaskIds.has(node.data.taskId)) {
                    rowsToRemove.push(node.data);
                  }
                }
              });

              // 正确分离新增和更新的任务
              const tasksToUpdate = newTasks.filter(task => existingTaskIds.has(task.taskId));
              const tasksToAdd = newTasks.filter(task => !existingTaskIds.has(task.taskId));

              // 使用事务同时处理新增、更新和删除，避免AG Grid行ID错误
              gridApi.applyTransaction({
                add: tasksToAdd,       // 添加新任务
                update: tasksToUpdate, // 更新现有任务
                remove: rowsToRemove,  // 移除已完成或消失的任务
              });
            }
          } catch (error) {
            console.error("Polling error:", error);
            // 这里可以选择性地弹出toast，但可能会打扰用户，因此暂时只在控制台打印
          }
        })();
      }, 5000); // 每5秒刷新一次

      // 清理函数：当组件卸载或status变更时，清除定时器
      return () => clearInterval(intervalId);
    }
  }, [fetchTasks, status]);

  const columnDefs = useMemo<ColDef<ProcessingTask>[]>(() => {
    const cols: ColDef<ProcessingTask>[] = [
      { 
        headerName: '文件名', 
        field: 'uploadedFile.originalName', 
        width: 200,
        filter: true, 
        cellRenderer: status === 'completed' ? 'agGroupCellRenderer' : undefined 
      },
      {
        headerName: '档案盒号',
        field: 'uploadedFile.archiveBoxNumber',
        filter: true,
      },
    ];

    cols.push({
      headerName: '状态',
      field: 'status',
      cellRenderer: (p: ICellRendererParams<ProcessingTask>) => {
        const statusKey = p.value as keyof typeof statusBadgeMap;
        return p.value ? statusBadgeMap[statusKey] : null;
      }
    });
    
    if (status === 'processing' || status === 'all') {
      cols.push({
        headerName: "进度",
        field: "progress",
        width: 150,
        cellRenderer: ProgressBarCellRenderer,
        resizable: true,
      });
    }

    cols.push(
      { headerName: '上传人', field: 'uploadedFile.username' },
      { headerName: '创建时间', field: 'createdAt', valueFormatter: (p: ValueFormatterParams) => p.value ? new Date(p.value).toLocaleString() : '' },
    );

    if (status === 'failed' || status === 'all') {
      cols.push({
        headerName: '错误信息',
        field: 'errorMessage',
        width: 200,
        cellRenderer: ErrorMessageCellRenderer,
      });
    }
    
    if (status === 'completed' || status === 'all') {
      cols.push({
        headerName: '处理摘要',
        field: 'summaryReportUrl',
        minWidth: 120,
        cellRenderer: LinkCellRenderer,
        cellRendererParams: {
            linkText: '查看',
            newWindow: true
        }
      });
    }

    cols.push({
      headerName: '操作',
      cellRenderer: (params: ICellRendererParams<ProcessingTask>) => (
        <ActionsCellRenderer {...params} refreshData={fetchTasks} />
      ),
      pinned: 'right',
    });

    return cols;
  }, [status, fetchTasks]);

  const onFirstDataRendered = useCallback((params: FirstDataRenderedEvent) => {
    params.api.autoSizeAllColumns(false);
    
    if (status === 'completed') {
      params.api.forEachNode(node => {
        if (node.data && node.data.resultData?.archived_count > 0) {
          node.setExpanded(true);
        }
      });
    }
  }, [status]);
  
  const detailCellRendererParams = useMemo(() => ({
    detailGridOptions: {
      ...agGridConfig.clientSideDefaults,
      rowNumbers: true,
      columnDefs: [
        { headerName: '统一编号', field: 'unifiedNumber', flex: 1 },
        { headerName: '归档状态', field: 'archiveStatus', flex: 1 },
        { headerName: '档案链接', field: 'archiveUrl', flex: 1, cellRenderer: LinkCellRenderer },
        { headerName: '报告链接', field: 'reportUrl', flex: 1, cellRenderer: LinkCellRenderer },
      ],
    },
    getDetailRowData: (params: GetDetailRowDataParams<ProcessingTask>) => {
      getTaskDetailRecords(params.data.taskId)
        .then((records: ArchiveRecord[]) => {
          params.successCallback(records);
        })
        .catch(error => {
          console.error("Failed to load detail records:", error);
          toast({
            title: "加载明细失败",
            description: `无法加载任务 ${params.data.taskId} 的明细记录。`,
            variant: "destructive",
          });
          params.successCallback([]);
        });
    },
  }), [toast]);
  
  // 将所有options统一到useMemo中，并使其依赖于所有动态变化的部分
  const baseGridOptions: GridOptions<ProcessingTask> = useMemo(() => {
      return {
          ...agGridConfig.clientSideDefaults,
          rowNumbers: true,
          onGridReady: onGridReady,
          getRowId: getRowId,
          onFirstDataRendered: onFirstDataRendered,
      };
  }, [getRowId, onGridReady, onFirstDataRendered]);

  const isMasterDetail = status === 'completed';

  return (
    <div className="ag-theme-quartz" style={{ width: '100%', height: '100%' }}>
      <AgGridReact 
        {...baseGridOptions}
        rowData={rowData} 
        loading={isLoading}
        columnDefs={columnDefs}
        masterDetail={isMasterDetail}
        detailCellRendererParams={isMasterDetail ? detailCellRendererParams : undefined}
      />
    </div>
  );
};

export default ProcessingTaskGrid; 