"use client"

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

export function StatusDistributionCard() {
  // 模拟数据 - 实际应用中应从API获取
  const reportData = [
    { name: "未发放", value: 384, total: 1284, color: "#f59e0b" },
    { name: "部分发放", value: 578, total: 1284, color: "#3b82f6" },
    { name: "全部发放", value: 322, total: 1284, color: "#10b981" },
  ]

  const formData = [
    { name: "草稿状态", value: 42, total: 1284, color: "#f59e0b" },
    { name: "待处理", value: 156, total: 1284, color: "#3b82f6" },
    { name: "处理中", value: 118, total: 1284, color: "#6366f1" },
    { name: "已完成", value: 968, total: 1284, color: "#10b981" },
  ]

  // 计算百分比
  const getPercentage = (value: number, total: number) => {
    return Math.round((value / total) * 100)
  }

  // 获取进度条颜色类
  const getColorClass = (name: string) => {
    switch (name) {
      case "未发放":
      case "草稿状态":
        return "rgb(245, 158, 11)" // amber-500
      case "部分发放":
      case "待处理":
        return "rgb(59, 130, 246)" // blue-500
      case "处理中":
        return "rgb(99, 102, 241)" // indigo-500
      case "全部发放":
      case "已完成":
        return "rgb(16, 185, 129)" // emerald-500
      default:
        return "rgb(107, 114, 128)" // gray-500
    }
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>状态分布</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="report" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="report">发放状态分布</TabsTrigger>
            <TabsTrigger value="form">发放单状态分布</TabsTrigger>
          </TabsList>

          <TabsContent value="report" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {reportData.map((item) => (
                <div key={item.name} className="space-y-2 bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{item.name}</span>
                    <span className="text-sm font-medium">{getPercentage(item.value, item.total)}%</span>
                  </div>
                  <Progress
                    value={getPercentage(item.value, item.total)}
                    className="h-2"
                    style={{ "--indicator-color": getColorClass(item.name) } as React.CSSProperties}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {item.value}/{item.total}
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-end mt-2">
              <Link href="/reports/management" className="text-sm font-medium flex items-center hover:underline">
                查看详细统计 <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </TabsContent>

          <TabsContent value="form" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {formData.map((item) => (
                <div key={item.name} className="space-y-2 bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{item.name}</span>
                    <span className="text-sm font-medium">{getPercentage(item.value, item.total)}%</span>
                  </div>
                  <Progress
                    value={getPercentage(item.value, item.total)}
                    className="h-2"
                    style={{ "--indicator-color": getColorClass(item.name) } as React.CSSProperties}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {item.value}/{item.total}
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-end mt-2">
              <Link href="/reports/management" className="text-sm font-medium flex items-center hover:underline">
                查看详细统计 <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
