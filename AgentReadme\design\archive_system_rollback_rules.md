# 档案系统回滚规则设计文档

## 文档概述

本文档定义了档案管理系统中的回滚机制规则，包括不同类型的回滚操作、适用场景、执行范围和实现要求。回滚机制是保证数据完整性和操作可逆性的关键功能，对于维护档案记录的准确性和可追溯性至关重要。

## 回滚类型定义

系统中的回滚操作分为两种主要类型：

### 1. 隐式回滚（Implicit Rollback）

隐式回滚是指由系统自动触发的、针对特定操作的回滚行为，主要出现在关联项删除时。

#### 1.1 发放记录删除触发的隐式回滚

当用户删除档案发放记录时，系统将自动回滚该发放记录对主档案记录的影响。

**回滚范围**：

- 仅回滚与该发放记录直接相关的字段
- 不影响档案记录的其他信息

**具体规则**：

- 如删除的是首次发放记录，且已存在后续发放记录，则后续发放记录将被标记为"已失效"（软删除）
- 系统回滚"发放份数"、"发放人"、"领取人"等直接相关字段到该发放记录创建前的状态
- 更新"发放次数"计数
- 在历史记录中标记为"由发放记录#ID删除操作触发的系统回滚"

#### 1.2 更改单删除触发的隐式回滚

当用户删除档案更改单时，系统将自动回滚该更改单对主档案记录的修改。

**回滚范围**：

- 仅回滚该更改单实际修改过的字段
- 不影响更改单未涉及的其他字段

**具体规则**：

- 系统分析该更改单涉及的所有修改，并仅回滚这些字段到更改单创建前的状态
- 如果删除的更改单是某条档案记录的第N次更改，且已存在第N+1及之后的更改，则需要软删除这些后续更改
- 更新"更改次数"计数
- 在历史记录中标记为"由更改单#ID删除操作触发的系统回滚"

### 2. 显式回滚（Explicit Rollback）

显式回滚是指用户主动发起的、针对整个档案记录的回滚操作。

**回滚范围**：

- 仅回滚档案记录主表数据到指定历史版本
- 不自动级联回滚关联表数据

**具体规则**：

- 用户可以选择档案记录的任一历史版本进行回滚
- 回滚操作将档案记录的所有字段恢复到选定版本的状态
- 系统不自动回滚关联表（如发放记录、更改单）的数据
- 系统在执行回滚前，必须向用户显示关联表项提示，告知这些表项需要单独处理
- 执行回滚时须记录回滚操作者、回滚原因和回滚时间
- 在历史记录中标记为"用户发起的档案记录显式回滚操作"

## 数据处理规范

### 1. 数据一致性

- 所有回滚操作必须在数据库事务中执行，确保操作的原子性
- 回滚前必须验证数据的完整性和一致性
- 系统必须维护字段间的业务约束，即使在回滚过程中

### 2. 历史记录

- 每次回滚操作都必须创建新的历史记录
- 历史记录必须包含回滚类型、回滚原因和操作人信息
- 对于部分字段回滚，必须明确记录哪些字段受到影响

### 3. 软删除实践

- 系统对关联表项（发放记录、更改单）采用软删除机制
- 软删除项应标记删除状态、删除原因和删除时间
- 软删除不应删除实际数据，以保持数据完整性和可追溯性

### 4. 审计与授权

- 显式回滚操作需要特定权限
- 系统必须记录所有回滚操作的完整审计日志
- 操作日志应包含足够信息以重建回滚过程

## 特殊情况处理

### 1. 回滚冲突

当回滚操作可能导致数据冲突时（如违反唯一性约束），系统应：

- 提供明确的错误信息，说明冲突原因
- 建议可能的解决方案
- 取消回滚操作并保持数据不变

### 2. 级联影响

虽然系统不自动级联回滚关联表数据，但应：

- 在回滚前向用户提供关联项清单
- 解释可能的影响
- 提供处理建议

### 3. 用户确认机制

所有回滚操作前必须经过用户确认：

- 隐式回滚：在删除关联项时提示用户将触发回滚
- 显式回滚：提供详细的回滚影响说明和确认对话框

## 用户界面要求

### 1. 回滚操作前确认

- 隐式回滚确认提示：

  ```
  删除此[发放记录/更改单]将导致相关字段回滚到其创建前的状态。以下字段将受影响:
  - [字段1]: 从"[当前值]"变更为"[回滚后值]"
  - [字段2]: 从"[当前值]"变更为"[回滚后值]"
  确定要继续吗？
  ```

- 显式回滚确认提示：

  ```
  您正准备将档案记录回滚到[日期时间]的版本。此操作:
  - 将恢复所有主表字段到该版本状态
  - 不会自动影响关联的[N个发放记录]和[M个更改单]
  - 可能需要您手动处理这些关联项
  
  请确认回滚原因: [输入框]
  确定要执行回滚吗？
  ```

### 2. 关联项警告

显式回滚时，系统应显示关联项清单：

```
此档案记录关联的项目:
- [N个]发放记录 (详情)
- [M个]更改单 (详情)

回滚操作不会自动处理这些关联项。您可能需要单独审查并处理它们。
```

## 实现指南

系统实现回滚功能时应遵循以下原则：

1. **明确分离不同回滚类型的逻辑**
   - 隐式回滚和显式回滚应使用不同的服务类实现
   - 每种回滚类型应有清晰的责任边界

2. **使用命令模式封装回滚操作**
   - 将回滚逻辑封装在专用命令对象中
   - 确保命令对象包含执行回滚所需的所有信息
   - 实现操作历史记录和可能的重做功能

3. **跟踪并记录回滚链**
   - 系统应记录回滚操作之间的关系
   - 用户应能查看完整的回滚历史和链式关系

4. **优化性能考虑**
   - 对于大型记录，考虑增量回滚而非全量复制
   - 在高并发环境中使用适当的锁机制防止回滚冲突

## 总结

本文档定义了档案系统中回滚操作的核心规则和实现要求。通过区分隐式和显式回滚，并为每种类型制定明确的执行范围和处理规则，系统能够在保持数据完整性的同时提供灵活的回滚能力。回滚机制的正确实现对于确保档案管理系统的可靠性和数据准确性至关重要。
