# Operation Document: Implement Basic Fetch Retry Mechanism for API Calls

## 📋 Change Summary

**Purpose**: 在 `frontend/services/excel-import-service.ts` 中实现一个通用的 `fetchWithRetry` 辅助函数，为前端API调用提供自动重试能力，以增强在网络不稳定或服务器临时故障时的健壮性。初步将其应用于 `getActiveImportSession` 方法。
**Scope**: `frontend/services/excel-import-service.ts` 文件。
**Associated**: 对应《remaining_excel_import_refactor_plan.md》文档中任务 "一.2 会话管理与API优化" 的 "请求优化" -> "完善错误处理与重试机制" 子项。

## 🔧 Operation Steps

### 📊 OP-001: Design `fetchWithRetry` Helper Function

**Precondition**: 需要一个可配置的、能处理特定错误的fetch重试机制。
**Operation**:

1. 定义函数签名 `fetchWithRetry(url, options, retries, initialDelay, shouldRetryPredicate)`。
2. 实现重试循环，最大次数由 `retries` 控制。
3. 实现指数退避延迟 (`currentDelay *= 2`)。
4. 实现 `shouldRetryPredicate` 默认逻辑：重试网络错误 (TypeError "Failed to fetch") 和特定的服务器临时错误 (502, 503, 504)。
5. 确保在所有重试失败后抛出最后的错误。
6. 对于非重试错误（如4xx客户端错误，或非临时的5xx错误），直接返回响应或抛出错误，不进行重试。
**Postcondition**: `fetchWithRetry` 函数设计完成，包含核心重试逻辑。

### ✏️ OP-002: Implement `fetchWithRetry` and Apply to `getActiveImportSession`

**Precondition**: 函数设计完成。
**Operation**:

1. 将 `fetchWithRetry` 函数添加到 `frontend/services/excel-import-service.ts` 文件顶部（或合适的工具模块位置，当前暂放于service文件内）。
2. 修改 `ExcelImportService` 类中的 `getActiveImportSession` 方法，将其内部的 `fetch` 调用替换为对 `fetchWithRetry` 的调用。
**Postcondition**: `fetchWithRetry` 已实现，并初步应用于 `getActiveImportSession`。

## 📝 Change Details

### CH-001: Add `fetchWithRetry` and Apply to `getActiveImportSession`

**File**: `frontend/services/excel-import-service.ts`
**Content**: (CHANGE: [2025-05-16] 添加fetchWithRetry辅助函数并应用于getActiveImportSession)

```typescript
// ... (imports)

async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries: number = 3,
  initialDelay: number = 1000, // ms
  shouldRetryPredicate: (error: any, response?: Response) => boolean = (error, response) => {
    if (error && error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      return true; // 网络错误
    }
    if (response && [502, 503, 504].includes(response.status)) {
      return true; // 服务器临时错误
    }
    return false;
  }
): Promise<Response> {
  let attempt = 0;
  let currentDelay = initialDelay;
  while (attempt < retries) {
    try {
      const response = await fetch(url, options);
      if (response.ok || (response.status >= 400 && response.status < 500) || !shouldRetryPredicate(null, response)) {
        return response;
      }
      console.warn(`[fetchWithRetry] Attempt ${attempt + 1}/${retries} failed for ${url} with status ${response.status}. Retrying in ${currentDelay / 1000}s...`);
    } catch (error: any) {
      if (attempt + 1 >= retries || !shouldRetryPredicate(error)) {
        console.error(`[fetchWithRetry] All ${retries} attempts failed for ${url}. Last error:`, error);
        throw error;
      }
      console.warn(`[fetchWithRetry] Attempt ${attempt + 1}/${retries} failed for ${url} with error: ${error.message}. Retrying in ${currentDelay / 1000}s...`);
    }
    await new Promise(resolve => setTimeout(resolve, currentDelay));
    currentDelay *= 2;
    attempt++;
  }
  throw new Error(`[fetchWithRetry] All ${retries} attempts failed for ${url} after loop completion.`);
}

class ExcelImportService {
  // ...
  async getActiveImportSession(): Promise<ActiveImportSessionResponseData> {
    console.log('[SVC.GetActiveSession] Fetching active session from API...');
    let response: Response;
    try {
      response = await fetchWithRetry(`${this.baseApiUrl}/active-import-session/`, { // 使用 fetchWithRetry
        method: 'GET',
        headers: getAuthHeaders()
      });
    // ... (rest of the method)
  }
  // ...
}
```

**Rationale**: 为前端API调用提供自动重试能力，可以提高应用在面对暂时性网络问题或服务器端临时故障时的用户体验和成功率。
**Potential Impact**: 提高了 `getActiveImportSession` 方法的健壮性。后续可以将 `fetchWithRetry` 应用于服务层其他适合的API调用。

## ✅ Verification Results

**Method**: 代码创建和初步审查。
**Results**: `fetchWithRetry` 辅助函数已创建并初步应用于 `getActiveImportSession`。
**Problems**: `fetchWithRetry` 目前定义在service文件内部，未来可考虑移至更通用的工具模块。尚未应用于所有适合的API调用。
**Solutions**: 后续迭代中可以将此函数移至utils，并逐步推广到其他API调用。
