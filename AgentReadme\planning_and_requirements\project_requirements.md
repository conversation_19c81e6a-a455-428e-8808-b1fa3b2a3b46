# 档案流管理系统 - 核心需求文档

**重要标注**:

* **版本状态**: 本文档为 **草案 (Draft)** 版本，记录当前对需求的理解，**并非最终版本**。后续会根据讨论和开发进展进行修订。
* **计划同步**: 本文档描述的功能，特别是"报告发放流程"部分，**尚未完全同步**到详细工作计划 (`detailed_work_plan_and_log.md`) 中。具体任务将在后续规划中添加。
* **愿景同步**: 本文档内容**尚未同步更新**项目愿景与路线图文档 (`project_vision_and_roadmap.md`)。

## 1. 用户角色与权限

系统需支持以下主要用户角色，并具备相应的权限控制：

1. **系统管理员 (Admin)**:
    * 拥有系统最高权限。
    * 负责用户管理、角色分配、系统配置等。
2. **台账上传者 (Ledger Uploader)**:
    * 负责从第三方系统导出 Excel 格式的档案台账，并通过本系统上传导入。
    * 权限：访问 Excel 导入接口、查看导入日志。
3. **归档人 (Archiver)**:
    * 负责上传需要归档的 PDF 文件，并指定物理盒号。
    * 监控 PDF 处理任务状态。
    * （可能）处理 PDF 处理失败的情况（例如，手动关联编号、触发重试）。
    * 权限：访问 PDF 上传接口、查看任务状态、查看处理结果。
4. **报告发放人 (Dispatcher)**:
    * 负责根据台账记录生成发放单。
    * 管理发放单状态（待发放、已发放）。
    * 生成并打印领取确认单。
    * 更新台账中的发放相关字段。
    * （可选）上传扫描的领取确认单。
    * 权限：访问台账数据（查询）、管理发放单、更新特定台账字段、管理领取确认单。
5. **（系统外）领取人 (Recipient)**:
    * 非系统用户。
    * 在纸质领取确认单上签字。

*(注：权限细节待进一步细化)*  

## 2. 档案记录管理体系

### 2.1 档案记录处理流程

1. **数据来源**:
   * 档案记录数据由**第三方系统生成并导出为Excel文件**，可能包含大量条目。

2. **上传接口**:
   * 系统提供API端点，允许"台账上传者"上传Excel文件。

3. **文件解析与验证**:
   * 后端服务解析上传的Excel文件。
   * 系统验证数据格式，识别无效或格式错误的行。

4. **条目处理策略**:
   * 系统对导入数据进行初步分析，分类为：
     * **新条目**：检测到新的统一编号时。
     * **重复条目（内容完全一致）**：已存在且内容一致。
     * **重复条目（差异-已归档）**：已存在、已归档且内容有差异。
     * **重复条目（差异-未归档）**：已存在、未归档且内容有差异。

5. **即时确认机制**:
   * 系统完成初步分析后，向用户显示**差异汇总表**，包含：
     * 各类条目数量统计
     * 未归档差异条目的详细差异信息（可导出但不支持单项操作）
   * 用户对未归档差异条目进行**整体决策**：
     * 选项A：**接受所有更新**（更新所有未归档差异条目）
     * 选项B：**拒绝所有更新**（保留原记录，忽略所有差异）
   * 系统进行**二次确认**，确保用户明确操作后果。
   * 记录用户的整体决策、操作人和时间。

6. **批次管理**:
   * 每次导入生成唯一批次ID。
   * 提供多维度查询，支持按**操作人员**、**日期范围**、**处理结果**等筛选。

7. **导入日志**:
   * 记录每次导入的详细信息：
     * **元数据**：导入时间、操作用户、文件名等
     * **总体统计**：总条目数、新增数、各类重复条目数、错误数
     * **整体决策记录**：用户对未归档差异条目的处理决策
     * **详细记录**：可查询每条数据的处理结果和原因

8. **处理流程**:
   * **上传分析**：系统接收Excel文件并进行完整分析
   * **差异展示**：向用户展示差异统计和未归档差异详情
   * **整体决策**：用户做出整体决策并确认
   * **执行处理**：系统根据用户决策执行数据处理
   * **结果反馈**：显示最终处理结果统计

9. **界面要求**:
   * 提供清晰的差异汇总表格，支持导出
   * 显示醒目的整体决策按钮
   * 二次确认提示必须明确说明操作影响
   * 完整的批次查询和日志查询界面

### 2.2 已归档记录正式更改管理

**重要说明**: 本功能专门用于对**已完成归档**的档案条目进行正式变更。未归档条目的修改应通过档案记录处理流程（第2.1节）进行。

1. **更改单定义**:
    * 更改单是对已归档档案记录进行正式变更的业务凭证。
    * 每个更改单可关联多个档案条目，但只能针对已归档状态的条目。
    * 系统应阻止使用更改单修改未归档条目。

2. **更改单创建流程**:
    * **发起更改**:
        * 用户点击"创建更改单"按钮。
        * 系统提供更改单创建界面。
    * **填写信息与选择条目**:
        * 用户填写更改原因（必填字段）。
        * 用户选择需要修改的已归档档案条目（支持多选）。
        * 用户对每个选中条目指定要修改的字段和新值。
    * **更改单预览**:
        * 系统生成更改单预览，显示所有将要修改的内容。
        * 提供打印更改单功能，方便存档和审查。
    * **提交与执行**:
        * 用户确认后提交更改单。
        * 系统记录每个字段的变更前后值。
        * 系统执行实际数据修改。
        * 系统自动归档更改单并关联到相关条目。

3. **扫描件关联**:
    * 支持上传与更改单相关的审批扫描件或其他佐证文件。
    * 扫描件与更改单形成永久关联，便于日后查证。

4. **更改单删除与回滚**:
    * 支持删除已归档的更改单（需要特殊权限）。
    * 删除时系统自动回滚所有相关更改。
    * 向用户显示回滚的字段名及值变化。
    * 所有回滚操作也记入变更历史。

5. **权限控制**:
    * 创建更改单需要特定权限。
    * 删除更改单应限制为更高级别权限。
    * 系统应记录所有更改单相关操作的执行人。

6. **更改单管理界面**:
    * 提供更改单列表查看功能。
    * 支持按创建人、时间范围、关联条目等进行搜索和筛选。
    * 提供查看更改历史的详细视图。
    * 支持导出更改单明细报表。

### 2.3 操作错误修正机制

**功能定位**: 此功能专为修正操作过程中的人为错误或技术故障而设计，不应替代正式的更改单流程。

1. **适用场景**:
    * 数据录入操作失误
    * 系统故障导致的数据异常
    * 因技术问题造成的不正确更新

2. **错误修正流程**:
    * **启动修正**:
        * 用户在档案条目界面点击"错误修正"按钮。
        * 系统可能请求简要错误说明（可选）。
        * 系统解锁界面，允许原位编辑。
    * **执行修正**:
        * 用户直接修改需要更正的字段。
        * 系统跟踪所有字段的变更。
    * **提交与记录**:
        * 用户提交修正内容。
        * 系统自动标记为"操作错误修正"类型。
        * 系统记录完整的修正前后值和操作元数据。

3. **限制与控制**:
    * 错误修正不计入正式变更次数统计。
    * 可考虑对短时间内的错误修正次数设置限制。
    * 错误修正应有明确的操作日志，便于审计。

4. **权限管理**:
    * 错误修正功能应限定给特定角色或管理员。
    * 系统应记录谁执行了错误修正及修正时间。
    * 重要字段的错误修正可能需要更高级权限。

### 2.4 操作追溯与历史记录

1. **条目变更来源类型**:
    * **重要说明**: 以下列表仅为示例，实际实施前应对系统中所有可能的变更来源进行全面梳理并制定统一的命名规范。
    * **示例类型**:
        * `EXCEL_IMPORT`: Excel导入创建/更新
        * `ARCHIVE_PROCESS`: 档案入库更新
        * `ISSUE_FORM`: 发放单更新
        * `CHANGE_ORDER_CREATE`: 更改单创建修改
        * `OPERATION_ERROR`: 操作错误修正
        * `CHANGE_ORDER_DELETE`: 更改单删除回滚
        * `SYSTEM`: 系统自动操作

2. **变更历史记录模型**:
    * 系统应记录所有类型变更的完整历史。
    * 每条记录至少包含：
        * 变更时间
        * 操作人
        * 变更来源类型
        * 变更原因（如适用）
        * 变更的字段和前后值
        * 关联业务单据ID（如适用）

3. **正式变更统计**:
    * 系统自动计算每个档案条目的正式变更次数。
    * 只计算"CHANGE_ORDER_CREATE"类型且未被删除的更改。
    * 在条目修改或相关更改单删除时自动更新计数。

4. **历史查询功能**:
    * 在档案条目详情页提供"变更历史"选项卡。
    * 显示所有变更记录，使用不同颜色标识不同来源。
    * 提供按变更类型、时间范围、操作人的筛选功能。
    * 支持展开查看详细变更内容。

5. **导出功能**:
    * 支持导出档案条目的完整变更历史。
    * 提供多种导出格式（如Excel、PDF）。
    * 导出内容应包含足够的元数据以供审计使用。

6. **安全与审计**:
    * 所有历史记录不可手动删除，确保完整追溯链。
    * 变更历史查询应有权限控制。
    * 高级查询功能（如跨条目批量查询）应限制给管理员。
    * 应记录谁查询了变更历史以及查询时间（审计日志）。

## 3. PDF 处理与归档流程

1. **文件上传**:
    * "归档人"通过 API 端点 (`/api/upload/`) 上传单个 PDF 文件。
    * 每次上传**必须**同时指定该 PDF 文件最终物理归档的盒号 (`assigned_box_number`)。
2. **任务创建**:
    * 系统为每次成功的上传创建一个文件记录 (`UploadedFile`) 和一个处理任务 (`ProcessingTask`)。
    * `ProcessingTask` 记录处理参数，包括 `assigned_box_number`。
3. **后台异步处理 (`process_pdf_task`)**:
    * 任务被提交到 Celery 队列，由 Worker 进程异步执行。
4. **信息提取**:
    * 使用 `PdfProcessingService` 对 PDF 进行处理，提取关键信息：
        * 识别文档结构，确定分割点 (`split_points`)。
        * 识别每个部分的**统一编号 (`unified_number`)**。
        * 提取其他元数据或统计信息 (`stats`)。
5. **条目预检查与整体性检查 (Item Pre-check & Integrity Verification)**:
    * **基本原则**: 系统处理PDF文档时必须遵循严格的"全部或无"原则，任何验证失败或处理错误都必须导致整个处理流程失败。
    * **验证规则**:
        1. **编号识别完整性**: 所有识别出的部分都必须有关联的有效 `unified_number`。
        2. **数据库记录存在性**: 所有识别出的 `unified_number` 都必须能在 `ArchiveRecord` 表中找到对应的记录。
        3. **子任务完整性**: 所有启动的子任务必须全部成功完成，不允许存在丢失或状态不明的子任务。
        4. **处理结果完整性**: 子任务的结果数量必须与预期一致，确保没有缺失的处理结果。
    * **处理流程**:
        * **第一阶段（预检查）**: 在执行任何物理文件操作前，验证编号识别完整性和数据库记录存在性。若不满足，立即中止任务。
        * **第二阶段（子任务验证）**: 对于并行处理，在最终处理阶段前验证所有子任务状态和结果完整性。
        * **第三阶段（事务执行）**: 所有验证通过后，在单一事务中执行所有数据库更新操作。任何错误都会触发整个事务回滚。
    * **错误处理**:
        * 任何验证失败都必须记录详细错误原因（例如哪些编号未识别/未找到、哪些子任务失败）。
        * 系统必须提供完整的错误报告，方便问题诊断和解决。

6. **物理分割与归档 (仅当所有验证通过)**:
    * 根据 `split_points` 计算各部分的页面范围。
    * 使用 `pdf_utils` 将每个部分写入临时 PDF 文件。
    * 使用 `FileStorageService` 将临时文件移动到最终的、结构化的归档路径。
7. **关联与状态更新 (仅当所有验证通过)**:
    * 使用 `record_update_service.update_archive_record` 更新数据库中对应的 `ArchiveRecord` 条目：
        * 更新记录状态（例如，标记为"已归档"）。
        * 记录最终归档文件的路径 (URL)。
        * 记录该部分所属的物理盒号（使用上传时指定的 `assigned_box_number`）。
    * 更新 `ProcessingTask` 的最终状态和结果数据。
8. **结果查询与重试**:
    * 提供 API (`TaskStatusView`, `ProcessingResultView` - 待实现) 供用户查询任务状态和详细结果。
    * 提供 API (`RetryProcessingView` - 待实现) 供用户对处理失败的任务触发重试。

## 4. 报告发放流程

*(注意：此部分需求为新增，尚未列入详细工作计划)*  

1. **基础设定**:
    * 每个档案条目 (`ArchiveRecord`) 对应一定数量的待发放报告（通常为**三份**）。
    * 台账 (`ArchiveRecord`) 中需要有字段记录报告的发放状态（例如：待发放数量、已发放数量、最后发放日期、发放操作人等）。
2. **生成发放单**:
    * "报告发放人"需要能够根据特定条件（例如：项目名称、时间范围、特定统一编号列表等）从 `ArchiveRecord` 中**批量选择**需要发放报告的条目。
    * 系统根据选择的条目生成"发放单"。发放单应包含：
        * 发放单编号、生成日期、操作人。
        * 包含的档案条目列表（关键信息如统一编号、标题等）。
        * 每个条目本次计划发放的报告数量。
        * 领取部门/领取人信息栏（待填写）。
3. **发放模式**:
    * **模式一：一次性发放**: 一次性发放完所选条目对应的所有报告（例如 3 份）。
    * **模式二：分次发放**: 第一次发放一部分（例如 1 份），后续再发放剩余部分（例如 2 份）。系统需要支持记录每次发放的数量。
4. **生成领取确认单**:
    * 发放人根据发放单和实际发放情况，生成"领取确认单"。
    * 领取确认单应包含发放单的主要信息，并有明确的**领取人签字区域**。
    * 系统需支持**打印**领取确认单。
5. **领取与确认**:
    * 领取人（非系统用户）在**纸质**领取确认单上签字确认。
6. **状态更新与归档**:
    * 发放人根据签字的领取确认单，在系统中确认发放完成。
    * 系统**自动更新** `ArchiveRecord` 中对应条目的发放相关字段（已发放数量、状态、最后发放日期等）。
    * 发放人将签字的纸质领取确认单进行物理归档。
7. **（可选）扫描件关联**:
    * 系统（可选）提供功能，允许发放人上传签字后的领取确认单扫描件，并将其与对应的发放单或确认记录进行**电子关联**。

## 5. 测试要求

*(保留现有的测试要求部分)*  

1. **核心功能测试覆盖**
    * 所有重构后的核心功能模块都必须有对应的单元测试
    * 关键业务流程（如预检查、文件归档等）必须有集成测试覆盖
    * 测试应涵盖正常路径和异常路径

2. **测试环境配置**
    * 测试必须在不同操作系统（Windows和Linux）上都能正常运行
    * 测试应考虑Python版本兼容性（3.9-3.12）
    * 对于依赖问题（如OpenMP运行时错误），必须提供解决方案和文档

3. **测试文档**
    * 项目必须包含详细的测试运行指南
    * 测试文件应有清晰的注释，说明测试目的和验证点
    * 文档应包括测试编写的最佳实践
