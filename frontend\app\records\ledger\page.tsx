'use client'; // Mark this component as a Client Component

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Upload,
  RotateCcw,
  Calendar as CalendarIcon,
  X,
  Download
} from "lucide-react"
import Link from "next/link"
import { RecordsFilter } from "@/components/records/records-filter"
// TODO: Replace with NextAuth imports

import apiClient from "@/lib/apiClient" // 引入 API 客户端
import { TablePageLayout } from "@/components/common/table-page-layout"
import { format } from "date-fns"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useToast } from "@/components/ui/use-toast" // 引入toast通知
import ClientOnly from "@/components/common/ClientOnly"

import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { AgGridReact } from 'ag-grid-react';
import {
  ColDef,
  ColGroupDef,
  IServerSideDatasource,
  IServerSideGetRowsParams,
  GridApi,
  GridReadyEvent,
  GetContextMenuItemsParams,
  MenuItemDef,
  FirstDataRenderedEvent,
  AgGridEvent
} from 'ag-grid-enterprise';
import { LicenseManager, ModuleRegistry, AllEnterpriseModule } from 'ag-grid-enterprise';
import { themeQuartz } from 'ag-grid-enterprise';

// 导入 AG Grid 配置
import agGridConfig from '@/lib/ag-grid-config';

// CHANGE: [2025-05-13] 从 utils 导入 getHeaderColumnWidth
import { getHeaderColumnWidth } from '@/utils/ag-grid-column-width-utils';

// CHANGE: [2025-07-29] 导入新的日期工具和组件
import { getMonthsAgoStr, getTodayStr, formatDate, formatDateTime } from '@/utils/date-utils';
import { DateRangeFilter } from '@/components/common/DateRangeFilter';

// CHANGE: [2025-06-19] 导入统一的档案记录类型定义，替代本地重复定义
import { ArchiveRecord, ArchiveRecordPaginatedResponse } from '@/types/archive-record';

// 注释掉重复的模块注册代码 - 这已在app入口中通过initializeAgGrid处理
// registerAgGridModules(); // 如果通过import自动注册失效，可以取消此行注释手动注册

// CHANGE: [2025-05-13] 添加自定义样式初始化函数
// 为AG Grid添加自定义样式
const initCustomStyles = () => {
  const styleId = 'ag-grid-records-custom-styles';
  if (!document.getElementById(styleId)) {
    const styleElement = document.createElement('style');
    styleElement.id = styleId;
    styleElement.innerHTML = `
      /* 调整行号列宽度为30px，最小化CSS干预 */
      .ag-theme-alpine .ag-row-number-column,
      .ag-theme-alpine .ag-header-cell.ag-row-number-header,
      .ag-theme-quartz .ag-row-number-column,
      .ag-theme-quartz .ag-header-cell.ag-row-number-header {
        width: 30px !important;
        min-width: 30px !important;
        max-width: 30px !important;
        background-color: #f8f8f8;
        color: #666;
        text-align: center;
        font-weight: 500;
      }
      
      /* 修复行号列表头样式，移除中间的竖线 */
      .ag-theme-alpine .ag-header-cell.ag-row-number-header::after,
      .ag-theme-quartz .ag-header-cell.ag-row-number-header::after {
        display: none !important; /* 移除竖线 */
      }
      
      /* 确保分页控件样式正确 */
      .ag-theme-alpine .ag-paging-panel,
      .ag-theme-quartz .ag-paging-panel {
        height: 30px !important;
        min-height: 30px !important;
        align-items: center;
      }
    `;
    document.head.appendChild(styleElement);
  }
};

// Define a flexible interface for archive records
// interface ArchiveRecordData extends Record<string, any> {
//   // 1. 基础标识信息
//   id: number;
//   sample_number: string | null;
//   account_from_excel: string | null;
//   commission_number: string;
//   unified_number: string | null;
//   report_number: string | null;
//   province_unified_number: string | null;
//   station_code: string | null;
//   organization_code: string | null;
//
//   // 2. 项目与委托信息
//   project_number: string | null;
//   project_name: string;
//   sub_project: string | null;
//   project_location: string | null;
//   project_address: string | null;
//   client_unit: string;
//   client_name: string | null;
//   commission_datetime: string | null;
//
//   // 3. 试验与结果信息
//   test_start_datetime: string | null;
//   test_end_datetime: string | null;
//   test_person1: string | null;
//   test_person2: string | null;
//   data_entry_person: string | null;
//   test_result: string | null;
//   conclusion: string | null;
//   test_parameters: string | null;
//   unqualified_parameters: string | null;
//
//   // 4. 档案生命周期信息
//   archive_status: string | null;
//   change_count: number | null;
//   current_status: string | null;
//   processing_status: string | null;
//   archive_box_number: string | null;
//   archive_url: string | null;
//   attachments_from_excel: string | null;
//   storage_datetime: string | null;
//   storage_person: string | null;
//   outbound_datetime: string | null;
//   outbound_person: string | null;
//   archive_datetime: string | null;
//   archive_person: string | null;
//
//   // 5. 报告管理信息
//   report_issue_status: string | null;
//   first_issue_copies: number | null;
//   first_issue_datetime: string | null;
//   first_issue_person: string | null;
//   first_receiver_name: string | null;
//   first_receiver_unit: string | null;
//   first_receiver_phone: string | null;
//   second_issue_copies: number | null;
//   second_issue_datetime: string | null;
//   second_issue_person: string | null;
//   second_receiver_name: string | null;
//   second_receiver_unit: string | null;
//   second_receiver_phone: string | null;
//   total_issue_copies: number | null;
//
//   // 6. 样品信息
//   group_number: string | null;
//   sample_name: string | null;
//   assigned_person: string | null;
//   component_count: number | null;
//   test_point_count: number | null;
//   unqualified_point_count: number | null;
//   sample_retention_datetime: string | null;
//   sample_remaining_time: number | null;
//
//   // 7. 财务信息
//   payment_status: string | null;
//   price_adjustment_status: string | null;
//   standard_price: number | null;
//   discount_price: number | null;
//   actual_price: number | null;
//
//   // 8. 系统元数据
//   import_user: number | null;
//   import_user_name?: string | null;
//   import_date: string | null;
//   batch_number: string | null;
//   source_system: string | null;
//   created_at: string | null;
//   updated_at: string | null;
// }

// interface LedgerApiResponse { count: number; next: string | null; previous: string | null; results: ArchiveRecordData[]; }

// 创建自定义主题对象
const myTheme = themeQuartz.withParams({
  browserColorScheme: "light",
  headerFontSize: 14
});

export default function RecordsLedgerPage() {
  // TODO: Replace with useSession() hook
  const { toast } = useToast(); // 引入toast通知

  // 初始化为最近一个月的数据
  const defaultStartDate = getMonthsAgoStr(1);
  const defaultEndDate = getTodayStr();

  const [gridApi, setGridApi] = useState<GridApi<ArchiveRecord> | null>(null);
  const [columnState, setColumnState] = useState<any>(null);

  // State for Quick Filter
  const [quickFilterText, setQuickFilterText] = useState<string>('');
  // 内部状态跟踪是否正在进行筛选操作
  const [isFiltering, setIsFiltering] = useState(false);
  const filterTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 跟踪是否为首次加载
  const isFirstLoad = useRef(true);
  
  // 表格编辑模式状态
  const [editMode, setEditMode] = useState(false);

  // 使用默认日期初始化日期范围筛选
  // const [externalStartDate, setExternalStartDate] = useState<string>(defaultStartDate);
  // const [externalEndDate, setExternalEndDate] = useState<string>(defaultEndDate);
  // const [datePickerOpen, setDatePickerOpen] = useState<boolean>(false);

  // 跟踪数据源参数的最新状态，避免多次刷新
  const currentFiltersRef = useRef({
    quickFilter: '',
    startDate: defaultStartDate,
    endDate: defaultEndDate
  });

  // CHANGE: [2025-05-13] 初始化自定义样式
  // 应用AG Grid自定义样式
  useEffect(() => {
    initCustomStyles();
    
    // 清理函数（可选）
    return () => {
      const styleElement = document.getElementById('ag-grid-records-custom-styles');
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, []);

  // 日期选择器相关逻辑 - 完全重写，彻底分离UI和数据操作

  // 切换日期选择器 - 纯UI操作，不影响数据
  // const toggleDatePicker = useCallback((e: React.MouseEvent) => {
  // e.preventDefault();
  // e.stopPropagation();
  // setDatePickerOpen(prev => !prev);
  // }, []);

  // 关闭日期选择器 - 纯UI操作
  // const closeDatePicker = useCallback(() => {
  // setDatePickerOpen(false);
  // }, []);

  // 应用日期筛选 - 只有在显式调用时才执行
  const applyDateFilter = useCallback((startDate: string, endDate: string) => {
    // 检查是否与当前筛选条件相同，避免重复刷新
    if (startDate === currentFiltersRef.current.startDate &&
      endDate === currentFiltersRef.current.endDate) {
      return; // 如果没有变化，不执行任何操作
    }

    // 更新UI相关的状态 (用于显示在按钮上)
    // setExternalStartDate(startDate); // 由 DateRangeFilter 组件内部管理显示
    // setExternalEndDate(endDate); // 由 DateRangeFilter 组件内部管理显示

    // 更新将用于API请求的ref
    currentFiltersRef.current.startDate = startDate;
    currentFiltersRef.current.endDate = endDate;

    // 只有在这里，才真正触发服务端数据刷新
    if (gridApi) {
      // 刷新表格数据，fetchArchiveLedgerRecords 会从 currentFiltersRef 读取日期
      gridApi.refreshServerSide({ purge: true });
    }
  }, [gridApi]);

  // 加载保存的列状态
  useEffect(() => {
    try {
      const savedState = localStorage.getItem('recordsGridColumnState');
      // DEBUG: Log the raw state string from localStorage
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        // DEBUG: Log the parsed state object
        setColumnState(parsedState);
      } else {
        // console.log("[AG Grid] No saved column state found, will show all columns by default");
      }
    } catch (error) {
      console.error('Error loading saved column state:', error);
    }
  }, []);

  // Save column state changes
  // DEBUG: Renamed to avoid conflict with AG Grid event listener registration which uses the same name internally sometimes.
  const saveColumnStateToLocalStorage = (event: AgGridEvent<ArchiveRecord, any>) => { 
    // DEBUG: Log the type of event received
    if (event && event.api) { // Check if event and api exist
      const gridApiInstance = event.api; // Get API from event
      const columnState = gridApiInstance.getColumnState();
      // DEBUG: Log the state being saved
      if (columnState && columnState.length > 0) {
        localStorage.setItem('recordsGridColumnState', JSON.stringify(columnState)); 
        // console.log('[AG Grid] 列状态已保存到localStorage');
      } else {
         // console.warn("[DEBUG] Attempted to save empty or invalid column state."); 
      }
    } else {
        // console.warn("[DEBUG] event.api not available in saveColumnStateToLocalStorage event handler."); 
    }
  }; // DEBUG: Removed useCallback and gridApi dependency. Get api from event object.

  // Clear saved column state
  const clearSavedColumnState = useCallback(() => {
    // DEBUG: Log gridApi value when the function is called
    // console.warn("[DEBUG] Executing clearSavedColumnState, gridApi is:", gridApi);
    // 从localStorage删除保存的状态
    localStorage.removeItem('recordsGridColumnState');

    if (gridApi) {
      // console.log('[AG Grid] 正在重置列状态...');

      // Primary action: Reset column state to initial definitions
      gridApi.resetColumnState();
      // console.log('[AG Grid] 列状态已通过 resetColumnState() 重置');
      
      // The following code for manually constructing resetState can be removed
      // as resetColumnState() is more comprehensive.
      /*
      const allColumns = gridApi.getColumns();
      if (allColumns && allColumns.length > 0) {
        const resetState = allColumns.map(column => ({
          colId: column.getColId(),
          hide: false,
          pinned: null // Ensure pinned state is reset
        }));

        gridApi.applyColumnState({
          state: resetState,
          defaultState: { hide: false, pinned: null }, // Ensure default also unpins
          applyOrder: true // Keep true if order reset is also desired, else consider
        });

        // console.log('[AG Grid] 列状态已重置，所有列现在都可见且未固定');
      } else {
        // console.warn('[AG Grid] 无法获取列信息，尝试备用重置');
        // Fallback if needed, but resetColumnState should be primary
        gridApi.resetColumnState(); 
      }
      */

      // 通知用户
      toast({
        title: "列配置已重置",
        description: "所有列已恢复默认显示和固定状态", // Updated message
        duration: 3000
      });
    } else {
      // console.warn("[DEBUG] gridApi not available in clearSavedColumnState."); 
    }
  }, [gridApi, toast]);

  // Date comparators and formatters
  const dateComparator = (filterLocalDateAtMidnight: Date, cellValue: string | null): number => {
    if (cellValue == null) return -1;
    const cellDate = new Date(cellValue);
    const filterDateOnly = new Date(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate());
    const cellDateOnly = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());
    if (filterDateOnly.getTime() === cellDateOnly.getTime()) return 0;
    return cellDateOnly < filterDateOnly ? -1 : 1;
  };

  // Date formatter
  const dateFormatter = (params: any) => params.value ? new Date(params.value).toLocaleDateString() : '';

  // Date time formatter
  const dateTimeFormatter = (params: any) => params.value ? formatDateTime(params.value) : '';

  // Date filter params
  const dateFilterParams = {
    comparator: dateComparator,
    browserDatePicker: true,
    minValidYear: 2000,
    maxValidYear: 2050,
    inRangeFloatingFilterType: 'greaterThan',
  };

  // 清理防抖计时器
  useEffect(() => {
    return () => {
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, []);

  // Update fetchArchiveLedgerRecords to handle default date range and quick filter
  const fetchArchiveLedgerRecords = useCallback(async (
    startRow: number,
    endRow: number,
    filterModel: any,
    sortModel: any
  ): Promise<{ rows: ArchiveRecord[], lastRow: number }> => {
    const queryParams = new URLSearchParams();

    // 1. 首先添加分页参数
    queryParams.append('limit', (endRow - startRow).toString());
    queryParams.append('offset', startRow.toString());

    // 使用当前筛选参数引用中的最新值，避免闭包问题
    const currentFilters = currentFiltersRef.current;

    // 2. 然后添加日期范围筛选 - 优先级最高
    if (currentFilters.startDate) {
      queryParams.append('commission_datetime__gte', currentFilters.startDate);
      // console.log('[筛选器] 开始日期:', currentFilters.startDate);
    }
    if (currentFilters.endDate) {
      queryParams.append('commission_datetime__lte', currentFilters.endDate);
      // console.log('[筛选器] 结束日期:', currentFilters.endDate);
    }

    // 3. 再添加列筛选 - 作为JSON字符串传递
    if (filterModel && Object.keys(filterModel).length > 0) {
      queryParams.append('filter_model', JSON.stringify(filterModel));
      // console.log('[筛选器] 发送列筛选模型:', JSON.stringify(filterModel, null, 2));
    }

    // 4. 最后添加快速筛选参数 - 由于处理最复杂，放在最后
    if (currentFilters.quickFilter && currentFilters.quickFilter.trim() !== '') {
      queryParams.append('q', currentFilters.quickFilter.trim());
      // console.log('[筛选器] 快速筛选:', currentFilters.quickFilter);
    }

    // 5. 排序参数处理 - 增强健壮性
    if (sortModel && sortModel.length > 0) {
      // 详细记录排序信息
      // console.log('[排序] 处理排序请求:', JSON.stringify(sortModel, null, 2));

      try {
        // 过滤无效排序项，确保colId存在且非空
        const sortParams = sortModel
          .filter((s: { colId?: string; sort?: string }) => s && s.colId)
          .map((s: { colId: string; sort: string }) => (s.sort === 'desc' ? '-' : '') + s.colId)
          .join(',');

        if (sortParams) {
          queryParams.append('ordering', sortParams);
          // console.log('[排序] 发送排序参数:', sortParams);
        }
      } catch (error) {
        console.error('[排序] 处理排序参数时出错:', error);
        // 继续执行，不中断请求流程
      }
    }

    // 构建API请求URL
    const endpoint = `/api/archive-records/records/?${queryParams.toString()}`;

    try {
      // console.log(`[Records] 发送请求: ${endpoint}`);
      const response = await apiClient.get<ArchiveRecordPaginatedResponse>(endpoint);

      if (!response.success || !response.data) {
        // console.error(`[Records] API错误: ${response.error || '未收到数据'}`);
        throw new Error(response.error || '获取数据失败');
      }

      const data = response.data;
      // console.log(`[Records] 成功: 收到${data.results.length}条记录，总计${data.count}条`);
      return { rows: data.results, lastRow: data.count };
    } catch (error) {
      // console.error('[Records] 请求失败:', error);
      throw error;
    }
  }, []);

  // Server-side datasource
  const createServerSideDatasource = useCallback((): IServerSideDatasource => {
    return {
      getRows: async (params: IServerSideGetRowsParams) => {
        // console.log('[ServerSide] Request params:', params.request);
        
        // 显示加载状态
        if (isFirstLoad.current) {
          // 考虑添加加载指示器
          isFirstLoad.current = false;
        }
        
        try {
          const result = await fetchArchiveLedgerRecords(
            params.request.startRow!,
            params.request.endRow!,
            params.request.filterModel,
            params.request.sortModel
          );
          
          // 直接调用success，不使用setTimeout
          params.success({ 
            rowData: result.rows, 
            rowCount: result.lastRow 
          });
        } catch (error) {
          // console.error('[ServerSide] Error fetching rows:', error);
          params.fail();
        }
      }
    };
  }, [fetchArchiveLedgerRecords]);

  // Memoize the datasource object itself to ensure a stable reference for the prop
  const memoizedServerSideDatasource = useMemo(() => createServerSideDatasource(), [createServerSideDatasource]);

  // 创建列定义分组
  const columnDefs = useMemo<(ColDef<ArchiveRecord> | ColGroupDef<ArchiveRecord>)[]>(() => {
    const processCols = (cols: (ColDef<ArchiveRecord> | ColGroupDef<ArchiveRecord>)[]): (ColDef<ArchiveRecord> | ColGroupDef<ArchiveRecord>)[] => {
      return cols.map(col => {
        if ((col as ColGroupDef<ArchiveRecord>).children) {
          return {
            ...col,
            children: processCols((col as ColGroupDef<ArchiveRecord>).children!)
          };
        }
        const colDef = col as ColDef<ArchiveRecord>;
        if (colDef.headerName && !colDef.width && !colDef.flex) {
          const isDateCol = colDef.headerName?.includes("日期") || colDef.headerName?.includes("时间");
          const isStatusCol = colDef.headerName?.includes("状态");
          
          let customOptions: Parameters<typeof getHeaderColumnWidth>[1] = {
            isSpecialCase: isDateCol || isStatusCol,
          };

          if (colDef.headerName === "委托编号" || colDef.headerName === "样品编号" || colDef.headerName === "报告编号" || colDef.headerName === "统一编号") {
            customOptions.padding = 86;
          } else if (colDef.headerName === "Excel文件名" || colDef.headerName === "工程地址" || colDef.headerName === "第一次领取单位" || colDef.headerName === "第二次领取单位") {
            customOptions.padding = 60;
          }
          
          colDef.width = getHeaderColumnWidth(colDef.headerName, customOptions);
        }
        return colDef;
      });
    };

    const initialDefs: (ColDef<ArchiveRecord> | ColGroupDef<ArchiveRecord>)[] = [
      // 1. 基础标识信息组
      {
        headerName: '基础标识',
        children: [
          { headerName: "样品编号", field: "sampleNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "委托编号", field: "commissionNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "统一编号", field: "unifiedNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "报告编号", field: "reportNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "省统一编号", field: "provinceUnifiedNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "站点编号", field: "stationCode", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "机构代号", field: "organizationCode", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "账号", field: "accountFromExcel", sortable: true, filter: 'agTextColumnFilter' }, // 已有 width: 100，但现在移除，让它自动计算
        ]
      },
      // 2. 项目与委托信息组
      {
        headerName: '项目与委托',
        children: [
          { headerName: "工程名称", field: "projectName", minWidth: 150, sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "委托单位", field: "clientUnit", minWidth: 150, sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "工程编号", field: "projectNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "分项工程", field: "subProject", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "工程部位", field: "projectLocation", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "工程地址", field: "projectAddress", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "委托人", field: "clientName", sortable: true, filter: 'agTextColumnFilter' },
          {
            headerName: "委托日期",
            field: "commissionDatetime",
            sortable: true,
            filter: false, // 注意这里 filter:false，如果也想应用日期筛选UI，需要调整
            valueFormatter: dateTimeFormatter,
            // width: 120, // 移除，让其自动计算
          },
        ]
      },
      // 3. 试验与结果信息组
      {
        headerName: '试验与结果',
        children: [
          { headerName: "检测结果", field: "testResult", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "结论", field: "conclusion", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "检测参数", field: "testParameters", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "不合格参数", field: "unqualifiedParameters", sortable: true, filter: 'agTextColumnFilter' },
          {
            headerName: "测试开始日期",
            field: "testStartDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          {
            headerName: "测试结束日期",
            field: "testEndDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "试验人1", field: "testPerson1", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "试验人2", field: "testPerson2", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "数据录入人", field: "dataEntryPerson", sortable: true, filter: 'agTextColumnFilter' },
        ]
      },
      // 4. 档案生命周期信息组
      {
        headerName: '档案流转状态',
        children: [
          { headerName: "归档状态", field: "archiveStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "档案盒号", field: "archiveBoxNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "当前数据状态", field: "currentStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "待处理状态", field: "processingStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "更改次数", field: "changeCount", sortable: true, filter: 'agNumberColumnFilter' },
          { headerName: "档案URL链接", field: "archiveUrl", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "附件", field: "attachmentsFromExcel", sortable: true, filter: 'agTextColumnFilter' },
          {
            headerName: "入库日期",
            field: "storageDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "入库人", field: "storagePerson", sortable: true, filter: 'agTextColumnFilter' },
          {
            headerName: "出库日期",
            field: "outboundDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "出库人", field: "outboundPerson", sortable: true, filter: 'agTextColumnFilter' },
          {
            headerName: "归档日期",
            field: "archiveDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "归档人", field: "archivePerson", sortable: true, filter: 'agTextColumnFilter' },
        ]
      },
      // 5. 报告管理信息组
      {
        headerName: '报告管理',
        children: [
          { headerName: "报告发放状态", field: "reportIssueStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第一次发放份数", field: "firstIssueCopies", sortable: true, filter: 'agNumberColumnFilter' },
          {
            headerName: "第一次发放日期",
            field: "firstIssueDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "第一次发放人", field: "firstIssuePerson", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第一次领取人", field: "firstReceiverName", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第一次领取单位", field: "firstReceiverUnit", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第一次领取人电话", field: "firstReceiverPhone", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第二次发放份数", field: "secondIssueCopies", sortable: true, filter: 'agNumberColumnFilter' },
          {
            headerName: "第二次发放日期",
            field: "secondIssueDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "第二次发放人", field: "secondIssuePerson", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第二次领取人", field: "secondReceiverName", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第二次领取单位", field: "secondReceiverUnit", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "第二次领取人电话", field: "secondReceiverPhone", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "总发放份数", field: "totalIssueCopies", sortable: true, filter: 'agNumberColumnFilter' },
        ]
      },
      // 6. 样品信息组
      {
        headerName: '样品信息',
        children: [
          { headerName: "组号", field: "groupNumber", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "样品/项目名称", field: "sampleName", minWidth: 150, sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "分配人", field: "assignedPerson", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "构件(桩)数", field: "componentCount", sortable: true, filter: 'agNumberColumnFilter' },
          { headerName: "测点数", field: "testPointCount", sortable: true, filter: 'agNumberColumnFilter' },
          { headerName: "不合格点数", field: "unqualifiedPointCount", sortable: true, filter: 'agNumberColumnFilter' },
          {
            headerName: "样品留样时间",
            field: "sampleRetentionDatetime",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams
          },
          { headerName: "样品剩余时间(天)", field: "sampleRemainingTime", sortable: true, filter: 'agNumberColumnFilter' },
        ]
      },
      // 7. 财务信息组
      {
        headerName: '财务信息',
        children: [
          { headerName: "付款状态", field: "paymentStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "价格调整状态", field: "priceAdjustmentStatus", sortable: true, filter: 'agTextColumnFilter' },
          { headerName: "标准价格费用", field: "standardPrice", sortable: true, filter: 'agNumberColumnFilter' },
          { headerName: "折扣价格费用", field: "discountPrice", sortable: true, filter: 'agNumberColumnFilter' },
          { headerName: "实际价格费用", field: "actualPrice", sortable: true, filter: 'agNumberColumnFilter' },
        ]
      },
      // 8. 系统元数据组
      {
        headerName: '系统信息',
        children: [
          { headerName: "导入人", field: "importUserName", sortable: true, filter: 'agTextColumnFilter', editable: false },
          {
            headerName: "导入时间",
            field: "importDate",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams,
            editable: false
          },
          { headerName: "批次号", field: "batchNumber", sortable: true, filter: 'agTextColumnFilter', editable: false },
          { headerName: "数据来源系统", field: "sourceSystem", sortable: true, filter: 'agTextColumnFilter', editable: false },
          {
            headerName: "创建时间",
            field: "createdAt",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams,
            editable: false
          },
          {
            headerName: "更新时间",
            field: "updatedAt",
            sortable: true,
            filter: 'agDateColumnFilter',
            valueFormatter: dateTimeFormatter,
            filterParams: dateFilterParams,
            editable: false
          },
        ]
      }
    ];
    return processCols(initialDefs);
  }, []);

  // 将 sideBar 配置提取到 useMemo 以确保引用稳定
  const sideBarConfig = useMemo(() => agGridConfig.sideBarConfig, []);

  // Default ColDef for AG Grid
  const defaultColDef = useMemo<ColDef>(() => ({
    ...agGridConfig.defaultColDef,
    // CHANGE: [2025-05-12] 移除全局 flex:1 (如果存在于 agGridConfig.defaultColDef)，确保计算宽度生效
    // flex: undefined, // 或者确保 agGridConfig.defaultColDef 中没有 flex:1
    resizable: true, // 确保用户可以调整列宽
    // 简化单元格渲染器，优先使用值格式化
    cellRenderer: undefined, // 除非必要，不使用自定义单元格渲染器
    valueFormatter: (params) => {
      // CHANGE: [2025-06-20] 针对日期字段使用值格式化器而不是单元格渲染器
      if (params.colDef.type === 'dateColumn' && params.value) {
        return formatDate(params.value);
      }
      return params.value;
    },
    // 编辑器相关配置
    editable: false, // 默认不可编辑，由编辑模式按钮控制
    cellEditor: 'agTextCellEditor', // 使用默认文本编辑器
    cellEditorPopup: false, // 内嵌编辑器而非弹出式
    // 编辑器参数
    cellEditorParams: {
      // 可根据需要配置编辑器参数
    },
    // 停止编辑时的验证
    valueSetter: (params) => {
      // 如果值没有变化，直接返回
      if (params.newValue === params.oldValue) {
        return false;
      }
      
      // 设置新值
      params.data[params.colDef.field || ''] = params.newValue;
      
      // 可以在这里添加更新到服务器的逻辑
      // console.log('值已更新:', {
      //   field: params.colDef.field,
      //   oldValue: params.oldValue,
      //   newValue: params.newValue,
      //   row: params.data
      // });
      
      return true; // 返回true表示值已更新
    }
  }), []);

  // 在组件渲染时添加列状态变更监听
  const onFirstDataRendered = useCallback((params: FirstDataRenderedEvent<ArchiveRecord>) => {
    // console.log('[AG Grid] 首次数据渲染完成');
    // DEBUG: Remove event listeners from here, move to onGridReady
    /*
    if (params.api) {
      // DEBUG: Use the renamed saving function
      // 添加列状态变更监听 - 使用AG Grid支持的事件
      params.api.addEventListener('columnResized', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnMoved', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnVisible', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnPinned', saveColumnStateToLocalStorage);
      console.log("[DEBUG] Event listeners for column state saving added in onFirstDataRendered.");
    }
    */
    // console.log("[DEBUG] Event listeners are now moved to onGridReady.");
  }, []);

  const onGridReady = useCallback((params: GridReadyEvent<ArchiveRecord>) => {
    setGridApi(params.api);
    // console.log('[AG Grid] Grid is ready and APIs are set!');

    if (params.api) {
      // 创建服务端数据源
      const dataSource = createServerSideDatasource();
      params.api.setGridOption('serverSideDatasource', dataSource);

      // 恢复列状态，提供详细日志
      // DEBUG: Log the state *before* attempting to apply it
      // console.log("[DEBUG] columnState in onGridReady (before apply):", columnState); 
      if (columnState) {
        // console.log('[AG Grid] Applying saved column state', columnState);
        try {
          const applyResult = params.api.applyColumnState({ // DEBUG: Capture result
            state: columnState,
            applyOrder: true,
            defaultState: { hide: false } // 确保没有明确隐藏的列都显示
          });
          // DEBUG: Log the result of applying the state
          // console.log('[DEBUG] applyColumnState result:', applyResult); 
          // console.log('[AG Grid] Successfully applied column state');
        } catch (err) {
           // DEBUG: Log error during apply
          // console.error('[DEBUG] Error applying column state in onGridReady:', err);
          console.error('[AG Grid] Error applying column state:', err);
        }
      } else {
        // console.log('[AG Grid] No saved column state found, showing default columns');
      }

      // 添加列状态变更监听器
      // DEBUG: It's generally recommended to add listeners either in onGridReady OR onFirstDataRendered, not both.
      // Let's keep them in onFirstDataRendered for now as it worked before conceptually.
      // Commenting out the listeners here to avoid duplication.
      /*
      params.api.addEventListener('columnVisible', saveColumnState);
      params.api.addEventListener('columnResized', saveColumnState);
      params.api.addEventListener('columnMoved', saveColumnState);
      params.api.addEventListener('columnPinned', saveColumnState);
      */
      // DEBUG: Re-activate listeners here and ensure they use the correct save function.
      // console.log("[DEBUG] Adding event listeners in onGridReady.");
      params.api.addEventListener('columnVisible', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnResized', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnMoved', saveColumnStateToLocalStorage);
      params.api.addEventListener('columnPinned', saveColumnStateToLocalStorage);
      // console.log("[DEBUG] Listeners in onGridReady are commented out to avoid duplication.");

      // 确认初始筛选状态已正确设置
      // console.log('[AG Grid] Initializing with date range:', {
      //   start: currentFiltersRef.current.startDate,
      //   end: currentFiltersRef.current.endDate
      // });
    } else {
      console.error('[AG Grid] Grid API not available at onGridReady');
    }
  }, [createServerSideDatasource, columnState]); // DEBUG: Removed saveColumnStateToLocalStorage from dependency array as it's stable

  // 快速筛选实现 - 使用官方推荐模式
  const quickFilterRef = useRef<HTMLInputElement>(null);

  // 更新AG Grid的快速筛选
  const onQuickFilterChanged = useCallback(() => {
    if (gridApi && quickFilterRef.current) {
      const value = quickFilterRef.current.value;

      // 如果值与当前筛选条件相同，不进行任何操作
      if (value === currentFiltersRef.current.quickFilter) {
        return;
      }

      // 设置标志位并更新状态
      setIsFiltering(true);
      setQuickFilterText(value);

      // 直接更新筛选条件引用，避免重复请求
      currentFiltersRef.current.quickFilter = value;

      // console.log('[AG Grid] 快速筛选值已更新 (不通过setGridOption):', value);

      // 刷新服务端数据
      gridApi.refreshServerSide({ purge: true });

      // 完成筛选后重置标志位
      setIsFiltering(false);
    }
  }, [gridApi, setQuickFilterText]);

  // 使用防抖处理输入
  const onInputBoxChanged = useCallback(() => {
    // 清除现有计时器
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }

    // 设置新的延迟执行
    filterTimeoutRef.current = setTimeout(() => {
      onQuickFilterChanged();
    }, 300); // 使用官方推荐的300ms防抖时间
  }, [onQuickFilterChanged]);

  // 解决右键菜单类型问题 
  const getContextMenuItems = useCallback((params: GetContextMenuItemsParams<ArchiveRecord>) => {
    // AG Grid 31+ 类型定义
    const menuItems: (string | MenuItemDef<ArchiveRecord>)[] = [
      'copy',
      'copyWithHeaders',
      'separator',
      {
        name: '导出选中行 (CSV)',
        action: () => {
          if (params.api) {
            params.api.exportDataAsCsv({
              onlySelected: true,
              fileName: 'selected_export.csv'
            });
          }
        }
      },
      {
        name: '导出全部可见 (CSV)',
        action: () => {
          if (params.api) {
            params.api.exportDataAsCsv({
              fileName: `档案台账_${new Date().toISOString().slice(0, 10)}.csv`
            });
          }
        }
      },
      'separator',
      {
        name: '刷新数据',
        action: () => {
          if (params.api) {
            // 使用 refreshServerSide 刷新数据
            params.api.refreshServerSide({ purge: true });
            // console.log('[AG Grid] Refreshed server-side data');
          }
        }
      }
    ];
    return menuItems as any; // 类型转换以兼容 AG Grid 接口
  }, []);

  // 添加排序变更事件处理函数
  const onSortChanged = useCallback(() => {
    // 仅记录排序变更，AG Grid的ServerSideRowModel会自动处理后续刷新
    if (gridApi) {
      const columnState = gridApi.getColumnState();
      const activeSorts = columnState
        .filter(column => column.sort)
        .map(column => ({
          colId: column.colId,
          sort: column.sort
        }));

      // console.log('[AG Grid] 排序已变更:', JSON.stringify(activeSorts, null, 2));
      // 无需手动刷新 - ServerSideRowModel会自动处理排序和数据请求
    }
  }, [gridApi]);

  // 优化表格组件性能配置 - 抽取为变量以避免内联对象
  const gridPerformanceConfig = useMemo(() => ({
    // 应用所有性能优化设置
    ...agGridConfig.performanceConfig,
    
    // 针对当前表格的特定优化
    pagination: false, // 服务器端模型不需要客户端分页
    cacheBlockSize: agGridConfig.serverSideDefaults.cacheBlockSize,
    maxBlocksInCache: agGridConfig.serverSideDefaults.maxBlocksInCache
  }), []);

  // 设置单元格是否可编辑
  const onCellEditingStarted = useCallback((params: any) => {
    // console.log('Cell editing started', params);
  }, []);

  // 单元格编辑结束事件处理
  const onCellEditingStopped = useCallback(async (event: any) => {
    // 如果值没有变化，不执行任何操作
    if (event.newValue === event.oldValue) {
      return;
    }
    
    // 使用camelCase字段名
    const fieldName = event.colDef.field;
    const patchData = { [fieldName]: event.newValue };

    try {
      const response = await apiClient.patch(`/api/archive-records/records/${event.data.id}/`, patchData);

      if (response.success) {
        toast({
          title: "更新成功",
          description: `记录 ${event.data.commissionNumber} 已更新。`,
          className: "bg-green-100 text-green-800",
        });
        
        // 更新前端缓存中的数据
        const updatedRowNode = gridApi?.getRowNode(String(event.data.id));
        if (updatedRowNode) {
          // 使用 camelCase 更新
          const updatedData = { ...updatedRowNode.data, ...response.data };
          updatedRowNode.setData(updatedData);
        }
      } else {
        toast({
          title: "更新失败",
          description: response.error || `保存记录时出错`,
          variant: "destructive",
        });
        // 还原单元格的值
        if (event.colDef.field) {
          const updatedRowNode = gridApi?.getRowNode(String(event.data.id));
          if (updatedRowNode) {
            updatedRowNode.setDataValue(event.colDef.field, event.oldValue);
          }
        }
      }
    } catch (error) {
      console.error('更新数据时出错:', error);
      toast({
        title: "更新失败",
        description: "发生错误，无法保存更改",
        duration: 5000,
        variant: "destructive"
      });
    }
  }, [gridApi, toast]);

  // 切换表格编辑模式
  const toggleEditMode = useCallback(() => {
    setEditMode(prevMode => !prevMode);
    
    if (gridApi) {
      // 更新所有列的editable属性
      const newEditable = !editMode;
      const colDefs = gridApi.getColumnDefs();
      
      if (colDefs) {
        // 更新所有列定义的editable属性
        const updatedColDefs = colDefs.map((colDef: any) => {
          // 如果是列组，更新子列
          if (colDef.children) {
            // 特别处理"系统信息"列组
            if (colDef.headerName === '系统信息') {
              return {
                ...colDef,
                children: colDef.children.map((child: any) => ({
                  ...child,
                  editable: false // "系统信息"下的列始终不可编辑
                }))
              };
            } else {
              // 其他列组，按原有逻辑处理其子列
              return {
                ...colDef,
                children: colDef.children.map((child: any) => {
                  // 委托编号和统一编号不可编辑
                  if (child.field === 'commissionNumber' || child.field === 'unifiedNumber') {
                    return {
                      ...child,
                      editable: false // 这两个字段始终不可编辑
                    };
                  }
                  return {
                    ...child,
                    editable: newEditable // 其他子列根据编辑模式切换
                  };
                })
              };
            }
          }
          
          // 普通列 - 对于委托编号和统一编号，始终设置为不可编辑
          if (colDef.field === 'commissionNumber' || colDef.field === 'unifiedNumber') {
            return {
              ...colDef,
              editable: false
            };
          }
          
          // 其他普通列
          return {
            ...colDef,
            editable: newEditable
          };
        });
        
        // 应用更新后的列定义 - 使用正确的API
        gridApi.setGridOption('columnDefs', updatedColDefs);
        
        // 通知用户
        toast({
          title: newEditable ? "更正编辑模式已启用" : "更正编辑模式已禁用",
          description: newEditable ? "点击单元格可以编辑内容（委托编号和统一编号除外）" : "表格已恢复为只读模式",
          duration: 3000
        });
      }
    }
  }, [gridApi, editMode, toast]);

  // 页面布局和Grid渲染
  return (
    <TablePageLayout
      title="档案台账"
      subtitle="查看和管理所有档案记录"
      actions={[
        {
          label: "导出台账",
          icon: <Download className="h-4 w-4" />,
          variant: "outline",
          onClick: () => {
            if (gridApi) {
              gridApi.exportDataAsCsv({
                fileName: `档案台账_${new Date().toISOString().slice(0, 10)}.csv`,
                columnKeys: gridApi.getColumns()
                  ?.filter(col => col.isVisible())
                  ?.map(col => col.getColId()) || [],
                processCellCallback: (params) => {
                  // 处理导出数据的特殊字段格式化
                  if (params.column.getColDef().type === 'dateColumn' && params.value) {
                    return formatDate(params.value);
                  }
                  return params.value;
                }
              });
              toast({
                title: "导出开始",
                description: "档案台账数据正在导出为CSV文件",
                duration: 3000
              });
            }
          }
        },
        {
          label: "导入台账",
          icon: <Upload className="h-4 w-4" />,
          href: "/records/import",
          variant: "default"
        }
      ]}
    >
      {/* 上方控制区域 */}
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            {/* 自定义搜索框实现，修复样式问题 */}
            <div className="relative w-60">
              <input
                ref={quickFilterRef}
                type="text"
                placeholder="快速搜索..."
                value={quickFilterText}
                onChange={(e) => {
                  setQuickFilterText(e.target.value);
                  onInputBoxChanged();
                }}
                className="w-full h-9 px-3 py-2 text-sm bg-background border border-input rounded-md focus-visible:outline-none focus-visible:border-primary"
              />
              {isFiltering && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-opacity-50 border-t-primary rounded-full" />
                </div>
              )}
            </div>

            {/* 使用新的 DateRangeFilter 组件 */}
            <DateRangeFilter
              initialStartDate={defaultStartDate}
              initialEndDate={defaultEndDate}
              onApplyFilter={applyDateFilter}
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={clearSavedColumnState}
          >
            <RotateCcw className="h-3.5 w-3.5 mr-1" />
            重置列配置
          </Button>
          
          <Button
            variant={editMode ? "destructive" : "outline"}
            size="sm"
            className="text-xs"
            onClick={toggleEditMode}
          >
            {editMode ? (
              <X className="h-3.5 w-3.5 mr-1" />
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" /><path d="m15 5 4 4" /></svg>
            )}
            {editMode ? "关闭更正编辑" : "开启更正编辑"}
          </Button>
        </div>
      </div>

      {/* 表格容器 - 设置为h-[calc(100%-48px)]确保不超出可视区域 */}
      <div className="h-[calc(100%-48px)]">
        <AgGridReact<ArchiveRecord>
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowModelType="serverSide"
          rowNumbers={true}
          serverSideDatasource={memoizedServerSideDatasource} // 使用 memoized 的数据源对象
          theme={myTheme} // 使用自定义主题对象
          onFirstDataRendered={onFirstDataRendered}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged} // 添加排序变更事件处理
          debug={process.env.NODE_ENV === 'development'}
          modules={[AllEnterpriseModule]}
          
          // 编辑相关配置
          stopEditingWhenCellsLoseFocus={true}
          onCellEditingStarted={onCellEditingStarted}
          onCellEditingStopped={onCellEditingStopped}
          
          // 应用所有性能优化设置 - 改用更保守的设置
          {...gridPerformanceConfig}
          
          // 特定设置覆盖
          domLayout="normal"
          enableCellTextSelection={true}
          suppressMovableColumns={false} // 保留列移动功能
          sideBar={sideBarConfig} // 使用 memoized 的配置
          
          // 高级优化
          getRowId={(params) => String(params.data.id)} // 提供稳定的行ID
          
          // 加载指示器
          loadingCellRenderer={'agLoadingCellRenderer'}
          
          // 右键菜单支持
          allowContextMenuWithControlKey={true}
          getContextMenuItems={getContextMenuItems}
        />
      </div>
    </TablePageLayout>
  )
}
