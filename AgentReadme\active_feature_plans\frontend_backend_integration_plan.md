# 前后端对接计划 - 核心业务优先

## 第一阶段：核心业务页面

1. **记录管理** (records)
   - 后端：实现记录相关API，包括查询、创建、更新和删除
   - 前端：对接records页面，保持现有UI布局
   - 调整：优化表单验证、筛选和分页功能

2. **档案管理** (archive)
   - 后端：实现档案CRUD接口，支持复杂查询和筛选
   - 前端：对接archive页面组件，包括档案列表和详情页
   - 调整：完善档案状态流转、关联查询功能

3. **报告管理** (reports)
   - 后端：实现报告生成、查询和分发API
   - 前端：对接reports页面，配合报告查看组件
   - 调整：优化报告模板和导出功能

4. **变更单管理** (change-orders)
   - 后端：实现变更单创建、审批、执行API
   - 前端：对接变更单页面和流程组件
   - 调整：完善变更流程、审批规则和历史记录

5. **PDF导入台账** (archive/ledger)
   - 后端：实现PDF上传、处理和切分API
   - 前端：对接pdf-import-ledger组件
   - 调整：优化文件处理流程、进度反馈机制

## 第二阶段：认证与基础设施

6. **登录认证**
   - 后端：实现JWT认证接口，用户验证
   - 前端：对接login页面，集成token管理
   - 调整：确保与已实现的业务功能权限对接

7. **用户管理基础**
   - 后端：实现用户信息和权限API
   - 前端：对接profile页面和header组件
   - 调整：基于用户角色调整功能可见性

8. **API基础设施完善**
   - 后端：统一响应格式、全面错误处理
   - 前端：完善API请求中间件、全局错误处理
   - 调整：增强安全性、审计日志

## 第三阶段：辅助功能

9. **统计分析** (statistics)
   - 后端：实现统计数据API，聚合查询
   - 前端：对接统计图表组件
   - 调整：增加自定义数据视图

10. **通知系统** (notifications)
    - 后端：实现通知API，支持不同通知类型
    - 前端：对接通知组件和消息中心
    - 调整：增加实时通知能力

11. **系统设置与管理功能** (settings, users, help)
    - 后端：实现系统配置、用户管理和帮助内容API
    - 前端：对接相关页面组件
    - 调整：完善管理功能和系统维护能力

## 对接方法与规范

### 对接流程（每个页面）

1. **API定义阶段**
   - 后端团队定义接口规范（参数、返回值）
   - 前端团队评审接口是否满足UI需求
   - 双方确认接口规范并形成文档

2. **后端实现阶段**
   - 后端优先实现API，提供测试环境
   - 提供API测试集合供前端调试
   - 编写API文档，包括示例

3. **前端对接阶段**
   - 前端基于mock数据开发（保持UI不变）
   - 逐步替换mock数据为真实API调用
   - 处理错误情况和边界条件

4. **联调优化阶段**
   - 处理数据格式不一致问题
   - 优化性能和用户体验
   - 完善错误处理和数据验证

### 技术规范

1. **API规范**
   - 使用RESTful风格接口
   - 返回格式：`{ success: true/false, data: {...} }` 或 `{ success: false, error: "错误信息" }`
   - 分页参数统一：`page`、`page_size`
   - 过滤参数命名统一：`filter_{字段名}`

2. **状态码使用**
   - 200: 成功
   - 400: 客户端错误
   - 401: 未认证/token失效
   - 403: 无权限
   - 500: 服务器错误

3. **无认证阶段的处理**
   - 初期API不强制认证，通过配置开启/关闭
   - 前端设置临时绕过认证的机制
   - 最终对接认证系统时平滑过渡

4. **版本控制**
   - API URL包含版本：`/api/v1/...`
   - 重大变更必须升级版本号

5. **安全性考虑**
   - 所有API输入必须验证
   - 敏感数据传输加密
   - 实施适当的CORS策略
   - 实现请求频率限制

6. **文档要求**
   - 每个API必须有文档
   - 需包含请求/响应示例
   - 参数类型和限制说明
   - 错误码及其含义

7. **测试策略**
   - API单元测试覆盖核心功能
   - 集成测试验证端到端流程
   - 自动化测试实现关键业务场景
