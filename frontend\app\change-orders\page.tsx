"use client"
import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import {
  FileEdit,
  ClipboardCheck,
  CheckCircle,
  FileText,
  Users,
  Calendar,
  ArrowUpRight,
  Plus,
  Archive,
  FileLock2,
  Upload,
  FileUp,
  Info,
  FileCheck,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export default function ChangeOrdersDashboard() {
  // 模拟数据
  const stats = [
    {
      title: "总更改单数",
      value: "1,284",
      icon: FileEdit,
      change: "+12% 本月",
      trend: "up",
    },
    {
      title: "处理中更改单",
      value: "316",
      icon: FileCheck,
      change: "-2% 本周",
      trend: "down",
      tooltip: "包含草稿、锁定和确认状态的更改单总数",
      breakdown: [
        { label: "草稿", value: "42" },
        { label: "锁定", value: "156" },
        { label: "确认", value: "118" },
      ],
    },
    {
      title: "已归档",
      value: "968",
      icon: Archive,
      change: "+8% 本月",
      trend: "up",
    },
    {
      title: "确认单关联率",
      value: "76%",
      icon: FileUp,
      change: "+5% 本月",
      trend: "up",
      tooltip: "已归档更改单中已上传确认单的比例",
    },
  ]

  const recentActivities = [
    {
      id: "CO-2023042501",
      action: "创建了更改单",
      user: "张三",
      time: "10分钟前",
      status: "draft",
      entries: 5,
    },
    {
      id: "CO-2023042405",
      action: "锁定了更改单",
      user: "李四",
      time: "1小时前",
      status: "locked",
      entries: 3,
    },
    {
      id: "CO-2023042404",
      action: "确认了更改单",
      user: "王五",
      time: "2小时前",
      status: "confirmed",
      entries: 7,
      hasConfirmationDoc: true,
    },
    {
      id: "CO-2023042403",
      action: "归档了更改单",
      user: "赵六",
      time: "3小时前",
      status: "archived",
      entries: 4,
      hasConfirmationDoc: true,
    },
    {
      id: "CO-2023042402",
      action: "上传了确认单",
      user: "张三",
      time: "5小时前",
      status: "confirmed",
      entries: 2,
    },
  ]

  const quickAccessItems = [
    {
      title: "更改单管理台账",
      description: "查看和管理所有更改单",
      icon: ClipboardCheck,
      href: "/change-orders/ledger",
    },
    {
      title: "新建档案更改单",
      description: "创建新的档案更改申请",
      icon: Plus,
      href: "/change-orders/detail/new",
      primary: true,
    },
    {
      title: "草稿更改单",
      description: "查看草稿状态的更改单",
      icon: FileText,
      href: "/change-orders/ledger?status=draft",
    },
    {
      title: "锁定及确认更改单",
      description: "查看锁定和确认状态的更改单",
      icon: FileCheck,
      href: "/change-orders/ledger?status=locked,confirmed",
    },
  ]

  const statusColors = {
    draft: "bg-yellow-500", // 草稿状态
    locked: "bg-blue-500", // 锁定状态
    confirmed: "bg-green-500", // 确认状态
    archived: "bg-gray-500", // 归档状态
  }

  return (
    <div className="space-y-6">
      <PageTitle title="档案更改单管理" subtitle="管理和跟踪档案更改申请" />

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                {stat.title}
                {stat.tooltip && (
                  <TooltipProvider delayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 ml-1 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="text-sm p-3">
                        <p>{stat.tooltip}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className={`text-xs ${stat.trend === "up" ? "text-green-500" : "text-blue-500"}`}>{stat.change}</p>

              {/* 处理中更改单的明细 */}
              {stat.breakdown && (
                <div className="mt-2 pt-2 border-t border-gray-100 grid grid-cols-3 gap-2">
                  {stat.breakdown.map((item) => (
                    <div key={item.label} className="text-center">
                      <div className="text-xs text-muted-foreground">{item.label}</div>
                      <div className="text-sm font-medium">{item.value}</div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 主要内容区域 - 使用两列网格布局 */}
      <div className="grid gap-6 md:grid-cols-12">
        {/* 左侧列 */}
        <div className="md:col-span-4 space-y-6">
          {/* 快速访问卡片 */}
          <Card className="h-auto">
            <CardHeader>
              <CardTitle>快速访问</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {quickAccessItems.map((item) => (
                <Button
                  key={item.title}
                  variant={item.primary ? "default" : "outline"}
                  className="w-full justify-start h-auto py-3"
                  asChild
                >
                  <Link href={item.href} className="flex items-start gap-3">
                    <item.icon className={`h-5 w-5 ${item.primary ? "" : "text-muted-foreground"}`} />
                    <div className="text-left">
                      <div className="font-medium">{item.title}</div>
                      <div className="text-xs text-muted-foreground mt-1">{item.description}</div>
                    </div>
                  </Link>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* 更改单状态分布卡片 */}
          <Card className="h-auto">
            <CardHeader>
              <CardTitle>更改单状态分布</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>草稿状态</span>
                  <span className="font-medium">42/1284</span>
                </div>
                <Progress value={3.3} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>锁定状态</span>
                  <span className="font-medium">156/1284</span>
                </div>
                <Progress value={12.1} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>确认状态</span>
                  <span className="font-medium">118/1284</span>
                </div>
                <Progress value={9.2} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>归档状态</span>
                  <span className="font-medium">968/1284</span>
                </div>
                <Progress value={75.4} className="h-2" />
              </div>

              <div className="pt-2">
                <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                  <Link href="/statistics?type=change-orders">
                    查看详细统计
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧列 */}
        <div className="md:col-span-8 space-y-6">
          {/* 选项卡内容卡片 */}
          <Card className="h-auto">
            <CardContent className="p-0">
              <Tabs defaultValue="recent" className="w-full">
                <TabsList className="grid w-full grid-cols-3 rounded-none border-b">
                  <TabsTrigger value="recent">最近活动</TabsTrigger>
                  <TabsTrigger value="byUser">按用户统计</TabsTrigger>
                  <TabsTrigger value="byMonth">按月统计</TabsTrigger>
                </TabsList>

                <TabsContent value="recent" className="p-4 space-y-4">
                  <div className="divide-y">
                    {recentActivities.map((activity) => (
                      <div key={activity.id} className="flex items-center py-3 first:pt-0 last:pb-0">
                        <div
                          className={`w-2 h-2 rounded-full ${statusColors[activity.status as keyof typeof statusColors]} mr-4`}
                        ></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div className="font-medium">{activity.action}</div>
                            <div className="text-sm text-muted-foreground">{activity.time}</div>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <div className="text-sm">
                              <span className="text-muted-foreground">由 </span>
                              <span className="font-medium">{activity.user}</span>
                              <span className="text-muted-foreground"> 处理</span>
                            </div>
                            <div className="flex items-center gap-4">
                              {activity.status === "archived" && (
                                <span className="text-xs bg-gray-100 px-2 py-0.5 rounded">
                                  涉及 {activity.entries} 条档案
                                </span>
                              )}
                              {activity.hasConfirmationDoc && (
                                <span className="text-xs bg-blue-50 text-blue-600 px-2 py-0.5 rounded flex items-center">
                                  <FileUp className="h-3 w-3 mr-1" />
                                  已上传确认单
                                </span>
                              )}
                              <Link
                                href={`/change-orders/detail/${activity.id}`}
                                className="text-sm text-blue-500 hover:underline"
                              >
                                {activity.id}
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-center pt-2">
                    <Button variant="outline" asChild>
                      <Link href="/change-orders/ledger">查看所有更改单</Link>
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="byUser" className="p-6">
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="mx-auto h-12 w-12 opacity-50" />
                    <p className="mt-2">按用户统计的更改单数据将在此显示</p>
                  </div>
                </TabsContent>

                <TabsContent value="byMonth" className="p-6">
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="mx-auto h-12 w-12 opacity-50" />
                    <p className="mt-2">按月统计的更改单数据将在此显示</p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* 工作流程卡片 */}
          <Card className="h-auto">
            <CardHeader className="pb-3">
              <CardTitle>更改单工作流程</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-6">
                {/* 主要流程 */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-col items-center">
                    <div className="rounded-full bg-gray-100 p-2">
                      <FileText className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="mt-2 text-xs font-medium">创建/草稿</span>
                  </div>

                  <div className="h-0.5 flex-1 bg-gray-200"></div>

                  <div className="flex flex-col items-center">
                    <div className="rounded-full bg-gray-100 p-2">
                      <FileLock2 className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="mt-2 text-xs font-medium">锁定</span>
                  </div>

                  <div className="h-0.5 flex-1 bg-gray-200"></div>

                  <div className="flex flex-col items-center">
                    <div className="rounded-full bg-gray-100 p-2">
                      <CheckCircle className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="mt-2 text-xs font-medium">确认</span>
                  </div>

                  <div className="h-0.5 flex-1 bg-gray-200"></div>

                  <div className="flex flex-col items-center">
                    <div className="rounded-full bg-gray-100 p-2">
                      <Archive className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="mt-2 text-xs font-medium">归档</span>
                  </div>
                </div>

                {/* 确认单上传说明 */}
                <div className="bg-blue-50 p-3 rounded-md text-sm">
                  <div className="flex items-center text-blue-700 font-medium mb-1">
                    <Upload className="h-4 w-4 mr-2" />
                    确认单上传
                  </div>
                  <p className="text-blue-600 text-xs">
                    确认单扫描件可以在确认阶段或归档阶段上传。上传确认单不是必须的，但建议上传以便于后续查阅和管理。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
