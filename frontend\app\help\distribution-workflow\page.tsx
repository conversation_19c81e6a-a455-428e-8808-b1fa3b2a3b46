import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { StateTransitionDiagram } from "@/components/domain/reports/state-transition-diagram"

export default function DistributionWorkflowPage() {
  return (
    <div className="space-y-6">
      <PageTitle title="发放单工作流程" subtitle="了解发放单的状态转换和操作流程" />

      <Card>
        <CardHeader>
          <CardTitle>发放单状态转换图</CardTitle>
          <CardDescription>发放单在不同状态之间的转换关系</CardDescription>
        </CardHeader>
        <CardContent>
          <StateTransitionDiagram className="w-full h-[300px]" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>状态说明</CardTitle>
          <CardDescription>各状态的含义和可执行的操作</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">草稿状态</h3>
              <p className="text-sm text-muted-foreground">
                发放单创建后的初始状态，此时可以自由编辑发放单的所有内容，包括领取单位、领取人、档案记录和发放份数等。
              </p>
              <p className="text-sm text-muted-foreground">可执行操作：</p>
              <ul className="list-disc list-inside text-sm text-muted-foreground ml-4">
                <li>编辑发放单</li>
                <li>锁定发放单</li>
                <li>删除发放单</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">已锁定状态</h3>
              <p className="text-sm text-muted-foreground">
                发放单已锁定，不能再进行编辑。此状态表示发放单内容已确认，等待进一步确认发放。
              </p>
              <p className="text-sm text-muted-foreground">可执行操作：</p>
              <ul className="list-disc list-inside text-sm text-muted-foreground ml-4">
                <li>解锁（回到草稿状态）</li>
                <li>确认发放</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">已确认状态</h3>
              <p className="text-sm text-muted-foreground">
                发放单已确认发放，表示报告已实际发放给领取单位。此状态下可以上传领取确认单。
              </p>
              <p className="text-sm text-muted-foreground">可执行操作：</p>
              <ul className="list-disc list-inside text-sm text-muted-foreground ml-4">
                <li>转为草稿（如需修改）</li>
                <li>归档（完成整个流程）</li>
                <li>上传领取确认单</li>
                <li>打印确认单</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">已归档状态</h3>
              <p className="text-sm text-muted-foreground">
                发放单已完成归档，表示整个发放流程已结束。此状态是发放单的最终状态，不可再变更。
              </p>
              <p className="text-sm text-muted-foreground">可执行操作：</p>
              <ul className="list-disc list-inside text-sm text-muted-foreground ml-4">
                <li>查看详情</li>
                <li>打印确认单</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>工作流程说明</CardTitle>
          <CardDescription>发放单的完整工作流程</CardDescription>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-4 text-sm text-muted-foreground ml-4">
            <li>
              <span className="font-medium">创建发放单</span>
              ：选择需要发放的档案记录，填写领取单位和领取人信息，设置发放份数。
            </li>
            <li>
              <span className="font-medium">锁定发放单</span>：确认发放单信息无误后，锁定发放单，防止意外修改。
            </li>
            <li>
              <span className="font-medium">确认发放</span>：实际发放报告后，确认发放单，表示报告已交付给领取单位。
            </li>
            <li>
              <span className="font-medium">上传确认单</span>：如有纸质确认单，可以扫描上传到系统中。
            </li>
            <li>
              <span className="font-medium">归档</span>
              ：完成所有流程后，将发放单归档，结束整个发放流程。归档后的发放单不可再修改。
            </li>
          </ol>
          <p className="mt-4 text-sm text-muted-foreground">
            注意：在已确认状态下，如发现信息有误，可以将发放单转回草稿状态进行修改。但这会在操作历史中留下记录，以保证流程的可追溯性。已归档的发放单为最终状态，不可再转为草稿。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
