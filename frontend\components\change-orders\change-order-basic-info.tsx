"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

interface ChangeOrderBasicInfoProps {
  changeOrder: any
  isNewOrder: boolean
  editMode: boolean
  onBasicInfoChange: (field: string, value: string, shouldSave?: boolean) => void
}

export function ChangeOrderBasicInfo({
  changeOrder,
  isNewOrder,
  editMode,
  onBasicInfoChange,
}: ChangeOrderBasicInfoProps) {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 gap-4">
          <div className="md:col-span-2">
            <h3 className="text-sm font-medium text-gray-500">更改原因</h3>
            <Textarea
              id="reason"
              placeholder="请输入更改原因..."
              value={changeOrder.reason || ""}
              onChange={(e) => onBasicInfoChange("reason", e.target.value)}
              onBlur={(e) => onBasicInfoChange("reason", e.target.value, true)}
              disabled={!editMode}
              className="min-h-[100px]"
            />
          </div>
          <div className="md:col-span-2">
            <h3 className="text-sm font-medium text-gray-500">详细说明</h3>
            <Textarea
              id="details"
              placeholder="请输入详细说明..."
              value={changeOrder.details || ""}
              onChange={(e) => onBasicInfoChange("details", e.target.value)}
              onBlur={(e) => onBasicInfoChange("details", e.target.value, true)}
              disabled={!editMode}
              className="min-h-[150px]"
            />
          </div>
          {!isNewOrder && (
            <>
              <div>
                <h3 className="text-sm font-medium text-gray-500">创建时间</h3>
                <p className="mt-1">{changeOrder.createdAt}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">创建人</h3>
                <p className="mt-1">{changeOrder.createdBy}</p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
