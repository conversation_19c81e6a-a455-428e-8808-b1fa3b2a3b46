/**
 * 导入历史服务
 * 
 * 提供与后端导入历史API的交互功能，包括:
 * - 获取导入历史记录列表
 * - 查看导入历史记录详情
 * - 搜索和筛选导入记录
 * - 下载导入报告
 * 
 * <AUTHOR> Team
 * @since 2024-07-27
 */

import apiClient, { ApiResponse } from '@/lib/apiClient';

// ==================== 核心类型定义 ====================

/** 导入历史记录基础信息 */
export interface ImportHistoryRecord {
  readonly id: string;
  readonly batchNumber: string;         // batch_number -> batchNumber
  readonly fileName: string;            // file_name -> fileName
  readonly fileSize?: number;           // file_size -> fileSize
  readonly importDate: string;          // import_date -> importDate
  readonly importUser: {                // import_user -> importUser
    readonly id: number;
    readonly username: string;
  } | null;
  readonly status: 'completed' | 'partial' | 'failed';
  readonly totalRecords?: number;       // total_records -> totalRecords
  readonly createdCount?: number;       // created_count -> createdCount
  readonly updatedCount?: number;       // updated_count -> updatedCount
  readonly failedCount?: number;        // failed_count -> failedCount
  readonly processingTime?: number;     // processing_time -> processingTime
  readonly errorMessage?: string;       // error_message -> errorMessage
}

/** 导入历史详情记录 */
export interface ImportHistoryDetailRecord extends ImportHistoryRecord {
  readonly fileHash?: string;           // file_hash -> fileHash
  readonly createdAt: string;           // created_at -> createdAt
  readonly updatedAt: string;           // updated_at -> updatedAt
  readonly createdBy?: {                // created_by -> createdBy
    readonly id: number;
    readonly username: string;
  } | null;
  
  // 详细统计信息 - 分析阶段
  readonly analysisTotalRowsRead?: number;           // analysis_total_rows_read -> analysisTotalRowsRead
  readonly analysisSuccessfullyParsedRows?: number;  // analysis_successfully_parsed_rows -> analysisSuccessfullyParsedRows
  readonly analysisFailedRows?: number;              // analysis_failed_rows -> analysisFailedRows
  readonly analysisFoundNewCount?: number;           // analysis_found_new_count -> analysisFoundNewCount
  readonly analysisFoundUpdateCount?: number;        // analysis_found_update_count -> analysisFoundUpdateCount
  readonly analysisSkippedIdentical?: number;        // analysis_skipped_identical -> analysisSkippedIdentical
  
  // 详细统计信息 - 用户决策阶段
  readonly userDecisionSkippedUpdateCount?: number;  // user_decision_skipped_update_count -> userDecisionSkippedUpdateCount
  readonly userDecisionConfirmedUpdateCount?: number; // user_decision_confirmed_update_count -> userDecisionConfirmedUpdateCount
  
  // 详细统计信息 - 导入任务阶段
  readonly importTaskTotalRecordsSubmitted?: number;     // import_task_total_records_submitted -> importTaskTotalRecordsSubmitted
  readonly importTaskCreatedCount?: number;              // import_task_created_count -> importTaskCreatedCount
  readonly importTaskUpdatedCount?: number;              // import_task_updated_count -> importTaskUpdatedCount
  readonly importTaskUnchangedCount?: number;            // import_task_unchanged_count -> importTaskUnchangedCount
  readonly importTaskProcessedSuccessfullyCount?: number; // import_task_processed_successfully_count -> importTaskProcessedSuccessfullyCount
  readonly importTaskFailedCount?: number;               // import_task_failed_count -> importTaskFailedCount
  
  // 详细统计信息 - 总体汇总
  readonly overallTotalInitialRecords?: number;         // overall_total_initial_records -> overallTotalInitialRecords
  readonly overallUserDecisionSkippedUpdates?: number;  // overall_user_decision_skipped_updates -> overallUserDecisionSkippedUpdates
  readonly overallFinalCreatedCount?: number;           // overall_final_created_count -> overallFinalCreatedCount
  readonly overallFinalUpdatedCount?: number;           // overall_final_updated_count -> overallFinalUpdatedCount
  readonly overallSkippedBySystemTotal?: number;        // overall_skipped_by_system_total -> overallSkippedBySystemTotal
  readonly overallSkippedTotal?: number;                // overall_skipped_total -> overallSkippedTotal
  readonly overallFailedTotal?: number;                 // overall_failed_total -> overallFailedTotal
  readonly overallProcessedSuccessfullyTotal?: number;  // overall_processed_successfully_total -> overallProcessedSuccessfullyTotal
  
  // 详细报告
  readonly detailedReport?: {           // detailed_report -> detailedReport
    readonly errors?: readonly {
      readonly row?: number;
      readonly field?: string;
      readonly message: string;
      readonly commissionNumber?: string;  // commission_number -> commissionNumber
      readonly sampleNumber?: string;      // sample_number -> sampleNumber
    }[];
    readonly importTaskProcessingErrors?: readonly {  // import_task_processing_errors -> importTaskProcessingErrors
      readonly row?: number;
      readonly field?: string;
      readonly message: string;
      readonly commissionNumber?: string;  // commission_number -> commissionNumber
      readonly sampleNumber?: string;      // sample_number -> sampleNumber
    }[];
  };
}

/** 导入历史查询参数 */
export interface ImportHistoryQueryParams {
  readonly page?: number;
  readonly pageSize?: number;           // page_size -> pageSize
  readonly status?: string;
  readonly userName?: string;           // user_name -> userName
  readonly fileName?: string;           // file_name -> fileName
  readonly batchNumber?: string;        // batch_number -> batchNumber
  readonly importDateStart?: string;    // import_date_start -> importDateStart
  readonly importDateEnd?: string;      // import_date_end -> importDateEnd
  readonly ordering?: string;
}

/** 分页响应基础类型 */
export interface ImportHistoryPaginatedResponse {
  readonly count: number;
  readonly next: string | null;
  readonly previous: string | null;
  readonly results: readonly ImportHistoryRecord[];
}

// ==================== 服务类 ====================

class ImportHistoryService {
  private readonly baseUrl = '/api/archive-records/import-logs';

  /**
   * 获取导入历史记录列表
   * 
   * @param params 查询参数（camelCase）
   * @returns 分页的导入历史记录列表
   */
  async getImportHistoryRecords(params: ImportHistoryQueryParams = {}): Promise<ImportHistoryPaginatedResponse> {
    const queryParams = new URLSearchParams();
    
    // 构建查询参数 - 这些会被后端自动转换为snake_case
    if (params.page) queryParams.append('page', String(params.page));
    if (params.pageSize) queryParams.append('pageSize', String(params.pageSize));
    if (params.status) queryParams.append('status', params.status);
    if (params.userName) queryParams.append('userName', params.userName);
    if (params.fileName) queryParams.append('fileName', params.fileName);
    if (params.batchNumber) queryParams.append('batchNumber', params.batchNumber);
    if (params.importDateStart) queryParams.append('importDateStart', params.importDateStart);
    if (params.importDateEnd) queryParams.append('importDateEnd', params.importDateEnd);
    if (params.ordering) queryParams.append('ordering', params.ordering);

    const url = queryParams.toString() ? `${this.baseUrl}/?${queryParams.toString()}` : `${this.baseUrl}/`;
    
    const response = await apiClient.get<ImportHistoryPaginatedResponse>(url);

    if (!response.success || !response.data) {
      throw new Error(response.error || '获取导入历史记录失败');
    }

    return response.data;
  }

  /**
   * 获取导入历史记录详情
   * 
   * @param id 导入历史记录ID
   * @returns 导入历史记录详情
   */
  async getImportHistoryRecord(id: string): Promise<ImportHistoryDetailRecord> {
    if (!id?.trim()) {
      throw new Error('导入历史记录ID不能为空');
    }

    const response = await apiClient.get<ImportHistoryDetailRecord>(`${this.baseUrl}/${id}/`);

    if (!response.success || !response.data) {
      throw new Error(response.error || '获取导入历史记录详情失败');
    }

    return response.data;
  }

  /**
   * 删除导入历史记录
   * 
   * @param id 导入历史记录ID
   * @returns 删除操作结果
   */
  async deleteImportHistoryRecord(id: string): Promise<{ success: boolean; message?: string }> {
    if (!id?.trim()) {
      throw new Error('导入历史记录ID不能为空');
    }

    try {
      const response = await apiClient.delete<{ message: string }>(`${this.baseUrl}/${id}/`);
      
      return {
        success: response.success,
        message: response.data?.message || response.error
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '删除导入历史记录失败'
      };
    }
  }
  
  /**
   * 批量删除导入历史记录
   * 
   * @param ids 导入历史记录ID数组
   * @returns 批量删除操作结果
   */
  async bulkDeleteImportHistoryRecords(ids: readonly string[]): Promise<{ success: boolean; message?: string; deletedCount?: number }> {
    if (!ids || ids.length === 0) {
      throw new Error('请选择要删除的记录');
    }

    try {
      const response = await apiClient.post<{ message: string; deletedCount: number }>(
        `${this.baseUrl}/bulk-delete/`,
        { ids }
      );
      
      return {
        success: response.success,
        message: response.data?.message || response.error,
        deletedCount: response.data?.deletedCount
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '批量删除导入历史记录失败'
      };
    }
  }
  
  /**
   * 下载导入历史记录报告
   * 
   * @param id 导入历史记录ID
   * @param format 报告格式 ('json' | 'excel')
   * @returns 下载响应
   */
  async downloadImportHistoryReport(id: string, format: 'json' | 'excel' = 'json'): Promise<Blob> {
    if (!id?.trim()) {
      throw new Error('导入历史记录ID不能为空');
    }

    const response = await fetch(`${this.baseUrl}/${id}/download/?format=${format}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      },
    });

    if (!response.ok) {
      throw new Error(`下载报告失败: ${response.statusText}`);
    }

    return await response.blob();
  }

  /**
   * 获取导入历史统计信息
   * 
   * @param dateRange 日期范围
   * @returns 统计信息
   */
  async getImportHistoryStatistics(dateRange?: { startDate: string; endDate: string }): Promise<{
    totalImports: number;           // total_imports -> totalImports
    successfulImports: number;      // successful_imports -> successfulImports
    failedImports: number;          // failed_imports -> failedImports
    totalRecordsProcessed: number;  // total_records_processed -> totalRecordsProcessed
    totalRecordsCreated: number;    // total_records_created -> totalRecordsCreated
    totalRecordsUpdated: number;    // total_records_updated -> totalRecordsUpdated
    averageProcessingTime: number;  // average_processing_time -> averageProcessingTime
  }> {
    const queryParams = new URLSearchParams();
    
    if (dateRange) {
      queryParams.append('startDate', dateRange.startDate);
      queryParams.append('endDate', dateRange.endDate);
    }

    const url = queryParams.toString() ? 
      `${this.baseUrl}/statistics/?${queryParams.toString()}` : 
      `${this.baseUrl}/statistics/`;
    
    const response = await apiClient.get<{
      totalImports: number;
      successfulImports: number;
      failedImports: number;
      totalRecordsProcessed: number;
      totalRecordsCreated: number;
      totalRecordsUpdated: number;
      averageProcessingTime: number;
    }>(url);

    if (!response.success || !response.data) {
      throw new Error(response.error || '获取导入历史统计信息失败');
    }

    return response.data;
  }
}

// ==================== 导出 ====================

// 创建单例实例
const importHistoryService = new ImportHistoryService();

export default importHistoryService;
export { ImportHistoryService }; 