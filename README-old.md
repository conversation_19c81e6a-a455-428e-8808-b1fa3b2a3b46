未更新！

# 档案流程管理系统 (Archive Flow Manager)

一个基于Django的档案流程管理系统，主要功能包括PDF文档的处理、分割和OCR识别。

## 主要功能

- PDF文档分割：根据指定文本自动分割PDF文档
- OCR文本识别：使用Tesseract OCR进行文本识别
- 档案流程自动化处理：通过API提供PDF处理服务

## 技术栈

- Python 3.x
- Django 5.1
- Django REST Framework
- PDF2Image：PDF转图像处理
- PyTesseract：OCR文本识别
- PyPDF2：PDF文件处理

## 依赖组件

- Poppler：PDF处理工具
- Tesseract OCR：光学字符识别工具

## 项目结构

```
archive_flow_manager/
├── archive_flow_manager/  # 主项目配置
├── pdf_split_app/         # PDF分割应用
├── tests/                 # 测试目录
│   ├── dependencies/      # 依赖测试
│   ├── functional/        # 功能测试
│   └── samples/           # 测试样例
└── media/                 # 媒体文件目录
```

## 安装和配置

1. 克隆仓库
2. 安装依赖：`pip install -r requirements.txt`
3. 配置Poppler和Tesseract OCR路径
4. 运行迁移：`python manage.py migrate`
5. 启动服务：`python manage.py runserver`

## API接口

- `POST /api/split-pdf/`：上传PDF文件和目标文本，返回分割后的PDF文件列表

## 注意事项！！！！！！！！
- 暂未考虑边缘情况（目前只考虑了代合同完全匹配，未考虑代合同未准确识别出的情况），未增加模糊匹配+重试机制，以提高OCR识别的准确性 （可考虑）
- 可尝试多种ORC组合（如PaddleOCR）（可考虑）
- 可在确认准确度的情况下进行速度优化。

## 计划：
- 3.26。计划 写 导入 Execl入库的功能
- 3.27。根据识别CMA章，执行报告发放自动统计功能
- 3.28。设计权限功能。
- 3.29。测试后端api端点
- 3.30，3.31。简要对接前端
- 4.01。使用AGgrid前端表格出来台账。

## 本地开发环境设置

### 1. 激活 Conda 环境

在运行任何项目命令之前，请确保您已激活项目的 Conda 环境：

```bash
conda activate ./archive_flow_manager.conda 
```

### 2. 运行 Django 开发服务器

```bash
python manage.py runserver
```

访问 `http://127.0.0.1:8000/` 查看应用。

### 3. (可选) 运行 Celery 异步任务 (用于测试 PDF 处理等后台任务)

本项目使用 Celery 处理耗时的后台任务，例如 PDF 分割和信息提取。为了在本地测试这些功能，您需要运行消息中间件 (推荐 Redis) 和 Celery Worker 进程。

**a) 启动 Redis 服务**

推荐使用 Docker 启动 Redis：

```bash
docker run -d --name afm-redis -p 6379:6379 redis:latest
```

(如果不再需要，可以使用 `docker stop afm-redis && docker rm afm-redis` 来停止和移除容器)

**b) 启动 Celery Worker**

打开一个新的终端窗口，确保 Conda 环境已激活，然后在项目根目录下运行：

```bash
celery -A archive_flow_manager worker -l info
```

*   `-A archive_flow_manager`: 指定包含 Celery 应用的 Django 项目。
*   `worker`: 启动 Worker 进程。
*   `-l info`: 设置日志级别为 INFO，方便查看任务执行情况。

现在，当 Web 应用触发异步任务时 (例如上传 PDF 后)，您应该能在 Celery Worker 的终端窗口中看到任务被接收和处理的日志。

### 4. 运行测试

项目支持多种测试方式：

#### Django测试命令

```bash
# 运行所有测试
python manage.py test

# 或者指定特定应用或模块
python manage.py test archive_processing
python manage.py test test_suite.integration.archive_processing.test_archive_processing_upload
```

#### 使用pytest运行测试（推荐）

pytest提供更丰富的功能和更详细的输出：

**Windows环境**:
```powershell
# 设置环境变量解决OpenMP运行时问题
$env:KMP_DUPLICATE_LIB_OK="TRUE"; python -m pytest test_suite/integration/archive_processing/test_tasks.py -vv
```

**Unix/Linux环境**:
```bash
# 设置环境变量解决OpenMP运行时问题
KMP_DUPLICATE_LIB_OK="TRUE" python -m pytest test_suite/integration/archive_processing/test_tasks.py -vv
```

> **注意**：设置环境变量 `KMP_DUPLICATE_LIB_OK="TRUE"` 可以解决某些依赖(如scikit-learn/scikit-image)引起的OpenMP运行时错误。

查看更多测试相关信息，请参考 [测试指南](AgentReadme/testing_guide.md)。