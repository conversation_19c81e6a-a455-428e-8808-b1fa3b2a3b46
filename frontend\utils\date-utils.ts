import { format } from "date-fns";

/**
 * 格式化日期字符串
 * @param dateStr 日期字符串
 * @returns 格式化后的日期字符串 (yyyy-MM-dd) 或原始字符串（如果转换失败）
 */
export const formatDate = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  try {
    return format(new Date(dateStr), 'yyyy-MM-dd');
  } catch (e) {
    // 如果 dateStr 是无效日期，Date 构造函数可能会抛出 RangeError，或者 format 会因无效 Date 对象而出错
    // console.warn(\`Error formatting date: \${dateStr}\`, e);
    return typeof dateStr === 'string' ? dateStr : ''; // 防御性返回，确保是字符串
  }
};

/**
 * 格式化日期时间字符串（包含时分秒）
 * @param dateStr 日期时间字符串
 * @returns 格式化后的日期时间字符串 (yyyy-MM-dd HH:mm:ss) 或原始字符串（如果转换失败）
 */
export const formatDateTime = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  try {
    return format(new Date(dateStr), 'yyyy-MM-dd HH:mm:ss');
  } catch (e) {
    // 如果 dateStr 是无效日期，Date 构造函数可能会抛出 RangeError，或者 format 会因无效 Date 对象而出错
    // console.warn(\`Error formatting datetime: \${dateStr}\`, e);
    return typeof dateStr === 'string' ? dateStr : ''; // 防御性返回，确保是字符串
  }
};

/**
 * 获取今天的日期字符串
 * @returns yyyy-MM-dd格式的今天日期
 */
export const getTodayStr = (): string => {
  return format(new Date(), 'yyyy-MM-dd');
};

/**
 * 获取N天前的日期字符串
 * @param days 天数
 * @returns yyyy-MM-dd格式的N天前日期
 */
export const getDaysAgoStr = (days: number): string => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return format(date, 'yyyy-MM-dd');
};

/**
 * 获取N月前的日期字符串
 * @param months 月数
 * @returns yyyy-MM-dd格式的N月前日期
 */
export const getMonthsAgoStr = (months: number): string => {
  const date = new Date();
  date.setMonth(date.getMonth() - months);
  return format(date, 'yyyy-MM-dd');
}; 