# Clean Archive Project - Product Requirements Document

## Overview
Clean Archive Project is a comprehensive document and record management system designed for organizations that need to maintain, track, and retrieve archives efficiently. It focuses on the processing, categorization, and retrieval of various document types, with special emphasis on PDF processing and Excel data import capabilities.

## Core Features

### Document Management
- Upload, store, and categorize various document types (PDF, Excel, etc.)
- Maintain document metadata and version history
- Support for document tagging and classification
- OCR for searchable PDFs

### Archive Processing
- Automated processing of incoming documents
- PDF splitting and merging capabilities
- Metadata extraction from documents
- Batch processing support

### Record Tracking
- Maintain records of all document transactions
- Track document lifecycle (creation, modification, archival)
- Generate audit logs for compliance
- Support for custom record fields

### Import/Export
- Bulk import from Excel files with conflict resolution
- Real-time progress tracking for large file imports
- Export records to various formats (Excel, CSV, PDF)
- Data validation during import

### Reporting
- Generate customizable reports
- Schedule automated report generation
- Export reports in multiple formats
- Visual dashboards for key metrics

### User Management
- Role-based access control
- User activity tracking
- Permission management
- Authentication and authorization

## Technical Requirements

### Frontend
- Next.js-based user interface
- Responsive design for all device types
- Modern UI with intuitive navigation
- Real-time updates for long-running processes

### Backend
- Django REST framework for API development
- PostgreSQL database for data storage
- Asynchronous processing for long-running tasks
- Robust error handling and logging

### Performance
- Fast document retrieval even with large archives
- Efficient handling of large file imports
- Optimized search functionality
- Responsive UI regardless of dataset size

### Security
- End-to-end encryption for sensitive documents
- Secure authentication system
- Comprehensive audit logging
- Data backup and recovery mechanisms

## Implementation Priorities
1. Core document upload and storage functionality
2. Excel import with real-time progress tracking
3. PDF processing and OCR capabilities
4. Record management and search
5. Reporting and analytics
6. User management and access control
7. Advanced features and integrations

## 开发路线图

### MVP阶段
1. **基础档案管理功能**
   - 档案CRUD操作
   - 基本搜索和过滤功能
   - 简单的用户认证系统

2. **记录管理基础功能**
   - 记录创建和编辑
   - 基本导入功能
   - 记录与档案关联

3. **简化版PDF处理**
   - PDF上传功能
   - 基本元数据提取
   - 手动分类功能

4. **核心UI组件**
   - 档案列表和详情页
   - 记录管理界面
   - 简单的仪表板

### 增强阶段
1. **高级档案管理**
   - 复杂查询和筛选
   - 档案关系管理
   - 档案历史和版本控制

2. **完整变更单系统**
   - 变更申请创建
   - 审批流程
   - 变更执行和跟踪

3. **增强的PDF处理**
   - 批量处理能力
   - 智能分类
   - OCR优化

4. **报告系统**
   - 报告模板管理
   - 自定义报告生成
   - 批量报告处理

### 完善阶段
1. **高级用户管理**
   - 细粒度权限控制
   - 用户组和角色管理
   - 活动日志和审计

2. **统计和分析**
   - 数据可视化仪表板
   - 趋势分析
   - 定制报表

3. **系统集成**
   - 外部系统API集成
   - 批量导入/导出工具
   - 自动化工作流

4. **性能优化**
   - 缓存策略
   - 查询优化
   - 大规模数据处理能力

## 逻辑依赖链

### 基础层（首先构建）
1. 用户认证和基本权限系统
2. 核心数据模型实现（档案、记录）
3. 基础CRUD API端点
4. 前端基础架构和路由系统

### 核心功能层
1. 档案管理功能实现
2. 记录管理功能实现
3. PDF上传和基础处理功能
4. 前端核心界面组件

### 增强功能层
1. 变更单管理系统
2. 高级PDF处理和OCR集成
3. 报告生成系统
4. 高级搜索和筛选功能

### 完善层
1. 权限管理细化和审计系统
2. 统计分析和数据可视化
3. 系统设置和配置功能
4. 性能优化和扩展功能

## 风险和缓解措施

### 技术挑战
- **挑战**：PDF处理和OCR准确性
  **缓解**：分阶段实现OCR功能，初期可结合人工审核
- **挑战**：处理大量档案时的性能问题
  **缓解**：实现分页、懒加载和异步处理策略
- **挑战**：复杂权限系统的实现
  **缓解**：逐步引入权限控制，确保核心功能先正常运行

### MVP构建
- **挑战**：确定合适的MVP范围
  **缓解**：专注于核心档案和记录管理功能，确保基础功能可用
- **挑战**：用户反馈整合
  **缓解**：建立早期用户测试机制，快速迭代MVP

### 资源约束
- **挑战**：开发资源有限
  **缓解**：优先级明确排序，采用模块化设计便于并行开发
- **挑战**：测试覆盖不足
  **缓解**：实现自动化测试，重点测试核心功能和边界条件

## 附录

### 技术规范
- 前端：React 18+, Next.js, TypeScript, Ant Design
- 后端：Django, Django REST Framework, Celery
- 数据库：SQLite (开发), PostgreSQL (生产)
- 部署：Docker, Docker Compose
- 版本控制：Git 

# Excel导入会话管理与多用户协作重构计划

## 背景

当前系统的Excel导入功能存在状态管理问题，需要进行重构，以提高可靠性、用户体验和多用户协作能力。我们需要实现全局唯一的会话管理，支持多用户查看和处理同一导入会话，并建立完整的会话生命周期管理。

## 主要需求

1. 实现全局唯一的导入会话机制
2. 支持多用户协作处理同一导入会话
3. 完善会话生命周期和状态流转管理
4. 改进异常处理和会话恢复机制
5. 实现完全状态驱动的UI设计
6. 优化前端状态管理架构
7. 实现会话锁定和权限控制

## 技术要求

1. 后端使用Django和Django REST framework
2. 前端使用React和Next.js框架
3. 状态管理使用Zustand
4. 轮询机制获取实时状态更新
5. 会话锁定使用缓存实现
6. 遵循"服务器为唯一真相来源"原则

## 功能详细说明

### 状态流转设计
系统需支持9个主要状态：选择文件、上传、分析开始、分析进行中、分析完成、冲突处理、导入开始、导入进行中、导入完成。同时支持取消和异常恢复机制。

### 状态管理改进
1. 将状态管理逻辑从UI组件分离
2. 实现完全状态驱动的UI
3. 页面加载时立即从服务器获取当前状态
4. 使用乐观更新+服务器确认模式
5. SessionStorage仅存储会话引用信息

### 多用户协作
1. 同权限用户可查看同一会话
2. 实现会话接管机制
3. 记录会话操作的执行用户
4. 防止多用户同时处理冲突

### API设计
需实现6个核心API端点，包括分析Excel、获取进度、获取结果、确认导入、取消导入和获取活跃会话等。

### 优化重点
1. 改进错误处理和恢复机制
2. 优化大文件处理性能
3. 提升用户体验
4. 加强安全控制 