from django.db import transaction
from ..models import ArchiveRecord
import logging

logger = logging.getLogger(__name__)

class ArchiveStatusService:
    """档案状态更新服务类"""
    
    @staticmethod
    @transaction.atomic
    def update_archive_status(unified_numbers: dict) -> dict:
        """
        更新档案记录的归档状态
        
        Args:
            unified_numbers: 字典，key为页码，value为统一编号
            
        Returns:
            dict: 更新结果统计
        """
        result = {
            'total': len(unified_numbers),
            'updated': 0,
            'not_found': 0,
            'not_found_numbers': []
        }
        
        for page, unified_number in unified_numbers.items():
            try:
                record = ArchiveRecord.objects.filter(
                    unified_number=unified_number
                ).first()
                
                if record:
                    record.archive_status = '已归档'
                    record.save()
                    result['updated'] += 1
                else:
                    result['not_found'] += 1
                    result['not_found_numbers'].append(unified_number)
                    
            except Exception as e:
                logger.error(f"更新档案状态失败 - 统一编号: {unified_number}, 错误: {str(e)}")
                
        return result 