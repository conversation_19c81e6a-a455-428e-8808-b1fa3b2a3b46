# Generated by Django 5.1.9 on 2025-05-28 16:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0003_add_results_display_expires_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='importsession',
            name='status',
            field=models.CharField(choices=[('select', '选择文件'), ('upload', '文件上传'), ('analysis_start', '分析开始'), ('analyzing', '分析中'), ('analyzed', '分析完成'), ('processing', '冲突处理'), ('queued', '排队等待导入'), ('import_start', '导入开始'), ('importing', '导入中'), ('completed_successfully', '成功完成导入'), ('completed_with_errors', '完成但有错误'), ('error', '出错'), ('cancelled', '已取消'), ('finalized', '已最终处理')], default='select', max_length=30),
        ),
        migrations.AlterField(
            model_name='sessionoperation',
            name='operation_type',
            field=models.CharField(choices=[('create_session', '创建会话'), ('analyze_start', '分析开始'), ('analyze_complete', '分析完成'), ('error_in_analysis', '分析出错'), ('error_in_analysis_manager', '分析管理器出错'), ('error_in_background_analysis', '后台分析出错'), ('import_start', '导入开始'), ('import_processing', '导入处理中'), ('import_complete', '导入完成'), ('import_error', '导入出错'), ('cancel_cleanup', '取消并清理'), ('takeover', '接管会话'), ('status_change', '状态变更'), ('system_cleanup_expired', '系统清理过期'), ('system_auto_status_update', '系统自动状态更新'), ('system_finalize_cancelled_session', '系统最终处理已取消会话'), ('system_timeout_to_error', '系统超时转错误'), ('system_finalize_completed_session', '系统最终处理已完成会话'), ('system_finalize_error_session', '系统最终处理错误会话'), ('system_resource_cleanup', '系统资源清理'), ('user_acknowledge_and_finalize_session', '用户确认并最终处理会话'), ('cancel_session', '取消会话'), ('import_queued_async', '异步导入排队'), ('import_start_async', '异步导入开始'), ('async_import_finalized', '异步导入完成'), ('async_import_failed', '异步导入失败'), ('error_in_confirm_trigger', '确认触发错误'), ('error', '通用错误')], max_length=50),
        ),
    ]
