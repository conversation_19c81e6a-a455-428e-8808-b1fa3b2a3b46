# AI自动化编程思路

## 计划或需求等内容分为

- AI能自主完成
- AI依靠人类反馈进行的

## 结构化说明

- 文档中的结构化是指最好能够结构化入数据库的内容，能够程序化检索或程序化处理的  
- 将结构化的处理逻辑，合理的打包成一套mcp服务

## 开发需求及维护开发管理需求

- 满足开发需求的同时，必须满足开发过程的管理需求。
- 保持过程管理与开发进度等内容完全对齐
- 保证始终能让无状态的AI理解项目并精准参与项目的具体环节

## 能够让AI并行处理项目（展望）

- 并行处理需求的不同环节
- 并行开发项目的各个部分
- 代码编写及文档编写的并行
- 并行的业务设计或流程设计等各种设计需求
- 如何处理好并行的沟通问题

** 操作并记录日志

- 在日志中更新错误的尝试及正确的处理
- 一次多轮会话或一类主题维护一份日志
- 将日志的内容结构化存储或结构化维护日志到数据库
- 将零散的问题或者日志内容合理的结构化分解并利用
- 能结构化提取也能整体利用（能将设计、进度、操作等一系列内容，化整为零，也能按需将其重新组织成一体，从而得出完整的上下文）

## 业务代码与测试代码并行

- 合理编排业务代码，确保每部分内容都能对应测试代码
- 测试代码需保证与业务代码对齐。一般是循环迭代更新，最终达到正常且对齐的状态。
- 测试代码保留测试结果文档或结构化数据
- 测试代码出错时，修正错误且必须判断是否反映业务代码问题
  - 如果业务代码问题，则在保证**业务逻辑、业务需求**的前提下，调整业务代码与修正的测试代码对齐
    -如果**不能保证***业务逻辑、业务需求*的前提下，则必须要沟通代码决策者来重新决策方向
  - 如果仅是测试代码问题，则修正就算完成

## 代码架构设计

- 代码需要有整体的架构原则
- 分业务或者模块时，应有具体的、考虑整体性的架构设计
- 架构可以采用大架构嵌套小架构
- 架构设计应与业务、资源等需求一致

## 代码文件组织

- 代码文件组织应有架构设计一致，考虑整体性及特殊性
- 分业务或者模块时，考虑具体的代码组织
- 业务代码编写时，优先考虑代码文件组织结构
  - 或者代码编写时，先在一个自定义的临时耦合区来编写小型业务代码，验证后，重构分离业务到规范的组织结构、补全对齐测试代码，并清理或者合理归档耦合区代码
  - 耦合区文件最好能结构化归档，可在出问题时利用排查、方便回看
- 合理设计临时耦合区，最好能程序化生成适合具体业务或者项目的耦合区文件结构
- 代码文件组织结构要有结构文档，最好可以将代码组织结构进行结构化，并关联内在信息
