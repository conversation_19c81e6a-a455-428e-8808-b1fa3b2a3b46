# React 组件设计文档: `ExcelImportReportSummary`

**文档版本**: 1.0
**日期**: 2025-05-29 (根据对话时间生成)
**负责人**: AI Assistant (辅助 User)

## 1. 组件目标与范围

* **目标**: 创建一个独立的、可复用的React组件 (`ExcelImportReportSummary.tsx`)，专门用于清晰、一致地展示Excel导入操作完成后的详细统计摘要报告。该组件应能有效地呈现导入过程的各个阶段（分析、用户决策、执行）的统计数据以及最终的总体总结。
* **核心职责**: 接收格式化的统计数据作为props，并将其渲染为用户友好的报告界面。
* **范围**:
  * 展示导入会话的元数据（如文件名、批次号）。
  * 根据导入的总体结果（完全成功、部分成功/有错误、完全失败）显示顶层的状态通知 (Alert)。
  * 分阶段（分析、用户决策、导入执行、总体总结）展示详细的统计指标。
  * 对错误计数进行视觉突出。
  * 如果分析阶段或导入执行阶段存在行级错误，并且错误数量较少，则在对应阶段下方选择性地即时展示少量错误摘要。
  * 引导用户查看更详细的导入历史日志（通过父组件提供的链接功能）。
* **不包含**:
  * 数据获取逻辑（所有数据均通过props传入）。
  * 会话操作的业务逻辑及按钮（例如："确认结果已查看"、"开始新的导入"、"查看完整历史日志"按钮等，这些由父组件根据应用流程和权限控制）。本组件专注于内容的"呈现"。

## 2. 组件Props接口定义

```typescript
// 建议位置: frontend/types/excel-import.ts 或组件内部定义

export interface DetailedErrorItem {
  row?: number;         // Excel原始行号 (从1开始计数)
  field?: string;       // 问题字段的Excel列名 (需要父组件或此组件进行名称映射)
  message: string;      // 主要错误信息/原因
  // code?: string;     // 可选的内部错误码
  // detail?: string;   // 可选的更详细技术错误描述
  // commission_number?: string; // 可选的关联委托编号
  // sample_number?: string;     // 可选的关联样品编号
}

export interface ImportDetailedReport {
  // 分析/行处理阶段的错误列表 (ExcelImportService._process_dataframe 中收集的 error_records)
  errors?: DetailedErrorItem[]; 
  // 导入Task执行阶段的错误列表 (ExcelImportService._process_dataframe 中收集的 error_records)
  // 注意：根据后端实现，这些可能合并到 errors，或有单独字段如 import_task_processing_errors
  import_task_processing_errors?: DetailedErrorItem[]; 
  // 其他结构化报告数据，例如成功创建/更新的ID列表 (如果需要展示)
  // created_ids?: string[];
  // updated_ids?: string[];
}

// 此接口对应前端 finalImportResults state 的结构, 数据源是 ImportLog 模型
export interface ImportReportData {
  // --- 元数据 ---
  file_name?: string;
  batch_number?: string;
  status?: 'completed' | 'partial' | 'failed'; // ImportLog 的 status
  error_message?: string; // ImportLog.error_log 的内容 (顶层/总体错误信息)
  detailed_report?: ImportDetailedReport; // 结构化的详细报告内容

  // --- 分析阶段统计 ---
  analysis_total_rows_read?: number;
  analysis_failed_rows?: number;
  analysis_skipped_identical?: number;
  analysis_found_new_count?: number;
  analysis_found_update_count?: number;
  analysis_successfully_parsed_rows?: number;

  // --- 用户决策阶段统计 ---
  user_decision_skipped_update_count?: number;
  user_decision_confirmed_update_count?: number; // 确认此字段在 ImportLog 中存在并被填充

  // --- 导入Task (数据库执行) 阶段统计 ---
  import_task_total_records_submitted?: number;
  import_task_created_count?: number;
  import_task_updated_count?: number;
  import_task_unchanged_count?: number; // 执行时发现与DB内容相同而跳过的
  import_task_processed_successfully_count?: number; // (创建+更新+执行时无变化)
  import_task_failed_count?: number;

  // --- Overall 统计 (大部分直接来自 ImportLog 的 overall_ 前缀字段) ---
  overall_total_initial_records?: number; // 等同于 analysis_total_rows_read
  overall_user_decision_skipped_updates?: number; // 等同于 user_decision_skipped_update_count
  overall_final_created_count?: number;    // 等同于 import_task_created_count
  overall_final_updated_count?: number;    // 等同于 import_task_updated_count
  overall_skipped_by_system_total?: number; // (analysis_skipped_identical + import_task_unchanged_count)
  overall_skipped_total?: number;           // (overall_skipped_by_system_total + user_decision_skipped_update_count)
  overall_processed_successfully_total?: number; // (import_task_processed_successfully_count + user_decision_skipped_update_count + analysis_skipped_identical)
  overall_failed_total?: number;            // (analysis_failed_rows + import_task_failed_count)
  
  // 其他可能从 ImportLog 传递过来的字段，例如 import_log_id (用于父组件生成详细日志链接)
  import_log_id?: string | number; 
}

export interface ExcelImportReportSummaryProps {
  reportData: ImportReportData | null; // 允许传入 null，组件内部优雅处理空状态或加载状态提示
  // 可选：从模型字段名到Excel列名的映射，用于更友好地展示错误字段中的字段名
  // 例如: { "sample_number": "样品编号", "commission_datetime": "委托日期" }
  // 如果 detailed_report.errors 中的 field 本身已经是Excel列名，则此 prop 可能不需要。
  excelColumnNameMapping?: Record<string, string>; 
}
```

## 3. 组件结构与UI展示方案

* **文件位置**: `frontend/components/records/import/excel-import-report-summary.tsx` (或根据项目结构调整)
* **依赖**: `React`, `lucide-react` (图标), `shadcn/ui` 组件 (如 `Alert`, `Card`, `Separator`)。

### 3.1. 顶层状态通知 (Alert)

* **目的**: 用户第一眼就能了解导入的总体结果。
* **逻辑**:
  * 根据 `props.reportData.status` (ImportLog的状态) 和 `props.reportData.overall_failed_total` 动态确定。
  * **完全成功**: `reportData.status === 'completed'` 且 `(reportData.overall_failed_total ?? 0) === 0`。
    * 图标: ✅ `CheckCircle2` (绿色)
    * 样式: 绿色系 (e.g., `bg-green-50`, `text-green-800`)
    * 标题: "🎉 导入成功完成！"
    * 描述: "所有记录均已成功处理。共分析 X 条，最终有效处理 Y 条。"
  * **完成但有错误/部分成功**: `reportData.status === 'partial'` 或 (`reportData.status === 'completed'` 且 `(reportData.overall_failed_total ?? 0) > 0`)。
    * 图标: ⚠️ `AlertTriangle` (琥珀色/黄色)
    * 样式: 琥珀色系 (e.g., `bg-amber-50`, `text-amber-800`)
    * 标题: "⚠️ 导入完成，但有部分记录处理失败"
    * 描述: "共分析 X 条记录。其中 Y 条被有效处理，但有 Z 条记录处理失败。请查看下方摘要和详细日志。"
  * **导入失败**: `reportData.status === 'failed'`。
    * 图标: ❌ `AlertCircle` (红色)
    * 样式: 红色系 (e.g., `bg-red-50`, `text-red-800`)
    * 标题: "❌ 导入失败"
    * 描述: "导入过程遇到严重错误。共 Z 条记录处理失败。请检查下方摘要和详细日志。"
  * **兜底/未知状态**: 如果 `reportData.status` 不是预期值，显示通用处理信息。

### 3.2. 统计摘要卡片 (shadcn/ui Card)

* **CardHeader**:
  * `<CardTitle>导入统计摘要</CardTitle>`
  * `<CardDescription>文件: {reportData.file_name || 'N/A'} (批次号: {reportData.batch_number || 'N/A'})</CardDescription>`
* **CardContent**: 使用CSS Grid (`grid-cols-1 md:grid-cols-2`) 进行布局。

  * **阶段一：分析阶段 (Excel文件解析与初步识别)**
    * 标题: `<h4>分析阶段 (Excel 文件解析)</h4>` (带下边框或分隔线)
    * 指标:
      * 原始读取行数: `{reportData.analysis_total_rows_read ?? 0}`
      * 成功解析行数: `{reportData.analysis_successfully_parsed_rows ?? 0}`
      * **解析/校验失败行数**: `{reportData.analysis_failed_rows ?? 0}`
        * 若大于0，数字用红色，并添加以下提示和详情（如果数据存在）：
        * 文字: "由于数据格式或必填项问题，在分析阶段有 X 条记录未能通过初步校验，未进入后续导入流程。"
        * 错误列表 (基于 `reportData.detailed_report.errors`):
          * 若错误数 > 0 且 ≤ 5: 直接列表展示 `行 {item.row}: 字段 "{mapFieldName(item.field)}" - {item.message}`。
          * 若错误数 > 5: 显示 "存在多条解析失败记录，详情请参考完整导入日志。"

      * 识别为新记录: `{reportData.analysis_found_new_count ?? 0}` (绿色)
      * 识别为可更新记录: `{reportData.analysis_found_update_count ?? 0}` (蓝色)
      * 内容完全相同 (系统跳过): `{reportData.analysis_skipped_identical ?? 0}` (灰色)

  * **阶段二：用户决策阶段 (针对"可更新记录")**
    * (仅当 `reportData.analysis_found_update_count > 0` 时渲染此区块)
    * 标题: `<h4>用户决策阶段</h4>`
    * 指标:
      * 分析发现可更新记录: `{reportData.analysis_found_update_count ?? 0}`
      * 用户确认更新数: `{reportData.user_decision_confirmed_update_count ?? 0}` (蓝色)
      * 用户选择跳过更新数: `{reportData.user_decision_skipped_update_count ?? 0}` (琥珀色)

  * **阶段三：导入执行阶段 (数据库操作)**
    * 标题: `<h4>导入执行阶段 (数据库操作)</h4>`
    * 指标:
      * 提交数据库处理总数: `{reportData.import_task_total_records_submitted ?? 0}`
      * 成功创建新记录: `{reportData.import_task_created_count ?? 0}` (绿色)
      * 成功更新现有记录: `{reportData.import_task_updated_count ?? 0}` (蓝色)
      * 执行时无实质变化 (系统跳过): `{reportData.import_task_unchanged_count ?? 0}` (灰色)
      * **数据库操作失败记录数**: `{reportData.import_task_failed_count ?? 0}`
        * 若大于0，数字用红色，并根据 `reportData.detailed_report.import_task_processing_errors` 提供类似分析阶段的错误详情指引或少量展示。

  * **阶段四：总体总结 (Overall Summary)**
    * 标题: `<h3 className="font-bold text-lg">总体结果</h3>` (更突出)
    * 指标:
      * 最初Excel有效总行数: `<span className="font-bold text-xl">{reportData.overall_total_initial_records ?? 0}</span>`
      * 最终成功创建: `<span className="font-bold text-xl text-green-600">{reportData.overall_final_created_count ?? 0}</span>`
      * 最终成功更新: `<span className="font-bold text-xl text-blue-600">{reportData.overall_final_updated_count ?? 0}</span>`
      * 总跳过记录数: `<span className="font-bold text-xl text-gray-600">{reportData.overall_skipped_total ?? 0}</span>`
        * (可选细分列表) 系统自动跳过: `{reportData.overall_skipped_by_system_total ?? 0}`
        * (可选细分列表) 用户决策跳过: `{reportData.overall_user_decision_skipped_updates ?? 0}`
      * 总失败记录数: `<span className="font-bold text-xl text-red-600">{reportData.overall_failed_total ?? 0}</span>` (可加 ❌ 图标)
        * (可选细分提示) "包括: Y 条分析阶段失败 + Z 条数据库执行失败。"
      * **总体有效处理数**: `<div className="md:col-span-2 mt-2 font-medium text-teal-600 text-lg">最终妥善处理记录总计: <span className="font-bold text-2xl ml-2">{reportData.overall_processed_successfully_total ?? 0}</span> 👍</div>`
        * 说明文本: "此数字包括所有成功导入、用户确认跳过及系统合理跳过的记录。"

### 4. 辅助功能

* **`mapFieldName(modelFieldName: string | undefined, excelColumnMapping?: Record<string, string>): string`**:
  * 一个纯函数，接收模型字段名 (可能来自 `detailed_report.errors[i].field`) 和一个 `excelColumnMapping` (格式：`{ modelFieldName: "Excel列名" }`)。
  * 返回对应的Excel列名（如果找到映射）或原始模型字段名（如果未找到或mapping未提供）。
  * 这个函数可以放在组件内部，或者作为一个共享的工具函数。父组件需要构建并传入 `excelColumnMapping`。

### 5. 空状态处理

* 如果 `props.reportData` 为 `null` 或不包含必要数据（例如，`batch_number` 缺失），组件应显示一个友好的"报告数据加载中..."或"无可用导入报告"的占位符。

### 6. 集成说明

* 该组件将被 `ExcelImportWithConflictResolution` 组件在 `derivedCurrentStep === 'completed'` 且 `finalImportResults` 有效时渲染。
* 父组件 (`ExcelImportWithConflictResolution`) 负责提供 `reportData` prop (即其 `finalImportResults` state) 和可选的 `excelColumnMapping` prop。
* 父组件仍然负责渲染页面级的操作按钮，如"确认结果已查看并关闭"、"开始新的导入"、"查看完整历史日志"（通过 `reportData.import_log_id` 构建链接）。

### 7. 注意事项与待确认

* **`ImportLog.detailed_report` 的确切结构**: 需要与后端确认 `detailed_report` 中 `errors` 和 `import_task_processing_errors` (或其他相关字段) 的实际JSON结构和字段名，以确保前端能正确解析和展示行级错误详情。
* **`user_decision_confirmed_update_count`**: 再次确认此字段已在 `ImportLog` 模型中，并且后端会正确填充它，前端才能直接展示。
* **字段名映射 (`excelColumnMapping`)**: 确认父组件是否有能力和责任构建并传递这个映射。如果 `detailed_report.errors[i].field` 已经是用户友好的Excel列名，则此prop和相关逻辑可以简化或移除。

---

这个设计文档应该为您分离和实现 `ExcelImportReportSummary` 组件提供了一个良好的起点。
我已将此内容保存到您工作区 `AgentReadme/design/` 目录下的 `excel_import_report_summary_component.md` 文件中。
如果您需要我基于此文档开始生成组件的初步代码，请告诉我。
