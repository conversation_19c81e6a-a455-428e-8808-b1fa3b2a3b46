# report_issuing/services/data_services/issue_form_data_service.py
import logging
from typing import Dict, Any, List
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.utils.dateparse import parse_datetime
from django.utils import timezone

from report_issuing.models import IssueForm, IssueFormItem
from archive_records.models import ArchiveRecord
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class IssueFormDataService:
    """
    数据服务层：负责所有与 IssueForm 和 IssueFormItem 模型相关的数据库操作。
    它封装了ORM的调用细节，向上层提供清晰、业务化的接口。
    """
    
    @staticmethod
    @transaction.atomic
    def create_form_with_items(form_data: Dict[str, Any], items_data: List[Dict[str, Any]], user: User) -> IssueForm:
        """
        在一个事务中，创建主发放单和其关联的初始条目。
        这是被 IssueInitializationService 调用的核心方法。
        """
        # 处理日期字段 - 草稿状态下使用占位符日期，发放时才设置真正的发放日期
        processed_form_data = form_data.copy()
        if 'issue_date' in processed_form_data and isinstance(processed_form_data['issue_date'], str):
            # 尝试解析ISO格式的日期字符串
            parsed_date = parse_datetime(processed_form_data['issue_date'])
            if parsed_date:
                processed_form_data['issue_date'] = parsed_date
            else:
                # 如果解析失败，使用当前时间作为占位符
                logger.warning(f"无法解析日期字符串: {processed_form_data['issue_date']}，使用当前时间作为占位符")
                processed_form_data['issue_date'] = timezone.now()
        else:
            # 草稿状态下如果没有传递issue_date，使用当前时间作为占位符
            # 真正的发放日期会在状态变为'issued'时设置
            processed_form_data['issue_date'] = timezone.now()
            logger.info("草稿状态下使用当前时间作为发放日期占位符，实际发放时会更新")
        
        # 1. 创建主表单，状态默认为 draft (小写)
        issue_form = IssueForm.objects.create(
            issuer=user,
            status='draft',
            **processed_form_data 
        )
        logger.info(f"已创建主发放单记录，ID: {issue_form.id}")

        # 2. 准备并批量创建条目
        items_to_create = []
        for item in items_data:
            # 根据issue_type设置first/second字段
            issue_type = item.get('issue_type', 'first')
            first_issue = issue_type == 'first'
            second_issue = issue_type == 'second'
            
            # 通过委托编号查找ArchiveRecord对象
            commission_number = item['archive_record_id']  # 这实际上是委托编号
            try:
                archive_record = ArchiveRecord.objects.get(commission_number=commission_number)
            except ArchiveRecord.DoesNotExist:
                logger.error(f"档案记录不存在，委托编号: {commission_number}")
                raise ValueError(f"档案记录不存在，委托编号: {commission_number}")
            
            items_to_create.append(IssueFormItem(
                issue_form=issue_form,
                archive_record=archive_record,  # 传递ArchiveRecord对象
                copies=item['copies'],
                first=first_issue,
                second=second_issue
            ))
        
        if items_to_create:
            IssueFormItem.objects.bulk_create(items_to_create)
            logger.info(f"已为发放单 {issue_form.id} 批量创建 {len(items_to_create)} 个条目。")
        
        return issue_form

    @staticmethod
    def get_form_by_id(form_id: int) -> IssueForm:
        """
        根据ID获取一个 IssueForm 实例。如果不存在则抛出404异常。
        """
        return get_object_or_404(IssueForm, pk=form_id)

    @staticmethod
    def get_form_by_id_with_related(form_id: int) -> IssueForm:
        """
        根据ID获取一个 IssueForm 实例，并预加载其关联的条目和档案记录。
        这有助于在序列化时避免N+1查询问题。
        """
        return get_object_or_404(
            IssueForm.objects.prefetch_related(
                'items', 
                'items__archive_record'
            ), 
            pk=form_id
        )

    @staticmethod
    def update_form_status(form_id: int, new_status: str) -> bool:
        """
        更新指定 IssueForm 的状态。
        如果状态变为'issued'，会自动设置真正的发放日期。
        """
        # 构建更新数据
        update_data = {'status': new_status}
        
        # 如果状态变为'issued'，设置真正的发放日期
        if new_status == 'issued':
            update_data['issue_date'] = timezone.now()
            logger.info(f"发放单 {form_id} 状态变为已发放，设置发放日期为当前时间")
        
        updated_count = IssueForm.objects.filter(pk=form_id).update(**update_data)
        logger.info(f"已将 ID 为 {form_id} 的发放单状态更新为 {new_status}。影响行数: {updated_count}。")
        return updated_count > 0
        
    @staticmethod
    def update_form_details(form_id: int, details_data: Dict[str, Any]) -> bool:
        """
        更新发放单的基础信息（非条目）。
        """
        # 复制 `create_form_with_items` 的日期处理逻辑，以防止类型错误
        if 'issue_date' in details_data and isinstance(details_data['issue_date'], str):
            # 尝试解析日期或日期时间字符串
            parsed_date = parse_datetime(details_data['issue_date'])
            if parsed_date:
                details_data['issue_date'] = parsed_date
            else:
                # 如果解析失败，则从更新数据中移除该键，以避免数据库错误
                logger.warning(f"无法解析日期字符串: {details_data['issue_date']}，将不会更新此字段。")
                del details_data['issue_date']
        
        # 如果处理后没有数据需要更新，则直接返回
        if not details_data:
            return True

        updated_count = IssueForm.objects.filter(pk=form_id).update(**details_data)
        logger.info(f"已更新 ID 为 {form_id} 的发放单详情。影响行数: {updated_count}。")
        return updated_count > 0

    @staticmethod
    @transaction.atomic
    def update_items_for_form(form_id: int, items_data: List[Dict[str, Any]]):
        """
        在一个事务中，通过替换操作来更新发放单的条目。
        它会先删除所有旧条目，然后根据 `items_data` 重新创建。
        """
        # 1. 删除所有现有条目
        deleted_count, _ = IssueFormItem.objects.filter(issue_form_id=form_id).delete()
        logger.info(f"从发放单 {form_id} 中删除 {deleted_count} 个旧条目。")

        # 2. 准备并批量创建新条目
        if items_data:
            new_items = []
            for item in items_data:
                archive_record_id = item.get('archive_record_id')
                if not archive_record_id:
                    logger.warning(f"跳过条目，因为缺少 'archive_record_id': {item}")
                    continue
                
                try:
                    # 假设 archive_record_id 是统一编号
                    archive_record = ArchiveRecord.objects.get(unified_number=archive_record_id)
                except ArchiveRecord.DoesNotExist:
                    try:
                        # 如果统一编号找不到，尝试作为主键查找
                        archive_record = ArchiveRecord.objects.get(pk=archive_record_id)
                    except (ArchiveRecord.DoesNotExist, ValueError):
                        logger.error(f"档案记录不存在，ID或统一编号: {archive_record_id}")
                        raise ValueError(f"档案记录不存在: {archive_record_id}")

                # 重新加入 first/second 逻辑
                issue_type = item.get('issue_type', 'first') # 默认为初次发放
                first_issue = issue_type == 'first'
                second_issue = issue_type == 'second'

                new_items.append(IssueFormItem(
                    issue_form_id=form_id,
                    archive_record=archive_record,
                    copies=item.get('copies', 1),
                    first=first_issue,
                    second=second_issue
                ))
            
            if new_items:
                IssueFormItem.objects.bulk_create(new_items)
                logger.info(f"为发放单 {form_id} 添加 {len(new_items)} 个新条目。")

    @staticmethod
    def hard_delete_form(form_id: int) -> bool:
        """
        硬删除一个发放单及其所有关联条目。
        """
        deleted_count, _ = IssueForm.objects.filter(pk=form_id).delete()
        logger.info(f"已硬删除 ID 为 {form_id} 的发放单。影响行数: {deleted_count}。")
        return deleted_count > 0

    # 你可以根据 DraftStateService 的需要，继续在这里添加其他数据操作方法
    # 例如：
    # @staticmethod
    # def add_items_to_form(...)
    #
    # @staticmethod
    # def remove_items_from_form(...) 