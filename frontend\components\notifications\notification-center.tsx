"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Bell } from "lucide-react"
import { useState, useEffect } from "react"
import { ScrollArea } from "../ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "../ui/tabs"
import { NotificationItem } from "./notification-item"

interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  read: boolean
  createdAt: string
  link?: string
}

export function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  // 模拟获取通知数据
  useEffect(() => {
    // 实际应用中，这里会从API获取通知数据
    // const fetchNotifications = async () => {
    //   const response = await fetch('/api/notifications');
    //   const data = await response.json();
    //   setNotifications(data);
    //   setUnreadCount(data.filter(n => !n.read).length);
    // };
    // fetchNotifications();

    // 模拟数据
    const mockNotifications: Notification[] = [
      {
        id: "1",
        title: "新的更改单",
        message: "用户 张三 创建了一个新的更改单，请审核",
        type: "info",
        read: false,
        createdAt: "2023-12-20 10:30",
        link: "/records/AR-2023-0001/change-order/1",
      },
      {
        id: "2",
        title: "归档任务完成",
        message: "PDF文件 'XYZ-2023-001-环评报告.pdf' 已成功归档",
        type: "success",
        read: false,
        createdAt: "2023-12-19 15:45",
        link: "/archive/tasks/TASK-2023-001",
      },
      {
        id: "3",
        title: "导入失败",
        message: "Excel文件 '2023年12月台账.xlsx' 导入失败，请检查文件格式",
        type: "error",
        read: false,
        createdAt: "2023-12-18 09:20",
        link: "/import/history/IMP-2023-006",
      },
      {
        id: "4",
        title: "发放单待确认",
        message: "发放单 FH-2023-0005 已创建，等待确认发放",
        type: "warning",
        read: true,
        createdAt: "2023-12-17 14:10",
        link: "/reports/FH-2023-0005",
      },
      {
        id: "5",
        title: "系统更新",
        message: "系统将于今晚22:00-23:00进行维护更新，请提前保存工作",
        type: "info",
        read: true,
        createdAt: "2023-12-16 11:25",
      },
    ]

    setNotifications(mockNotifications)
    setUnreadCount(mockNotifications.filter((n) => !n.read).length)
  }, [])

  const handleMarkAllAsRead = async () => {
    // 实际应用中，这里会调用API将所有通知标记为已读
    // const response = await fetch('/api/notifications/mark-all-read', {
    //   method: 'POST',
    // });
    // if (response.ok) {
    //   setNotifications(notifications.map(n => ({ ...n, read: true })));
    //   setUnreadCount(0);
    // }

    // 模拟标记为已读
    setNotifications(notifications.map((n) => ({ ...n, read: true })))
    setUnreadCount(0)
  }

  const handleMarkAsRead = async (id: string) => {
    // 实际应用中，这里会调用API将通知标记为已读
    // const response = await fetch(`/api/notifications/${id}/mark-read`, {
    //   method: 'POST',
    // });
    // if (response.ok) {
    //   setNotifications(notifications.map(n => n.id === id ? { ...n, read: true } : n));
    //   setUnreadCount(prev => prev - 1);
    // }

    // 模拟标记为已读
    setNotifications(notifications.map((n) => (n.id === id ? { ...n, read: true } : n)))
    setUnreadCount((prev) => Math.max(0, prev - 1))
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (activeTab === "all") return true
    if (activeTab === "unread") return !notification.read
    return true
  })

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
              {unreadCount > 9 ? "9+" : unreadCount}
            </span>
          )}
          <span className="sr-only">通知</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[380px] p-0" align="end">
        <div className="flex items-center justify-between border-b p-3">
          <h4 className="font-medium">通知</h4>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead} className="h-8 px-2 text-muted-foreground">
              全部标为已读
            </Button>
          )}
        </div>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b px-3">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="unread">
                未读
                {unreadCount > 0 && <span className="ml-1 text-xs">({unreadCount})</span>}
              </TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="all" className="m-0">
            <ScrollArea className="h-[300px]">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-[300px] text-muted-foreground">暂无通知</div>
              ) : (
                <div>
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                    />
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
          <TabsContent value="unread" className="m-0">
            <ScrollArea className="h-[300px]">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-[300px] text-muted-foreground">暂无未读通知</div>
              ) : (
                <div>
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                    />
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
        <div className="border-t p-2 text-center">
          <Button variant="link" size="sm" asChild className="text-xs">
            <a href="/notifications">查看全部通知</a>
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
