# Operation Document: 移除向后兼容的process_pdf_task函数

## 📋 Change Summary

**Purpose**: 移除向后兼容的process_pdf_task函数，简化代码结构并减少维护成本
**Scope**:

- `archive_processing/tasks.py`
- `archive_processing/services/task_service.py`
**Associated**: #AFM-refactor-atomic

## 🔧 Operation Steps

### 📊 OP-001: 分析向后兼容函数的使用情况

**Precondition**: 代码中存在仅作为向后兼容保留的`process_pdf_task`函数，实际上只是调用`dispatch_pdf_processing`
**Operation**:

1. 使用grep搜索确认`process_pdf_task`的调用点
2. 分析函数依赖关系，确定可以安全移除并替换为`dispatch_pdf_processing`
3. 确认调用参数兼容性
**Postcondition**: 明确`process_pdf_task`的移除策略和影响范围

### ✏️ OP-002: 修改依赖此函数的调用点

**Precondition**: 确认`process_pdf_task`的主要调用点在`task_service.py`中
**Operation**: 修改`TaskService.create_task`方法，替换为直接调用`dispatch_pdf_processing`
**Postcondition**: 所有`process_pdf_task`的调用点都已更新为使用`dispatch_pdf_processing`

### ✏️ OP-003: 从tasks.py中移除函数定义

**Precondition**: 已更新所有调用点，确保没有代码依赖该函数
**Operation**: 从`tasks.py`中删除`process_pdf_task`函数定义及相关注释，添加说明注释
**Postcondition**: 函数已移除，代码更加简洁

## 📝 Change Details

### CH-001: 修改TaskService.create_task调用

**File**: `archive_processing/services/task_service.py`
**Before**:

```python
# CHANGE: [2024-07-26] 延迟导入 process_pdf_task 以解决循环依赖问题
from ..tasks import process_pdf_task

# CHANGE: [2025-04-18] 修复参数不匹配问题
# 原来的代码：process_pdf_task.delay(task_record.task_id, uploaded_file.file_id, target_text, user_id)
# 修改为只传递task_id参数，其他所需参数已存储在task_record中
process_pdf_task.delay(task_record.task_id)
```

**After**:

```python
# CHANGE: [2024-07-26] 延迟导入 process_pdf_task 以解决循环依赖问题
# CHANGE: [2025-04-22] 使用dispatch_pdf_processing替换过时的process_pdf_task
from ..tasks import dispatch_pdf_processing

# CHANGE: [2025-04-18] 修复参数不匹配问题
# CHANGE: [2025-04-22] 直接调用dispatch_pdf_processing而不是process_pdf_task
# 原来的代码：process_pdf_task.delay(task_record.task_id, uploaded_file.file_id, target_text, user_id)
# 修改为只传递task_id参数，其他所需参数已存储在task_record中
dispatch_pdf_processing(task_record.task_id)
```

**Rationale**: 使用正式的函数接口，移除冗余的中间层，简化代码结构
**Potential Impact**: 低风险，因为`dispatch_pdf_processing`已被`process_pdf_task`使用且参数兼容

### CH-002: 从tasks.py移除process_pdf_task函数

**File**: `archive_processing/tasks.py`
**Before**:

```python
# 保留原任务函数作为向后兼容（但实现改为调用dispatch_pdf_processing）
@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_pdf_task(self, task_id, use_parallel=True, chunk_size=20):
    """
    Celery 任务：异步处理上传的 PDF 文件

    这是一个向后兼容的入口点，实际会调用dispatch_pdf_processing来选择处理方式

    Args:
        task_id: 任务ID
        use_parallel: 是否使用并行处理
        chunk_size: 每块的页数
    """
    logger.info(
        f"处理PDF任务 {task_id} (use_parallel={use_parallel}, chunk_size={chunk_size})"
    )
    return dispatch_pdf_processing(task_id, use_parallel, chunk_size)
```

**After**:
函数已完全移除，添加了注释说明:

```python
# CHANGE: [2025-04-22] 移除向后兼容的process_pdf_task函数
# 原函数只是简单调用dispatch_pdf_processing，现已直接在task_service.py中调用dispatch_pdf_processing
# 移除此重复函数简化代码结构，减少维护成本，同时消除潜在混淆
# 如需查看原实现，可参考历史版本
```

**Rationale**: 移除不必要的向后兼容代码，简化代码库，减少维护负担
**Potential Impact**: 单元测试或其他直接调用`process_pdf_task`的代码可能需要更新

## ✅ Verification Results

**Method**: 代码审查和功能验证
**Results**:

- 已确认所有`process_pdf_task`的关键调用点都已适当更新
- 测试文件可能需要后续更新
- 代码结构更加清晰，移除了一层不必要的中间调用

**Problems**: 需要注意测试代码中可能存在直接调用`process_pdf_task`的情况
**Solutions**: 在运行测试时如果发现相关错误，需要更新测试代码使用`dispatch_pdf_processing`
