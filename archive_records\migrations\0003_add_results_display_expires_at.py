# Generated by Django 5.1.8 on 2025-05-27 16:43

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0002_remove_importlog_created_count_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='importsession',
            name='results_display_expires_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='结果展示过期时间'),
        ),
        migrations.AlterField(
            model_name='importsession',
            name='status',
            field=models.CharField(choices=[('select', '选择文件'), ('upload', '文件上传'), ('analysis_start', '分析开始'), ('analyzing', '分析中'), ('analyzed', '分析完成'), ('processing', '冲突处理'), ('queued', '排队等待导入'), ('import_start', '导入开始'), ('importing', '导入中'), ('imported', '导入完成'), ('cancelled', '已取消'), ('error', '出错'), ('archived', '已归档/数据固化')], default='select', max_length=20),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['expires_at'], name='archive_rec_expires_09a5ef_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['results_display_expires_at'], name='archive_rec_results_dbb1a2_idx'),
        ),
    ]
