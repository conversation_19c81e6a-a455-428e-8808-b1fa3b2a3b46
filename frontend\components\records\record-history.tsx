"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Filter } from "lucide-react"
import { useEffect, useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import apiClient from "@/lib/apiClient"

interface RecordHistoryProps {
  recordId: string
}

interface FieldChange {
  field: string;
  oldValue: string | null;
  newValue: string | null;
}

interface ArchiveRecordHistory {
  id: number;
  timestamp: string;
  user: string;
  sourceType: string;
  reason: string | null;
  changes: FieldChange[];
  relatedDocumentId: string | null;
}

export function RecordHistory({ recordId }: RecordHistoryProps) {
  const [history, setHistory] = useState<ArchiveRecordHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState("all")

  useEffect(() => {
    const fetchHistory = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const response = await apiClient.get<ArchiveRecordHistory[]>(`/api/archive-records/records/${recordId}/history/`);
        if (response.success && response.data) {
          setHistory(response.data);
        } else {
          setError(response.error || "未能加载历史记录。");
        }
      } catch (err) {
        setError("加载历史记录时发生网络错误。");
      } finally {
        setIsLoading(false);
      }
    };
    
    if (recordId) {
      fetchHistory();
    }
  }, [recordId])

  const filteredHistory = filter === "all" ? history : history.filter((item) => item.sourceType === filter)

  const getSourceTypeBadge = (sourceType: string) => {
    switch (sourceType) {
      case "EXCEL_IMPORT":
        return <Badge variant="outline">Excel导入</Badge>
      case "ARCHIVE_PROCESS":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            档案入库
          </Badge>
        )
      case "ISSUE_FORM":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            发放单
          </Badge>
        )
      case "CHANGE_ORDER_CREATE":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            更改单创建
          </Badge>
        )
      case "OPERATION_ERROR":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            错误修正
          </Badge>
        )
      case "CHANGE_ORDER_DELETE":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            更改单删除
          </Badge>
        )
      case "SYSTEM":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            系统
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  if (isLoading) {
    return <div className="flex justify-center items-center h-40">加载中...</div>
  }

  if (error) {
    return <div className="flex justify-center items-center h-40 text-red-500">{error}</div>
  }

  if (history.length === 0) {
    return <div className="flex justify-center items-center h-40">暂无变更历史</div>
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="筛选来源" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部来源</SelectItem>
                <SelectItem value="EXCEL_IMPORT">Excel导入</SelectItem>
                <SelectItem value="ARCHIVE_PROCESS">档案入库</SelectItem>
                <SelectItem value="ISSUE_FORM">发放单</SelectItem>
                <SelectItem value="CHANGE_ORDER_CREATE">更改单创建</SelectItem>
                <SelectItem value="OPERATION_ERROR">错误修正</SelectItem>
                <SelectItem value="CHANGE_ORDER_DELETE">更改单删除</SelectItem>
                <SelectItem value="SYSTEM">系统</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">高级筛选</span>
            </Button>
          </div>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出历史
          </Button>
        </div>

        <div className="space-y-8">
          {filteredHistory.map((item) => (
            <div key={item.id} className="relative pl-6 pb-8 border-l border-gray-200 dark:border-gray-800">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-primary"></div>
              <div className="space-y-2">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm font-medium">{item.timestamp}</span>
                  <span className="text-sm text-muted-foreground">由 {item.user} 操作</span>
                  {getSourceTypeBadge(item.sourceType)}
                  {item.relatedDocumentId && (
                    <span className="text-sm text-muted-foreground">关联单据: {item.relatedDocumentId}</span>
                  )}
                </div>
                {item.reason && <p className="text-sm italic">原因: {item.reason}</p>}
                <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-3">
                  <h4 className="text-sm font-medium mb-2">变更内容</h4>
                  <div className="space-y-2">
                    {item.changes.map((change: FieldChange, index: number) => (
                      <div key={index} className="grid grid-cols-3 gap-2 text-sm">
                        <div className="font-medium">{change.field}</div>
                        <div className="text-muted-foreground">
                          {change.oldValue === null ? "(空)" : change.oldValue}
                        </div>
                        <div>{change.newValue === null ? "(空)" : change.newValue}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
