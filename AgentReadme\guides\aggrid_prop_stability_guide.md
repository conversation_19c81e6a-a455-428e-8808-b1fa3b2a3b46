# AG Grid 与 React：避免因 Props 变化导致不必要刷新的指南

## 1. 问题描述

在 `frontend/app/records/ledger/page.tsx` 档案台账页面中，我们遇到了一个问题：当用户点击自定义的日期范围选择器按钮（用于打开或关闭其弹出层）时，AG Grid 表格会意外地重新加载数据。理想情况下，这类纯粹的 UI 交互（如打开/关闭一个弹出层）不应该触发表格数据的刷新。

## 2. 核心原因：React Props 的引用稳定性

React 组件在其父组件重新渲染时也会重新渲染（除非有 `React.memo` 或其他优化）。当 AG Grid (`<AgGridReact />`) 这样的复杂子组件接收对象或函数作为 props 时，如果这些 props 的引用在每次父组件渲染时都发生变化（即便是内容完全相同的对象或函数，但它们是新的实例），AG Grid 可能会认为其配置已更改。这可能导致 AG Grid 进行内部状态重置、重新应用配置，甚至在 Server-Side Row Model (SSRM) 模式下重新触发数据获取（调用 `getRows`）。

在我们的案例中，父组件 `RecordsLedgerPage` 因为日期选择器弹出框的显示状态 (`datePickerOpen`) 改变而重新渲染。在此过程中，若传递给 `<AgGridReact />` 的某些 props 是在渲染函数中即时创建的（例如，内联的对象字面量），它们的引用就会不稳定。

AG Grid 的调试日志中出现的类似 "Updated property X from ... to ..." 的信息，即便 X 的内容看起来没变，也通常暗示着 X 的对象引用发生了变化。

## 3. 解决方案与关键修复点

核心解决方案是确保传递给 `<AgGridReact />` 组件的关键 props（尤其是对象和函数类型）在父组件不必要的重新渲染之间保持稳定的引用。这主要通过 React 的 `useMemo` 和 `useCallback` 钩子来实现。

### 3.1. 原则：保持 Props 引用的稳定

- **`useMemo`**: 用于缓存计算结果（包括对象和数组）。当你想确保一个对象的引用仅在其依赖项改变时才改变，就应该使用 `useMemo`。

    ```javascript
    const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
    ```

- **`useCallback`**: 用于缓存函数定义。确保一个函数 prop 的引用仅在其依赖项改变时才改变。

    ```javascript
    const memoizedCallback = useCallback(() => {
      doSomething(a, b);
    }, [a, b]);
    ```

### 3.2. 具体修复措施 (`frontend/app/records/ledger/page.tsx`)

#### a) 稳定化 `sideBar` Prop

**问题**: `sideBar` prop 是作为一个内联对象字面量直接在 JSX 中定义的，导致每次 `RecordsLedgerPage` 渲染时都会创建一个新的 `sideBar` 对象实例。

```typescript
// 之前 (不稳定的引用):
<AgGridReact
  // ...
  sideBar={{
    toolPanels: [
      {
        id: 'columns',
        labelDefault: '列管理',
        // ...其他配置
      }
    ],
    defaultToolPanel: 'columns'
  }}
  // ...
/>
```

**修复**: 使用 `useMemo` 将 `sideBar` 的配置对象化并缓存其引用。由于此配置是静态的，依赖数组为空 `[]`。

```typescript
// 之后 (稳定的引用):
const sideBarConfig = useMemo(() => ({
  toolPanels: [
    {
      id: 'columns',
      labelDefault: '列管理',
      labelKey: 'columns',
      iconKey: 'columns',
      toolPanel: 'agColumnsToolPanel',
      toolPanelParams: {
        suppressRowGroups: false,
        suppressValues: true,
        suppressPivots: true,
        suppressPivotMode: true,
        suppressColumnFilter: false,
        suppressColumnSelectAll: false,
        suppressColumnExpandAll: false
      }
    }
  ],
  defaultToolPanel: 'columns'
}), []);

// ...
<AgGridReact
  // ...
  sideBar={sideBarConfig} // 使用 memoized 的配置
  // ...
/>
```

#### b) 稳定化 `serverSideDatasource` Prop

**问题**: `serverSideDatasource` prop 被赋值为 `createServerSideDatasource()` 函数的调用结果。尽管 `createServerSideDatasource` 本身可能已通过 `useCallback` 进行了 memoization，但每次在渲染路径中 *调用* 它 (`createServerSideDatasource()`) 都会返回一个新的数据源对象实例 (`{ getRows: ... }`)。AG Grid 因此检测到 `serverSideDatasource` prop 发生了变化。

```typescript
// 之前 (不稳定的数据源对象引用):
const createServerSideDatasource = useCallback((): IServerSideDatasource => {
  return {
    getRows: async (params: IServerSideGetRowsParams) => { /* ... */ }
  };
}, [fetchArchiveLedgerRecords]);

// ...
<AgGridReact
  // ...
  serverSideDatasource={createServerSideDatasource()} // 每次渲染都调用，返回新对象
  // ...
/>
```

**修复**: 使用 `useMemo` 来缓存 `createServerSideDatasource()` 返回的 *数据源对象本身*。其依赖项是 `createServerSideDatasource` 函数（它本身已经是 memoized 的）。

```typescript
// 之后 (稳定的数据源对象引用):
const createServerSideDatasource = useCallback((): IServerSideDatasource => {
  return {
    getRows: async (params: IServerSideGetRowsParams) => { /* ... */ }
  };
}, [fetchArchiveLedgerRecords]);

// Memoize 数据源对象本身
const memoizedServerSideDatasource = useMemo(() => createServerSideDatasource(), [createServerSideDatasource]);

// ...
<AgGridReact
  // ...
  serverSideDatasource={memoizedServerSideDatasource} // 使用 memoized 的数据源对象
  // ...
/>
```

**注意**: 在 `onGridReady` 回调中通过 `params.api.setGridOption('serverSideDatasource', dataSource)` 初始化数据源是正确的做法。上述修复主要针对的是直接在 `<AgGridReact>` 组件上通过 prop 传递数据源的场景，确保该 prop 引用稳定。

#### c) `commission_datetime` 列的筛选器配置 (回顾)

为了实现完全由自定义弹出框控制 `commission_datetime`（委托日期）列的筛选，我们之前已将其在 `columnDefs` 中的定义修改为：

```typescript
{
  headerName: "委托日期",
  field: "commission_datetime",
  sortable: true,
  filter: false, // 关键: 禁用 AG Grid 的内置筛选器 UI 和默认行为
  width: 120,
  valueFormatter: dateFormatter
  // filterParams 不再需要，因为 filter: false
},
```

将 `filter: false` 确保了 AG Grid 不会尝试为该列自动管理或应用其内置的日期筛选逻辑，所有筛选行为都通过 `applyDateFilter` 函数中调用 `gridApi.setFilterModel()` 来显式控制。这一步是前提，确保了对日期筛选的完全掌控权，避免了与 AG Grid 内置行为的冲突。

## 4. 通用建议与最佳实践

- **仔细检查 Props**: 对于传递给 AG Grid（或其他类似重型组件）的对象和函数 props，务必检查它们是否在每次父组件渲染时都生成了新的引用。
- **`useMemo` 和 `useCallback` 的依赖数组**: 正确设置依赖数组至关重要。空数组 `[]` 表示仅在组件挂载时创建一次。如果 props 确实依赖于某些值，应将这些值加入依赖数组。
- **AG Grid 日志**: AG Grid 本身提供了详细的控制台日志，可以帮助识别哪些 props 被视为"已更新"。
- **React DevTools**: 使用 React DevTools Profiler 可以帮助分析组件重渲染的原因和次数，有助于定位性能瓶颈和不必要的更新。

## 5. 总结

通过确保传递给 AG Grid 的核心 props（特别是 `sideBar` 和 `serverSideDatasource`）具有稳定的对象引用，我们成功解决了因日期选择器 UI 状态变化而导致表格意外刷新的问题。这不仅改善了用户体验，也提高了应用的性能。在 React 中使用复杂第三方库时，理解并管理好 props 的引用稳定性是一个常见的关键点。
