"""
Excel导入会话管理服务

此模块负责管理Excel导入的会话状态，支持两阶段导入流程：
1. 分析阶段：分析Excel数据，识别冲突
2. 确认阶段：根据用户决策执行导入

重构: 使用数据库存储会话信息，通过ImportSession模型管理会话状态
"""

import os
import uuid
import json
import pandas as pd
import tempfile
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass

from django.core.cache import cache
from django.conf import settings
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q
from celery import current_app
from django.db import OperationalError

from .excel_import import ExcelImportService, DuplicateStrategy
from .excel_conflict_analyzer import ExcelConflictAnalyzer
from ..models import (
    ImportSession,
    ImportSessionStatus,
    SessionOperation,
    ImportConflictDetail,
    ImportLog,
)

# 配置日志
logger = logging.getLogger(__name__)

# 会话过期时间（分钟）
SESSION_EXPIRATION_MINUTES = getattr(
    settings, "EXCEL_IMPORT_SESSION_EXPIRATION_MINUTES", 30
)

# CHANGE: [YYYY-MM-DD] 定义活动会话的终止状态列表 #任务1.4
CONCLUSIVE_SESSION_STATUSES = [
    ImportSessionStatus.CANCELLED, 
    ImportSessionStatus.ERROR, 
    ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
    ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
    ImportSessionStatus.FINALIZED,
]


class ConflictResolution:
    """冲突解决决策"""

    UPDATE = "update"  # 更新现有记录
    SKIP = "skip"  # 跳过此记录
    CREATE = "create"  # 创建新记录


class ImportSessionManager:
    """Excel导入会话管理器"""

    # 缓存键前缀 - 冲突记录仍使用缓存存储
    CACHE_CONFLICTS_PREFIX = "excel_import_conflicts:"

    def __init__(self):
        """初始化会话管理器"""
        self.import_service = ExcelImportService()
        self.conflict_analyzer = ExcelConflictAnalyzer()

    # CHANGE: [2025-05-28] 新增内部协调方法，用于确保会话状态实时准确
    def _ensure_session_status_is_current(
        self, session: Optional[ImportSession]
    ) -> Optional[ImportSession]:
        """
        内部协调方法：检查给定会话对象的状态，并根据定义的过期规则和状态流转规则按需更新。
        如果状态发生变化，则保存会话并记录SessionOperation。
        返回处理后的会话对象（可能状态已更新），或在错误时返回None。
        """
        if not session:
            return None

        # 获取原始状态，用于后续比较和日志记录
        # 注意：session对象可能是从外部传入的，其状态可能不是DB中的最新状态
        # 因此，在事务内，我们会用select_for_update重新获取并锁定它
        original_status_in_memory = session.status
        now = timezone.now()
        status_changed_in_tx = False
        log_op_type = "system_auto_status_update"
        log_details_message = f"系统按需检查会话 {session.session_id} 状态 ({original_status_in_memory})。"

        # 如果内存中的状态已是FINALIZED，通常意味着它已被处理，直接返回
        if original_status_in_memory == ImportSessionStatus.FINALIZED:
            return session

        try:
            with transaction.atomic():
                # 在事务中通过pk和select_for_update获取最新数据并锁定行，确保操作的原子性和一致性
                session_in_tx = ImportSession.objects.select_for_update().get(
                    pk=session.pk
                )

                # 用从数据库新鲜获取的、已锁定的实例更新我们的工作副本
                # session = session_in_tx # 这样做会改变外部传入session的引用，可能不是最佳实践
                # 更好的做法是直接在 session_in_tx 上操作，并在最后返回它
                # 或者，如果方法签名坚持要修改并返回传入的session对象，需要小心处理并发
                # 此处我们选择在 session_in_tx 上操作，并最后返回它（或其更新版本）

                current_db_status = session_in_tx.status
                # 如果数据库中的状态已经是FINALIZED，说明在调用此方法到进入事务之间，它已被其他进程处理
                if current_db_status == ImportSessionStatus.FINALIZED:
                    logger.info(
                        f"[EnsureStatus] 会话 {session_in_tx.session_id} 在事务开始时已是FINALIZED状态。无需操作。"
                    )
                    return session_in_tx

                # 1. CANCELLED -> FINALIZED (立即转换)
                if session_in_tx.status == ImportSessionStatus.CANCELLED:
                    session_in_tx.status = ImportSessionStatus.FINALIZED
                    session_in_tx.error_message = (
                        session_in_tx.error_message or "会话已取消并由系统最终处理。"
                    )
                    log_op_type = "system_finalize_cancelled_session"
                    log_details_message = f"会话从CANCELLED自动转为FINALIZED。原取消原因: {session_in_tx.error_message}"
                    status_changed_in_tx = True
                    logger.info(
                        f"[EnsureStatusUpdate] 会话 {session_in_tx.session_id} ({current_db_status}) 从 CANCELLED 转为 FINALIZED。"
                    )

                # 2. 中间状态的通用过期 -> ERROR (仅当状态未被规则1改变时)
                if not status_changed_in_tx:
                    # CHANGE: [2025-06-01] 更新中间状态列表以包含新的冲突处理状态
                    intermediate_statuses = [
                        ImportSessionStatus.SELECT,
                        ImportSessionStatus.UPLOAD,
                        ImportSessionStatus.ANALYSIS_START,
                        ImportSessionStatus.ANALYSIS_IN_PROGRESS,
                        ImportSessionStatus.ANALYSIS_COMPLETE,
                        # ImportSessionStatus.CONFLICT_RESOLUTION, # REMOVE OLD
                        ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
                        ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,
                        ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
                        ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED,
                        ImportSessionStatus.IMPORT_QUEUED,
                        ImportSessionStatus.IMPORT_START,
                        ImportSessionStatus.IMPORT_IN_PROGRESS,
                    ]
                    # END CHANGE
                    if (
                        session_in_tx.status in intermediate_statuses
                        and session_in_tx.expires_at
                        and session_in_tx.expires_at < now
                    ):
                        session_in_tx.status = ImportSessionStatus.ERROR
                        exp_minutes = getattr(
                            settings, "EXCEL_IMPORT_SESSION_EXPIRATION_MINUTES", 30
                        )
                        err_msg = f"会话因通用超时 ({exp_minutes}分钟) 自动终止 (原状态: {current_db_status})。"
                        session_in_tx.error_message = err_msg + (
                            f" 原有信息: {session_in_tx.error_message[:200]}"
                            if session_in_tx.error_message
                            else ""
                        )
                        session_in_tx.updated_at = now
                        log_op_type = "system_timeout_to_error"
                        log_details_message = (
                            f"通用会话过期 (原状态: {current_db_status})，转为ERROR。"
                        )
                        status_changed_in_tx = True
                        logger.info(
                            f"[EnsureStatusUpdate] 会话 {session_in_tx.session_id} ({current_db_status}) 因通用过期转为 ERROR。"
                        )

                # 3. 结果/错误展示期过期 -> FINALIZED (仅当状态未被前序规则改变时)
                if not status_changed_in_tx:
                    if session_in_tx.status in [
                        ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
                        ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
                    ]:
                        if (
                            session_in_tx.results_display_expires_at
                            and session_in_tx.results_display_expires_at < now
                        ):
                            session_in_tx.status = ImportSessionStatus.FINALIZED
                            session_in_tx.results_display_expires_at = None
                            err_msg = f"结果展示期已过，系统自动最终处理 (原状态: {current_db_status})。"
                            session_in_tx.error_message = err_msg + (
                                f" 原有信息: {session_in_tx.error_message[:200]}"
                                if session_in_tx.error_message
                                else ""
                            )
                            log_op_type = "system_finalize_completed_session"
                            log_details_message = f"结果展示期已过 (原状态: {current_db_status})，转为FINALIZED。"
                            status_changed_in_tx = True
                            logger.info(
                                f"[EnsureStatusUpdate] 会话 {session_in_tx.session_id} ({current_db_status}) 因结果展示期过期转为 FINALIZED。"
                            )
                    elif (
                        session_in_tx.status == ImportSessionStatus.ERROR
                        and session_in_tx.import_log is None
                    ):
                        error_display_minutes = getattr(
                            settings, "ERROR_SESSION_DISPLAY_MINUTES", 15
                        )
                        if session_in_tx.updated_at and (
                            now
                            > (
                                session_in_tx.updated_at
                                + timedelta(minutes=error_display_minutes)
                            )
                        ):
                            session_in_tx.status = ImportSessionStatus.FINALIZED
                            err_msg = f"错误信息展示期已过，系统自动最终处理 (原状态: {current_db_status})。"
                            session_in_tx.error_message = err_msg + (
                                f" 原有信息: {session_in_tx.error_message[:200]}"
                                if session_in_tx.error_message
                                else ""
                            )
                            log_op_type = "system_finalize_error_session"
                            log_details_message = (
                                f"流程性ERROR的展示期已过，转为FINALIZED。"
                            )
                            status_changed_in_tx = True
                            logger.info(
                                f"[EnsureStatusUpdate] 会话 {session_in_tx.session_id} ({current_db_status}) 因错误信息展示期过期转为 FINALIZED。"
                            )

                if status_changed_in_tx:
                    session_in_tx.updated_at = now
                    update_fields = ["status", "error_message", "updated_at"]
                    if (
                        session_in_tx.results_display_expires_at is None
                        and current_db_status
                        in [
                            ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
                            ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
                        ]
                    ):
                        update_fields.append("results_display_expires_at")

                    session_in_tx.save(update_fields=list(set(update_fields)))

                    SessionOperation.objects.create(
                        session=session_in_tx,
                        operation_type=log_op_type,
                        user=None,
                        old_status=current_db_status,  # Log with the status read from DB inside transaction
                        new_status=session_in_tx.status,
                        details={"message": log_details_message},
                    )
                    logger.info(
                        f"[EnsureStatusUpdate] 会话 {session_in_tx.session_id} 状态已成功更新并保存: {current_db_status} -> {session_in_tx.status}"
                    )

                return session_in_tx  # 返回从数据库获取并可能已更新的实例

        except ImportSession.DoesNotExist:
            logger.warning(
                f"[EnsureStatusUpdate] 尝试更新状态时会话 {session.session_id if session else 'UnknownIDViaArgs'} 已不存在于数据库中。"
            )
            return None
        except Exception as e:
            logger.error(
                f"[EnsureStatusUpdate] 处理会话 {session.session_id if session else 'UnknownIDViaArgs'} 状态时发生严重错误: {e}",
                exc_info=True,
            )
            return None

    # CHANGE: [2025-05-28] 重构 get_system_active_session 以实现严格唯一活跃会话和即时状态更新
    def get_system_active_session(self) -> Optional[ImportSession]:
        """
        获取系统中当前唯一的"活跃"导入会话（如果有）。
        "活跃"包括核心处理流程中（未通用过期）或结果/错误展示期内。
        此方法会先确保候选会话的状态是当前逻辑时间点最新的。
        """
        now = timezone.now()  # now for this method's scope

        # CHANGE: [2025-06-01] 修正核心处理状态列表，只包含正在进行处理的状态，不包含已完成的结果展示状态
        core_processing_statuses = [
            ImportSessionStatus.UPLOAD,
            ImportSessionStatus.ANALYSIS_START,
            ImportSessionStatus.ANALYSIS_IN_PROGRESS,
            ImportSessionStatus.ANALYSIS_COMPLETE,
            ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
            ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,
            ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
            ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED,
            ImportSessionStatus.IMPORT_QUEUED,
            ImportSessionStatus.IMPORT_START,
            ImportSessionStatus.IMPORT_IN_PROGRESS,
            # 移除了 IMPORT_COMPLETED_SUCCESSFULLY, IMPORT_COMPLETED_WITH_ERRORS, ERROR
            # 这些是结果展示状态，不是核心处理状态
        ]

        # 查询时排除绝对的最终态 FINALIZED。CANCELLED 会被 _ensure_session_status_is_current 迅速转为 FINALIZED。
        # 按 updated_at 降序，确保我们总是先检查最近活动的，以确定唯一的活跃会话
        potential_sessions_qs = ImportSession.objects.select_related('import_log').exclude(
            status=ImportSessionStatus.FINALIZED
        ).order_by("-updated_at")

        for candidate_session_from_db in potential_sessions_qs:
            # 对每个候选会话，调用内部协调方法确保其状态是当前最新的
            session = self._ensure_session_status_is_current(candidate_session_from_db)

            if not session or session.status == ImportSessionStatus.FINALIZED:
                # 如果 _ensure_session_status_is_current 返回None (如DB删除或严重错误)
                # 或将会话状态更新为 FINALIZED，则此候选不再是活跃的，继续检查下一个
                continue

            # 检查1: 是否处于核心处理流程中 (且未被 _ensure_... 转为ERROR或FINALIZED)
            if session.status in core_processing_statuses:
                # _ensure_session_status_is_current 已经处理了通用过期并可能将其转为ERROR
                # 所以，如果状态仍是 core_processing_status，说明它未通用过期
                logger.info(
                    f"[GetSystemActiveSession] Found active session (in core processing): {session.session_id}, Status: {session.status}"
                )
                return session  # 这是最高优先级的活跃会话

            # 检查2: 是否处于结果/错误展示期内
            if session.status in [
                ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
                ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
            ]:
                # _ensure_session_status_is_current 已处理展示期过期并转为FINALIZED
                # 所以，如果状态仍是 IMPORT_COMPLETED_...，说明它仍在展示期内
                if (
                    session.results_display_expires_at
                    and session.results_display_expires_at > now
                ):  # 双重确认，尽管ensure已处理
                    logger.info(
                        f"[GetSystemActiveSession] Found active session (completed, in display period): {session.session_id}, Status: {session.status}"
                    )
                    return session
            elif (
                session.status == ImportSessionStatus.ERROR
                and session.import_log is None
            ):  # 流程性错误
                # _ensure_session_status_is_current 已处理展示期过期并转为FINALIZED
                # 所以，如果状态仍是 ERROR，说明它仍在展示期内
                error_display_duration_minutes = getattr(
                    settings, "ERROR_SESSION_DISPLAY_MINUTES", 15
                )
                if session.updated_at and (
                    now
                    < (
                        session.updated_at
                        + timedelta(minutes=error_display_duration_minutes)
                    )
                ):  # 双重确认
                    logger.info(
                        f"[GetSystemActiveSession] Found active session (ERROR, in display period): {session.session_id}, Status: {session.status}"
                    )
                    return session

            # 如果一个会话经过 _ensure_session_status_is_current 处理后，
            # 状态既不是 core_processing 也不是在有效展示期的结果/错误状态，
            # 那么它不应被视为当前"系统唯一活跃会话"。
            # （理论上，如果 _ensure... 将其变为非FINALIZED的展示期状态，上面的if/elif会捕获它）
            logger.debug(
                f"[GetSystemActiveSession] Session {session.session_id} (status {session.status}) not meeting active criteria after ensure. Checking next candidate or finishing."
            )

        logger.info("[GetSystemActiveSession] No system active import session found.")
        return None

    def get_active_session_for_user(self, user: User) -> Optional[ImportSession]:
        """
        获取指定用户当前最新的相关活动导入会话。
        在严格的"系统唯一活跃会话"模型下，此方法首先获取系统唯一活跃会话，然后检查该会话是否与用户相关。
        """
        # CHANGE: [2025-05-28] 简化逻辑，依赖 get_system_active_session
        active_system_session = self.get_system_active_session()
        if active_system_session and (
            active_system_session.created_by == user
            or active_system_session.processing_user == user
        ):
            logger.info(
                f"[GetActiveSessionForUser] User {user.username} is associated with the current system active session: {active_system_session.session_id}"
            )
            return active_system_session

        logger.info(
            f"[GetActiveSessionForUser] User {user.username} is not associated with any current system active session, or no system active session exists."
        )
        return None

    # CHANGE: [2024-07-26] 重构create_session方法，使用ImportSession模型
    # CHANGE: [YYYY-MM-DD] 修改create_session以检查系统级别活动会话 #任务1.4
    def create_session(
        self, uploaded_file, user_id: int, sheet_name: Union[str, int] = 0
    ) -> Dict[str, Any]:  # Return type to Dict
        """
        创建一个新的导入会话。
        在创建前，会检查系统中（全局）是否已有活动的会话。

        Args:
            uploaded_file: 上传的Excel文件
            user_id: 用户ID (用于记录创建者)
            sheet_name: 工作表名称或索引

        Returns:
            Dict: 包含会话实例或错误信息的字典。
                  成功: {"success": True, "session": ImportSession实例}
                  失败: {"success": False, "error": "错误信息", "error_type": "...", "existing_session_id": "session_id_if_exists"}
        """
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)  # 获取当前请求的用户实例
        except User.DoesNotExist:
            logger.error(f"[CreateSession] 尝试为不存在的用户ID {user_id} 创建会话。")
            return {
                "success": False,
                "error": "用户不存在。",
                "error_type": "user_not_found",
            }

        # CHANGE: [2025-05-28] 使用 get_system_active_session 进行严格的前置检查
        existing_system_active_session = self.get_system_active_session()
        if existing_system_active_session:
            # 任何形式的活跃会话（处理中或展示中）都应阻止新会话创建，以维护严格唯一性
            creator_username = (
                existing_system_active_session.created_by.username
                if existing_system_active_session.created_by
                else "未知用户"
            )
            processing_username = (
                existing_system_active_session.processing_user.username
                if existing_system_active_session.processing_user
                else "无"
            )
            error_msg = (
                f"系统当前已有一个正在进行的导入会话 (ID: {existing_system_active_session.session_id}, "
                f"文件: {existing_system_active_session.file_name or 'N/A'}, "
                f"状态: {existing_system_active_session.get_status_display()}). "
                f"请等待该会话完成后再尝试。"
            )
            logger.warning(
                f"[CreateSession] 用户 {user.username} 尝试创建新会话，但已存在系统级活跃会话 {existing_system_active_session.session_id}。"
            )
            return {
                "success": False, 
                "error": error_msg, 
                "error_type": "system_has_active_session", 
                "existing_session_id": str(existing_system_active_session.session_id),
                "existing_session_status": existing_system_active_session.status,
            }

        # 文件保存操作（通常应在事务外，因为它可能耗时）
        temp_file_path = None
        try:
            temp_dir = os.path.join(settings.MEDIA_ROOT, "temp_import")
            os.makedirs(temp_dir, exist_ok=True)
            file_ext = os.path.splitext(uploaded_file.name)[1].lower()
            if not file_ext or file_ext not in (".xls", ".xlsx"):
                file_ext = ".xlsx"
            
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=file_ext, dir=temp_dir
            ) as temp_file:
                temp_file_path = temp_file.name
                for chunk in uploaded_file.chunks():
                    temp_file.write(chunk)
            logger.info(f"[CreateSession] 临时文件已保存至: {temp_file_path}")

        except Exception as e_file:
            logger.error(f"[CreateSession] 保存上传文件时出错: {e_file}", exc_info=True)
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass  # Best effort cleanup
            return {
                "success": False,
                "error": "保存上传文件失败。",
                "error_type": "file_save_error",
            }
        
        # 创建ImportSession记录 (在事务中)
        try:
            with transaction.atomic():
                now = timezone.now()
                expires_at = now + timedelta(minutes=SESSION_EXPIRATION_MINUTES)
                session_id_val = (
                    uuid.uuid4()
                )  # Renamed from session_id to avoid conflict with arg

                import_session = ImportSession.objects.create(
                    session_id=session_id_val,
                    status=ImportSessionStatus.UPLOAD,
                    file_path=temp_file_path,
                    file_name=uploaded_file.name,
                    created_by=user,
                    created_at=now,
                    expires_at=expires_at,
                )

                SessionOperation.objects.create(
                    session=import_session,
                    operation_type="create_session",
                    user=user,
                    old_status=None,
                    new_status=ImportSessionStatus.UPLOAD,
                    details={
                        "original_filename": uploaded_file.name,
                        "sheet_name": str(sheet_name),
                    },
                )
                logger.info(
                    f"[CreateSession] 已为用户 {user.username} 创建新的导入会话: {import_session.session_id}"
                )
                return {
                    "success": True,
                    "session": import_session,
                }  # Return the session instance directly
        except Exception as e_db:
            logger.error(
                f"[CreateSession] 创建会话数据库记录时出错: {e_db}", exc_info=True
            )
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass  # Best effort cleanup
            return {
                "success": False,
                "error": "创建会话记录失败。",
                "error_type": "db_create_error",
            }

    def analyze_session(self, session_id: str) -> Tuple[List[Dict], Dict[str, int]]:
        """
        分析导入会话中的Excel文件，识别冲突并将详情持久化到数据库。
        """
        db_session: Optional[ImportSession] = None
        try:
            logger.info(f"[Mgr.Analyze] 开始分析会话: {session_id}")
            try:
                db_session = ImportSession.objects.get(session_id=session_id)
            except ImportSession.DoesNotExist:
                logger.error(f"[Mgr.Analyze] 导入会话不存在: {session_id}")
                raise ValueError(f"导入会话不存在: {session_id}")
            if not db_session:
                raise ValueError("db_session is None after initial checks") 
            if not db_session.is_active():
                # CHANGE: [2025-06-02] 即使会话过期，如果状态是UPLOAD，也允许尝试分析，因为文件已在
                if db_session.status != ImportSessionStatus.UPLOAD:
                    logger.warning(f"[Mgr.Analyze] 导入会话 {session_id} 非活跃 (状态: {db_session.status}) 且不是UPLOAD状态，无法分析。")
                    raise ValueError(f"导入会话已过期或非活跃: {session_id}")
                else:
                    logger.info(f"[Mgr.Analyze] 导入会话 {session_id} 非活跃但状态为UPLOAD，继续分析。")
            
            if db_session.status != ImportSessionStatus.UPLOAD:
                 # 如果不是因为过期而非活跃，但状态也不是UPLOAD，则不允许分析
                if not (db_session.expires_at and timezone.now() > db_session.expires_at):
                    raise ValueError(
                        f"会话状态不正确 ({db_session.status})，无法开始分析。期望状态: {ImportSessionStatus.UPLOAD}"
                    )

            logger.info(
                f"[Mgr.Analyze] 开始读取Excel文件，路径: '{db_session.file_path}'"
            )
            sheet_name_param_for_pandas: Union[str, int, None] = 0
            try:
                create_op = (
                    SessionOperation.objects.filter(
                        session=db_session, operation_type="create_session"
                    )
                    .order_by("-timestamp")
                    .first()
                )
                if create_op and create_op.details and "sheet_name" in create_op.details:
                    raw_sheet_name = create_op.details["sheet_name"]
                    if isinstance(raw_sheet_name, int):
                        sheet_name_param_for_pandas = raw_sheet_name
                    elif isinstance(raw_sheet_name, str):
                        if raw_sheet_name.isdigit():
                            try:
                                sheet_name_param_for_pandas = int(raw_sheet_name)
                            except ValueError:
                                sheet_name_param_for_pandas = raw_sheet_name
                        else:
                            sheet_name_param_for_pandas = raw_sheet_name
                    logger.info(
                        f"[Mgr.Analyze] 从SessionOperation获取到 sheet_name: '{sheet_name_param_for_pandas}' (类型: {type(sheet_name_param_for_pandas)})"
                    )
                else:
                    logger.info(
                        f"[Mgr.Analyze] 未在SessionOperation中找到sheet_name，将尝试读取第一个工作表 (索引 0)。"
                    )
                    sheet_name_param_for_pandas = 0 
            except Exception as e_sheet_name:
                logger.error(
                    f"[Mgr.Analyze] 从 SessionOperation 获取 sheet_name 时发生错误: {e_sheet_name}, 使用默认值 0 (index)",
                    exc_info=True,
                )
                sheet_name_param_for_pandas = 0

            if not db_session.file_path or not os.path.exists(db_session.file_path):
                logger.error(
                    f"[Mgr.Analyze] Excel文件路径 '{db_session.file_path}' 无效或文件不存在."
                )
                # 更新会话状态为错误
                db_session.status = ImportSessionStatus.ERROR
                db_session.error_message = f"Excel文件路径无效或文件不存在: {db_session.file_path}"
                db_session.save(update_fields=['status', 'error_message', 'updated_at'])
                raise ValueError(f"指定的Excel文件路径无效或文件不存在: {db_session.file_path}")

            logger.info(
                f"[Mgr.Analyze] 准备使用pandas读取Excel。文件: '{db_session.file_path}', 原始工作表参数: '{sheet_name_param_for_pandas}'"
            )
            
            actual_sheet_to_read: Union[str, int, None] = None
            try:
                excel_file_obj = pd.ExcelFile(db_session.file_path)
                sheet_names_in_file = excel_file_obj.sheet_names
                logger.info(
                    f"[Mgr.Analyze] 文件中的实际工作表名称列表: {sheet_names_in_file}"
                )
                if not sheet_names_in_file:
                    raise ValueError("Excel文件中没有找到任何工作表。")
                if isinstance(sheet_name_param_for_pandas, int):
                    if 0 <= sheet_name_param_for_pandas < len(sheet_names_in_file):
                        actual_sheet_to_read = sheet_names_in_file[sheet_name_param_for_pandas]
                    else:
                        actual_sheet_to_read = sheet_names_in_file[0]
                elif isinstance(sheet_name_param_for_pandas, str):
                    actual_sheet_to_read = sheet_name_param_for_pandas if sheet_name_param_for_pandas in sheet_names_in_file else sheet_names_in_file[0]
                # 注释：移除了不可达的else分支，因为sheet_name_param_for_pandas根据初始化逻辑只能是int或str类型
            except Exception as e_excel_file_check:
                logger.error(
                    f"[Mgr.Analyze] 检查Excel文件工作表时发生错误: {e_excel_file_check}",
                    exc_info=True,
                )
                # CHANGE: [2025-06-03] 修复系统错误处理：文件读取失败是真正的系统错误，
                # 应该抛出异常让上层将会话设置为ERROR状态，而不是创建failed状态的ImportLog
                raise Exception(f"读取Excel文件失败（系统错误）: {e_excel_file_check}") from e_excel_file_check

            df_original = pd.read_excel(db_session.file_path, sheet_name=actual_sheet_to_read, header=None)
            header_offset = 0
            for idx, row in df_original.iterrows():
                if any(key_col_name in row.values for key_col_name in self.import_service.DEFAULT_FIELD_MAPPING.keys()):
                    header_offset = idx
                    break
            df = self.import_service._process_excel_data(df_original)
            if db_session.record_count != len(df):
                db_session.record_count = len(df)
                db_session.save(update_fields=["record_count", "updated_at"])

            # 调用 conflict_analyzer.analyze_dataframe，它内部会将状态设置为 ANALYSIS_COMPLETE
            # analyze_dataframe 内部不应再有其他状态转换的逻辑
            conflict_details_list, stats = self.conflict_analyzer.analyze_dataframe(
                df,
                self.import_service.field_mapping,
                import_session=db_session, 
                header_offset=header_offset,
                progress_update_interval=((db_session.record_count // 20) if db_session.record_count > 20 else 1),
            )
            logger.info(f"[Mgr.Analyze] conflict_analyzer.analyze_dataframe 执行完成 for session {session_id}, 统计: {stats}")

            # FIX: [2025-06-19] 修复分析进度卡在95%的问题并添加事务处理
            # 使用原子事务确保状态更新的完整性和一致性。
            try:
                with transaction.atomic():
                    # 锁定会话以防止并发修改
                    session_to_update = ImportSession.objects.select_for_update().get(pk=db_session.pk)
                    
                    logger.info(f"[Mgr.Analyze] 会话 {session_id} 分析完成，准备在事务中更新状态为 ANALYSIS_COMPLETE。")
                    session_to_update.status = ImportSessionStatus.ANALYSIS_COMPLETE
                    session_to_update.progress = 100.0
                    session_to_update.conflict_count = stats.get('update', 0) + stats.get('new', 0)
                    session_to_update.record_count = stats.get('total', 0)
                    session_to_update.analysis_stats = {
                        'total': stats.get('total', 0),
                        'new': stats.get('new', 0),
                        'identical': stats.get('identical', 0),
                        'update': stats.get('update', 0),
                        'error': stats.get('error', 0)
                    }
                    session_to_update.save(update_fields=['status', 'progress', 'conflict_count', 'record_count', 'analysis_stats', 'updated_at'])
                    logger.info(f"[Mgr.Analyze] 会话 {session_id} 状态已成功更新为 ANALYSIS_COMPLETE。")

                    # 更新内存中的db_session对象以反映更改
                    db_session = session_to_update

                    # 紧接着执行状态自动转换逻辑
                    if session_to_update.status == ImportSessionStatus.ANALYSIS_COMPLETE:
                        old_status_for_log = session_to_update.status
                        session_to_update.status = ImportSessionStatus.CONFLICT_RESOLUTION_STARTED
                        session_to_update.processing_user = None
                        session_to_update.updated_at = timezone.now()
                        session_to_update.save(update_fields=['status', 'processing_user', 'updated_at'])
                        
                        SessionOperation.objects.create(
                            session=session_to_update,
                            operation_type='system_auto_status_update',
                            user=None,
                            old_status=old_status_for_log,
                            new_status=session_to_update.status,
                            details={"message": "分析完成，系统自动进入冲突处理开始阶段。"}
                        )
                        logger.info(f"[Mgr.Analyze] 会话 {session_to_update.session_id} 状态已在同一事务中自动转换为 CONFLICT_RESOLUTION_STARTED.")
                        db_session.status = session_to_update.status # 再次更新内存对象状态
                    else:
                        logger.warning(f"[Mgr.Analyze] 尝试自动转换会话 {session_to_update.session_id} 状态时，其状态不是 ANALYSIS_COMPLETE (当前: {session_to_update.status})。不执行转换。")

            except ImportSession.DoesNotExist:
                 logger.error(f"[Mgr.Analyze] 尝试在事务中更新会话状态时，会话 {db_session.session_id} 未找到。")
            except Exception as e_atomic_update:
                logger.error(f"[Mgr.Analyze] 在原子事务中更新会话 {db_session.session_id} 状态时发生错误: {e_atomic_update}", exc_info=True)
                # 发生错误时，将状态设置为ERROR以避免流程卡住
                db_session.status = ImportSessionStatus.ERROR
                db_session.error_message = f"在完成分析时发生内部错误: {e_atomic_update}"
                db_session.save(update_fields=['status', 'error_message', 'updated_at'])

            return conflict_details_list, stats # 返回从 analyze_dataframe 获取的冲突列表和统计数据

        except ValueError as ve:
            logger.error(f"[Mgr.Analyze] analyze_session 中发生 ValueError: {str(ve)}", exc_info=True)
            if db_session and db_session.status not in CONCLUSIVE_SESSION_STATUSES:
                # ... (error handling for ValueError remains the same)
                old_status_for_error_log = db_session.status
                db_session.status = ImportSessionStatus.ERROR
                error_msg_for_db = f"分析Excel时发生错误: {str(ve)}"
                db_session.error_message = error_msg_for_db[:1000] 
                db_session.save(update_fields=["status", "error_message", "updated_at"])
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type="error_in_analysis_manager",
                    user=db_session.created_by,
                    old_status=old_status_for_error_log,
                    new_status=ImportSessionStatus.ERROR,
                    details={"error_message": error_msg_for_db[:500], "stage": "analyze_session_value_error"},
                )
            raise ve
        except Exception as e:
            logger.error(f"[Mgr.Analyze] analyze_session 核心逻辑发生一般异常: {str(e)}", exc_info=True)
            if db_session and db_session.status not in CONCLUSIVE_SESSION_STATUSES:
                # ... (error handling for general Exception remains the same)
                old_status_for_error_log = db_session.status
                db_session.status = ImportSessionStatus.ERROR
                error_msg_for_db = f"分析过程中发生未知系统错误: {str(e)}"
                db_session.error_message = error_msg_for_db[:1000]
                db_session.save(update_fields=["status", "error_message", "updated_at"])
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type="error_in_analysis_manager",
                    user=db_session.created_by,
                    old_status=old_status_for_error_log,
                    new_status=ImportSessionStatus.ERROR,
                    details={"error_message": error_msg_for_db[:500], "stage": "analyze_session_general_error"},
                )
            raise e

    def confirm_import(
        self,
        session_id: str,
        resolutions: List[Dict], 
        user: User,
        analysis_stats_dict: Optional[Dict] = None, 
        user_decision_stats_dict: Optional[Dict] = None,
    ) -> Dict: 
        logger.info(
            f"[Mgr.Confirm.Entry] confirm_import called for session {session_id} by user {user.username}. "
            f"Analysis stats: {analysis_stats_dict is not None}, User decision stats: {user_decision_stats_dict is not None}"
        )
        db_session = None
        try:
            with transaction.atomic(): 
                try:
                    db_session = ImportSession.objects.select_for_update().get(session_id=session_id)
                except ImportSession.DoesNotExist:
                    logger.error(f"[Mgr.Confirm] Import session {session_id} not found.")
                    return {
                        "success": False,
                        "error": f"导入会话不存在: {session_id}",
                        "error_type": "session_not_found",
                    }

                if not db_session.is_active():
                     logger.warning(f"[Mgr.Confirm] Session {session_id} is inactive (status: {db_session.status}). Cannot confirm import.")
                     return {"success": False, "error": "会话已非活跃，无法确认导入。", "error_type": "session_inactive"}

                # CHANGE: [2025-06-01] 严格要求确认导入的前置状态必须是 CONFLICT_RESOLUTION_IN_PROGRESS
                if db_session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
                    logger.error(
                        f"[Mgr.Confirm] Session {session_id} status is {db_session.status}, expected {ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS}."
                    )
                    return {
                        "success": False,
                        "error": f"导入会话状态错误({db_session.get_status_display()})，期望状态为'冲突处理中'。请先开始处理冲突。",
                        "error_type": "invalid_session_state_for_confirm",
                    }
                
                # 验证提交用户是否为当前处理用户
                if db_session.processing_user != user:
                    logger.warning(f"[Mgr.Confirm] User {user.username} attempted to confirm session {session_id} which is being processed by {db_session.processing_user.username if db_session.processing_user else 'None'}.")
                    return {
                        "success": False,
                        "error": f"您不是此会话的当前处理用户 ('{db_session.processing_user.username if db_session.processing_user else '未知'}')，无法提交决策。",
                        "error_type": "confirm_by_non_processing_user",
                    }
                # END CHANGE

                valid_actions = [ConflictResolution.UPDATE, ConflictResolution.SKIP, ConflictResolution.CREATE]
                for res_item in resolutions:
                    action = res_item.get("action")
                    if action and action not in valid_actions:
                        error_msg = f"无效的处理动作: {action}，有效值为: {', '.join(valid_actions)}"
                        logger.warning(f"[Mgr.Confirm] {error_msg} for session {session_id}")
                        return {"success": False, "error": error_msg, "error_type": "invalid_resolution_action"}

                old_status_for_op_log_comp = db_session.status
                db_session.status = ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED
                db_session.processing_user = None  
                db_session.updated_at = timezone.now()
                db_session.save(update_fields=["status", "processing_user", "updated_at"])
                
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type='status_change', 
                    user=user,
                    old_status=old_status_for_op_log_comp,
                    new_status=db_session.status,
                    details={
                        "resolutions_count": len(resolutions),
                        "message": "用户提交冲突解决方案，冲突处理完成。",
                        "final_submitting_user_id": user.id
                    },
                )
                logger.info(f"[Mgr.Confirm] Session {session_id} status updated to CONFLICT_RESOLUTION_COMPLETED by user {user.username}.")

                old_status_for_op_log_queue = db_session.status
                db_session.status = ImportSessionStatus.IMPORT_QUEUED
                db_session.updated_at = timezone.now()
                db_session.save(update_fields=["status", "updated_at"])
                
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type="import_queued_async",
                    user=user, 
                    old_status=old_status_for_op_log_queue,
                    new_status=db_session.status,
                    details={"message": "冲突处理完成后，任务自动进入后台处理队列。"},
                )
                logger.info(f"[Mgr.Confirm] Session {session_id} status updated to IMPORT_QUEUED, preparing for Celery task.")

            # 事务提交后，再准备和分发Celery任务 (事务外)
            try:
                resolutions_json = json.dumps(resolutions)
                analysis_stats_json_str = json.dumps(analysis_stats_dict) if analysis_stats_dict else None
                user_decision_stats_json_str = json.dumps(user_decision_stats_dict) if user_decision_stats_dict else None
            except TypeError as te:
                logger.error(f"[Mgr.Confirm] Serialization error for Celery task params, session {session_id}: {te}", exc_info=True)
                # 尝试将会话标记为错误（事务外，尽力而为）
                try:
                    db_session_err = ImportSession.objects.get(session_id=session_id) # Re-fetch
                    if db_session_err.status not in CONCLUSIVE_SESSION_STATUSES:
                        db_session_err.status = ImportSessionStatus.ERROR
                        db_session_err.error_message = "无法处理导入决策数据（序列化失败）。"
                        db_session_err.save(update_fields=["status", "error_message", "updated_at"])
                except Exception as e_final_err_log:
                    logger.error(f"[Mgr.Confirm] Failed to mark session {session_id} as ERROR after serialization failure: {e_final_err_log}")
                return {"success": False, "error": "无法处理导入决策数据。", "error_type": "resolution_serialization_error"}

            from ..tasks import process_excel_import_confirmation_task # 延迟导入
            task_result = process_excel_import_confirmation_task.delay(
                session_id=str(db_session.session_id),
                resolutions_json=resolutions_json,
                user_id=user.id, # 传递的是触发 confirmImport 的用户ID
                analysis_stats_json=analysis_stats_json_str, 
                user_decision_stats_json=user_decision_stats_json_str,
            )
            
            logger.info(f"[Mgr.Confirm] Celery task process_excel_import_confirmation_task submitted for session {session_id}. Task ID: {task_result.id}")
            return {
                "success": True,
                "data": {
                    "message": "导入任务已成功提交到后台处理队列。",
                    "session_id": str(db_session.session_id),
                    "task_id": task_result.id,
                    "current_status": db_session.status, # Should be IMPORT_QUEUED
                },
            }

        except ValueError as ve: 
            logger.warning(f"[Mgr.Confirm] Validation or state error for session {session_id}: {str(ve)}", exc_info=False) # Don't need full stack for simple ValueErrors
            if db_session and db_session.status not in CONCLUSIVE_SESSION_STATUSES:
                try:
                    with transaction.atomic(): # Ensure this save is atomic
                        session_err_update = ImportSession.objects.select_for_update().get(pk=db_session.pk)
                        if session_err_update.status not in CONCLUSIVE_SESSION_STATUSES:
                            old_status_val = session_err_update.status
                            session_err_update.status = ImportSessionStatus.ERROR
                            session_err_update.error_message = session_err_update.error_message or str(ve)
                            session_err_update.save(update_fields=["status", "error_message", "updated_at"])
                            SessionOperation.objects.create(
                                session=session_err_update,
                                operation_type="error_in_confirm_trigger",
                                user=user,
                                old_status=old_status_val,
                                new_status=session_err_update.status,
                                details={"error": str(ve)},
                            )
                except Exception as e_save_err:
                     logger.error(f"[Mgr.Confirm] Failed to save ERROR state for session {session_id} after ValueError: {e_save_err}")
            return {"success": False, "error": str(ve), "error_type": "validation_or_state_error"}
        except Exception as e:
            logger.error(f"[Mgr.Confirm] Unexpected error during confirm_import for session {session_id}: {str(e)}", exc_info=True)
            if db_session and db_session.status not in CONCLUSIVE_SESSION_STATUSES:
                try:
                    with transaction.atomic():
                        session_err_update = ImportSession.objects.select_for_update().get(pk=db_session.pk)
                        if session_err_update.status not in CONCLUSIVE_SESSION_STATUSES:
                            old_status_val = session_err_update.status
                            session_err_update.status = ImportSessionStatus.ERROR
                            session_err_update.error_message = session_err_update.error_message or f"触发导入任务时发生意外服务器错误: {str(e)[:200]}"
                            session_err_update.save(update_fields=["status", "error_message", "updated_at"])
                            SessionOperation.objects.create(
                                session=session_err_update,
                                operation_type="error_in_confirm_trigger", # More generic error type
                                user=user,
                                old_status=old_status_val,
                                new_status=session_err_update.status,
                                details={"error": str(e)[:500]},
                            )
                except Exception as e_save_err:
                     logger.error(f"[Mgr.Confirm] Failed to save ERROR state for session {session_id} after general exception: {e_save_err}")
            return {"success": False, "error": "触发导入任务时发生意外服务器错误。", "error_type": "task_dispatch_error"}

    def extend_session(self, session_id: str) -> bool:
        """
        延长会话过期时间
        """
        try:
            # CHANGE: [2025-05-29] Fix AttributeError by using direct ORM query
            # db_session = self.get_session(session_id)  # 使用已重构的get_session
            try:
                db_session = ImportSession.objects.get(session_id=session_id)
            except ImportSession.DoesNotExist:
                logger.warning(
                    f"[Mgr.Extend] 尝试延长会话时，会话不存在: {session_id}"
                )
                return False
            # END CHANGE

            if not db_session or not db_session.is_active():  # 确保会话存在且是活跃的
                logger.warning(
                    f"[Mgr.Extend] 尝试延长不存在或非活跃的会话: {session_id}"
                )
                return False

            new_expires_at = timezone.now() + timedelta(
                minutes=SESSION_EXPIRATION_MINUTES
            )
            db_session.expires_at = new_expires_at
            db_session.save(update_fields=["expires_at", "updated_at"])

            logger.info(
                f"[Mgr.Extend] 会话 {session_id} 过期时间已延长至: {new_expires_at.isoformat()}"
            )
            return True
        except Exception as e:
            logger.error(
                f"[Mgr.Extend] 延长会话 {session_id} 过期时间时出错: {str(e)}",
                exc_info=True,
            )
            return False

    # CHANGE: [2025-05-28] 移除旧的_cleanup_session_resources，该逻辑已整合入 _ensure_session_status_is_current 和 process_finalized_sessions_task
    # def _cleanup_session_resources(self, session: ImportSession, reason: str = "未知原因") -> None:
    def _cleanup_session_resources(
        self,
        session: ImportSession,
        cancelled_by: Optional[User] = None,
        reason: Optional[str] = "会话资源清理",
    ) -> None:
        """
        处理会话取消：将状态设置为CANCELLED，然后迅速转为FINALIZED。
        实际的物理资源清理将由定期的 `process_finalized_sessions_task` 处理。
        此方法主要负责状态流转和记录操作。

        Args:
            session: 要取消的 ImportSession 对象 (应已通过 _ensure_session_status_is_current 更新了状态)。
            cancelled_by: 操作的用户。
            reason: 取消的原因。
        """
        if not session:
            logger.warning(
                f"[_cleanup_session_resources] 传入的会话对象为 None，无法处理取消。"
            )
            return

        session_id = session.session_id  # For logging
        logger.info(
            f"[_cleanup_session_resources] 开始处理会话 {session_id} 的取消请求。原因: {reason}"
        )

        try:
            with transaction.atomic():
                # 重新从数据库获取并锁定行，确保我们操作的是最新版本并防止并发问题
                session_in_tx = ImportSession.objects.select_for_update().get(
                    pk=session.pk
                )

                original_status_for_log = session_in_tx.status

                if session_in_tx.status == ImportSessionStatus.FINALIZED:
                    logger.info(
                        f"[_cleanup_session_resources] 会话 {session_id} 已处于 FINALIZED 状态，取消操作终止。"
                    )
                    return

                if session_in_tx.status == ImportSessionStatus.CANCELLED:
                    # 如果已经是CANCELLED，理论上应该立即转FINALIZED，但为防意外情况，这里也处理下
                    logger.info(
                        f"[_cleanup_session_resources] 会话 {session_id} 已是 CANCELLED 状态，将尝试直接转 FINALIZED。"
                    )
                else:
                    # 步骤1: 设置为 CANCELLED (短暂过渡状态)
                    session_in_tx.status = ImportSessionStatus.CANCELLED
                    session_in_tx.error_message = reason or "用户手动取消"
                    session_in_tx.processing_user = None
                    session_in_tx.cancelled_at = timezone.now()
                    session_in_tx.updated_at = timezone.now()
                    session_in_tx.save(
                        update_fields=[
                            "status",
                            "error_message",
                            "processing_user",
                            "cancelled_at",
                            "updated_at",
                        ]
                    )

                    SessionOperation.objects.create(
                        session=session_in_tx,
                        operation_type="cancel_session",
                        user=cancelled_by,
                        old_status=original_status_for_log,
                        new_status=ImportSessionStatus.CANCELLED,
                        details={"reason": reason or "用户取消了导入会话"},
                    )
                    logger.info(
                        f"[_cleanup_session_resources] 会话 {session_id} 已被标记为 CANCELLED。原因: {reason}"
                    )

                # 步骤2: 立即将会话状态从 CANCELLED (或任何允许取消的前置状态) 更新为 FINALIZED
                current_status_before_finalize = session_in_tx.status
                session_in_tx.status = ImportSessionStatus.FINALIZED
                session_in_tx.error_message = (
                    session_in_tx.error_message
                    or f"会话因取消 ({reason or '用户手动取消'}) 而最终处理。"
                )
                session_in_tx.updated_at = timezone.now()
                session_in_tx.save(
                    update_fields=["status", "error_message", "updated_at"]
                )

                SessionOperation.objects.create(
                    session=session_in_tx,
                    operation_type="system_finalize_cancelled_session",
                    user=None,
                    old_status=current_status_before_finalize,
                    new_status=ImportSessionStatus.FINALIZED,
                    details={
                        "message": f"会话因取消 (原因: {reason or '未指定'}) 而最终处理完毕，等待后台资源清理。"
                    },
                )
                logger.info(
                    f"[_cleanup_session_resources] 会话 {session_id} 已从 {current_status_before_finalize} 转为 FINALIZED。"
                )
        except ImportSession.DoesNotExist:
            logger.warning(
                f"[_cleanup_session_resources] 尝试取消/清理不存在的会话: {session_id}"
            )
        except Exception as e:
            logger.error(
                f"[_cleanup_session_resources] 处理会话 {session_id} 取消并标记为FINALIZED时发生错误: {str(e)}",
                exc_info=True,
            )

    def _execute_import_with_resolutions(
        self,
        db_session: ImportSession,
        user: User,
        conflict_records_data: List[Dict],
        resolution_map: Dict[str, str],
        analysis_phase_stats: Optional[Dict] = None, 
        user_decision_phase_stats: Optional[Dict] = None,
    ):
        """
        根据用户决策执行导入
        analysis_phase_stats: e.g., {'total_rows_read': N, 'failed_rows': N, 'skipped_identical': N, 'found_new_count':N, 'found_update_count':N}
        user_decision_phase_stats: e.g., {'skipped_update_count': S_u, 'confirmed_update_count': C_u}
        """
        logger.info(
            f"[Mgr.ExecuteImport] 开始执行导入，会话ID: {db_session.session_id}"
        )
        if analysis_phase_stats:
            logger.info(
                f"[Mgr.ExecuteImport] 接收到的分析阶段统计: {analysis_phase_stats}"
            )
        else:
            analysis_phase_stats = {} 
            logger.warning(
                f"[Mgr.ExecuteImport] 未接收到分析阶段统计数据 for session {db_session.session_id}。相关ImportLog分析字段将为0。"
            )
        
        if user_decision_phase_stats:
            logger.info(
                f"[Mgr.ExecuteImport] 接收到的用户决策统计: {user_decision_phase_stats}"
            )
        else:
            user_decision_phase_stats = {} 
            logger.warning(
                f"[Mgr.ExecuteImport] 未接收到用户决策统计数据 for session {db_session.session_id}。相关ImportLog字段将为0。"
            )

        # 文件和DataFrame准备 (与之前一致)
        sheet_name_to_use: Union[str, int, None] = 0
        try:
            create_op = (
                SessionOperation.objects.filter(
                    session=db_session, operation_type="create_session"
                )
                .order_by("-timestamp")
                .first()
            )
            if create_op and create_op.details and "sheet_name" in create_op.details:
                raw_sheet_name = create_op.details["sheet_name"]
                if isinstance(raw_sheet_name, int):
                    sheet_name_to_use = raw_sheet_name
                elif isinstance(raw_sheet_name, str):
                    if raw_sheet_name.isdigit():
                        try:
                            sheet_name_to_use = int(raw_sheet_name)
                        except ValueError:
                            sheet_name_to_use = raw_sheet_name
                    else:
                        sheet_name_to_use = raw_sheet_name
            else:
                sheet_name_to_use = 0
        except Exception as e_sheet_name:
            logger.error(
                f"[Mgr.ExecuteImport] 从 SessionOperation 获取 sheet_name 时发生错误: {e_sheet_name}, 使用默认值 0 (index)",
                exc_info=True,
            )
            sheet_name_to_use = 0

        actual_sheet_to_read: Union[str, int, None] = None
        df_original = None
        
        # 文件读取、数据处理和导入逻辑
        try:
            # 检查文件存在性
            if not db_session.file_path or not os.path.exists(db_session.file_path):
                raise FileNotFoundError(
                    f"Import file path not found or file does not exist: {db_session.file_path}"
                )
            
            # 分析Excel工作表
            excel_file_obj = pd.ExcelFile(db_session.file_path)
            sheet_names_in_file = excel_file_obj.sheet_names
            if not sheet_names_in_file:
                raise ValueError("Excel文件中没有找到任何工作表以执行导入。")
                
            # 确定要读取的工作表名称
            if isinstance(sheet_name_to_use, int):
                actual_sheet_to_read = (
                    sheet_names_in_file[sheet_name_to_use]
                    if 0 <= sheet_name_to_use < len(sheet_names_in_file)
                    else sheet_names_in_file[0]
                )
            elif isinstance(sheet_name_to_use, str):
                actual_sheet_to_read = (
                    sheet_name_to_use
                    if sheet_name_to_use in sheet_names_in_file
                    else sheet_names_in_file[0]
                )
            # 移除了不可达的else分支，因为sheet_name_to_use根据初始化逻辑只能是int或str类型

            # 读取Excel数据
            df_original = pd.read_excel(
                db_session.file_path, sheet_name=actual_sheet_to_read, header=None
            )
            
            # 数据处理逻辑（移到try块内部）
            header_offset = 0
            for idx, row in df_original.iterrows():
                if any(
                    key_col_name in row.values
                    for key_col_name in self.import_service.DEFAULT_FIELD_MAPPING.keys()
                ):
                    header_offset = idx
                    break
            df = self.import_service._process_excel_data(df_original)
            logger.info(
                f"[Mgr.ExecuteImport] 文件读取和表头处理完成. 记录数: {len(df)}, 表头偏移: {header_offset}"
            )
            
            # 冲突解决和数据分类逻辑
            update_records_cn = [] 
            skip_records_cn = []   
            unchanged_records_cn = [] 
            for record_data in conflict_records_data:
                commission_number = record_data.get("commission_number")
                conflict_type = record_data.get("conflict_type")
                action = resolution_map.get(commission_number)
                if not commission_number:
                    continue
                if conflict_type == "identical":
                    unchanged_records_cn.append(commission_number)
                    continue
                if action == ConflictResolution.SKIP:
                    skip_records_cn.append(commission_number)
                elif (
                    action == ConflictResolution.UPDATE
                    or action == ConflictResolution.CREATE
                ):
                    update_records_cn.append(commission_number)
                else: 
                    if conflict_type == "update":
                        update_records_cn.append(commission_number)

            # 创建用于导入的DataFrame：包含需要更新/创建的记录
            df_update_create = df[
                df.apply(
                    lambda row: self._get_commission_number(
                        row, self.import_service.field_mapping
                    )
                    in update_records_cn,
                    axis=1,
                )
            ].copy()

            # 注意：df_user_skip 和 df_identical_skip 在当前实现中不需要，
            # 因为统计信息通过 skip_records_cn 和 unchanged_records_cn 列表已经可以获取
            # 这里我们只处理需要导入的数据

            # 获取所有已处理过的委托编号集合
            processed_cns = set(update_records_cn + skip_records_cn + unchanged_records_cn)

            # 提取剩余的新记录（未在冲突解决中处理的记录）
            df_remaining_new = df[
                ~df.apply(
                    lambda row: self._get_commission_number(
                        row, self.import_service.field_mapping
                    )
                    in processed_cns,
                    axis=1,
                )
            ].copy()

            # 合并所有需要处理的数据：更新/创建的记录 + 新记录
            df_to_process = pd.concat(
                [df_update_create, df_remaining_new], ignore_index=True
            ).drop_duplicates()

            # 导入阶段初始化
            if db_session:
                db_session.record_count = len(df_to_process) 
                db_session.current_record = 0  # 初始化已处理记录数为0

                if len(df_to_process) == 0:
                    db_session.progress = 100.0
                
                update_fields_init = ["record_count", "current_record", "updated_at"]
                if len(df_to_process) == 0:
                    update_fields_init.extend(["progress"]) # 不再在此处修改status
                
                db_session.save(update_fields=list(set(update_fields_init))) # 使用set避免重复
                logger.info(
                    f"[Mgr.ExecuteImport] 会话 {db_session.session_id} 导入阶段初始化/检查。待处理记录: {db_session.record_count}, 当前进度: {db_session.progress}%, 状态: {db_session.status}"
                )

            import_log_result = None 
            if len(df_to_process) > 0: 
                # CHANGE: [日期] 不再写入临时Excel文件，直接调用 import_from_dataframe_object
                logger.info(f"[Mgr.ExecuteImport] 调用 import_service.import_from_dataframe_object 处理 {len(df_to_process)} 条记录。")
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type="import_processing_from_dataframe", # 新的操作类型
                    user=user,
                    details={"record_count_to_process": len(df_to_process)},
                )
                import_log_result = self.import_service.import_from_dataframe_object(
                    dataframe_to_import=df_to_process,
                    user=user,
                    duplicate_strategy=DuplicateStrategy.SMART_UPDATE.value, # 或者从会话/配置获取
                    import_session=db_session,
                    original_file_name=db_session.file_name, # 传递原始文件名等信息用于日志
                    original_file_size=os.path.getsize(db_session.file_path) if db_session.file_path and os.path.exists(db_session.file_path) else 0,
                    original_file_hash=self.import_service._calculate_file_hash(db_session.file_path) if db_session.file_path and os.path.exists(db_session.file_path) else "N/A_Original_File_Missing"
                )
                # END CHANGE
                
                # 检查导入服务是否返回有效的ImportLog
                if import_log_result is None:
                    logger.error(
                        "[Mgr.ExecuteImport] import_service.import_from_dataframe_object 未返回有效的ImportLog。"
                    )
                    # CHANGE: [2025-06-03] 修复系统错误处理：导入服务未返回ImportLog也是系统错误
                    raise Exception("导入服务未返回有效的ImportLog（系统错误）")
            else:
                # CHANGE: [2025-06-03] 处理空数据集的情况，创建一个NOOP ImportLog
                logger.info(f"[Mgr.ExecuteImport] 没有数据需要处理，创建NOOP ImportLog。")
                SessionOperation.objects.create(
                    session=db_session,
                    operation_type="import_processing_noop",
                    user=user,
                    details={"record_count_to_process": 0, "reason": "no_data_to_process"},
                )
                # 使用import_service创建NOOP日志（如果该方法支持），否则手动创建
                import_log_result = ImportLog.objects.create(
                    batch_number=f"NOOP-{db_session.session_id.hex[:8]}",
                    file_name=db_session.file_name or "No Data To Process",
                    import_user=user,
                    status="completed",  # 空数据集处理视为成功完成
                    error_log="",
                    # 基本统计字段会在后面设置
                )

        except Exception as e_excel_file_check:
            logger.error(
                f"[Mgr.ExecuteImport] 读取或检查Excel文件时发生错误: {e_excel_file_check}",
                exc_info=True,
            )
            # CHANGE: [2025-06-03] 修复系统错误处理：文件读取失败是真正的系统错误，
            # 应该抛出异常让上层将会话设置为ERROR状态，而不是创建failed状态的ImportLog
            raise Exception(f"读取Excel文件失败（系统错误）: {e_excel_file_check}") from e_excel_file_check

        # CHANGE: [2025-06-03] 健壮性检查：确保import_log_result存在才进行统计处理
        if import_log_result:
            is_noop_log = (
                "NOOP-" in import_log_result.batch_number
                or "EMPTYDF-" in import_log_result.batch_number
                or "INITFAIL-" in import_log_result.batch_number
            )

            task_created_count = getattr(
                import_log_result, "import_task_created_count", 0
            )
            task_updated_count = getattr(
                import_log_result, "import_task_updated_count", 0
            )
            task_unchanged_count = getattr(
                import_log_result, "import_task_unchanged_count", 0
            )
            task_failed_count = getattr(
                import_log_result, "import_task_failed_count", 0
            )
            task_processed_successfully_count = getattr(
                import_log_result, "import_task_processed_successfully_count", 0
            )
            task_total_records_submitted = getattr(
                import_log_result, "import_task_total_records_submitted", 0
            )

            count_analysis_total_rows_read = analysis_phase_stats.get(
                "total_rows_read", len(df) if df is not None else 0
            )
            count_analysis_failed_rows = analysis_phase_stats.get("failed_rows", 0)
            count_analysis_skipped_identical = analysis_phase_stats.get(
                "skipped_identical", 0
            )
            count_analysis_found_new = analysis_phase_stats.get("found_new_count", 0)
            count_analysis_found_update = analysis_phase_stats.get(
                "found_update_count", 0
            )
            count_analysis_successfully_parsed = (
                count_analysis_total_rows_read - count_analysis_failed_rows
            )

            count_user_decision_skipped_updates = user_decision_phase_stats.get(
                "skipped_update_count", 0
            )
            count_user_decision_confirmed_updates = user_decision_phase_stats.get(
                "confirmed_update_count", 0
            )
            
            import_log_result.analysis_total_rows_read = count_analysis_total_rows_read 
            import_log_result.analysis_failed_rows = count_analysis_failed_rows
            import_log_result.analysis_skipped_identical = (
                count_analysis_skipped_identical
            )
            import_log_result.analysis_found_new_count = count_analysis_found_new
            import_log_result.analysis_found_update_count = count_analysis_found_update
            import_log_result.analysis_successfully_parsed_rows = (
                count_analysis_successfully_parsed
            )

            import_log_result.user_decision_skipped_update_count = (
                count_user_decision_skipped_updates
            )
            import_log_result.user_decision_confirmed_update_count = (
                count_user_decision_confirmed_updates
            )

            import_log_result.import_task_total_records_submitted = (
                task_total_records_submitted
            )
            import_log_result.import_task_created_count = task_created_count
            import_log_result.import_task_updated_count = task_updated_count
            import_log_result.import_task_unchanged_count = task_unchanged_count
            import_log_result.import_task_processed_successfully_count = (
                task_processed_successfully_count
            )
            import_log_result.import_task_failed_count = task_failed_count

            import_log_result.overall_total_initial_records = (
                import_log_result.analysis_total_rows_read
            )
            import_log_result.overall_user_decision_skipped_updates = (
                import_log_result.user_decision_skipped_update_count
            )
            import_log_result.overall_final_created_count = (
                import_log_result.import_task_created_count
            )
            import_log_result.overall_final_updated_count = (
                import_log_result.import_task_updated_count
            )

            import_log_result.overall_skipped_by_system_total = (
                import_log_result.analysis_skipped_identical
                + import_log_result.import_task_unchanged_count
            )
            import_log_result.overall_skipped_total = (
                import_log_result.overall_skipped_by_system_total
                + import_log_result.user_decision_skipped_update_count
            )
            import_log_result.overall_failed_total = (
                import_log_result.analysis_failed_rows
                + import_log_result.import_task_failed_count
            )
            import_log_result.overall_processed_successfully_total = (
                import_log_result.import_task_processed_successfully_count
                + import_log_result.user_decision_skipped_update_count
                + import_log_result.analysis_skipped_identical
            )
            
            if is_noop_log: 
                # This block is for when df_to_process was empty initially, or if import_from_file failed severely.
                # Import task fields should reflect that no task processing happened or it failed early.
                if (
                    not "INITFAIL-" in import_log_result.batch_number
                ):  # if not an init fail, means it was a planned NOOP
                    import_log_result.import_task_total_records_submitted = 0
                    import_log_result.import_task_created_count = 0
                    import_log_result.import_task_updated_count = 0
                    import_log_result.import_task_unchanged_count = 0
                    import_log_result.import_task_processed_successfully_count = 0
                    import_log_result.import_task_failed_count = 0
                
                # Overall final counts for NOOP reflect no task execution
                import_log_result.overall_final_created_count = 0
                import_log_result.overall_final_updated_count = 0
                
                # Recalculate overall totals based on analysis and user decisions only for NOOP
                import_log_result.overall_processed_successfully_total = (
                    import_log_result.analysis_skipped_identical
                    + import_log_result.user_decision_skipped_update_count
                )
                import_log_result.overall_skipped_by_system_total = (
                    import_log_result.analysis_skipped_identical
                )
                import_log_result.overall_skipped_total = (
                    import_log_result.overall_skipped_by_system_total
                    + import_log_result.user_decision_skipped_update_count
                )
                import_log_result.overall_failed_total = (
                    import_log_result.analysis_failed_rows
                )

            summary_details = {
                "analysis_total_rows_read": import_log_result.analysis_total_rows_read,
                "analysis_failed_rows": import_log_result.analysis_failed_rows,
                "analysis_skipped_identical": import_log_result.analysis_skipped_identical,
                "analysis_found_new_count": import_log_result.analysis_found_new_count,
                "analysis_found_update_count": import_log_result.analysis_found_update_count,
                "analysis_successfully_parsed_rows": import_log_result.analysis_successfully_parsed_rows,
                "user_decision_skipped_update_count": import_log_result.user_decision_skipped_update_count,
                "user_decision_confirmed_update_count": import_log_result.user_decision_confirmed_update_count,
                "import_task_total_records_submitted": import_log_result.import_task_total_records_submitted,
                "import_task_created_count": import_log_result.import_task_created_count,
                "import_task_updated_count": import_log_result.import_task_updated_count,
                "import_task_unchanged_count": import_log_result.import_task_unchanged_count,
                "import_task_processed_successfully_count": import_log_result.import_task_processed_successfully_count,
                "import_task_failed_count": import_log_result.import_task_failed_count,
                "overall_total_initial_records": import_log_result.overall_total_initial_records,
                "overall_user_decision_skipped_updates": import_log_result.overall_user_decision_skipped_updates,
                "overall_final_created_count": import_log_result.overall_final_created_count,
                "overall_final_updated_count": import_log_result.overall_final_updated_count,
                "overall_skipped_by_system_total": import_log_result.overall_skipped_by_system_total,
                "overall_skipped_total": import_log_result.overall_skipped_total,
                "overall_processed_successfully_total": import_log_result.overall_processed_successfully_total,
                "overall_failed_total": import_log_result.overall_failed_total,
            }
            if import_log_result.detailed_report and isinstance(
                import_log_result.detailed_report, dict
            ):
                import_log_result.detailed_report["summary_execution_phase"] = (
                    summary_details
                )
            else:
                import_log_result.detailed_report = {
                    "summary_execution_phase": summary_details
                }
            
            # 根据总体统计结果，设置ImportLog的最终状态
            if (
                import_log_result.overall_failed_total == 0
                and import_log_result.analysis_total_rows_read > 0
            ):
                # 如果所有最初分析的行都通过某种方式成功处理了（包括跳过），且没有失败
                if (
                    import_log_result.overall_processed_successfully_total
                    == import_log_result.analysis_total_rows_read
                ):
                    import_log_result.status = "completed"
                elif (
                    import_log_result.overall_processed_successfully_total
                    + import_log_result.overall_failed_total
                    == import_log_result.analysis_total_rows_read
                ):
                    import_log_result.status = "partial"  # 有失败，但所有行都有处置
                else: 
                    import_log_result.status = (
                        "partial"  # 有些行未被明确处理，但无失败，也视为部分
                    )
            elif import_log_result.overall_failed_total > 0:
                if import_log_result.overall_processed_successfully_total > 0:
                    import_log_result.status = "partial"
                else:
                    import_log_result.status = "failed"
            elif (
                import_log_result.analysis_total_rows_read == 0
            ):  # 没有读取到行（例如空文件）
                import_log_result.status = "completed"  # 可视为完成，无错误
            else:  # 其他未知情况，保守起见标记为部分
                import_log_result.status = "partial"
            
            # is_noop_log 情况下，status 通常已经是 'completed' (在NOOP分支中设置了)
            if is_noop_log and "INITFAIL" not in import_log_result.batch_number:
                import_log_result.status = "completed"
            elif is_noop_log and "INITFAIL" in import_log_result.batch_number:
                 import_log_result.status = "failed"

            try:
                fields_to_save_log = [
                    "analysis_total_rows_read",
                    "analysis_failed_rows",
                    "analysis_skipped_identical",
                    "analysis_found_new_count",
                    "analysis_found_update_count",
                    "analysis_successfully_parsed_rows",
                    "user_decision_skipped_update_count",
                    "user_decision_confirmed_update_count",
                    "import_task_total_records_submitted",
                    "import_task_created_count",
                    "import_task_updated_count",
                    "import_task_unchanged_count",
                    "import_task_processed_successfully_count",
                    "import_task_failed_count",
                    "overall_total_initial_records",
                    "overall_user_decision_skipped_updates",
                    "overall_final_created_count",
                    "overall_final_updated_count",
                    "overall_skipped_by_system_total",
                    "overall_skipped_total",
                    "overall_processed_successfully_total",
                    "overall_failed_total",
                    "status",
                    "error_message",
                    "detailed_report",
                    "updated_at",
                ]
                current_model_fields = [f.name for f in ImportLog._meta.get_fields()]
                fields_to_save_log_existing = [
                    f for f in fields_to_save_log if f in current_model_fields
                ]
                
                import_log_result.save(update_fields=fields_to_save_log_existing)
                logger.info(
                    f"[Mgr.ExecuteImport] ImportLog {import_log_result.id} 统计更新完成。FinalStatus: {import_log_result.status}, OverallSuccess: {import_log_result.overall_processed_successfully_total}, ImportTaskProcessedSuccess: {import_log_result.import_task_processed_successfully_count}"
                )
            except Exception as e_save_log:
                logger.error(
                    f"[Mgr.ExecuteImport] 保存 ImportLog {import_log_result.id} 时发生错误: {e_save_log}",
                    exc_info=True,
                )

            # 在保存 ImportLog 之前，根据 ImportLog 的最终状态更新 ImportSession 的最终状态和进度
            if db_session:  # 再次确保 db_session 存在
                if import_log_result and import_log_result.status == "completed":
                    # CHANGE: [2025-06-03] 移除所有状态设置，让Celery任务做最终权威状态设置
                    # 这里只设置进度和清理错误信息
                    db_session.progress = 100.0
                    db_session.error_message = None  # 清除可能存在的旧错误信息
                    # CHANGE: [2025-06-03] 手动设置结果展示过期时间
                    duration_minutes = getattr(settings, 'IMPORT_SUCCESS_DISPLAY_MINUTES', 5)
                    db_session.results_display_expires_at = timezone.now() + timedelta(minutes=duration_minutes)
                elif import_log_result and import_log_result.status in ["partial", "failed"]:
                    # CHANGE: [2025-06-03] 修复状态映射逻辑：
                    # - partial: 部分成功，部分失败 → IMPORT_COMPLETED_WITH_ERRORS
                    # - failed: 全部失败但能生成ImportLog → 通常是数据质量问题 → IMPORT_COMPLETED_WITH_ERRORS
                    # 只有无法生成ImportLog的情况才是真正的系统ERROR
                    db_session.progress = 100.0 
                    if import_log_result.status == "partial":
                        db_session.error_message = (
                            import_log_result.error_log
                            or f"导入部分成功，详情见导入日志ID: {import_log_result.id}"
                        )
                    else:  # failed
                        db_session.error_message = (
                            import_log_result.error_log
                            or f"导入失败（数据质量问题），详情见导入日志ID: {import_log_result.id}"
                        )
                elif (
                    not import_log_result
                ):  # 如果 import_log_result 本身就是 None (真正的系统错误)
                    # CHANGE: [2025-06-03] 只有无法生成ImportLog时才是真正的系统ERROR
                    db_session.status = ImportSessionStatus.ERROR
                    db_session.progress = 100.0  # 流程也算结束了
                    db_session.error_message = (
                        db_session.error_message
                        or "执行导入时发生严重系统错误，未能生成导入日志。"
                    )
                
                # 清理处理者和心跳，因为导入流程已结束 (无论成功与否)
                db_session.processing_user = None
                db_session.last_heartbeat_at = None
                
                # 此处不再调用 db_session.set_results_display_expiry()
                # results_display_expires_at 字段由 Celery 任务 (tasks.py) 在此方法返回后，
                # 根据 import_log_instance.status 的最终结果来显式设置。

                # 记录最终的会话操作
                # 注意：Celery任务 (tasks.py) 在 _execute_import_with_resolutions 返回后，也会根据 import_log_instance.status
                # 来设置 session.status, session.import_log, session.error_message 并保存。
                # 为了避免冲突或重复保存，这里的 session 更新主要是为了确保 progress 是100% (如果已完成)
                # Celery 任务中的 session 保存应该作为最终权威。
                # 也许这里不需要显式 save db_session，而是让 Celery 任务根据返回的 import_log 来做最终更新。
                # 但为了确保 progress 在成功时是100%，我们在这里尝试更新它。
                # 稍后检查 Celery task 中的 session save 操作。
                fields_for_final_session_save = [
                    "progress",
                    "error_message",
                    "processing_user",
                    "last_heartbeat_at",
                    "updated_at",
                ]
                # CHANGE: [2025-06-03] 只有在无法生成ImportLog的系统错误时才设置status字段
                if not import_log_result:  # 只有真正的系统错误（无法生成ImportLog）
                    fields_for_final_session_save.append("status")
                # 移除了对import_log_result.status == "failed"的状态设置，因为这通常是数据问题，不是系统问题
                # CHANGE: 如果设置了results_display_expires_at，也需要保存
                if (
                    hasattr(db_session, "results_display_expires_at")
                    and db_session.results_display_expires_at
                ):
                    fields_for_final_session_save.append("results_display_expires_at")
                try:
                    # 确保只保存模型中存在的字段
                    session_model_fields = [
                        f.name for f in ImportSession._meta.get_fields()
                    ]
                    fields_to_save_session_existing = [
                        f
                        for f in fields_for_final_session_save
                        if f in session_model_fields
                    ]
                    if fields_to_save_session_existing:
                        db_session.save(update_fields=fields_to_save_session_existing)
                        logger.info(
                            f"[Mgr.ExecuteImport.FinalizeSession] 会话 {db_session.session_id} 最终状态: {db_session.status}, 最终进度: {db_session.progress}"
                        )
                except Exception as e_final_save_session:
                    logger.error(
                        f"[Mgr.ExecuteImport.FinalizeSession] 保存最终会话状态时失败: {e_final_save_session}",
                        exc_info=True,
                    )

        else: 
            logger.error(
                "[Mgr.ExecuteImport] _execute_import_with_resolutions 未能生成或获取有效的ImportLog实例，无法更新统计数据。"
            )
            # CHANGE: [2025-06-03] 如果无法生成ImportLog，这是系统错误，应该抛出异常
            raise Exception("未能生成有效的ImportLog实例（系统错误）")
        
        return import_log_result
    
    def _get_commission_number(
        self, row: pd.Series, field_mapping: Dict[str, str]
    ) -> str:
        """
        从行数据中获取委托编号

        Args:
            row: DataFrame行
            field_mapping: 字段映射

        Returns:
            委托编号或空字符串
        """
        # 尝试不同的可能字段名
        for excel_col, model_field in field_mapping.items():
            if model_field == "commission_number" and excel_col in row:
                return row[excel_col]

        return ""

    def _cleanup_session_data(self, session_id: str) -> None:
        """
        清理会话相关数据 (此方法名在之前讨论中已更新为 _cleanup_session_resources, 此处仅为占位)

        Args:
            session_id: 会话ID
        """
        # ... (此方法的实际实现已在 _cleanup_session_resources 中)
        pass

    # CHANGE: [2025-05-16] 添加 record_heartbeat 方法用于记录会话心跳
    def record_heartbeat(self, session_id: uuid.UUID, user: User) -> Tuple[bool, str]:
        """
        记录指定会话的心跳。

        Args:
            session_id: 要记录心跳的会话ID。
            user: 发起心跳的用户。

        Returns:
            Tuple[bool, str]: 一个元组，第一个元素表示操作是否成功，第二个元素是消息。
        """
        try:
            session = ImportSession.objects.get(session_id=session_id)
        except ImportSession.DoesNotExist:
            logger.warning(
                f"[RecordHeartbeat] 尝试为不存在的会话 {session_id} 记录心跳。"
            )
            return False, "会话不存在。"

        if not session.is_active():
            logger.info(
                f"[RecordHeartbeat] 会话 {session_id} 非活跃 (状态: {session.status})，不记录心跳。"
            )
            # 注意：如果会话因为已完成或取消而非活跃，前端通常不应再发送心跳。
            # 如果是因为过期，is_active()会返回False。
            return False, "会话非活跃，无法记录心跳。"

        if session.processing_user != user:
            logger.warning(
                f"[RecordHeartbeat] 用户 {user.username} (ID: {user.id}) "
                f"尝试为非其处理的会话 {session_id} (处理者: {session.processing_user.username if session.processing_user else '无'}) 记录心跳。"
            )
            return False, "您不是当前会话的处理者，无法发送心跳。"

        try:
            # 使用 transaction.atomic 来确保更新的原子性
            with transaction.atomic():
                # 重新获取并锁定行，防止并发更新问题，确保我们操作的是最新数据
                session_to_update = ImportSession.objects.select_for_update().get(
                    session_id=session_id
                )
                
                # 再次校验处理者和活跃状态，确保在获取和锁定之间状态未改变
                if session_to_update.processing_user != user:
                    logger.warning(
                        f"[RecordHeartbeat] 在锁定后，会话 {session_id} 的处理者已变为 {session_to_update.processing_user.username if session_to_update.processing_user else '无'}，心跳未记录。"
                    )
                    return False, "会话处理者已改变，心跳未记录。"
                
                if (
                    not session_to_update.is_active()
                ):  # is_active 会检查 status 和 expires_at
                    logger.warning(
                        f"[RecordHeartbeat] 在锁定后，会话 {session_id} 已变为非活跃状态 (状态: {session_to_update.status})，心跳未记录。"
                    )
                    return False, "会话已变为非活跃状态，心跳未记录。"

                session_to_update.last_heartbeat_at = timezone.now()
                session_to_update.last_activity = (
                    timezone.now()
                )  # 更新心跳时，也应视为一次活动
                session_to_update.save(
                    update_fields=["last_heartbeat_at", "last_activity", "updated_at"]
                )

            logger.info(
                f"[RecordHeartbeat] 用户 {user.username} 成功记录会话 {session_id} 的心跳。"
            )
            return True, "心跳已成功记录。"
        except ImportSession.DoesNotExist:  # 在select_for_update时，如果记录已被删除
            logger.warning(
                f"[RecordHeartbeat] 在尝试锁定并更新时，会话 {session_id} 已不存在。"
            )
            return False, "会话在更新心跳时已不存在。"
        except Exception as e:
            logger.error(
                f"[RecordHeartbeat] 记录会话 {session_id} 心跳时发生数据库错误: {str(e)}",
                exc_info=True,
            )
            return False, f"记录心跳时发生服务器内部错误。"

    def acknowledge_session_results(
        self, session: ImportSession, user: User
    ) -> Tuple[
        bool, str, Optional[ImportSessionStatus]
    ]:  # CHANGED: session_id to session object
        logger.info(
            f"[AcknowledgeResults] 用户 {user.username} 请求确认处理会话 {session.session_id} (当前状态: {session.status})"
        )

        allowed_statuses_for_acknowledgement = [
            ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
            ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
            ImportSessionStatus.ERROR,
        ]

        if session.status not in allowed_statuses_for_acknowledgement:
            logger.warning(
                f"[AcknowledgeResults] 用户 {user.username} 尝试确认状态为 {session.status} 的会话 {session.session_id}，操作被拒绝。"
            )
            if session.status == ImportSessionStatus.FINALIZED:
                error_message = "会话已处于最终处理状态，无需再次确认。"
            elif session.status == ImportSessionStatus.CANCELLED:
                 error_message = "会话已被取消，无需确认。"
            else: 
                error_message = f"会话当前状态为 ({session.get_status_display()})，不适用于此确认操作。"
            return False, error_message, session.status

        original_status_for_log = session.status
        new_status = ImportSessionStatus.FINALIZED
        
        log_op_details_message = ""
        update_fields = ["status", "error_message", "updated_at"]
        log_op_type = "user_acknowledge_and_finalize_session" # 统一的操作类型

        if original_status_for_log in [
            ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
            ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS,
        ]:
            session.results_display_expires_at = None 
            update_fields.append("results_display_expires_at")
            session.error_message = session.error_message or "用户已确认导入结果，会话已最终处理。"
            success_message = "导入结果已成功确认并最终处理。"
            log_op_details_message = f"{success_message} (原状态: {original_status_for_log})"
        elif original_status_for_log == ImportSessionStatus.ERROR:
            current_error_msg = session.error_message or "未知流程错误"
            session.error_message = f"{current_error_msg} (用户已确认此错误，会话已由用户操作结束并最终处理)"
            success_message = "此错误会话已由您确认并最终处理。"
            log_op_details_message = f"{success_message} (原状态: {original_status_for_log})"
        else:
            logger.error(f"[AcknowledgeResults] 意外的原始状态 {original_status_for_log} 在确认逻辑中用于会话 {session.session_id}。")
            return False, "确认操作遇到意外的内部状态，请联系管理员。", original_status_for_log

        session.status = new_status
        session.save(update_fields=list(set(update_fields)))
        
        SessionOperation.objects.create(
            session=session,
            operation_type=log_op_type, # 使用统一的类型
            user=user,
            old_status=original_status_for_log,
            new_status=new_status,
            details={"message": log_op_details_message, "acknowledged_by": user.username, "original_status": original_status_for_log},
        )
        logger.info(
            f"[AcknowledgeResults] 会话 {session.session_id} (原状态: {original_status_for_log}) 已被用户 {user.username} 确认并更新为 {new_status}。"
        )
        return True, success_message, new_status
