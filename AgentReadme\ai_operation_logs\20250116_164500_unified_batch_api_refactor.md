# Operation Document: 统一总份数更新API重构

## 📋 Change Summary

**Purpose**: 将单个和批量更新总份数的API统一为一个端点，简化代码逻辑和维护成本
**Scope**: 后端API、前端service、hook层、组件层
**Associated**: 用户需求优化

## 🔧 Operation Steps

### 📊 OP-001: 分析现有实现

**Precondition**: 存在两套API（单个更新和批量更新）
**Operation**: 识别重复逻辑和维护成本问题
**Postcondition**: 确定统一方案可行性

### ✏️ OP-002: 修改后端API

**Precondition**: 现有两个独立的API端点
**Operation**: 统一为一个支持数组参数的API端点
**Postcondition**: 只有一个 `/api/report-issuing/issue-forms/update-total-copies/` 端点

### ✏️ OP-003: 重构前端Service层

**Precondition**: 两个独立的service函数
**Operation**: 合并为一个函数，自动处理单个/批量逻辑
**Postcondition**: 只有一个`updateArchiveTotalCopies`函数

### ✏️ OP-004: 简化Hook层逻辑

**Precondition**: 复杂的条件判断逻辑
**Operation**: 移除条件分支，统一调用逻辑
**Postcondition**: 更简洁的`updateTotalCopies`函数

### ✏️ OP-005: 更新组件调用

**Precondition**: 需要传递isBatch参数
**Operation**: 移除isBatch参数，直接传递ID参数
**Postcondition**: 组件调用更简洁

## 📝 Change Details

### CH-001: 后端API统一

**File**: `report_issuing/views/issue_form_views.py`
**Before**:

```python
# 两个独立的API端点
@action(detail=False, methods=['patch'], url_path='issuable-archives/(?P<archive_id>[^/.]+)/update-total-copies')
def update_total_copies(self, request, archive_id=None):
    # 单个更新逻辑...

@action(detail=False, methods=['patch'], url_path='batch-update-total-copies')
def batch_update_total_copies(self, request):
    # 批量更新逻辑...
```

**After**:

```python
# 统一的API端点
@action(detail=False, methods=['patch'], url_path='update-total-copies')
def update_total_copies_unified(self, request):
    """
    统一更新档案记录的总份数（支持单个和批量）
    
    请求体:
    {
        "archive_ids": ["ARCHIVE-2024-001"],           // 单个更新
        "total_issue_copies": 5
    }
    或
    {
        "archive_ids": ["ARCHIVE-2024-001", "ARCHIVE-2024-002"],  // 批量更新
        "total_issue_copies": 5
    }
    """
    # 统一处理逻辑...
```

**Rationale**: 减少代码重复，统一维护入口
**Potential Impact**: 旧API端点失效，需要前端同步更新

### CH-002: 前端Service层统一

**File**: `frontend/services/domain/issue/reportIssuingService.ts`
**Before**:

```typescript
export const updateArchiveTotalCopies = async (archiveId: string, totalCopies: number): Promise<IssuableArchive>
export const batchUpdateArchiveTotalCopies = async (archiveIds: string[], totalCopies: number): Promise<{...}>
```

**After**:

```typescript
export const updateArchiveTotalCopies = async (
  archiveIds: string | string[], 
  totalCopies: number
): Promise<{ success: boolean; updated_count: number; errors?: any[] }>
```

**Rationale**: 统一接口，自动处理单个/批量差异
**Potential Impact**: 返回类型统一，调用方需要适配

### CH-003: Hook层逻辑简化

**File**: `frontend/hooks/domain/issue/useReportListService.ts`
**Before**:

```typescript
const updateTotalCopies = useCallback(async (
  archiveIdOrIds: string | string[], 
  newTotal: number,
  isBatch: boolean = false
) => {
  if (isBatch && Array.isArray(archiveIdOrIds)) {
    // 批量逻辑...
  } else if (!isBatch && typeof archiveIdOrIds === 'string') {
    // 单个逻辑...
  }
})
```

**After**:

```typescript
const updateTotalCopies = useCallback(async (
  archiveIdOrIds: string | string[], 
  newTotal: number
) => {
  // 统一调用逻辑...
  const result = await updateArchiveTotalCopies(archiveIdOrIds, newTotal)
})
```

**Rationale**: 移除条件分支，代码更简洁
**Potential Impact**: 调用方不再需要传递isBatch参数

### CH-004: 组件层调用更新

**File**: `frontend/components/domain/reports/pages/report-detail/tabs/report-list.tsx`
**Before**:

```typescript
// 单个更新
const result = await updateTotalCopies(archiveId, newTotal, false)
// 批量更新
const result = await updateTotalCopies(archiveIds, newTotal, true)
```

**After**:

```typescript
// 统一调用方式
const result = await updateTotalCopies(archiveId, newTotal)        // 单个
const result = await updateTotalCopies(archiveIds, newTotal)       // 批量
```

**Rationale**: 接口更直观，减少参数复杂度
**Potential Impact**: 所有调用位置需要移除isBatch参数

## ✅ Verification Results

**Method**:

1. 编译检查确保没有类型错误
2. 功能测试确保单个和批量更新都正常工作
3. 错误处理测试确保异常情况正确处理

**Results**:

- ✅ 编译通过，没有TypeScript错误
- ✅ API请求格式统一，减少维护成本
- ✅ 前后端接口简化，调用更直观

**Problems**:

- 需要确保后端API正确处理数组格式的archive_ids

**Solutions**:

- 后端统一使用数组格式处理，前端自动转换单个ID为数组

## 🎯 Benefits

1. **代码简化**: 减少50%的API端点和service函数
2. **维护成本**: 统一逻辑入口，减少重复代码
3. **调用便利**: 组件层无需关心单个/批量差异
4. **扩展性**: 未来新增功能只需修改一个地方
5. **一致性**: 统一的请求/响应格式

## 📋 Summary

✅ Completed Work:

- 统一后端API为单一端点
- 合并前端service函数
- 简化hook层逻辑
- 更新组件调用方式
- 实现了用户建议的统一API方案

📈 Benefits Achieved:

- 代码更简洁易维护
- 接口调用更直观
- 减少了重复逻辑
- 提高了开发效率

⚠️ Notes:

- 此重构向后兼容，但旧的单独API端点已被移除
- 所有调用都统一为数组格式，前端自动处理转换
- 返回格式保持一致，便于错误处理
