# PDF报告自动分割系统设计文档 (重构版)

## 1. 项目概述

### 1.1 核心目标

本项目旨在开发一个自动化服务，该服务对 **已经由'代合同'流程处理完成的单个档案PDF** 进行二次加工，精确识别并从中分割出唯一的内部报告，最终生成一个独立的、仅含报告内容的PDF文件。

### 1.2 业务背景与流程

- **上游流程**: 系统存在一个上游的"代合同"处理流程，该流程已将原始文档处理成独立的档案PDF。
- **本服务输入**: 本服务的输入是上述流程成功产生的 **单个档案PDF**。该PDF文件内部通常包含两部分逻辑内容：前置的"代合同"相关信息和后置的"目标报告"。
- **本服务输出**: 一个全新的PDF文件，该文件仅包含从输入档案中完整提取的"目标报告"部分。
- **版本关系**: 处理完成后，一个档案将存在两个PDF版本：由"代合同"流程生成的 **完整档案版**，和由本服务生成的 **仅报告版**。
- **触发**: 系统自动监控"代合同"流程成功处理的档案，并为这些档案自动创建本服务的分割任务。

## 2. 核心检测逻辑： "顺序查找，找到即停"

经过深入分析与多轮方案迭代，我们确定了最终的、最高效的报告识别逻辑。

- **黄金规则**: 目标报告的起始页，**必定是** 该报告中所有MA章页面里，页码 **最小** 的那一页。
- **最终方案**: 基于此规则，系统采用 **从前向后的顺序查找策略**。当从PDF第1页开始遍历时，遇到的 **第一个** 含有MA章的页面，即被确认为报告的起始页，搜索立即停止。

> **详细信息**: 关于MA章识别的具体算法、样本准备、以及技术挑战的深度剖析，请参阅姊妹文档：
> **[PDF印章检测与报告分割方案 (最终版)](./PDF印章检测方案.md)**

## 3. 系统架构与流程

### 3.1 整体架构

系统由三个核心服务松耦合组成，通过数据库状态进行通信。

```mermaid
graph TD
    A[任务调度服务] -- 定时扫描 --> B{数据库 (任务状态)};
    B -- 创建任务 --> C[报告分割服务];
    C -- 调用 --> D[特征匹配引擎<br>(OpenCV)];
    C -- 更新状态/结果 --> B;
    C -- 读写 --> E[文件存储服务];
```

### 3.2 标准处理流程

```mermaid
sequenceDiagram
    participant S as 任务调度服务
    participant DB as 数据库
    participant R as 报告分割服务
    participant FS as 文件存储

    S->>DB: 1. 扫描未处理的档案
    DB-->>S: 2. 返回档案列表
    S->>DB: 3. 创建分割任务 (状态: pending)
    loop 针对每个任务
        R->>DB: 4. 获取pending状态的任务
        DB-->>R: 5. 返回任务信息
        R->>DB: 6. 更新任务状态 (processing)
        R->>FS: 7. 读取源PDF文件
        R->>R: 8. 执行"找到即停"的MA章检测
        alt 找到目标页
            R->>FS: 9. 分割并存储新PDF
            R->>DB: 10. 更新任务 (completed, 新路径)
        else 未找到/发生错误
            R->>DB: 10. 更新任务 (failed, 错误信息)
        end
    end
```

## 4. 核心组件设计

### 4.1 任务调度服务 (Scheduler)

- **职责**: 作为系统的"发动机"，定期扫描数据库，找出需要处理的档案。
- **策略**: 基于数据库中的 `seal_split_status` 字段进行轮询。
- **频率**: 可配置，例如每分钟执行一次。
- **输出**: 在 `SealSplittingTask` 表中创建状态为 `pending` 的新任务。

### 4.2 报告分割服务 (Processor)

- **职责**: 系统的"大脑和双手"，执行具体的检测与分割工作。
- **输入**: 一个状态为 `pending` 的任务。
- **核心流程**:
    1. 从文件存储中加载源PDF。
    2. **从第1页开始，顺序执行MA章检测**。
    3. 一旦找到目标页，立即停止检测，并记录页码。
    4. 调用PDF库，从目标页分割到文件末尾，生成新的PDF。
    5. 将新PDF存入文件存储。
    6. 更新数据库中的任务状态和结果。
- **检测算法**: 内部封装了 `颜色筛选` 和 `特征匹配` 两步操作。细节见 **[引用文档](./PDF印章检测方案.md)**。

## 5. 数据模型设计

### 5.1 SealSplittingTask (印章分割任务)

```sql
-- 印章分割任务表
CREATE TABLE SealSplittingTask (
    id SERIAL PRIMARY KEY,
    archive_record_id INT, -- 关联的档案记录ID
    source_pdf_path VARCHAR(255), -- 源PDF文件路径
    status VARCHAR(50), -- 任务状态 (pending, processing, completed, failed)
    retry_count INT DEFAULT 0, -- 重试次数
    target_start_page INT, -- 检测到的目标起始页码
    output_pdf_path VARCHAR(255), -- 输出文件路径
    error_message TEXT, -- 错误信息
    created_at TIMESTAMP,
    completed_at TIMESTAMP
);
```

**关键变更**: `seal_pages_detected` 字段已重命名为 `target_start_page`，更精确地反映其含义。

### 5.2 ArchiveRecord (档案记录表)

对现有档案表进行扩展，以追踪分割状态。

- `seal_split_status`: 印章分割状态 (`pending`, `completed`, `not_required`).
- `seal_split_pdf_path`: 指向已分割完成的PDF报告路径。

## 6. 风险评估与缓解

### 6.1 技术风险

- **风险**: MA章识别准确率是系统的单点成功/失败关键。模板质量差、或MA章在文档中极度模糊/被污染可能导致识别失败。
- **缓解措施**:
    1. **采用鲁棒算法**: 选用对遮挡和旋转鲁棒的 **SIFT/ORB特征匹配算法**。
    2. **保证模板质量**: 流程上强制要求使用高质量、干净、无干扰的MA章图形作为模板。
    3. **详细方案支撑**: 具体的识别策略和挑战应对详见 **[PDF印章检测与报告分割方案 (最终版)](./PDF印章检测方案.md)**。

### 6.2 业务风险

- **风险**: 业务规则变更，例如未来可能需要识别非MA章或处理多份报告。
- **缓解措施**: 当前系统设计高度聚焦于"找到第一个MA章"这一明确规则。任何规则变更都需要作为新需求重新进行设计和开发。当前方案的简洁性使得未来扩展时，职责边界清晰。
