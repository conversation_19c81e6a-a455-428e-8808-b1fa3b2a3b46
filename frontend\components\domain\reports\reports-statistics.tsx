"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { FileText, Clock, FileCheck, Archive } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ReportStatProps {
  title: string
  value: string
  icon: React.ElementType
  description: string
  href: string
  color: string
  percentage?: string
}

function ReportStat({ title, value, icon: Icon, description, href, color, percentage }: ReportStatProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 text-${color}-500`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {percentage && <div className="text-xs text-muted-foreground mt-1">占比 {percentage}</div>}
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
        <Button variant="link" className="p-0 h-auto text-xs text-muted-foreground mt-2" asChild>
          <Link href={href}>查看详情</Link>
        </Button>
      </CardContent>
    </Card>
  )
}

export function ReportsStatistics() {
  // 这里可以从API获取实际数据
  const stats = [
    {
      title: "草稿发放单",
      value: "24",
      icon: Clock,
      description: "处于草稿状态的发放单",
      href: "/reports/management?status=draft",
      color: "yellow",
    },
    {
      title: "今日已发放",
      value: "12",
      icon: FileText,
      description: "今日完成发放的报告项数",
      href: "/reports/management?date=today&status=issued",
      color: "green",
    },
    {
      title: "已锁定发放单",
      value: "8",
      icon: Archive,
      description: "已锁定待发放的发放单",
      href: "/reports/management?status=locked",
      color: "indigo",
    },
    {
      title: "已发放完成",
      value: "156",
      icon: FileCheck,
      description: "已完成发放的发放单总数",
      href: "/reports/management?status=issued",
      color: "blue",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <ReportStat key={stat.title} {...stat} />
      ))}
    </div>
  )
}
