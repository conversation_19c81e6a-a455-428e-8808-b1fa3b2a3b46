/**
 * 统一的 API 请求客户端模块 (基于 Axios)
 * 
 * CHANGE: [2025-01-16] 全面重构为现代化、健壮的API客户端
 * - 深度集成NextAuth，自动处理认证
 * - 完整的错误处理和重试机制
 * - 支持请求/响应拦截器
 * - 统一的响应格式处理
 * - 自动token刷新和认证错误处理
 */

import axios, { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  InternalAxiosRequestConfig,
  AxiosError 
} from 'axios';
import { getSession, signOut } from 'next-auth/react';

// ==========================================
// 类型定义
// ==========================================

/**
 * 统一的API响应格式
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  error_code?: string;
  message?: string;
}

/**
 * 标准化API错误类型
 */
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

/**
 * API客户端配置选项
 */
export interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  withCredentials?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * 请求重试配置
 */
interface RetryConfig {
  attempts: number;
  delay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

// ==========================================
// 默认配置
// ==========================================

const DEFAULT_CONFIG: Required<ApiClientConfig> = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 30000, // 30秒
  withCredentials: true,
  retryAttempts: 3,
  retryDelay: 1000, // 1秒
};

// ==========================================
// 工具函数
// ==========================================

/**
 * 延迟函数
 */
const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * 判断是否应该重试的错误
 */
const shouldRetry = (error: AxiosError): boolean => {
  if (!error.response) {
    // 网络错误，应该重试
    return true;
  }
  
  const status = error.response.status;
  // 5xx服务器错误和408超时错误应该重试
  return status >= 500 || status === 408 || status === 429;
};

/**
 * 检查响应是否为标准格式
 */
const isStandardResponse = (data: any): data is ApiResponse => {
  return data && typeof data === 'object' && 'success' in data;
};

// ==========================================
// 创建Axios实例
// ==========================================

class ApiClient {
  private instance: AxiosInstance;
  private retryConfig: RetryConfig;

  constructor(config: ApiClientConfig = {}) {
    const finalConfig = { ...DEFAULT_CONFIG, ...config };
    
    this.retryConfig = {
      attempts: finalConfig.retryAttempts,
      delay: finalConfig.retryDelay,
      retryCondition: shouldRetry,
    };

    // 创建Axios实例
    this.instance = axios.create({
      baseURL: finalConfig.baseURL,
      timeout: finalConfig.timeout,
      withCredentials: finalConfig.withCredentials,
      headers: {
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器 - 自动添加认证头
    this.instance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        try {
          const session = await getSession();
          
          if (session?.accessToken) {
            config.headers.Authorization = `Bearer ${session.accessToken}`;
          }

          // CHANGE: [YYYY-MM-DD] 动态设置Content-Type
          // 只有当请求体不是FormData时，才设置Content-Type为application/json
          if (!(config.data instanceof FormData)) {
            config.headers['Content-Type'] = 'application/json';
          }
          // 如果是FormData，则让浏览器自动设置，它会包含正确的boundary
          
          // 记录请求日志（仅在开发环境）
          if (process.env.NODE_ENV === 'development') {
            console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
              headers: config.headers,
              data: config.data,
            });
          }
          
          return config;
        } catch (error) {
          console.error('[API] 请求拦截器错误:', error);
          return config;
        }
      },
      (error) => {
        console.error('[API] 请求配置错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 统一处理响应和错误
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 记录响应日志（仅在开发环境）
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API] Response ${response.status}:`, response.data);
        }
        
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };
        
        // 处理401认证错误
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            // 尝试刷新session
            const session = await getSession();
            if (!session) {
              // 没有session，重定向到登录页
              await signOut({ redirect: true, callbackUrl: '/login' });
              return Promise.reject(error);
            }
            
            // 重新发起请求
            return this.instance(originalRequest);
          } catch (refreshError) {
            console.error('[API] Token刷新失败:', refreshError);
            await signOut({ redirect: true, callbackUrl: '/login' });
            return Promise.reject(error);
          }
        }
        
        // 处理其他错误
        console.error('[API] 请求失败:', {
          url: error.config?.url,
          status: error.response?.status,
          message: error.message,
          data: error.response?.data,
        });
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * 执行带重试的请求
   */
  private async requestWithRetry<T>(
    config: AxiosRequestConfig,
    attempt: number = 1
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.instance.request<T>(config);
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // 检查是否应该重试
      if (
        attempt < this.retryConfig.attempts &&
        this.retryConfig.retryCondition?.(axiosError)
      ) {
        console.warn(`[API] 请求失败，${this.retryConfig.delay}ms后进行第${attempt + 1}次重试:`, {
          url: config.url,
          attempt,
          error: axiosError.message,
        });
        
        await delay(this.retryConfig.delay * attempt); // 指数退避
        return this.requestWithRetry<T>(config, attempt + 1);
      }
      
      throw error;
    }
  }

  /**
   * 统一的API请求方法
   */
  async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.requestWithRetry<T>(config);
      const { data, status } = response;
      
      // 处理标准响应格式
      if (isStandardResponse(data)) {
        return {
          success: data.success,
          data: data.data,
          error: data.error,
          error_code: data.error_code,
          message: data.message,
        };
      }
      
      // 处理非标准响应格式
      if (status === 204) {
        // No Content
        return { success: true, data: undefined as T };
      }
      
      // 其他成功响应
      return { success: true, data: data as T };
      
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // 构造错误响应
      const errorResponse: ApiResponse<T> = {
        success: false,
        error: this.extractErrorMessage(axiosError),
      };
      
      return errorResponse;
    }
  }

  /**
   * 提取错误信息
   */
  private extractErrorMessage(error: AxiosError): string {
    if (error.response?.data) {
      const data = error.response.data as any;
      
      // 尝试从不同字段提取错误信息
      if (typeof data === 'string') {
        return data;
      }
      
      if (data.error) {
        return data.error;
      }
      
      if (data.message) {
        return data.message;
      }
      
      if (data.detail) {
        return data.detail;
      }
    }
    
    // 网络错误或其他错误
    if (error.code === 'ECONNABORTED') {
      return '请求超时，请稍后重试';
    }
    
    if (error.code === 'ERR_NETWORK') {
      return '网络连接失败，请检查网络设置';
    }
    
    return error.message || '请求失败，请稍后重试';
  }

  // ==========================================
  // 便捷方法
  // ==========================================

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      ...config,
      method: 'POST',
      url,
      data: formData,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * 下载文件
   */
  async download(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    try {
      const response = await this.requestWithRetry<Blob>({
        ...config,
        method: 'GET',
        url,
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      console.error('[API] 文件下载失败:', error);
      throw error;
    }
  }

  /**
   * 获取原始Axios实例（用于特殊需求）
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// ==========================================
// 导出
// ==========================================

// 创建默认实例
const apiClient = new ApiClient();

// 导出默认实例和类
export default apiClient;
export { ApiClient };

// 导出便捷方法
export const {
  get: apiGet,
  post: apiPost,
  put: apiPut,
  patch: apiPatch,
  delete: apiDelete,
  upload: apiUpload,
  download: apiDownload,
} = apiClient; 