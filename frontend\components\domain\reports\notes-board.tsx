"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Save, Plus, X } from "lucide-react"

interface Note {
  id: string
  content: string
  date: string
}

export function NotesBoard() {
  const [notes, setNotes] = useState<Note[]>([
    {
      id: "1",
      content: "5月15日需要完成所有4月份的报告发放工作",
      date: "2023-05-10",
    },
    {
      id: "2",
      content: "注意：张工负责的项目报告需要优先处理",
      date: "2023-05-09",
    },
  ])
  const [newNote, setNewNote] = useState("")
  const [isAdding, setIsAdding] = useState(false)

  const handleAddNote = () => {
    if (newNote.trim()) {
      const today = new Date().toISOString().split("T")[0]
      setNotes([
        {
          id: Date.now().toString(),
          content: newNote,
          date: today,
        },
        ...notes,
      ])
      setNewNote("")
      setIsAdding(false)
    }
  }

  const handleDeleteNote = (id: string) => {
    setNotes(notes.filter((note) => note.id !== id))
  }

  return (
    <Card className="col-span-1">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-medium">备注板</CardTitle>
        {!isAdding && (
          <Button variant="outline" size="sm" onClick={() => setIsAdding(true)} className="h-8 px-2">
            <Plus className="h-4 w-4 mr-1" /> 添加备注
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {isAdding && (
          <div className="mb-4 space-y-2">
            <Textarea
              placeholder="输入备注内容..."
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              className="min-h-[80px]"
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" size="sm" onClick={() => setIsAdding(false)}>
                取消
              </Button>
              <Button size="sm" onClick={handleAddNote}>
                <Save className="h-4 w-4 mr-1" /> 保存
              </Button>
            </div>
          </div>
        )}

        <div className="space-y-3 max-h-[300px] overflow-y-auto pr-1">
          {notes.length > 0 ? (
            notes.map((note) => (
              <div key={note.id} className="p-3 border rounded-md relative group">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleDeleteNote(note.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
                <p className="text-sm">{note.content}</p>
                <p className="text-xs text-muted-foreground mt-2">{note.date}</p>
              </div>
            ))
          ) : (
            <p className="text-center text-muted-foreground py-8">暂无备注</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
