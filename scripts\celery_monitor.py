#!/usr/bin/env python3
"""
Celery队列监控和管理脚本

用法:
    python scripts/celery_monitor.py status          # 查看所有队列状态
    python scripts/celery_monitor.py queues          # 查看队列信息
    python scripts/celery_monitor.py workers         # 查看worker信息
    python scripts/celery_monitor.py stats           # 查看统计信息
    python scripts/celery_monitor.py scale pdf 3     # 调整PDF worker并发数
"""

import os
import sys
import subprocess
import json
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from celery import Celery
from archive_flow_manager.celery import app

class CeleryMonitor:
    """Celery监控和管理工具"""
    
    def __init__(self):
        self.app = app
        self.inspect = app.control.inspect()
        self.control = app.control
        
    def get_worker_status(self) -> Dict[str, Any]:
        """获取worker状态"""
        try:
            stats = self.inspect.stats()
            active = self.inspect.active()
            reserved = self.inspect.reserved()
            
            return {
                'stats': stats or {},
                'active': active or {},
                'reserved': reserved or {},
            }
        except Exception as e:
            print(f"获取worker状态失败: {e}")
            return {}
    
    def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息"""
        try:
            active_queues = self.inspect.active_queues()
            return active_queues or {}
        except Exception as e:
            print(f"获取队列信息失败: {e}")
            return {}
    
    def print_status(self):
        """打印整体状态"""
        print("=" * 60)
        print("Celery 队列状态监控")
        print("=" * 60)
        
        status = self.get_worker_status()
        
        if not status:
            print("❌ 无法连接到Celery workers")
            return
        
        # 打印worker统计
        print("\n📊 Worker 统计:")
        for worker, stats in status['stats'].items():
            if stats:
                print(f"  {worker}:")
                print(f"    - 进程数: {stats.get('pool', {}).get('max-concurrency', 'N/A')}")
                print(f"    - 已处理任务: {stats.get('total', {})}")
                print(f"    - 运行时间: {stats.get('clock', 'N/A')}")
        
        # 打印活跃任务
        print("\n🔄 活跃任务:")
        total_active = 0
        for worker, tasks in status['active'].items():
            if tasks:
                print(f"  {worker}: {len(tasks)} 个任务")
                for task in tasks[:3]:  # 只显示前3个任务
                    print(f"    - {task.get('name', 'Unknown')} ({task.get('id', 'No ID')[:8]}...)")
                if len(tasks) > 3:
                    print(f"    - ... 还有 {len(tasks) - 3} 个任务")
                total_active += len(tasks)
            else:
                print(f"  {worker}: 0 个任务")
        
        print(f"\n总活跃任务数: {total_active}")
        
        # 打印预留任务
        print("\n📋 预留任务:")
        total_reserved = 0
        for worker, tasks in status['reserved'].items():
            if tasks:
                print(f"  {worker}: {len(tasks)} 个任务")
                total_reserved += len(tasks)
            else:
                print(f"  {worker}: 0 个任务")
        
        print(f"总预留任务数: {total_reserved}")
    
    def print_queues(self):
        """打印队列信息"""
        print("=" * 60)
        print("队列信息")
        print("=" * 60)
        
        queues = self.get_queue_info()
        
        if not queues:
            print("❌ 无法获取队列信息")
            return
        
        for worker, worker_queues in queues.items():
            print(f"\n🔧 {worker}:")
            if worker_queues:
                for queue in worker_queues:
                    print(f"  - 队列: {queue.get('name', 'Unknown')}")
                    print(f"    交换机: {queue.get('exchange', {}).get('name', 'N/A')}")
                    print(f"    路由键: {queue.get('routing_key', 'N/A')}")
            else:
                print("  无队列信息")
    
    def print_workers(self):
        """打印worker详细信息"""
        print("=" * 60)
        print("Worker 详细信息")
        print("=" * 60)
        
        status = self.get_worker_status()
        
        if not status:
            print("❌ 无法连接到workers")
            return
        
        for worker, stats in status['stats'].items():
            if not stats:
                continue
                
            print(f"\n🔧 {worker}:")
            
            # 基本信息
            print(f"  状态: {'🟢 在线' if stats else '🔴 离线'}")
            print(f"  进程ID: {stats.get('pid', 'N/A')}")
            print(f"  并发数: {stats.get('pool', {}).get('max-concurrency', 'N/A')}")
            
            # 任务统计
            total_tasks = stats.get('total', {})
            if total_tasks:
                print(f"  任务统计:")
                for task_name, count in total_tasks.items():
                    if count > 0:
                        print(f"    - {task_name}: {count}")
            
            # 内存使用
            rusage = stats.get('rusage', {})
            if rusage:
                print(f"  资源使用:")
                print(f"    - 用户时间: {rusage.get('utime', 'N/A')}")
                print(f"    - 系统时间: {rusage.get('stime', 'N/A')}")
                print(f"    - 最大内存: {rusage.get('maxrss', 'N/A')}")
    
    def scale_worker(self, worker_type: str, concurrency: int):
        """调整worker并发数"""
        worker_mapping = {
            'pdf': 'pdf@',
            'default': 'default@'
        }
        
        if worker_type not in worker_mapping:
            print(f"❌ 未知的worker类型: {worker_type}")
            print(f"可用类型: {', '.join(worker_mapping.keys())}")
            return
        
        # 获取当前worker列表
        stats = self.inspect.stats()
        if not stats:
            print("❌ 无法连接到workers")
            return
        
        # 查找匹配的worker
        target_workers = [w for w in stats.keys() if worker_mapping[worker_type] in w]
        
        if not target_workers:
            print(f"❌ 未找到类型为 {worker_type} 的worker")
            return
        
        print(f"🔧 调整 {worker_type} worker并发数到 {concurrency}...")
        
        for worker in target_workers:
            try:
                # 获取当前并发数
                current_concurrency = stats[worker].get('pool', {}).get('max-concurrency', 0)
                
                if concurrency > current_concurrency:
                    # 增加并发数
                    grow_by = concurrency - current_concurrency
                    result = self.control.pool_grow(grow_by, destination=[worker])
                    print(f"  ✅ {worker}: 增加 {grow_by} 个进程")
                elif concurrency < current_concurrency:
                    # 减少并发数
                    shrink_by = current_concurrency - concurrency
                    result = self.control.pool_shrink(shrink_by, destination=[worker])
                    print(f"  ✅ {worker}: 减少 {shrink_by} 个进程")
                else:
                    print(f"  ℹ️ {worker}: 并发数已经是 {concurrency}")
                    
            except Exception as e:
                print(f"  ❌ {worker}: 调整失败 - {e}")

def main():
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)
    
    monitor = CeleryMonitor()
    command = sys.argv[1].lower()
    
    if command == 'status':
        monitor.print_status()
    elif command == 'queues':
        monitor.print_queues()
    elif command == 'workers':
        monitor.print_workers()
    elif command == 'stats':
        monitor.print_status()
        print("\n")
        monitor.print_workers()
    elif command == 'scale':
        if len(sys.argv) != 4:
            print("用法: python celery_monitor.py scale <worker_type> <concurrency>")
            print("示例: python celery_monitor.py scale pdf 3")
            sys.exit(1)
        
        worker_type = sys.argv[2]
        try:
            concurrency = int(sys.argv[3])
        except ValueError:
            print("❌ 并发数必须是整数")
            sys.exit(1)
        
        monitor.scale_worker(worker_type, concurrency)
    else:
        print(f"❌ 未知命令: {command}")
        print(__doc__)
        sys.exit(1)

if __name__ == '__main__':
    main()
