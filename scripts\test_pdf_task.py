#!/usr/bin/env python3
"""
测试PDF任务是否能正确路由到PDF worker

这个脚本用于手动触发PDF处理任务，验证任务路由是否正常工作。
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_flow_manager.celery import app
from archive_processing.models import ProcessingTask

def test_pdf_task_routing():
    """测试PDF任务路由"""
    print("=" * 60)
    print("PDF任务路由测试")
    print("=" * 60)
    
    # 检查是否有可用的PDF处理任务
    try:
        from archive_processing.tasks.core_tasks import process_pdf_serial_task
        print("✅ 成功导入 process_pdf_serial_task")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    # 检查任务路由配置
    task_name = 'archive_processing.tasks.core_tasks.process_pdf_serial_task'
    route = app.conf.task_routes.get(task_name)
    
    if route:
        queue = route.get('queue', 'default')
        print(f"✅ 任务路由配置正确: {task_name} -> {queue}")
    else:
        print(f"⚠️ 未找到任务路由配置: {task_name}")
        # 检查模式匹配
        for pattern, route_config in app.conf.task_routes.items():
            if '*' in pattern:
                pattern_prefix = pattern.replace('*', '')
                if task_name.startswith(pattern_prefix):
                    queue = route_config.get('queue', 'default')
                    print(f"✅ 通过模式匹配找到路由: {pattern} -> {queue}")
                    break
        else:
            print("❌ 未找到任何匹配的路由配置")
            return
    
    # 检查是否有处理任务记录
    processing_tasks = ProcessingTask.objects.filter(status__in=['pending', 'processing'])
    print(f"\n📋 当前待处理任务数量: {processing_tasks.count()}")
    
    if processing_tasks.exists():
        print("📝 待处理任务列表:")
        for task in processing_tasks[:5]:  # 只显示前5个
            print(f"  - ID: {task.id}, 状态: {task.status}, 文件: {task.pdf_path}")
            
        # 尝试手动触发一个任务
        test_task = processing_tasks.first()
        print(f"\n🚀 尝试手动触发任务 ID: {test_task.id}")
        
        try:
            # 异步调用任务
            result = process_pdf_serial_task.delay(str(test_task.id))
            print(f"✅ 任务已提交到队列")
            print(f"   任务ID: {result.id}")
            print(f"   状态: {result.status}")
            
            # 等待一小段时间查看结果
            import time
            time.sleep(2)
            
            print(f"   2秒后状态: {result.status}")
            
        except Exception as e:
            print(f"❌ 任务提交失败: {e}")
    else:
        print("⚠️ 没有找到待处理的任务")
        print("💡 提示: 请先上传PDF文件创建处理任务")
    
    print("\n" + "=" * 60)

def check_worker_connectivity():
    """检查worker连接性"""
    print("\n🔗 检查Worker连接性:")
    print("-" * 40)
    
    try:
        # 检查活跃的worker
        inspect = app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print("✅ 发现活跃的worker:")
            for worker_name, tasks in active_workers.items():
                print(f"  - {worker_name}: {len(tasks)} 个活跃任务")
        else:
            print("❌ 没有发现活跃的worker")
            
        # 检查队列信息
        active_queues = inspect.active_queues()
        if active_queues:
            print("\n📋 Worker队列信息:")
            for worker_name, queues in active_queues.items():
                print(f"  - {worker_name}:")
                for queue in queues:
                    print(f"    * 队列: {queue.get('name', 'Unknown')}")
        
        # 发送ping测试
        ping_result = app.control.ping(timeout=5)
        if ping_result:
            print(f"\n🏓 Ping测试成功: {len(ping_result)} 个worker响应")
            for result in ping_result:
                for worker, response in result.items():
                    print(f"  - {worker}: {response}")
        else:
            print("\n❌ Ping测试失败: 没有worker响应")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始PDF任务测试...\n")
    
    # 检查worker连接性
    check_worker_connectivity()
    
    # 测试PDF任务路由
    test_pdf_task_routing()
    
    print("\n💡 如果PDF worker没有收到任务，可能的原因:")
    print("1. 没有待处理的PDF任务记录")
    print("2. 任务路由配置问题")
    print("3. Worker没有正确监听pdf_processing队列")
    print("4. Redis连接问题")
    print("\n🔧 建议检查:")
    print("1. 查看ProcessingTask表中是否有pending状态的任务")
    print("2. 检查PDF上传流程是否正常创建任务")
    print("3. 查看worker日志: docker logs archive-flow-worker-pdf")

if __name__ == '__main__':
    main()
