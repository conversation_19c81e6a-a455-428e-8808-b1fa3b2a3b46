# 开发环境设置指南

## 🚀 快速开始

### Windows用户

直接使用默认配置即可：

```bash
docker-compose up -d
```

### Linux/Mac用户

为了避免文件权限问题，需要设置用户ID：

#### 方法1：使用环境变量

```bash
# 设置当前用户ID
export USER_ID=$(id -u)
export GROUP_ID=$(id -g)

# 启动服务
docker-compose up -d
```

#### 方法2：创建.env文件

在项目根目录创建`.env`文件：

```bash
# 获取并设置用户ID
echo "USER_ID=$(id -u)" > .env
echo "GROUP_ID=$(id -g)" >> .env

# 启动服务
docker-compose up -d
```

#### 方法3：一行命令启动

```bash
USER_ID=$(id -u) GROUP_ID=$(id -g) docker-compose up -d
```

## 🔧 权限问题排查

如果遇到权限问题：

1. **检查文件所有者**：

   ```bash
   ls -la media/ static/
   ```

2. **重新构建容器**：

   ```bash
   docker-compose down
   docker-compose build --no-cache
   USER_ID=$(id -u) GROUP_ID=$(id -g) docker-compose up -d
   ```

3. **进入容器检查**：

   ```bash
   docker-compose exec web bash
   whoami  # 应该显示 appuser
   id      # 检查用户和组ID
   ```

## 🐛 调试

### 进入容器调试

```bash
# 进入web容器
docker-compose exec web bash

# 进入worker容器
docker-compose exec worker bash

# 查看日志
docker-compose logs web
docker-compose logs worker
```

### 权限修复（临时解决方案）

如果权限问题严重，可以临时使用root用户：

```bash
docker-compose exec --user root web bash
chown -R appuser:appuser /app
```

## 📝 配置说明

- **默认用户ID**：1000（适用于大多数Linux发行版的第一个用户）
- **容器用户**：appuser
- **工作目录**：/app
- **挂载点**：整个项目目录挂载到容器的/app

## 🔄 重建镜像

当修改Dockerfile后，需要重建镜像：

```bash
docker-compose down
docker-compose build
docker-compose up -d
```
