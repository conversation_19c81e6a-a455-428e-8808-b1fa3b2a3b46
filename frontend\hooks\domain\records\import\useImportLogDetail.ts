import { useState, useEffect } from 'react';
import importHistoryService, { ImportHistoryDetailRecord } from '@/services/domain/records/import-history/import-history-service';
import { ImportReportData } from '@/components/records/import/excel-import-report-summary';

interface UseImportLogDetailReturn {
  reportData: ImportReportData | null;
  rawData: ImportHistoryDetailRecord | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * 专用于获取ImportLog详细数据的hook
 * 
 * @param importLogId - ImportLog的ID
 * @returns 包含报告数据、原始数据、加载状态等的对象
 */
export function useImportLogDetail(importLogId: string | null): UseImportLogDetailReturn {
  const [rawData, setRawData] = useState<ImportHistoryDetailRecord | null>(null);
  const [reportData, setReportData] = useState<ImportReportData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const convertToReportData = (details: ImportHistoryDetailRecord): ImportReportData => {
    let status: 'completed' | 'partial' | 'failed';
    
    const hasCompleteReportData = (details.overallTotalInitialRecords ?? 0) > 0;
    
    if (hasCompleteReportData) {
      status = (details.failedCount ?? 0) > 0 ? 'partial' : 'completed';
    } else {
      if (details.errorMessage && details.errorMessage.trim().length > 0) {
        status = 'failed';
      } else {
        status = 'partial';
      }
    }

    return {
      fileName: details.fileName,
      fileSize: details.fileSize,
      fileHash: details.fileHash,
      batchNumber: details.batchNumber,
      status: status,
      errorMessage: details.errorMessage || undefined,
      detailedReport: details.detailedReport,
      importDate: details.importDate,
      processingTime: details.processingTime,
      createdBy: details.createdBy,
      importUser: details.importUser,

      analysisTotalRowsRead: details.analysisTotalRowsRead,
      analysisSuccessfullyParsedRows: details.analysisSuccessfullyParsedRows,
      analysisFailedRows: details.analysisFailedRows,

      analysisFoundNewCount: details.analysisFoundNewCount,
      analysisFoundUpdateCount: details.analysisFoundUpdateCount,
      analysisSkippedIdentical: details.analysisSkippedIdentical,

      userDecisionSkippedUpdateCount: details.userDecisionSkippedUpdateCount,
      userDecisionConfirmedUpdateCount: details.userDecisionConfirmedUpdateCount,

      importTaskTotalRecordsSubmitted: details.importTaskTotalRecordsSubmitted,
      importTaskCreatedCount: details.importTaskCreatedCount,
      importTaskUpdatedCount: details.importTaskUpdatedCount,
      importTaskUnchangedCount: details.importTaskUnchangedCount,
      importTaskProcessedSuccessfullyCount: details.importTaskProcessedSuccessfullyCount,
      importTaskFailedCount: details.importTaskFailedCount,

      overallTotalInitialRecords: details.overallTotalInitialRecords,
      overallFinalCreatedCount: details.overallFinalCreatedCount,
      overallFinalUpdatedCount: details.overallFinalUpdatedCount,
      overallSkippedTotal: details.overallSkippedTotal,
      overallFailedTotal: details.overallFailedTotal,
      overallProcessedSuccessfullyTotal: details.overallProcessedSuccessfullyTotal,
      
      importLogId: details.id
    };
  };

  const fetchData = async () => {
    if (!importLogId) {
      setRawData(null);
      setReportData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const details = await importHistoryService.getImportHistoryRecord(importLogId);
      setRawData(details);
      setReportData(convertToReportData(details));
    } catch (err: any) {
      console.error('获取导入详情失败:', err);
      setError(err.message || '获取详情失败');
      setRawData(null);
      setReportData(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [importLogId]);

  return {
    reportData,
    rawData,
    isLoading,
    error,
    refetch: fetchData
  };
} 