---
description: 
globs: 
alwaysApply: false
---
# 文档导航指南

## 核心文档

在开始任何开发工作前，应先查阅以下核心文档：

1. **项目总体规划**
   - [项目愿景和路线图](mdc:AgentReadme/planning_and_requirements/project_vision_and_roadmap.md) - 了解项目的总体目标、方向和技术选型
   - [项目需求文档](mdc:AgentReadme/planning_and_requirements/project_requirements.md) - 详细的功能和非功能需求

2. **工作计划**
   - [详细工作计划和日志](mdc:AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md) - 查找当前任务、状态和相关上下文
   - [活跃功能计划](mdc:AgentReadme/active_feature_plans) - 正在开发的功能计划
   - [已完成功能计划](mdc:AgentReadme/completed_feature_plans) - 已完成的功能计划

3. **设计文档**
   - [设计文档目录](mdc:AgentReadme/design) - 系统架构、数据模型和API设计等

4. **功能映射**
   - [功能映射文档](mdc:AgentReadme/function_map) - 了解系统功能和代码位置的映射关系

## 操作日志

开发过程中需记录所有重要操作，并存储在以下位置：

- [AI操作日志](mdc:AgentReadme/ai_operation_logs) - AI辅助开发的操作记录
- [测试日志](mdc:AgentReadme/testing_logs) - 测试过程和结果记录

## 文档更新规范

1. **操作日志格式**
   - 每个操作日志必须包含：变更摘要、操作步骤、变更详情和验证结果
   - 日志文件名格式：`YYYYMMDD_HHMMSS_description.md`

2. **功能计划更新**
   - 完成功能后，将文档从`active_feature_plans`移至`completed_feature_plans`
   - 更新`detailed_work_plan_and_log.md`中的任务状态

## 文档创建命令

使用Python生成时间戳创建日志文件:

```python
import datetime
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"AgentReadme/ai_operation_logs/{timestamp}_description.md"
print(log_filename)
```

Windows命令行执行:

```bash
python -c "import datetime; print(f\"AgentReadme/ai_operation_logs/{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_description.md\")"
```

