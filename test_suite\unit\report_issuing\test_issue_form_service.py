"""
发放单服务测试模块

注意：为简化测试，用户权限处理已被简化为使用超级用户权限。
在实际部署时，应实现完整的权限检查机制。
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction

from archive_records.models import ArchiveRecord
from report_issuing.models import IssueForm, IssueFormItem, IssueRecord
from report_issuing.services.old_server import IssueFormService
from datetime import datetime, timedelta
from report_issuing.services.issue_form_service import IssueFormService, IssueFormError


class IssueFormServiceTest(TestCase):
    def setUp(self):
        """设置测试环境"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='issuer_test_user', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 将用户设置为超级用户以绕过权限检查
        self.user.is_superuser = True
        self.user.save()
        
        # 创建测试档案记录
        self.archive_records = []
        for i in range(5):
            record = ArchiveRecord.objects.create(
                sample_number=f'S{i+1:03d}',
                report_number=f'R{i+1:03d}',
                commission_number=f'C{i+1:03d}',
                project_name=f'项目{i+1}',
                client_unit=f'委托单位{i+1}',
                commission_datetime=timezone.now() - timedelta(days=30),
                archive_datetime=timezone.now() - timedelta(days=10),
                archive_status='archived',
                total_issue_copies=3
            )
            self.archive_records.append(record)
        
        # 初始化服务
        self.service = IssueFormService(user=self.user)
    
    def test_create_issue_form(self):
        """测试创建发放单"""
        data = {
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位',
            'receiver_phone': '12345678901',
            'notes': '测试备注'
        }
        
        issue_form = self.service.create_issue_form(data)
        
        # 验证发放单基本信息
        self.assertIsNotNone(issue_form)
        self.assertEqual(issue_form.status, 'draft')
        self.assertEqual(issue_form.issuer, self.user)
        self.assertEqual(issue_form.receiver_name, '测试领取人')
        self.assertEqual(issue_form.receiver_unit, '测试单位')
        
        # 验证编号格式
        today_str = timezone.now().strftime('%Y%m%d')
        self.assertTrue(issue_form.number.startswith(f'ISSUE-{today_str}-'))
        
        # 验证无条目
        self.assertEqual(issue_form.items.count(), 0)
    
    def test_add_form_items(self):
        """测试添加发放单条目"""
        # 先创建发放单
        data = {
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位',
            'receiver_phone': '12345678901'
        }
        issue_form = self.service.create_issue_form(data)
        
        # 添加条目 - 只传递ID列表
        added_items = self.service.add_form_items(issue_form.id, [
            self.archive_records[0].id, 
            self.archive_records[1].id
        ])
        
        # 为第二个条目设置专属领取人
        self.service.update_form_item(added_items[1].id, {'receiver_name': '专属领取人'})
        
        # 验证结果
        self.assertEqual(len(added_items), 2)
        self.assertEqual(issue_form.items.count(), 2)
        
        # 验证第一个条目
        item1 = issue_form.items.get(archive_record=self.archive_records[0])
        # 不验证具体份数，这由服务层根据issue_type和archive_record决定
        self.assertIsNone(item1.receiver_name)
        
        # 验证第二个条目
        item2 = issue_form.items.get(archive_record=self.archive_records[1])
        self.assertEqual(item2.receiver_name, '专属领取人')
    
    def test_add_form_items_with_different_formats(self):
        """测试使用不同格式添加发放单条目"""
        # 创建发放单
        issue_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        
        # 测试使用ID列表格式
        id_list_items = self.service.add_form_items(issue_form.id, [self.archive_records[0].id])
        self.assertEqual(len(id_list_items), 1)
        self.assertIsNone(id_list_items[0].receiver_name)
        
        # 测试使用字典列表格式
        dict_list_items = self.service.add_form_items(issue_form.id, [
            {'archive_record': self.archive_records[1].id, 'receiver_name': '专属领取人'}
        ])
        self.assertEqual(len(dict_list_items), 1)
        self.assertEqual(dict_list_items[0].receiver_name, '专属领取人')
        
        # 验证份数始终由系统计算（对于single类型应该是1）
        self.assertEqual(id_list_items[0].copies, 1)
        self.assertEqual(dict_list_items[0].copies, 1)
        
        # 验证数据库中添加的条目总数
        self.assertEqual(issue_form.items.count(), 2)
    
    def test_add_form_items_error_handling(self):
        """测试添加发放单条目的错误处理"""
        # 创建发放单
        issue_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        
        # 测试无效的档案记录ID
        items = self.service.add_form_items(issue_form.id, [999999])  # 不存在的ID
        self.assertEqual(len(items), 0)  # 应该没有成功添加的条目
        
        # 测试无效的发放单ID
        with self.assertRaises(IssueFormError):
            self.service.add_form_items(999999, [self.archive_records[0].id])
    
    def test_remove_form_items(self):
        """测试移除发放单条目"""
        # 先创建发放单和条目
        issue_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        
        # 添加条目 - 只传递ID列表
        added_items = self.service.add_form_items(issue_form.id, [
            self.archive_records[0].id,
            self.archive_records[1].id,
            self.archive_records[2].id
        ])
        
        # 移除部分条目
        item_ids_to_remove = [added_items[0].id, added_items[2].id]
        removed_count = self.service.remove_form_items(issue_form.id, item_ids_to_remove)
        
        # 验证结果
        self.assertEqual(removed_count, 2)
        self.assertEqual(issue_form.items.count(), 1)
        remaining_item = issue_form.items.first()
        self.assertEqual(remaining_item.archive_record, self.archive_records[1])
    
    def test_status_transitions(self):
        """测试状态转换"""
        # 准备阶段
        issue_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        self.service.add_form_items(issue_form.id, [self.archive_records[0].id])
        
        # 验证初始状态
        self.assertEqual(issue_form.status, 'draft')
        
        # 测试状态转换：draft -> locked
        locked_form = self.service.lock_issue_form(issue_form.id)
        self.assertEqual(locked_form.status, 'locked')
        
# CHANGE: [2025-06-07] 更新测试用例以适应简化的生命周期状态
        # 测试状态转换：locked -> draft (通过解锁)
        unlocked_form = self.service.unlock_issue_form(issue_form.id)
        self.assertEqual(unlocked_form.status, 'draft')
        
        # 再次进行状态转换以完成发放
        self.service.lock_issue_form(issue_form.id)
        issued_form = self.service.issue_form(issue_form.id)
        self.assertEqual(issued_form.status, 'issued')
        
        # 验证发放记录
        records = IssueRecord.objects.filter(
            archive_record=self.archive_records[0],
            issue_type='first',
            source='issue_form',
            issue_form=issue_form
        )
        self.assertEqual(records.count(), 1)
        record = records.first()
        self.assertTrue(record.is_active)
        self.assertFalse(record.is_deleted)
        
        # 验证档案记录更新
        archive_record = self.archive_records[0]
        archive_record.refresh_from_db()  # 刷新对象以获取最新状态
        
        # 验证第一次发放信息
        self.assertEqual(archive_record.first_issue_copies, 1)
        self.assertIsNotNone(archive_record.first_issue_datetime)
        self.assertEqual(archive_record.first_receiver_name, '测试领取人')
        
        # 验证总份数不变
        self.assertEqual(archive_record.total_issue_copies, 3)
    
    def test_delete_issue_form(self):
        """测试删除发放单"""
        # 测试删除未归档发放单
        draft_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        
        # 添加一个条目 - 只传递ID列表
        self.service.add_form_items(draft_form.id, [self.archive_records[0].id])
        
        # 删除未归档发放单
        self.service.delete_issue_form(draft_form.id, "测试删除")
        # 验证物理删除
        self.assertEqual(IssueForm.objects.filter(id=draft_form.id).count(), 0)
        
        # 测试删除已归档发放单
        archived_form = self.service.create_issue_form({
            'issue_date': timezone.now(),
            'receiver_name': '测试领取人',
            'receiver_unit': '测试单位'
        })
        
        # 添加条目并归档 - 只传递ID列表
        self.service.add_form_items(archived_form.id, [self.archive_records[1].id])
        
# CHANGE: [2025-06-07] 更新状态转换为简化的生命周期
        self.service.lock_issue_form(archived_form.id)
        self.service.issue_form(archived_form.id)
        
        # 验证生成了发放记录
        original_record = IssueRecord.objects.get(
            issue_form=archived_form,
            archive_record=self.archive_records[1]
        )
        
        # 删除已归档发放单
        self.service.delete_issue_form(archived_form.id, "测试删除已归档发放单")
        
# CHANGE: [2025-06-07] 更新删除验证逻辑，适应简化生命周期
        # 验证软删除
        updated_form = IssueForm.objects.get(id=archived_form.id)
        self.assertTrue(updated_form.is_deleted)
        self.assertEqual(updated_form.status, 'issued')  # 状态保持为issued，仅标记删除
        self.assertEqual(updated_form.deletion_reason, "测试删除已归档发放单")
        self.assertEqual(updated_form.deleted_by, self.user)
        
        # 验证关联记录的状态变更
        record = IssueRecord.objects.get(id=original_record.id)
        self.assertTrue(record.is_deleted)
        self.assertFalse(record.is_active)
        self.assertIsNone(record.issue_form)
    
    def test_get_available_archive_records(self):
        """测试获取可用于发放的档案记录"""
        # 获取所有可用记录（统一接口）
        available_records = self.service.get_available_archive_records()
        self.assertEqual(available_records.count(), 5)  # 所有档案都可用于发放
        
        # 为一个档案记录创建第一次发放记录
        IssueRecord.objects.create(
            archive_record=self.archive_records[0],
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False
        )
        
        # 检查第二次发放是否可用（应该还能进行第二次发放）
        available_records = self.service.get_available_archive_records()
        self.assertEqual(available_records.count(), 5)  # 所有档案仍可用（可以进行二次发放）
        
        # 为一个档案记录创建第二次发放记录
        IssueRecord.objects.create(
            archive_record=self.archive_records[1],
            issue_type='second',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False
        )
        
        # 再次查询可用记录，应排除已完成发放的记录（有第二次发放的记录）
        available_records = self.service.get_available_archive_records()
        self.assertEqual(available_records.count(), 4)
        self.assertNotIn(self.archive_records[1], available_records)

    def test_check_archive_records_status_first(self):
        """测试检查档案记录状态 - 首发"""
        # 测试首发状态检查
        archive_ids = [record.id for record in self.archive_records[:3]]
        results = self.service.check_archive_records_status(
            archive_ids, is_first=True, is_second=False
        )
        
        # 验证所有记录都可首发
        for record_id in archive_ids:
            self.assertIn(record_id, results)
            can_issue, reason = results[record_id]
            self.assertTrue(can_issue)
            self.assertEqual(reason, "可发放")
        
        # 为一个档案创建首发记录
        IssueRecord.objects.create(
            archive_record=self.archive_records[0],
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False
        )
        
        # 再次检查首发状态
        results = self.service.check_archive_records_status(
            archive_ids, is_first=True, is_second=False
        )
        
        # 验证已首发的记录不能再次首发
        can_issue, reason = results[self.archive_records[0].id]
        self.assertFalse(can_issue)
        self.assertIn("已有活跃的首发记录", reason)
        
        # 其他记录仍可首发
        for i in [1, 2]:
            can_issue, reason = results[self.archive_records[i].id]
            self.assertTrue(can_issue)

    def test_check_archive_records_status_second(self):
        """测试检查档案记录状态 - 二次发放"""
        # 测试二次发放状态检查（没有首发时应失败）
        archive_ids = [record.id for record in self.archive_records[:2]]
        results = self.service.check_archive_records_status(
            archive_ids, is_first=False, is_second=True
        )
        
        # 验证没有首发的记录不能二次发放
        for record_id in archive_ids:
            can_issue, reason = results[record_id]
            self.assertFalse(can_issue)
            self.assertIn("尚未首发", reason)
        
        # 为第一个档案创建首发记录
        IssueRecord.objects.create(
            archive_record=self.archive_records[0],
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试单位',
            receiver_phone='12345678901',
            source='manual_create',
            is_active=True,
            is_deleted=False
        )
        
        # 再次检查二次发放状态
        results = self.service.check_archive_records_status(
            archive_ids, is_first=False, is_second=True
        )
        
        # 验证有首发的记录可以二次发放
        can_issue, reason = results[self.archive_records[0].id]
        self.assertTrue(can_issue)
        self.assertEqual(reason, "可发放")
        
        # 没有首发的记录仍不能二次发放
        can_issue, reason = results[self.archive_records[1].id]
        self.assertFalse(can_issue)
        self.assertIn("尚未首发", reason)
