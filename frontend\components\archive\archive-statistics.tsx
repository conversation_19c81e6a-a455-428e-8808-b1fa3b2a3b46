"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ArrowUpRight, ArrowDownRight } from "lucide-react"

interface ArchiveStatisticsProps {
  type: "by-archive" | "by-box" | "by-user"
  timeFrame?: "day" | "week" | "month"
}

export function ArchiveStatistics({ type, timeFrame = "day" }: ArchiveStatisticsProps) {
  // 获取时间范围标题
  const getTimeFrameTitle = () => {
    switch (timeFrame) {
      case "day":
        return "今日"
      case "week":
        return "本周"
      case "month":
        return "本月"
      default:
        return ""
    }
  }

  // 简化的模拟数据渲染
  if (type === "by-archive") {
    const archiveData = [
      { id: "DOC-2023-001", name: "技术规范文档", count: 12, trend: "up", percentage: 18 },
      { id: "DOC-2023-002", name: "会议纪要", count: 8, trend: "up", percentage: 13 },
    ]

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>档案编号</TableHead>
            <TableHead>档案名称</TableHead>
            <TableHead>{getTimeFrameTitle()}归档数量</TableHead>
            <TableHead>变化趋势</TableHead>
            <TableHead>占比</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {archiveData.map((item) => (
            <TableRow key={item.id}>
              <TableCell className="font-medium">{item.id}</TableCell>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.count}</TableCell>
              <TableCell>
                {item.trend === "up" ? (
                  <Badge className="bg-green-100 text-green-800">
                    <ArrowUpRight className="mr-1 h-3 w-3" />
                    上升
                  </Badge>
                ) : (
                  <Badge className="bg-red-100 text-red-800">
                    <ArrowDownRight className="mr-1 h-3 w-3" />
                    下降
                  </Badge>
                )}
              </TableCell>
              <TableCell>{item.percentage}%</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )
  }

  if (type === "by-box") {
    const boxData = [
      { id: "ARC-2023-001", count: 8, capacity: 200, usage: 78, trend: "up", percentage: 15 },
      { id: "ARC-2023-002", count: 7, capacity: 150, usage: 95, trend: "up", percentage: 12 },
    ]

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>档案盒号</TableHead>
            <TableHead>{getTimeFrameTitle()}归档文件数</TableHead>
            <TableHead>容量</TableHead>
            <TableHead>使用率</TableHead>
            <TableHead>变化趋势</TableHead>
            <TableHead>占比</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {boxData.map((item) => (
            <TableRow key={item.id}>
              <TableCell className="font-medium">{item.id}</TableCell>
              <TableCell>{item.count}</TableCell>
              <TableCell>{item.capacity}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <div className="w-24 h-2 bg-gray-200 rounded-full mr-2">
                    <div
                      className={`h-full rounded-full ${
                        item.usage > 90 ? "bg-red-500" : item.usage > 70 ? "bg-yellow-500" : "bg-green-500"
                      }`}
                      style={{ width: `${item.usage}%` }}
                    ></div>
                  </div>
                  <span className="text-xs">{item.usage}%</span>
                </div>
              </TableCell>
              <TableCell>
                {item.trend === "up" ? (
                  <Badge className="bg-green-100 text-green-800">
                    <ArrowUpRight className="mr-1 h-3 w-3" />
                    上升
                  </Badge>
                ) : (
                  <Badge className="bg-red-100 text-red-800">
                    <ArrowDownRight className="mr-1 h-3 w-3" />
                    下降
                  </Badge>
                )}
              </TableCell>
              <TableCell>{item.percentage}%</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )
  }

  // by-user
  const userData = [
    { id: "USER001", name: "张三", department: "技术部", count: 6, trend: "up", percentage: 12 },
    { id: "USER002", name: "李四", department: "财务部", count: 5, trend: "down", percentage: 9 },
  ]

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>用户ID</TableHead>
          <TableHead>用户名</TableHead>
          <TableHead>部门</TableHead>
          <TableHead>{getTimeFrameTitle()}归档数量</TableHead>
          <TableHead>变化趋势</TableHead>
          <TableHead>占比</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {userData.map((item) => (
          <TableRow key={item.id}>
            <TableCell className="font-medium">{item.id}</TableCell>
            <TableCell>{item.name}</TableCell>
            <TableCell>{item.department}</TableCell>
            <TableCell>{item.count}</TableCell>
            <TableCell>
              {item.trend === "up" ? (
                <Badge className="bg-green-100 text-green-800">
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                  上升
                </Badge>
              ) : (
                <Badge className="bg-red-100 text-red-800">
                  <ArrowDownRight className="mr-1 h-3 w-3" />
                  下降
                </Badge>
              )}
            </TableCell>
            <TableCell>{item.percentage}%</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
