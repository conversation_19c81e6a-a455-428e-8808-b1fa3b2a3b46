# Nginx配置示例 - Docker开发环境
# 此配置文件用于docker-compose中的nginx服务

server {
    listen 80;
    server_name 127.0.0.1;
    
    # 专用于Next.js图片优化的location块
    # 这个块必须放在'location /'之前
    location /_next/image {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # 允许Next.js处理缓存
        proxy_cache_bypass 1;
        proxy_no_cache 1;
    }
    
    # 前端应用
    location / {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Django认证API - 使用新的路径前缀避免与NextAuth冲突
    # 这个块必须在 /api/auth/ 块之前，以确保优先匹配
    location /api/django-auth/ {
        # 重写路径：/api/django-auth/* -> /api/auth/*
        rewrite ^/api/django-auth/(.*) /api/auth/$1 break;
        proxy_pass http://web:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 允许较大的上传文件（PDF等文档）
        client_max_body_size 500M;
    }
    
    # NextAuth.js 的内部API路由 - 统一处理所有 /api/auth/* 路径
    # 这个块必须在下面的 /api/ 块之前，以确保优先匹配
    location /api/auth/ {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 后端API请求代理
    location /api/ {
        # CHANGE: [2025-05-10] 移除路径重写规则，保留/api前缀
        # 不再去掉/api前缀，直接转发原始路径到后端
        # 这样可以保持前端、Nginx和后端路径的一致性
        # rewrite ^/api/(.*) /$1 break;
        
        proxy_pass http://web:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 允许较大的上传文件（PDF等文档）
        client_max_body_size 500M;
    }

    # 静态文件
    location /static/ {
        proxy_pass http://web:8000/static/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # 媒体文件
    location /media/ {
        proxy_pass http://web:8000/media/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }
    
    # 健康检查接口
    location /health {
        access_log off;
        return 200 "ok";
    }
}

# 生产环境配置示例 - 取消注释后可用于生产部署
# server {
#     listen 80;
#     server_name your-domain.com;
#     
#     # HTTP跳转到HTTPS
#     return 301 https://$host$request_uri;
# }
# 
# server {
#     listen 443 ssl;
#     server_name your-domain.com;
#     
#     # SSL配置
#     ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
#     
#     # 安全设置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_prefer_server_ciphers on;
#     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # 前端应用 - 静态文件服务
#     location / {
#         root /var/www/html/frontend;
#         index index.html;
#         try_files $uri $uri/ /index.html;
#         
#         # 安全头
#         add_header X-Frame-Options "SAMEORIGIN";
#         add_header X-XSS-Protection "1; mode=block";
#         add_header X-Content-Type-Options "nosniff";
#         
#         # 缓存控制
#         location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
#             expires 30d;
#             add_header Cache-Control "public, max-age=2592000";
#         }
#     }
#     
#     # API请求代理
#     location /api/ {
#         # CHANGE: [2025-05-10] 移除路径重写规则，保留/api前缀
#         # 不再去掉/api前缀，直接转发原始路径到后端
#         # 这样可以保持前端、Nginx和后端路径的一致性
#         # rewrite ^/api/(.*) /$1 break;
#         
#         proxy_pass http://localhost:8000;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         
#         # 超时设置
#         proxy_connect_timeout 60s;
#         proxy_send_timeout 60s;
#         proxy_read_timeout 60s;
#         
#         # 允许较大的上传文件
#         client_max_body_size 20M;
#     }
#     
#     # 静态文件
#     location /static/ {
#         alias /var/www/html/staticfiles/;
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
#     
#     # 媒体文件
#     location /media/ {
#         alias /var/www/html/media/;
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
# } 