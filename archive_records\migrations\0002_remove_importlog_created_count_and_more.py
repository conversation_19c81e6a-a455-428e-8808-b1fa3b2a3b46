# Generated by Django 5.1.8 on 2025-05-19 16:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='importlog',
            name='created_count',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='failed_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='processed_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='success_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='system_skipped_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='total_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='total_skipped_records',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='unchanged_count',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='updated_count',
        ),
        migrations.RemoveField(
            model_name='importlog',
            name='user_manual_skipped_records',
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_failed_rows',
            field=models.IntegerField(default=0, verbose_name='分析阶段-识别错误的行数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_found_new_count',
            field=models.IntegerField(default=0, verbose_name='分析阶段-识别为新记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_found_update_count',
            field=models.IntegerField(default=0, verbose_name='分析阶段-识别可更新记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_skipped_identical',
            field=models.IntegerField(default=0, verbose_name='分析阶段-识别为内容相同的记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_successfully_parsed_rows',
            field=models.IntegerField(default=0, verbose_name='分析阶段-成功解析的行数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='analysis_total_rows_read',
            field=models.IntegerField(default=0, verbose_name='分析阶段-读取Excel有效行数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_created_count',
            field=models.IntegerField(default=0, verbose_name='导入Task-成功创建记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_failed_count',
            field=models.IntegerField(default=0, verbose_name='导入Task-处理失败记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_processed_successfully_count',
            field=models.IntegerField(default=0, verbose_name='导入Task-处理成功总计数 (创建+更新+内容相同)'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_total_records_submitted',
            field=models.IntegerField(default=0, verbose_name='导入Task-提交处理总记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_unchanged_count',
            field=models.IntegerField(default=0, verbose_name='导入Task-执行时发现与DB内容相同数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='import_task_updated_count',
            field=models.IntegerField(default=0, verbose_name='导入Task-成功更新记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_failed_total',
            field=models.IntegerField(default=0, verbose_name='总计-最终失败记录数 (分析阶段失败数 + 导入Task阶段失败数)'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_final_created_count',
            field=models.IntegerField(default=0, verbose_name='总览-最终实际创建记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_final_updated_count',
            field=models.IntegerField(default=0, verbose_name='总览-最终实际更新记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_processed_successfully_total',
            field=models.IntegerField(default=0, verbose_name='总计-最终成功处理记录数 (导入Task成功处理数 + 用户决策跳过数 + 分析阶段Identical数)'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_skipped_by_system_total',
            field=models.IntegerField(default=0, verbose_name='总计-系统跳过总数 (分析Identical + Task Unchanged)'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_skipped_total',
            field=models.IntegerField(default=0, verbose_name='总计-全部跳过总数 (系统总跳过 + 用户决策跳过)'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_total_initial_records',
            field=models.IntegerField(default=0, verbose_name='总览-最初Excel有效总行数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='overall_user_decision_skipped_updates',
            field=models.IntegerField(default=0, verbose_name='总览-用户决策跳过更新数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='user_decision_confirmed_update_count',
            field=models.IntegerField(default=0, verbose_name='用户决策-确认更新记录数'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='user_decision_skipped_update_count',
            field=models.IntegerField(default=0, verbose_name='用户决策-跳过更新记录数'),
        ),
        migrations.AlterField(
            model_name='importsession',
            name='status',
            field=models.CharField(choices=[('select', '选择文件'), ('upload', '文件上传'), ('analysis_start', '分析开始'), ('analyzing', '分析中'), ('analyzed', '分析完成'), ('processing', '冲突处理'), ('queued', '排队等待导入'), ('import_start', '导入开始'), ('importing', '导入中'), ('imported', '导入完成'), ('cancelled', '已取消'), ('error', '出错')], default='select', max_length=20),
        ),
    ]
