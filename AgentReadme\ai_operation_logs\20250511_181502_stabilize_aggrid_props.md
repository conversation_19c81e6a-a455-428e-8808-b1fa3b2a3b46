# Operation Document: Stabilize AG Grid Props to Prevent Unnecessary Refresh

## 📋 Change Summary

**Purpose**: To prevent the AG Grid table from refreshing data when parent component UI state (like the date picker popover's visibility) changes. This is caused by AG Grid detecting new object references for some of its props on each re-render.
**Scope**: `frontend/app/records/ledger/page.tsx`
**Associated**: User-reported issue of table refreshing when date picker button is clicked.

## 🔧 Operation Steps

### 📊 OP-001: Analyze AG Grid Logs and Prop Definitions

**Precondition**: User logs show "Updated property serverSideDatasource" and "Updated property sideBar" when the date picker popover is toggled.
**Operation**: Reviewed how `sideBar` and `serverSideDatasource` props are passed to `<AgGridReact />`. Identified that:

- `sideBar` was an object literal, creating a new reference on each render.
- `serverSideDatasource` was being assigned the result of `createServerSideDatasource()`, which, even if `createServerSideDatasource` is memoized via `useCallback`, its *invocation* in the render path creates a new datasource object instance each time.
**Postcondition**: Confirmed that unstable prop references were the cause of AG Grid detecting updates.

### ✏️ OP-002: Memoize `sideBar` Prop

**Precondition**: `sideBar` prop is an inline object literal.
**Operation**: Extracted the `sideBar` configuration into a `useMemo` hook with an empty dependency array `[]` to ensure its reference remains stable across re-renders.
**Postcondition**: The `sideBar` prop now has a stable reference.

### ✏️ OP-003: Memoize `serverSideDatasource` Prop Object

**Precondition**: `serverSideDatasource` prop was assigned `createServerSideDatasource()`, which returns a new object instance on each call during render.
**Operation**: Wrapped the call to `createServerSideDatasource()` in a `useMemo` hook. This memoizes the *datasource object itself*, ensuring a stable reference is passed to the `<AgGridReact />` prop. The dependencies for this `useMemo` include `createServerSideDatasource` (which itself is a `useCallback` with its own dependencies).
**Postcondition**: The `serverSideDatasource` prop now receives a stable object reference.

### ✏️ OP-004: Correct Typo in `defaultColDef` Prop

**Precondition**: A typo `defaultColColDef` existed.
**Operation**: Corrected the typo to `defaultColDef`.
**Postcondition**: Typo fixed.

## 📝 Change Details

### CH-001: Memoize `sideBar` Configuration

**File**: `frontend/app/records/ledger/page.tsx`
**Before**:

```typescript
// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            sideBar={{
              toolPanels: [
                // ...
              ],
              defaultToolPanel: 'columns'
            }}
            // ...
          />
// ...
```

**After**:

```typescript
// ...
  const sideBarConfig = useMemo(() => ({
    toolPanels: [
      {
        id: 'columns',
        labelDefault: '列管理',
        // ... (rest of sideBar config)
      }
    ],
    defaultToolPanel: 'columns'
  }), []);

// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            sideBar={sideBarConfig} // 使用 memoized 的配置
            // ...
          />
// ...
```

**Rationale**: Prevents AG Grid from detecting a change in the `sideBar` prop due to a new object reference on each render of the parent component.
**Potential Impact**: None negative. Improves performance by reducing unnecessary processing by AG Grid.

### CH-002: Memoize Server-Side Datasource Object

**File**: `frontend/app/records/ledger/page.tsx`
**Before**:

```typescript
// ...
  const createServerSideDatasource = useCallback((): IServerSideDatasource => {
    return {
      getRows: async (params: IServerSideGetRowsParams) => { /* ... */ }
    };
  }, [fetchArchiveLedgerRecords]);

// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            serverSideDatasource={createServerSideDatasource()} // Creates new object on each render
            // ...
          />
// ...
```

**After**:

```typescript
// ...
  const createServerSideDatasource = useCallback((): IServerSideDatasource => {
    return {
      getRows: async (params: IServerSideGetRowsParams) => { /* ... */ }
    };
  }, [fetchArchiveLedgerRecords]);

  const memoizedServerSideDatasource = useMemo(() => createServerSideDatasource(), [createServerSideDatasource]);

// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            serverSideDatasource={memoizedServerSideDatasource} // 使用 memoized 的数据源对象
            // ...
          />
// ...
```

**Rationale**: Prevents AG Grid from detecting a change in the `serverSideDatasource` prop. Each call to `createServerSideDatasource()` returns a new object. Memoizing the result ensures a stable reference is passed to the grid, as long as `createServerSideDatasource` itself (and its dependencies) are stable.
**Potential Impact**: None negative. This is crucial for preventing AG Grid's SSRM from re-triggering `getRows` due to perceived prop changes.

### CH-003: Correct Typo in `defaultColDef`

**File**: `frontend/app/records/ledger/page.tsx`
**Before**:

```typescript
// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            defaultColDef={defaultColColDef}
            // ...
          />
// ...
```

**After**:

```typescript
// ...
          <AgGridReact<ArchiveRecordData>
            // ...
            defaultColDef={defaultColDef}
            // ...
          />
// ...
```

**Rationale**: Simple typo fix.
**Potential Impact**: Prevents potential runtime error if `defaultColColDef` was undefined.

## ✅ Verification Results

**Method**: Manual testing after changes.

1. Click the date range picker button to open/close the popover. Check browser console for AG Grid logs about updated properties (`serverSideDatasource`, `sideBar`). Check if network requests for data are made.
2. Confirm data filtering via the date picker's "Apply" button still works correctly.
**Expected Results**:

- AG Grid should no longer log updates for `serverSideDatasource` or `sideBar` when the date picker popover is toggled.
- No data refresh (i.e., no call to `getRows` in the datasource) should occur when merely toggling the date picker popover.
- Applying a date filter via the popover's "Apply" button should still trigger a data refresh correctly.
**Problems**: None anticipated. These changes directly address common causes of re-renders and prop instability in React applications using AG Grid.
**Solutions**: Not applicable.
