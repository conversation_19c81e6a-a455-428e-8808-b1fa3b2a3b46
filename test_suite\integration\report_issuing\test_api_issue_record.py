"""
发放记录API测试模块

注意：为简化测试，用户权限处理已被简化为使用超级用户权限。
在实际部署时，应实现完整的权限检查机制。
CHANGE: [2025-06-07] 更新注释说明：设置 status='issued' 是为了方便测试与已发放状态相关的功能，实际应用中发放单应从 draft 开始。
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta

from archive_records.models import ArchiveRecord
from report_issuing.models import IssueForm, IssueRecord, IssueRecordHistory


class IssueRecordAPITest(TestCase):
    def setUp(self):
        """设置测试环境"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='api_record_user', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 将用户设置为超级用户以绕过权限检查
        self.user.is_superuser = True
        self.user.save()
        
        # 设置API客户端
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 创建测试档案记录
        self.archive_record = ArchiveRecord.objects.create(
            sample_number='API-REC001',
            report_number='API-REC-R001',
            commission_number='API-REC-C001',
            project_name='API记录测试项目',
            client_unit='API记录测试单位',
            commission_datetime=timezone.now(),
            archive_status='archived'
        )
        
        # 创建测试发放单
        self.issue_form = IssueForm.objects.create(
            number='ISSUE-REC-20230101-001',
            issue_date=timezone.now(),
            receiver_name='API记录测试领取人',
            receiver_unit='API记录测试单位',
            receiver_phone='12345678901',
# CHANGE: [2025-06-07] 更新测试数据状态为简化生命周期的已发放状态
            status='issued',
            issuer=self.user
        )
        
        # 创建测试发放记录
        self.issue_record = IssueRecord.objects.create(
            archive_record=self.archive_record,
            issue_type='first',
            issue_date=timezone.now(),
            issuer=self.user,
            receiver_name='测试领取人',
            receiver_unit='测试领取单位',
            receiver_phone='12345678901',
            source='issue_form',
            issue_form=self.issue_form,
            is_active=True,
            is_deleted=False,
            created_by=self.user,
            notes='测试备注'
        )
    
    def test_list_issue_records(self):
        """测试获取发放记录列表"""
        url = reverse('issuerecord-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['receiver_name'], '测试领取人')
    
    def test_create_issue_record(self):
        """测试创建发放记录"""
        url = reverse('issuerecord-list')
        
        # 创建另一个档案记录用于测试
        new_record = ArchiveRecord.objects.create(
            sample_number='API-REC002',
            report_number='API-REC-R002',
            commission_number='API-REC-C002',
            project_name='新API记录测试项目',
            archive_status='archived'
        )
        
        data = {
            'archive_record': new_record.id,
            'issue_type': 'first',
            'issue_date': timezone.now().isoformat(),
            'receiver_name': 'API创建领取人',
            'receiver_unit': 'API创建单位',
            'receiver_phone': '98765432101',
            'notes': 'API创建备注'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['issue_type'], 'first')
        self.assertEqual(response.data['receiver_name'], 'API创建领取人')
        self.assertEqual(response.data['receiver_unit'], 'API创建单位')
        self.assertEqual(response.data['source'], 'manual_create')
        self.assertTrue(response.data['is_active'])
        
        # 验证数据库中已创建
        self.assertEqual(IssueRecord.objects.count(), 2)
    
    def test_update_issue_record(self):
        """测试更新发放记录"""
        url = reverse('issuerecord-detail', args=[self.issue_record.id])
        
        data = {
            'receiver_name': '更新后的领取人',
            'receiver_unit': '更新后的单位',
            'notes': '更新后的备注',
            'reason': '测试更新原因'
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['receiver_name'], '更新后的领取人')
        self.assertEqual(response.data['receiver_unit'], '更新后的单位')
        self.assertEqual(response.data['notes'], '更新后的备注')
        
        # 验证原记录变为非活跃，且创建了新记录
        self.assertEqual(IssueRecord.objects.count(), 2)
        original_record = IssueRecord.objects.get(id=self.issue_record.id)
        self.assertFalse(original_record.is_active)
        
        new_record = IssueRecord.objects.exclude(id=self.issue_record.id).first()
        self.assertTrue(new_record.is_active)
        self.assertEqual(new_record.receiver_name, '更新后的领取人')
        self.assertEqual(new_record.receiver_unit, '更新后的单位')
        self.assertEqual(new_record.source, 'manual_update')
    
    def test_delete_record(self):
        """测试删除发放记录"""
        url = reverse('issuerecord-delete-record', args=[self.issue_record.id])
        
        data = {'reason': '测试删除原因'}
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        # 验证记录被标记为删除
        self.issue_record.refresh_from_db()
        self.assertTrue(self.issue_record.is_deleted)
        self.assertFalse(self.issue_record.is_active)
        
        # 验证创建了历史记录
        history = IssueRecordHistory.objects.filter(
            issue_record=self.issue_record,
            operation_type='delete'
        ).first()
        self.assertIsNotNone(history)
        self.assertEqual(history.operation_reason, '测试删除原因')
    
    def test_get_history(self):
        """测试获取发放记录历史"""
        # 先创建一些历史记录
        IssueRecordHistory.objects.create(
            issue_record=self.issue_record,
            operation_type='create',
            after_data={'receiver_name': '测试领取人'},
            operator=self.user,
            operation_reason='测试创建'
        )
        
        url = reverse('issuerecord-history', args=[self.issue_record.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['operation_type'], 'create')
        self.assertEqual(response.data[0]['operation_reason'], '测试创建')
    
    def test_archive_status(self):
        """测试获取档案记录的发放状态"""
        url = reverse('issuerecord-archive-status')
        response = self.client.get(url, {'archive_id': self.archive_record.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['has_first_issue'])
        self.assertFalse(response.data['has_second_issue'])
        self.assertFalse(response.data['can_issue_first'])
        self.assertTrue(response.data['can_issue_second'])
        
        # 如果测试代码检查了first_issue或second_issue字段，需要修改为检查first_issue_id和second_issue_id
        # 例如，如果有类似这样的断言:
        # self.assertIsNotNone(response.data['first_issue'])
        # 需要修改为:
        # self.assertIsNotNone(response.data['first_issue_id'])
