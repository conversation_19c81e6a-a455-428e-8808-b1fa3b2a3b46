"use client"

import { useEffect, useRef, useCallback } from 'react'

export interface UploadProtectionOptions {
  /**
   * 是否启用页面离开保护
   */
  enabled: boolean
  
  /**
   * 是否正在进行文件上传或处理
   */
  isUploading: boolean
  
  /**
   * 是否正在进行关键操作（如分析、确认等）
   */
  isCriticalOperation?: boolean
  
  /**
   * 自定义提示消息
   */
  message?: string
  
  /**
   * 当用户尝试离开时的回调函数
   */
  onBeforeUnload?: () => void
}

/**
 * 文件上传页面离开保护Hook
 * 
 * 用于在文件上传、分析或其他关键操作期间防止用户意外离开页面
 * 
 * @example
 * ```tsx
 * const { setUploadProtection } = useUploadProtection({
 *   enabled: true,
 *   isUploading: isFileUploading,
 *   isCriticalOperation: isAnalyzing || isConfirming,
 *   message: "文件正在上传中，离开页面将中断上传。确定要离开吗？",
 *   onBeforeUnload: () => {
 *     console.log('用户尝试离开页面')
 *   }
 * })
 * ```
 */
export function useUploadProtection(options: UploadProtectionOptions) {
  const {
    enabled = true,
    isUploading = false,
    isCriticalOperation = false,
    message = "文件正在处理中，离开页面可能导致操作中断。确定要离开吗？",
    onBeforeUnload
  } = options

  const protectionActiveRef = useRef<boolean>(false)
  const listenerAttachedRef = useRef<boolean>(false)

  /**
   * beforeunload 事件处理函数
   */
  const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
    // 只有在保护激活时才拦截
    if (!protectionActiveRef.current) {
      return
    }

    // 调用用户自定义回调
    if (onBeforeUnload) {
      onBeforeUnload()
    }

    // 现代浏览器会忽略自定义消息，但仍需要设置returnValue
    event.preventDefault()
    event.returnValue = message
    
    // 一些旧版浏览器可能需要返回字符串
    return message
  }, [message, onBeforeUnload])

  /**
   * 添加事件监听器
   */
  const addProtection = useCallback(() => {
    if (typeof window === 'undefined' || listenerAttachedRef.current) {
      return
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    listenerAttachedRef.current = true
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('[UploadProtection] 页面离开保护已启用')
    }
  }, [handleBeforeUnload])

  /**
   * 移除事件监听器
   */
  const removeProtection = useCallback(() => {
    if (typeof window === 'undefined' || !listenerAttachedRef.current) {
      return
    }

    window.removeEventListener('beforeunload', handleBeforeUnload)
    listenerAttachedRef.current = false
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('[UploadProtection] 页面离开保护已停用')
    }
  }, [handleBeforeUnload])

  /**
   * 手动设置保护状态
   */
  const setUploadProtection = useCallback((active: boolean) => {
    protectionActiveRef.current = active
    
    if (active && enabled) {
      addProtection()
    } else if (!active) {
      removeProtection()
    }
  }, [enabled, addProtection, removeProtection])

  /**
   * 获取当前保护状态
   */
  const isProtectionActive = useCallback(() => {
    return protectionActiveRef.current && listenerAttachedRef.current
  }, [])

  // 根据上传状态和关键操作状态自动管理保护
  useEffect(() => {
    if (!enabled) {
      removeProtection()
      protectionActiveRef.current = false
      return
    }

    const shouldProtect = isUploading || isCriticalOperation
    
    if (shouldProtect !== protectionActiveRef.current) {
      protectionActiveRef.current = shouldProtect
      
      if (shouldProtect) {
        addProtection()
      } else {
        removeProtection()
      }
    }
  }, [enabled, isUploading, isCriticalOperation, addProtection, removeProtection])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      removeProtection()
      protectionActiveRef.current = false
    }
  }, [removeProtection])

  return {
    /**
     * 手动设置保护状态
     */
    setUploadProtection,
    
    /**
     * 获取当前保护状态
     */
    isProtectionActive,
    
    /**
     * 强制移除保护（用于特殊情况）
     */
    forceRemoveProtection: removeProtection,
    
    /**
     * 强制添加保护（用于特殊情况）
     */
    forceAddProtection: addProtection
  }
}

/**
 * 简化版本的上传保护Hook，用于基本场景
 * 
 * @param isUploading 是否正在上传
 * @param customMessage 自定义提示消息
 */
export function useSimpleUploadProtection(
  isUploading: boolean, 
  customMessage?: string
) {
  return useUploadProtection({
    enabled: true,
    isUploading,
    message: customMessage
  })
} 