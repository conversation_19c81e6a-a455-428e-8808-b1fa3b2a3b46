#!/usr/bin/env python
"""
权限模式切换脚本

使用方法:
    python set_permission_mode.py [mode]
    
参数:
    mode: 权限模式，可选值为 debug, demo, prod。如果不提供，则显示当前模式

示例:
    python set_permission_mode.py debug  # 设置为调试模式
    python set_permission_mode.py prod   # 设置为生产模式
    python set_permission_mode.py        # 显示当前模式
"""

import os
import sys
import re
import platform
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 权限模式
MODES = {
    'debug': '调试模式（所有API允许匿名访问）',
    'demo': '演示模式（部分API允许匿名访问）',
    'prod': '生产模式（严格权限控制）'
}

def get_current_mode():
    """
    获取当前的权限模式
    """
    # 检查环境变量
    env_mode = os.environ.get('PERMISSION_MODE')
    if env_mode in MODES:
        return env_mode, '环境变量'
        
    # 检查settings.py文件
    settings_path = BASE_DIR / 'archive_flow_manager' / 'settings.py'
    if settings_path.exists():
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
            match = re.search(r"PERMISSION_MODE\s*=\s*['\"]([a-z]+)['\"]", content)
            if match:
                mode = match.group(1)
                if mode in MODES:
                    return mode, 'settings.py'
    
    # 默认为生产模式
    return 'prod', '默认值'

def set_mode(mode):
    """
    设置权限模式
    """
    if mode not in MODES:
        print(f"错误: 无效的模式 '{mode}'")
        print(f"有效的模式: {', '.join(MODES.keys())}")
        return False
        
    # 设置环境变量
    if platform.system() == 'Windows':
        os.system(f'set PERMISSION_MODE={mode}')
        # 为当前进程设置环境变量
        os.environ['PERMISSION_MODE'] = mode
        print(f"已为当前会话设置环境变量 PERMISSION_MODE={mode}")
        print("注意: 此设置仅对当前命令窗口有效。关闭窗口后需要重新设置。")
        print(f"在 PowerShell 中使用: $env:PERMISSION_MODE = \"{mode}\"")
        print(f"在 CMD 中使用: set PERMISSION_MODE={mode}")
    else:
        # Linux/Mac
        os.environ['PERMISSION_MODE'] = mode
        print(f"已为当前会话设置环境变量 PERMISSION_MODE={mode}")
        print("注意: 此设置仅对当前终端会话有效。关闭终端后需要重新设置。")
        print(f"设置持久环境变量: echo 'export PERMISSION_MODE={mode}' >> ~/.bashrc")
        
    # 提示修改settings.py的方法
    settings_path = BASE_DIR / 'archive_flow_manager' / 'settings.py'
    print("\n要永久设置权限模式，请修改settings.py文件:")
    print(f"文件路径: {settings_path}")
    print("修改内容: PERMISSION_MODE = '{}'".format(mode))
    
    return True

def main():
    """
    主函数
    """
    if len(sys.argv) > 1:
        # 设置模式
        mode = sys.argv[1].lower()
        if set_mode(mode):
            print(f"\n成功设置权限模式为: {mode} - {MODES.get(mode)}")
    else:
        # 显示当前模式
        mode, source = get_current_mode()
        print(f"当前权限模式: {mode} - {MODES.get(mode)}")
        print(f"来源: {source}")
        print("\n可用模式:")
        for k, v in MODES.items():
            print(f"  {k}: {v}")
        print("\n设置模式: python set_permission_mode.py [mode]")

if __name__ == "__main__":
    main() 