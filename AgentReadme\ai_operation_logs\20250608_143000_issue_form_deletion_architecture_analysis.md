# 操作文档：发放单删除逻辑架构分析

## 📋 变更概述

**目的**: 分析发放单删除逻辑的架构设计合理性，并提出改进建议
**范围**: 模型层业务规则、服务层事务管理、归档服务设计
**关联**: 发放单生命周期管理

## 🔧 操作步骤

### 📊 OP-001: 分析当前删除逻辑实现

**前置条件**: 已有IssueForm模型和IssueFormService
**操作**: 分析模型层业务规则的合理性
**后置条件**: 明确当前实现的优缺点

#### 当前模型层实现分析

```python
# report_issuing/models.py - IssueForm模型
def can_hard_delete(self):
    """检查发放单是否可硬删除"""
    return self.status == 'draft'

def can_soft_delete(self):
    """检查发放单是否可软删除"""
    return self.status == 'issued' and not self.is_deleted
```

**分析结果**:

- ✅ 硬删除逻辑正确：只有草稿状态才能硬删除
- ✅ 软删除逻辑正确：只有已发放（即归档）状态才能软删除
- ✅ 简单状态检查在模型层实现合理，符合DDD原则

### 📊 OP-002: 识别架构设计问题

**前置条件**: 已分析当前实现
**操作**: 识别跨模型操作的事务管理需求
**后置条件**: 明确架构改进方向

#### 发现的问题

1. **缺失归档服务层**
   - 软删除涉及多个模型的状态变更
   - 需要事务管理确保数据一致性
   - 当前在数据服务层实现，职责不够清晰

2. **跨模型操作散落**
   - 删除发放单需要清理关联的发放记录
   - 需要更新档案记录的状态
   - 缺乏统一的事务协调机制

## 📝 变更详情

### CH-001: 建议的架构分层设计

**当前架构**:

```
Model Layer: 简单业务规则检查
Service Layer: 数据操作 + 部分跨模型事务
```

**建议架构**:

```
Model Layer: 简单业务规则检查 (保持不变)
Data Service Layer: 单模型CRUD操作
Transaction Service Layer: 跨模型事务协调
Business Service Layer: 复杂业务逻辑
```

**合理性说明**:

- 模型层的简单状态检查保持不变 ✅
- 复杂的删除操作移到事务服务层
- 确保多模型操作的一致性

### CH-002: 推荐的删除操作分层实现

```python
# 模型层：保持现有简单检查
class IssueForm(models.Model):
    def can_hard_delete(self):
        return self.status == 'draft'
    
    def can_soft_delete(self):
        return self.status == 'issued' and not self.is_deleted

# 数据服务层：单模型操作
class IssueFormService:
    def soft_delete_issue_form(self, form_id, user_id, reason):
        # 只处理发放单本身的软删除
        
    def hard_delete_issue_form(self, form_id):
        # 只处理发放单本身的硬删除

# 事务服务层：跨模型协调（新增）
class IssueFormTransactionService:
    def archive_and_soft_delete_form(self, form_id, user_id, reason):
        """归档并软删除发放单的完整事务操作"""
        with transaction.atomic():
            # 1. 验证业务规则
            # 2. 更新发放记录状态
            # 3. 更新档案记录状态
            # 4. 软删除发放单
            # 5. 记录审计日志
    
    def cleanup_and_hard_delete_form(self, form_id):
        """清理并硬删除发放单的完整事务操作"""
        with transaction.atomic():
            # 1. 验证业务规则
            # 2. 清理关联的发放条目
            # 3. 清理档案记录的临时状态
            # 4. 硬删除发放单
```

## ✅ 验证结果

**架构合理性验证**:

- ✅ 模型层的简单业务规则检查合理
- ✅ 用户关于"归档状态才能软删除"的理解正确（issued = 发放并归档）
- ⚠️ 需要增加事务服务层处理复杂的跨模型操作
- ⚠️ 需要实现完整的归档服务

**改进建议优先级**:

1. **P1**: 实现事务服务层的跨模型删除操作
2. **P2**: 完善归档服务的设计和实现
3. **P3**: 增加完整的审计日志机制

## 📊 总结

**当前实现评估**: 基础合理，但缺乏完整的事务协调
**主要问题**: 缺失归档服务层，跨模型操作需要事务管理
**解决方案**: 保持模型层业务规则，增加事务服务层协调复杂操作
**下一步**: 设计并实现IssueFormTransactionService和归档服务
