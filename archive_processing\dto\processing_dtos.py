"""
处理和统计相关的数据传输对象 (DTOs)
"""
import dataclasses
from typing import Dict, List, Optional

@dataclasses.dataclass
class ProcessingStats:
    """处理统计信息对象"""
    total_pages: int
    processed_pages: int
    matched_pages: int
    processing_time: float
    memory_usage: Dict[str, float]
    pages_per_second: float = 0  # 有默认值的参数放在最后

@dataclasses.dataclass
class ProcessingResultDto:
    """PDF处理服务的结果对象，包含信息提取结果"""
    success: bool
    split_points: List[int]
    unified_numbers: Dict[int, Optional[str]] # 页码 -> 统一编号 (可能为None)
    stats: ProcessingStats
    error_message: Optional[str] = None
    recognition_stats: Optional[Dict[str, int]] = None # 添加字段存储识别方法统计

@dataclasses.dataclass
class SplitPartInfo:
    """存储分割出的PDF部分的信息"""
    output_path: str
    start_page: int
    end_page: int 