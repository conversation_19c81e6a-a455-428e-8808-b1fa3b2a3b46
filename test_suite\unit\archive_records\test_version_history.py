from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APIClient
import pandas as pd
import tempfile
import os
import uuid

from archive_records.models import (
    ArchiveRecord, ImportLog, ChangeLogBatch, 
    RecordChangeLog, FieldChangeLog
)
from archive_records.services.excel_import import ExcelImportService
from test_suite.utils.test_helpers import get_test_file_path
from test_suite.unit.archive_records.base_version_test import BaseVersionTest

class VersionHistoryTest(BaseVersionTest):
    """测试档案记录变更历史功能"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试记录
        self.record = ArchiveRecord.objects.create(
            sample_number="S001",
            commission_number="C001",
            unified_number="C001",
            project_name="测试项目",
            client_unit="测试单位"
        )
        
        # 创建初始版本
        self._create_initial_version(self.record)
    
    def test_record_initial_version(self):
        """测试记录初始创建时的版本记录"""
        # 确保先检查是否已存在该版本
        existing_log = RecordChangeLog.objects.filter(
            record=self.record,
            version_number=1
        ).first()
        
        if not existing_log:
            change_log = RecordChangeLog.objects.create(
                batch=self.batch,
                record=self.record,
                version_number=1,
                change_type='create',
                record_before=None,
                record_after={
                    'sample_number': self.record.sample_number,
                    'commission_number': self.record.commission_number,
                    'unified_number': self.record.unified_number, 
                    'project_name': self.record.project_name,
                    'client_unit': self.record.client_unit
                }
            )
            
            # 为每个字段创建变更日志
            field_logs = []
            for field, value in {
                'sample_number': self.record.sample_number,
                'commission_number': self.record.commission_number,
                'unified_number': self.record.unified_number,
                'project_name': self.record.project_name,
                'client_unit': self.record.client_unit
            }.items():
                field_logs.append(FieldChangeLog(
                    record_change=change_log,
                    field_name=field,
                    field_label=self.import_service._get_field_label(field),
                    old_value=None,
                    new_value=value,
                    field_importance=self.import_service._get_field_importance(field)
                ))
            
            FieldChangeLog.objects.bulk_create(field_logs)
            
            # 验证版本记录创建成功
            self.assertEqual(RecordChangeLog.objects.count(), 1)
            self.assertEqual(RecordChangeLog.objects.first().version_number, 1)
            self.assertEqual(FieldChangeLog.objects.count(), len(field_logs))
        else:
            change_log = existing_log
    
    def test_record_update_version(self):
        """测试记录更新时的版本记录"""
        # 使用基类方法创建更新版本
        change_log = self._create_update_version(
            self.record,
            version_number=2,
            changes={
                'project_name': "更新后的项目名称",
                'client_unit': "更新后的客户单位"
            },
            reason="测试更新"
        )
        
        # 验证版本记录创建成功
        self.assertEqual(RecordChangeLog.objects.count(), 2)
        self.assertEqual(
            RecordChangeLog.objects.order_by('-version_number').first().version_number, 
            2
        )
        # 验证字段变更日志
        self.assertEqual(
            FieldChangeLog.objects.filter(record_change=change_log).count(),
            2  # 更新了两个字段
        )
    
    def test_api_record_history(self):
        """测试记录历史API"""
        # 先创建两个版本
        self.test_record_update_version()
        
        # 测试简要历史API
        url = reverse('record_history', args=[self.record.id])
        response = self.client.get(f"{url}?brief=true")
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['versions_count'], 2)
        self.assertEqual(len(response.data['versions']), 2)
        
        # 测试详细历史API
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['versions_count'], 2)
        self.assertEqual(len(response.data['versions']), 2)
        self.assertIn('record', response.data)
        
    def test_api_version_compare(self):
        """测试版本比较API"""
        # 先创建两个版本
        self.test_record_update_version()
        
        # 测试版本比较API
        url = reverse('record_compare', args=[self.record.id])
        response = self.client.get(f"{url}?v1=1&v2=2")
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['version1'], 1)
        self.assertEqual(response.data['version2'], 2)
        self.assertIn('differences', response.data)
        self.assertEqual(len(response.data['differences']), 2)  # 只有两个字段有变化
        
    def test_api_version_rollback(self):
        """测试版本回滚API"""
        # 先创建两个版本
        self.test_record_update_version()
        
        # 记录当前状态
        record_before_rollback = {
            'project_name': self.record.project_name,
            'client_unit': self.record.client_unit
        }
        
        # 测试回滚到初始版本
        url = reverse('record_rollback', args=[self.record.id, 1])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('message', response.data)
        self.assertIn('changes_count', response.data)
        self.assertIn('new_version', response.data)
        
        # 重新获取记录，验证已回滚
        self.record.refresh_from_db()
        
        # 验证字段已恢复到初始值
        self.assertNotEqual(self.record.project_name, record_before_rollback['project_name'])
        self.assertNotEqual(self.record.client_unit, record_before_rollback['client_unit'])
        
        # 验证现在有三个版本
        self.assertEqual(RecordChangeLog.objects.filter(record=self.record).count(), 3)
        
    def test_excel_import_versioning(self):
        """测试Excel导入时的版本记录"""
        # 假设Excel中包含了这条记录的更新数据
        # 这个测试需要准备一个包含适当数据的Excel文件
        # 在此处模拟导入过程
        
        # 测试前记录版本数
        initial_version_count = RecordChangeLog.objects.filter(record=self.record).count()
        
        # 模拟导入过程(需要实际的Excel文件或模拟导入方法)
        # ...
        
        # 验证导入后版本数增加
        # final_version_count = RecordChangeLog.objects.filter(record=self.record).count()
        # self.assertGreater(final_version_count, initial_version_count)
        
        # 由于这需要实际的Excel文件,所以这里只是概念性的测试框架
        pass 

    def test_version_numbering(self):
        """测试版本号自动递增功能"""
        # 创建初始版本
        self.test_record_initial_version()
        
        # 连续更新记录三次
        batch = ChangeLogBatch.objects.create(
            change_source='manual_edit',
            change_reason='测试版本号递增',
            changed_by=self.user
        )
        
        # 第一次更新
        old_project_name = self.record.project_name
        self.record.project_name = "更新项目名称1"
        self.record.save()
        
        record_before = {'project_name': old_project_name}
        record_after = {'project_name': self.record.project_name}
        
        # 手动获取下一个版本号
        next_version = RecordChangeLog.objects.filter(
            record=self.record
        ).order_by('-version_number').first().version_number + 1
        
        # 创建变更日志
        change_log1 = RecordChangeLog.objects.create(
            batch=batch,
            record=self.record,
            version_number=next_version,
            change_type='update',
            record_before=record_before,
            record_after=record_after,
            changed_fields_count=1
        )
        
        # 第二次更新
        old_project_name = self.record.project_name
        self.record.project_name = "更新项目名称2"
        self.record.save()
        
        record_before = {'project_name': old_project_name}
        record_after = {'project_name': self.record.project_name}
        
        # 手动获取下一个版本号
        next_version = RecordChangeLog.objects.filter(
            record=self.record
        ).order_by('-version_number').first().version_number + 1
        
        # 创建变更日志
        change_log2 = RecordChangeLog.objects.create(
            batch=batch,
            record=self.record,
            version_number=next_version,
            change_type='update',
            record_before=record_before,
            record_after=record_after,
            changed_fields_count=1
        )
        
        # 验证版本号
        versions = RecordChangeLog.objects.filter(record=self.record).order_by('version_number')
        self.assertEqual(versions.count(), 3)  # 初始版本 + 两次更新
        self.assertEqual(versions[0].version_number, 1)
        self.assertEqual(versions[1].version_number, 2)
        self.assertEqual(versions[2].version_number, 3)

    def test_unified_number_sync(self):
        """测试统一编号始终与委托编号同步"""
        # 创建初始版本
        self.test_record_initial_version()
        
        # 更新委托编号
        new_commission_number = "NEW-COMM-2023"
        original_commission_number = self.record.commission_number
        original_unified_number = self.record.unified_number
        
        # 记录更新前状态
        record_before = {
            'commission_number': original_commission_number,
            'unified_number': original_unified_number
        }
        
        # 更新委托编号
        self.record.commission_number = new_commission_number
        # 关键: 确保统一编号也同步更新
        self.record.unified_number = new_commission_number
        self.record.save()
        
        # 记录更新后状态
        record_after = {
            'commission_number': new_commission_number,
            'unified_number': new_commission_number  # 应该与委托编号一致
        }
        
        # 创建变更批次
        batch = ChangeLogBatch.objects.create(
            change_source='manual_edit',
            change_reason='测试统一编号同步',
            changed_by=self.user
        )
        
        # 创建变更日志
        change_log = RecordChangeLog.objects.create(
            batch=batch,
            record=self.record,
            version_number=2,  # 第二个版本
            change_type='update',
            record_before=record_before,
            record_after=record_after,
            changed_fields_count=2  # 两个字段变更
        )
        
        # 为两个变更字段创建变更日志
        FieldChangeLog.objects.create(
            record_change=change_log,
            field_name='commission_number',
            field_label=self.import_service._get_field_label('commission_number'),
            old_value=original_commission_number,
            new_value=new_commission_number,
            field_importance='critical'
        )
        
        FieldChangeLog.objects.create(
            record_change=change_log,
            field_name='unified_number',
            field_label=self.import_service._get_field_label('unified_number'),
            old_value=original_unified_number,
            new_value=new_commission_number,
            field_importance='critical'
        )
        
        # 验证数据库记录
        updated_record = ArchiveRecord.objects.get(id=self.record.id)
        self.assertEqual(updated_record.commission_number, new_commission_number)
        self.assertEqual(updated_record.unified_number, new_commission_number)
        self.assertEqual(updated_record.unified_number, updated_record.commission_number)

class VersionHistoryWorkflowTest(TransactionTestCase):
    """测试版本历史的完整工作流"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 创建测试客户端
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 导入服务
        self.import_service = ExcelImportService()
        
        # 测试文件路径（如有）
        self.test_file_path = get_test_file_path('excel', 'valid_data.xlsx')
    
    def test_complete_workflow(self):
        """测试完整工作流：创建->更新->查看历史->比较->回滚"""
        update_path = None
        temp_path = None
        
        try:
            # 1. 创建一个临时Excel文件用于首次导入
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                temp_path = temp_file.name
                
                # 创建一个包含测试数据的DataFrame - 包含唯一标识字段
                unique_id = f"TEST-{uuid.uuid4().hex[:8]}"  # 创建唯一标识符
                df = pd.DataFrame({
                    '样品编号': [unique_id],  # 使用唯一标识符
                    '委托编号': [f"C-{unique_id}"],
                    '委托日期': ['2023-01-01'],
                    '工程名称': ['初始项目'],
                    '委托单位': ['初始单位'],
                    '结论': ['通过测试']
                })
                
                # 保存到Excel
                df.to_excel(temp_path, index=False)
            
            # 2. 执行首次导入
            with open(temp_path, 'rb') as file:
                initial_response = self.client.post(
                    reverse('import_excel'),
                    {
                        'file': file,
                        'duplicate_strategy': 'skip'
                    },
                    format='multipart'
                )
            
            self.assertEqual(initial_response.status_code, 200)
            self.assertIn('import_log', initial_response.data)
            
            # 在首次导入后
            print(f"\n导入结果: {initial_response.data}")
            record_count = ArchiveRecord.objects.count()
            print(f"数据库中的记录数: {record_count}")
            if record_count > 0:
                records = ArchiveRecord.objects.all()
                for r in records:
                    print(f"记录: ID={r.id}, 样品编号={r.sample_number}, 委托编号={r.commission_number}")
            
            # 3. 查找刚刚导入的记录
            # 重要修改：明确查询条件，使用上面生成的唯一标识符
            import_log_id = initial_response.data['import_log']['id']
            import_log = ImportLog.objects.get(id=import_log_id)

            # 获取导入创建的记录
            created_records = ArchiveRecord.objects.filter(
                created_at__gte=import_log.import_date  # 查找在导入日志创建时间之后创建的记录
            ).order_by('created_at')

            self.assertTrue(created_records.exists(), "没有找到导入创建的记录")
            record = created_records.first()
            
            # 4. 验证初始版本
            version_logs = RecordChangeLog.objects.filter(record=record)
            self.assertEqual(version_logs.count(), 1)
            self.assertEqual(version_logs[0].version_number, 1)
            
            # 5. 创建临时更新Excel文件
            update_data = {
                '样品编号': [unique_id],  # 使用相同的唯一标识符
                '委托编号': [f"C-{unique_id}"],
                '委托日期': ['2023-01-01'],
                '工程名称': ['更新后的项目'],
                '委托单位': ['更新后的单位'],
                '结论': ['更新后结论']
            }
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                update_path = temp_file.name
                pd.DataFrame(update_data).to_excel(update_path, index=False)
            
            # 6. 导入更新Excel
            with open(update_path, 'rb') as file:
                update_response = self.client.post(
                    reverse('import_excel'),
                    {
                        'file': file,
                        'duplicate_strategy': 'smart_update'  # 确保使用智能更新策略
                    },
                    format='multipart'
                )
            
            print(f"更新响应: {update_response.status_code}")
            print(f"响应内容: {update_response.data}")
            
            self.assertEqual(update_response.status_code, 200)
            
            # 7. 刷新记录并验证更新
            record.refresh_from_db()
            self.assertEqual(record.project_name, '更新后的项目', f"记录更新后的项目名称不符，当前值为: {record.project_name}")
            self.assertEqual(record.client_unit, '更新后的单位', f"记录更新后的客户单位不符，当前值为: {record.client_unit}")
            
            # 8. 验证版本记录已更新
            version_logs = RecordChangeLog.objects.filter(record=record)
            self.assertEqual(version_logs.count(), 2)
            latest_version = version_logs.order_by('-version_number').first()
            self.assertEqual(latest_version.version_number, 2)
            
            # 9. 测试历史API
            history_response = self.client.get(
                reverse('record_history', args=[record.id])
            )
            
            self.assertEqual(history_response.status_code, 200)
            self.assertEqual(history_response.data['versions_count'], 2)
            
            # 10. 测试版本比较API
            compare_response = self.client.get(
                f"{reverse('record_compare', args=[record.id])}?v1=1&v2=2"
            )
            
            self.assertEqual(compare_response.status_code, 200)
            self.assertTrue(len(compare_response.data['differences']) >= 2)
            
            # 11. 测试回滚功能
            rollback_response = self.client.post(
                reverse('record_rollback', args=[record.id, 1])
            )
            
            self.assertEqual(rollback_response.status_code, 200)
            
            # 12. 验证回滚后的状态
            record.refresh_from_db()
            self.assertEqual(record.project_name, '初始项目')
            self.assertEqual(record.client_unit, '初始单位')
            
            # 13. 验证现在有三个版本记录
            self.assertEqual(
                RecordChangeLog.objects.filter(record=record).count(), 
                3
            )
            
        except Exception as e:
            self.fail(f"测试过程中出现异常: {str(e)}")
        finally:
            # 清理临时文件
            for path in [temp_path, update_path]:
                if path and os.path.exists(path):
                    try:
                        os.unlink(path)
                        print(f"清理临时文件: {path}")
                    except Exception as e:
                        print(f"清理临时文件失败: {path}, 错误: {str(e)}") 