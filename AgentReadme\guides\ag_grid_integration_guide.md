# AG Grid Enterprise 集成与应用指南

> **重要提示**：本项目实现了AG Grid的集中配置系统，详见[AG Grid集中配置使用指南](./ag_grid_centralized_config_guide.md)。本文档侧重于一般集成知识，而集中配置指南侧重于项目特定的配置系统使用方法。

本文档总结了在本项目中集成和应用 AG Grid Enterprise 的关键经验和注意事项，旨在为后续其他表格的实现或替换提供参考。

## 1. 版本与依赖管理

- **统一版本**: 确保所有 AG Grid 相关包版本一致至关重要。
  - 企业版主要包: `ag-grid-enterprise`, `ag-grid-react`。
  - 本项目建议统一到较新版本，如 `^33.2.4` 或更高稳定版。
- **独立模块包**: AG Grid v33+ 倾向于将许多核心模块包含在主包中。如果遇到特定模块找不到的编译或运行时错误，请务必查阅您使用的 AG Grid 版本的官方文档确认该模块的来源。
- **TypeScript 类型兼容性**: 版本不一致是导致 TypeScript 类型错误的主要原因。
- **版本升级**: 如果需要升级AG Grid版本，特别是升级到v31及以上版本，请参考[AG Grid v31升级指南](./ag_grid_v31_upgrade_guide.md)，其中详细记录了API变更和迁移步骤。

## 2. 模块注册 (使用 AllEnterpriseModule)

AG Grid v33+ 要求显式注册所有使用到的功能模块。这些模块通过 `AgGridReact` 组件的 `modules` prop 传入，或者通过 `ModuleRegistry.registerModules` 全局注册。

- **推荐方法：使用 `AllEnterpriseModule`**
  - **优点**: 包含所有企业版功能模块，包括高级筛选、高级分组、Excel导出、服务器端行模型等
  - **实现**:

    ```typescript
    import { ModuleRegistry, AllEnterpriseModule } from 'ag-grid-enterprise';
    
    // 在应用入口或Grid组件文件顶部全局注册
    ModuleRegistry.registerModules([AllEnterpriseModule]);
    ```

    或者通过 props 传递:

    ```jsx
    import { AllEnterpriseModule } from 'ag-grid-enterprise';
    import { AgGridReact } from 'ag-grid-react';
    
    // ... component ...
    <AgGridReact
      // ... other props
      modules={[AllEnterpriseModule]}
    />
    ```

  - **许可证设置**: 企业版需要设置有效的许可证密钥:

    ```typescript
    import { LicenseManager } from 'ag-grid-enterprise';

    // 设置许可证密钥 (应在应用入口处设置一次)
    LicenseManager.setLicenseKey('YOUR_LICENSE_KEY');
    // 没有有效许可证时，表格会带有水印但在本地开发中仍可正常使用
    ```

- **可选方法：单独注册模块 (用于优化包大小)**
  - **场景**: 如果项目对最终打包体积有严格要求，可以选择只导入和注册实际使用到的模块。
  - **挑战**: 需要精确了解每个功能依赖哪些模块，这可能导致 Linter 或运行时错误（如 Error #200），需要仔细调试。

- **诊断与调试**:
  - **Error #200**: 明确提示缺少某个模块。根据错误信息确认模块名称，然后注册相应模块。
  - **`ValidationModule`**: 为了详细诊断，可以额外注册 `ValidationModule`。

    ```typescript
    import { ModuleRegistry, AllEnterpriseModule, ValidationModule } from 'ag-grid-enterprise';
    ModuleRegistry.registerModules([AllEnterpriseModule, ValidationModule]); // ValidationModule 帮助调试
    ```

## 3. 主题 (Theming) 应用

AG Grid v33 推荐使用新的 Theming API 替代旧的 CSS 文件导入方式。

- **问题诊断**: Error #239 通常指示主题配置冲突 (同时使用新旧方式)。
- **v33 推荐方式**:
  1. **移除旧的 CSS 文件导入**: 不再需要 `import 'ag-grid-enterprise/styles/ag-grid.css';` 等。
  2. **通过 CSS 类名应用主题**: 为包裹 `AgGridReact` 组件的 `div` 添加主题类名。

     ```jsx
     // 示例：应用 Alpine 主题
     <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
       <AgGridReact {...props} />
     </div>
     ```

     其他可用主题包括 `ag-theme-quartz` (v33 新默认), `ag-theme-balham`, `ag-theme-material`。每个主题都有对应的 `dark` 版本，如 `ag-theme-alpine-dark`。

## 4. 基本配置与常用功能

参考 `frontend/app/records/ledger/page.tsx` 中的实现。

- **核心 Props**:
  - `rowData`: 表格数据数组。
  - `columnDefs`: 列定义数组，配置每列的行为和显示。
  - `defaultColDef`: 应用于所有列的默认配置。
  - `modules`: 已注册的 AG Grid 功能模块数组。
  - `pagination`: 布尔值，是否开启分页。
  - `paginationPageSize`: 每页显示的行数。
  - `domLayout`: `'normal'` (推荐，AG Grid 内部处理滚动) 或 `'autoHeight'` 等。
  - `debug`: 开启调试模式，显示额外的调试信息。**必须基于环境变量动态设置**:

    ```jsx
    debug={process.env.NODE_ENV === 'development'}
    ```

  - 企业版专属参数:
    - `rowModelType="serverSide"`: 启用服务器端表格模型
    - `serverSideDatasource={datasource}`: 定义数据源
    - `cacheBlockSize={500}`: 设置数据缓存块大小
    - `maxBlocksInCache={15}`: 设置最大缓存块数

- **Column Definitions (`columnDefs`) 示例**:

  ```javascript
  const columnDefs = [
    { headerName: "ID", field: "id", minWidth: 80, pinned: 'left' },
    { headerName: "名称", field: "name", filter: true, floatingFilter: true }, // 开启列筛选和浮动筛选
    { headerName: "状态", field: "status", cellRenderer: StatusBadgeCellRenderer }, // 自定义渲染
    { headerName: "描述", field: "description", tooltipField: "description" }, // 使用字段内容作为tooltip
    // ...更多列
    { headerName: "操作", field: "actions", cellRenderer: ActionsCellRenderer, pinned: 'right', sortable: false, 
    filter: false }
  ];
  ```

- **Default Column Definitions (`defaultColDef`) 示例**:

  ```javascript
  const defaultColDef = {
    sortable: true,       // 允许排序
    filter: true,         // 允许筛选
    resizable: true,      // 允许调整列宽
    floatingFilter: true, // 默认开启浮动筛选
    suppressHeaderMenuButton: false, // 是否隐藏表头菜单按钮
  };
  ```

- **自定义单元格渲染器**:
  - 通过 `colDef.cellRenderer` 指定一个 React 组件。
  - 接收 `ICellRendererParams` 作为 props。
  - 示例: `ActionsCellRenderer` (操作按钮), `StatusBadgeCellRenderer` (状态徽章)。
- **确保父容器尺寸**: AG Grid 通常会填充其父容器。确保包裹 AG Grid 的 `div` 有明确的高度和宽度 (如使用 flex 布局的
`flex-1` 并配合 `min-h-0`，或直接设置像素值)。

## 5. 高级功能与优化

### 5.1 表头文本宽度自适应 (初始宽度设置)

为了在处理中英文混合表头时获得更合适的初始列宽，项目在 `frontend/utils/grid-utils.ts` 中提供了一套工具函数。
核心思想是在定义 `columnDefs` 时，为每列预估一个基于其表头文本的宽度。

**核心工具函数 (位于 `frontend/utils/grid-utils.ts`)**:

1. `getCharWidth(char: string): number` (内部函数):
    - 根据字符的 Unicode 范围估算单个字符的像素宽度（例如，中文16px，英文8px，数字8px，符号6px）。
2. `calculateTextWidth(text: string): number` (导出函数):
    - 调用 `getCharWidth` 累加字符串中所有字符的估算宽度，得到纯文本的总像素宽度。
3. `getHeaderColumnWidth(headerName: string, options: {...}): number` (导出函数):
    - **推荐用于设置初始列宽的方法。**
    - 内部调用 `calculateTextWidth(headerName)` 获取基础文本宽度。
    - 接受 `options` 对象，允许配置 `padding` (默认为70px，用于表头图标和两侧间距)、`minWidth` (默认90px)、以及针对特殊列（如日期/状态列）的 `isSpecialCase` 和 `specialCaseMinWidth`。
    - 返回最终计算得出的列宽，用于在 `columnDefs` 中直接设置 `width` 属性。

**使用方法：在列定义 (columnDefs) 中设置初始宽度**  

```typescript
// 在你的表格组件中 (例如 MyGridComponent.tsx)
import { useMemo } from 'react';
import { ColDef } from 'ag-grid-enterprise';
import { getHeaderColumnWidth } from '@/utils/grid-utils'; // 导入工具函数

// ...
const columnDefs = useMemo<ColDef[]>(() => {
  const processCol = (colDef: ColDef): ColDef => {
    if (colDef.headerName && !colDef.width && !colDef.flex) {
      let calculatedWidthOptions = {}; // 根据需要传递 specific options
      if (colDef.headerName === "特定列名") {
        calculatedWidthOptions = { padding: 80, minWidth: 100 };
      }
      return {
        ...colDef,
        width: getHeaderColumnWidth(colDef.headerName, calculatedWidthOptions),
      };
    }
    return colDef;
  };

  const initialDefs: ColDef[] = [
    { headerName: "样品编号", field: "sampleNumber" },
    { headerName: "Excel文件名", field: "fileName" },
    { headerName: "操作", field: "actions", width: 120 }, // 已有width，不会被计算
    // ... 其他列定义
  ];
  
  // 如果有列组，需要递归处理
  // return initialDefs.map(processCol); // 简单示例，复杂嵌套列组需要递归 processCols
  // 对于有列组的情况，可以参考 RecordsLedgerPage.tsx 中的 processCols 实现
  return initialDefs.map(col => processCol(col)); // 简化版，实际应处理列组

}, []);

// 在 AgGridReact 中使用 columnDefs
<AgGridReact
  columnDefs={columnDefs}
  // ... 其他props
/>
```

这种方法在列定义（`useMemo`中）时就确定了初始宽度，避免了在表格渲染后再去调整列宽可能带来的闪烁。

### 5.2 服务器端表格模型

企业版特有的服务器端表格模型支持更高效地处理大数据集：

```typescript
// 创建数据源
const createServerSideDatasource = useCallback((): IServerSideDatasource => {
  return {
    getRows: async (params: IServerSideGetRowsParams) => {
      try {
        // 实现根据请求参数获取数据的逻辑
        const response = await fetchData(
          params.request.startRow,
          params.request.endRow,
          params.request.sortModel,
          params.request.filterModel
        );
        params.success({ 
          rowData: response.data, 
          rowCount: response.totalCount 
        });
      } catch (error) {
        params.fail();
      }
    },
  };
}, [fetchData]);

// 在组件中使用
<AgGridReact
  rowModelType="serverSide"
  serverSideDatasource={createServerSideDatasource()}
  cacheBlockSize={20}
  // ... 其他props
/>
```

## 6. 错误处理与调试技巧

- **常见错误代码**:
  - **Error #200**: 模块未注册。查看错误详情，按提示导入并注册相应模块。
  - **Error #239**: 主题冲突或配置问题。注册 `ValidationModule` 以获取详细信息。
- **利用 `ValidationModule`**: 在遇到不明确的错误时，优先注册 `ValidationModule`，它会在控制台输出更详细的诊断信息。
- **使用 `debug` 属性**: 设置 `debug={process.env.NODE_ENV === 'development'}` 可以启用更详细的调试日志。
- **查阅官方文档**: AG Grid 的错误信息通常会提供一个链接到其官网错误代码解释页面，这是解决问题的重要资源。
- **浏览器控制台**: 始终关注浏览器开发者工具的控制台，AG Grid 的详细错误和警告会在这里输出。

## 7. 项目文件参考

- **企业版实现**: `frontend/app/records/ledger/page.tsx`
- **自定义工具函数**: `frontend/utils/grid-utils.ts`

通过遵循这些指南和参考现有实现，可以在项目中更顺畅地集成和使用 AG Grid Enterprise，并方便未来对其他表格进行类似的替换或升级。
