"use client"

import { useState } from "react"
import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Button } from "@/components/ui/button"
import { Download, RefreshCw } from "lucide-react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "recharts"

export default function StatisticsPage() {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date | undefined }>({
    from: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    to: new Date(),
  })
  const [timeFrame, setTimeFrame] = useState("month")
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // 实际应用中，这里会重新获取统计数据
    // await fetchStatistics();
    setTimeout(() => setIsRefreshing(false), 1000)
  }

  // 模拟数据 - 档案统计
  const archiveStats = [
    { name: "1月", 新增: 65, 归档: 40, 发放: 24 },
    { name: "2月", 新增: 59, 归档: 38, 发放: 22 },
    { name: "3月", 新增: 80, 归档: 45, 发放: 35 },
    { name: "4月", 新增: 81, 归档: 60, 发放: 40 },
    { name: "5月", 新增: 56, 归档: 45, 发放: 30 },
    { name: "6月", 新增: 55, 归档: 48, 发放: 25 },
    { name: "7月", 新增: 40, 归档: 30, 发放: 20 },
    { name: "8月", 新增: 45, 归档: 25, 发放: 15 },
    { name: "9月", 新增: 60, 归档: 40, 发放: 30 },
    { name: "10月", 新增: 70, 归档: 50, 发放: 40 },
    { name: "11月", 新增: 90, 归档: 70, 发放: 50 },
    { name: "12月", 新增: 100, 归档: 80, 发放: 60 },
  ]

  // 模拟数据 - 项目类型分布
  const projectTypeData = [
    { name: "环境影响评价", value: 40 },
    { name: "安全评估", value: 30 },
    { name: "技术可行性", value: 20 },
    { name: "验收报告", value: 10 },
  ]

  // 模拟数据 - 用户活动
  const userActivityData = [
    { name: "周一", 活动次数: 120 },
    { name: "周二", 活动次数: 150 },
    { name: "周三", 活动次数: 180 },
    { name: "周四", 活动次数: 170 },
    { name: "周五", 活动次数: 190 },
    { name: "周六", 活动次数: 60 },
    { name: "周日", 活动次数: 40 },
  ]

  // 饼图颜色
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <PageTitle title="数据统计" subtitle="系统数据分析和统计报表" />
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            <span className="sr-only">刷新</span>
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <DateRangePicker onChange={setDateRange} />
        <Select value={timeFrame} onValueChange={setTimeFrame}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="时间粒度" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">按日</SelectItem>
            <SelectItem value="week">按周</SelectItem>
            <SelectItem value="month">按月</SelectItem>
            <SelectItem value="quarter">按季度</SelectItem>
            <SelectItem value="year">按年</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>总档案数</CardTitle>
            <CardDescription>系统中的档案总数</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">12,345</div>
            <p className="text-xs text-muted-foreground">较上月增长 8.2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>归档率</CardTitle>
            <CardDescription>已归档档案占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">76.5%</div>
            <p className="text-xs text-muted-foreground">较上月增长 2.3%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>发放率</CardTitle>
            <CardDescription>已发放档案占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">68.2%</div>
            <p className="text-xs text-muted-foreground">较上月增长 1.5%</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="archive" className="space-y-4">
        <TabsList>
          <TabsTrigger value="archive">档案统计</TabsTrigger>
          <TabsTrigger value="project">项目类型</TabsTrigger>
          <TabsTrigger value="user">用户活动</TabsTrigger>
        </TabsList>

        <TabsContent value="archive">
          <Card>
            <CardHeader>
              <CardTitle>档案数量趋势</CardTitle>
              <CardDescription>按月统计的档案新增、归档和发放数量</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={archiveStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="新增" fill="#8884d8" />
                    <Bar dataKey="归档" fill="#82ca9d" />
                    <Bar dataKey="发放" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="project">
          <Card>
            <CardHeader>
              <CardTitle>项目类型分布</CardTitle>
              <CardDescription>不同类型项目的数量分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={projectTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {projectTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="user">
          <Card>
            <CardHeader>
              <CardTitle>用户活动统计</CardTitle>
              <CardDescription>按日统计的用户活动</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={userActivityData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="活动次数" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
