# Generated by Django 5.1.10 on 2025-06-07 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0012_alter_importlog_options_remove_importlog_import_date'),
        ('report_issuing', '0001_initial'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='issueformitem',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='issueformitem',
            name='first',
            field=models.BooleanField(default=False, verbose_name='是否为第一次发放'),
        ),
        migrations.AddField(
            model_name='issueformitem',
            name='second',
            field=models.BooleanField(default=False, verbose_name='是否为第二次发放'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['archive_record', 'first'], name='report_issu_archive_d18534_idx'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['archive_record', 'second'], name='report_issu_archive_9701e2_idx'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['issue_form', 'first'], name='report_issu_issue_f_7b24be_idx'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['issue_form', 'second'], name='report_issu_issue_f_8888e2_idx'),
        ),
        migrations.AddConstraint(
            model_name='issueformitem',
            constraint=models.UniqueConstraint(fields=('issue_form', 'archive_record'), name='unique_issue_form_archive'),
        ),
        migrations.AddConstraint(
            model_name='issueformitem',
            constraint=models.CheckConstraint(condition=models.Q(('first', True), ('second', True), _negated=True), name='not_both_first_and_second'),
        ),
        migrations.AddConstraint(
            model_name='issueformitem',
            constraint=models.CheckConstraint(condition=models.Q(('first', True), ('second', True), _connector='OR'), name='must_be_first_or_second'),
        ),
        migrations.AlterModelTable(
            name='issueformitem',
            table='report_issuing_issueformitem',
        ),
    ]
