import { Skeleton } from "@/components/ui/skeleton"
import { PageTitle } from "@/components/page-title"

export default function ImportLoading() {
  return (
    <div className="space-y-6">
      <PageTitle title="台账导入" subtitle="导入Excel格式的档案台账数据" />

      {/* Excel导入表单骨架 */}
      <div className="rounded-lg border bg-card shadow-sm">
        <div className="p-6">
          <Skeleton className="h-8 w-1/3 mb-2" />
          <Skeleton className="h-4 w-2/3 mb-6" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-10 w-full mb-4" />
          <Skeleton className="h-10 w-1/4" />
        </div>
      </div>

      {/* 导入历史骨架 */}
      <div className="rounded-lg border bg-card shadow-sm">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <Skeleton className="h-8 w-1/4" />
            <Skeleton className="h-10 w-1/6" />
          </div>
          <div className="border rounded-md">
            <div className="p-4">
              <div className="grid grid-cols-9 gap-4 mb-4">
                {Array(9)
                  .fill(0)
                  .map((_, i) => (
                    <Skeleton key={i} className="h-4" />
                  ))}
              </div>
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="grid grid-cols-9 gap-4 mb-4">
                    {Array(9)
                      .fill(0)
                      .map((_, j) => (
                        <Skeleton key={j} className="h-4" />
                      ))}
                  </div>
                ))}
            </div>
          </div>
          <div className="flex justify-center mt-4">
            <Skeleton className="h-10 w-1/3" />
          </div>
        </div>
      </div>
    </div>
  )
}
