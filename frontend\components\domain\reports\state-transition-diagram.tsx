"use client"

import { useEffect, useRef } from "react"
import mermaid from "mermaid"

interface StateTransitionDiagramProps {
  className?: string
}

export function StateTransitionDiagram({ className }: StateTransitionDiagramProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    mermaid.initialize({
      startOnLoad: true,
      theme: "neutral",
      securityLevel: "loose",
      fontFamily: "inherit",
    })

    if (ref.current) {
      const diagram = `
        graph LR
          draft["草稿"]
          locked["已锁定"]
          confirmed["已确认"]
          archived["已归档"]
          
          draft -->|锁定| locked
          locked -->|解锁| draft
          locked -->|确认| confirmed
          confirmed -->|转为草稿| draft
          confirmed -->|归档| archived
          
          classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px
          classDef draft fill:#f3f4f6,stroke:#9ca3af
          classDef locked fill:#fef3c7,stroke:#d97706
          classDef confirmed fill:#dbeafe,stroke:#3b82f6
          classDef archived fill:#d1fae5,stroke:#10b981
          
          class draft draft
          class locked locked
          class confirmed confirmed
          class archived archived
      `

      mermaid.render("state-diagram", diagram).then((result) => {
        if (ref.current) {
          ref.current.innerHTML = result.svg
        }
      })
    }
  }, [])

  return <div ref={ref} className={className}></div>
}
