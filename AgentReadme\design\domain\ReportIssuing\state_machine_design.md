# 发放单模块状态机模式设计文档

## 1. 概述

### 1.1. 设计目标

本文档旨在为发放单模块设计一个现代化、可扩展的状态机（State Machine）架构。该架构旨在替代原有的、单一的事务服务层，通过将业务逻辑分散到具体的状态处理器（State Handler）中，实现更清晰的职责划分和更高的内聚性。

核心目标：

- **职责清晰**：每个状态处理器只负责其对应状态下的业务逻辑。
- **高内聚性**：与特定状态相关的代码集中管理。
- **可扩展性**：未来新增状态或修改操作时，只需添加或修改对应的处理器，不影响其他状态。
- **安全性**：从架构层面防止在错误的状态下执行非法的操作。

### 1.2. 核心思想

本设计遵循"行为归属于状态"的核心思想。一个发放单（`IssueForm`）的行为（能做什么）完全由其当前的状态决定。我们将创建一个状态机引擎，它根据发放单的当前状态，自动选择正确的状态处理器来响应操作请求。

**特别说明**: 本次设计 **不包含** 任何审批（Approve）或驳回（Reject）相关的流程。

---

## 2. 状态定义

发放单（`IssueForm`）的生命周期包含以下几种明确定义的状态。

### 2.1. 状态枚举 (`report_issuing/states.py`)

我们将使用Python的`Enum`来统一定义状态，以确保类型安全。

```python
# report_issuing/states.py
from enum import Enum

class IssueFormState(Enum):
    """发放单的生命周期状态"""
    DRAFT = "draft"      # 草稿
    LOCKED = "locked"    # 锁定
    ISSUED = "issued"    # 已归档/已发放
    DELETED = "deleted"  # 软删除 (仅归档后)
```

### 2.2. 状态描述

- **DRAFT (草稿)**: 发放单的初始状态。在此状态下，可以自由编辑发放单的所有信息，包括增删条目。
- **LOCKED (锁定)**: 发放单信息确认无误后进入此状态。此状态为只读状态，等待最终的归档操作。
- **ISSUED (已归档)**: 发放单已完成其使命。此状态下，会生成正式的 `IssueRecord` 历史记录，并更新相关档案的发放数量。此状态为最终状态之一，不可再编辑。
- **DELETED (软删除)**: 一种逻辑删除状态，仅用于标记已归档（`ISSUED`）的发放单作废。

---

## 3. 架构设计

### 3.1. 目录结构

```组织结构
report_issuing/
├── states.py                    # 状态枚举定义
├── state_handlers/              # 状态处理器目录
│   ├── __init__.py
│   ├── base_handler.py          # 处理器抽象基类
│   ├── draft_handler.py         # 草稿状态处理器
│   ├── locked_handler.py        # 锁定状态处理器
│   ├── issued_handler.py        # 已归档状态处理器
│   └── deleted_handler.py       # 软删除状态处理器 (可选)
└── form_state_machine.py        # 状态机引擎 (统一入口)
```

### 3.2. 处理器基类 (`state_handlers/base_handler.py`)

定义所有状态处理器都必须遵守的接口契约。默认情况下，所有操作都会抛出`InvalidTransitionError`异常，强制子类必须显式实现其允许的操作。

```python
# report_issuing/state_handlers/base_handler.py
from abc import ABC, abstractmethod

class InvalidTransitionError(Exception):
    """当在不允许的状态下执行操作时抛出此异常。"""
    pass

class BaseStateHandler(ABC):
    """状态处理器抽象基类"""
    
    def __init__(self, form, user_id):
        self.form = form
        self.user_id = user_id
        # 此处将通过依赖注入传入所需的服务
        # self.form_data_service = ...
        # self.business_service = ...
        # self.audit_service = ...

    def update_items(self, to_add, to_remove):
        raise InvalidTransitionError("当前状态不允许更新条目")

    def lock(self):
        raise InvalidTransitionError("当前状态不允许锁定")

    def unlock(self):
        raise InvalidTransitionError("当前状态不允许解锁")
    
    def issue(self):
        raise InvalidTransitionError("当前状态不允许归档(发放)")

    def soft_delete(self, reason):
        raise InvalidTransitionError("当前状态不允许软删除")
        
    def hard_delete(self):
        raise InvalidTransitionError("当前状态不允许硬删除")

```

### 3.3. 状态机引擎 (`form_state_machine.py`)

这是提供给外部（如视图层）调用的统一入口。它根据发放单的当前状态，自动查找并实例化对应的状态处理器，然后将操作请求委托给该处理器。

```python
# report_issuing/form_state_machine.py
from report_issuing.states import IssueFormState
from report_issuing.state_handlers import DraftHandler, LockedHandler, IssuedHandler

class FormStateMachine:
    """发放单状态机引擎"""
    
    _handler_map = {
        IssueFormState.DRAFT: DraftHandler,
        IssueFormState.LOCKED: LockedHandler,
        IssueFormState.ISSUED: IssuedHandler,
    }

    def __init__(self, form_id: int, user_id: int):
        # 1. 从数据服务获取 form 对象
        # 2. 根据 form.status 获取 IssueFormState 枚举
        # 3. 从 _handler_map 查找对应的 Handler 类
        # 4. 实例化 Handler
        # self.handler = Handler(form, user_id)
        pass

    def update_items(self, to_add, to_remove):
        return self.handler.update_items(to_add, to_remove)

    def lock(self):
        return self.handler.lock()
    
    # ... 其他所有操作都委托给 self.handler ...

```

---

## 4. 状态行为定义

### 4.1. DRAFT (草稿) 状态

**处理器**: `DraftHandler`

**允许的操作**:

- **`update_items(to_add, to_remove)`**: 更新发放单条目。
- **`lock()`**: 将状态转换为 `LOCKED`。
- **`hard_delete()`**: **硬删除**。直接从数据库中移除 `IssueForm` 及其所有关联的 `IssueFormItem`。这是不可逆的。

### 4.2. LOCKED (锁定) 状态

**处理器**: `LockedHandler`

**允许的操作**:

- **`unlock()`**: 将状态转换回 `DRAFT`。
- **`issue()`**: 执行归档操作，将状态转换为 `ISSUED`。

### 4.3. ISSUED (已归档) 状态

**处理器**: `IssuedHandler`

**允许的操作**:

- **`soft_delete(reason)`**: **软删除**。将状态转换为 `DELETED`，并记录删除原因。此操作是逻辑删除，数据依然保留在数据库中。

### 4.4. DELETED (软删除) 状态

**处理器**: `DeletedHandler` (如果需要)

**允许的操作**:

- 通常此状态为最终状态，不允许任何操作。可以根据需要实现"恢复"等逻辑。

---

## 5. 后续计划

1. **创建文件和目录**: 根据本文档 `3.1` 节的定义，创建相关的目录和Python文件。
2. **实现服务注入**: 为 `BaseStateHandler` 实现依赖注入机制，使其能够访问数据服务、业务服务等。
3. **填充处理器逻辑**: 逐一实现每个状态处理器 (`DraftHandler`, `LockedHandler` 等) 中允许的操作。
4. **完善状态机引擎**: 完成 `FormStateMachine` 的实现，使其能够正确地实例化和委托处理器。
5. **集成到视图层**: 在API视图中，使用 `FormStateMachine` 来处理所有与状态相关的业务请求。
