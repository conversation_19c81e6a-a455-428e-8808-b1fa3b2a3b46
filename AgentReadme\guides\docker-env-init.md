# docker 环境初始化

## 1.生成并清理Django迁移文件的Docker命令

### 1.清理所有迁移文件（保留`__init__.py`）

```bash
docker-compose exec web bash -c "find . -path '*/migrations/*.py' -not -name '__init__.py' -delete && find . -path '*/migrations/*.pyc' -delete"
```

- 说明：此命令会删除所有`migrations`目录下的`.py`和`.pyc`文件，除了`__init__.py`。

### 2. 重新生成迁移文件

```bash
docker-compose exec web python manage.py makemigrations archive_records

docker-compose exec web python manage.py makemigrations report_issuing

docker-compose exec web python manage.py makemigrations archive_processing

docker-compose exec web python manage.py makemigrations
```

- 说明：此命令会根据当前models生成新的迁移文件。

## 2.创建超级用户命令

```bash
docker-compose exec web python manage.py createsuperuser
```
