# Operation Document: 提取AG Grid错误拦截器为独立组件

## 📋 Change Summary

**Purpose**: 将AG Grid许可证错误拦截逻辑提取为独立组件，支持通过环境变量配置，提高代码复用性和灵活性
**Scope**: 影响错误处理和控制台输出
**Associated**: 用户功能需求

## 🔧 Operation Steps

### 📊 OP-001: 分析需求

**Precondition**: 已有AG Grid错误拦截逻辑在client-layout.tsx中实现
**Operation**: 分析需要提取为独立组件，并支持环境变量配置的需求
**Postcondition**: 确定需要创建独立组件，支持环境变量和属性配置

### ✏️ OP-002: 创建独立组件

**Precondition**: 分析完成且明确需求
**Operation**:

1. 创建frontend/components/common/AgGridErrorInterceptor.tsx组件文件
2. 实现错误拦截逻辑，支持环境变量配置和属性配置
3. 默认不显示激活消息，只有在debug模式下显示拦截信息
**Postcondition**: 创建完成AgGridErrorInterceptor组件

### ✏️ OP-003: 修改客户端布局文件

**Precondition**: AgGridErrorInterceptor组件已实现
**Operation**:

1. 从client-layout.tsx中移除原有拦截逻辑
2. 引入新创建的AgGridErrorInterceptor组件
3. 在布局中添加AgGridErrorInterceptor组件
**Postcondition**: client-layout.tsx中使用了新的拦截器组件

## 📝 Change Details

### CH-001: 创建AgGridErrorInterceptor组件

**File**: `frontend/components/common/AgGridErrorInterceptor.tsx`
**Before**: 文件不存在

**After**:

```tsx
"use client"

import { useEffect } from 'react'

interface AgGridErrorInterceptorProps {
  // 可选：是否输出拦截器激活消息
  showActivationMessage?: boolean
  // 可选：是否启用拦截器，优先级高于环境变量
  enabled?: boolean
}

/**
 * AG Grid 许可证错误拦截器组件
 * 拦截控制台中的AG Grid许可证相关错误消息
 * 
 * 环境变量:
 * - NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT: 设置为 "false" 可禁用拦截
 * - NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG: 设置为 "true" 可在拦截时输出调试信息
 */
export function AgGridErrorInterceptor({ 
  showActivationMessage = false,
  enabled
}: AgGridErrorInterceptorProps) {
  useEffect(() => {
    // 检查环境变量是否禁用了拦截器
    const isEnabledByEnv = process.env.NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT !== 'false'
    // props的enabled参数优先级高于环境变量
    const shouldEnable = enabled !== undefined ? enabled : isEnabledByEnv
    
    // 如果明确禁用，则不激活拦截器
    if (!shouldEnable) return
    
    // IIFE (Immediately Invoked Function Expression) to avoid polluting the global scope
    (function() {
      // 保存原始的 console.error 函数
      const originalConsoleError = console.error

      // 定义AG Grid许可证错误相关的关键词
      const agGridErrorKeywords = [
        "AG Grid Enterprise License",
        "Invalid License Key",
        "ag-grid.com/licensing",
        "Your license key is not valid",
        "*****************************", // 添加星号行，AG Grid常用于错误格式化
        "License Key",
        "validateLicense",
        "centerPadAndOutput", // 拦截格式化函数
        "padAndOutput" // 拦截格式化函数
      ]

      // 重写 console.error 函数
      console.error = function(...args) {
        // 将所有参数转换为字符串，以便进行检查
        const message = args.map(arg => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg)
            } catch (e) {
              return String(arg)
            }
          }
          return String(arg)
        }).join(' ')

        // 检查消息中是否包含任何AG Grid许可证错误关键词
        const isAgGridLicenseError = agGridErrorKeywords.some(keyword => message.includes(keyword))

        // 如果不是AG Grid许可证错误，则调用原始的 console.error
        if (!isAgGridLicenseError) {
          originalConsoleError.apply(console, args)
        } 
        // 如果是AG Grid错误且debug模式开启，则输出提示
        else if (process.env.NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG === 'true') {
          originalConsoleError("拦截到AG Grid许可证错误，已被屏蔽")
        }
      }
      
      // 如果配置了显示激活消息，则输出
      if (showActivationMessage && process.env.NODE_ENV === 'development') {
        originalConsoleError("AG Grid 许可证错误拦截器已激活")
      }
    })()
  }, [showActivationMessage, enabled]) // 仅在依赖项变化时重新运行

  // 这是一个纯功能组件，不需要渲染任何内容
  return null
}

export default AgGridErrorInterceptor
```

**Rationale**: 将错误拦截逻辑提取为独立组件可增强复用性和可维护性，同时支持通过环境变量配置使其更灵活

### CH-002: 更新client-layout.tsx

**File**: `frontend/app/client-layout.tsx`
**Before**:

```tsx
import { usePathname } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"

// CHANGE: [2025-05-10] 添加 AG Grid 错误拦截逻辑
// 引入 useEffect （如果之前没有单独引入，现在确保引入）
// import { useState, useEffect } from "react" // 这行已存在，无需重复

const inter = Inter({ subsets: ["latin"] })
// ... existing code ...
  const pathname = usePathname()
  
  // 在组件挂载后设置状态，防止服务器端和客户端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  // CHANGE: [2025-05-10] 添加 AG Grid 错误拦截逻辑
  useEffect(() => {
    // IIFE (Immediately Invoked Function Expression) to avoid polluting the global scope
    (function() {
      // 保存原始的 console.error 函数
      const originalConsoleError = console.error;

      // 定义AG Grid许可证错误相关的关键词
      const agGridErrorKeywords = [
        "AG Grid Enterprise License",
        "Invalid License Key",
        "ag-grid.com/licensing",
        "Your license key is not valid",
        "*****************************", // 添加星号行，AG Grid常用于错误格式化
        "License Key",
        "validateLicense"
      ];

      // 重写 console.error 函数
      console.error = function(...args) {
        // 将所有参数转换为字符串，以便进行检查
        const message = args.map(arg => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg);
            } catch (e) {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        // 检查消息中是否包含任何AG Grid许可证错误关键词
        const isAgGridLicenseError = agGridErrorKeywords.some(keyword => message.includes(keyword));

        // 如果不是AG Grid许可证错误，则调用原始的 console.error
        if (!isAgGridLicenseError) {
          originalConsoleError.apply(console, args);
        }
        // 调试模式：如果想在开发中看到被拦截的错误，可以取消下面的注释
        // else {
        //   originalConsoleError("拦截到AG Grid许可证错误，已被屏蔽");
        // }
      };
      
      // 最好在开发模式下才激活
      if (process.env.NODE_ENV === 'development') {
        originalConsoleError("AG Grid 许可证错误拦截器已激活。拦截器本身使用原始 console.error 输出此消息。");
      }
    })();
  }, []); // 空依赖数组确保只运行一次
```

**After**:

```tsx
import { usePathname } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"
import AgGridErrorInterceptor from "@/components/common/AgGridErrorInterceptor"

// 引入 useEffect （如果之前没有单独引入，现在确保引入）
// import { useState, useEffect } from "react" // 这行已存在，无需重复

const inter = Inter({ subsets: ["latin"] })
// ... existing code ...
  const pathname = usePathname()
  
  // 在组件挂载后设置状态，防止服务器端和客户端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  // AG Grid错误拦截器组件已单独提取
  
  // 检查当前路径是否不需要导航布局

  // ...
  
  <div style={{ visibility: mounted ? 'visible' : 'hidden' }}>
    {/* AG Grid错误拦截器 */}
    <AgGridErrorInterceptor />
    
    {isLayoutDisabled ? (
      // ...
```

**Rationale**: 使用独立组件替代内联逻辑，减少布局组件的复杂度，提高代码可维护性

## ✅ Verification Results

**Method**: 通过手动测试验证拦截器功能，确保AG Grid许可证错误被正确过滤

**Results**:

- 拦截器成功消除控制台中的AG Grid许可证错误
- 启动时不再显示激活消息，除非明确配置
- 能够通过环境变量控制拦截器的启用/禁用

**Problems**: 无

**Solutions**: 不适用

## 💻 环境变量配置说明

要配置AG Grid错误拦截器行为，可在.env.local文件中添加以下环境变量：

```
# 设置为 "false" 可禁用AG Grid错误拦截
NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT=true

# 设置为 "true" 可在拦截时显示调试信息
NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG=false
```

也可以通过组件属性在特定页面或组件中覆盖默认配置：

```tsx
// 禁用错误拦截
<AgGridErrorInterceptor enabled={false} />

// 启用错误拦截并显示激活消息
<AgGridErrorInterceptor enabled={true} showActivationMessage={true} />
```
