import { PageTitle } from "@/components/page-title"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, Key, UserCog } from "lucide-react"
import Link from "next/link"

export default async function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  // 在实际应用中，这里会根据ID从API获取用户数据
  // 这里使用模拟数据
  const user = {
    id: id,
    name: id === "1" ? "张三" : "李四",
    email: id === "1" ? "<EMAIL>" : "<EMAIL>",
    role: id === "1" ? "系统管理员" : "台账上传者",
    status: "active",
    lastLogin: "2023-12-20 10:30",
    avatar: "/placeholder.svg?key=yfxsp",
    initials: id === "1" ? "张" : "李",
    department: id === "1" ? "信息技术部" : "档案管理部",
    position: id === "1" ? "IT主管" : "档案专员",
    phone: id === "1" ? "13800138000" : "13900139000",
    createdAt: "2023-01-15",
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/users">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">返回</span>
            </Link>
          </Button>
          <PageTitle title={`用户详情: ${user.name}`} subtitle="查看和管理用户信息" />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/users/detail/${user.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              编辑用户
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/users/detail/${user.id}/permissions`}>
              <Key className="mr-2 h-4 w-4" />
              管理权限
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/users/detail/${user.id}/role`}>
              <UserCog className="mr-2 h-4 w-4" />
              分配角色
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-7">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
            <CardDescription>用户的基本个人信息</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <Avatar className="h-24 w-24 mb-4">
              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
              <AvatarFallback className="text-2xl">{user.initials}</AvatarFallback>
            </Avatar>
            <h3 className="text-xl font-semibold">{user.name}</h3>
            <p className="text-muted-foreground mb-2">{user.email}</p>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 mb-4">
              {user.status === "active" ? "活跃" : "未激活"}
            </Badge>
            <div className="w-full space-y-2 text-left">
              <div className="flex justify-between">
                <span className="text-muted-foreground">部门:</span>
                <span>{user.department}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">职位:</span>
                <span>{user.position}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">角色:</span>
                <span>{user.role}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">电话:</span>
                <span>{user.phone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">创建时间:</span>
                <span>{user.createdAt}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">最后登录:</span>
                <span>{user.lastLogin}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-5">
          <Tabs defaultValue="activity">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="activity">活动记录</TabsTrigger>
              <TabsTrigger value="permissions">权限概览</TabsTrigger>
              <TabsTrigger value="settings">账户设置</TabsTrigger>
            </TabsList>
            <TabsContent value="activity" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                  <CardDescription>用户的最近系统操作记录</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-l-2 border-muted pl-4 py-2">
                      <p className="text-sm font-medium">登录系统</p>
                      <p className="text-xs text-muted-foreground">今天 10:30</p>
                    </div>
                    <div className="border-l-2 border-muted pl-4 py-2">
                      <p className="text-sm font-medium">更新了个人资料</p>
                      <p className="text-xs text-muted-foreground">昨天 15:45</p>
                    </div>
                    <div className="border-l-2 border-muted pl-4 py-2">
                      <p className="text-sm font-medium">上传了新的台账文件</p>
                      <p className="text-xs text-muted-foreground">2023-12-18 09:20</p>
                    </div>
                    <div className="border-l-2 border-muted pl-4 py-2">
                      <p className="text-sm font-medium">创建了新的分发报告</p>
                      <p className="text-xs text-muted-foreground">2023-12-15 14:10</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="permissions" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>权限概览</CardTitle>
                  <CardDescription>用户当前的系统权限</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="rounded-md border p-3">
                        <h4 className="font-medium mb-2">档案管理</h4>
                        <ul className="space-y-1 text-sm">
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            查看档案
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            上传台账
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                            删除档案
                          </li>
                        </ul>
                      </div>
                      <div className="rounded-md border p-3">
                        <h4 className="font-medium mb-2">报告管理</h4>
                        <ul className="space-y-1 text-sm">
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            查看报告
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            创建报告
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            分发报告
                          </li>
                        </ul>
                      </div>
                      <div className="rounded-md border p-3">
                        <h4 className="font-medium mb-2">用户管理</h4>
                        <ul className="space-y-1 text-sm">
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            查看用户
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                            创建用户
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                            编辑用户
                          </li>
                        </ul>
                      </div>
                      <div className="rounded-md border p-3">
                        <h4 className="font-medium mb-2">系统设置</h4>
                        <ul className="space-y-1 text-sm">
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                            基本设置
                          </li>
                          <li className="flex items-center">
                            <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                            高级设置
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground mt-4">
                      <p>
                        <span className="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                        已授权
                        <span className="inline-block h-2 w-2 rounded-full bg-red-500 mx-2 ml-4"></span>
                        未授权
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="settings" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>账户设置</CardTitle>
                  <CardDescription>管理用户账户设置和偏好</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">双因素认证</p>
                        <p className="text-sm text-muted-foreground">增强账户安全性</p>
                      </div>
                      <div className="text-sm">未启用</div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">登录通知</p>
                        <p className="text-sm text-muted-foreground">接收新设备登录通知</p>
                      </div>
                      <div className="text-sm">已启用</div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">密码过期</p>
                        <p className="text-sm text-muted-foreground">密码过期时间</p>
                      </div>
                      <div className="text-sm">90天</div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">会话超时</p>
                        <p className="text-sm text-muted-foreground">自动登出时间</p>
                      </div>
                      <div className="text-sm">30分钟</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
