# Authentication System Upgrade: Design Document & Implementation Plan

## Design Document

### 1. Introduction

#### 1.1. Document Purpose

This document outlines the design and implementation plan for upgrading the authentication system of the enterprise internal management system. The goal is to enhance security, robustness, and maintainability by adopting a modern, well-tested technology stack.

#### 1.2. Current System Overview (Brief)

The current system utilizes a custom JWT-based authentication mechanism implemented with a Next.js frontend (`AuthContext`) and a Django backend (`djangorestframework-simplejwt`). While functional, it requires significant manual effort for security hardening and advanced feature implementation.

#### 1.3. Problem Statement

For an enterprise internal system requiring public internet access, the existing custom authentication solution presents several challenges:

- **Security Hardening**: Ensuring comprehensive protection against evolving threats (CSRF, XSS, secure session management, brute-force attacks) requires continuous expert effort.
- **Robustness & Reliability**: Custom logic માટે edge cases and concurrent access can be complex to manage and test thoroughly.
- **Maintainability**: As the system grows, maintaining and extending custom authentication logic becomes increasingly burdensome.
- **Advanced Features**: Implementing features like Multi-Factor Authentication (MFA) or fine-grained session control from scratch is time-consuming and error-prone.

#### 1.4. Proposed Solution

To address these challenges, we propose upgrading the authentication system using the following combination:

- **Frontend (Next.js)**: **NextAuth.js (Auth.js)** for client-side session management, credential handling, and security features.
- **Backend (Django)**: **`dj-rest-auth`** and **`django-allauth`** for comprehensive account management, secure API endpoints, and robust authentication & authorization primitives.

### 2. New Authentication System Goals

- **Enhanced Security**: Implement industry best practices for authentication, session management, and threat mitigation. This is the primary goal.
- **Improved Robustness & Reliability**: Leverage well-tested libraries to ensure stable and predictable authentication flows.
- **Increased Maintainability**: Reduce custom authentication code, relying on established libraries with strong community support and documentation.
- **Foundation for Future Enhancements**: Facilitate easier implementation of future security features like Multi-Factor Authentication (MFA).

### 3. Architecture Overview

#### 3.1. Frontend (Next.js + NextAuth.js)

- **NextAuth.js Role & Responsibilities**:
  - Manages user sessions securely (e.g., using HttpOnly, SameSite, Secure cookies).
  - Provides `CredentialsProvider` to interact with the Django backend's login API.
  - Handles CSRF protection for authentication-related actions.
  - Abstracts away complexities of client-side authentication state management.
- **Key NextAuth.js Configuration (`pages/api/auth/[...nextauth].ts` or `app/api/auth/[...nextauth]/route.ts`)**:
  - `providers`: Primarily `CredentialsProvider`.
  - `pages`: Custom login page path.
  - `session`: JWT strategy (`strategy: 'jwt'`) is recommended for flexibility.
  - `callbacks`:
    - `jwt`: To enrich the NextAuth.js JWT with user details received from Django (e.g., user ID, roles, permissions, access/refresh tokens if Django issues them directly to NextAuth.js).
    - `session`: To expose necessary user information from the NextAuth.js JWT to the client-side `useSession()` hook.
  - `cookies`: Configuration for session and CSRF cookies (e.g., `httpOnly`, `secure`, `sameSite`).
- **Interaction with Django Backend**:
  - The `CredentialsProvider`'s `authorize` function will make a POST request to the Django backend's login API endpoint (provided by `dj-rest-auth`).
  - It will send username/password and receive user data (and potentially JWTs) upon successful authentication.
- **API Client Adaptation**:
  - The existing `useApiClient` or `fetchApi` will need to be modified.
  - Instead of custom token management, it should retrieve the session token (if NextAuth.js is configured to store backend JWTs in its own JWT/session) using `getToken()` (server-side/API routes) or `getSession()` (client-side) from `next-auth/react` and include it in the `Authorization` header for requests to protected Django APIs.
- **Protected Routes & Components**:
  - Achieved using Next.js Middleware with `getToken()`, or client-side with `useSession({ required: true })` or by checking the session status.

#### 3.2. Backend (Django + `dj-rest-auth` + `django-allauth`)

- **`django-allauth` Role**:
  - Robust local user account management: secure password hashing, (optional) email verification, password policies, login attempt tracking, and a foundation for MFA.
  - Manages user registration flows if API-based registration is enabled.
- **`dj-rest-auth` Role**:
  - Provides secure REST API endpoints for:
    - Login (`/login/`)
    - Logout (`/logout/`)
    - Password change (`/password/change/`)
    - Password reset (`/password/reset/`, `/password/reset/confirm/`)
    - User details (`/user/`)
    - (Optional) Registration (`/registration/`)
  - Handles JWT (e.g., access and refresh tokens via `djangorestframework-simplejwt`) issuance and refresh if configured to do so.
- **JWT Configuration (if `dj-rest-auth` issues JWTs directly to NextAuth.js)**:
  - Configure `djangorestframework-simplejwt` settings (token lifetimes, signing algorithm).
  - `dj-rest-auth` settings like `REST_USE_JWT = True`, `JWT_AUTH_HTTPONLY = True/False` (False if NextAuth.js needs to read it from response body, True if HttpOnly cookie is used by `dj-rest-auth` itself), `JWT_AUTH_COOKIE`, `JWT_AUTH_REFRESH_COOKIE`.
- **API Endpoint Protection**:
  - Django REST framework's `DEFAULT_AUTHENTICATION_CLASSES` (e.g., `SessionAuthentication`, `JWTAuthentication`) and `DEFAULT_PERMISSION_CLASSES` (e.g., `IsAuthenticated`) will protect API views.

#### 3.3. Authentication Flow

1. **Login**:
    - User enters credentials on the Next.js login page.
    - Next.js frontend calls `signIn('credentials', { username, password, redirect: false })`.
    - NextAuth.js `CredentialsProvider`'s `authorize` function POSTs credentials to Django's `/api/auth/login/` endpoint.
    - `dj-rest-auth` validates credentials.
    - If valid, Django returns a success response (e.g., user details, and optionally JWT access/refresh tokens if not using server-side sessions primarily with NextAuth.js).
    - The `authorize` function returns user data (or an error) to NextAuth.js.
    - NextAuth.js creates its own session (often a JWT stored in an HttpOnly cookie), potentially including the backend tokens or user details in its payload via callbacks.
2. **Session Management**:
    - NextAuth.js manages the client-side session using its configured strategy (e.g., JWT in HttpOnly cookie).
    - `useSession()` hook provides session state (`data`, `status`) to React components.
3. **Accessing Protected APIs**:
    - Client-side API calls fetch the token managed by NextAuth.js (e.g., via `getSession()` or by an API client wrapper that uses it) and include it in the `Authorization` header.
    - Django backend validates this token.
4. **Token Refresh (if applicable for backend-issued JWTs managed by NextAuth.js)**:
    - If short-lived access tokens are issued by Django and stored within NextAuth.js's session JWT, NextAuth.js's `jwt` callback can handle refreshing them using a Django-issued refresh token when the access token is about to expire.
    - Alternatively, NextAuth.js can manage its own session lifecycle, and Django simply validates the token NextAuth.js presents.
5. **Logout**:
    - Client calls `signOut()`.
    - NextAuth.js clears its session (e.g., deletes its session cookie).
    - (Recommended) NextAuth.js makes a call to Django's `/api/auth/logout/` endpoint to invalidate any server-side session or tokens (like JWT refresh tokens if stored server-side).

### 4. Security Considerations

- **Strong Password Policies**: Enforced by `django-allauth` on the backend.
- **HTTPS**: Mandatory for all public network communication.
- **CSRF Protection**:
  - NextAuth.js provides built-in CSRF protection for its API routes (`/api/auth/...`).
  - For other state-changing API calls to Django, ensure Django's CSRF protection is correctly configured and tokens are handled (NextAuth.js can help by providing the CSRF token if needed for custom API calls).
- **XSS Prevention**: Standard Next.js/React practices, sanitizing user inputs, and potentially Content Security Policy (CSP).
- **Secure Cookie Configuration**: Handled by NextAuth.js (e.g., `HttpOnly`, `Secure` in production, `SameSite=Lax` or `Strict`).
- **Multi-Factor Authentication (MFA)**: Planned as a future enhancement. `django-allauth` provides a good foundation (e.g., TOTP support).
- **Rate Limiting & Brute-Force Protection**: `django-allauth` offers login attempt limits. Consider additional rate limiting at the web server (Nginx) or API gateway level.

### 5. Data Model Changes

- Minimal. `django-allauth` integrates with Django's `User` model and adds its own related tables (e.g., for email addresses, social accounts if used, MFA). No breaking changes to the core user schema are typically required.

### 6. API Endpoint Changes/Additions

- **New Endpoints (provided by `dj-rest-auth`)**:
  - `/api/auth/login/` (POST)
  - `/api/auth/logout/` (POST)
  - `/api/auth/user/` (GET, PUT, PATCH)
  - `/api/auth/password/change/` (POST)
  - `/api/auth/password/reset/` (POST)
  - `/api/auth/password/reset/confirm/` (POST)
  - (If registration enabled) `/api/auth/registration/` (POST)
- **Existing Protected APIs**:
  - Will need to ensure their `authentication_classes` in DRF are set to accept tokens validated by the new mechanism (e.g., `rest_framework_simplejwt.authentication.JWTAuthentication` if Django issues JWTs, or a custom one if NextAuth.js uses a different token format that Django needs to verify).

### 7. Migration Strategy

- **User Accounts**: Existing user accounts in the Django database will be compatible. Password hashes remain valid.
- **`auth-context.tsx`**: The existing custom `AuthContext` will be largely replaced by NextAuth.js's `<SessionProvider>` and `useSession()` hook. Logic for `login`, `logout`, `hasPermission`, `getAuthToken`, `isAuthenticated`, `isLoading` will now primarily come from or be adapted to use NextAuth.js functionalities.
- **Transition**:
    1. Implement backend changes first.
    2. Implement NextAuth.js frontend.
    3. Gradually update components and pages to use the new `useSession()` and API client.
    4. Thorough testing in a staging environment is crucial before production rollout.

---

## Implementation Plan

### Phase 1: Backend Setup (Django)

**Goal**: Configure Django with `django-allauth` and `dj-rest-auth` to provide secure authentication APIs.

- **Task 1.1: Install Dependencies**
  - Add `django-allauth`, `dj-rest-auth`, `djangorestframework-simplejwt` (if `dj-rest-auth` is configured for JWTs) to `requirements.txt`.
  - Run `pip install -r requirements.txt` (or `docker-compose build web` if in Docker).
- **Task 1.2: Configure `settings.py`**
  - Add to `INSTALLED_APPS`:

        ```python
        'rest_framework',
        'rest_framework.authtoken', # Needed by dj-rest-auth by default, or for simplejwt
        'allauth',
        'allauth.account',
        # 'allauth.socialaccount', # Optional, if social login might be used later
        'dj_rest_auth',
        # 'dj_rest_auth.registration', # Optional, if API registration is needed
        ```

  - Add `AUTHENTICATION_BACKENDS`:

        ```python
        AUTHENTICATION_BACKENDS = [
            'django.contrib.auth.backends.ModelBackend', # Default
            'allauth.account.auth_backends.AuthenticationBackend',
        ]
        ```

  - Set `SITE_ID = 1`.
  - **`django-allauth` Configuration**:

        ```python
        ACCOUNT_AUTHENTICATION_METHOD = 'username_email' # Or 'email' or 'username'
        ACCOUNT_EMAIL_REQUIRED = True
        ACCOUNT_EMAIL_VERIFICATION = 'optional' # Or 'mandatory' or 'none'
        ACCOUNT_USERNAME_REQUIRED = True # If username is part of login
        LOGIN_ATTEMPTS_LIMIT = 5
        LOGIN_ATTEMPTS_TIMEOUT = 300 # 5 minutes
        # Add other password policies as needed via django-allauth settings
        ```

  - **`dj-rest-auth` and JWT Configuration (Example using `djangorestframework-simplejwt`)**:

        ```python
        REST_FRAMEWORK = {
            'DEFAULT_AUTHENTICATION_CLASSES': [
                'rest_framework_simplejwt.authentication.JWTAuthentication',
                # 'rest_framework.authentication.SessionAuthentication', # If also using Django sessions for browsable API
            ],
            # ... other DRF settings
        }
        REST_USE_JWT = True
        JWT_AUTH_COOKIE = 'my-app-auth' # Name of the cookie to store JWT if dj-rest-auth sets it
        JWT_AUTH_REFRESH_COOKIE = 'my-app-refresh-token'
        JWT_AUTH_HTTPONLY = True # Recommended for JWT_AUTH_COOKIE if set by dj-rest-auth
        JWT_AUTH_SAMESITE = 'Lax'

        # djangorestframework-simplejwt settings
        from datetime import timedelta
        SIMPLE_JWT = {
            'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60), # Example: 1 hour
            'REFRESH_TOKEN_LIFETIME': timedelta(days=7),   # Example: 7 days
            'ROTATE_REFRESH_TOKENS': True,
            'BLACKLIST_AFTER_ROTATION': True,
        }
        ```

        *Note: If NextAuth.js handles all cookie/session management, `JWT_AUTH_COOKIE` related settings in Django might not be used directly by NextAuth.js if it expects tokens in the response body.*
- **Task 1.3: Configure `urls.py` (Root)**

    ```python
    from django.urls import path, include

    urlpatterns = [
        # ... other urls
        path('api/auth/', include('dj_rest_auth.urls')),
        # path('api/auth/registration/', include('dj_rest_auth.registration.urls')), # If using API registration
        # path('accounts/', include('allauth.urls')), # For allauth's own views if needed (e.g., password reset HTML pages)
    ]
    ```

- **Task 1.4: Database Migrations**
  - Run `python manage.py makemigrations`
  - Run `python manage.py migrate`

- **Task 1.5: Test Backend Endpoints**
  - Create a superuser or test user.
  - Use Postman or a similar tool to test:
    - `POST /api/auth/login/` (with username/password) -> Expect JWT tokens in response.
    - `POST /api/auth/logout/` (with Authorization header)
    - `GET /api/auth/user/` (with Authorization header)

### Phase 2: Frontend Setup (Next.js + NextAuth.js)

**Goal**: Integrate NextAuth.js for client-side authentication and session management.

- **Task 2.1: Install `next-auth`**
  - `npm install next-auth` or `yarn add next-auth`
- **Task 2.2: Create NextAuth.js API Route**
  - Create file: `pages/api/auth/[...nextauth].ts` (for Pages Router) or `app/api/auth/[...nextauth]/route.ts` (for App Router).
- **Task 2.3: Configure NextAuth.js**

    ```typescript
    // pages/api/auth/[...nextauth].ts
    import NextAuth, { NextAuthOptions, User as NextAuthUser } from 'next-auth';
    import CredentialsProvider from 'next-auth/providers/credentials';
    import { JWT } from 'next-auth/jwt';

    // Define a type for your backend user and token structure
    interface BackendUser {
      pk: string; // or id
      username: string;
      email: string;
      // any other fields your backend /user/ endpoint returns
    }
    interface BackendTokenResponse {
      access_token: string;
      refresh_token: string;
      user: BackendUser;
    }

    export const authOptions: NextAuthOptions = {
      providers: [
        CredentialsProvider({
          name: 'Credentials',
          credentials: {
            username: { label: "Username", type: "text" },
            password: { label: "Password", type: "password" }
          },
          async authorize(credentials, req) {
            if (!credentials) return null;
            try {
              // Replace with your actual backend API URL
              const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login/`, {
                method: 'POST',
                body: JSON.stringify({
                  username: credentials.username, // Or 'email' if using email for login
                  password: credentials.password
                }),
                headers: { "Content-Type": "application/json" }
              });

              if (!res.ok) {
                // const errorData = await res.json(); // Contains backend error details
                // console.error("Authorize error:", errorData);
                return null; // Or throw an error
              }
              
              const data: BackendTokenResponse = await res.json();

              if (data && data.user && data.access_token) {
                // Return an object that will be stored in the NextAuth.js JWT
                return {
                  id: data.user.pk, // Map backend user id to NextAuth id
                  name: data.user.username,
                  email: data.user.email,
                  accessToken: data.access_token,
                  refreshToken: data.refresh_token,
                  // any other user fields you want in the JWT/session
                } as NextAuthUser & { accessToken: string; refreshToken: string; }; // Cast for custom props
              }
              return null;
            } catch (e) {
              console.error("Authorize exception:", e);
              return null;
            }
          }
        })
      ],
      session: {
        strategy: 'jwt', // Use JWTs for session management
        maxAge: 60 * 60 * 24, // 1 day (example, align with backend access token or use for session cookie)
      },
      jwt: {
        // secret: process.env.NEXTAUTH_SECRET, // A secret to sign and encrypt JWTs
        maxAge: 60 * 60 * 24, // 1 day (align with session maxAge)
        // async encode() {}, // Custom encode/decode if needed
        // async decode() {},
      },
      callbacks: {
        async jwt({ token, user, account }) {
          // Persist the OAuth access_token and user id to the token right after signin
          if (account && user) { // user and account are only passed on first sign-in
            token.accessToken = (user as any).accessToken; // Cast if custom props were added in authorize
            token.refreshToken = (user as any).refreshToken;
            token.userId = user.id; 
            // token.backendUser = user; // Optionally store the whole user object from backend
          }
          
          // TODO: Implement Access Token Rotation / Refresh Token Logic here if needed
          // This is where you would check if token.accessToken is expired
          // and use token.refreshToken to get a new accessToken from your backend
          // Example (conceptual, needs actual API call and error handling):
          // if (Date.now() / 1000 < (token.accessTokenExpiresAt as number)) {
          //   return token;
          // }
          // return refreshAccessToken(token); // refreshAccessToken would call your backend

          return token;
        },
        async session({ session, token, user }) {
          // Send properties to the client, like an access_token and user id from the JWT
          (session.user as any).id = token.userId as string; // Add user id to session
          (session as any).accessToken = token.accessToken as string; // Add accessToken to session
          // session.backendUser = token.backendUser as BackendUser; // Expose backend user details
          // It's generally recommended not to expose raw refresh tokens to the client-side session.
          
          return session;
        }
      },
      pages: {
        signIn: '/login', // Path to your custom login page
        // error: '/auth/error', // Path to custom error page
      },
      // Ensure you have NEXTAUTH_URL and NEXTAUTH_SECRET in your .env file
      // NEXTAUTH_URL=http://localhost:3000 (for dev)
      // NEXTAUTH_SECRET= (generate a strong secret: `openssl rand -base64 32`)
    };
    export default NextAuth(authOptions);
    ```

- **Task 2.4: Setup `<SessionProvider>`**
  - In `pages/_app.tsx` (or `app/layout.tsx` for App Router):

    ```typescript
    // pages/_app.tsx
    import { SessionProvider } from "next-auth/react";
    import type { AppProps } from 'next/app';

    function MyApp({ Component, pageProps: { session, ...pageProps } }: AppProps) {
      return (
        <SessionProvider session={session}>
          <Component {...pageProps} />
        </SessionProvider>
      );
    }
    export default MyApp;
    ```

- **Task 2.5: Adapt/Replace `AuthContext`**
  - Most functionalities of the old `AuthContext` will be replaced by `useSession()`, `signIn()`, `signOut()`.
  - `user` data comes from `session.user`.
  - `isAuthenticated` is `session.status === 'authenticated'`.
  - `isLoading` is `session.status === 'loading'`.
  - `hasPermission` logic might still be needed, operating on `session.user` data.

- **Task 2.6: Update Login Page UI**
  - Use `signIn('credentials', { username: '...', password: '...', redirect: false })`.
  - Handle `signIn` promise risultato (success/error).
- **Task 2.7: Update Logout Functionality**
  - Use `signOut({ callbackUrl: '/login' })`.
- **Task 2.8: Update API Client (`useApiClient` / `fetchApi`)**
  - Modify it to get the `accessToken` from `getSession()` (client-side) or `getToken({ req })` (server-side, e.g., in API routes or `getServerSideProps`).

    ```typescript
    // Example conceptual change in an API client hook
    import { useSession } from 'next-auth/react';

    // const { data: session } = useSession();
    // const token = session?.accessToken; // Access token from NextAuth session
    // headers: { 'Authorization': `Bearer ${token}` }
    ```

### Phase 3: Integration, Testing & Migration

**Goal**: Ensure seamless integration and migrate the application to the new auth system.

- **Task 3.1: Implement Protected Frontend Routes**
  - Client-side:

        ```typescript
        import { useSession } from 'next-auth/react';
        import { useRouter } from 'next/router';
        
        function ProtectedPage() {
          const { status, data: session } = useSession();
          const router = useRouter();

          if (status === 'loading') return <p>Loading...</p>;
          if (status === 'unauthenticated') {
            router.push('/login'); // Or handle appropriately
            return null; 
          }
          // Render protected content
        }
        ```

  - Server-side (Middleware `middleware.ts`):

        ```typescript
        export { default } from "next-auth/middleware"
        export const config = { matcher: ["/dashboard/:path*", "/reports/management"] } // Example protected paths
        ```

        (Requires `NEXTAUTH_SECRET` to be set for middleware).
- **Task 3.2: End-to-End Testing**
  - Login with valid/invalid credentials.
  - Logout.
  - Session persistence across page reloads/browser restart.
  - Access to protected routes/APIs (authenticated vs. unauthenticated).
  - Error handling for auth failures.
- **Task 3.3: Gradual Replacement**
  - Identify all usages of the old `AuthContext`.
  - Replace them with `useSession()`, `signIn()`, `signOut()`.
  - Update components relying on auth state.

### Phase 4: Security Hardening & Documentation

**Goal**: Finalize security configurations and document the new system.

- **Task 4.1: Review Security Configurations**
  - Verify all NextAuth.js cookie settings (`secure`, `httpOnly`, `sameSite`).
  - Verify `NEXTAUTH_SECRET` is strong and properly set in environment variables.
  - Verify Django `django-allauth` and `dj-rest-auth` security settings (password policies, login attempt limits, JWT settings).
- **Task 4.2: (Future) Plan & Implement MFA**
  - `django-allauth` supports TOTP. Research how to integrate this with NextAuth.js flow (likely custom UI in Next.js calling specific Django MFA verification endpoints).
- **Task 4.3: Update Project Documentation**
  - New authentication flow diagram.
  - Key configuration files and settings.
  - Notes for developers on how to work with the new auth system.

### Phase 5: Rollout & Monitoring

**Goal**: Deploy the new authentication system and monitor its operation.

- **Task 5.1: Staging Environment Deployment & Testing**
  - Thorough UAT and regression testing.
- **Task 5.2: Production Deployment Plan**
  - Consider a maintenance window if significant downtime is expected (should be minimal if backend/frontend are deployed ब्लू/ग्रीन style or with good overlap).
  - Backup existing user database.
- **Task 5.3: Production Deployment**
- **Task 5.4: Post-Deployment Monitoring**
  - Closely monitor Django and Next.js application logs for any authentication-related errors or unusual activity.
  - Track login success/failure rates.

This plan provides a structured approach to upgrading your authentication system. Each task may have sub-tasks and require careful consideration of your specific application details.
