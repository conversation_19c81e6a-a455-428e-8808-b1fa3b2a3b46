"use client"

import type React from "react"
import { useEffect } from "react"

import { cn } from "@/lib/utils"
import {
  Archive,
  ClipboardList,
  FileText,
  Home,
  Settings,
  Upload,
  Users,
  X,
  BarChart3,
  HelpCircle,
  FileEdit,
  ChevronDown,
  ChevronRight,
  FileIcon as FilePdf,
  Database,
  ClipboardCheck,
  ListChecks,
  Plus,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "./ui/button"
import { ScrollArea } from "./ui/scroll-area"
// CHANGE: [2025-06-12] 移除NextAuth导入，使用全局登录保护
import { useState } from "react"

interface SidebarProps {
  open: boolean
  setOpen: (open: boolean) => void
}

interface RouteItem {
  title: string
  href: string
  icon: React.ElementType
  permission: string
  children?: RouteItem[]
  isPlaceholder?: boolean
}

export default function Sidebar({ open, setOpen }: SidebarProps) {
  const pathname = usePathname()
  
  // CHANGE: [2025-06-12] 移除权限检查逻辑，使用全局登录保护

  // 初始化所有菜单为展开状态
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    "/records": true,
    "/archive": true,
    "/reports": true,
    "/change-orders": true,
  })

  // 检查路径是否匹配，包括特殊路径处理
  const isPathMatch = (path: string, currentPath: string): boolean => {
    // 精确匹配
    if (currentPath === path) return true

    // 前缀匹配
    if (currentPath.startsWith(path + "/")) return true

    // 特殊路径匹配 - 处理新建页面
    // 例如：/reports/detail/new 应该匹配 /reports/detail/new
    // 例如：/change-orders/detail/new 应该匹配 /change-orders/detail/new
    if (path.includes("/detail/") && currentPath.includes("/detail/")) {
      const pathBase = path.split("/detail/")[0]
      const currentPathBase = currentPath.split("/detail/")[0]
      if (pathBase === currentPathBase) return true
    }

    return false
  }

  // 根据当前路径自动展开父菜单
  useEffect(() => {
    const routes: RouteItem[] = [
      {
        title: "档案台账记录",
        href: "/records",
        icon: ClipboardList,
        permission: "view_records",
        children: [
          {
            title: "台账管理",
            href: "/records/ledger",
            icon: ListChecks,
            permission: "view_records",
          },
          {
            title: "台账导入",
            href: "/records/import",
            icon: Upload,
            permission: "import_excel",
          },
          {
            title: "导入历史",
            href: "/records/import-history",
            icon: FileText,
            permission: "import_excel",
          },
        ],
      },
      {
        title: "档案归档",
        href: "/archive",
        icon: Archive,
        permission: "upload_pdf",
        children: [
          {
            title: "上传PDF文件",
            href: "/archive/upload",
            icon: FilePdf,
            permission: "upload_pdf",
          },
          {
            title: "PDF导入台账",
            href: "/archive/ledger",
            icon: Database,
            permission: "upload_pdf",
          },
        ],
      },
      {
        title: "报告发放",
        href: "/reports",
        icon: FileText,
        permission: "manage_reports",
        children: [
          {
            title: "报告发放台账",
            href: "/reports/management",
            icon: ClipboardCheck,
            permission: "manage_reports",
          },
          {
            title: "新建报告发放单",
            href: "/reports/detail/new",
            icon: FileText,
            permission: "manage_reports",
          },
        ],
      },
      {
        title: "档案更改单",
        href: "/change-orders",
        icon: FileEdit,
        permission: "manage_change_orders",
        children: [
          {
            title: "更改单台账",
            href: "/change-orders/ledger",
            icon: ClipboardCheck,
            permission: "manage_change_orders",
          },
          {
            title: "新建档案更改单",
            href: "/change-orders/detail/new",
            icon: FileEdit,
            permission: "manage_change_orders",
          },
        ],
      },
    ]

    // 检查当前路径是否匹配任何子菜单，如果是，则展开父菜单
    const newExpandedMenus = { ...expandedMenus }

    routes.forEach((route) => {
      if (route.children) {
        const hasActiveChild = route.children.some((child) => isPathMatch(child.href, pathname))

        if (hasActiveChild) {
          newExpandedMenus[route.href] = true
        }
      }
    })

    setExpandedMenus(newExpandedMenus)
  }, [pathname])

  const toggleMenu = (href: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setExpandedMenus((prev) => ({
      ...prev,
      [href]: !prev[href],
    }))
  }

  const routes: RouteItem[] = [
    {
      title: "仪表盘",
      href: "/dashboard",
      icon: Home,
      permission: "view_dashboard",
    },
    {
      title: "档案台账记录",
      href: "/records",
      icon: ClipboardList,
      permission: "view_records",
      children: [
        {
          title: "台账管理",
          href: "/records/ledger",
          icon: ListChecks,
          permission: "view_records",
        },
        {
          title: "台账导入",
          href: "/records/import",
          icon: Upload,
          permission: "import_excel",
        },
        {
          title: "导入历史",
          href: "/records/import-history",
          icon: FileText,
          permission: "import_excel",
        },
      ],
    },
    {
      title: "档案归档",
      href: "/archive",
      icon: Archive,
      permission: "upload_pdf",
      children: [
        {
          title: "上传PDF文件",
          href: "/archive/upload",
          icon: FilePdf,
          permission: "upload_pdf",
        },
        {
          title: "PDF导入台账",
          href: "/archive/ledger",
          icon: Database,
          permission: "upload_pdf",
        },
      ],
    },
    {
      title: "报告发放",
      href: "/reports",
      icon: FileText,
      permission: "manage_reports",
      children: [
        {
          title: "报告发放台账",
          href: "/reports/management",
          icon: ClipboardCheck,
          permission: "manage_reports",
        },
        {
          title: "新建报告发放单",
          href: "/reports/detail/new",
          icon: FileText,
          permission: "manage_reports",
        },
      ],
    },
    {
      title: "档案更改单",
      href: "/change-orders",
      icon: FileEdit,
      permission: "manage_change_orders",
      children: [
        {
          title: "更改单台账",
          href: "/change-orders/ledger",
          icon: ClipboardCheck,
          permission: "manage_change_orders",
        },
        {
          title: "新建档案更改单",
          href: "/change-orders/detail/new",
          icon: FileEdit,
          permission: "manage_change_orders",
        },
      ],
    },
    {
      title: "用户管理",
      href: "/users",
      icon: Users,
      permission: "manage_users",
      children: [
        {
          title: "用户列表",
          href: "/users/list",
          icon: ListChecks,
          permission: "manage_users",
        },
        {
          title: "新建用户",
          href: "/users/new",
          icon: Plus,
          permission: "manage_users",
        },
        {
          title: "角色权限管理",
          href: "/users/roles",
          icon: Settings,
          permission: "manage_users",
        },
      ],
    },
    {
      title: "数据统计",
      href: "/statistics",
      icon: BarChart3,
      permission: "view_statistics",
    },
    {
      title: "系统设置",
      href: "/settings",
      icon: Settings,
      permission: "manage_settings",
    },
    {
      title: "帮助中心",
      href: "/help/distribution-workflow",
      icon: HelpCircle,
      permission: "view_dashboard", // 所有用户都可以访问帮助
    },
  ]

  const renderMenuItem = (route: RouteItem) => {
    const isActive = pathname === route.href
    const hasChildren = route.children && route.children.length > 0
    const isExpanded = expandedMenus[route.href]
    const isChildActive = hasChildren && route.children?.some((child) => isPathMatch(child.href, pathname))

    // CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护

    return (
      <div key={route.href} className="space-y-1">
        <div className="flex items-center">
          {route.isPlaceholder ? (
            <div
              className={cn(
                "flex flex-1 items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                isActive || isChildActive
                  ? "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
                  : "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700",
              )}
              onClick={(e) => {
                e.preventDefault()
                toggleMenu(route.href, e)
              }}
            >
              <route.icon className="h-5 w-5" />
              {route.title}
            </div>
          ) : (
            <Link
              href={route.href}
              className={cn(
                "flex flex-1 items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                isActive || isChildActive
                  ? "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
                  : "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700",
              )}
            >
              <route.icon className="h-5 w-5" />
              {route.title}
            </Link>
          )}
          {hasChildren && (
            <button
              onClick={(e) => toggleMenu(route.href, e)}
              className={cn(
                "flex h-9 w-9 items-center justify-center rounded-md p-0",
                "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700",
              )}
              aria-label={isExpanded ? "折叠菜单" : "展开菜单"}
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-6 space-y-1 border-l pl-3">
            {(route.children || []).map((child) => {
              // CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护
              
              return (
                  <Link
                    key={child.href}
                    href={child.href}
                    className={cn(
                      "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                      isPathMatch(child.href, pathname)
                        ? "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
                        : "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700",
                    )}
                  >
                    <child.icon className="h-4 w-4" />
                    {child.title}
                  </Link>
              )
            })}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <div
        className={cn("fixed inset-0 z-40 bg-black/80 lg:hidden", open ? "block" : "hidden")}
        onClick={() => setOpen(false)}
      />
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-72 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto",
          open ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <h2 className="text-lg font-semibold">档案管理系统</h2>
          <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setOpen(false)}>
            <X className="h-5 w-5" />
            <span className="sr-only">关闭侧边栏</span>
          </Button>
        </div>
        <ScrollArea className="h-[calc(100vh-4rem)]">
          <div className="py-4 px-3">
            <nav className="space-y-1">{routes.map(renderMenuItem)}</nav>
          </div>
        </ScrollArea>
      </div>
    </>
  )
}
