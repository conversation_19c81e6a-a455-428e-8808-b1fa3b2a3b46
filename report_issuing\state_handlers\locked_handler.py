from .base_handler import BaseStateHandler, InvalidTransitionError
from report_issuing.states import IssueFormState
import logging

logger = logging.getLogger(__name__)

class LockedHandler(BaseStateHandler):
    """处理锁定状态下的发放单操作。"""

    def unlock(self):
        """
        在锁定状态下，允许解锁发放单，将其状态转换回 DRAFT。
        """
        logger.info(f"解锁发放单 {self.form.id}，状态将从 LOCKED 变为 DRAFT。")
        # TODO: [P1] 用户将在此处补充具体的解锁逻辑
        # 1. 调用数据服务更新状态
        # 2. 调用审计服务
        # self.form_data_service.update_status(self.form.id, IssueFormState.DRAFT)
        pass

    def issue(self):
        """
        在锁定状态下，允许归档(发放)发放单，将其状态转换为 ISSUED。
        这是核心的业务闭环操作。
        """
        logger.info(f"归档(发放)发放单 {self.form.id}，状态将从 LOCKED 变为 ISSUED。")
        # TODO: [P1] 用户将在此处补充具体的归档发放逻辑
        # 1. 对所有 IssueFormItem 创建对应的 IssueRecord
        # 2. 更新所有相关的 ArchiveRecord 的发放状态
        # 3. 调用数据服务将 IssueForm 的状态更新为 ISSUED
        # 4. 调用审计服务记录整个归档事件
        pass 