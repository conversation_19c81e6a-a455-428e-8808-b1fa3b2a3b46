---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程

## 开发流程

在开发任何新功能或修改现有功能时，请遵循以下步骤：

1. **了解需求和上下文**
   - 查阅项目文档了解上下文
   - 确定核心目标和用户场景
   - 了解与系统其他部分的交互

2. **分析现有代码和模式**
   - 查找相关函数和实现
   - 确定可复用的代码和模式
   - 分析现有代码的改进机会

3. **设计解决方案**
   - 考虑多种实现方案
   - 分析每种方案的优缺点
   - 确保设计遵循单一职责和关注点分离原则

4. **计划实现细节**
   - 制定具体实施步骤
   - 考虑边缘情况和异常场景
   - 规划输入验证和错误处理
   - 考虑必要的安全措施

5. **编写和优化代码**
   - 遵循代码规范（如中文注释和文档字符串）
   - 适当使用注释和文档字符串
   - 考虑性能优化机会
   - 准备必要的文档

6. **审查和测试**
   - 确保代码遵循各项规则
   - 规划关键测试场景
   - 使用TODO标记未完成部分

7. **总结和计划**
   - 总结已解决的问题
   - 记录尚待解决的问题
   - 规划下一步优先事项
   - 创建/更新操作日志

## 文档变更管理

所有代码变更必须按以下格式标记：

```python
# CHANGE: [YYYY-MM-DD] 变更描述 #问题号
```

所有待办事项必须按以下格式标记：

```python
# TODO: [优先级] 描述
```

## 提交变更

1. **生成提交信息**
   - 提交信息应包括：功能描述、解决的问题、关联的需求/问题号

2. **更新文档**
   - 更新相关文档，如操作日志、功能计划等

## 命令参考

### 创建操作日志文件

使用Python生成时间戳：

```bash
python -c "import datetime; print(datetime.datetime.now().strftime('%%Y-%%m-%%d'))"
```

### 获取当前日期用于代码变更标记

```bash
python -c "import datetime; print(datetime.datetime.now().strftime('%%Y-%%m-%%d'))"
```

