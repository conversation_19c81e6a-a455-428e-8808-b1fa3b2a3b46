/**
 * 档案记录服务
 * 
 * 提供与后端档案记录API的交互功能，使用camelCase接口
 * 自动通过djangorestframework-camel-case转换字段格式
 * 
 * <AUTHOR> Team
 * @since 2025-06-19
 */

import apiClient, { ApiResponse } from '@/lib/apiClient';
import type { 
  ArchiveRecord, 
  ArchiveRecordListItem, 
  ArchiveRecordQueryParams,
  ArchiveRecordPaginatedResponse 
} from '@/types/archive-record';

// ==================== 档案记录服务类 ====================

class ArchiveRecordService {
  private readonly baseUrl = '/api/archive-records';

  /**
   * 获取档案记录列表
   * @param params 查询参数（camelCase）
   * @returns 分页的档案记录列表
   */
  async getArchiveRecords(params: ArchiveRecordQueryParams = {}): Promise<ArchiveRecordPaginatedResponse> {
    const queryParams = new URLSearchParams();
    
    // 构建查询参数 - 这些会被后端自动转换为snake_case
    if (params.page) queryParams.append('page', String(params.page));
    if (params.pageSize) queryParams.append('pageSize', String(params.pageSize));
    if (params.sampleNumber) queryParams.append('sampleNumber', params.sampleNumber);
    if (params.commissionNumber) queryParams.append('commissionNumber', params.commissionNumber);
    if (params.unifiedNumber) queryParams.append('unifiedNumber', params.unifiedNumber);
    if (params.reportNumber) queryParams.append('reportNumber', params.reportNumber);
    if (params.projectName) queryParams.append('projectName', params.projectName);
    if (params.clientUnit) queryParams.append('clientUnit', params.clientUnit);
    if (params.archiveStatus) queryParams.append('archiveStatus', params.archiveStatus);
    if (params.batchNumber) queryParams.append('batchNumber', params.batchNumber);
    if (params.search) queryParams.append('search', params.search);
    if (params.ordering) queryParams.append('ordering', params.ordering);
    
    const apiUrl = `${this.baseUrl}/?${queryParams.toString()}`;
    
    console.log(`[档案记录服务] 请求记录列表，URL: ${apiUrl}`);
    
    try {
      const response = await apiClient.get<ArchiveRecordPaginatedResponse>(apiUrl);
      
      if (!response.success) {
        throw new Error(response.error || '获取档案记录失败');
      }
      
      return response.data!;
      
    } catch (error) {
      console.error('[档案记录服务] 获取记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取档案记录详情
   * @param id 记录ID
   * @returns 档案记录详细信息
   */
  async getArchiveRecord(id: number): Promise<ArchiveRecord> {
    const apiUrl = `${this.baseUrl}/${id}/`;
    console.log(`[档案记录服务] 请求记录详情，URL: ${apiUrl}`);
    
    try {
      const response = await apiClient.get<ArchiveRecord>(apiUrl);
      
      if (!response.success) {
        throw new Error(response.error || '获取档案记录详情失败');
      }
      
      return response.data!;
      
    } catch (error) {
      console.error(`[档案记录服务] 获取记录详情失败(ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 创建档案记录
   * @param recordData 记录数据（camelCase）
   * @returns 创建的档案记录
   */
  async createArchiveRecord(recordData: Partial<ArchiveRecord>): Promise<ArchiveRecord> {
    const apiUrl = `${this.baseUrl}/`;
    
    try {
      const response = await apiClient.post<ArchiveRecord>(apiUrl, recordData);
      
      if (!response.success) {
        throw new Error(response.error || '创建档案记录失败');
      }
      
      return response.data!;
      
    } catch (error) {
      console.error('[档案记录服务] 创建记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新档案记录
   * @param id 记录ID
   * @param recordData 更新的记录数据（camelCase）
   * @returns 更新后的档案记录
   */
  async updateArchiveRecord(id: number, recordData: Partial<ArchiveRecord>): Promise<ArchiveRecord> {
    const apiUrl = `${this.baseUrl}/${id}/`;
    
    try {
      const response = await apiClient.patch<ArchiveRecord>(apiUrl, recordData);
      
      if (!response.success) {
        throw new Error(response.error || '更新档案记录失败');
      }
      
      return response.data!;
      
    } catch (error) {
      console.error(`[档案记录服务] 更新记录失败(ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 删除档案记录
   * @param id 记录ID
   * @returns 操作结果
   */
  async deleteArchiveRecord(id: number): Promise<{ success: boolean; message: string }> {
    const apiUrl = `${this.baseUrl}/${id}/`;
    
    try {
      const response = await apiClient.delete(apiUrl);
      
      if (!response.success) {
        throw new Error(response.error || '删除失败');
      }
      
      return {
        success: true,
        message: '删除成功'
      };
      
    } catch (error) {
      console.error(`[档案记录服务] 删除记录失败(ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 批量更新档案记录状态
   * @param ids 记录ID数组
   * @param status 新状态
   * @returns 操作结果
   */
  async batchUpdateStatus(ids: number[], status: string): Promise<{ success: boolean; message: string }> {
    const apiUrl = `${this.baseUrl}/batch-update-status/`;
    
    try {
      const response = await apiClient.post(apiUrl, {
        ids,
        archiveStatus: status  // camelCase，会被自动转换为archive_status
      });
      
      if (!response.success) {
        throw new Error(response.error || '批量更新失败');
      }
      
      return {
        success: true,
        message: '批量更新成功'
      };
      
    } catch (error) {
      console.error('[档案记录服务] 批量更新状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取档案统计信息
   * @returns 统计数据
   */
  async getArchiveStatistics(): Promise<{
    totalRecords: number;
    archivedRecords: number;
    pendingRecords: number;
    issuedRecords: number;
  }> {
    const apiUrl = `${this.baseUrl}/statistics/`;
    
    try {
      const response = await apiClient.get<{
        totalRecords: number;
        archivedRecords: number;
        pendingRecords: number;
        issuedRecords: number;
      }>(apiUrl);
      
      if (!response.success) {
        throw new Error(response.error || '获取统计信息失败');
      }
      
      return response.data!;
      
    } catch (error) {
      console.error('[档案记录服务] 获取统计信息失败:', error);
      throw error;
    }
  }
}

// ==================== 导出 ====================

// 创建单例实例
const archiveRecordService = new ArchiveRecordService();

export default archiveRecordService;
export { ArchiveRecordService };

// 导出类型
export type {
  ArchiveRecord,
  ArchiveRecordListItem,
  ArchiveRecordQueryParams,
  ArchiveRecordPaginatedResponse
} from '@/types/archive-record'; 