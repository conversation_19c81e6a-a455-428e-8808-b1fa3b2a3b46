#!/usr/bin/env python3
"""
OCR缓存效果分析脚本
分析缓存在实际业务场景中的收益
"""
import time
import sys
import os
from PIL import Image, ImageDraw, ImageFont

# 添加项目路径
sys.path.append('.')

def create_test_image(text: str, size=(400, 200)) -> Image.Image:
    """创建测试图像"""
    image = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 回退到默认字体
        font = ImageFont.load_default()
    
    # 计算文本位置
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    draw.text((x, y), text, fill='black', font=font)
    return image

def simulate_business_scenario():
    """模拟实际业务场景"""
    print("🔍 OCR缓存效果分析")
    print("=" * 60)
    
    # 创建测试图像（模拟PDF页面）
    test_images = {
        "contract_page_1": create_test_image("合同第一页\n统一编号: 2025001"),
        "contract_page_2": create_test_image("合同第二页\n详细条款内容"),
        "template_page": create_test_image("标准模板页\n公司信息"),
    }
    
    print(f"📄 创建了 {len(test_images)} 个测试图像")
    
    # 模拟业务场景
    scenarios = [
        {
            "name": "场景1: 代合同识别",
            "operations": [
                ("contract_page_1", "basic", "识别合同内容"),
                ("contract_page_2", "basic", "识别合同内容"),
                ("template_page", "basic", "识别模板内容"),
            ]
        },
        {
            "name": "场景2: 统一编号提取",
            "operations": [
                ("contract_page_1", "enhanced", "提取统一编号"),
                ("contract_page_2", "enhanced", "查找统一编号"),
                ("template_page", "enhanced", "查找统一编号"),
            ]
        },
        {
            "name": "场景3: 重复处理（缓存收益）",
            "operations": [
                ("contract_page_1", "basic", "重复识别合同"),
                ("contract_page_1", "enhanced", "重复提取编号"),
                ("template_page", "basic", "重复识别模板"),
                ("template_page", "enhanced", "重复处理模板"),
            ]
        }
    ]
    
    total_operations = sum(len(s["operations"]) for s in scenarios)
    cache_hits = 0
    
    print(f"\n📊 业务场景分析（共 {total_operations} 次操作）:")
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        for image_name, ocr_type, description in scenario["operations"]:
            # 模拟缓存命中判断
            is_cache_hit = image_name in ["contract_page_1", "template_page"] and scenario["name"] == "场景3: 重复处理（缓存收益）"
            
            if is_cache_hit:
                cache_hits += 1
                print(f"  ✅ {description} ({ocr_type}) - 缓存命中")
            else:
                print(f"  🔄 {description} ({ocr_type}) - 执行OCR")
    
    cache_hit_rate = (cache_hits / total_operations) * 100
    print(f"\n📈 缓存效果统计:")
    print(f"  总操作数: {total_operations}")
    print(f"  缓存命中: {cache_hits}")
    print(f"  命中率: {cache_hit_rate:.1f}%")
    
    # 性能收益估算
    print(f"\n⚡ 性能收益估算:")
    basic_ocr_time = 0.5  # 基础OCR平均耗时（秒）
    enhanced_ocr_time = 2.0  # 增强OCR平均耗时（秒）
    
    total_time_without_cache = (6 * basic_ocr_time) + (6 * enhanced_ocr_time)
    time_saved = (2 * basic_ocr_time) + (2 * enhanced_ocr_time)  # 缓存命中节省的时间
    total_time_with_cache = total_time_without_cache - time_saved
    
    print(f"  无缓存总耗时: {total_time_without_cache:.1f}秒")
    print(f"  有缓存总耗时: {total_time_with_cache:.1f}秒")
    print(f"  节省时间: {time_saved:.1f}秒 ({(time_saved/total_time_without_cache)*100:.1f}%)")
    
    return {
        "total_operations": total_operations,
        "cache_hits": cache_hits,
        "cache_hit_rate": cache_hit_rate,
        "time_saved": time_saved
    }

def analyze_cache_strategy():
    """分析缓存策略"""
    print(f"\n🎯 缓存策略分析:")
    print("-" * 40)
    
    strategies = [
        {
            "name": "主应用缓存",
            "pros": ["跨OCR引擎共享", "业务逻辑集成"],
            "cons": ["网络传输仍需要", "缓存分散"]
        },
        {
            "name": "微服务缓存",
            "pros": ["接近计算资源", "减少重复计算", "独立管理"],
            "cons": ["无法跨服务共享"]
        }
    ]
    
    for strategy in strategies:
        print(f"\n{strategy['name']}:")
        print(f"  优势: {', '.join(strategy['pros'])}")
        print(f"  劣势: {', '.join(strategy['cons'])}")
    
    print(f"\n💡 结论:")
    print(f"  - 微服务缓存更适合当前架构")
    print(f"  - 主要收益在于避免重复的OCR计算")
    print(f"  - 网络传输无法避免，但计算缓存仍有价值")
    print(f"  - 增强OCR的缓存收益更大（计算成本更高）")

def main():
    """主函数"""
    try:
        # 业务场景分析
        results = simulate_business_scenario()
        
        # 缓存策略分析
        analyze_cache_strategy()
        
        print(f"\n✅ 分析完成")
        print(f"缓存在实际业务中可节省 {results['time_saved']:.1f}秒 ({results['cache_hit_rate']:.1f}% 命中率)")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
