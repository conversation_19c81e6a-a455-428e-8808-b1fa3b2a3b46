# CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 #AFM-Refactor-PDFLib
# 注意：该文件大部分功能已废弃并迁移至其他模块，此处仅为保持兼容性而修改导入
import fitz  # PyMuPDF
import pytesseract
import os
import logging
from typing import List, Dict, Tuple, Optional, Union
from PIL import Image, ImageEnhance, ImageFilter
from django.conf import settings
import cv2
import numpy as np
import re
import gc
import time
import sys
import warnings
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from collections import Counter
import uuid  # <-- Import uuid module
import shutil # <-- Import shutil module
from . import image_utils # 新增导入
from . import text_utils # 更新导入
from . import system_utils # 新增导入：系统工具
from . import ocr_utils # <-- 新增导入 ocr_utils
from archive_processing.dto.pdf_dtos import PDFPageImage, PDFPartRange
from archive_processing.dto.result_dtos import UnifiedNumberResult
from archive_processing.dto.processing_dtos import ProcessingStats, ProcessingResultDto #<-- Import new DTO

# 尝试导入PaddleOCR (可选依赖)
try:
    from paddleocr import PaddleOCR
    PADDLE_OCR_AVAILABLE = True
    warnings.filterwarnings("ignore", category=UserWarning, module='paddle')
    # CHANGE: [2024-04-16] 抑制PaddleOCR的DEBUG级别日志 #AFM-59
    paddle_logger = logging.getLogger("ppocr")
    if paddle_logger:
        paddle_logger.setLevel(logging.INFO)  # 将PaddleOCR日志级别从DEBUG调整为INFO
except ImportError:
    PADDLE_OCR_AVAILABLE = False
    warnings.warn("PaddleOCR未安装，将使用Tesseract OCR。安装PaddleOCR可显著提高识别效果: pip install paddlepaddle paddleocr")

# 设置Tesseract数据目录环境变量
tessdata_dir = getattr(settings, 'TESSDATA_PREFIX', None)
if tessdata_dir:
    os.environ['TESSDATA_PREFIX'] = tessdata_dir

# 设置Tesseract命令路径
pytesseract.pytesseract.tesseract_cmd = getattr(settings, 'TESSERACT_CMD_PATH', r'C:\Program Files\Tesseract-OCR\tesseract.exe')

logger = logging.getLogger(__name__)

# 注释: 以下函数已移动到 system_utils 模块
# def _detect_cpu_type():
#    """检测CPU类型，返回是否为Intel处理器"""
#    [原函数实现]

def _detect_cpu_type():
    """
    检测CPU类型，返回CPU平台类型
    
    MOVED: [2024-05-14] 移动到 archive_processing/utils/system_utils.py #AFM-32
    """
    return system_utils.detect_cpu_type()

# 添加格式化时间函数
def _format_time(seconds):
    """
    格式化时间为易读形式
    
    MOVED: [2024-05-14] 移动到 archive_processing/utils/system_utils.py #AFM-32
    """
    return system_utils.format_time(seconds)

# 使用从DTO模块导入的数据类:
# - PDFPageImage 来自 archive_processing.dto.pdf_dtos
# - PDFPartRange 来自 archive_processing.dto.pdf_dtos
# - UnifiedNumberResult 来自 archive_processing.dto.result_dtos
# - ProcessingStats 来自 archive_processing.dto.processing_dtos

# 标记旧类为废弃
# @deprecated("Use PdfProcessingService instead.")
class PDFProcessor:
    """PDF处理器类 (旧版，已废弃，请使用 PdfProcessingService)。
    
    保留此类仅为兼容旧的调用点或参考实现。
    核心逻辑已迁移至 PdfProcessingService。
    """
    
    def __init__(self, 
                 dpi: int = 150, 
                 crop_ratio: float = 0.2,
                 batch_size: int = 100,
                 case_sensitive: bool = False,
                 ignore_punctuation: bool = True,
                 use_paddle_ocr: bool = True,
                 cpu_optimization: Optional[str] = None,
                 enable_cache: bool = True):
        """
        (已废弃) 初始化PDF处理器。
        """
        logger.warning("PDFProcessor 类已废弃，请迁移到使用 PdfProcessingService。")
        # 保留初始化逻辑，以便旧代码能运行，但核心方法会出错或为空
        self.dpi = dpi
        self.crop_ratio = crop_ratio
        self.batch_size = batch_size
        self.case_sensitive = case_sensitive
        self.ignore_punctuation = ignore_punctuation
        self.enable_cache = enable_cache
        self.ocr_cache = {} 
        self.use_paddle_ocr = use_paddle_ocr and ocr_utils.PADDLE_OCR_AVAILABLE # 使用 ocr_utils 判断
        self.paddle_ocr = None
        self.cpu_type = cpu_optimization or system_utils.detect_cpu_type()
        self.stats = ProcessingStats(0, 0, 0, 0, {}, 0)
        self.candidate_results = {}
        self.unified_numbers = {}
        self.recognition_stats = {}
        
        # 初始化调用已移至服务
        # if self.use_paddle_ocr:
        #     self.paddle_ocr = ocr_utils.init_paddle_ocr(use_paddle=True)
        #     if self.paddle_ocr is None:
        #         self.use_paddle_ocr = False

    def process_pdf(self, pdf_path: str, target_text: str) -> List[Tuple[str, Optional[str]]]:
        """(已废弃) 处理PDF并执行物理分割。请改用 PdfProcessingService + 调用方编排。"""
        logger.error("PDFProcessor.process_pdf 方法已废弃且不再有效！请使用 PdfProcessingService。")
        # raise NotImplementedError("PDFProcessor.process_pdf is deprecated. Use PdfProcessingService and orchestrate splitting separately.")
        # 返回空列表以避免旧代码完全崩溃
        return []

    # --- 以下辅助方法已被迁移或将在 PdfProcessingService 中重构 ---

    # MOVED: [2024-05-15] Logic moved to PdfProcessingService (as _find_split_points initially)
    # def _find_split_points(...):
    #    [原实现]
    
    # MOVED: [2024-05-15] Logic moved to PdfProcessingService (as _process_page_batch initially)
    # def _process_page_batch(...):
    #    [原实现]

    # MOVED: [2024-05-15] Logic moved to PdfProcessingService (as _enhance_unified_number_recognition initially)
    # def _enhance_unified_number_recognition(...):
    #    [原实现]

    # MOVED: [2024-05-15] Logic moved to PdfProcessingService (as _get_page_crop initially)
    # def _get_page_crop(...):
    #    [原实现]

    # MOVED: [2024-05-15] Logic moved to PdfProcessingService (as _perform_deep_detection initially)
    # def _perform_deep_detection(...):
    #    [原实现]

    # BRIDGE: Calls text_utils.choose_best_number
    # def _choose_best_number(...):
    #    [旧实现或直接调用 text_utils]
    
    # BRIDGE: Calls ocr_utils.perform_basic_ocr
    # def _perform_basic_ocr(...):
    #    [旧实现或直接调用 ocr_utils]

    # BRIDGE: Calls ocr_utils.perform_enhanced_ocr
    # def _perform_enhanced_ocr(...):
    #    [旧实现或直接调用 ocr_utils]

    # BRIDGE: Calls image_utils.prepare_standard_image_for_paddle/tesseract
    # def _preprocess_image(...):
    #     [旧实现或直接调用 image_utils]

    # BRIDGE: Calls text_utils.preprocess_text
    # def _preprocess_text(...):
    #     [旧实现或直接调用 text_utils]

    # BRIDGE: Calls text_utils.is_text_match
    # def _is_text_match(...):
    #     [旧实现或直接调用 text_utils]

    # BRIDGE: Calls text_utils.remove_parentheses_content
    # def _remove_parentheses_content(...):
    #     [旧实现或直接调用 text_utils]
    
    # BRIDGE: Calls text_utils.extract_unified_number
    # def _extract_unified_number(...):
    #     [旧实现或直接调用 text_utils]

    # BRIDGE: Calls text_utils.normalize_unified_number
    # def _normalize_unified_number(...):
    #     [旧实现或直接调用 text_utils]

    # BRIDGE: Calls system_utils.detect_cpu_type
    # def _detect_cpu_type(...):
    #     [旧实现或直接调用 system_utils]

    # BRIDGE: Calls system_utils.format_time
    # def _format_time(...):
    #     [旧实现或直接调用 system_utils]

    # BRIDGE: Calls image_utils.image_hash
    # def _image_hash(...):
    #     [旧实现或直接调用 image_utils]

    # TO BE REFACTORED/MOVED: PDF Splitting Logic
    def _split_pdf(self, pdf_path: str, split_points: List[int], unified_numbers: Dict[int, str]) -> List[Tuple[str, Optional[str]]]:
        """(已废弃) PDF物理分割逻辑已被移出。请使用 pdf_utils.create_temp_pdf_for_single_archive 配合调用方逻辑。"""
        logger.error("_split_pdf 方法已废弃且不再实现PDF分割。")
        raise NotImplementedError("PDFProcessor._split_pdf is deprecated and removed. Use pdf_utils.create_temp_pdf_for_single_archive in the calling orchestration logic.")
        # 返回空列表以避免旧代码完全崩溃 (如果去掉 raise)
        # return []

    # MOVED: [2024-05-16] Logic moved to pdf_utils.calculate_part_ranges
    # def _calculate_part_ranges(...):
    #     [Original implementation removed]
    
    def get_stats(self) -> dict:
        """(保留，但数据源已变化) 获取处理统计信息。"""
        # 注意：此方法现在返回的是实例的状态，可能与 PdfProcessingService 返回的不同
        import dataclasses # Ensure dataclasses is imported if used standalone
        return dataclasses.asdict(self.stats)

# --- 以下为独立函数，部分已被移至相应 utils 或 services --- 

# MOVED to services/file_storage_service.py
# def generate_archive_filename(...):
#     [原实现]

# MOVED to services/file_storage_service.py
# def generate_archive_storage_path(...):
#     [原实现]

# MOVED POTENTIAL to pdf_utils.py or similar
# def _get_pdf_reader(...):
#     [原实现]

# MOVED POTENTIAL to pdf_utils.py or similar
# def _write_pdf_part(...):
#     [拆分自 _split_pdf]


# 统一入口函数
def split_pdf(pdf_path: str, target_text: str, use_paddle_ocr: bool = True, cpu_optimization: Optional[str] = None, enable_cache: bool = True, **kwargs) -> List[str]:
    """
    分割PDF文件
    
    Args:
        pdf_path: PDF文件路径
        target_text: 目标文本
        use_paddle_ocr: 是否使用PaddleOCR引擎(如果可用)
        cpu_optimization: CPU优化模式("intel", "amd"或None自动检测)
        enable_cache: 是否启用OCR缓存提高性能
        **kwargs: 其他参数设置
    
    Returns:
        分割后的PDF文件路径列表
    """
    # 更新参数
    kwargs.update({
        'use_paddle_ocr': use_paddle_ocr,
        'cpu_optimization': cpu_optimization,
        'enable_cache': enable_cache
    })
    
    processor = PDFProcessor(**kwargs)
    return processor.process_pdf(pdf_path, target_text)


def debug_pdf_processing(pdf_path: str, target_text: str, output_dir: str = None, **kwargs) -> Dict:
    """
    (已废弃) 调试PDF处理功能。请使用新的服务和工具自行实现调试逻辑。
    """
    logger.error("debug_pdf_processing 函数已废弃且不再有效！")
    raise NotImplementedError("debug_pdf_processing is deprecated. Implement debugging logic using the new services and utils.")
    # 返回空字典
    # return {}


def benchmark_pdf_processing(pdf_path: str, repeat: int = 3) -> Dict:
    """
    (可能需要重构) 基准测试PDF处理性能。
    
    注意：此函数目前基于旧的 PDFProcessor 模拟处理速度，
    可能需要重构以使用 PdfProcessingService 进行更真实的基准测试。
    """
    logger.warning("benchmark_pdf_processing 可能需要重构以使用新的 PdfProcessingService。")
    # [保留原实现，但添加警告]
    results = {
        'runs': [],
        'avg_time': 0,
        'avg_speed': 0,
        'best_time': float('inf'),
        'worst_time': 0
    }
    
    total_time = 0
    for i in range(repeat):
        print(f"运行测试 {i+1}/{repeat}...")
        
        # 测试不同的DPI和批量大小
        configs = [
            {"dpi": 100, "batch_size": 50},
            {"dpi": 150, "batch_size": 100},
            {"dpi": 200, "batch_size": 75}
        ]
        
        config = configs[i % len(configs)]
        # 仍然使用旧的 Processor 进行模拟
        processor = PDFProcessor(**config)
        
        start_time = time.time()
        doc = None
        total_pages = 0
        try:
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
        except Exception as e:
            logger.error(f"无法打开 PDF {pdf_path} 进行基准测试: {e}")
            return results # 无法测试则提前返回
        finally:
             if doc: doc.close()
        
        # 模拟处理，只更新统计
        processor.stats.total_pages = total_pages
        for batch_start in range(0, total_pages, processor.batch_size):
            batch_end = min(batch_start + processor.batch_size, total_pages)
            processor.stats.processed_pages += (batch_end - batch_start)
        
        end_time = time.time()
        run_time = end_time - start_time
        
        # 更新结果
        speed = total_pages / run_time if run_time > 0 else 0
        results['runs'].append({
            'config': config,
            'time': run_time,
            'speed': speed,
            'pages': total_pages
        })
        
        total_time += run_time
        results['best_time'] = min(results['best_time'], run_time)
        results['worst_time'] = max(results['worst_time'], run_time)
        
        print(f"  完成: {_format_time(run_time)}, 速度: {speed:.2f}页/秒")
    
    # 计算平均值
    results['avg_time'] = total_time / repeat
    results['avg_speed'] = total_pages * repeat / total_time if total_time > 0 else 0
    
    print(f"\n==== 性能测试结果 ====")
    print(f"平均处理时间: {_format_time(results['avg_time'])}")
    print(f"平均处理速度: {results['avg_speed']:.2f}页/秒")
    print(f"最佳处理时间: {_format_time(results['best_time'])}")
    print(f"最慢处理时间: {_format_time(results['worst_time'])}")
    
    return results

"""
使用说明:

1. 安装PaddleOCR (可选但推荐):
   - 安装PaddlePaddle CPU版本: pip install paddlepaddle
   - 安装PaddleOCR: pip install paddleocr

2. 基本使用:
   - 默认情况下会自动使用PaddleOCR (如果可用)
   - 如果PaddleOCR不可用，会自动回退使用Tesseract

3. 配置示例:
   a) 使用PaddleOCR并自动检测CPU类型:
      output_files = split_pdf("input.pdf", "代合同")
   
   b) 使用PaddleOCR并指定CPU优化:
      output_files = split_pdf("input.pdf", "代合同", cpu_optimization="intel")
   
   c) 禁用PaddleOCR，使用传统Tesseract:
      output_files = split_pdf("input.pdf", "代合同", use_paddle_ocr=False)
   
   d) 禁用缓存以节省内存:
      output_files = split_pdf("input.pdf", "代合同", enable_cache=False)

4. 性能调优:
   - 对于大文档，考虑调整batch_size参数
   - 对于内存受限环境，设置enable_cache=False
   - 针对Intel或AMD处理器优化，显式设置cpu_optimization参数

5. 测试OCR效果:
   test_ocr_engines("sample_page.jpg", "代合同")
"""

#################################################
# 档案文件命名与存储功能
#################################################

def generate_archive_filename(unified_number=None, original_path=None, timestamp=None):
    """
    生成归档文件的规范化文件名。

    优先使用统一编号，如果提供的话。否则，可以基于原始文件名或时间戳。
    
    Args:
        unified_number (str, optional): 文件的统一编号。
        original_path (str, optional): 原始文件的路径，用于提取文件名和扩展名。
        timestamp (datetime, optional): 文件处理的时间戳。
        
    Returns:
        str: 生成的文件名。
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import generate_archive_filename as new_gen_filename
    return new_gen_filename(unified_number, original_path, timestamp)


def get_archive_storage_path(unified_number=None):
    """
    获取档案存储路径
    
    Args:
        unified_number: 统一编号用于生成子目录
        
    Returns:
        完整存储路径
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import get_archive_storage_path as new_get_path
    return new_get_path(unified_number)


def generate_file_hash(file_path):
    """
    生成文件哈希值
    
    Args:
        file_path: 文件路径
        
    Returns:
        SHA-256哈希值
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import generate_file_hash as new_gen_hash
    return new_gen_hash(file_path)


#################################################
# 档案URL生成与访问控制
#################################################

def create_secure_access_token(file_path, user_id, expires_in=86400):
    """
    创建安全访问令牌
    
    Args:
        file_path: 文件路径
        user_id: 用户ID
        expires_in: 有效期(秒)
        
    Returns:
        安全访问令牌
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/security_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.security_service import create_secure_access_token as new_create_token
    return new_create_token(file_path, user_id, expires_in)


def verify_access_token(token, file_path):
    """
    验证访问令牌
    
    Args:
        token: 访问令牌
        file_path: 文件路径
        
    Returns:
        是否有效
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/security_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.security_service import verify_access_token as new_verify_token
    is_valid, _ = new_verify_token(token, file_path)
    return is_valid


#################################################
# 档案备份与完整性验证
#################################################

def backup_archive_file(file_path, backup_type="daily"):
    """
    备份档案文件
    
    Args:
        file_path: 文件路径
        backup_type: 备份类型
        
    Returns:
        备份文件路径
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import backup_archive_file as new_backup_file
    return new_backup_file(file_path, backup_type)


def verify_file_integrity(file_path, original_hash=None):
    """
    验证文件完整性
    
    Args:
        file_path: 文件路径
        original_hash: 原始哈希值
        
    Returns:
        是否完整
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import verify_file_integrity as new_verify_integrity
    return new_verify_integrity(file_path, original_hash)


#################################################
# 通知与进度跟踪
#################################################

def create_progress_tracker(task_id, total_steps):
    """
    创建进度追踪器
    
    Args:
        task_id: 任务ID
        total_steps: 总步骤数
        
    Returns:
        进度追踪器实例
    """
    # MOVED: [2024-05-05] 此函数已移动到 archive_processing/services/progress_tracking_service.py #AFM-30
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.progress_tracking_service import create_progress_tracker as new_create_tracker
    return new_create_tracker(task_id, total_steps)


def update_progress(task_id, completed_steps, message=None, status=None):
    """
    更新任务进度
    
    Args:
        task_id: 任务ID
        completed_steps: 已完成步骤数
        message: 进度消息
        status: 任务状态
        
    Returns:
        更新后的进度信息
    """
    # MOVED: [2024-05-05] 此函数已移动到 archive_processing/services/progress_tracking_service.py #AFM-30
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.progress_tracking_service import update_progress as new_update_progress
    return new_update_progress(task_id, completed_steps, message, status)


def get_task_progress(task_id):
    """
    获取任务进度
    
    Args:
        task_id: 任务ID
        
    Returns:
        进度信息
    """
    # MOVED: [2024-05-05] 此函数已移动到 archive_processing/services/progress_tracking_service.py #AFM-30
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.progress_tracking_service import get_task_progress as new_get_progress
    return new_get_progress(task_id)


def send_completion_notification(user_id, task_info):
    """
    发送完成通知
    
    Args:
        user_id: 用户ID
        task_info: 任务信息
        
    Returns:
        发送结果
    """
    # MOVED: [2024-05-05] 此函数已移动到 archive_processing/services/progress_tracking_service.py #AFM-30
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.progress_tracking_service import send_completion_notification as new_send_notification
    return new_send_notification(user_id, task_info)

# 在 generate_file_url 函数前后删除
# 1441-1443 行左右

# 前一个函数后面接上注释，表明 generate_file_url 已移除
def generate_archive_storage_path(unified_number):
    """
    根据统一编号生成存档路径
    
    Args:
        unified_number: 统一编号
        
    Returns:
        存档路径
    """
    # MOVED: [2024-04-19] 此函数已移动到 archive_processing/services/file_storage_service.py #AFM-29
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.file_storage_service import generate_archive_storage_path as new_gen_path
    return new_gen_path(unified_number)

# REMOVED: [2024-04-18] 以下函数已移动到 archive_processing/services/record_update_service.py #AFM-13
# - generate_file_url: 已移动到 archive_processing/services/record_update_service.py

# def generate_archive_filename(unified_number=None, original_path=None, timestamp=None):
#     """
#     生成存档文件名
    
#     Args:
#         unified_number: 统一编号
#         original_path: 原始文件路径
#         timestamp: 时间戳
        
#     Returns:
#         新文件名
#     """
#     import os
#     import time
#     import hashlib
    
#     # 处理timestamp
#     if timestamp is None:
#         timestamp = int(time.time())
    
#     # 1. 基于统一编号生成文件名
#     if unified_number:
#         # 简单情况：直接使用统一编号 + .pdf
#         return f"{unified_number}.pdf"
    
#     # 2. 基于原始路径生成文件名
#     if original_path:
#         # 获取基本文件名
#         basename = os.path.basename(original_path)
#         # 分离文件名和扩展名
#         name, ext = os.path.splitext(basename)
#         # 如果没有扩展名，使用 .pdf
#         if not ext:
#             ext = ".pdf"
#         # 生成时间戳和哈希
#         timestamp_str = time.strftime("%Y%m%d-%H%M%S", time.localtime(timestamp))
#         hash_str = hashlib.md5(name.encode('utf-8')).hexdigest()[:8]
#         # 组合新文件名：原文件名_时间戳_哈希.扩展名
#         return f"{name}_{timestamp_str}_{hash_str}{ext}"
    
#     # 3. 如果都没有，生成一个基于时间戳的唯一文件名
#     timestamp_str = time.strftime("%Y%m%d-%H%M%S", time.localtime(timestamp))
#     random_str = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()[:12]
#     return f"archive_{timestamp_str}_{random_str}.pdf"

# REMOVED: [2024-04-18] 以下生成归档文件名的重复定义已移除 #AFM-13
# def generate_archive_filename(unified_number=None, original_path=None, timestamp=None):

def send_task_notification(user_id, notification_type, message, data=None):
    """
    发送任务通知
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        message: 通知内容
        data: 附加数据
        
    Returns:
        发送结果
    """
    # MOVED: [2024-05-05] 此函数已移动到 archive_processing/services/progress_tracking_service.py #AFM-30
    # 为了向后兼容，仍然保留此函数
    from archive_processing.services.progress_tracking_service import send_task_notification as new_send_task_notification
    return new_send_task_notification(user_id, notification_type, message, data)


