# Operation Document: Create Django Management Command for Expired Session Cleanup

## 📋 Change Summary

**Purpose**: 创建一个Django管理命令 (`cleanup_expired_imports`)，用于定期调用 `ImportSessionManager.cleanup_expired_sessions()` 方法，以清理系统中过期的Excel导入会话。
**Scope**: 新建文件 `archive_records/management/commands/cleanup_expired_imports.py`。
**Associated**: 对应《remaining_excel_import_refactor_plan.md》文档中的任务二.1 "实现过期导入会话的自动清理" 的管理命令创建和实现部分。

## 🔧 Operation Steps

### 📊 OP-001: Define Command Structure and Arguments

**Precondition**: `ImportSessionManager.cleanup_expired_sessions()` 方法已存在于服务层。
**Operation**:

1. 规划管理命令的文件路径：`archive_records/management/commands/cleanup_expired_imports.py`。
2. 提示用户确保 `management` 和 `commands` 目录及相应的 `__init__.py` 文件存在。
3. 设计命令类，继承自 `django.core.management.base.BaseCommand`。
4. 定义 `help` 文本。
5. 添加命令行参数：
    * `--dry-run`: 用于模拟执行而不实际修改数据。
    * `-v` / `--verbosity`: 控制输出详细程度。
**Postcondition**: 命令的基本结构和参数定义完成。

### ✏️ OP-002: Implement `handle` Method

**Precondition**: 命令结构和参数已定义。
**Operation**:

1. 在 `handle` 方法中获取 `dry_run` 和 `verbosity` 选项。
2. 实例化 `ImportSessionManager`。
3. 如果不是 `dry_run`：
    * 调用 `session_manager.cleanup_expired_sessions()`。
    * 根据 `verbosity` 输出成功处理的会话数量或无过期会话的消息。
    * 使用 `logger.info` 记录更详细的日志（如果 `verbosity > 1`）。
4. 如果是 `dry_run`：
    * 输出提示信息，说明实际的清理调用已被跳过。
    * (当前简化实现：未在dry-run中模拟查询过期会话数量，以避免复制查询逻辑或要求服务层方法支持dry-run模式。)
5. 添加全局的 `try...except` 块捕获执行过程中的潜在异常，记录错误并通过 `self.stderr.write` 和 `CommandError` 报告。
**Postcondition**: 管理命令的核心逻辑实现完成，能够调用服务层方法并提供适当的输出和日志。

## 📝 Change Details

### CH-001: Create `cleanup_expired_imports.py` Management Command

**File**: `archive_records/management/commands/cleanup_expired_imports.py`
**Content**: (CHANGE: [2025-05-16] Created Django management command for cleaning up expired import sessions)

```python
"""
Django管理命令，用于清理过期的Excel导入会话。
"""
import logging
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from archive_records.services.import_session_manager import ImportSessionManager
# from archive_records.models import ImportSession, ImportSessionStatus # Potentially for advanced dry-run

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Cleans up expired Excel import sessions from the system.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate the cleanup process without actually modifying data.',
        )
        parser.add_argument(
            '-v',
            '--verbosity',
            type=int,
            default=1,
            help='Verbosity level; 0=minimal output, 1=normal output, 2=verbose output, 3=very verbose output'
        )

    def handle(self, *args, **options):
        """执行命令的主要逻辑"""
        dry_run = options['dry_run']
        verbosity = options['verbosity']

        if verbosity > 0:
            self.stdout.write(self.style.SUCCESS('Starting cleanup of expired Excel import sessions...'))
        
        if dry_run:
            if verbosity > 0:
                self.stdout.write(self.style.WARNING('Dry run mode activated. No data will be changed.'))
        
        session_manager = ImportSessionManager()
        
        try:
            if not dry_run:
                cleaned_count = session_manager.cleanup_expired_sessions()
                if verbosity > 0:
                    self.stdout.write(self.style.SUCCESS(f'Successfully processed {cleaned_count} expired session(s) for cleanup.'))
                if verbosity > 1 and cleaned_count > 0:
                    logger.info(f'[ManagementCommand] cleanup_expired_imports: Processed {cleaned_count} sessions.')
                elif verbosity > 1:
                    logger.info('[ManagementCommand] cleanup_expired_imports: No expired sessions found to process.')
            else:
                self.stdout.write(self.style.NOTICE('Dry run: Actual cleanup_expired_sessions() call skipped.'))
                self.stdout.write(self.style.NOTICE('In a real run, session_manager.cleanup_expired_sessions() would be invoked here.'))
                # For a more informative dry run, one might query potentially affected sessions here
                # and report the count, without calling the modifying service method.

            if verbosity > 0:
                self.stdout.write(self.style.SUCCESS('Cleanup process finished.'))

        except Exception as e:
            logger.error(f"[ManagementCommand] Error during cleanup_expired_imports: {str(e)}", exc_info=True)
            self.stderr.write(self.style.ERROR(f'An error occurred: {str(e)}'))
            # It's good practice to raise CommandError for unhandled exceptions in management commands
            # so Django's error reporting mécanismes can catch them if run by automated systems.
            raise CommandError(f'Failed to cleanup expired sessions: {str(e)}') from e

# Ensure the directory structure archive_records/management/commands/ exists
# and __init__.py files are present in archive_records/management/ and archive_records/management/commands/.
```

**Rationale**: 提供一个可通过命令行或定时任务调用的标准化方式来执行过期会话的清理，有助于系统维护和资源管理。
**Potential Impact**: 需要确保 `archive_records/management/` 和 `archive_records/management/commands/` 目录及对应的 `__init__.py` 文件被正确创建，否则命令无法被Django识别。

## ✅ Verification Results

**Method**: 代码创建和初步审查。
**Results**: Django管理命令 `cleanup_expired_imports.py` 已创建，包含基本逻辑和参数。
**Problems**: `dry-run`模式当前仅跳过实际调用，未实现模拟查询。
**Solutions**: 暂无。`dry-run`的增强可作为后续优化。
