#!/usr/bin/env python
"""
档案记录版本管理系统实际数据测试脚本
测试各种情况下的Excel导入和版本管理功能
"""
import os
import sys
import django
import pandas as pd
from datetime import datetime

# 设置Django环境
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 设置Django设置模块（替换为实际的项目名称）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')
django.setup()

# 现在可以导入Django应用的模块
from django.contrib.auth.models import User
from django.utils import timezone
from archive_records.models import (
    ArchiveRecord, ImportLog, ChangeLogBatch, 
    RecordChangeLog, FieldChangeLog
)
from archive_records.services.excel_import import ExcelImportService
from archive_records.views.views import RecordVersionCompareView

class VersionManagementTester:
    def __init__(self):
        # 获取或创建测试用户
        self.user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={'email': '<EMAIL>', 'is_staff': True}
        )
        if created:
            self.user.set_password('password123')
            self.user.save()
            print(f"已创建测试用户: {self.user.username}")
        else:
            print(f"使用已存在的测试用户: {self.user.username}")
        
        self.import_service = ExcelImportService()
        self.test_files_dir = os.path.join(project_root, 'dev_tools', 'test_files')
        os.makedirs(self.test_files_dir, exist_ok=True)
    
    def create_test_file(self, data, filename):
        """创建测试Excel文件"""
        file_path = os.path.join(self.test_files_dir, filename)
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False)
        print(f"已创建测试文件: {file_path}")
        return file_path
    
    def test_initial_import(self):
        """测试首次导入"""
        print("\n=== 测试一：首次导入 ===")
        
        # 准备导入数据
        initial_data = {
            '样品编号': ['S001', 'S002', 'S003'],
            '委托编号': ['C2023-001', 'C2023-002', 'C2023-003'],
            '委托日期': ['2023-01-01', '2023-01-02', '2023-01-03'],
            '工程名称': ['测试项目1', '测试项目2', '测试项目3'],
            '委托单位': ['测试单位1', '测试单位2', '测试单位3'],
            '结论': ['测试结论1', '测试结论2', '测试结论3'],
            '试验开始日期': ['2023-01-05', '2023-01-06', '2023-01-07'],
            '试验结束日期': ['2023-01-10', '2023-01-11', '2023-01-12'],
            '试验人1': ['张三', '李四', '王五'],
            '试验人2': ['赵六', '钱七', '孙八'],
            '数据录入人': ['录入员A', '录入员B', '录入员C'],
            '检测结果': ['合格', '合格', '不合格'],
            '检测参数': ['参数集A', '参数集B', '参数集C'],
            '不合格参数': ['', '', '参数3'],
            '报告归档状态': ['已归档', '未归档', '未归档'],
            '入库人': ['管理员', '管理员', '管理员']
        }
        
        file_path = self.create_test_file(initial_data, 'initial_import.xlsx')
        print(f"已创建测试文件: {file_path}")
        
        # 检查文件是否已存在相同样品编号的记录
        existing_records = ArchiveRecord.objects.filter(
            sample_number__in=initial_data['样品编号']
        )
        if existing_records.exists():
            print(f"⚠️ 警告: 数据库中已存在 {existing_records.count()} 条样品编号相同的记录:")
            for record in existing_records:
                print(f"  ID: {record.id}, 样品编号: {record.sample_number}, 委托编号: {record.commission_number}")
        
        try:
            # 添加查询以显示导入前记录数
            before_count = ArchiveRecord.objects.count()
            print(f"导入前记录总数: {before_count}")
            
            # 修改策略选择逻辑，使用更符合业务的策略
            if existing_records.exists():
                # 如果记录已存在，使用skip策略避免重复
                strategy = 'skip'
                print(f"检测到已存在记录，使用策略: {strategy}（跳过已存在记录）")
            else:
                # 如果记录不存在，可以用默认策略
                strategy = 'smart_update'  # 这里用什么都可以，因为没有重复记录
                print(f"没有检测到已存在记录，使用策略: {strategy}")
            
            import_log = self.import_service.import_from_file(
                file_path, 
                user=self.user,
                duplicate_strategy=strategy
            )
            
            print(f"导入状态: {import_log.status}")
            print(f"成功记录数: {import_log.success_records}")
            print(f"错误记录数: {import_log.failed_records}")
            print(f"创建记录: {import_log.created_count}, 更新记录: {import_log.updated_count}, 跳过记录: {import_log.system_skipped_records}")
            
            # 添加导入日志检查
            print("\n导入结果详情:")
            if hasattr(import_log, 'detailed_report') and import_log.detailed_report:
                detailed_report = import_log.detailed_report
                if isinstance(detailed_report, str):
                    try:
                        import json
                        detailed_report = json.loads(detailed_report)
                    except:
                        pass
                
                if isinstance(detailed_report, dict):
                    for key, value in detailed_report.items():
                        print(f"  {key}: {value if not isinstance(value, list) else len(value)} 条记录")
            
            if hasattr(import_log, 'error_log') and import_log.error_log:
                print(f"错误日志: {import_log.error_log}")
            
            # 验证记录创建
            after_count = ArchiveRecord.objects.count()
            new_records_count = after_count - before_count
            print(f"导入后记录总数: {after_count}")
            print(f"新增记录数: {new_records_count}")
            
            records = ArchiveRecord.objects.filter(
                sample_number__in=initial_data['样品编号']
            ).order_by('created_at')
            print(f"查询到的记录数: {records.count()}")
            
            # 验证版本记录
            for i, record in enumerate(records[:5]):  # 只显示前5条，避免太多输出
                versions = RecordChangeLog.objects.filter(record=record)
                print(f"记录 {record.sample_number} (ID={record.id}) 版本数: {versions.count()}")
                for v in versions:
                    print(f"  版本 {v.version_number}, 类型: {v.change_type}, 时间: {v.batch.changed_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if records.count() != len(initial_data['样品编号']) and strategy != 'skip':
                print("⚠️ 警告: 创建的记录数与预期不符")
            elif import_log.status == 'failed' and records.count() > 0:
                print("⚠️ 警告: 导入状态为失败，但仍创建了记录")
            
            return records
            
        except Exception as e:
            print(f"首次导入测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def test_update_import(self, records):
        """测试更新导入"""
        print("\n=== 测试二：更新导入 ===")
        
        # 先检查现有记录的状态
        sample_numbers = [record.sample_number for record in records[:1]]
        print(f"即将更新的样品编号: {sample_numbers}")
        
        # 检查记录更新前的状态
        original_records = {}
        print("\n更新前的记录状态:")
        for sample_number in sample_numbers:
            existing_record = ArchiveRecord.objects.filter(sample_number=sample_number).first()
            if existing_record:
                original_records[sample_number] = existing_record
                print(f"  记录 {sample_number} 更新前数据:")
                print(f"    ID: {existing_record.id}")
                print(f"    委托编号: {existing_record.commission_number}")
                print(f"    工程名称: {existing_record.project_name}")
                print(f"    委托单位: {existing_record.client_unit}")
                print(f"    结论: {existing_record.conclusion}")
                print(f"    版本数: {RecordChangeLog.objects.filter(record=existing_record).count()}")
            else:
                print(f"  找不到样品编号为 {sample_number} 的记录")
        
        # 准备更新数据 - 确保使用完全相同的委托编号，但可以改变样品编号
        update_data = {
            '样品编号': [original_records[num].sample_number for num in sample_numbers if num in original_records],  # 保持样品编号相同
            '委托编号': [original_records[num].commission_number for num in sample_numbers if num in original_records],  # 保持委托编号相同
            '委托日期': ['2023-01-05' for _ in range(len(sample_numbers))],
            '工程名称': ['更新后的项目名称' for _ in range(len(sample_numbers))],
            '委托单位': ['更新后的委托单位' for _ in range(len(sample_numbers))],
            '结论': ['更新后的结论' for _ in range(len(sample_numbers))]
        }
        
        # 确保具备必需字段，防止验证失败
        # 检查第一条记录必需字段
        required_fields = ['委托日期', '试验开始日期', '试验结束日期']
        for field in required_fields:
            if field not in update_data and sample_numbers and sample_numbers[0] in original_records:
                orig_record = original_records[sample_numbers[0]]
                field_value = getattr(orig_record, field.replace('委托日期', 'commission_datetime').replace('试验开始日期', 'test_start_datetime').replace('试验结束日期', 'test_end_datetime'), None)
                if field_value:
                    update_data[field] = [field_value.strftime('%Y-%m-%d')] * len(sample_numbers)
        
        file_path = self.create_test_file(update_data, 'update_test.xlsx')
        print(f"\n创建更新测试文件: {file_path}")
        
        # 保存原始IDs用于后续比较
        original_ids = {sample_number: original_records[sample_number].id 
                       for sample_number in sample_numbers if sample_number in original_records}
        
        try:
            # 记录更新前的版本数
            before_versions = {}
            for sample_number in sample_numbers:
                if sample_number in original_records:
                    record = original_records[sample_number]
                    before_versions[sample_number] = RecordChangeLog.objects.filter(record=record).count()
            
            # 打印导入策略
            print(f"\n使用导入策略: smart_update")
            print(f"导入数据: {update_data}")
            
            # 使用smart_update策略进行更新
            import_log = self.import_service.import_from_file(
                file_path, 
                user=self.user,
                duplicate_strategy='smart_update'  # 明确使用smart_update策略
            )
            
            print(f"\n导入结果:")
            print(f"  状态: {import_log.status}")
            print(f"  总记录数: {import_log.total_records}")
            print(f"  处理记录数: {import_log.processed_records}")
            print(f"  成功记录数: {import_log.success_records}")
            print(f"  失败记录数: {import_log.failed_records}")
            print(f"  创建记录数: {import_log.created_count}")
            print(f"  更新记录数: {import_log.updated_count}")
            print(f"  跳过记录数: {import_log.system_skipped_records}")
            
            # 检查详细报告
            if hasattr(import_log, 'detailed_report') and import_log.detailed_report:
                print("\n详细报告:")
                detailed_report = import_log.detailed_report
                if isinstance(detailed_report, str):
                    try:
                        import json
                        detailed_report = json.loads(detailed_report)
                    except:
                        pass
                
                print(f"详细报告类型: {type(detailed_report)}")
                print(f"详细报告内容: {detailed_report}")
                
                if isinstance(detailed_report, dict):
                    # 打印成功记录
                    success_details = detailed_report.get('success', [])
                    if success_details:
                        print(f"  成功记录: {len(success_details)}")
                        for i, detail in enumerate(success_details[:3]):  # 只显示前3条
                            print(f"    - {detail}")
                        if len(success_details) > 3:
                            print(f"    ... 还有 {len(success_details) - 3} 条")
                    
                    # 打印失败记录
                    error_details = detailed_report.get('errors', [])
                    if error_details:
                        print(f"  失败记录: {len(error_details)}")
                        for i, detail in enumerate(error_details[:5]):  # 显示前5条错误
                            print(f"    - {detail}")
                        if len(error_details) > 5:
                            print(f"    ... 还有 {len(error_details) - 5} 条")
            
            # 检查错误日志
            if import_log.error_log:
                print("\n错误日志:")
                print(import_log.error_log)
            
            # 验证记录更新 - 修改此部分以检测问题
            print("\n更新后的记录状态:")
            
            # 先通过ID查找
            for sample_number, orig_id in original_ids.items():
                try:
                    record_by_id = ArchiveRecord.objects.get(id=orig_id)
                    print(f"  记录ID={orig_id} ({sample_number}) 状态:")
                    print(f"    样品编号: {record_by_id.sample_number}")
                    print(f"    工程名称: {record_by_id.project_name}")
                    print(f"    委托单位: {record_by_id.client_unit}")
                    print(f"    结论: {record_by_id.conclusion}")
                    
                    # 检查版本
                    versions = RecordChangeLog.objects.filter(record=record_by_id).order_by('version_number')
                    print(f"    版本数: {versions.count()} (更新前: {before_versions.get(sample_number, '未知')})")
                    # 检查是否确实更新了
                    was_updated = (record_by_id.project_name == '更新后的项目名称' and 
                                  record_by_id.client_unit == '更新后的委托单位' and
                                  record_by_id.conclusion == '更新后的结论')
                    print(f"    是否成功更新: {'是' if was_updated else '否'}")
                except ArchiveRecord.DoesNotExist:
                    print(f"  原始记录ID={orig_id} ({sample_number}) 不存在!")
            
            # 总结
            duplicates = [r for r in records if r.id not in original_ids.values()]
            if duplicates:
                print(f"\n⚠️ 检测到 {len(duplicates)} 条重复创建的记录而不是更新现有记录!")
                print("可能的原因: 导入时没有正确识别现有记录，特别检查委托编号是否完全匹配")
            
            if import_log.success_records == 0 and import_log.updated_count == 0:
                print("\n⚠️ 警告: 导入日志显示无成功记录，可能是导入过程有问题")
                print("建议检查: ExcelImportService中的重复识别逻辑和错误处理")
            
            # 检查是否至少有一个记录被成功更新
            any_updated = False
            for record in ArchiveRecord.objects.filter(id__in=original_ids.values()):
                if (record.project_name == '更新后的项目名称' and 
                    record.client_unit == '更新后的委托单位' and
                    record.conclusion == '更新后的结论'):
                    any_updated = True
                    break
            
            if any_updated:
                print("\n✅ 部分记录更新成功，但导入日志可能有问题")
            else:
                print("\n❌ 更新导入测试失败: 没有记录被成功更新")
        
        except Exception as e:
            print(f"\n❌ 更新导入测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def test_commission_number_update(self):
        """测试委托编号更新同步统一编号"""
        print("\n=== 测试三：委托编号更新同步统一编号 ===")
        
        # 创建数据
        data = {
            '样品编号': ['S100'],
            '委托编号': ['C2023-100'],
            '委托日期': ['2023-01-10'],
            '工程名称': ['委托编号测试项目'],
            '委托单位': ['委托编号测试单位'],
            '结论': ['通过']
        }
        
        file_path = self.create_test_file(data, 'commission_test_initial.xlsx')
        
        try:
            # 首次导入
            import_log = self.import_service.import_from_file(
                file_path, 
                user=self.user,
                duplicate_strategy='skip'
            )
            
            # 获取创建的记录
            records = ArchiveRecord.objects.filter(sample_number='S100')
            if records.exists():
                record = records.order_by('-id').first()  # 获取最新创建的记录
                print(f"找到记录数量: {records.count()}, 使用最新记录ID: {record.id}")
            else:
                print("没有找到匹配的记录")
                return
            
            original_commission = record.commission_number
            original_unified = record.unified_number
            
            print(f"原始委托编号: {original_commission}")
            print(f"原始统一编号: {original_unified}")
            
            # 创建更新数据 - 修改委托编号
            update_data = {
                '样品编号': ['S100'],
                '委托编号': ['C2023-100-UPDATED'],  # 更新后的委托编号
                '委托日期': ['2023-01-10'],
                '工程名称': ['委托编号测试项目'],
                '委托单位': ['委托编号测试单位'],
                '结论': ['通过']
            }
            
            update_file_path = self.create_test_file(update_data, 'commission_test_update.xlsx')
            
            # 执行更新
            update_log = self.import_service.import_from_file(
                update_file_path, 
                user=self.user,
                duplicate_strategy='smart_update'
            )
            
            # 验证更新结果
            record.refresh_from_db()
            updated_commission = record.commission_number
            updated_unified = record.unified_number
            
            print(f"更新后委托编号: {updated_commission}")
            print(f"更新后统一编号: {updated_unified}")
            
            # 验证统一编号是否跟随委托编号更新
            if updated_unified == updated_commission:
                print("✅ 统一编号成功同步更新")
            else:
                print("❌ 统一编号未同步更新")
            
            # 验证版本历史
            versions = RecordChangeLog.objects.filter(record=record).order_by('version_number')
            latest_version = versions.last()
            print(f"最新版本号: {latest_version.version_number}")
            
            # 查看字段变更
            commission_changes = FieldChangeLog.objects.filter(
                record_change=latest_version,
                field_name='commission_number'
            )
            unified_changes = FieldChangeLog.objects.filter(
                record_change=latest_version,
                field_name='unified_number'
            )
            
            if commission_changes.exists():
                print(f"委托编号变更: {commission_changes[0].old_value} -> {commission_changes[0].new_value}")
            
            if unified_changes.exists():
                print(f"统一编号变更: {unified_changes[0].old_value} -> {unified_changes[0].new_value}")
            
        except Exception as e:
            print(f"委托编号更新测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def test_rollback(self):
        """测试版本回滚"""
        print("\n=== 测试四：版本回滚 ===")
        
        # 创建初始数据
        initial_data = {
            '样品编号': ['S200'],
            '委托编号': ['C2023-200'],
            '委托日期': ['2023-01-20'],
            '工程名称': ['回滚测试初始项目'],
            '委托单位': ['回滚测试初始单位'],
            '结论': ['初始结论']
        }
        
        file_path = self.create_test_file(initial_data, 'rollback_test_initial.xlsx')
        
        try:
            # 首次导入
            self.import_service.import_from_file(
                file_path, 
                user=self.user,
                duplicate_strategy='skip'
            )
            
            # 获取创建的记录
            record = ArchiveRecord.objects.get(sample_number='S200')
            print(f"初始记录: {record.project_name}, {record.client_unit}, {record.conclusion}")
            
            # 创建第一次更新数据
            update1_data = {
                '样品编号': ['S200'],
                '委托编号': ['C2023-200'],
                '委托日期': ['2023-01-20'],
                '工程名称': ['回滚测试更新项目1'],
                '委托单位': ['回滚测试更新单位1'],
                '结论': ['更新结论1']
            }
            
            update1_path = self.create_test_file(update1_data, 'rollback_test_update1.xlsx')
            
            # 执行第一次更新
            self.import_service.import_from_file(
                update1_path, 
                user=self.user,
                duplicate_strategy='smart_update'
            )
            
            # 创建第二次更新数据
            update2_data = {
                '样品编号': ['S200'],
                '委托编号': ['C2023-200'],
                '委托日期': ['2023-01-20'],
                '工程名称': ['回滚测试更新项目2'],
                '委托单位': ['回滚测试更新单位2'],
                '结论': ['更新结论2']
            }
            
            update2_path = self.create_test_file(update2_data, 'rollback_test_update2.xlsx')
            
            # 执行第二次更新
            self.import_service.import_from_file(
                update2_path, 
                user=self.user,
                duplicate_strategy='smart_update'
            )
            
            # 验证更新后状态
            record.refresh_from_db()
            print(f"更新后记录: {record.project_name}, {record.client_unit}, {record.conclusion}")
            
            # 获取版本信息
            versions = RecordChangeLog.objects.filter(record=record).order_by('version_number')
            print(f"版本数量: {versions.count()}")
            for v in versions:
                print(f"版本 {v.version_number}, 类型: {v.change_type}, 时间: {v.batch.changed_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 执行回滚到版本1
            from archive_records.views.views import RecordVersionRollbackView
            from rest_framework.test import APIRequestFactory
            from rest_framework.request import Request
            
            factory = APIRequestFactory()
            request = factory.post(f'/api/records/{record.id}/rollback/1/')
            request.user = self.user
            view = RecordVersionRollbackView()
            view.setup(request, record_id=record.id, version_number=1)
            
            response = view.post(request, record_id=record.id, version_number=1)
            print(f"回滚响应: {response.status_code}, {response.data}")
            
            # 验证回滚后状态
            record.refresh_from_db()
            print(f"回滚后记录: {record.project_name}, {record.client_unit}, {record.conclusion}")
            
            # 验证版本历史
            versions = RecordChangeLog.objects.filter(record=record).order_by('version_number')
            print(f"回滚后版本数量: {versions.count()}")
            
            latest_version = versions.last()
            print(f"最新版本: {latest_version.version_number}, 类型: {latest_version.change_type}")
            
            # 验证最新版本的回滚标记
            print(f"是否为回滚版本: {latest_version.is_rollback}")
            print(f"回滚源版本: {latest_version.rollback_source_version}")
            
        except Exception as e:
            print(f"回滚测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def test_version_compare(self):
        """测试版本比较功能"""
        print("\n=== 测试五：版本比较 ===")
        
        # 从测试四(回滚测试)获取记录
        test_record = ArchiveRecord.objects.filter(sample_number='S200').first()
        if not test_record:
            print("❌ 版本比较测试失败: 找不到测试记录")
            return
        
        print(f"使用记录: {test_record.sample_number}, ID: {test_record.id}")
        
        try:
            # 获取记录的版本
            versions = RecordChangeLog.objects.filter(record=test_record).order_by('version_number')
            if versions.count() < 2:
                print(f"❌ 版本比较测试失败: 记录 {test_record.sample_number} 没有足够的版本进行比较")
                return
            
            print(f"记录 {test_record.sample_number} 有 {versions.count()} 个版本")
            
            # 选择要比较的版本
            v1 = 1
            v2 = versions.count()  # 最新版本
            
            print(f"比较版本 {v1} 和版本 {v2}")
            
            # 直接使用RecordVersionCompareView的比较逻辑
            from archive_records.views.views import RecordVersionCompareView
            
            # 获取要比较的版本记录
            v1_record = RecordChangeLog.objects.get(
                record_id=test_record.id, 
                version_number=v1
            )
            
            v2_record = RecordChangeLog.objects.get(
                record_id=test_record.id, 
                version_number=v2
            )
            
            # 创建视图实例并直接调用比较方法
            view = RecordVersionCompareView()
            differences = view._compare_versions(v1_record, v2_record)
            
            print(f"差异字段数: {len(differences)}")
            
            for diff in differences[:5]:  # 显示前5个差异
                print(f"  字段: {diff.get('field_label', diff.get('field'))}")
                print(f"    版本{v1}值: {diff.get('v1_value')}")
                print(f"    版本{v2}值: {diff.get('v2_value')}")
            
            print("✅ 版本比较测试成功")
            
        except Exception as e:
            print(f"❌ 版本比较测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def test_force_create_strategy(self):
        """专门测试force_create策略 - 即使记录已存在也创建新记录"""
        print("\n=== 测试：强制创建重复记录 ===")
        
        # 先创建一条记录
        initial_data = {
            '样品编号': ['S100'],
            '委托编号': ['C2023-100'],
            '委托日期': ['2023-01-01'],
            '工程名称': ['测试项目'],
            '委托单位': ['测试单位'],
            '结论': ['通过']
        }
        
        initial_file_path = self.create_test_file(initial_data, 'force_create_initial.xlsx')
        
        # 先执行一次导入
        first_import = self.import_service.import_from_file(
            initial_file_path, 
            user=self.user,
            duplicate_strategy='skip'
        )
        
        # 检查记录是否成功创建
        records = ArchiveRecord.objects.filter(sample_number='S100')
        if not records.exists():
            print("❌ 测试失败: 没有成功创建初始记录")
            return
        
        # 记录初始ID
        initial_id = records.first().id
        print(f"初始记录创建成功, ID: {initial_id}")
        
        # 使用相同数据强制创建新记录
        force_import = self.import_service.import_from_file(
            initial_file_path, 
            user=self.user,
            duplicate_strategy='force_create'  # 明确使用force_create
        )
        
        # 验证是否创建了新记录而不是更新
        updated_records = ArchiveRecord.objects.filter(sample_number='S100')
        if updated_records.count() > 1:
            print(f"✅ 测试成功: 强制创建了新记录，共有 {updated_records.count()} 条S100记录")
            for r in updated_records:
                print(f"  记录 ID: {r.id}, 委托编号: {r.commission_number}")
        else:
            print("❌ 测试失败: 没有强制创建新记录")

    def run_all_tests(self, skip_force_create=True):
        """运行所有测试（跳过强制创建模式测试）
        
        Args:
            skip_force_create: 是否跳过强制创建模式测试
        """
        print("开始执行实际数据测试...\n")
        
        # 执行测试一：首次导入
        records = self.test_initial_import()
        
        # 执行测试二：更新导入
        self.test_update_import(records)
        
        # 执行测试三：委托编号更新同步统一编号
        self.test_commission_number_update()
        
        # 执行测试四：版本回滚
        self.test_rollback()
        
        # 执行测试五：版本比较
        self.test_version_compare()
        
        # 执行测试六：强制创建重复记录（可选）
        if not skip_force_create:
            self.test_force_create_strategy()
        else:
            print("\n跳过强制创建模式测试")
        
        print("\n所有测试执行完毕！")

if __name__ == "__main__":
    tester = VersionManagementTester()
    tester.run_all_tests()