# AG Grid v33 升级指南

本文档详细记录了从旧版本（特别是 v31 及更早版本）升级到 AG Grid v33 的关键变更点、API 修改、新特性以及迁移注意事项，为项目中的表格组件升级提供参考。

## 1. v33 重大变更概述

AG Grid v33 引入了多项重大变更，主要包括：

- **模块与包结构调整**: 统一了社区版和企业版包，简化了导入。
- **主题系统更新**: 引入基于 JavaScript 对象的新主题 API。
- **API 方法命名调整与弃用**: 许多 API 进行了调整，特别是倾向于批量操作。
- **其他行为变更与性能改进**。

## 2. v33 模块与包结构变更

v33 版本统一了模块和包的结构，以简化配置并减小打包体积。

- **包替换**:
  - 所有 `@ag-grid-community/**` 包（除了 `@ag-grid-community/locale`）合并到 `ag-grid-community`。
  - 所有 `@ag-grid-enterprise/**` 包合并到 `ag-grid-enterprise`。
  - `@ag-grid-community/angular` 被替换为 `ag-grid-angular`。
- **迁移**:
  - 更新 `package.json` 中的依赖项。
  - 使用官方 Codemod 工具可以自动更新导入路径：

      ```bash
      npx @ag-grid-devtools/cli@33.0 migrate --from=<your_previous_ag_grid_version>
      ```

**示例 `package.json` 变更 (从 v32 到 v33 企业版):**

```diff
  "dependencies": {
-    "@ag-grid-community/angular": "^32.x.x",
-    "@ag-grid-community/client-side-row-model": "^32.x.x", // 示例，具体模块视项目而定
-    "@ag-grid-enterprise/master-detail": "^32.x.x",      // 示例
-    "@ag-grid-enterprise/row-grouping": "^32.x.x",       // 示例
+    "ag-grid-angular": "~33.x.x",
+    "ag-grid-enterprise": "~33.x.x", // 包含了所有企业版模块，可摇树优化
}
```

- **模块注册**: 虽然包结构改变，但模块注册的方式 (`ModuleRegistry.registerModules([...])`) 保持不变。推荐使用 `AllEnterpriseModule` (或 `AllCommunityModules`)，或者根据项目特性精确导入所需模块（参考官方 Module Selector）。

## 3. v33 主题系统变更

v33 引入了新的基于 JavaScript 的主题 API，允许通过 `theme` 属性传递主题对象，取代了旧的在父 `div` 上设置 CSS 类名的方式。

- **旧方式 (v32 及之前)**:

  ```typescript
  import 'ag-grid-enterprise/styles/ag-theme-quartz.css';
  // ...
  <div className="ag-theme-quartz" style={{ height: 400 }}>
    <AgGridReact {...} />
  </div>
  ```

- **新方式 (v33+)**:

  ```typescript
  import { themeQuartz } from 'ag-grid-enterprise'; // 或其他基础主题对象
  // ...
  <div style={{ height: 400 }}> {/* 不再需要主题类名 */}
    <AgGridReact theme={themeQuartz} {...} /> {/* 通过 theme 属性设置 */}
  </div>
  ```

- **项目实践**:
  - 项目在 `frontend/lib/ag-grid-config.ts` 中定义并导出了自定义主题对象 `agGridConfig.themes.quartzCustom`。
  - **推荐**: 所有表格组件都应使用 `theme={agGridConfig.themes.quartzCustom}`。
  - **必须**: 移除包裹 `AgGridReact` 的 `div` 上的 `ag-theme-xxx` CSS 类名。
  - **不再需要**: 导入 AG Grid 的 CSS 主题文件 (如 `ag-theme-quartz.css`)，因为主题样式由 JavaScript 动态注入。

## 4. v33 API 方法与接口主要变更

v33 对 API 进行了多项调整，以下是一些关键点：

- **批量操作 API**: v33 倾向于使用批量操作 API，并移除了许多针对单个列、过滤器等的 API 方法。
  - 例如：`api.moveColumn(key, toIndex)` 移除，使用 `api.moveColumns([key], toIndex)`。
  - 例如：`api.getColumnFilterInstance(key)` 移除，使用 `api.getColumnFilterModel(key)` / `api.setColumnFilterModel(key, model)`。
- **GridOptions 移除/弃用**:
  - `advancedFilterModel`: 移除，使用 `initialState.filter.advancedFilterModel`。
  - `suppressAsyncEvents`: 移除，事件应异步处理。
  - `cellFlashDelay`, `cellFadeDelay`: 移除，使用 `cellFlashDuration`, `cellFadeDuration`。
  - `enableCellChangeFlash`: 移除，应在 `ColDef` 中设置。
  - `groupIncludeFooter`, `groupIncludeTotalFooter`: 移除，使用 `groupTotalRow`, `grandTotalRow`。
  - `suppressPropertyNamesCheck`: **弃用且无替代品**。应使用 `context` 属性在 `gridOptions` 或 `columnDefs` 中传递自定义元数据。
  - `rowSelection="single/multiple"`: 弃用，使用对象格式 `rowSelection={{ mode: 'singleRow/multiRow', ... }}`。
  - `suppressRowClickSelection`: 弃用，使用 `rowSelection.enableClickSelection = false`。
  - 更多变更请查阅官方 v33 升级文档。
- **ColDef 移除/弃用**:
  - `suppressCellFlash`: 移除，使用 `enableCellChangeFlash: false`。
  - `columnsMenuParams`: 移除，使用 `columnChooserParams`。
  - `suppressMenu`: 移除，使用 `suppressHeaderMenuButton`。
- **浮动过滤器 (Floating Filters)**: 旧的过滤器名称 (如 `text`, `number`) 不再有效，必须使用带 `ag` 前缀的新名称 (如 `agTextColumnFilter`, `agNumberColumnFilter`)。
- **模块弃用**:
  - `MenuModule` 弃用，使用 `ColumnMenuModule` 和/或 `ContextMenuModule`。
  - `RangeSelectionModule` 弃用，使用 `CellSelectionModule`。

## 5. 升级实施建议 (至 v33)

1. **更新依赖**: 修改 `package.json` 以使用 v33 的包结构 (`ag-grid-community`, `ag-grid-enterprise`, `ag-grid-angular`)。
2. **使用官方 Codemod 工具**: 运行针对 v33 的 Codemod 工具来自动迁移大部分代码。

    ```bash
    npx @ag-grid-devtools/cli@33.0 migrate --from=<your_previous_ag_grid_version>
    ```

    *如果您从 v31 之前的版本升级，可能需要分步迁移或检查 v31 的 Codemod。*
3. **手动修复关键代码**:
    - **主题**: 更新主题应用方式（移除 CSS 类名，使用 `theme` 属性）。
    - **导入**: 检查并更新所有 AG Grid 相关的导入路径。
    - **API 调用**: 根据 v33 的 API 变更（特别是批量操作和移除的属性）更新代码。
    - **列定义**: 检查 `ColDef` 是否使用了已移除/弃用的属性。
4. **处理控制台警告**: 在开发模式下，启用 AG Grid 的 `debug=true` 属性查看详细警告，并逐一修复。
5. **类型修复**: 更新 TypeScript 类型引用和断言，确保与 v33 的接口兼容。

### 常见迁移示例

#### 行选择配置迁移

```typescript
// 旧格式 (v32 之前)
<AgGridReact
  rowSelection="single"
  suppressRowClickSelection={true}
  // ...
/>

// 新格式 (v32.2+，v33 强制)
<AgGridReact
  rowSelection={{
    mode: 'singleRow',       // 替代 rowSelection="single"
    enableClickSelection: false  // 替代 suppressRowClickSelection={true}
  }}
  // ...
/>
```

### 推荐配置模式 (v33+)

```typescript
// AG Grid v33+ 推荐配置示例 (假设使用中央配置)
import agGridConfig from '@/lib/ag-grid-config'; 

<AgGridReact
  // 基本配置
  rowModelType="serverSide" // 或 "clientSide"
  theme={agGridConfig.themes.quartzCustom} // 使用项目定义的自定义主题对象

  // 从中央配置应用默认设置
  columnDefs={/* ... */}
  defaultColDef={agGridConfig.defaultColDef}
  {...agGridConfig.performanceConfig} // 应用性能相关的默认配置
  cacheBlockSize={agGridConfig.serverSideDefaults.cacheBlockSize} // 示例：服务器端缓存大小
  maxBlocksInCache={agGridConfig.serverSideDefaults.maxBlocksInCache} // 示例

  // ... 其他特定于组件的 AG Grid 配置
/>
```

*请确保 `ag-grid-config.ts` 中的配置与 v33 兼容。*

## 6. 参考资源

- [AG Grid v33 升级指南 (官方)](https://www.ag-grid.com/javascript-data-grid/upgrading-to-ag-grid-33/)
- [AG Grid v33 模块文档](https://www.ag-grid.com/javascript-data-grid/modules/)
- [AG Grid v33 主题文档](https://www.ag-grid.com/javascript-data-grid/themes/)
- [AG Grid Changelog (查找 AG-XXXX 参考)](https://www.ag-grid.com/ag-grid-changelog/)
- [服务端行模型文档](https://www.ag-grid.com/javascript-data-grid/server-side-model/)
- [网格选项与 API 文档](https://www.ag-grid.com/javascript-data-grid/grid-interface/)
