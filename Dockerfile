# 使用官方 Python 运行时作为父镜像
FROM python:3.11

# 设置环境变量，防止 Python 写入 .pyc 文件
ENV PYTHONDONTWRITEBYTECODE=1
# 设置环境变量，确保 Python 输出是无缓冲的
ENV PYTHONUNBUFFERED=1

# CHANGE: [2025-06-19] 添加中文字符集支持和locale设置
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# CHANGE: [2025-06-19] 强制所有AI库使用CPU模式，避免GPU相关问题
ENV CUDA_VISIBLE_DEVICES=""
ENV PADDLEPADDLE_USE_GPU=0
ENV USE_CUDA=0

# 设置工作目录
WORKDIR /app

# 安装系统依赖 (例如 OpenCV, GLib 和其他库可能需要的)
# 更新软件包列表，安装所需库, 然后清理 apt 缓存
# 注意：使用 \ 进行换行以提高可读性
RUN apt-get update && apt-get install -y --no-install-recommends \
    # CHANGE: [2025-06-19] 添加中文字符集支持
    locales \
    # CHANGE: [2025-04-20] 添加 OpenCV 依赖
    libgl1-mesa-glx \
    # CHANGE: [2025-04-20] 添加 GLib 依赖
    libglib2.0-0 \
    # CHANGE: [2025-04-20] 添加 Poppler 依赖用于 pdf2image
    poppler-utils \
    # CHANGE: [2025-04-20] 添加 MuPDF 依赖用于 PyMuPDF
    libmupdf-dev \
    # CHANGE: [2025-04-20] 添加构建依赖和图像处理库
    build-essential \
    libffi-dev \
    libjpeg-dev \
    zlib1g-dev \
    # CHANGE: [2025-04-20] 添加 ccache 依赖
    ccache \
    # CHANGE: [2025-04-20] 暂时不安装 Tesseract OCR
    # tesseract-ocr \
    # tesseract-ocr-chi-sim \
    && rm -rf /var/lib/apt/lists/*

# CHANGE: [2025-06-19] 配置UTF-8 locale支持
RUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen

# 安装 Python 依赖
# 首先复制 requirements.txt 并安装，以便利用 Docker 的层缓存
COPY requirements.txt /app/

# 安装依赖
# CHANGE: [2025-06-26] 解决pip版本警告
RUN python -m pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# CHANGE: [2025-06-26] 强制清理旧的PaddleOCR模型缓存，以防损坏 #AFM-fix-paddle-crash
RUN rm -rf /root/.paddleocr

# 预下载 PaddleOCR 模型 (通过实例化触发中英文模型下载)
# CHANGE: [2024-07-26] 添加 PaddleOCR 模型预下载步骤，增加重试机制
# CHANGE: [2025-01-12] 使用独立的下载脚本以解决网络连接问题
COPY scripts/download_paddleocr_models.py /tmp/download_paddleocr_models.py
RUN python /tmp/download_paddleocr_models.py || echo "PaddleOCR models download completed or skipped, will retry at runtime"

# CHANGE: [2025-06-29] 创建非root用户以提高安全性，支持开发环境权限配置
# 允许通过构建参数传入用户ID，默认为1000
ARG USER_ID=1000
ARG GROUP_ID=1000

# 创建用户组和用户，使用传入的ID或默认值
# CHANGE: [2025-06-30] 修复用户创建逻辑，确保为appuser创建主目录
# 使用 -m 参数创建主目录 /home/<USER>
RUN groupadd -g $GROUP_ID appuser && \
    useradd -ms /bin/bash -d /home/<USER>

# 复制项目代码到工作目录
COPY . /app/

# CHANGE: [2025-06-30] 确保/app和/home/<USER>
RUN chown -R appuser:appuser /app && \
    chown -R appuser:appuser /home/<USER>
USER appuser

# （可选）如果 Django 应用需要运行数据库迁移
# RUN python manage.py migrate

# 默认暴露 Django 服务的端口 (如果 web 服务也使用此 Dockerfile)
# EXPOSE 8000

# 默认启动命令设置为运行 Celery worker
# 使用 exec 形式确保信号能正确传递
CMD ["celery", "-A", "archive_flow_manager", "worker", "--loglevel=info"] 