"use client"

import { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { SessionInfoData } from '@/services/domain/records/import/excel-import-service';

// 简化的活跃会话提示上下文类型
interface ActiveSessionNotificationContextType {
  // 模态框状态
  isModalVisible: boolean;
  activeSessionData: SessionInfoData | null;
  
  // 操作方法
  showActiveSessionModal: (sessionData: SessionInfoData) => void;
  hideActiveSessionModal: () => void;
}

// 创建默认值
const defaultContextValue: ActiveSessionNotificationContextType = {
  isModalVisible: false,
  activeSessionData: null,
  showActiveSessionModal: () => {},
  hideActiveSessionModal: () => {},
};

// 创建上下文
const ActiveSessionNotificationContext = createContext<ActiveSessionNotificationContextType>(defaultContextValue);

// Provider组件 - 只负责模态框状态管理
export function ActiveSessionNotificationProvider({ children }: { children: ReactNode }) {
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [activeSessionData, setActiveSessionData] = useState<SessionInfoData | null>(null);

  // 显示活跃会话模态框
  const showActiveSessionModal = useCallback((sessionData: SessionInfoData) => {
    setActiveSessionData(sessionData);
    setIsModalVisible(true);
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('[活跃会话提示] 显示模态框:', {
        sessionId: sessionData.sessionId,
        fileName: sessionData.fileName,
        status: sessionData.status
      });
    }
  }, []);

  // 隐藏模态框
  const hideActiveSessionModal = useCallback(() => {
    setIsModalVisible(false);
    setActiveSessionData(null);
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('[活跃会话提示] 隐藏模态框');
    }
  }, []);

  return (
    <ActiveSessionNotificationContext.Provider
      value={{
        isModalVisible,
        activeSessionData,
        showActiveSessionModal,
        hideActiveSessionModal,
      }}
    >
      {children}
    </ActiveSessionNotificationContext.Provider>
  );
}

// 自定义hook，用于访问活跃会话提示上下文
export function useActiveSessionNotification() {
  const context = useContext(ActiveSessionNotificationContext);
  if (context === defaultContextValue) {
    throw new Error("useActiveSessionNotification 必须在 ActiveSessionNotificationProvider 内部使用");
  }
  return context;
}

// 为了保持向后兼容，提供一个简化的适配器
export function useActiveSessionWarning() {
  const { isModalVisible, activeSessionData, showActiveSessionModal, hideActiveSessionModal } = useActiveSessionNotification();
  
  return {
    // 简化的属性映射
    activeSessionToProcess: activeSessionData,
    setActiveSessionToProcess: (session: SessionInfoData | null) => {
      if (session) {
        showActiveSessionModal(session);
      } else {
        hideActiveSessionModal();
      }
    },
    isSessionOverrideActive: isModalVisible,
    setIsSessionOverrideActive: (visible: boolean) => {
      if (!visible) {
        hideActiveSessionModal();
      }
    },
    
    // 简化的方法
    saveSession: () => {}, // 空实现，不再需要
    clearSession: hideActiveSessionModal,
  };
}

// 导出别名以保持兼容性
export const ExcelImportSessionGuardProvider = ActiveSessionNotificationProvider;
export const useExcelImportSessionGuard = useActiveSessionNotification; 