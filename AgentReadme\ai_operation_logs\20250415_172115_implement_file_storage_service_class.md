# Operation Document: 实现 FileStorageService 类及文件归档逻辑

## 📋 Change Summary

**Purpose**: 实现文件归档的核心逻辑，将临时生成的单个档案 PDF 文件移动到最终的、结构化的存储位置。并将相关文件操作辅助函数整合到 `FileStorageService` 类中。
**Scope**:

- `archive_processing/services/file_storage_service.py` (主要修改)
- `archive_processing/tasks.py` (更新调用方式)
**Associated**: #AFM-29 (实现文件归档)

## 🔧 Operation Steps

### 📊 OP-001: 分析与规划

**Precondition**: 项目计划确定需要实现文件归档逻辑，并已完成相关函数重命名。
**Operation**:

- 检查 `file_storage_service.py` 中的现有辅助函数。
- 确定将独立函数整合为 `FileStorageService` 类的静态方法。
- 确定使用 `get_archive_storage_path` 作为最终目录生成方法，弃用 `generate_archive_storage_path`。
- 规划 `archive_single_archive_pdf` 方法的实现细节（输入、输出、核心逻辑、错误处理）。
- 规划更新 `tasks.py` 以适配静态方法调用。
**Postcondition**: 实现方案确定。

### ✏️ OP-002: 实现 FileStorageService 类

**Precondition**: 实施方案已确定。
**Operation**:

- 在 `file_storage_service.py` 中创建 `FileStorageService` 类。
- 将 `generate_archive_filename`, `get_archive_storage_path`, `get_temp_directory`, `generate_file_hash`, `backup_archive_file`, `verify_file_integrity` 移入类中，标记为 `@staticmethod`。
- 实现 `archive_single_archive_pdf` 静态方法，包含验证、路径生成、文件移动 (`shutil.move`)、错误处理逻辑。
- 添加必要的导入 (`shutil`, `datetime`, etc.)。
- 移除或注释掉弃用的 `generate_archive_storage_path` 和不再需要的 `ensure_directory_exists`。
**Postcondition**: `FileStorageService` 类及其核心方法实现完成。

### ✏️ OP-003: 更新 tasks.py 调用

**Precondition**: `FileStorageService` 类已实现。
**Operation**:

- 修改 `archive_processing/tasks.py`。
- 移除 `FileStorageService` 的实例化。
- 将调用 `storage_service.get_temp_dir()` 修改为 `FileStorageService.get_temp_directory()`。
- 将调用 `storage_service.archive_single_archive_pdf()` 修改为 `FileStorageService.archive_single_archive_pdf()`。
**Postcondition**: `tasks.py` 正确调用 `FileStorageService` 的静态方法。

### ✅ OP-004: 验证 (代码审查)

**Precondition**: 代码修改完成。
**Operation**: (AI 执行) 审查修改后的代码，确认逻辑符合计划，调用方式正确。
**Postcondition**: 代码修改通过初步审查。

## 📝 Change Details

### CH-001: 创建 FileStorageService 类并整合辅助函数

**File**: `archive_processing/services/file_storage_service.py`
**Before**: 独立的辅助函数。
**After**: 创建 `FileStorageService` 类，将 `generate_archive_filename`, `get_archive_storage_path`, `get_temp_directory`, `generate_file_hash`, `backup_archive_file`, `verify_file_integrity` 设为静态方法。
**Rationale**: 提高代码组织性，符合面向服务的设计。
**Potential Impact**: 调用这些函数的地方需要修改为通过类名调用。

### CH-002: 实现 archive_single_archive_pdf 方法

**File**: `archive_processing/services/file_storage_service.py`
**Before**: 方法不存在。
**After**: 实现 `archive_single_archive_pdf` 静态方法，包含文件路径生成、目录确保、文件移动 (`shutil.move`)、错误处理。
**Rationale**: 实现核心的文件归档功能。
**Potential Impact**: 无直接影响，为新功能实现。

### CH-003: 更新 tasks.py 调用方式

**File**: `archive_processing/tasks.py`
**Before**: 通过实例 `storage_service` 调用方法。
**After**: 直接通过类名 `FileStorageService` 调用静态方法。移除 `storage_service` 实例化。
**Rationale**: 适配 `FileStorageService` 的静态方法设计。
**Potential Impact**: 无功能性影响，仅为调用方式调整。

## ✅ Verification Results

**Method**: 代码审查。
**Results**: `file_storage_service.py` 已重构为类，`archive_single_archive_pdf` 已实现。`tasks.py` 已更新为调用静态方法。
**Problems**: 无。
**Solutions**: N/A。
