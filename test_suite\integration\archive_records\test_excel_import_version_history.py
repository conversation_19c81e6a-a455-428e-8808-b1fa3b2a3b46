import os
from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APIClient
import pandas as pd
import tempfile
from django.utils import timezone
from archive_records.models import (
    ArchiveRecord, ImportLog, 
    ChangeLogBatch, RecordChangeLog, FieldChangeLog
)
from archive_records.services.excel_import import ExcelImportService
from test_suite.utils.test_helpers import get_test_file_path
from test_suite.unit.archive_records.base_version_test import BaseVersionTest


class ExcelImportVersionHistoryIntegrationTest(BaseVersionTest):
    """测试Excel导入和版本历史的完整流程"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345',
            email='<EMAIL>'
        )
        
        # 创建API客户端
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 导入服务
        self.import_service = ExcelImportService()
    
    def test_full_workflow(self):
        """测试完整工作流：导入 -> 更新 -> 查看历史 -> 比较版本 -> 回滚"""
        # 步骤1: 创建测试用Excel数据
        initial_data = {
            '样品编号': ['S001', 'S002'],
            '委托编号': ['C001', 'C002'],
            '工程名称': ['初始项目1', '初始项目2'],
            '委托单位': ['初始单位1', '初始单位2'],
            '委托日期': ['2023-01-01', '2023-01-02'],
            '结论': ['初始结论1', '初始结论2']
        }
        
        # 创建临时Excel文件
        initial_df = pd.DataFrame(initial_data)
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            initial_path = temp_file.name
            initial_df.to_excel(initial_path, index=False)
        
        try:
            # 步骤2: 执行初始导入
            import_log1 = self.import_service.import_from_file(
                initial_path,
                user=self.user
            )
            
            # 验证导入成功
            self.assertEqual(import_log1.status, 'completed')
            self.assertEqual(import_log1.success_records, 2)
            
            # 获取导入的记录
            records = ArchiveRecord.objects.all().order_by('sample_number')
            self.assertEqual(records.count(), 2)
            record1 = records[0]  # S001
            
            # 检查记录版本
            initial_versions = RecordChangeLog.objects.filter(record=record1)
            self.assertEqual(initial_versions.count(), 1)
            self.assertEqual(initial_versions[0].version_number, 1)
            self.assertEqual(initial_versions[0].change_type, 'create')
            
            # 步骤3: 导入更新数据
            update_data = {
                '样品编号': ['S001'],
                '委托编号': ['C001'],
                '工程名称': ['更新后项目1'],
                '委托单位': ['初始单位1'],
                '委托日期': ['2023-01-01'],
                '结论': ['更新后结论1']
            }
            
            update_df = pd.DataFrame(update_data)
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                update_path = temp_file.name
                update_df.to_excel(update_path, index=False)
            
            # 执行更新导入
            import_log2 = self.import_service.import_from_file(
                update_path,
                user=self.user,
                duplicate_strategy='smart_update'
            )
            
            # 验证更新成功
            self.assertEqual(import_log2.status, 'completed')
            
            # 刷新记录
            record1.refresh_from_db()
            self.assertEqual(record1.project_name, '更新后项目1')
            self.assertEqual(record1.conclusion, '更新后结论1')
            
            # 检查记录版本
            updated_versions = RecordChangeLog.objects.filter(record=record1).order_by('version_number')
            self.assertEqual(updated_versions.count(), 2)
            self.assertEqual(updated_versions[1].version_number, 2)
            self.assertEqual(updated_versions[1].change_type, 'update')
            
            # 检查版本包含的字段变更
            field_changes = FieldChangeLog.objects.filter(record_change=updated_versions[1])
            self.assertEqual(field_changes.count(), 2)  # 项目名称和结论变更
            
            changed_fields = [f.field_name for f in field_changes]
            self.assertIn('project_name', changed_fields)
            self.assertIn('conclusion', changed_fields)
            
            # 步骤4: 使用API查看记录历史
            history_url = reverse('record_history', args=[record1.id])
            history_response = self.client.get(history_url)
            
            self.assertEqual(history_response.status_code, 200)
            self.assertEqual(history_response.data['versions_count'], 2)
            
            # 步骤5: 使用API比较两个版本
            compare_url = reverse('record_compare', args=[record1.id])
            compare_response = self.client.get(f"{compare_url}?v1=1&v2=2")
            
            self.assertEqual(compare_response.status_code, 200)
            differences = compare_response.data['differences']
            
            # 验证差异包含两个字段
            self.assertEqual(len(differences), 2)
            diff_fields = [d['field'] for d in differences]
            self.assertIn('project_name', diff_fields)
            self.assertIn('conclusion', diff_fields)
            
            # 步骤6: 导入第三次更新，只修改项目名称（不改变委托编号）
            third_update_data = {
                '样品编号': ['S001'],
                '委托编号': ['C001'],  # 保持委托编号不变
                '工程名称': ['第三次更新项目1'],  # 只修改工程名称
                '委托单位': ['初始单位1'],
                '委托日期': ['2023-01-01']
            }
            
            third_df = pd.DataFrame(third_update_data)
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                third_path = temp_file.name
                third_df.to_excel(third_path, index=False)
            
            # 执行第三次更新
            import_log3 = self.import_service.import_from_file(
                third_path,
                user=self.user,
                duplicate_strategy='smart_update'
            )
            
            # 验证更新成功
            self.assertEqual(import_log3.status, 'completed')
            
            # 刷新记录
            record1.refresh_from_db()
            self.assertEqual(record1.commission_number, 'C001')  # 委托编号未变
            self.assertEqual(record1.project_name, '第三次更新项目1')  # 项目名称已更新
            
            # 检查记录版本
            third_versions = RecordChangeLog.objects.filter(record=record1).order_by('version_number')
            self.assertEqual(third_versions.count(), 3)
            self.assertEqual(third_versions[2].version_number, 3)
            
            # 步骤7: 使用API回滚到版本1
            rollback_url = reverse('record_rollback', args=[record1.id, 1])
            rollback_response = self.client.post(rollback_url)
            
            self.assertEqual(rollback_response.status_code, 200)
            
            # 刷新记录，验证回滚成功
            record1.refresh_from_db()
            self.assertEqual(record1.project_name, '初始项目1')  # 回滚到初始值
            self.assertEqual(record1.conclusion, '初始结论1')    # 回滚到初始值
            self.assertEqual(record1.commission_number, 'C001')  # 委托编号保持不变
            
            # 验证回滚操作创建了新版本
            final_versions = RecordChangeLog.objects.filter(record=record1).order_by('version_number')
            self.assertEqual(final_versions.count(), 4)  # 1初始 + 2更新 + 3更新项目名称 + 4回滚
            self.assertEqual(final_versions[3].version_number, 4)
            self.assertEqual(final_versions[3].change_type, 'rollback')
            
        finally:
            # 清理临时文件
            for path_name in ['initial_path', 'update_path', 'third_path']:
                if path_name in locals() and locals()[path_name] and os.path.exists(locals()[path_name]):
                    os.unlink(locals()[path_name]) 