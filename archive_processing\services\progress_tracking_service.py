import json
import logging
import datetime
from typing import Dict, Any, Optional, List, Union
from django.core.cache import cache
from django.core.mail import send_mail
from django.contrib.auth.models import User
from django.conf import settings
from django.db import transaction

logger = logging.getLogger(__name__)

class ProgressTracker:
    """
    任务进度跟踪类，提供创建、更新和查询任务进度功能
    """
    # 缓存超时时间（秒）
    DEFAULT_TIMEOUT = 3600  # 1小时
    
    # 预定义状态
    STATUS_PENDING = 'pending'
    STATUS_PROCESSING = 'processing'
    STATUS_COMPLETED = 'completed'
    STATUS_FAILED = 'failed'
    STATUS_CANCELED = 'canceled'
    STATUS_PAUSED = 'paused'
    
    @classmethod
    def create(cls, task_id: str, total_steps: int, 
               message: str = '任务初始化完成', 
               timeout: int = DEFAULT_TIMEOUT) -> Dict[str, Any]:
        """
        创建进度追踪器
        
        Args:
            task_id: 任务ID
            total_steps: 总步骤数
            message: 初始进度消息
            timeout: 缓存超时时间(秒)
            
        Returns:
            进度追踪器实例
        """
        # 初始化进度信息
        progress_info = {
            'task_id': task_id,
            'total': total_steps,
            'completed': 0,
            'percentage': 0,
            'status': cls.STATUS_PENDING,
            'message': message,
            'created_at': datetime.datetime.now().isoformat(),
            'updated_at': datetime.datetime.now().isoformat(),
            'history': []  # 历史进度记录
        }
        
        # 存储到缓存
        cache_key = cls._get_cache_key(task_id)
        cache.set(cache_key, json.dumps(progress_info), timeout=timeout)
        
        logger.debug(f"创建进度跟踪器: {task_id}, 总步骤: {total_steps}")
        return progress_info
    
    @classmethod
    def update(cls, task_id: str, completed_steps: int, 
               message: Optional[str] = None, 
               status: Optional[str] = None, 
               timeout: int = DEFAULT_TIMEOUT) -> Optional[Dict[str, Any]]:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            completed_steps: 已完成步骤数
            message: 进度消息
            status: 任务状态
            timeout: 缓存超时时间(秒)
            
        Returns:
            更新后的进度信息，如果任务不存在则返回None
        """
        # 获取当前进度
        cache_key = cls._get_cache_key(task_id)
        current_progress_json = cache.get(cache_key)
        
        if not current_progress_json:
            logger.warning(f"任务进度不存在: {task_id}")
            return None
        
        current_progress = json.loads(current_progress_json)
        
        # 保存历史记录
        history_entry = {
            'timestamp': datetime.datetime.now().isoformat(),
            'completed': current_progress['completed'],
            'percentage': current_progress['percentage'],
            'status': current_progress['status'],
            'message': current_progress['message']
        }
        
        # 确保历史记录列表存在
        if 'history' not in current_progress:
            current_progress['history'] = []
        
        # 限制历史记录条数，保留最新的10条
        current_progress['history'].append(history_entry)
        if len(current_progress['history']) > 10:
            current_progress['history'] = current_progress['history'][-10:]
        
        # 更新进度
        current_progress['completed'] = completed_steps
        if current_progress['total'] > 0:
            current_progress['percentage'] = min(
                int((completed_steps / current_progress['total']) * 100), 
                100  # 确保百分比不超过100
            )
        
        # 更新状态和消息
        if status:
            current_progress['status'] = status
        if message:
            current_progress['message'] = message
        
        # 更新时间戳
        current_progress['updated_at'] = datetime.datetime.now().isoformat()
        
        # 存回缓存
        cache.set(cache_key, json.dumps(current_progress), timeout=timeout)
        
        logger.debug(f"更新任务进度: {task_id}, 完成: {completed_steps}/{current_progress['total']}")
        return current_progress
    
    @classmethod
    def get(cls, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            进度信息，如果任务不存在则返回None
        """
        cache_key = cls._get_cache_key(task_id)
        progress_json = cache.get(cache_key)
        
        if not progress_json:
            logger.debug(f"任务进度不存在: {task_id}")
            return None
        
        progress = json.loads(progress_json)
        logger.debug(f"获取任务进度: {task_id}, 状态: {progress['status']}")
        return progress
    
    @classmethod
    def complete(cls, task_id: str, message: str = "任务已完成", 
                 result_data: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """
        将任务标记为已完成
        
        Args:
            task_id: 任务ID
            message: 完成消息
            result_data: 结果数据
            
        Returns:
            更新后的进度信息
        """
        progress = cls.get(task_id)
        if not progress:
            return None
        
        # 更新为100%完成
        progress = cls.update(
            task_id=task_id,
            completed_steps=progress['total'],
            status=cls.STATUS_COMPLETED,
            message=message
        )
        
        # 添加结果数据
        if progress and result_data:
            progress['result'] = result_data
            cache_key = cls._get_cache_key(task_id)
            cache.set(cache_key, json.dumps(progress), timeout=cls.DEFAULT_TIMEOUT)
        
        logger.info(f"任务已完成: {task_id}")
        return progress
    
    @classmethod
    def fail(cls, task_id: str, error_message: str, 
             error_details: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """
        将任务标记为失败
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            error_details: 错误详情
            
        Returns:
            更新后的进度信息
        """
        progress = cls.get(task_id)
        if not progress:
            return None
        
        # 更新为失败状态
        progress = cls.update(
            task_id=task_id,
            completed_steps=progress['completed'],  # 保持当前进度
            status=cls.STATUS_FAILED,
            message=error_message
        )
        
        # 添加错误详情
        if progress and error_details:
            progress['error'] = error_details
            cache_key = cls._get_cache_key(task_id)
            cache.set(cache_key, json.dumps(progress), timeout=cls.DEFAULT_TIMEOUT)
        
        logger.error(f"任务失败: {task_id}, 错误: {error_message}")
        return progress
    
    @classmethod
    def delete(cls, task_id: str) -> bool:
        """
        删除任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功删除
        """
        cache_key = cls._get_cache_key(task_id)
        deleted = cache.delete(cache_key)
        
        if deleted:
            logger.info(f"删除任务进度: {task_id}")
        else:
            logger.warning(f"删除任务进度失败，任务不存在: {task_id}")
        
        return deleted
    
    @classmethod
    def _get_cache_key(cls, task_id: str) -> str:
        """生成缓存键名"""
        return f"task_progress:{task_id}"


# 向后兼容的函数接口
def create_progress_tracker(task_id, total_steps):
    """
    创建进度追踪器（向后兼容接口）
    
    Args:
        task_id: 任务ID
        total_steps: 总步骤数
        
    Returns:
        进度追踪器实例
    """
    return ProgressTracker.create(task_id, total_steps)


def update_progress(task_id, completed_steps, message=None, status=None):
    """
    更新任务进度（向后兼容接口）
    
    Args:
        task_id: 任务ID
        completed_steps: 已完成步骤数
        message: 进度消息
        status: 任务状态
        
    Returns:
        更新后的进度信息
    """
    return ProgressTracker.update(task_id, completed_steps, message, status)


def get_task_progress(task_id):
    """
    获取任务进度（向后兼容接口）
    
    Args:
        task_id: 任务ID
        
    Returns:
        进度信息
    """
    return ProgressTracker.get(task_id)


class NotificationService:
    """通知服务类，提供邮件、站内消息等通知功能"""
    
    @classmethod
    def send_email_notification(cls, user_id: int, subject: str, message: str) -> Dict[str, Any]:
        """
        发送邮件通知
        
        Args:
            user_id: 用户ID
            subject: 邮件主题
            message: 邮件内容
            
        Returns:
            发送结果
        """
        try:
            # 获取用户信息
            user = User.objects.get(id=user_id)
            
            # 如果用户没有邮箱，则无法通知
            if not user.email:
                logger.warning(f"用户 {user_id} 无邮箱地址，无法发送邮件通知")
                return {'success': False, 'error': '用户无邮箱地址'}
            
            # 发送邮件
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=False,
            )
            
            logger.info(f"已向用户 {user_id} 发送邮件通知: {subject}")
            return {'success': True}
            
        except User.DoesNotExist:
            logger.error(f"用户不存在: {user_id}")
            return {'success': False, 'error': f'用户不存在: {user_id}'}
        except Exception as e:
            logger.error(f"发送邮件通知失败: {e}")
            return {'success': False, 'error': str(e)}
    
    @classmethod
    def send_completion_notification(cls, user_id: int, task_id: str, task_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送任务完成通知
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            task_info: 任务信息
            
        Returns:
            发送结果
        """
        # 构建通知内容
        subject = f"PDF处理任务完成: {task_id}"
        
        message = f"""
        尊敬的用户:
        
        您的PDF处理任务已完成。
        
        处理摘要:
        - 总文件数: {task_info.get('total', 0)}
        - 成功处理: {task_info.get('successful', 0)}
        - 处理失败: {task_info.get('failed', 0)}
        
        请登录系统查看详细结果。
        """
        
        return cls.send_email_notification(user_id, subject, message)

    @classmethod
    def send_in_app_notification(cls, user_id: int, notification_type: str, 
                                message: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送站内消息通知
        
        Args:
            user_id: 用户ID
            notification_type: 通知类型
            message: 通知内容
            data: 附加数据
            
        Returns:
            发送结果
        """
        try:
            # 这里可以集成实现站内消息系统，例如使用Django的消息框架或自定义模型
            # 目前仅记录日志
            logger.info(f"向用户 {user_id} 发送站内通知: {notification_type} - {message}")
            
            # 模拟成功结果
            return {'success': True, 'message': '站内通知已发送'}
            
        except Exception as e:
            logger.error(f"发送站内通知失败: {e}")
            return {'success': False, 'error': str(e)}


# 向后兼容的函数接口
def send_completion_notification(user_id, task_info):
    """
    发送完成通知（向后兼容接口）
    
    Args:
        user_id: 用户ID
        task_info: 任务信息
        
    Returns:
        发送结果
    """
    task_id = task_info.get('task_id', 'unknown')
    return NotificationService.send_completion_notification(user_id, task_id, task_info)


def send_task_notification(user_id, notification_type, message, data=None):
    """
    发送任务通知（向后兼容接口）
    
    Args:
        user_id: 用户ID
        notification_type: 通知类型
        message: 通知内容
        data: 附加数据
        
    Returns:
        发送结果
    """
    return NotificationService.send_in_app_notification(user_id, notification_type, message, data) 