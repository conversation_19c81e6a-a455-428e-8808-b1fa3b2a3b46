"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { PageTitle } from "@/components/page-title"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useChangeOrder } from "@/hooks/use-change-order"
import { PageLayout } from "@/components/common/page-layout"

// Import our extracted components
import { ChangeOrderHeader } from "@/components/change-orders/change-order-header"
import { ChangeOrderBasicInfo } from "@/components/change-orders/change-order-basic-info"
import { ChangeOrderChanges, mockAccountData } from "@/components/change-orders/change-order-changes"
import { ChangeOrderRecords } from "@/components/change-orders/change-order-records"
import { ChangeOrderHistory } from "@/components/change-orders/change-order-history"
import { DeleteChangeOrderDialog } from "@/components/change-orders/delete-change-order-dialog"

export default function ChangeOrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { id } = params

  // 模拟当前用户角色 - 实际应用中应从认证上下文获取
  const [[userRole]] = useState<["change_manager"]>(["change_manager"])

  // 使用自定义hook管理更改单状态和逻辑
  const {
    isLoading,
    isProcessing,
    changeOrder,
    editedChanges,
    isSaving,
    lastSavedAt,
    hasUnsavedChanges,
    editMode,
    setChangeOrder,
    setEditedChanges,
    handleAutoSaveAsDraft,
    handleAutoUpdate,
    handleChangeEdit,
    handleBasicInfoChange,
    handleLockChangeOrder,
    handleUnlockChangeOrder,
    handleConfirmChangeOrder,
    handleArchiveChangeOrder,
    handleRevertToDraft,
    handleDeleteChangeOrder,
    isNewOrder,
    canAutoSave,
  } = useChangeOrder({
    id: id as string,
  })

  // 状态
  const [activeTab, setActiveTab] = useState("details")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // 处理记录选择 - 这是关键函数，当选择记录时自动更新档案列表
  const handleRecordSelection = (recordId: string, isSelected: boolean) => {
    if (!changeOrder) return

    // 获取当前选中的记录ID列表
    const currentRecordIds = changeOrder.records?.map((record: any) => record.id) || []

    // 更新选中的记录ID列表
    let newRecordIds: string[]
    if (isSelected) {
      // 添加新选中的记录ID
      if (!currentRecordIds.includes(recordId)) {
        newRecordIds = [...currentRecordIds, recordId]
      } else {
        return // 记录已经被选中，无需更新
      }
    } else {
      // 移除取消选中的记录ID
      newRecordIds = currentRecordIds.filter((id) => id !== recordId)
    }

    // 根据ID获取完整的记录对象
    const updatedRecords = newRecordIds
      .map((id) => mockAccountData.find((record) => record.id === id))
      .filter(Boolean) as any[]

    // 更新changeOrder
    const updatedChangeOrder = {
      ...changeOrder,
      records: updatedRecords,
    }

    // 更新状态
    setChangeOrder(updatedChangeOrder)

    // 为新选中的记录创建默认的更改项
    if (isSelected) {
      const record = mockAccountData.find((r) => r.id === recordId)
      if (record) {
        const newChange = {
          recordId: recordId,
          field: "title",
          fieldLabel: "标题",
          oldValue: record.title || "",
          newValue: record.title || "",
        }

        // 检查是否已存在相同的更改项
        const exists = editedChanges.some((c) => c.recordId === recordId && c.field === "title")

        if (!exists) {
          // 更新editedChanges
          const newChanges = [...editedChanges, newChange]
          setEditedChanges(newChanges)

          // 保存更改
          if (isNewOrder && changeOrder.reason?.trim()) {
            handleAutoSaveAsDraft(newChanges, true)
          } else if (!isNewOrder) {
            handleAutoUpdate(newChanges, true)
          }
        }
      }
    }
  }

  // 页面离开前提示
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isNewOrder && changeOrder && !isProcessing) {
        e.preventDefault()
        e.returnValue = ""
        return ""
      }
    }

    window.addEventListener("beforeunload", handleBeforeUnload)
    return () => window.removeEventListener("beforeunload", handleBeforeUnload)
  }, [isNewOrder, changeOrder, isProcessing])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2">加载中...</p>
        </div>
      </div>
    )
  }

  if (!changeOrder) {
    return (
      <PageLayout
        title="创建更改单"
        subtitle=""
        actions={[
          {
            label: "返回更改单列表",
            icon: <ArrowLeft className="h-4 w-4" />,
            href: "/change-orders",
            variant: "outline" as const
          }
        ]}
      >
        <Alert variant="destructive">
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>未找到更改单数据</AlertDescription>
        </Alert>
      </PageLayout>
    )
  }

  // 定义页面操作按钮
  const actions = [
    {
      label: "返回更改单列表",
      icon: <ArrowLeft className="h-4 w-4" />,
      href: "/change-orders",
      variant: "outline" as const
    }
  ]

  return (
    <PageLayout
      title={isNewOrder ? "创建更改单" : "更改单详情"}
      subtitle={!isNewOrder ? `更改单 ${changeOrder?.id}` : ""}
      actions={actions}
      statusCards={
        <ChangeOrderHeader
          changeOrder={changeOrder}
          isNewOrder={isNewOrder}
          lastSavedAt={lastSavedAt}
          isSaving={isSaving}
          isProcessing={isProcessing}
          canAutoSave={canAutoSave}

          onSaveAsDraft={handleAutoSaveAsDraft}
          onLock={handleLockChangeOrder}
          onUnlock={handleUnlockChangeOrder}
          onConfirm={handleConfirmChangeOrder}
          onRevertToDraft={handleRevertToDraft}
          onArchive={handleArchiveChangeOrder}
          onPrint={() => {
            window.print()
            toast({
              title: "打印请求已发送",
              description: "正在准备打印确认单",
            })
          }}
          onDelete={() => setShowDeleteDialog(true)}
        />
      }
      fixedTabs={
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="details">基本信息</TabsTrigger>
            <TabsTrigger value="changes">更改内容</TabsTrigger>
            <TabsTrigger value="records">档案列表</TabsTrigger>
            <TabsTrigger value="history">操作历史</TabsTrigger>
          </TabsList>
        </Tabs>
      }
      disableScrollArea={true}
    >
      <Tabs value={activeTab} className="h-full flex flex-col">
        <div className="hidden">
          <TabsList>
            <TabsTrigger value="details">基本信息</TabsTrigger>
            <TabsTrigger value="changes">更改内容</TabsTrigger>
            <TabsTrigger value="records">档案列表</TabsTrigger>
            <TabsTrigger value="history">操作历史</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="details" className="flex-1">
          <ChangeOrderBasicInfo
            changeOrder={changeOrder}
            isNewOrder={isNewOrder}
            editMode={editMode}
            onBasicInfoChange={handleBasicInfoChange}
          />
        </TabsContent>

        <TabsContent value="changes" className="flex-1">
          <ChangeOrderChanges
            changeOrder={changeOrder}
            editedChanges={editedChanges}
            editMode={editMode}
            onChangeEdit={handleChangeEdit}
            onRecordSelection={handleRecordSelection}
          />
        </TabsContent>

        <TabsContent value="records" className="flex-1">
          <ChangeOrderRecords changeOrder={changeOrder} />
        </TabsContent>

        <TabsContent value="history" className="flex-1">
          <ChangeOrderHistory changeOrder={changeOrder} />
        </TabsContent>
      </Tabs>
      
      {/* 删除更改单对话框 */}
      <DeleteChangeOrderDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteChangeOrder}
        isProcessing={isProcessing}
      />
    </PageLayout>
  )
}
