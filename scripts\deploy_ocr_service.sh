#!/bin/bash
# OCR 微服务部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_prerequisites() {
    log_info "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 构建 OCR 服务镜像
build_ocr_service() {
    log_info "构建 OCR 微服务镜像..."
    
    cd ocr_service
    
    if docker build -t archive-flow-ocr:latest .; then
        log_success "OCR 服务镜像构建成功"
    else
        log_error "OCR 服务镜像构建失败"
        exit 1
    fi
    
    cd ..
}

# 启动服务
start_services() {
    log_info "启动服务栈..."
    
    # 首先启动基础服务
    log_info "启动基础服务 (Redis, PostgreSQL)..."
    docker-compose up -d redis db
    
    # 等待基础服务就绪
    log_info "等待基础服务就绪..."
    sleep 10
    
    # 启动 OCR 服务
    log_info "启动 OCR 微服务..."
    docker-compose up -d ocr-service
    
    # 等待 OCR 服务就绪
    log_info "等待 OCR 服务就绪..."
    sleep 30
    
    # 启动主应用
    log_info "启动主应用和其他服务..."
    docker-compose up -d
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查 OCR 服务健康状态
    log_info "检查 OCR 服务健康状态..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8001/health &> /dev/null; then
            log_success "OCR 服务健康检查通过"
            break
        else
            log_warning "OCR 服务健康检查失败，重试 $attempt/$max_attempts"
            sleep 2
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "OCR 服务健康检查超时"
        return 1
    fi
    
    # 检查主应用
    log_info "检查主应用状态..."
    
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/ &> /dev/null; then
            log_success "主应用健康检查通过"
            break
        else
            log_warning "主应用健康检查失败，重试 $attempt/$max_attempts"
            sleep 2
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "主应用健康检查超时"
        return 1
    fi
    
    log_success "所有服务运行正常"
}

# 运行测试
run_tests() {
    log_info "运行 OCR 微服务测试..."
    
    if python ocr_service/test_ocr_service.py; then
        log_success "OCR 微服务测试通过"
    else
        log_error "OCR 微服务测试失败"
        return 1
    fi
    
    log_info "运行集成测试..."
    
    if python test_ocr_migration.py; then
        log_success "集成测试通过"
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    echo "  🌐 主应用: http://localhost:8000"
    echo "  🔍 OCR 服务: http://localhost:8001"
    echo "  📊 OCR 健康检查: http://localhost:8001/health"
    echo "  📈 OCR 指标: http://localhost:8001/metrics"
    echo "  📚 OCR API 文档: http://localhost:8001/docs"
    echo ""
    echo "  📋 查看日志:"
    echo "    docker-compose logs -f ocr-service"
    echo "    docker-compose logs -f web"
    echo ""
    echo "  🛑 停止服务:"
    echo "    docker-compose down"
}

# 主函数
main() {
    echo "🚀 OCR 微服务部署脚本"
    echo "=========================="
    
    case "${1:-deploy}" in
        "build")
            check_prerequisites
            build_ocr_service
            ;;
        "start")
            start_services
            ;;
        "check")
            check_services
            ;;
        "test")
            run_tests
            ;;
        "deploy")
            check_prerequisites
            build_ocr_service
            start_services
            check_services
            run_tests
            show_service_info
            ;;
        "info")
            show_service_info
            ;;
        *)
            echo "用法: $0 [build|start|check|test|deploy|info]"
            echo ""
            echo "  build  - 仅构建 OCR 服务镜像"
            echo "  start  - 仅启动服务"
            echo "  check  - 仅检查服务状态"
            echo "  test   - 仅运行测试"
            echo "  deploy - 完整部署流程 (默认)"
            echo "  info   - 显示服务信息"
            exit 1
            ;;
    esac
    
    log_success "操作完成！"
}

# 执行主函数
main "$@"
