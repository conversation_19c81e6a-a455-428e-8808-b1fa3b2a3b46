import { v4 as uuidv4 } from "uuid"

// 模块类型
export type ModuleType = "change-order" | "report-distribution" | "archive" | "record"

// 生成基于UUID的临时ID
export const generateTempId = (moduleType: ModuleType): string => {
  return `temp-${moduleType}-${uuidv4()}`
}

// 检查ID是否为临时ID
export const isTempId = (id: string, moduleType?: ModuleType): boolean => {
  // 添加类型检查，确保 id 是字符串
  if (!id || typeof id !== 'string') return false

  if (moduleType) {
    return id.startsWith(`temp-${moduleType}-`)
  }

  // 如果没有指定模块类型，检查是否是任何类型的临时ID
  return id.startsWith("temp-")
}

// 获取会话存储键
const getSessionStorageKey = (moduleType: ModuleType): string => {
  return `temp-${moduleType}-ids`
}

// 获取本地存储键
const getLocalStorageKey = (moduleType: ModuleType, tempId: string): string => {
  return `${moduleType}-${tempId}`
}

// 保存临时ID与当前会话的关联
export const associateTempIdWithSession = (tempId: string, moduleType: ModuleType): void => {
  try {
    const storageKey = getSessionStorageKey(moduleType)
    const currentTempIds = JSON.parse(sessionStorage.getItem(storageKey) || "[]")

    if (!currentTempIds.includes(tempId)) {
      currentTempIds.push(tempId)
      sessionStorage.setItem(storageKey, JSON.stringify(currentTempIds))
    }
  } catch (error) {
    console.error(`Failed to associate temp ${moduleType} ID with session:`, error)
  }
}

// 验证临时ID是否属于当前会话
export const validateTempIdBelongsToSession = (tempId: string, moduleType: ModuleType): boolean => {
  try {
    const storageKey = getSessionStorageKey(moduleType)
    const currentTempIds = JSON.parse(sessionStorage.getItem(storageKey) || "[]")
    return currentTempIds.includes(tempId)
  } catch (error) {
    console.error(`Failed to validate temp ${moduleType} ID:`, error)
    return false
  }
}

// 从会话中移除临时ID
export const removeTempIdFromSession = (tempId: string, moduleType: ModuleType, removeLocalData = true): void => {
  try {
    const sessionKey = getSessionStorageKey(moduleType)
    const currentTempIds = JSON.parse(sessionStorage.getItem(sessionKey) || "[]")
    const updatedTempIds = currentTempIds.filter((id: string) => id !== tempId)
    sessionStorage.setItem(sessionKey, JSON.stringify(updatedTempIds))

    // 同时从localStorage中移除相关数据
    if (removeLocalData) {
      const localKey = getLocalStorageKey(moduleType, tempId)
      localStorage.removeItem(localKey)
    }
  } catch (error) {
    console.error(`Failed to remove temp ${moduleType} ID from session:`, error)
  }
}

// 保存临时数据到localStorage
export const saveTemporaryData = (tempId: string, moduleType: ModuleType, data: any): void => {
  try {
    const localKey = getLocalStorageKey(moduleType, tempId)
    localStorage.setItem(localKey, JSON.stringify(data))
  } catch (error) {
    console.error(`Failed to save temporary ${moduleType} data:`, error)
  }
}

// 从localStorage获取临时数据
export const getTemporaryData = (tempId: string, moduleType: ModuleType): any => {
  try {
    const localKey = getLocalStorageKey(moduleType, tempId)
    const data = localStorage.getItem(localKey)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error(`Failed to get temporary ${moduleType} data:`, error)
    return null
  }
}

// 从localStorage删除临时数据
export const removeTemporaryData = (tempId: string, moduleType: ModuleType): void => {
  try {
    const localKey = getLocalStorageKey(moduleType, tempId)
    localStorage.removeItem(localKey)
  } catch (error) {
    console.error(`Failed to remove temporary ${moduleType} data:`, error)
  }
}
