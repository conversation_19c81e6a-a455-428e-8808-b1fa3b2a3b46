# Generated by Django 5.1.10 on 2025-06-07 13:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report_issuing', '0002_alter_issueformitem_unique_together_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='issueformitem',
            name='issue_type',
        ),
        migrations.RemoveField(
            model_name='issuerecord',
            name='legacy_id',
        ),
        migrations.RemoveField(
            model_name='issuerecord',
            name='source',
        ),
        migrations.RemoveField(
            model_name='issuerecordhistory',
            name='operation_type',
        ),
        migrations.AddField(
            model_name='issuerecordhistory',
            name='action',
            field=models.CharField(choices=[('archive_table_modify', '总台账表修改'), ('issue_form_create', '发放单创建'), ('issue_form_delete', '发放单删除')], default=0, max_length=30, verbose_name='业务动作'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='issueform',
            name='status',
            field=models.CharField(choices=[('draft', '草稿'), ('locked', '锁定'), ('issued', '已发放')], default='draft', max_length=20, verbose_name='状态'),
        ),
    ]
