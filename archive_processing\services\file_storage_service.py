import logging
import os
import re
import time
import uuid
import hashlib
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from django.conf import settings
from datetime import datetime

logger = logging.getLogger(__name__)

# ==============================================================================
# FileStorageService Implementation Plan (to be removed after testing)
# ==============================================================================
# 1. 创建 FileStorageService 类。
# 2. 将 generate_archive_filename, get_archive_storage_path, get_temp_directory,
#    generate_file_hash, backup_archive_file, verify_file_integrity 等现有
#    独立函数整合为类的静态方法。
# 3. 弃用 generate_archive_storage_path，保留其代码作为注释或直接删除，
#    统一使用 get_archive_storage_path。
# 4. 实现 archive_single_archive_pdf 静态方法：
#    - 接收 temp_path: str, unified_number: str, original_pdf_path: Optional[str] = None。
#    - 验证输入 (temp_path 存在, unified_number 非空)。
#    - 调用 cls.generate_archive_filename 获取最终文件名。
#    - 调用 cls.get_archive_storage_path 获取最终目录路径。
#    - 构建最终完整路径。
#    - 检查最终路径是否已存在 (os.path.exists)，若存在则记录警告。
#    - 使用 shutil.move(temp_path, final_path) 移动文件。
#    - try...except 捕获文件操作异常。
#    - 返回结果字典 {'success': bool, 'final_path': Optional[str], 'error': Optional[str]}。
# 5. (后续任务) 检查并修改 tasks.py 中获取临时文件路径的逻辑，
#    确保其调用 FileStorageService.get_temp_directory()。
# ==============================================================================

class FileStorageService:
    """文件存储服务，处理文件归档、路径生成、临时文件、备份等。"""

    @staticmethod
    def generate_archive_filename(unified_number: Optional[str] = None,
                                  original_path: Optional[str] = None,
                                  timestamp: Optional[int] = None) -> str:
        """
        生成归档文件的规范化文件名。

        优先使用统一编号，如果提供的话。否则，可以基于原始文件名或时间戳。

        Args:
            unified_number (str, optional): 文件的统一编号。
            original_path (str, optional): 原始文件的路径，用于提取文件名和扩展名。
            timestamp (int, optional): 文件处理的时间戳 (秒级).

        Returns:
            str: 生成的文件名。
        """
        # import os # Moved to top level
        # import time # Moved to top level
        # import hashlib # Moved to top level

        # 处理timestamp
        current_timestamp = int(time.time()) if timestamp is None else timestamp

        # 1. 基于统一编号生成文件名
        if unified_number:
            # 简单情况：直接使用统一编号 + .pdf
            # TODO: [P3] 考虑更严格的 unified_number 格式验证
            # 替换可能存在的文件系统非法字符，虽然 get_archive_storage_path 已处理目录，文件名也处理下更安全
            safe_unified_number = re.sub(r'[\\/*?:"<>|]', '_', unified_number)
            return f"{safe_unified_number}.pdf"

        # 2. 基于原始路径生成文件名
        if original_path:
            basename = os.path.basename(original_path)
            name, ext = os.path.splitext(basename)
            if not ext:
                ext = ".pdf"
            timestamp_str = time.strftime("%Y%m%d-%H%M%S", time.localtime(current_timestamp))
            # 使用 uuid 替代哈希，减少潜在冲突，同时保留一定信息
            random_str = str(uuid.uuid4())[:8]
            # 清理 name 中的非法字符
            safe_name = re.sub(r'[\\/*?:"<>|]', '_', name)
            return f"{safe_name}_{timestamp_str}_{random_str}{ext}"

        # 3. 如果都没有，生成一个基于时间戳和UUID的唯一文件名
        timestamp_str = time.strftime("%Y%m%d-%H%M%S", time.localtime(current_timestamp))
        random_str = str(uuid.uuid4())[:12]
        return f"archive_{timestamp_str}_{random_str}.pdf"

    # --- generate_archive_storage_path (已弃用) ---
    # @staticmethod
    # def generate_archive_storage_path(unified_number):
    #     """ (已弃用) 根据统一编号生成存档路径。请使用 get_archive_storage_path。"""
    #     logger.warning("generate_archive_storage_path is deprecated. Use get_archive_storage_path instead.")
    #     # ... (原代码) ...

    @staticmethod
    def get_archive_storage_path(unified_number: Optional[str] = None) -> str:
        """
        获取档案存储的目录路径，按 年/月/统一编号前缀 组织。
        此方法会确保目标目录存在。

        Args:
            unified_number (str, optional): 统一编号，用于生成最后一级子目录。
                                          如果为 None 或格式不符合 '前缀-后缀'，
                                          则文件会被放入 'unclassified' 子目录。

        Returns:
            str: 最终的归档目录绝对路径。
                 即使目录创建失败，也会尝试返回路径。
        """
        # import os # Moved to top level
        # from datetime import datetime # Moved to top level
        # from django.conf import settings # Moved to top level

        try:
            # 基础存储路径
            base_path = getattr(settings, 'ARCHIVE_STORAGE_ROOT', os.path.join(settings.MEDIA_ROOT, 'archives'))
            if not base_path:
                 logger.error("未配置 ARCHIVE_STORAGE_ROOT 或 MEDIA_ROOT，无法确定归档根目录！")
                 # Fallback to a temporary directory if settings are missing
                 fallback_dir = os.path.join(tempfile.gettempdir(), 'archive_fallback')
                 os.makedirs(fallback_dir, exist_ok=True) # Try to create fallback
                 return fallback_dir

            # 创建年月子目录
            now = datetime.now()
            year_dir = str(now.year)
            month_dir = f"{now.month:02d}"
            date_path = os.path.join(base_path, year_dir, month_dir)

            # 如果有统一编号，则创建前缀子目录
            if unified_number and '-' in unified_number:
                prefix = unified_number.split('-')[0]
                # 清理前缀中的非法字符
                safe_prefix = re.sub(r'[\\/*?:"<>|]', '_', prefix)
                final_path = os.path.join(date_path, safe_prefix)
            else:
                # 无统一编号或格式不符时使用 'unclassified' 目录
                final_path = os.path.join(date_path, 'unclassified')
                if unified_number:
                    logger.warning(f"统一编号 '{unified_number}' 格式不符合 '前缀-后缀'，将归档到 unclassified 目录。")
                else:
                    logger.info("未提供统一编号，将归档到 unclassified 目录。")

            # 确保目录存在 (os.makedirs 会处理中间路径)
            try:
                os.makedirs(final_path, exist_ok=True)
            except OSError as e:
                logger.error(f"创建归档目录失败: '{final_path}', 错误: {e}", exc_info=True)
                # Fallback even if directory creation fails, maybe permissions issue
                # Returning the path allows the caller to attempt the move anyway
                # (though it will likely fail)
                # Alternatively, could raise the exception or return None

            return final_path

        except Exception as e:
            logger.error(f"生成归档存储路径时发生意外错误: {e}", exc_info=True)
            # Provide a fallback temporary directory in case of unexpected errors
            fallback_dir = os.path.join(tempfile.gettempdir(), 'archive_error_fallback')
            try:
                os.makedirs(fallback_dir, exist_ok=True)
            except Exception as fallback_e:
                 logger.error(f"创建回退目录失败 '{fallback_dir}': {fallback_e}")
                 return tempfile.gettempdir() # Absolute fallback
            return fallback_dir

    @staticmethod
    def archive_single_archive_report_pdf(
        unified_number: str,
        archive_temp_path: str,
        report_temp_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        同时归档档案PDF和报告PDF到最终存储位置
        支持报告路径为None的情况，此时只处理档案归档
        
        Args:
            unified_number: 统一编号
            archive_temp_path: 档案PDF临时文件路径（必须）
            report_temp_path: 报告PDF临时文件路径（可选，为None时跳过报告处理）
            
        Returns:
            Dict[str, Any]: 归档结果
            {
                'success': bool,                    # 整体是否成功（至少档案成功）
                'archive_final_path': str,         # 档案最终路径
                'report_final_path': str,          # 报告最终路径（可能为None）
                'archive_file_url': str,           # 档案访问URL
                'report_file_url': str,            # 报告访问URL（可能为None）
                'archive_success': bool,           # 档案是否成功
                'report_success': bool,            # 报告是否成功
                'report_error': str,               # 报告处理错误信息（如果有）
                'error': str                       # 整体错误信息（如果完全失败）
            }
        """
        logger.info(f"开始归档档案和报告文件 (编号: {unified_number})...")

        # 1. 验证必须的输入
        if not unified_number:
            err = "归档失败：必须提供统一编号 (unified_number)。"
            logger.error(err)
            return {
                'success': False, 
                'archive_success': False,
                'report_success': False,
                'error': err
            }
        
        if not archive_temp_path or not os.path.isfile(archive_temp_path):
            err = f"归档失败：档案临时文件路径无效或文件不存在: '{archive_temp_path}'。"
            logger.error(err)
            return {
                'success': False, 
                'archive_success': False,
                'report_success': False,
                'error': err
            }

        # 2. 检查报告文件状态
        report_available = bool(report_temp_path and os.path.isfile(report_temp_path))
        report_error = None
        
        if not report_available:
            if report_temp_path is None:
                report_error = "报告路径未提供"
                logger.info(f"编号 {unified_number}: 报告路径未提供，将只处理档案归档")
            else:
                report_error = f"报告临时文件路径无效或文件不存在: '{report_temp_path}'"
                logger.warning(f"编号 {unified_number}: {report_error}，将只处理档案归档")

        try:
            # 3. 生成最终文件名
            archive_filename = f"{unified_number}_archive.pdf"
            report_filename = f"{unified_number}_report.pdf"

            # 4. 获取最终存储目录 (此方法会确保目录存在)
            final_directory_path = FileStorageService.get_archive_storage_path(
                unified_number=unified_number
            )
            if not final_directory_path:
                err = "归档失败：无法确定最终存储目录。"
                logger.error(err)
                return {
                    'success': False, 
                    'archive_success': False,
                    'report_success': False,
                    'error': err
                }

            # 5. 构建最终完整路径
            archive_final_path = os.path.join(final_directory_path, archive_filename)
            report_final_path = os.path.join(final_directory_path, report_filename) if report_available else None

            # 6. 检查档案文件名冲突
            if os.path.exists(archive_final_path):
                logger.warning(f"档案目标文件已存在，将被覆盖: '{archive_final_path}'")

            # 7. 执行档案文件移动（必须成功）
            logger.info(f"准备移动档案文件到: '{archive_final_path}'...")
            shutil.move(archive_temp_path, archive_final_path)
            logger.info(f"档案文件成功移动到归档位置")
            archive_success = True

            # 8. 执行报告文件移动（可选）
            report_success = False
            if report_available:
                try:
                    # 检查报告文件名冲突
                    if os.path.exists(report_final_path):
                        logger.warning(f"报告目标文件已存在，将被覆盖: '{report_final_path}'")
                    
                    logger.info(f"准备移动报告文件到: '{report_final_path}'...")
                    shutil.move(report_temp_path, report_final_path)
                    logger.info(f"报告文件成功移动到归档位置")
                    report_success = True
                except (OSError, IOError) as e:
                    report_error = f"报告文件移动失败: {e}"
                    logger.error(f"编号 {unified_number}: {report_error}")
                    report_final_path = None
                    # 报告失败不影响整体成功，因为档案已成功

            # 9. 生成访问URL
            from ..services.record_update_service import generate_file_url
            archive_file_url = generate_file_url(archive_final_path)
            report_file_url = generate_file_url(report_final_path) if (report_success and report_final_path) else None

            # 10. 构建返回结果
            result = {
                'success': True,  # 至少档案成功就算整体成功
                'archive_success': archive_success,
                'report_success': report_success,
                'archive_final_path': archive_final_path,
                'report_final_path': report_final_path,
                'archive_file_url': archive_file_url,
                'report_file_url': report_file_url,
            }
            
            if report_error:
                result['report_error'] = report_error
                
            logger.info(f"编号 {unified_number} 归档完成: 档案{'成功' if archive_success else '失败'}, 报告{'成功' if report_success else '失败'}")
            return result

        except (OSError, IOError) as e:
            err = f"档案文件移动/IO操作失败: {e}"
            logger.error(err, exc_info=True)
            return {
                'success': False, 
                'archive_success': False,
                'report_success': False,
                'error': err
            }
        except Exception as e:
            err = f"归档过程中发生意外错误: {e}"
            logger.error(err, exc_info=True)
            return {
                'success': False, 
                'archive_success': False,
                'report_success': False,
                'error': err
            }

    @staticmethod
    def generate_file_hash(file_path: str) -> Optional[str]:
        """
        生成文件哈希值 (SHA-256)，用于唯一标识和验证。

        Args:
            file_path: 文件路径。

        Returns:
            str: SHA-256 哈希值 (十六进制字符串)，如果出错则返回 None。
        """
        # import hashlib # Moved to top level
        # import logging # Moved to top level

        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                # 分块读取文件，适用于大文件
                for byte_block in iter(lambda: f.read(4096 * 1024), b""): # Read in 4MB chunks
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except FileNotFoundError:
             logger.error(f"生成哈希失败：文件未找到 '{file_path}'")
             return None
        except Exception as e:
            logger.error(f"生成文件哈希失败 for '{file_path}': {e}", exc_info=True)
            return None

    # @staticmethod
    # def ensure_directory_exists(directory_path: str) -> bool:
    #     """ (不再需要单独调用) 确保目录存在，如果不存在则创建。"""
    #     # import os # Moved to top level
    #     # import logging # Moved to top level
    #     # logger.debug(f"Ensuring directory exists: {directory_path}")
    #     # try:
    #     #     os.makedirs(directory_path, exist_ok=True)
    #     #     return True
    #     # except Exception as e:
    #     #     logger.error(f"创建目录失败: {directory_path}, 错误: {e}", exc_info=True)
    #     #     return False

    @staticmethod
    def get_temp_directory() -> Path:
        """
        获取一个用于处理过程的临时目录路径。

        尝试使用 Django settings 中的 TEMP_FILE_DIR，如果未配置，
        则在系统临时目录下创建一个带时间戳的项目特定子目录。

        Returns:
            Path: 临时目录的 Path 对象。
                  即使创建失败也会回退到系统临时目录。
        """
        # import os # Moved to top level
        # import tempfile # Moved to top level
        # import time # Moved to top level
        # from pathlib import Path # Moved to top level
        # from django.conf import settings # Moved to top level
        # import logging # Moved to top level

        try:
            # 尝试从 settings 获取基础临时目录
            temp_base = getattr(settings, 'TEMP_FILE_DIR', None)
            if not temp_base:
                # 如果未配置，使用系统临时目录下的项目子目录
                temp_base = os.path.join(tempfile.gettempdir(), 'archive_flow_manager_temp')
                logger.info(f"TEMP_FILE_DIR 未配置，使用默认临时基础目录: '{temp_base}'")
            else:
                 logger.debug(f"使用配置的 TEMP_FILE_DIR: '{temp_base}'")

            # 添加时间戳子目录，以隔离不同的处理任务
            # 改用 UUID 提高唯一性，防止极短时间内任务冲突
            # timestamp = time.strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())
            temp_dir = os.path.join(temp_base, unique_id)

            # 确保目录存在
            try:
                os.makedirs(temp_dir, exist_ok=True)
                logger.info(f"获取或创建临时工作目录: '{temp_dir}'")
                return Path(temp_dir)
            except OSError as e:
                logger.error(f"创建临时目录失败: '{temp_dir}', 错误: {e}", exc_info=True)
                # 回退到系统临时目录根目录
                logger.warning("回退到系统临时目录根目录。")
                return Path(tempfile.gettempdir())

        except Exception as e:
            logger.error(f"获取临时目录时发生意外错误: {e}", exc_info=True)
            # 最终回退
            logger.warning("获取临时目录时发生未知错误，回退到系统临时目录根目录。")
            return Path(tempfile.gettempdir())

    @staticmethod
    def backup_archive_file(file_path: str, backup_type: str = "daily") -> Optional[Dict[str, Any]]:
        """
        备份档案文件到按时间组织的备份目录。

        Args:
            file_path (str): 要备份的文件路径。
            backup_type (str): 备份周期类型，可选值："daily", "weekly", "monthly"。
                                (目前仅实现 daily 结构)。

        Returns:
            Optional[Dict[str, Any]]: 包含备份信息的字典 {'backup_path': str, 'hash': str}，
                                     如果备份失败则返回 None。
        """
        # import os # Moved to top level
        # import shutil # Moved to top level
        # from datetime import datetime # Moved to top level
        # from django.conf import settings # Moved to top level
        # import logging # Moved to top level

        try:
            # 1. 确保文件存在
            if not os.path.isfile(file_path):
                logger.error(f"备份失败：文件不存在或不是文件 '{file_path}'")
                return None

            # 2. 生成文件哈希 (用于验证和可能的去重)
            file_hash = FileStorageService.generate_file_hash(file_path)
            if not file_hash:
                logger.warning(f"备份警告：无法为 '{file_path}' 生成哈希值，将继续备份但不包含哈希信息。")

            # 3. 创建备份目录结构
            backup_base = getattr(settings, 'BACKUP_STORAGE_ROOT', os.path.join(settings.MEDIA_ROOT, 'backups'))
            if not backup_base:
                 logger.error("备份失败：未配置 BACKUP_STORAGE_ROOT 或 MEDIA_ROOT。")
                 return None

            now = datetime.now()
            year_dir = str(now.year)
            # TODO: [P3] 实现 weekly 和 monthly 的逻辑
            # if backup_type == "weekly": ... elif backup_type == "monthly": ...
            # 默认按日备份
            month_dir = f"{now.month:02d}"
            day_dir = f"{now.day:02d}"
            backup_dir = os.path.join(backup_base, year_dir, month_dir, day_dir)

            try:
                os.makedirs(backup_dir, exist_ok=True)
            except OSError as e:
                logger.error(f"创建备份目录失败: '{backup_dir}', 错误: {e}", exc_info=True)
                return None

            # 4. 生成备份文件名 (保留原名 + 时间戳 + 哈希片段)
            original_basename = os.path.basename(file_path)
            timestamp_str = now.strftime("%Y%m%d_%H%M%S")
            hash_suffix = f"_{file_hash[:8]}" if file_hash else ""
            backup_filename = f"{timestamp_str}{hash_suffix}_{original_basename}"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 5. 检查备份是否已存在 (基于路径)
            if os.path.exists(backup_path):
                logger.info(f"备份已存在，跳过: '{backup_path}'")
                # 可以选择验证哈希是否一致
                # existing_hash = FileStorageService.generate_file_hash(backup_path)
                # if file_hash and existing_hash == file_hash:
                #     logger.info("现有备份文件哈希一致。")
                #     return {'backup_path': backup_path, 'hash': file_hash, 'status': 'exists'}
                # else:
                #     logger.warning(f"现有备份文件哈希不一致或无法验证！原始: {file_hash}, 现有: {existing_hash}")
                #     # 决定是否覆盖或创建新版本？暂时返回存在状态
                return {'backup_path': backup_path, 'hash': file_hash, 'status': 'exists'}

            # 6. 执行备份 (复制文件)
            logger.info(f"开始备份 '{file_path}' 到 '{backup_path}'...")
            shutil.copy2(file_path, backup_path) # copy2 保留元数据
            logger.info("文件备份成功。")

            return {'backup_path': backup_path, 'hash': file_hash, 'status': 'created'}

        except Exception as e:
            logger.error(f"备份文件 '{file_path}' 时发生意外错误: {e}", exc_info=True)
            return None

    @staticmethod
    def verify_file_integrity(file_path: str, original_hash: Optional[str] = None) -> bool:
        """
        验证文件的完整性，可以选择与原始哈希值进行比较。

        Args:
            file_path (str): 要验证的文件路径。
            original_hash (str, optional): 原始的SHA256哈希值。

        Returns:
            bool: 如果文件存在且（如果提供了哈希）哈希匹配，则返回 True。
        """
        if not os.path.isfile(file_path):
            logger.warning(f"完整性验证失败：文件不存在于 '{file_path}'")
            return False

        if original_hash:
            current_hash = FileStorageService.generate_file_hash(file_path)
            if current_hash != original_hash:
                logger.warning(f"完整性验证失败：哈希不匹配 for '{file_path}'")
                return False
            else:
                logger.info(f"文件完整性验证成功（哈希匹配） for '{file_path}'")
        else:
            logger.info(f"文件存在性验证成功 for '{file_path}' (未提供哈希)")

        return True

    @staticmethod
    def archive_summary_report(task_id: str, temp_report_path: str) -> Optional[str]:
        """
        将生成的摘要报告从临时位置归档到永久存储位置。

        Args:
            task_id (str): 关联的任务ID，用于生成文件名。
            temp_report_path (str): 临时报告文件的路径。

        Returns:
            Optional[str]: 归档后的永久文件路径，如果失败则返回None。
        """
        logger.info(f"任务 {task_id}: 开始归档摘要报告...")

        if not temp_report_path or not os.path.isfile(temp_report_path):
            logger.error(f"归档报告失败：临时报告路径无效或文件不存在: '{temp_report_path}'")
            return None

        try:
            # 1. 定义报告的永久存储根目录
            base_path = getattr(settings, 'REPORTS_STORAGE_ROOT', os.path.join(settings.MEDIA_ROOT, 'reports'))
            now = datetime.now()
            year_dir = str(now.year)
            month_dir = f"{now.month:02d}"
            permanent_dir = os.path.join(base_path, year_dir, month_dir)

            # 2. 确保目录存在
            os.makedirs(permanent_dir, exist_ok=True)

            # 3. 生成永久文件名 - 保持原始文件扩展名
            original_filename = os.path.basename(temp_report_path)
            _, original_ext = os.path.splitext(original_filename)
            permanent_filename = f"summary_{task_id}_{now.strftime('%Y%m%d%H%M%S')}{original_ext}"
            permanent_path = os.path.join(permanent_dir, permanent_filename)
            
            # 4. 检查文件名冲突 (虽然不太可能，但为了健壮性)
            if os.path.exists(permanent_path):
                logger.warning(f"报告目标文件已存在，将被覆盖: '{permanent_path}'")

            # 5. 移动文件
            shutil.move(temp_report_path, permanent_path)
            logger.info(f"任务 {task_id}: 摘要报告成功移动到永久位置: {permanent_path}")
            
            return permanent_path

        except Exception as e:
            logger.error(f"任务 {task_id}: 归档摘要报告时发生意外错误: {e}", exc_info=True)
            return None


# --- 移除旧的独立函数定义 --- 