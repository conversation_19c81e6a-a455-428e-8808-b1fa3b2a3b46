"""
权限管理模块 - 提供集中式权限配置

该模块提供了各种权限配置，可根据不同环境（开发、生产）切换，
便于管理整个项目的API权限设置。
"""

from django.conf import settings
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
import logging

# 获取日志记录器
logger = logging.getLogger(__name__)

# 权限模式枚举
DEBUG_MODE = 'debug'   # 调试模式：需要认证，推荐使用superuser账户
PROD_MODE = 'prod'     # 生产模式：严格权限控制

# 移除在模块级别对settings的访问，这是导致启动错误的根源
# CURRENT_MODE = getattr(settings, 'PERMISSION_MODE', PROD_MODE)

# 日志应在需要时记录，而不是在导入时
# logger.info(f"Current permission mode: {CURRENT_MODE}")

# 是否处于调试模式下
def is_debug_mode():
    """
    检查当前是否处于调试模式
    
    返回:
        bool: 是否处于调试模式
    """
    # 在函数内部安全地访问settings
    current_mode = getattr(settings, 'PERMISSION_MODE', PROD_MODE)
    return current_mode == DEBUG_MODE

# 根据当前模式获取权限类
def get_permission_classes():
    """
    根据当前模式返回合适的权限类
    
    返回:
        list: 权限类列表，始终返回IsAuthenticated
    """
    # 在函数内部安全地访问settings
    current_mode = getattr(settings, 'PERMISSION_MODE', PROD_MODE)
    logger.info(f"Current permission mode: {current_mode}")
    # 所有模式都返回IsAuthenticated
    return [IsAuthenticated]

# 用于替换视图中权限设置的装饰器
def permission_mode_aware(view_class):
    """
    装饰器：使视图根据当前模式自动调整权限
    
    CHANGE: [2024-08-05] 移除demo模式，所有模式都需要认证
    推荐使用superuser账户进行开发和测试
    
    参数:
        view_class: 要装饰的视图类
        
    返回:
        class: 调整了权限设置的视图类
    """
    logger.debug(f"Setting {view_class.__name__} permissions to IsAuthenticated")
    view_class.permission_classes = [IsAuthenticated]
    
    return view_class

# API视图基类，自动根据当前模式设置权限
class PermissionModeAPIView(APIView):
    """
    API视图基类，自动根据当前模式设置权限
    
    继承此类的视图将根据当前权限模式自动调整权限设置
    
    CHANGE: [2024-08-05] 移除demo模式，所有模式都需要认证
    """
    @classmethod
    def as_view(cls, **initkwargs):
        view = super().as_view(**initkwargs)
        
        logger.debug(f"Setting {cls.__name__} view permissions to IsAuthenticated")
        view.permission_classes = [IsAuthenticated]
            
        return view 