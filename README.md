# 档案流管理系统 (Archive Flow Manager)

*(项目简介，可以稍后补充)*

## 开发环境设置 (Docker)

本项目推荐使用 Docker 和 Docker Compose 进行本地开发，以确保环境一致性并简化服务管理（包括 Django、Celery Worker、Redis 和 前端）。

### 前提条件

* **Docker**: 确保您的系统已安装并运行 Docker Desktop (Windows/Mac) 或 Docker Engine + Docker Compose (Linux)。

### 依赖管理

所有 Python 依赖（包括后端）都已在 Dockerfile 和 docker-compose.yml 中定义，无需在本地手动安装。
前端React依赖将通过pnpm管理。

### 启动项目

1. **打开终端**，确保当前目录是项目的根目录（包含 `docker-compose.yml` 文件）。
2. **首次启动或需要重建镜像时**，运行以下命令：

    ```bash
    docker-compose up --build
    ```

    * `--build` 参数会根据 `Dockerfile` 构建或重新构建服务镜像。
    * 此命令会同时启动 `redis`, `web` (Django), `worker` (Celery) 和 `frontend` (React) 服务。
    * 首次构建可能需要一些时间来下载基础镜像和安装依赖。
3. **(重要) 首次启动或模型更改后运行数据库迁移：**
    在**另一个终端**窗口中，运行以下命令将 Django 数据库迁移应用到 `web` 容器内的数据库：

    ```bash
    docker-compose exec web python manage.py migrate
    ```

4. **后续启动（如果镜像未更改）：**
    如果不需要重新构建镜像，可以简单地运行：

    ```bash
    docker-compose up
    ```

### 访问服务

* **Django 后端 API**: `http://localhost:8000/`
* **React 前端**: `http://localhost:3000/`

### 停止项目

在运行 `docker-compose up` 的终端中按 `Ctrl+C`。或者，在另一个终端中，进入项目根目录，运行：

```bash
docker-compose down
```

* `docker-compose down` 会停止并移除由 `docker-compose up` 创建的容器和网络。
* 如果您希望同时删除 Redis 数据卷（会丢失所有 Redis 数据），可以添加 `-v` 参数：`docker-compose down -v`。

## 前后端API请求模式配置

系统支持两种前端到后端的API请求模式，通过环境变量`NEXT_PUBLIC_USE_PROXY`控制：

### 1. 开发环境 - 代理模式

适用于本地开发和Docker Compose环境：

```yaml
environment:
  NEXT_PUBLIC_API_URL=http://web:8000/api
  NEXT_PUBLIC_USE_PROXY=true
```

**工作原理**：

* 前端请求首先发送到Next.js的API路由`/api/proxy/...`
* Next.js服务器将请求转发到后端服务
* 避免了浏览器跨域限制和容器网络访问问题

### 2. 生产环境 - 直接模式

适用于使用Nginx等反向代理的生产环境：

```yaml
environment:
  NEXT_PUBLIC_API_URL=/api
  NEXT_PUBLIC_USE_PROXY=false
```

**工作原理**：

* 前端请求直接发送到`/api/...`路径
* Nginx配置将这些请求路由到相应的后端服务
* 无需额外的代理层，提高性能

### 如何切换模式

1. 在`docker-compose.yml`或环境配置中修改相应的环境变量
2. 重启前端服务或重新构建容器
3. 检查网络请求确认模式已切换成功

### 故障排查

如遇到API请求失败问题：

* 检查浏览器网络请求，确认URL格式正确
* 开发环境中确认`NEXT_PUBLIC_USE_PROXY=true`
* 生产环境中检查Nginx配置是否正确路由`/api`请求
* 使用环境配置调试功能：打开浏览器控制台，查看`环境配置`日志信息

## 前端开发

项目前端使用React框架开发。

### 技术栈

* React 18+
* TypeScript
* Ant Design 组件库
* React Router 用于路由管理
* Axios 用于API请求

### 目录结构

前端代码位于 `frontend` 目录中，主要结构如下：

```
frontend/
  ├── public/         # 静态资源
  ├── src/            # 源代码
  │   ├── api/        # API请求
  │   ├── components/ # 组件
  │   ├── pages/      # 页面
  │   ├── hooks/      # 自定义Hooks
  │   ├── utils/      # 工具函数
  │   ├── contexts/   # 上下文
  │   ├── App.tsx     # 应用入口
  │   └── index.tsx   # 渲染入口
  ├── package.json    # 依赖定义
  └── tsconfig.json   # TypeScript配置
```

### 本地开发

如果需要单独启动前端开发服务器（不使用Docker），可按以下步骤操作：

1. 进入前端目录:

   ```bash
   cd frontend
   ```

2. 安装依赖:

   ```bash
   pnpm install
   ```

3. 启动开发服务器:

   ```bash
   pnpm dev
   ```

4. 在浏览器中访问 `http://localhost:3000`

*(可以继续添加其他项目说明，如 API 文档链接、测试运行方法等)*
