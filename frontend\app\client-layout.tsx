"use client"

import type React from "react"

import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import Sidebar from "@/components/sidebar"
import Header from "@/components/header"
import { SessionProvider } from "next-auth/react"
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
// TODO: Add NextAuth imports

import { Toaster } from "@/components/ui/toaster"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"
import { initializeAgGrid } from "@/lib/ag-grid-config"
import { queryClient } from "@/lib/query-client"

const inter = Inter({ subsets: ["latin"] })

// 不需要导航的路径
const PATHNAME_WITHOUT_LAYOUT = [
  '/login',
  '/register',
]

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()

  // 初始化AG Grid配置
  useEffect(() => {
    // 仅在客户端执行初始化
    if (typeof window !== 'undefined') {
      initializeAgGrid();
    }
  }, []);

  // 在组件挂载后设置状态，防止服务器端和客户端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  // 检查当前路径是否不需要导航布局
  const isLayoutDisabled = PATHNAME_WITHOUT_LAYOUT.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  )

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <div style={{ visibility: mounted ? 'visible' : 'hidden' }}>
            {isLayoutDisabled ? (
              // 登录页等无导航页面布局
              <div className="h-full w-full">
                {children}
              </div>
            ) : (
              // 主应用布局
              <div className="flex h-screen w-full overflow-hidden">
                <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />
                <div className="flex flex-col flex-1 overflow-hidden">
                  <Header setSidebarOpen={setSidebarOpen} />
                  <div className="flex-1 overflow-hidden">
                    <div className="px-6 pt-1.5 pb-3 h-full">
                      {children}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <Toaster />
        </ThemeProvider>
      </SessionProvider>
    </QueryClientProvider>
  )
}
