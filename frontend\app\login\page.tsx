"use client"

import { useState, useEffect } from "react"
import { signIn, useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Eye, EyeOff, Shield, LogIn } from "lucide-react"
import Image from "next/image"

export default function LoginPage() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'authenticated') {
      router.replace('/dashboard')
    }
  }, [status, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    try {
      const result = await signIn('credentials', {
        username,
        password,
        redirect: false,
      })

      if (result?.error) {
        if (result.error === 'CredentialsSignin') {
            setError('用户名或密码错误，请重试。')
        } else {
            setError(result.error)
        }
      } else if (result?.ok) {
        router.replace('/dashboard')
      }
    } catch (err: any) {
      setError(err.message || "登录失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  if (status === 'loading' || status === 'authenticated') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            {status === 'loading' ? '正在加载...' : '已登录，正在跳转...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-200 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-white/80 dark:bg-black/70 backdrop-blur-sm border-gray-200 dark:border-gray-800 shadow-2xl shadow-gray-300/20 dark:shadow-black/50 rounded-2xl overflow-hidden">
        
        {/* Decorative Glow */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-2/3 h-24 bg-purple-500/20 dark:bg-purple-500/10 blur-3xl rounded-full" />

        <CardHeader className="text-center pt-8 pb-6 relative z-10">
          <div className="flex justify-center mb-4">
            <div className="w-24 h-24 relative p-2 bg-white/50 dark:bg-black/30 rounded-full shadow-inner">
              <Image
                src="/jintao-Fillet.jpg"
                alt="金涛"
                fill
                className="object-contain rounded-full"
                priority
                sizes="96px"
              />
            </div>
          </div>
          
          <CardTitle className="text-3xl font-bold text-gray-900 dark:text-white">
            档案管理系统
          </CardTitle>
          <CardDescription className="mt-2 text-gray-600 dark:text-gray-400">
            欢迎回来，请登录以继续
          </CardDescription>
        </CardHeader>
        
        <CardContent className="px-8 pb-8 relative z-10">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="username">
                用户名
              </Label>
              <Input
                id="username"
                type="text"
                placeholder="请输入用户名"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                disabled={isLoading}
                className="h-12 text-base bg-white/50 dark:bg-black/30 focus:bg-white dark:focus:bg-black transition-all duration-300"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">
                密码
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="请输入密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                  className="h-12 pr-12 text-base bg-white/50 dark:bg-black/30 focus:bg-white dark:focus:bg-black transition-all duration-300"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1 h-10 w-10 text-gray-500 hover:bg-gray-200/50 dark:hover:bg-gray-800/50 rounded-lg"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff /> : <Eye />}
                </Button>
              </div>
            </div>
            
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
              <div className="flex items-center space-x-1.5">
                <Shield className="w-3.5 h-3.5" />
                <span>登录后7天内保持登录状态</span>
              </div>
            </div>
            
            {error && (
              <div className="text-sm font-medium text-red-600 dark:text-red-400 bg-red-100/50 dark:bg-red-900/20 p-3 rounded-lg text-center">
                {error}
              </div>
            )}
            
            <Button 
              type="submit" 
              className="w-full h-12 text-base font-bold text-white bg-blue-500 hover:bg-blue-600 rounded-full transition-all duration-300 ease-in-out hover:scale-105"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>登录中...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <LogIn className="w-5 h-5" />
                  <span>登录</span>
                </div>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
