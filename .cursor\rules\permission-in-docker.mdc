---
description: 
globs: 
alwaysApply: true
---
# Docker环境下的权限管理规则

**作用**: 指导开发人员在Docker容器化环境中正确实现和调试权限控制功能。

**触发指令示例**: "用户权限", "权限检查", "访问控制", "角色管理", "Docker权限"

## 权限系统概述

本项目采用多层权限控制机制:

- **认证层**: JWT令牌验证用户身份
- **权限层**: Django权限系统控制功能访问
- **数据访问层**: 对象级权限控制数据访问

## 权限模式配置

项目支持三种权限模式，通过环境变量`PERMISSION_MODE`控制:

```python
# 权限模式设置
# 可选值: 'debug' (所有API允许匿名访问), 'demo' (部分API允许匿名访问), 'prod' (严格权限控制)
PERMISSION_MODE = os.environ.get('PERMISSION_MODE', 'prod')
```

### 在Docker中切换权限模式

在`docker-compose.yml`中设置环境变量:

```yaml
services:
  web:
    environment:
      - PERMISSION_MODE=debug  # 开发调试时使用
      # - PERMISSION_MODE=prod  # 生产环境使用
```

## 权限实现详情

### 1. 用户权限结构

项目使用Django标准权限系统:

- **User**: 用户实体，包含基本权限
- **Group**: 用户组，包含角色权限集
- **Permission**: 具体权限，如"查看档案"、"添加记录"等

用户权限来源:

1. 直接分配给用户的权限
2. 通过用户组分配的权限

### 2. API权限控制

#### 视图级权限控制

```python
from rest_framework.permissions import IsAuthenticated, IsAdminUser, DjangoModelPermissions

class MyAPIView(APIView):
    # 基本认证保护
    permission_classes = [IsAuthenticated]
    
    # 或者管理员权限
    # permission_classes = [IsAdminUser]
    
    # 或者模型权限
    # permission_classes = [DjangoModelPermissions]
```

#### 自定义权限

```python
from rest_framework import permissions

class CanViewArchive(permissions.BasePermission):
    """
    自定义权限: 允许查看档案
    """
    def has_permission(self, request, view):
        # 检查用户是否有查看档案的权限
        return request.user.has_perm('archive_records.view_archive')
```

### 3. 前端权限检查

前端使用`auth-context.tsx`中的`hasPermission`函数检查权限:

```typescript
// 在组件中检查权限
const { hasPermission } = useAuth();

// 检查是否有特定权限
if (hasPermission('archive_records.view_archive')) {
  // 显示档案查看组件
}
```

## Docker环境中的权限调试

### 1. 查看当前用户权限

在Django shell中:

```bash
docker-compose exec web python manage.py shell

# 在shell中
from django.contrib.auth.models import User
user = User.objects.get(username='admin')
print(user.get_all_permissions())  # 显示所有权限
```

### 2. 添加测试权限

```bash
docker-compose exec web python manage.py shell

# 在shell中
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from archive_records.models import Archive

# 获取模型和用户
content_type = ContentType.objects.get_for_model(Archive)
permission = Permission.objects.get(content_type=content_type, codename='view_archive')
user = User.objects.get(username='testuser')

# 添加权限
user.user_permissions.add(permission)
user.save()
```

### 3. 验证JWT权限

使用curl测试API权限:

```bash
# 获取令牌
TOKEN=$(curl -s -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"adminpassword"}' \
  | grep -o '"token":"[^"]*' | cut -d'"' -f4)

# 使用令牌访问需要权限的API
curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/archive-records/
```

## 权限开发最佳实践

### 1. 权限命名约定

- 使用标准Django权限命名: `app_label.action_model`
- 示例: `archive_records.view_archive`, `archive_records.add_record`

### 2. 权限粒度控制

- **粗粒度**: 视图级权限控制整个API访问
- **细粒度**: 对象级权限控制具体数据访问
- **超细粒度**: 字段级权限控制敏感字段

### 3. 权限测试

为每个权限控制编写测试:

```python
def test_archive_view_permission(self):
    """测试用户需要适当权限才能查看档案"""
    # 创建没有权限的用户
    user = User.objects.create_user(username='noperm', password='password')
    self.client.force_authenticate(user=user)
    
    # 尝试访问需要权限的API
    response = self.client.get('/api/archive-records/')
    self.assertEqual(response.status_code, 403)  # 应该返回403 Forbidden
    
    # 添加权限
    permission = Permission.objects.get(codename='view_archive')
    user.user_permissions.add(permission)
    
    # 再次尝试访问
    response = self.client.get('/api/archive-records/')
    self.assertEqual(response.status_code, 200)  # 现在应该成功
```

## 常见问题及解决方案

### 1. 权限不生效

检查点:

- JWT认证是否成功(检查request.user是否正确)
- 权限是否正确分配(检查数据库)
- 权限名称是否正确(检查拼写)

### 2. 容器内用户权限

注意:

- 容器重启可能导致数据库重置
- 使用Django migrations或fixtures确保权限固定
- 考虑使用数据卷持久化用户数据

### 3. 跨服务权限验证

处理方法:

- 使用共享JWT密钥
- 实现服务间权限验证
- 考虑使用API网关进行集中权限控制

