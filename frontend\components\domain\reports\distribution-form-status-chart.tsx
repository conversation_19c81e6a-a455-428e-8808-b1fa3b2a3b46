"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

export function DistributionFormStatusChart() {
  // 模拟数据 - 实际应用中应从API获取
  const total = 1284
  const data = [
    { name: "草稿状态", value: 42, color: "#f59e0b" },
    { name: "待处理", value: 156, color: "#3b82f6" },
    { name: "处理中", value: 118, color: "#6366f1" },
    { name: "已完成", value: 968, color: "#10b981" },
  ]

  // 计算百分比
  const getPercentage = (value: number) => {
    return Math.round((value / total) * 100)
  }

  // 获取颜色
  const getColor = (name: string) => {
    switch (name) {
      case "草稿状态":
        return "rgb(245, 158, 11)" // amber-500
      case "待处理":
        return "rgb(59, 130, 246)" // blue-500
      case "处理中":
        return "rgb(99, 102, 241)" // indigo-500
      case "已完成":
        return "rgb(16, 185, 129)" // emerald-500
      default:
        return "rgb(107, 114, 128)" // gray-500
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>发放单状态分布</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {data.map((item) => (
          <div key={item.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{item.name}</span>
              <span className="text-sm font-medium">
                {item.value}/{total} ({getPercentage(item.value)}%)
              </span>
            </div>
            <Progress
              value={getPercentage(item.value)}
              className="h-2"
              style={{ "--indicator-color": getColor(item.name) } as React.CSSProperties}
            />
          </div>
        ))}
      </CardContent>
      <CardFooter>
        <Link href="/reports/management" className="text-sm font-medium flex items-center hover:underline">
          查看详细统计 <ArrowRight className="ml-1 h-4 w-4" />
        </Link>
      </CardFooter>
    </Card>
  )
}
