#!/usr/bin/env python3
"""
现代化的 OCR 服务启动脚本
支持开发和生产环境
"""
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_development():
    """运行开发环境"""
    import uvicorn
    from app.main import app
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="debug",
        access_log=True
    )


def run_production():
    """运行生产环境"""
    import uvicorn
    from app.main import app
    
    # 生产环境配置
    workers = int(os.getenv('OCR_WORKERS', '1'))
    host = os.getenv('OCR_HOST', '0.0.0.0')
    port = int(os.getenv('OCR_PORT', '8001'))
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        workers=workers,
        log_level="info",
        access_log=True,
        loop="uvloop",  # 高性能事件循环
        http="httptools"  # 高性能 HTTP 解析器
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OCR 微服务启动器")
    parser.add_argument(
        '--env', 
        choices=['dev', 'prod'], 
        default='prod',
        help='运行环境 (dev=开发, prod=生产)'
    )
    
    args = parser.parse_args()
    
    print(f"🚀 启动 OCR 微服务 ({args.env} 环境)")
    
    if args.env == 'dev':
        run_development()
    else:
        run_production()


if __name__ == "__main__":
    main()
