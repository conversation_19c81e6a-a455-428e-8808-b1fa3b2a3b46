# archive_processing/utils/image_utils.py
"""
图像处理工具模块

此模块包含与图像处理相关的公共函数，主要用于OCR前的图像预处理和增强。
包括:
1. 为Tesseract和PaddleOCR优化的图像预处理函数
2. 图像增强函数，生成多个处理版本以提高OCR识别率
3. 图像哈希计算，用于缓存OCR结果

从pdf_processor_usefull.py中提取和重构，作为独立模块以提高代码组织性和可维护性。
"""
import logging
from typing import List
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
import cv2 # OpenCV for image processing
import time  # For fallback hash generation

logger = logging.getLogger(__name__)

def prepare_standard_image_for_tesseract(image: Image.Image) -> Image.Image:
    """
    为 Tesseract 准备标准的单个预处理图像 (灰度化 + Otsu二值化)
    
    Args:
        image: 原始 PIL 图像对象
        
    Returns:
        处理后的 PIL 图像对象 (二值化)
    """
    try:
        img_gray = image.convert('L')
        img_array = np.array(img_gray)
        # 使用 Otsu's thresholding
        _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        logger.debug("Image preprocessed for Tesseract using grayscale and Otsu thresholding.")
        return Image.fromarray(binary)
    except Exception as e:
        logger.error(f"Error during Tesseract preprocessing: {e}", exc_info=True)
        # 返回原始灰度图作为后备
        return image.convert('L')

def prepare_standard_image_for_paddle(image: Image.Image) -> Image.Image:
    """
    为 PaddleOCR 准备标准的单个预处理图像 (确保是 RGB)
    
    Args:
        image: 原始 PIL 图像对象
        
    Returns:
        处理后的 PIL 图像对象 (RGB)
    """
    if image.mode != 'RGB':
        logger.debug("Converting image to RGB for PaddleOCR.")
        return image.convert('RGB')
    return image

def get_enhanced_images_for_paddle(image: Image.Image) -> List[Image.Image]:
    """
    创建多个增强版本的 RGB 图像，用于 PaddleOCR 识别尝试
    
    Args:
        image: 原始 RGB PIL 图像对象
            
    Returns:
        处理后的 RGB PIL 图像列表
    """
    processed_images = []
    try:
        # 确保输入是 RGB
        rgb_image = prepare_standard_image_for_paddle(image)
        # processed_images.append(rgb_image) # 添加原始RGB图像，原始图像已执行过OCR

        # 1. 增强对比度
        enhanced_contrast = ImageEnhance.Contrast(rgb_image).enhance(1.5)
        processed_images.append(enhanced_contrast)
        
        # 2. 亮度增强
        enhanced_bright = ImageEnhance.Brightness(rgb_image).enhance(1.2)
        processed_images.append(enhanced_bright)
        
        # 3. 锐化处理
        sharpened = rgb_image.filter(ImageFilter.SHARPEN)
        processed_images.append(sharpened)
        
        # 4. 对比度和锐化结合
        contrast_sharp = ImageEnhance.Contrast(sharpened).enhance(1.3)
        processed_images.append(contrast_sharp)

        logger.debug(f"Generated {len(processed_images)} enhanced RGB images for PaddleOCR.")

    except Exception as e:
        logger.error(f"Error generating enhanced images for Paddle: {e}", exc_info=True)
        # 如果出错，至少返回原始RGB图像
        if not processed_images:
            processed_images.append(prepare_standard_image_for_paddle(image))
            
    return processed_images

def get_enhanced_images_for_tesseract(image: Image.Image) -> List[Image.Image]:
    """
    创建多个增强版本的灰度/二值化图像，用于 Tesseract 识别尝试
    
    Args:
        image: 原始 PIL 图像对象
            
    Returns:
        处理后的灰度/二值化 PIL 图像列表
    """
    processed_images = []
    try:
        img_gray = image.convert('L')
        img_array = np.array(img_gray)
        processed_images.append(Image.fromarray(img_array)) # 添加原始灰度图

        # 1. 对比度增强的灰度图
        enhanced_contrast = ImageEnhance.Contrast(img_gray).enhance(2.0)
        processed_images.append(enhanced_contrast)
        
        # 2. 锐化处理后的灰度图
        # Tesseract有时在锐化过度的图像上表现不佳，谨慎使用或提供不同程度锐化
        sharpened = image.filter(ImageFilter.SHARPEN) 
        sharpened_gray = sharpened.convert('L')
        processed_images.append(sharpened_gray)
        
        # 3. 自适应二值化 (Gaussian)
        block_size = 35  # 需要根据图像特性调整
        c_value = 10
        adaptive_thresh_gauss = cv2.adaptiveThreshold(
            img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, block_size, c_value
        )
        processed_images.append(Image.fromarray(adaptive_thresh_gauss))

        # 4. Otsu 二值化 (现在由 prepare_standard_image_for_tesseract 实现)
        otsu_binary_image = prepare_standard_image_for_tesseract(image)
        processed_images.append(otsu_binary_image)

        # 5. 轻微膨胀后腐蚀 (尝试连接断裂字符) - 可能效果不佳，谨慎使用
        # kernel = np.ones((2, 2), np.uint8)
        # img_dilated = cv2.dilate(img_array, kernel, iterations=1)
        # img_eroded = cv2.erode(img_dilated, kernel, iterations=1)
        # processed_images.append(Image.fromarray(img_eroded))

        logger.debug(f"Generated {len(processed_images)} enhanced grayscale/binary images for Tesseract.")

    except Exception as e:
        logger.error(f"Error generating enhanced images for Tesseract: {e}", exc_info=True)
         # 如果出错，至少返回原始灰度图
        if not processed_images:
             processed_images.append(image.convert('L'))
            
    return processed_images 

def image_hash(image: Image.Image) -> str:
    """
    生成图像的哈希值用于缓存
    
    Args:
        image: 原始 PIL 图像对象
        
    Returns:
        图像的哈希值字符串
    """
    if not image:
        return ""
    try:
        # 缩小图像计算哈希值
        small_img = image.resize((32, 32), Image.LANCZOS).convert('L')
        pixels = list(small_img.getdata())
        avg = sum(pixels) / len(pixels)
        hash_value = ''.join('1' if p > avg else '0' for p in pixels)
        return hash_value
    except Exception as e:
        logger.error(f"计算图像哈希值失败: {e}")
        return str(time.time())  # 失败时返回时间戳作为备用哈希 