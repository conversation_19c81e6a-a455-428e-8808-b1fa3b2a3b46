import apiClient, { type ApiResponse } from '@/lib/apiClient';

// 对齐Django的Group模型
export interface UserGroup {
  id: number;
  name: string;
}

// 对齐Django的User模型，由DRF返回
export interface UserData {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  groups: UserGroup[];
}

/**
 * 获取单个用户的数据
 * @param userId - 要获取的用户的ID
 * @returns 返回包含用户数据的API响应
 */
export const getUser = async (
  userId: string
): Promise<ApiResponse<UserData>> => {
  const endpoint = `/api/users/${userId}/`;
  return await apiClient.get<UserData>(endpoint);
};

/**
 * 更新用户数据
 * @param userId - 要更新的用户的ID
 * @param data - 要更新的部分用户数据
 * @returns 返回包含已更新用户数据的API响应
 */
export const updateUser = async (
  userId: string,
  data: Partial<UserData>
): Promise<ApiResponse<UserData>> => {
  const endpoint = `/api/users/${userId}/`;
  return await apiClient.patch<UserData>(endpoint, data);
}; 