# 发放单模块：服务架构与工作流指南

## 1. 概述

本文档旨在总结和固化发放单模块（`report_issuing`）的后端服务架构。它为所有状态（草稿、锁定、已归档等）的开发提供统一的指导，确保代码的组织方式清晰、职责明确、易于维护。

本架构的核心思想是 **职责分离** 和 **分层解耦**。

---

## 2. 宏观架构：多层分离模型

我们将业务逻辑清晰地划分到不同的层次，每一层都有其独特的职责。

```mermaid
graph TD
    subgraph "流程与状态处理 (Process & State Handling)"
        direction LR
        A["API 视图 (Views)"] --> B{"FormStateMachine (总机)"};
        A --> QS["Query Services (独立查询)"];
        B --> C{"State Handlers (协调员)"};
        C --> D["State-Specific Services (状态业务专家)"];
        A --> F["Stateless Services (流程启动器)"];
    end

    subgraph "基础服务 (Foundational Services)"
        direction LR
        D --> E["Data Services (数据库守门人)"];
        F --> E;
        D --> US["Utility Services (领域计算)"];
        F --> US;
    end

    style F fill:#cde4ff
    style QS fill:#cde4ff
    style US fill:#cde4ff
    style E fill:#d5e8d4
```

- **State Handlers (协调员)**: 状态机的一部分，仅负责**决策**和**委托**。
- **State-Specific Services (状态业务专家)**: 负责实现特定状态下**所有复杂业务逻辑**的"写"操作。
- **Stateless Services (流程启动器)**: 提供**启动新流程**的入口"写"操作。
- **Query Services (独立查询)**: 提供可被API层直接调用的、独立的**"读"操作**。
- **Utility Services (领域计算)**: 提供无状态的、可重用的**纯业务计算**，服务于其他服务。
- **Data Services (数据库守门人)**: 唯一负责与**数据库**直接交互的层次。

---

## 3. 文件组织与组件职责

为了实现上述架构，我们约定了严格的文件组织方式。所有新功能都必须遵循此约定。

### 3.1. `state_handlers/` - 状态协调员

- **职责**: 状态机模式的核心，控制特定状态下的行为出口和状态流转。**不包含复杂业务逻辑**，仅做决策和委托。
- **示例**: `draft_handler.py`

```python
# report_issuing/state_handlers/draft_handler.py

class DraftHandler(BaseStateHandler):
    """草稿状态的协调员。负责接收指令，并将其委托给专门的业务服务。"""
    
    def __init__(self, form, user_id):
        """注入草稿状态的专属业务服务。"""
        # ...
        self.draft_service = DraftStateService(user_id)

    def update_form(self, update_data):
        """委托：更新发放单的基本信息。"""
        self.draft_service.update_issue_form(...)

    def update_items(self, to_add, to_remove):
        """委托：更新发放单条目。"""
        self.draft_service.update_issue_form_items(...)

    def lock(self):
        """决策：将状态转换为 LOCKED。这是状态流转操作，由 Handler 自己决策和执行。"""
        # ...
```

### 3.2. `services/state_specific/` - 状态业务专家

- **职责**: 实现特定状态下允许的所有操作的**具体业务逻辑**。
- **示例**: `draft_state_service.py`

```python
# report_issuing/services/state_specific/draft_state_service.py

class DraftStateService:
    """提供发放单在"草稿"状态下所有允许操作的具体业务逻辑实现。"""

    def update_issue_form(self, form_id: int, update_data: Dict[str, Any]):
        """更新指定的草稿发放单的基本信息。"""
        # ...

    def update_issue_form_items(self, form_id: int, items_to_add: List[int], items_to_remove: List[int]):
        """为指定的草稿发放单更新条目。"""
        # ...
```

### 3.3. `services/stateless/` - 流程启动器

- **职责**: 作为发起一个全新业务流程的入口，**不依赖任何已有状态**。
- **示例**: `issue_initialization_service.py`

```python
# report_issuing/services/stateless/issue_initialization_service.py

class IssueInitializationService:
    """负责初始化一个新的发放流程，核心职责是创建包含初始条目的草稿发放单。"""
    
    def create_draft_issue_form_with_items(self, form_data, items_data):
        """一步到位：在一个事务中，验证并创建一个带初始条目的新草稿发放单。"""
        # 1. 验证条目 (调用 Utility Service)
        # 2. 创建表单 (调用 Data Service)
        # ...
```

### 3.4. `services/query_services/` - 独立查询服务

- **职责**: 提供独立的、只读的业务数据查询。通常直接被API视图层调用，用于向前端提供数据展示所需的信息。
- **示例**: `issue_query_service.py`

```python
# report_issuing/services/query_services/issue_query_service.py

class IssueQueryService:
    """查询服务：负责提供独立的、只读的业务数据查询。"""

    def get_issuable_archives(self, search_params):
        """查询可用于创建或编辑发放单的档案条目。"""
        # ...
```

### 3.5. `services/utility_services/` - 领域工具服务

- **职责**: 提供无状态的、可重用的纯业务计算或验证逻辑。这是一个"服务于服务的服务"，被其他业务服务（如流程启动器、状态业务专家）调用。**不应被API层直接调用**。
- **示例**: `issue_rule_service.py`

```python
# report_issuing/services/utility_services/issue_rule_service.py

class IssueRuleService:
    """领域工具服务：提供纯业务计算逻辑。"""

    def calculate_issue_quantity(self, archive_item_id: int):
        """计算档案项的发放数量和选项。"""
        # ...
```

### 3.6. `services/data_services/` - 数据库守门人

- **职责**: **唯一**与数据库直接交互的层。封装所有 ORM 调用，提供原子化的数据操作接口。**所有方法都应是静态的**，不持有状态。
- **示例**: `issue_form_data_service.py`

```python
# report_issuing/services/data_services/issue_form_data_service.py

class IssueFormDataService:
    """数据服务层：负责所有与 IssueForm 和 IssueFormItem 模型相关的数据库操作。"""

    @staticmethod
    @transaction.atomic
    def create_form_with_items(form_data, items_data, user):
        """在一个事务中，创建主发放单和其关联的初始条目。"""
        # IssueForm.objects.create(...)
        # IssueFormItem.objects.bulk_create(...)
        # ...

    @staticmethod
    def get_form_by_id(form_id: int):
        """根据ID获取一个 IssueForm 实例。"""
        # ...
```

---

## 4. 核心工作流：一步到位创建发放单

以下是用户点击"创建"按钮后，一个请求在后端系统中的完整旅程，它完美地展示了各层如何协同工作：

1. **API 视图**接收到请求，包含 `form_data` 和 `items_data`。
2. 视图实例化并调用 **`IssueInitializationService.create_draft_issue_form_with_items()`**。
3. `IssueInitializationService` 开始执行：
    a.  它循环遍历 `items_data`。
    b.  在循环中，它调用 **`IssueRuleService.calculate_issue_quantity()`** 来验证每个条目的业务规则。
    c.  如果所有条目都通过验证，它将打包好的数据一次性委托给 **`IssueFormDataService.create_form_with_items()`**。
4. `IssueFormDataService` 在一个**数据库事务**中，执行 `INSERT` 操作，创建 `IssueForm` 和 `IssueFormItem` 记录。
5. 操作成功后，逐层返回，最终 API 视图向前端返回成功响应。

这个流程确保了业务逻辑的校验、流程的编排和数据的持久化被清晰地分离在不同的层次中，使得整个系统非常健壮和易于理解。
