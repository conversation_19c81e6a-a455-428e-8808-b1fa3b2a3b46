# report_issuing/services/query_services/issue_query_service.py
import logging
from typing import Dict, Any, List, Tuple
from django.db.models import Q, F, Value
from django.db.models.functions import Coalesce
from archive_records.models import ArchiveRecord
from datetime import datetime

logger = logging.getLogger(__name__)

class IssueQueryService:
    """
    查询服务：负责提供独立的、只读的业务数据查询。
    通常直接被API视图层调用，用于向前端提供数据展示所需的信息。
    """
    def __init__(self, user_id: int):
        self.user_id = user_id
        # 此处可以注入数据服务
        # self.archive_data_service = ArchiveDataService()

    def get_issuable_archives(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询可用于创建或编辑发放单的档案条目。
        该查询首先筛选出剩余报告份数大于0的档案，然后应用前端传递的筛选条件。
        支持 AG Grid 服务端筛选和分页模式。
        """
        logger.info(f"用户 {self.user_id} 正在查询可发放的档案，参数: {search_params}")

        # CHANGE: [2025-06-17] 修复None值问题：使用Coalesce将None值转换为0
        # 核心业务规则：可发放的档案 = 总发放份数 > (第一次发放份数 + 第二次发放份数)
        # 这里的 "total_issue_copies" 被理解为报告的总份数，而不是已发放份数的和。
        # 使用Coalesce确保None值被当作0处理
        base_query = ArchiveRecord.objects.filter(
            total_issue_copies__gt=Coalesce('first_issue_copies', Value(0)) + Coalesce('second_issue_copies', Value(0))
        )

        # 构建动态筛选条件
        filters = Q()
        filter_map = {
            'unified_number': 'unified_number__icontains',
            'sample_number': 'sample_number__icontains',
            'client_unit': 'client_unit__icontains',
            'client_name': 'client_name__icontains',
            'project_number': 'project_number__icontains',
            'project_name': 'project_name__icontains',
            'project_location': 'project_location__icontains',
        }

        for param, lookup in filter_map.items():
            if search_params.get(param):
                filters &= Q(**{lookup: search_params[param]})
        
        # 处理委托日期范围
        if search_params.get('commission_datetime_start'):
            try:
                start_date = datetime.fromisoformat(search_params['commission_datetime_start'])
                filters &= Q(commission_datetime__gte=start_date)
            except (ValueError, TypeError):
                logger.warning(f"无效的起始日期格式: {search_params['commission_datetime_start']}")

        if search_params.get('commission_datetime_end'):
            try:
                end_date = datetime.fromisoformat(search_params['commission_datetime_end'])
                filters &= Q(commission_datetime__lte=end_date)
            except (ValueError, TypeError):
                logger.warning(f"无效的结束日期格式: {search_params['commission_datetime_end']}")

        # 应用筛选条件
        issuable_archives = base_query.filter(filters)
        
        total_count = issuable_archives.count()
        logger.info(f"查询到 {total_count} 条可发放的档案记录。")

        # 处理分页
        try:
            offset = int(search_params.get('offset', 0))
            limit = int(search_params.get('limit', 20)) # 默认每页20条
            paginated_archives = issuable_archives[offset : offset + limit]
        except (ValueError, TypeError):
            paginated_archives = issuable_archives[:20]

        # 格式化并返回结果
        results = list(paginated_archives.values(
            'id',
            'unified_number',
            'sample_number',
            'project_name',
            'project_number',
            'project_location',
            'client_name',
            'client_unit',
            'commission_datetime',
            'total_issue_copies',
            'first_issue_copies',
            'first_issue_datetime',
            'first_issue_person',
            'first_receiver_name',
            'first_receiver_phone',
            'second_issue_copies',
            'second_issue_datetime',
            'second_issue_person',
            'second_receiver_name',
            'second_receiver_phone'
        ))

        return {
            'count': total_count,
            'results': results
        } 