import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union

from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User

from archive_records.models import (
    ArchiveRecord,
    ChangeOrder,
    ChangeOrderItem,
    ChangeLogBatch,
    RecordChangeLog,
    FieldChangeLog
)

logger = logging.getLogger(__name__)

class ChangeOrderService:
    """
    更改单服务类，提供更改单的创建、执行、取消、回滚等业务功能
    """
    
    def __init__(self, user: User = None):
        """
        初始化服务类
        
        Args:
            user: 当前操作用户
        """
        self.user = user
    
    def create_change_order(self, change_reason: str, notes: str = None) -> ChangeOrder:
        """
        创建一个更改单（草稿状态）
        
        Args:
            change_reason: 变更原因
            notes: 备注信息
            
        Returns:
            ChangeOrder: 创建的更改单对象
        """
        if not self.user:
            raise ValueError("当前用户未设置")
            
        if not change_reason:
            raise ValueError("变更原因不能为空")
        
        change_order = ChangeOrder.objects.create(
            created_by=self.user,
            status='draft',
            change_reason=change_reason,
            notes=notes
        )
        
        logger.info(f"用户 {self.user.username} 创建了更改单 {change_order.order_number}")
        return change_order
    
    def add_record_to_change_order(
        self, 
        change_order: ChangeOrder, 
        archive_record: ArchiveRecord, 
        changes: Dict[str, Any]
    ) -> ChangeOrderItem:
        """
        向更改单添加一个档案记录变更项
        
        Args:
            change_order: 更改单对象
            archive_record: 档案记录对象
            changes: 变更字段字典 {字段名: 新值}
            
        Returns:
            ChangeOrderItem: 创建的更改单条目
        """
        if change_order.status != 'draft':
            raise ValueError(f"只能向草稿状态的更改单添加条目，当前状态: {change_order.get_status_display()}")
            
        # 检查档案是否已归档
        if archive_record.archive_status != '已归档':
            raise ValueError(f"只能更改已归档的档案记录，当前状态: {archive_record.archive_status}")
        
        # 检查是否已存在同一档案记录的条目
        if ChangeOrderItem.objects.filter(
            change_order=change_order, 
            archive_record=archive_record
        ).exists():
            raise ValueError(f"更改单中已存在档案记录 {archive_record.unified_number} 的变更条目")
        
        # 获取当前档案记录状态作为变更前状态
        record_before = {}
        for field in archive_record._meta.fields:
            if not field.primary_key and field.name != 'id':
                value = getattr(archive_record, field.name)
                record_before[field.name] = str(value) if value is not None else None
        
        # 验证变更字段
        valid_changes = {}
        for field_name, new_value in changes.items():
            # 检查字段是否存在
            try:
                field = archive_record._meta.get_field(field_name)
            except:
                logger.warning(f"档案记录模型中不存在字段 {field_name}，已忽略")
                continue
            
            # 检查字段是否可修改 (可以根据业务需要增加限制)
            # if field_name in ['unified_number', 'created_at']:  # 示例：某些字段不允许修改
            #     logger.warning(f"字段 {field_name} 不允许修改，已忽略")
            #     continue
            
            # 如果新值与旧值相同，则忽略
            old_value = getattr(archive_record, field_name)
            old_value_str = str(old_value) if old_value is not None else None
            new_value_str = str(new_value) if new_value is not None else None
            
            if old_value_str == new_value_str:
                continue
                
            # 添加到有效变更中
            valid_changes[field_name] = new_value
        
        # 如果没有有效变更，则抛出异常
        if not valid_changes:
            raise ValueError(f"没有有效的字段变更，无法添加条目")
        
        # 创建更改单条目
        item = ChangeOrderItem.objects.create(
            change_order=change_order,
            archive_record=archive_record,
            record_before=record_before,
            planned_changes=valid_changes,
            changes_count=len(valid_changes)
        )
        
        # 更新更改单的记录数和变更字段总数
        self._update_change_order_stats(change_order)
        
        logger.info(f"向更改单 {change_order.order_number} 添加了档案记录 {archive_record.unified_number} 的变更条目")
        return item
    
    def remove_record_from_change_order(
        self, 
        change_order: ChangeOrder, 
        archive_record: ArchiveRecord
    ) -> bool:
        """
        从更改单中移除档案记录变更项
        
        Args:
            change_order: 更改单对象
            archive_record: 档案记录对象
            
        Returns:
            bool: 是否成功移除
        """
        if change_order.status != 'draft':
            raise ValueError(f"只能从草稿状态的更改单移除条目，当前状态: {change_order.get_status_display()}")
        
        # 查找并删除条目
        item = ChangeOrderItem.objects.filter(
            change_order=change_order, 
            archive_record=archive_record
        ).first()
        
        if not item:
            logger.warning(f"更改单 {change_order.order_number} 中不存在档案记录 {archive_record.unified_number} 的变更条目")
            return False
        
        item.delete()
        
        # 更新更改单的记录数和变更字段总数
        self._update_change_order_stats(change_order)
        
        logger.info(f"从更改单 {change_order.order_number} 移除了档案记录 {archive_record.unified_number} 的变更条目")
        return True
    
    def update_record_changes(
        self, 
        change_order: ChangeOrder, 
        archive_record: ArchiveRecord, 
        changes: Dict[str, Any]
    ) -> ChangeOrderItem:
        """
        更新更改单中档案记录的变更内容
        
        Args:
            change_order: 更改单对象
            archive_record: 档案记录对象
            changes: 新的变更字段字典 {字段名: 新值}
            
        Returns:
            ChangeOrderItem: 更新后的更改单条目
        """
        if change_order.status != 'draft':
            raise ValueError(f"只能更新草稿状态的更改单，当前状态: {change_order.get_status_display()}")
        
        # 查找条目
        item = ChangeOrderItem.objects.filter(
            change_order=change_order, 
            archive_record=archive_record
        ).first()
        
        if not item:
            raise ValueError(f"更改单 {change_order.order_number} 中不存在档案记录 {archive_record.unified_number} 的变更条目")
        
        # 验证变更字段
        valid_changes = {}
        for field_name, new_value in changes.items():
            # 检查字段是否存在
            try:
                field = archive_record._meta.get_field(field_name)
            except:
                logger.warning(f"档案记录模型中不存在字段 {field_name}，已忽略")
                continue
            
            # 添加到有效变更中
            valid_changes[field_name] = new_value
        
        # 更新条目
        item.planned_changes = valid_changes
        item.changes_count = len(valid_changes)
        item.save()
        
        # 更新更改单的记录数和变更字段总数
        self._update_change_order_stats(change_order)
        
        logger.info(f"更新了更改单 {change_order.order_number} 中档案记录 {archive_record.unified_number} 的变更内容")
        return item
    
    def submit_change_order(self, change_order: ChangeOrder) -> ChangeOrder:
        """
        提交更改单，将状态从草稿改为待处理
        
        Args:
            change_order: 更改单对象
            
        Returns:
            ChangeOrder: 更新后的更改单对象
        """
        if change_order.status != 'draft':
            raise ValueError(f"只能提交草稿状态的更改单，当前状态: {change_order.get_status_display()}")
        
        # 检查是否有变更条目
        if not ChangeOrderItem.objects.filter(change_order=change_order).exists():
            raise ValueError(f"更改单 {change_order.order_number} 没有变更条目，不能提交")
        
        # 更新状态
        change_order.status = 'pending'
        change_order.save()
        
        logger.info(f"用户 {self.user.username} 提交了更改单 {change_order.order_number}")
        return change_order
    
    def execute_change_order(self, change_order: ChangeOrder) -> Dict[str, Any]:
        """
        执行更改单，实际应用变更到档案记录
        
        Args:
            change_order: 更改单对象
            
        Returns:
            Dict: 执行结果
        """
        if not self.user:
            raise ValueError("当前用户未设置")
            
        if change_order.status != 'pending':
            raise ValueError(f"只能执行待处理状态的更改单，当前状态: {change_order.get_status_display()}")
        
        # 更新状态为处理中
        change_order.status = 'processing'
        change_order.save()
        
        # 执行前检查所有条目是否合法
        items = ChangeOrderItem.objects.filter(change_order=change_order)
        if not items.exists():
            change_order.status = 'pending'  # 恢复状态
            change_order.save()
            return {'success': False, 'error': f"更改单 {change_order.order_number} 没有变更条目"}
        
        # 创建变更批次
        results = {'success': True, 'stats': {'total': 0, 'success': 0, 'failed': 0}, 'errors': []}
        
        try:
            with transaction.atomic():
                # 创建变更批次
                batch = ChangeLogBatch.objects.create(
                    change_source='change_order',
                    change_reason=change_order.change_reason,
                    changed_by=self.user,
                    reference_id=change_order.order_number,
                    notes=change_order.notes
                )
                
                # 关联更改单和批次
                change_order.change_batch = batch
                change_order.save()
                
                # 执行每个条目的变更
                results['stats']['total'] = items.count()
                
                for item in items:
                    try:
                        # 获取档案记录
                        record = item.archive_record
                        
                        # 获取当前状态作为变更前状态（可能与创建条目时的状态有变化）
                        record_before = {}
                        for field in record._meta.fields:
                            if not field.primary_key and field.name != 'id':
                                value = getattr(record, field.name)
                                record_before[field.name] = str(value) if value is not None else None
                        
                        # 应用变更
                        for field_name, new_value in item.planned_changes.items():
                            try:
                                setattr(record, field_name, new_value)
                            except Exception as e:
                                raise ValueError(f"设置字段 {field_name} 值失败: {str(e)}")
                        
                        # 保存记录
                        record.save()
                        
                        # 获取变更后状态
                        record_after = {}
                        for field in record._meta.fields:
                            if not field.primary_key and field.name != 'id':
                                value = getattr(record, field.name)
                                record_after[field.name] = str(value) if value is not None else None
                        
                        # 获取下一个版本号
                        version_number = RecordChangeLog.objects.filter(record=record).count() + 1
                        
                        # 创建记录变更日志
                        record_change = RecordChangeLog.objects.create(
                            batch=batch,
                            record=record,
                            version_number=version_number,
                            change_type='update',
                            record_before=record_before,
                            record_after=record_after,
                            changed_fields_count=len(item.planned_changes),
                            changed_by=self.user,
                            notes=f"更改单 {change_order.order_number}"
                        )
                        
                        # 创建字段变更日志
                        field_logs = []
                        for field_name, new_value in item.planned_changes.items():
                            old_value = record_before.get(field_name)
                            
                            # 获取字段标签
                            field_label = field_name
                            try:
                                field = record._meta.get_field(field_name)
                                field_label = field.verbose_name
                            except:
                                pass
                                
                            # 添加字段变更日志
                            field_logs.append(FieldChangeLog(
                                record_change=record_change,
                                field_name=field_name,
                                field_label=field_label,
                                old_value=old_value,
                                new_value=new_value,
                                change_action='modified',
                                field_importance=self._get_field_importance(field_name)
                            ))
                        
                        # 批量创建字段变更日志
                        if field_logs:
                            FieldChangeLog.objects.bulk_create(field_logs)
                        
                        # 更新条目状态
                        item.is_executed = True
                        item.execution_time = timezone.now()
                        item.execution_result = 'success'
                        item.record_change_log = record_change
                        item.save()
                        
                        results['stats']['success'] += 1
                    
                    except Exception as e:
                        # 单个条目失败，记录错误但继续处理其他条目
                        item.is_executed = True
                        item.execution_time = timezone.now()
                        item.execution_result = 'failed'
                        item.execution_message = str(e)
                        item.save()
                        
                        results['stats']['failed'] += 1
                        results['errors'].append({
                            'record_id': item.archive_record.id,
                            'unified_number': item.archive_record.unified_number,
                            'error': str(e)
                        })
                
                # 更新批次摘要
                batch.total_records = results['stats']['total']
                batch.successful_records = results['stats']['success']
                batch.failed_records = results['stats']['failed']
                batch.summary = json.dumps(results)
                batch.save()
                
                # 如果全部失败，回滚整个更改单
                if results['stats']['success'] == 0:
                    results['success'] = False
                    raise ValueError("所有变更条目都执行失败")
                
                # 更新更改单状态
                change_order.status = 'completed'
                change_order.execution_time = timezone.now()
                change_order.executed_by = self.user
                change_order.save()
        
        except Exception as e:
            # 事务回滚，更新更改单状态
            change_order.status = 'pending'  # 恢复状态
            change_order.save()
            
            results['success'] = False
            results['error'] = str(e)
            
            logger.error(f"执行更改单 {change_order.order_number} 失败: {str(e)}")
        
        return results
    
    def cancel_change_order(self, change_order: ChangeOrder) -> ChangeOrder:
        """
        取消更改单
        
        Args:
            change_order: 更改单对象
            
        Returns:
            ChangeOrder: 更新后的更改单对象
        """
        if change_order.status not in ['draft', 'pending']:
            raise ValueError(f"只能取消草稿或待处理状态的更改单，当前状态: {change_order.get_status_display()}")
        
        # 更新状态
        change_order.status = 'cancelled'
        change_order.save()
        
        logger.info(f"用户 {self.user.username} 取消了更改单 {change_order.order_number}")
        return change_order
    
    def delete_change_order(self, change_order: ChangeOrder) -> bool:
        """
        删除更改单（软删除）
        
        Args:
            change_order: 更改单对象
            
        Returns:
            bool: 是否成功删除
        """
        if change_order.status not in ['draft', 'cancelled']:
            raise ValueError(f"只能删除草稿或已取消状态的更改单，当前状态: {change_order.get_status_display()}")
        
        # 更新状态
        change_order.delete()  # 软删除方法
        
        logger.info(f"用户 {self.user.username} 删除了更改单 {change_order.order_number}")
        return True
    
    def get_change_order_details(self, change_order: ChangeOrder) -> Dict[str, Any]:
        """
        获取更改单详情，包括条目和变更字段
        
        Args:
            change_order: 更改单对象
            
        Returns:
            Dict: 更改单详情
        """
        items = ChangeOrderItem.objects.filter(change_order=change_order)
        
        # 转换为可序列化的字典
        details = {
            'order_number': change_order.order_number,
            'status': change_order.status,
            'status_display': change_order.get_status_display(),
            'change_reason': change_order.change_reason,
            'notes': change_order.notes,
            'created_by': change_order.created_by.username,
            'created_at': change_order.created_at.isoformat(),
            'updated_at': change_order.updated_at.isoformat(),
            'records_count': change_order.records_count,
            'changes_count': change_order.changes_count,
            'execution_time': change_order.execution_time.isoformat() if change_order.execution_time else None,
            'executed_by': change_order.executed_by.username if change_order.executed_by else None,
            'attachment': change_order.attachment.url if change_order.attachment else None,
            'attachment_name': change_order.attachment_name,
            'items': []
        }
        
        # 添加条目详情
        for item in items:
            item_details = {
                'id': item.id,
                'unified_number': item.archive_record.unified_number,
                'commission_number': item.archive_record.commission_number if hasattr(item.archive_record, 'commission_number') else None,
                'project_name': item.archive_record.project_name if hasattr(item.archive_record, 'project_name') else None,
                'changes_count': item.changes_count,
                'is_executed': item.is_executed,
                'execution_time': item.execution_time.isoformat() if item.execution_time else None,
                'execution_result': item.execution_result,
                'field_changes': []
            }
            
            # 添加字段变更详情
            for field_change in item.get_field_changes():
                item_details['field_changes'].append({
                    'field_name': field_change['field_name'],
                    'field_label': field_change['field_label'],
                    'old_value': field_change['old_value'],
                    'new_value': field_change['new_value']
                })
            
            details['items'].append(item_details)
        
        return details
    
    def rollback_change_order(self, change_order: ChangeOrder) -> Dict[str, Any]:
        """
        回滚更改单，创建一个新的更改单恢复到变更前的状态
        
        Args:
            change_order: 需要回滚的更改单对象
            
        Returns:
            Dict: 回滚结果
        """
        if not self.user:
            raise ValueError("当前用户未设置")
            
        if change_order.status != 'completed':
            raise ValueError(f"只能回滚已完成状态的更改单，当前状态: {change_order.get_status_display()}")
            
        if change_order.is_rollback_source:
            raise ValueError(f"更改单 {change_order.order_number} 已被回滚，不能重复回滚")
        
        # 创建回滚更改单
        rollback_order = ChangeOrder.objects.create(
            created_by=self.user,
            status='draft',
            change_reason=f"回滚更改单 {change_order.order_number}",
            notes=f"回滚对应更改单：{change_order.order_number}\n原因：{change_order.change_reason}"
        )
        
        # 关联原更改单
        rollback_order.rollback_order = change_order
        rollback_order.save()
        
        # 标记原更改单为已回滚
        change_order.is_rollback_source = True
        change_order.save()
        
        # 添加回滚条目
        items = ChangeOrderItem.objects.filter(change_order=change_order, is_executed=True, execution_result='success')
        for item in items:
            try:
                # 创建回滚变更（原来的变更前状态作为新的变更值）
                rollback_changes = {}
                for field_name, old_value in item.record_before.items():
                    current_value = getattr(item.archive_record, field_name, None)
                    current_value_str = str(current_value) if current_value is not None else None
                    
                    # 只回滚与当前值不同的字段
                    if old_value != current_value_str:
                        rollback_changes[field_name] = old_value if old_value != 'None' else None
                
                # 如果有需要回滚的字段，创建回滚条目
                if rollback_changes:
                    rollback_item = ChangeOrderItem.objects.create(
                        change_order=rollback_order,
                        archive_record=item.archive_record,
                        record_before=item.record_after,  # 原来的变更后状态作为新的变更前状态
                        planned_changes=rollback_changes,
                        changes_count=len(rollback_changes)
                    )
            except Exception as e:
                logger.error(f"为更改单 {change_order.order_number} 的条目 {item.id} 创建回滚条目失败: {str(e)}")
        
        # 更新回滚更改单的统计信息
        self._update_change_order_stats(rollback_order)
        
        logger.info(f"用户 {self.user.username} 创建了回滚更改单 {rollback_order.order_number} 用于回滚更改单 {change_order.order_number}")
        
        # 如果没有回滚条目，删除回滚更改单
        if rollback_order.records_count == 0:
            rollback_order.hard_delete()
            return {
                'success': False, 
                'error': f"更改单 {change_order.order_number} 没有可以回滚的条目"
            }
        
        return {
            'success': True,
            'rollback_order': rollback_order.order_number,
            'records_count': rollback_order.records_count,
            'changes_count': rollback_order.changes_count
        }
    
    def _update_change_order_stats(self, change_order: ChangeOrder) -> None:
        """
        更新更改单的统计信息（记录数和变更字段总数）
        
        Args:
            change_order: 更改单对象
        """
        items = ChangeOrderItem.objects.filter(change_order=change_order)
        
        # 更新统计信息
        change_order.records_count = items.count()
        change_order.changes_count = items.aggregate(total=models.Sum('changes_count'))['total'] or 0
        change_order.save()
    
    def _get_field_importance(self, field_name: str) -> str:
        """
        获取字段重要性
        
        Args:
            field_name: 字段名
            
        Returns:
            str: 字段重要性级别
        """
        # 确定关键、重要和普通字段
        critical_fields = ['commission_number', 'unified_number', 'report_number']
        important_fields = ['project_name', 'client_unit', 'archive_url']
        
        if field_name in critical_fields:
            return 'critical'
        elif field_name in important_fields:
            return 'important'
        else:
            return 'normal' 