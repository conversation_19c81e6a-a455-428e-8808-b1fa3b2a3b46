# CHANGE: [2024-03-29] 在 utils 包的 __init__ 中导入核心功能，方便外部调用 #AFM-28
# CHANGE: [2024-03-29] 添加 OCR 相关导出 #AFM-29
# CHANGE: [2024-03-29] 修改 DTO 导入为绝对路径 #AFM-29
# from .pdf_processor import PDFProcessor, create_result_summary # PDFProcessor 将移至 Service
from archive_processing.dto import * # 导出所有 DTOs，使用绝对路径
# from .image_processing import ...
# from .text_processing import ...
# from .pdf_utils import ...

from archive_processing.utils import *

# 定义 __all__ 来明确导出的公开接口 (可选但推荐)
__all__ = [
    # DTOs (如果使用 from .dto import *，则不需要列出)
    # 'PDFPageImage', 'PDFPartRange', ...
    
    # OCR Functions
    'initialize_ocr_engine',
    'perform_ocr',
    'calculate_image_hash',
    
    # File Operations
    'ensure_directory_exists',
    'save_file_chunk',
    'generate_archive_filename',
    'get_archive_storage_path',
    'secure_filename',
    
    # Other Utils (待添加)
    # 'preprocess_image', 
    # 'clean_text', 
    # 'find_unified_number', 
    # 'render_pdf_page', 
    # 'split_and_save_pdf',
    # 'create_result_summary' # 如果它是一个通用工具
]
