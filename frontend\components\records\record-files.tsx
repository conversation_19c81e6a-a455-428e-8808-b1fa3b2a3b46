"use client"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Eye, FileText } from "lucide-react"
import { useEffect, useState } from "react"

interface RecordFilesProps {
  recordId: string
}

export function RecordFiles({ recordId }: RecordFilesProps) {
  const [files, setFiles] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 实际应用中，这里会从API获取文件数据
    // const fetchFiles = async () => {
    //   const response = await fetch(`/api/records/${recordId}/files`);
    //   const data = await response.json();
    //   setFiles(data);
    //   setIsLoading(false);
    // };
    // fetchFiles();

    // 模拟数据加载
    setTimeout(() => {
      setFiles([
        {
          id: 1,
          fileName: "XYZ-2023-001-环评报告.pdf",
          fileSize: "2.5 MB",
          uploadDate: "2023-05-15",
          uploadedBy: "王五",
          fileType: "application/pdf",
          url: "#",
        },
      ])
      setIsLoading(false)
    }, 500)
  }, [recordId])

  if (isLoading) {
    return <div className="flex justify-center items-center h-40">加载中...</div>
  }

  if (files.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-40 p-6">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">该记录尚未关联归档文件</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          {files.map((file) => (
            <div
              key={file.id}
              className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4 border rounded-md"
            >
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-primary" />
                <div>
                  <p className="font-medium">{file.fileName}</p>
                  <div className="flex flex-wrap gap-x-4 text-sm text-muted-foreground">
                    <span>大小: {file.fileSize}</span>
                    <span>上传日期: {file.uploadDate}</span>
                    <span>上传者: {file.uploadedBy}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 mt-2 sm:mt-0">
                <Button variant="outline" size="sm" asChild>
                  <a href={file.url} target="_blank" rel="noopener noreferrer">
                    <Eye className="mr-2 h-4 w-4" />
                    预览
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href={file.url} download>
                    <Download className="mr-2 h-4 w-4" />
                    下载
                  </a>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
