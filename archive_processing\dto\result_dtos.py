"""
结果和识别相关的数据传输对象 (DTOs)
"""
import dataclasses
from typing import List, Dict, Tuple, Optional

@dataclasses.dataclass
class UnifiedNumberResult:
    """统一编号识别结果"""
    number: Optional[str]
    method: str  # 识别方法标识
    
    def __str__(self):
        if self.number:
            return f"{self.number} (通过{self.method}识别)"
        return f"未识别 ({self.method})"

@dataclasses.dataclass
class ProcessResult:
    """存储 split_pdf 函数的处理结果"""
    success: bool
    # 存储 (文件路径, 统一编号) 的元组列表
    output_mapping: List[Tuple[str, Optional[str]]] = dataclasses.field(default_factory=list)
    stats: Dict = dataclasses.field(default_factory=dict)
    error_message: Optional[str] = None
    original_pdf_path: Optional[str] = None  # 添加原始文件路径以便追踪 