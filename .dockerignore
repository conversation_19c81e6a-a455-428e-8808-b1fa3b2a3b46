# Ignore frontend build artifacts and dependencies for backend builds
frontend/node_modules/
frontend/.next/

# Python specific
__pycache__/
*.py[cod]
*$py.class

# Environment files
.env
.env.*
!.env.example

# IDE / Editor specific
.vscode/
.idea/
*.swp

# OS specific
.DS_Store
Thumbs.db

# Django specific
db.sqlite3
media/
static_collected/

# Docker
Dockerfile.bak
docker-compose.yml.bak

# Logs
*.log
logs/

# Other
.mypy_cache/
.pytest_cache/
.tox/
*.egg-info/
dist/
build/
*.tar.gz
*.zip 