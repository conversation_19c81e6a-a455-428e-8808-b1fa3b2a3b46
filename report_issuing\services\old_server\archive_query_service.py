# CHANGE: [2025-06-08] 创建档案查询数据服务层 - 可发放档案查询功能
from typing import Dict, List, Optional, Tuple
from django.db.models import Q, F, Count, Sum, Case, When, IntegerField, Value
from django.db.models.query import QuerySet
from django.core.paginator import Paginator
from archive_records.models import ArchiveRecord
from report_issuing.models import IssueRecord
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ArchiveQueryService:
    """
    档案查询数据服务层
    
    专门负责档案相关的数据查询操作，为业务层提供数据支持。
    专注CRUD操作，不包含业务逻辑。
    """
    
    def get_issuable_archives_queryset(self, filters: Optional[Dict] = None) -> QuerySet:
        """
        获取可发放档案的查询集
        
        Args:
            filters: 查询过滤条件 {
                'unified_number': str,           # 统一编号
                'sample_number': str,            # 样品编号  
                'client_unit': str,              # 委托单位
                'client_name': str,              # 委托人
                'commission_datetime_start': datetime,  # 委托时间开始
                'commission_datetime_end': datetime,    # 委托时间结束
                'project_number': str,           # 工程编号
                'project_name': str,             # 工程名称
                'project_location': str,         # 工程部位
            }
        
        Returns:
            QuerySet: 可发放档案的查询集，包含发放状态注解
        """
        # 基础查询集 - 获取所有档案记录
        queryset = ArchiveRecord.objects.select_related('import_user').prefetch_related('issue_records')
        
        # 添加发放状态计算注解
        queryset = self._add_issue_status_annotations(queryset)
        
        # 过滤出可发放的档案（总份数 > 已发放份数）
        queryset = queryset.filter(
            Q(remaining_copies__gt=0) |  # 剩余份数大于0
            Q(total_issue_copies__isnull=True) |  # 或者总份数为空（默认可发放）
            Q(total_issue_copies=0)  # 或者总份数为0（使用默认最少3份规则）
        )
        
        # 应用业务过滤条件
        if filters:
            queryset = self._apply_filters(queryset, filters)
        
        return queryset
    
    def _add_issue_status_annotations(self, queryset: QuerySet) -> QuerySet:
        """
        为查询集添加发放状态相关注解
        
        计算每个档案的发放状态，包括：
        - 第一次发放份数
        - 第二次发放份数
        - 总已发放份数
        - 剩余可发放份数
        """
        return queryset.annotate(
            # 计算第一次发放份数（从IssueRecord表聚合）
            first_issue_copies_from_records=Sum(
                Case(
                    When(
                        issue_records__issue_type='first',
                        issue_records__is_active=True,
                        issue_records__is_deleted=False,
                        then='issue_records__copies'
                    ),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ),
            # 计算第二次发放份数（从IssueRecord表聚合）
            second_issue_copies_from_records=Sum(
                Case(
                    When(
                        issue_records__issue_type='second',
                        issue_records__is_active=True,
                        issue_records__is_deleted=False,
                        then='issue_records__copies'
                    ),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ),
            # 计算总已发放份数
            total_issued_copies=F('first_issue_copies_from_records') + F('second_issue_copies_from_records'),
            # 计算有效总份数（最少3份）
            effective_total_copies=Case(
                When(total_issue_copies__isnull=True, then=Value(3)),
                When(total_issue_copies=0, then=Value(3)),
                When(total_issue_copies__lt=3, then=Value(3)),
                default=F('total_issue_copies'),
                output_field=IntegerField()
            ),
            # 计算剩余可发放份数
            remaining_copies=F('effective_total_copies') - F('total_issued_copies')
        )
    
    def _apply_filters(self, queryset: QuerySet, filters: Dict) -> QuerySet:
        """
        应用查询过滤条件
        
        Args:
            queryset: 基础查询集
            filters: 过滤条件字典
            
        Returns:
            QuerySet: 应用过滤条件后的查询集
        """
        # 统一编号过滤
        if unified_number := filters.get('unified_number'):
            queryset = queryset.filter(unified_number__icontains=unified_number)
        
        # 样品编号过滤
        if sample_number := filters.get('sample_number'):
            queryset = queryset.filter(sample_number__icontains=sample_number)
        
        # 委托单位过滤
        if client_unit := filters.get('client_unit'):
            queryset = queryset.filter(client_unit__icontains=client_unit)
        
        # 委托人过滤
        if client_name := filters.get('client_name'):
            queryset = queryset.filter(client_name__icontains=client_name)
        
        # 委托时间区间过滤
        if commission_start := filters.get('commission_datetime_start'):
            queryset = queryset.filter(commission_datetime__gte=commission_start)
        
        if commission_end := filters.get('commission_datetime_end'):
            queryset = queryset.filter(commission_datetime__lte=commission_end)
        
        # 工程编号过滤
        if project_number := filters.get('project_number'):
            queryset = queryset.filter(project_number__icontains=project_number)
        
        # 工程名称过滤
        if project_name := filters.get('project_name'):
            queryset = queryset.filter(project_name__icontains=project_name)
        
        # 工程部位过滤
        if project_location := filters.get('project_location'):
            queryset = queryset.filter(project_location__icontains=project_location)
        
        return queryset
    
    def get_paginated_archives(
        self, 
        queryset: QuerySet, 
        page: int = 1, 
        page_size: int = 20,
        order_by: Optional[List[str]] = None
    ) -> Tuple[List[ArchiveRecord], Dict]:
        """
        获取分页的档案数据
        
        Args:
            queryset: 档案查询集
            page: 页码（从1开始）
            page_size: 每页记录数
            order_by: 排序字段列表
            
        Returns:
            Tuple[List[ArchiveRecord], Dict]: (档案列表, 分页信息)
        """
        # 应用排序
        if order_by:
            # 验证排序字段安全性
            allowed_fields = [
                'id', 'unified_number', 'sample_number', 'client_unit', 
                'client_name', 'commission_datetime', 'project_number',
                'project_name', 'project_location', 'created_at',
                'remaining_copies', 'effective_total_copies', 'total_issued_copies'
            ]
            safe_order_by = []
            for field in order_by:
                # 处理降序标记
                if field.startswith('-'):
                    field_name = field[1:]
                    prefix = '-'
                else:
                    field_name = field
                    prefix = ''
                
                if field_name in allowed_fields:
                    safe_order_by.append(prefix + field_name)
            
            if safe_order_by:
                queryset = queryset.order_by(*safe_order_by)
        else:
            # 默认按创建时间倒序
            queryset = queryset.order_by('-created_at')
        
        # 分页处理
        paginator = Paginator(queryset, page_size)
        
        # 验证页码范围
        if page < 1:
            page = 1
        elif page > paginator.num_pages and paginator.num_pages > 0:
            page = paginator.num_pages
        
        page_obj = paginator.get_page(page)
        
        # 分页信息
        pagination_info = {
            'total_count': paginator.count,
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
        }
        
        return list(page_obj.object_list), pagination_info
    
    def get_archive_by_id(self, archive_id: int) -> Optional[ArchiveRecord]:
        """
        根据ID获取档案记录
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            Optional[ArchiveRecord]: 档案记录或None
        """
        try:
            return ArchiveRecord.objects.select_related('import_user').get(id=archive_id)
        except ArchiveRecord.DoesNotExist:
            return None
    
    def get_archive_issue_status(self, archive_id: int) -> Dict:
        """
        获取单个档案的发放状态详情
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            Dict: 发放状态详情 {
                'archive_id': int,
                'total_copies': int,              # 总可发放份数
                'first_issued_copies': int,       # 第一次已发放份数
                'second_issued_copies': int,      # 第二次已发放份数
                'total_issued_copies': int,       # 总已发放份数
                'remaining_copies': int,          # 剩余可发放份数
                'can_issue': bool,               # 是否可以发放
                'first_issue_records': List,     # 第一次发放记录
                'second_issue_records': List,    # 第二次发放记录
            }
        """
        try:
            archive = ArchiveRecord.objects.get(id=archive_id)
            
            # 获取发放记录
            first_records = list(archive.issue_records.filter(
                issue_type='first', is_active=True, is_deleted=False
            ).values('id', 'issue_date', 'copies', 'receiver_name', 'receiver_unit'))
            
            second_records = list(archive.issue_records.filter(
                issue_type='second', is_active=True, is_deleted=False
            ).values('id', 'issue_date', 'copies', 'receiver_name', 'receiver_unit'))
            
            # 计算发放数量
            first_issued = sum(record['copies'] for record in first_records)
            second_issued = sum(record['copies'] for record in second_records)
            total_issued = first_issued + second_issued
            
            # 确定有效总份数（最少3份）
            effective_total = max(3, archive.total_issue_copies or 3)
            remaining = max(0, effective_total - total_issued)
            
            return {
                'archive_id': archive_id,
                'total_copies': effective_total,
                'first_issued_copies': first_issued,
                'second_issued_copies': second_issued,
                'total_issued_copies': total_issued,
                'remaining_copies': remaining,
                'can_issue': remaining > 0,
                'first_issue_records': first_records,
                'second_issue_records': second_records,
            }
            
        except ArchiveRecord.DoesNotExist:
            return {
                'archive_id': archive_id,
                'error': '档案记录不存在'
            }

    def get_archive_issue_history(self, archive_id: int) -> Dict:
        """
        获取单个档案的完整发放历史和状态
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            Dict: {
                'success': bool,
                'archive_exists': bool,
                'archive': ArchiveRecord or None,
                'effective_total_copies': int,
                'first_issue_records': List[IssueRecord],
                'second_issue_records': List[IssueRecord],
                'error': str or None
            }
        """
        try:
            archive = self.get_archive_by_id(archive_id)
            if not archive:
                return {
                    'success': False,
                    'archive_exists': False,
                    'archive': None,
                    'effective_total_copies': 0,
                    'first_issue_records': [],
                    'second_issue_records': [],
                    'error': '档案记录不存在'
                }
            
            effective_total_copies = max(3, archive.total_issue_copies or 3)
    
            first_records = list(IssueRecord.objects.filter(
                archive_record_id=archive_id,
                issue_type='first',
                is_active=True,
                is_deleted=False
            ))
            
            second_records = list(IssueRecord.objects.filter(
                archive_record_id=archive_id,
                issue_type='second',
                is_active=True,
                is_deleted=False
            ))
            
            return {
                'success': True,
                'archive_exists': True,
                'archive': archive,
                'effective_total_copies': effective_total_copies,
                'first_issue_records': first_records,
                'second_issue_records': second_records,
                'error': None
            }
        except Exception as e:
            logger.error(f"获取档案发放历史失败: archive_id={archive_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'archive_exists': False,
                'archive': None,
                'effective_total_copies': 0,
                'first_issue_records': [],
                'second_issue_records': [],
                'error': f'获取历史记录时发生未知错误: {str(e)}'
            } 