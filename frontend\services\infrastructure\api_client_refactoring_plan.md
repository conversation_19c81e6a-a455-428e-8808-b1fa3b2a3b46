# API Client Refactoring Design Document

- **Status:** Proposal
- **Date:** 2025-06-29
- **Author:** AI Assistant

## 1. Background & Problem Statement

Our current API client, located at `lib/apiClient.ts`, is a robust, centralized client for handling all frontend HTTP requests. It successfully standardizes error handling, request retries, and authentication token injection.

However, a subtle design choice has led to a recurring issue with type safety. The client's core `request` method inspects successful responses (`2xx`). If the response body conforms to our standard `{ success: true, data: T }` wrapper (`ApiResponse`), the client "unwraps" it, returning only the `data` property to the caller.

This creates a discrepancy:

- **Compile-Time Type**: TypeScript, looking at the generic method signature, often infers the return type as the full `ApiResponse<T>`.
- **Runtime Value**: The actual value returned is just `T` (the unwrapped data).

This mismatch forces developers to use type assertions (`as unknown as ...`) to reconcile the difference, which undermines type safety and can be a source of confusion and potential bugs. The recent fix in `uploadPdfService` is a prime example of this necessary workaround.

## 2. Goals & Principles

The primary goals of this refactoring are:

1. **Full Type Safety**: Eliminate the root cause of the type mismatch. The return type signature of any `apiClient` method must precisely match the value it returns at runtime. The need for type assertions in calling code should be completely removed.
2. **Predictable & Consistent API**: The client's contract must be simple and unambiguous. Developers should not have to guess whether a response will be wrapped or unwrapped.
3. **Explicit Error & Success Handling**: Callers should be explicitly required to handle both success and failure states, promoting more robust code.
4. **Maintainability**: The new design should be easy to understand, use, and maintain for all developers.

## 3. Proposed Solution: The "Always Wrapped" Approach

I propose we refactor `apiClient` to adopt a strict, non-negotiable contract: **All data-fetching methods (`get`, `post`, `upload`, etc.) will always return the complete `ApiResponse<T>` object.**

The client will no longer perform any "unwrapping" of the `data` property. Its sole responsibility is to execute the HTTP request and normalize the result (either a successful response or a network/server error) into a single, consistent `ApiResponse<T>` format.

The responsibility of inspecting the `success` flag and handling the `data` or `error` is explicitly shifted to the immediate caller (typically a service or a custom hook).

### 3.1. Code Implementation Example

**Before (Current `apiClient.ts` logic):**

```typescript
// In apiClient.request method
// ...
if (isStandardResponse(data)) {
  // This "unwrapping" is the source of the problem.
  return {
    success: data.success,
    data: data.data, // Returns the inner data
    // ...
  };
}
// ...
```

**After (Proposed `apiClient.ts` logic):**

```typescript
// In apiClient.request method
// ...
// The response from the server IS the ApiResponse. We just return it.
// No unwrapping, no magic. What we get is what the caller gets.
if (status >= 200 && status < 300) {
    return data as ApiResponse<T>;
}
// ...
// The catch block will package any non-2xx response or network error
// into our standard { success: false, error: '...' } object.
```

### 3.2. Usage Example

This change propagates up the call stack, enforcing a clear pattern.

**Service Layer (`uploadPdfService.ts`):**

- **Before:** Required a type assertion workaround.
- **After:** No workaround needed. The service simply passes the `ApiResponse` through. The code becomes cleaner and fully type-safe.

```typescript
// AFTER: in uploadPdfService.ts
export const uploadPdfService = async (
    // ...args
): Promise<ApiResponse<UploadResult['data']>> => {
    const formData = new FormData();
    // ...
    // The call is the same, but now the return type is honest.
    const response = await apiClient.post<UploadResult['data']>(
        "/api/...",
        formData,
        { /* ...config... */ }
    );
    return response; // Simply return the wrapped response.
};
```

**Hook Layer (`usePdfUploader.ts`):**

- **Before:** The logic worked, but it was receiving a response object whose type was corrected by a workaround in the service layer.
- **After:** The logic remains almost identical, but it's now operating on a predictably-typed object straight from the source.

```typescript
// AFTER: in usePdfUploader.ts
const response = await uploadPdfService(...);

if (response.success) { // This check is now the standard pattern everywhere
    // Use the data: response.data
    setFiles(/* ... */);
} else {
    // Use the error: response.error
    setFiles(/* ... */);
}
```

## 4. Migration Plan

This is a significant breaking change that will affect numerous files. A careful, staged migration is crucial.

1. **Step 1: Implement the New `apiClient`**:
    - Create a new file, e.g., `apiClient.v2.ts`, with the refactored logic. This allows for a gradual migration without breaking the entire application at once.
    - Alternatively, perform the change on a dedicated Git branch.

2. **Step 2: Identify All Call Sites**:
    - Perform a global search for all usages of `apiClient.get`, `apiClient.post`, `apiClient.put`, `apiClient.patch`, `apiClient.delete`, and `apiClient.upload`.

3. **Step 3: Gradual Refactoring**:
    - For each call site (starting from services and moving up to hooks/components), update the code to handle the new "always wrapped" `ApiResponse<T>` object.
    - This primarily involves adding the `if (response.success) { ... } else { ... }` pattern and accessing `response.data` in the success block.
    - Update the function signatures and type annotations along the call chain to reflect the new return type.

4. **Step 4: Codemod (Recommended for large projects)**:
    - For a large codebase, writing a one-off automated script (a "codemod" using tools like `jscodeshift`) to handle the most common refactoring patterns can save dozens of hours of manual work. The codemod would find calls and wrap them in the standard `if/else` block.

5. **Step 5: Finalization & Cleanup**:
    - Once all call sites are migrated, the old `apiClient.ts` can be replaced by the new one (or the branch merged).
    - A final search should be done to remove all now-obsolete type assertions (`as unknown as ...`).

6. **Step 6: Testing**:
    - Perform thorough regression testing across the entire application, paying close attention to data fetching, form submissions, and error message displays.

## 5. Risks & Mitigation

- **Risk**: High impact due to the wide-ranging nature of the change. Potential to introduce bugs if a call site is missed or refactored incorrectly.
- **Mitigation**:
  - **TypeScript is our safety net**: The compiler will flag every location where the old return type is expected. This is the biggest advantage of this refactor.
  - **Dedicated Branch**: Perform all work on a separate feature branch.
  - **Automated Tests**: A strong suite of integration and end-to-end tests will be invaluable for catching regressions.
  - **Paired Refactoring**: Have two developers work together to review the changes.

## 6. Rejected Alternatives

1. **Dual-Method Approach (`get` vs. `getWrapped`)**: Introduce two sets of methods—one that returns unwrapped data (and throws on error) and one that returns the wrapped response. This was rejected because it complicates the API surface and leads to inconsistent patterns across the codebase.
2. **Complex Conditional Types**: Attempting to create a single method whose return type changes based on the success of the runtime call. This was rejected as being overly complex, potentially leading to cryptic TypeScript errors, and offering little benefit over the simpler, more explicit "Always Wrapped" approach.
