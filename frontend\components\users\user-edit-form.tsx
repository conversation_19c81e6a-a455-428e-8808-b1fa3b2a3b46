"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Eye, EyeOff, Loader2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import apiClient from "@/lib/apiClient"
import { getUser, updateUser, UserData, UserGroup } from "@/services/userService"

// 角色数据，实际应用中可能也来自API
const roles = [
  { id: "admin", name: "管理员" },
  { id: "archivist", name: "档案管理员" },
  { id: "operator", name: "操作员" },
]

const userEditFormSchema = z.object({
  username: z
    .string()
    .min(2, { message: "用户名至少需要2个字符" })
    .max(150, { message: "用户名不能超过150个字符" }),
  first_name: z.string().max(150, { message: "名字不能超过150个字符" }).optional(),
  last_name: z.string().max(150, { message: "姓氏不能超过150个字符" }).optional(),
  email: z.string().email({ message: "请输入有效的电子邮件地址" }),
  password: z.string().optional(),
  groups: z.array(z.number()).optional(), // 假设角色/组以ID数组形式提交
  is_active: z.boolean().optional().default(true),
  // 假设后端有一个profile模型来存储额外信息
  // profile: z.object({
  //   notes: z.string().optional(),
  // }).optional(),
})

type UserEditFormValues = z.infer<typeof userEditFormSchema>

type UserEditFormProps = {
  userId: string
}

export function UserEditForm({ userId }: UserEditFormProps) {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const form = useForm<UserEditFormValues>({
    resolver: zodResolver(userEditFormSchema) as any, // 修复zod-resolver的兼容性问题
    defaultValues: {
      username: "",
      first_name: "",
      last_name: "",
      email: "",
      password: "",
      groups: [],
      is_active: true,
    },
  })

  useEffect(() => {
    const loadUserData = async () => {
      setIsLoading(true)
      try {
        const response = await getUser(userId)
        if (response.success && response.data) {
          form.reset({
            ...response.data,
            groups: response.data.groups.map((g: UserGroup) => g.id), // 假设API返回完整的group对象
          });
        } else {
          throw new Error(response.error || "加载用户数据失败")
        }
      } catch (error: any) {
        toast({
          title: "加载用户数据失败",
          description: error.message || "无法加载用户数据，请稍后重试",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadUserData()
  }, [userId, form])

  async function onSubmit(data: UserEditFormValues) {
    setIsSubmitting(true)

    try {
      // 过滤掉空的密码字段，这样就不会发送空字符串到后端
      const updateData = { ...data };
      if (!updateData.password) {
        delete updateData.password;
      }
      
      const response = await updateUser(userId, updateData as Partial<UserData>);

      if (response.success) {
        toast({
          title: "用户更新成功",
          description: `用户 ${data.username} 已成功更新`,
        })
        router.push(`/users/detail/${userId}`)
        router.refresh() // 刷新页面数据
      } else {
        throw new Error(response.error || "更新用户失败")
      }
    } catch (error: any) {
      toast({
        title: "更新用户失败",
        description: error.message || "更新用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>用户名</FormLabel>
                <FormControl>
                  <Input placeholder="请输入用户名" {...field} />
                </FormControl>
                <FormDescription>用户登录系统时使用的唯一标识符</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>名</FormLabel>
                <FormControl>
                  <Input placeholder="请输入名字" {...field} />
                </FormControl>
                <FormDescription>用户的名字</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>姓</FormLabel>
                <FormControl>
                  <Input placeholder="请输入姓氏" {...field} />
                </FormControl>
                <FormDescription>用户的姓氏</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>电子邮箱</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="请输入电子邮箱" {...field} />
                </FormControl>
                <FormDescription>用于系统通知和密码重置</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>密码 (留空表示不修改)</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showPassword ? "text" : "password"} placeholder="请输入新密码" {...field} />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword((prev) => !prev)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" aria-hidden="true" />
                      ) : (
                        <Eye className="h-4 w-4" aria-hidden="true" />
                      )}
                      <span className="sr-only">
                        {showPassword ? "隐藏密码" : "显示密码"}
                      </span>
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="groups"
            render={({ field }) => (
              <FormItem>
                <FormLabel>角色/用户组</FormLabel>
                <Select onValueChange={(value) => field.onChange([parseInt(value, 10)])} value={String(field.value?.[0] || '')}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择一个角色" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {roles.map(role => (
                      <SelectItem key={role.id} value={role.id}>{role.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>用户的权限角色</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">账号状态</FormLabel>
                <FormDescription>
                  禁用账号将阻止用户登录
                </FormDescription>
              </div>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="profile.notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备注</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="关于用户的备注信息"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            保存更改
          </Button>
        </div>
      </form>
    </Form>
  )
}
