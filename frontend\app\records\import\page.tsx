"use client"

import { PageLayout } from "@/components/common/page-layout"
import { PageAction } from "@/components/common/page-header"
import ExcelImportWithConflictResolution from "@/components/records/import/excel-import-with-conflict-resolution"
import { FileText } from "lucide-react"
import { ExcelImportSessionGuardProvider } from "@/contexts/domain/records/import/ExcelImportSessionGuard"
import { ActiveSessionNotificationModal } from "@/contexts/domain/records/import/ActiveSessionNotificationModal"

export default function ImportPage() {
  const actions: PageAction[] = [
    {
      label: "查看导入历史",
      icon: <FileText className="h-4 w-4" />,
      href: "/records/import-history",
      variant: "outline"
    }
  ];

  return (
    <PageLayout
      title="Excel导入"
      subtitle="上传Excel文件，系统将分析数据冲突，您可以确认处理方案后完成导入。"
      actions={actions}
    >
      <ExcelImportSessionGuardProvider>
        <ActiveSessionNotificationModal>
          <ExcelImportWithConflictResolution />
        </ActiveSessionNotificationModal>
      </ExcelImportSessionGuardProvider>
    </PageLayout>
  )
}
