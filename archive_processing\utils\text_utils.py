import re
import logging
from typing import Optional, Tuple, List, Dict
from collections import Counter
from archive_processing.dto.result_dtos import UnifiedNumberResult

logger = logging.getLogger(__name__)

def preprocess_text(
    text: str, 
    case_sensitive: bool = False, 
    ignore_punctuation: bool = True
) -> str:
    """
    预处理文本用于匹配
    
    Args:
        text: 原始文本
        case_sensitive: 是否区分大小写
        ignore_punctuation: 是否忽略标点符号
        
    Returns:
        处理后的文本
    """
    processed = text if case_sensitive else text.lower()
    if ignore_punctuation:
        # 移除非单词字符和非空白字符 (保留字母、数字、下划线和空白)
        processed = re.sub(r'[^\w\s]', '', processed)
    return processed

def normalize_unified_number(number: str) -> str:
    """
    专门负责规范化编号格式
    
    Args:
        number: 原始编号字符串
        
    Returns:
        规范化后的编号字符串
    """
    if not number:
        return number
    
    # 1. 统一连字符 (- 和 －)
    normalized = re.sub(r'[－—]', '-', number)
    
    # 2. 修正常见的字符混淆
    char_corrections = {
        'O': '0', 'o': '0',        # 字母O/o → 数字0
        'I': '1', 'l': '1',        # 字母I/l → 数字1
        'Z': '2',                  # 字母Z → 数字2
        'S': '5',                  # 字母S → 数字5
        'B': '8',                  # 字母B → 数字8
    }
    
    # 将number拆分为前缀、年份和序号部分
    parts = re.split(r'-', normalized)
    if len(parts) >= 3:
        # 前缀部分保持字母，通常大写
        prefix = parts[0].upper()
        
        # 年份部分应该是数字
        year_part = parts[1]
        for char, correction in char_corrections.items():
            if re.search(r'^\d{4}$', year_part.replace(char, correction)):
                year_part = year_part.replace(char, correction)
        
        # 序号部分应该是数字
        num_part = parts[2]
        for char, correction in char_corrections.items():
            if re.search(r'^\d+$', num_part.replace(char, correction)):
                num_part = num_part.replace(char, correction)
        
        # 重新组合
        normalized = f"{prefix}-{year_part}-{num_part}"
    
    # 验证格式 (更宽松，允许不同前缀长度)
    if not re.match(r'^[A-Z0-9]{1,6}-\d{4}-\d{5,6}$', normalized):
        logger.warning(f"规范化后的统一编号格式可能不符合预期: {normalized}")
    
    return normalized

def extract_unified_number(ocr_text: str, target_text: str = "代合同") -> Optional[str]:
    """
    从文本中提取可能的统一编号
    
    Args:
        ocr_text: OCR识别出的原始文本
        target_text: 用于定位编号区域的目标文本，例如"代合同"
        
    Returns:
        提取并规范化后的统一编号，如果未找到则返回None
    """
    try:
        # 首先找到目标文本的位置
        target_text_pos = ocr_text.find(target_text)
        if target_text_pos == -1:
            logger.debug(f"在文本中未找到目标文本: '{target_text}'")
            return None
        
        # 只检查目标文本之后的文本
        text_after = ocr_text[target_text_pos:]
        
        # 定义可能的正则表达式模式
        patterns = [
            # 1. 显式包含"统一编号"标签
            r'统一编号[:：]\s*([A-Za-z0-9]+[-－](?:2[0-9lIoO]{3}|\d{4})[-－][0-9lIoOZSB]{5,6})',
            # 2. 格式为 字母/数字-年份-序号
            r'([A-Za-z0-9]+[-－](?:2[0-9lIoO]{3}|\d{4})[-－][0-9lIoOZSB]{5,6})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text_after)
            if match:
                raw_number = match.group(1).strip()
                if raw_number:
                    normalized = normalize_unified_number(raw_number)
                    logger.debug(f"从文本中提取到统一编号: {raw_number} -> {normalized}")
                    return normalized
        
        logger.debug(f"在文本 '{text_after[:50]}...' 中未找到符合模式的统一编号")
        return None
        
    except Exception as e:
        logger.error(f"提取统一编号时发生错误: {e}", exc_info=True)
        return None

def remove_parentheses_content(text: str) -> str:
    """移除文本中的括号及其包含的内容"""
    # 处理中文括号和英文括号
    text = re.sub(r'（[^）]*）', '', text)
    text = re.sub(r'\([^)]*\)', '', text)
    return text

def is_text_match(
    text: str, 
    target: str, 
    match_keywords: str = "检测验委托单代合同", 
    min_keyword_count: int = 2
) -> Tuple[bool, str]:
    """
    检查文本是否匹配目标或包含足够的关键字
    
    Args:
        text: 待检查文本
        target: 目标文本（用于精确匹配，例如 "代合同"）
        match_keywords: 用于模糊匹配的关键字字符串
        min_keyword_count: 模糊匹配所需的最少关键字数量
        
    Returns:
        Tuple[bool, str]: (是否匹配, 匹配类型)
        匹配类型: "exact" 完全匹配, "fuzzy" 模糊匹配, "none" 不匹配
    """
    # 1. 精确匹配检查 (通常不区分大小写和标点)
    processed_text = preprocess_text(text, case_sensitive=False, ignore_punctuation=True)
    processed_target = preprocess_text(target, case_sensitive=False, ignore_punctuation=True)
    if processed_target in processed_text:
        return True, "exact"
    
    # 2. 模糊匹配检查：包含关键字字符串中的任意 N 个字符
    cleaned_text = remove_parentheses_content(text)
    found_count = sum(1 for char in match_keywords if char in cleaned_text)
    
    if found_count >= min_keyword_count:
        return True, "fuzzy"
    
    return False, "none"

def choose_best_number(results: List[UnifiedNumberResult]) -> Optional[str]:
    """从多个OCR结果中选择最可靠的统一编号。
    
    Args:
        results: UnifiedNumberResult 对象的列表，包含识别到的编号和方法名。
        
    Returns:
        最可靠的统一编号字符串，如果无法确定则返回 None。
    """
    # MOVED: [2024-05-15] 从 pdf_processor_usefull.py 迁移 #AFM-XXX
    valid_results = [r.number for r in results if r.number]
    
    if not valid_results:
        logger.debug("选择最佳编号：未能识别到任何有效编号")
        return None
    
    counts = Counter(valid_results)
    total_count = len(valid_results)
    
    logger.debug(f"选择最佳编号：候选编号统计: {dict(counts)}")

    # 策略1: 绝对优势 (超过半数且至少3次)
    most_common_num, most_common_count = counts.most_common(1)[0]
    if most_common_count >= max(3, total_count / 2):
        logger.info(f"选择最佳编号：找到高一致性编号: {most_common_num} ({most_common_count}/{total_count}) - 策略1")
        return most_common_num

    # 策略2: 部分组合 (适用于样本较多但无绝对优势的情况)
    if total_count >= 5:
        prefix_pattern = Counter()
        year_pattern = Counter()
        num_pattern = Counter()
        valid_for_combine = []

        for number in valid_results:
            # 确保编号格式基本正确才用于组合
            if re.match(r'^[A-Z0-9]+-\d{4}-\d{5,6}$', number):
                valid_for_combine.append(number)
                parts = number.split('-')
                if len(parts) == 3:
                    prefix_pattern[parts[0]] += 1
                    year_pattern[parts[1]] += 1
                    num_pattern[parts[2]] += 1
            else:
                logger.debug(f"选择最佳编号：编号 {number} 格式不符，不用于组合策略。")

        if len(valid_for_combine) >= 3: # 至少有3个格式正确的编号才尝试组合
            try:
                common_prefix = prefix_pattern.most_common(1)[0][0] if prefix_pattern else ''
                common_year = year_pattern.most_common(1)[0][0] if year_pattern else ''
                common_num = num_pattern.most_common(1)[0][0] if num_pattern else ''

                # 检查组合出的编号是否在候选列表中实际出现过至少一次
                combined = f"{common_prefix}-{common_year}-{common_num}"
                # 再次校验组合编号格式
                if combined in valid_results and re.match(r'^[A-Z0-9]+-\d{4}-\d{5,6}$', combined):
                    logger.info(f"选择最佳编号：通过部分组合策略确认编号: {combined} - 策略2")
                    return combined
                else:
                    logger.debug(f"选择最佳编号：组合出的编号 {combined} 未在有效结果中找到或格式无效，放弃组合策略。")
            except IndexError: # most_common(1) 可能在Counter为空时出错
                 logger.warning("选择最佳编号：组合编号时发生索引错误，跳过组合策略。")

    # 策略3: 无法确定，返回None
    different_numbers = [f"{number}({count}次)" for number, count in counts.most_common()]
    logger.info(f"选择最佳编号：识别结果不一致或无法通过策略确认，最终未能确定编号: {', '.join(different_numbers)} - 策略3")
    return None 