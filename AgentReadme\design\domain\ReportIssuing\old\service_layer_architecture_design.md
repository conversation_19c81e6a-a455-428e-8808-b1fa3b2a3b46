# 发放单模块服务层架构设计文档

## 📋 文档信息

- **文档名称**: 发放单模块服务层架构设计
- **版本**: v1.0
- **创建日期**: 2025-06-07
- **最后更新**: 2025-06-08 (架构统一优化)
- **相关文档**: [发放单生命周期设计 V2](./issue_form_lifecycle_design_v2.md)

## 📝 优化记录

### v1.0 优化 (2025-06-07)

基于代码实现过程中发现的问题，进行了以下优化：

1. **合并创建和更新方法**
   - 将 `create_issue_form` 和 `update_issue_form` 合并为统一的 `save_issue_form` 方法
   - 保留原方法作为向后兼容的包装器
   - 减少代码重复，简化逻辑

2. **移除冗余的 update_status 方法**
   - 在新架构中，状态更新通过具体的状态转换方法进行
   - `lock_issue_form`、`unlock_issue_form`、`issue_form` 提供更清晰的语义
   - 避免了通用状态更新可能带来的业务逻辑混乱

3. **职责边界进一步明确**
   - `_handle_form_deleted_records` 方法移至 `IssueRecordService`
   - `IssueFormService.issue_form()` 仅处理发放单状态转换
   - `IssueFormService.soft_delete_issue_form()` 仅标记删除
   - 跨模型操作统一由 `IssueTransactionService` 协调

4. **事务处理流程优化**
   - **发放流程**: `IssueTransactionService` 协调 → `IssueFormService.issue_form()` 状态转换 → `IssueRecordService` 创建发放记录 → 档案记录状态同步
   - **删除流程**: `IssueTransactionService` 协调 → `IssueFormService.soft_delete_issue_form()` 标记删除 → `IssueRecordService` 处理关联记录
   - 确保了完整的事务边界和数据一致性

### v1.1 优化 (2025-06-07)

基于代码实现过程中发现的命名和职责问题，进行了以下优化：

1. **优化方法命名和职责边界**
   - 将 `save_issue_form` 重命名为 `create_or_update_draft_form`，明确其只处理草稿状态
   - 保留 `save_issue_form` 作为向后兼容的别名
   - 在方法文档中明确说明状态约束和操作边界

2. **强化状态验证**
   - 在创建和更新操作中添加状态检查
   - 确保只有草稿状态的发放单才能被更新
   - 拒绝对已锁定或已发放的发放单进行编辑操作

3. **完善错误处理**
   - 为状态违规操作提供清晰的错误信息
   - 统一错误码和错误消息格式

### v1.2 优化 (2025-06-07)

基于进一步的业务需求优化，进行了以下改进：

1. **分离验证方法**
   - 将统一的验证方法分离为 `_validate_status_transition` 和 `_validate_operation_permission`
   - 状态转换验证专注于检查状态流转的合法性
   - 操作权限验证专注于检查当前状态下能否执行特定操作

2. **添加确认单功能**
   - 新增 `generate_confirmation_document` 方法，支持在锁定状态下生成可打印的确认单
   - 确认单包含发放单完整信息、条目详情和统计数据
   - 发放时自动生成确认单数据并关联到发放单

3. **优化发放流程**
   - `issue_form` 方法现在返回包含确认单数据的字典结构
   - 事务服务自动处理确认单的生成和关联
   - 支持手动上传确认单文件或自动生成确认单数据

### v1.3 架构重构 (2025-06-08)

基于复杂审计需求的深入分析，进行了重大架构调整：

1. **引入统一审计服务**
   - 新增 `AuditService` 统一管理所有审计逻辑
   - 支持双层审计：业务审计（IssueRecordHistory）+ 通用审计（ChangeLogBatch/RecordChangeLog/FieldChangeLog）
   - 审计批次管理确保操作级别的完整追踪

2. **业务服务职责纯化**
   - 各业务服务（IssueFormService、IssueRecordService、ArchiveRecordService）专注于纯业务数据操作
   - 移除所有审计相关逻辑，由 AuditService 统一处理
   - 提高业务逻辑的清晰度和可测试性

3. **事务协调强化**
   - IssueTransactionService 负责协调业务操作和审计操作
   - 确保复杂跨模型操作的完整事务边界
   - 支持操作失败时的审计批次状态管理

## 🎯 设计目标

### 主要目标

1. **职责分离**: 将原本混乱的服务层职责明确分离，每个服务专注于单一领域
2. **事务管理**: 统一管理跨模型的复杂事务操作，确保数据一致性
3. **代码复用**: 通过合理的服务分层，提高代码复用性和可维护性
4. **业务与数据分离**: 将业务逻辑与数据访问清晰分离

### 解决的问题

- **原服务职责混乱**: `IssueFormService` 既管理发放单，又处理档案记录查询
- **跨模型操作复杂**: 发放操作涉及 `IssueForm`、`ArchiveRecord`、`IssueRecord` 多个模型
- **事务边界不清**: 缺乏统一的事务管理机制
- **历史记录不一致**: `IssueRecord` 与 `ArchiveRecord` 的历史记录管理分散

## 🏗️ 服务层架构

### 整体架构图

```mermaid
graph TB
    subgraph "API/视图层"
        A[IssueFormViewSet]
        B[ArchiveRecordViewSet]
        C[IssueRecordViewSet]
    end
    
    subgraph "业务服务层 (Business Service Layer)"
        D[IssueBusinessService<br/>统一发放业务服务]
    end
    
    subgraph "事务服务层 (Transaction Service Layer)"
        E[IssueTransactionService<br/>跨模型事务协调]
    end
    
    subgraph "数据服务层 (Data Service Layer)"
        F[IssueFormService<br/>发放单CRUD]
        G[ArchiveRecordService<br/>档案记录操作]
        H[IssueRecordService<br/>发放记录管理]
    end
    
    subgraph "审计服务层 (Audit Service Layer)"
        I[AuditService<br/>统一审计管理]
        I1[BusinessAuditManager<br/>业务审计]
        I2[GenericAuditManager<br/>通用审计]
        I --> I1
        I --> I2
    end
    
    subgraph "数据模型层 (Model Layer)"
        J[(IssueForm)]
        K[(ArchiveRecord)]
        L[(IssueRecord)]
        M[(IssueRecordHistory)]
        N[(ChangeLogBatch)]
        O[(RecordChangeLog)]
        P[(FieldChangeLog)]
    end
    
    A --> D
    B --> D
    C --> D
    
    D -.->|只读操作| F
    D -.->|只读操作| G
    D -.->|只读操作| H
    D -->|写入/事务| E
    
    E --> F
    E --> G
    E --> H
    E --> I
    F --> J
    G --> K
    H --> L
    I1 --> M
    I2 --> N
    I2 --> O
    I2 --> P
```

## 📚 服务详细设计

### 1. IssueFormService (发放单数据服务)

**职责**: 专注于发放单模型的CRUD操作和状态转换

```python
class IssueFormService:
    """
    发放单数据服务类
    
    专门处理发放单模型的数据操作，不涉及其他模型。
    负责发放单的创建、更新、删除和状态转换。
    """
    
    # 核心方法 (优化后)
    def create_or_update_draft_form(self, data, form_id=None) -> IssueForm  # 草稿状态专用的创建/更新方法
    def save_issue_form(self, data, form_id=None) -> IssueForm             # 向后兼容别名，调用create_or_update_draft_form
    def create_issue_form(self, data) -> IssueForm                         # 向后兼容包装器
    def update_issue_form(self, form_id, data) -> IssueForm                # 向后兼容包装器
    
    # 确认单功能
    def generate_confirmation_document(self, form_id) -> dict               # 生成确认单数据（仅限锁定状态）
    def get_issue_form(self, form_id) -> IssueForm
    def list_issue_forms(self, filters, page, page_size) -> dict
    
    # 条目管理
    def add_form_items(self, form_id, items_data) -> List[IssueFormItem]
    def remove_form_items(self, form_id, item_ids) -> int
    
    # 状态转换 (核心职责)
    def lock_issue_form(self, form_id) -> IssueForm      # draft -> locked
    def unlock_issue_form(self, form_id) -> IssueForm    # locked -> draft  
    def issue_form(self, form_id, auto_generate_confirmation=True) -> dict  # locked -> issued，返回包含确认单数据的字典
    
    # 删除操作
    def hard_delete_issue_form(self, form_id) -> bool
    def soft_delete_issue_form(self, form_id, reason) -> IssueForm  # 仅标记删除
    
    # 内部验证 (v1.2 优化后)
    def _validate_status_transition(self, issue_form, target_status)           # 验证状态转换合法性
    def _validate_operation_permission(self, issue_form, operation_name, allowed_statuses=None)  # 验证操作权限
```

**设计原则**:

- 只操作 `IssueForm` 和 `IssueFormItem` 模型
- 不直接查询或修改 `ArchiveRecord` 和 `IssueRecord`
- 状态转换是其核心职责，但只处理发放单状态
- `create_or_update_draft_form()` 方法仅处理草稿状态的发放单，拒绝对已锁定或已发放的发放单进行编辑

### 核心方法实现示例

```python
@transaction.atomic
def create_or_update_draft_form(self, data, form_id=None):
    """
    创建或更新草稿状态的发放单
    
    这个方法专门处理草稿状态的发放单操作，严格限制操作边界：
    - 创建: 新发放单默认为草稿状态
    - 更新: 只允许更新草稿状态的发放单
    - 拒绝: 对已锁定或已发放的发放单进行编辑操作
    
    Args:
        data: 发放单数据字典
        form_id: 发放单ID，None表示创建新发放单
        
    Returns:
        IssueForm: 保存后的发放单对象
        
    Raises:
        IssueFormError: 当尝试编辑非草稿状态的发放单时
    """
    if form_id:
        # 更新现有发放单 - 强制状态检查
        issue_form = self.get_issue_form(form_id)
        self._validate_draft_operation(issue_form, "更新")
        
        # 只有草稿状态才能更新
        if issue_form.status != 'draft':
            raise IssueFormError(
                f"不能更新{issue_form.get_status_display()}状态的发放单，只能更新草稿状态的发放单"
            )
        
        # 更新字段...
        return issue_form
    else:
        # 创建新发放单 - 总是草稿状态
        issue_form = IssueForm.objects.create(
            # ... 字段设置
            status='draft'  # 明确设置为草稿状态
        )
        return issue_form

def save_issue_form(self, data, form_id=None):
    """向后兼容方法，调用create_or_update_draft_form"""
    return self.create_or_update_draft_form(data, form_id)

def _validate_draft_operation(self, issue_form, operation_name):
    """验证是否可以对发放单执行草稿操作"""
    if issue_form.status != 'draft':
        raise IssueFormError(
            f"不能{operation_name}{issue_form.get_status_display()}状态的发放单"
        )
```

- `issue_form()` 方法仅负责状态转换，发放记录创建由 `IssueTransactionService` 协调
- `soft_delete_issue_form()` 方法仅标记删除，关联记录处理由 `IssueTransactionService` 协调

### 2. ArchiveRecordService (档案记录服务)

**职责**: 专门处理档案记录相关的查询、验证和计算操作

```python
class ArchiveRecordService:
    """
    档案记录服务类
    
    专门处理档案记录相关的查询和验证操作，
    不直接操作发放单，通过业务服务类进行编排。
    """
    
    # 查询方法
    def get_available_archive_records_for_issue(self, filters=None) -> QuerySet
    
    # 验证方法
    def check_archive_records_status_for_issue(self, archive_record_ids, is_first, is_second) -> dict
    def _validate_archive_record_for_issue(self, archive_record, is_first, is_second)
    
    # 计算方法
    def calculate_copies_for_issue_sequence(self, archive_record, is_first, is_second) -> int
```

**设计原则**:

- 专注于档案记录的业务逻辑
- 提供发放前的验证和计算服务
- 不直接修改档案记录，状态更新由 `IssueRecordService` 负责
- 为其他服务提供档案记录相关的工具方法

### 3. IssueRecordService (发放记录服务)

**职责**: 统一管理发放记录的完整生命周期，确保与档案记录的状态同步

```python
class IssueRecordService:
    """
    发放记录服务类
    
    专门处理发放记录的创建、更新、删除和历史记录管理。
    确保 IssueRecord 和 ArchiveRecord 的状态同步，以及历史记录的一致性。
    """
    
    # 核心创建方法
    def create_issue_records_for_form(self, issue_form, archive_record_ids) -> List[IssueRecord]
    def _create_single_issue_record(self, issue_form, archive_record) -> IssueRecord
    
    # 状态同步 (关键职责)
    def _update_archive_record_issue_status(self, archive_record, issue_record)
    def _recalculate_archive_record_status(self, archive_record)
    
    # 记录管理
    def update_issue_record(self, record_id, update_data, reason) -> IssueRecord
    def soft_delete_issue_record(self, record_id, reason) -> IssueRecord
    
    # 历史记录管理
    def _create_issue_record_history(self, issue_record, action, detail) -> IssueRecordHistory
    
    # 查询方法
    def get_issue_records_by_form(self, issue_form_id, include_deleted=False) -> QuerySet
    def get_issue_history_by_record(self, record_id) -> QuerySet
    
    # 内部逻辑
    def _determine_issue_type_and_copies(self, archive_record, issue_form) -> tuple
```

**设计原则**:

- 负责 `IssueRecord` 和 `ArchiveRecord` 的状态同步
- 专注于纯业务数据操作，不包含审计逻辑
- 确保发放操作的原子性和一致性
- 提供完整的发放记录生命周期管理

### 4. AuditService (统一审计服务)

**职责**: 统一管理整个系统的审计追踪，包括业务语义审计和技术字段审计

```python
class AuditService:
    """
    统一审计服务类
    
    管理整个系统的审计追踪，提供双层审计机制：
    1. 业务审计：基于业务语义的操作记录（如发放记录历史）
    2. 通用审计：基于字段级别的完整变更追踪
    """
    
    def __init__(self):
        self.business_audit_manager = BusinessAuditManager()
        self.generic_audit_manager = GenericAuditManager()
    
    # 审计批次管理
    def create_audit_batch(self, operation_type, operation_description, user_id=None) -> ChangeLogBatch
    def finalize_audit_batch(self, batch_id, status='completed')
    
    # 核心审计方法
    @transaction.atomic
    def record_issue_operation(self, operation_type, issue_form, records_data=None, user_id=None) -> dict
    
    @transaction.atomic
    def record_archive_operation(self, operation_type, archive_records, user_id=None) -> dict
    
    # 查询方法
    def get_audit_history_for_record(self, model_name, record_id) -> QuerySet
    def get_business_history_for_issue_record(self, issue_record_id) -> QuerySet


class BusinessAuditManager:
    """
    业务审计管理器
    
    专门处理具有业务语义的审计记录，如发放记录历史。
    这些记录包含业务操作的上下文信息和业务术语。
    """
    
    # 发放记录业务审计
    def create_issue_record_history(self, issue_record, action, detail, user_id=None) -> IssueRecordHistory
    def update_issue_record_history(self, history_id, update_data) -> IssueRecordHistory
    
    # 查询方法
    def get_issue_record_business_history(self, issue_record_id) -> QuerySet
    def get_issue_form_related_business_history(self, issue_form_id) -> QuerySet


class GenericAuditManager:
    """
    通用审计管理器
    
    提供完整的字段级别变更追踪，记录所有模型的增删改操作。
    采用三层结构：批次 -> 记录 -> 字段，确保操作的完整性。
    """
    
    # 核心记录方法
    def record_model_operation(self, batch_id, operation_type, model_instance, 
                              changed_fields=None, user_id=None) -> RecordChangeLog
    
    def record_field_changes(self, record_change_log, field_changes) -> List[FieldChangeLog]
    
    # 批量操作
    def record_bulk_operation(self, batch_id, operation_type, model_instances, user_id=None) -> List[RecordChangeLog]
    
    # 查询方法
    def get_record_changes_by_batch(self, batch_id) -> QuerySet
    def get_field_changes_for_record(self, record_change_log_id) -> QuerySet
    def get_complete_audit_trail(self, model_name, record_id) -> dict
```

**设计原则**:

- **双层审计**: 业务审计提供业务语义，通用审计提供完整技术追踪
- **批次管理**: 通过ChangeLogBatch确保操作级别的完整性和原子性
- **可扩展性**: 通用审计管理器可支持任何Django模型的审计
- **查询友好**: 提供多维度的审计数据查询接口

### 审计数据模型关系

```mermaid
graph TB
    subgraph "业务审计"
        A[IssueRecordHistory<br/>发放记录历史]
    end
    
    subgraph "通用审计"
        B[ChangeLogBatch<br/>变更批次]
        C[RecordChangeLog<br/>记录变更日志]
        D[FieldChangeLog<br/>字段变更日志]
        
        B --> C
        C --> D
    end
    
    subgraph "业务模型"
        E[IssueRecord]
        F[ArchiveRecord]
        G[IssueForm]
    end
    
    A -.-> E
    C --> E
    C --> F
    C --> G
```

### 5. IssueTransactionService (发放事务服务)

**职责**: 协调跨模型的复杂事务操作，确保数据一致性

```python
class IssueTransactionService:
    """
    发放事务服务类
    
    统一管理涉及多个模型的事务操作，确保数据一致性。
    处理跨 IssueForm、ArchiveRecord、IssueRecord 的复杂业务流程，
    协调业务操作和审计操作的完整事务边界。
    """
    
    def __init__(self):
        self.issue_form_service = IssueFormService()
        self.issue_record_service = IssueRecordService()
        self.archive_record_service = ArchiveRecordService()
        self.audit_service = AuditService()
    
    # 核心事务方法（包含完整审计）
    @transaction.atomic
    def issue_form_with_records(self, form_id, user_id=None) -> dict
    
    @transaction.atomic  
    def lock_form_with_validation(self, form_id, user_id=None) -> dict
    
    @transaction.atomic
    def soft_delete_issued_form(self, form_id, reason, user_id=None) -> dict
    
    @transaction.atomic
    def hard_delete_draft_form(self, form_id, reason, user_id=None) -> bool
    
    # 档案归档事务（新增）
    @transaction.atomic
    def archive_issued_form(self, form_id, user_id=None) -> dict
    
    # 状态查询
    def get_form_issue_status_summary(self, form_id) -> dict
    def get_audit_summary_for_form(self, form_id) -> dict
```

**核心事务流程** (`issue_form_with_records`):

```python
def issue_form_with_records(self, form_id, user_id=None):
    """发放单发放操作 - 核心事务方法（包含完整审计）"""
    # 1. 创建审计批次
    audit_batch = self.audit_service.create_audit_batch(
        operation_type='发放单发放',
        operation_description=f'发放单 {form_id} 执行发放操作',
        user_id=user_id
    )
    
    try:
        # 2. 获取并验证发放单
        issue_form = self.issue_form_service.get_issue_form(form_id)
        
        # 3. 验证档案记录状态
        validation_results = self.archive_record_service.check_archive_records_status_for_issue(...)
        
        # 4. 创建发放记录 (纯业务操作)
        created_records = self.issue_record_service.create_issue_records_for_form(...)
        
        # 5. 更新发放单状态 (纯业务操作)
        updated_form = self.issue_form_service.issue_form(form_id)
        
        # 6. 记录完整的审计信息
        audit_result = self.audit_service.record_issue_operation(
            operation_type='发放',
            issue_form=updated_form,
            records_data=created_records,
            user_id=user_id
        )
        
        # 7. 完成审计批次
        self.audit_service.finalize_audit_batch(audit_batch.id, 'completed')
        
        # 8. 返回完整结果
        return {
            'issue_form': updated_form, 
            'issue_records': created_records,
            'audit_info': audit_result
        }
        
    except Exception as e:
        # 异常时标记审计批次失败
        self.audit_service.finalize_audit_batch(audit_batch.id, 'failed')
        raise e
```

**设计原则**:

- 提供原子性的跨模型操作，包含完整的审计追踪
- 负责复杂业务流程的事务边界管理，协调业务操作和审计操作
- 协调各个数据服务和审计服务，不直接操作数据库
- 确保操作失败时的完整回滚，包括审计批次状态管理
- 为每个重要操作提供完整的审计上下文和追踪能力

### 5. 业务服务层设计

#### 5.1 IssueBusinessService (统一发放业务服务)

**职责**: 整合发放单、发放记录、档案状态的完整业务流程，专注业务逻辑验证和计算

```python
class IssueBusinessService:
    """
    统一发放业务服务类
    
    整合发放单、发放记录、档案状态的完整业务流程。
    专注业务规则验证、业务计算和权限控制，不处理数据同步。
    """
    
    def __init__(self):
        self.issue_form_service = IssueFormService()
        self.issue_record_service = IssueRecordService()
        self.archive_record_service = ArchiveRecordService()
        self.transaction_service = IssueTransactionService()
    
    # === 发放单完整流程 ===
    def create_issue_form_with_validation(self, data, user_id=None) -> IssueForm
    def add_items_with_validation(self, form_id, items_data, user_id=None) -> List[IssueFormItem]
    def lock_with_validation(self, form_id, user_id=None) -> IssueForm
    def unlock_with_validation(self, form_id, user_id=None) -> IssueForm
    def issue_with_validation(self, form_id, user_id=None) -> dict  # 调用 IssueTransactionService
    def archive_with_validation(self, form_id, user_id=None) -> dict
    
    # === 发放记录管理 ===
    def get_issue_records_with_permission(self, filters, user_id=None) -> QuerySet
    def get_issue_record_detail_with_validation(self, record_id, user_id=None) -> IssueRecord
    def update_issue_record_with_audit(self, record_id, update_data, reason, user_id=None) -> IssueRecord
    def revoke_issue_records_with_validation(self, record_ids, reason, user_id=None) -> List[IssueRecord]
    
    # === 核心业务计算 ===
    def calculate_issue_type_and_copies(self, archive_record, issue_form) -> dict
    def validate_issue_eligibility(self, archive_ids, form_data, user_id=None) -> dict
    def get_available_archives_for_issue(self, filters, user_id=None) -> QuerySet
    
    # === 查询聚合 ===
    def get_issue_statistics_with_permission(self, filters, user_id=None) -> dict
    def get_issue_history_with_permission(self, record_id, user_id=None) -> QuerySet
    def generate_issue_report_with_validation(self, report_params, user_id=None) -> dict
    
    # === 验证规则 ===
    def validate_issue_form_consistency(self, form_id) -> dict
    def check_archive_issue_eligibility(self, archive_id, user_id=None) -> dict
    def _check_permission(self, action, obj=None, user_id=None)
    def _validate_business_rules(self, action, data, user_id=None)
    
    # === 删除操作 ===
    def hard_delete_with_validation(self, form_id, reason, user_id=None) -> bool
    def soft_delete_with_validation(self, form_id, reason, user_id=None) -> IssueForm
```

**核心设计原则**:

- **完整业务流程**: 整合发放单、发放记录、档案状态的完整业务流程，提供统一的业务接口
- **纯业务逻辑**: 专注业务规则验证和计算，不处理数据同步，所有跨模型同步逻辑由事务层处理
- **核心业务计算**: 发放类型判断和数量控制逻辑的明确归属，确保业务规则的一致性
- **简洁的API接口**: 为上层提供高级业务操作接口，屏蔽底层服务复杂性
- **读写分离**: 读操作直接访问数据服务，写操作通过事务服务确保数据一致性和审计完整性
- **权限统一**: 统一的权限检查机制，确保所有业务操作的安全性
- **用户上下文**: 所有方法都接受user_id参数，支持用户相关的权限和审计

## 🔄 典型调用流程

### 发放单发放流程（包含完整审计）

```mermaid
sequenceDiagram
    participant API as API层
    participant BS as IssueBusinessService
    participant TS as IssueTransactionService
    participant FS as IssueFormService
    participant RS as IssueRecordService
    participant AS as ArchiveRecordService
    participant AUD as AuditService
    
    API->>BS: issue_with_validation(form_id, user_id)
    BS->>FS: get_issue_form(form_id)
    BS->>BS: _check_permission('issue')
    BS->>BS: calculate_issue_type_and_copies()
    BS->>TS: issue_form_with_records(form_id, calculated_data, user_id)
    
    TS->>AUD: create_audit_batch('发放单发放', ...)
    TS->>FS: get_issue_form(form_id)
    TS->>TS: 跨模型同步逻辑
    TS->>RS: create_issue_records_for_form(...)
    TS->>AS: update_archive_issue_status(...)
    TS->>FS: issue_form(form_id)
    TS->>AUD: record_issue_operation('发放', ...)
    TS->>AUD: finalize_audit_batch(batch_id, 'completed')
    
    TS-->>BS: 返回发放结果（含审计信息）
    BS-->>API: 返回业务结果
```

### 档案归档流程（跨6个模型的复杂事务）

```mermaid
sequenceDiagram
    participant API as API层
    participant TS as IssueTransactionService
    participant FS as IssueFormService
    participant RS as IssueRecordService
    participant AS as ArchiveRecordService
    participant AUD as AuditService
    
    API->>TS: archive_issued_form(form_id, user_id)
    
    TS->>AUD: create_audit_batch('档案归档', ...)
    
    Note over TS,AUD: 业务数据更新
    TS->>FS: get_issue_form(form_id)
    TS->>RS: get_issue_records_by_form(form_id)
    TS->>AS: update_archive_records_status(...)
    
    Note over TS,AUD: 完整审计记录
    TS->>AUD: record_issue_operation('归档', issue_form, ...)
    TS->>AUD: record_archive_operation('状态更新', archive_records, ...)
    
    Note over AUD: 审计覆盖6个模型
    AUD->>AUD: 记录 IssueRecord 变更
    AUD->>AUD: 记录 ArchiveRecord 变更
    AUD->>AUD: 创建 IssueRecordHistory
    AUD->>AUD: 创建 ChangeLogBatch
    AUD->>AUD: 创建 RecordChangeLog
    AUD->>AUD: 创建 FieldChangeLog
    
    TS->>AUD: finalize_audit_batch(batch_id, 'completed')
    TS-->>API: 返回归档结果（含完整审计追踪）
```

### 发放单条目管理流程

```mermaid
sequenceDiagram
    participant API as API层
    participant BS as IssueBusinessService  
    participant FS as IssueFormService
    participant AS as ArchiveRecordService
    
    API->>BS: add_items_with_validation(form_id, items)
    BS->>FS: get_issue_form(form_id)
    BS->>BS: 验证发放单状态和权限
    
    loop 验证每个条目
        BS->>AS: _validate_archive_record_for_issue(...)
        BS->>BS: calculate_issue_type_and_copies(...)
    end
    
    BS->>FS: add_form_items(form_id, validated_items)
    FS-->>BS: 返回添加的条目
    BS-->>API: 返回验证和添加结果
```

## 🏛️ 服务调用规则：只读与写入分离

为了在确保数据完整性的同时获得最佳性能，本架构采用读写分离的调用策略。业务服务层 (`Business Service Layer`) 扮演着"调用路由器"的角色，根据操作类型决定调用路径。

### 1. 只读操作 (Read-Only Operations)

- **定义**: 任何不修改数据库状态的查询操作，如获取列表、查看详情等。
- **路径**: `API -> BusinessService -> DataService`
- **原则**:
  - `BusinessService` 在执行完权限检查和业务规则验证后，**可以直接调用**对应的 `DataService`。
  - 这样做可以避免启动不必要的数据库事务，性能更高。

### 2. 写入操作 (Write Operations)

- **定义**: 任何会修改数据库状态的操作，如创建、更新、删除、状态变更等。
- **路径**: `API -> BusinessService -> TransactionService -> (DataServices + AuditService)`
- **原则**:
  - `BusinessService` **必须通过** `TransactionService` 来执行所有写入操作。
  - **严禁** `BusinessService` 直接调用 `DataService` 的写入方法。
  - `TransactionService` 负责封装数据库事务、协调多个数据服务、并调用 `AuditService` 进行完整的审计追踪。

### 调用示例

```python
class ArchiveRecordBusinessService:
    # ...
    
    def get_archive_detail_with_validation(self, archive_id, user_id=None):
        """只读操作：直接调用数据服务"""
        self._check_archive_access_permission('view', archive_id, user_id)
        # 直接调用 DataService
        return self.archive_record_service.get_archive_record(archive_id)

    def update_archive_status_with_audit(self, archive_id, status_data, user_id=None):
        """写入操作：必须通过事务服务"""
        self._check_archive_access_permission('update_status', archive_id, user_id)
        # 调用 TransactionService
        return self.transaction_service.update_archive_status(
            archive_id, status_data, user_id
        )
```

## 🏛️ 架构一致性原则

### API视图层统一访问规则

**为什么所有API都必须通过业务服务层？**

1. **权限控制统一**:
   - 所有API操作都需要进行权限检查
   - 业务服务层提供统一的权限验证机制
   - 避免权限逻辑分散在各个视图中

2. **业务规则一致性**:
   - 每个领域都有特定的业务规则和验证逻辑
   - 业务服务层确保这些规则得到统一执行
   - 防止直接调用数据服务绕过业务约束

3. **审计追踪完整性**:
   - 重要操作需要通过事务服务进行审计记录
   - 业务服务层协调审计需求
   - 确保所有用户操作都有完整的审计轨迹

4. **错误处理统一**:
   - 业务服务层提供统一的错误处理和响应格式
   - 确保API响应的一致性
   - 便于前端统一处理错误情况

### 修正后的API访问模式

```python
# ✅ 正确的API访问模式 - 统一通过业务服务
class IssueFormViewSet(ViewSet):
    def __init__(self):
        self.business_service = IssueBusinessService()
    
    def create(self, request):
        return self.business_service.create_issue_form_with_validation(
            request.data, request.user.id
        )
    
    def issue(self, request, pk):
        return self.business_service.issue_with_validation(pk, request.user.id)

class ArchiveRecordViewSet(ViewSet):
    def __init__(self):
        self.business_service = IssueBusinessService()
    
    def list(self, request):
        return self.business_service.get_available_archives_for_issue(
            request.GET, request.user.id
        )

class IssueRecordViewSet(ViewSet):
    def __init__(self):
        self.business_service = IssueBusinessService()
    
    def retrieve(self, request, pk):
        return self.business_service.get_issue_record_detail_with_validation(
            pk, request.user.id
        )
    
    def revoke(self, request):
        return self.business_service.revoke_issue_records_with_validation(
            request.data.get('record_ids'), 
            request.data.get('reason'),
            request.user.id
        )

# ❌ 错误的API访问模式（绕过业务层）
class BadArchiveRecordViewSet(ViewSet):
    def __init__(self):
        self.data_service = ArchiveRecordService()  # 直接调用数据服务
    
    def list(self, request):
        return self.data_service.get_available_archive_records_for_issue()  # 缺少权限检查
```

## 📊 服务依赖关系

### 依赖层次

1. **数据服务层**: 相互独立，只依赖模型层，专注于纯业务数据操作
2. **审计服务层**: 独立的审计管理，支持多种模型的审计追踪
3. **事务服务层**: 依赖数据服务和审计服务，协调跨模型操作和审计
4. **业务服务层**: 依赖事务服务和数据服务，提供业务逻辑

### 具体依赖关系

**业务服务层依赖**:

- `IssueBusinessService` → `IssueTransactionService`, `IssueFormService`, `ArchiveRecordService`, `IssueRecordService`

**事务服务层依赖**:

- `IssueTransactionService` → `IssueFormService`, `ArchiveRecordService`, `IssueRecordService`, `AuditService`

**审计服务层依赖**:

- `AuditService` → `BusinessAuditManager`, `GenericAuditManager`

**依赖原则**:

- 数据服务层之间无直接依赖，也不依赖审计服务
- 审计服务独立于业务服务，可支持任何模型的审计
- **所有API必须通过对应的业务服务层**，确保权限和业务规则的统一执行

## 🔧 与现有设计的配合

### 与生命周期设计的关系

- 完全支持 [发放单生命周期设计 V2](./issue_form_lifecycle_design_v2.md) 中的三状态模型
- `IssueFormService` 的状态转换方法直接对应生命周期状态流转
- `IssueTransactionService` 确保状态转换的事务完整性

### 删除策略的实现

- **硬删除**: 通过 `IssueTransactionService.hard_delete_draft_form` 实现
- **软删除**: 通过 `IssueTransactionService.soft_delete_issued_form` 实现
- 确保删除操作的事务一致性和相关记录的正确处理

## ✅ 设计优势

### 1. 职责清晰

- 统一业务服务整合完整发放业务流程，避免职责分散
- 数据服务专注于纯CRUD操作，完全不涉及跨模型同步
- 事务服务负责所有跨模型协调和数据一致性
- 审计服务独立管理所有审计需求，支持双层审计架构
- 便于理解、测试和维护

### 2. 事务安全

- 统一的事务管理机制，包含业务操作和审计操作
- `IssueTransactionService` 确保跨模型操作的原子性和一致性
- 审计批次管理确保操作级别的完整性追踪
- 避免了服务间的循环依赖

### 3. 审计完整性

- 双层审计机制：业务语义审计 + 技术字段审计
- 批次管理确保复杂操作的审计完整性
- 支持操作失败时的审计状态管理
- 提供完整的审计查询和追踪能力

### 4. 可扩展性

- 通过服务组合可以灵活实现新的业务场景
- 审计服务可支持任何Django模型的审计需求
- 新增功能不会影响现有服务的稳定性
- 业务逻辑与审计逻辑完全解耦

### 5. 代码复用

- 基础服务可以被多个业务场景复用
- 统一的审计服务避免重复的审计实现
- 避免重复的验证和数据操作逻辑
- 状态转换通过具体方法实现，语义更清晰

### 6. 测试友好

- 每个服务可以独立测试
- 业务逻辑与审计逻辑分离，便于单独测试
- 通过依赖注入可以轻松进行单元测试和集成测试
- 移除了混合职责，测试更加专注

### 7. 维护性

- 业务变更不影响审计逻辑，审计变更不影响业务逻辑
- 清晰的层次结构便于问题定位和修复
- 统一的审计接口简化了审计功能的维护

## 🚀 未来扩展方向

### 1. 性能优化

- 可在 `ArchiveRecordService` 中添加缓存机制
- 在 `IssueRecordService` 中优化批量操作

### 2. 异步处理

- 对于大批量发放操作，可以引入异步处理机制
- 在 `IssueTransactionService` 中实现异步发放队列

### 3. 事件驱动

- 可以在关键操作点添加事件发布机制
- 支持审计日志、通知等横切关注点

### 4. API版本兼容

- 通过业务服务层的适配，可以支持多版本API
- 向后兼容性的统一管理

## 📝 实施注意事项

### 1. 迁移策略

- 逐步重构现有代码，避免大爆炸式修改
- 保持向后兼容，确保现有功能正常运行

### 2. 测试覆盖

- 优先为核心事务流程编写集成测试
- 为每个服务编写完整的单元测试

### 3. 文档维护

- 及时更新相关技术文档
- 提供清晰的使用示例和最佳实践

### 4. 监控和日志

- 在关键操作点添加适当的日志记录
- 建立服务调用链的监控机制

---

**文档维护**: 本文档应随着代码实现的变化及时更新，确保设计与实现的一致性。
