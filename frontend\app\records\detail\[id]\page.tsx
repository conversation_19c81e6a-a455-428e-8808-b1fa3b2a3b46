import { PageTitle } from "@/components/page-title"
import { RecordDetails } from "@/components/records/record-details"
import { RecordActions } from "@/components/records/record-actions"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RecordHistory } from "@/components/records/record-history"
import { RecordFiles } from "@/components/records/record-files"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { PageLayout } from "@/components/common/page-layout"

export default async function RecordPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  // 定义页面操作按钮
  const actions = [
    {
      label: "返回台账列表",
      icon: <ArrowLeft className="h-4 w-4" />,
      href: "/records",
      variant: "outline" as const
    }
  ]
  
  // 定义固定标签栏
  const fixedTabs = (
    <Tabs defaultValue="details" className="w-full">
      <TabsList>
        <TabsTrigger value="details">基本信息</TabsTrigger>
        <TabsTrigger value="files">归档文件</TabsTrigger>
        <TabsTrigger value="history">变更历史</TabsTrigger>
      </TabsList>
    </Tabs>
  )
  
  // 定义状态卡片区域
  const statusCards = <RecordActions recordId={id} />
  
  return (
    <PageLayout
      title="档案详情"
      subtitle={`档案记录 ${id} 的详细信息`}
      actions={actions}
      fixedTabs={fixedTabs}
      statusCards={statusCards}
    >
      <Tabs defaultValue="details" className="space-y-6">
        {/* 隐藏TabsList，因为已经在fixedTabs中定义 */}
        <div className="hidden">
          <TabsList>
            <TabsTrigger value="details">基本信息</TabsTrigger>
            <TabsTrigger value="files">归档文件</TabsTrigger>
            <TabsTrigger value="history">变更历史</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="details" className="space-y-6 pt-2">
          <RecordDetails recordId={id} />
        </TabsContent>
        <TabsContent value="files" className="space-y-6 pt-2">
          <RecordFiles recordId={id} />
        </TabsContent>
        <TabsContent value="history" className="space-y-6 pt-2">
          <RecordHistory recordId={id} />
        </TabsContent>
      </Tabs>
    </PageLayout>
  )
}
