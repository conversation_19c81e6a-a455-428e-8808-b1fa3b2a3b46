from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import IssueForm, IssueFormItem

User = get_user_model()


class IssueFormItemSerializer(serializers.ModelSerializer):
    """
    发放单条目序列化器
    """
    archive_record_id = serializers.IntegerField(source='archive_record.id', read_only=True)
    unified_number = serializers.CharField(source='archive_record.unified_number', read_only=True)
    sample_name = serializers.CharField(source='archive_record.sample_name', read_only=True)
    
    class Meta:
        model = IssueFormItem
        fields = [
            'id', 'archive_record_id', 'unified_number', 'sample_name', 'copies', 'first', 'second', 
            'receiver_name', 'receiver_unit', 'receiver_phone', 'notes'
        ]


class IssueFormSerializer(serializers.ModelSerializer):
    """
    发放单序列化器
    """
    items = IssueFormItemSerializer(many=True, read_only=True)
    status_display = serializers.Char<PERSON><PERSON>(source='get_status_display', read_only=True)
    issuer_username = serializers.Char<PERSON><PERSON>(source='issuer.username', read_only=True)
    
    class Meta:
        model = IssueForm
        fields = [
            'id', 'issue_number', 'issue_date', 'receiver_name', 'receiver_unit',
            'receiver_phone', 'status', 'status_display', 'is_deleted',
            'issuer', 'issuer_username', 'deleted_by', 'confirmation_file',
            'created_at', 'updated_at', 'deleted_at', 'notes', 'deletion_reason',
            'items'
        ]
        read_only_fields = ('issuer', 'deleted_by')


class DraftUpdateSerializer(serializers.ModelSerializer):
    """
    专门用于草稿更新的简化序列化器
    返回扁平化格式，确保前端能正确处理所有字段
    """
    # 返回简化的items_data格式
    items_data = serializers.SerializerMethodField()
    
    class Meta:
        model = IssueForm
        fields = [
            'id', 'status', 'issue_number', 'receiver_name', 
            'receiver_unit', 'receiver_phone', 'notes', 'items_data'
        ]
    
    def get_items_data(self, obj):
        """返回与前端请求格式一致的items_data"""
        return [
            {
                'archive_record_id': item.archive_record.unified_number,
                'copies': item.copies
            }
            for item in obj.items.all()
        ]
