"use client";

import React, { useEffect, useState, useMemo, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";
import {
  ColDef,
  ValueGetterParams,
  ICellRendererParams,
  GridReadyEvent,
  GridOptions,
} from "ag-grid-enterprise";

import agGridConfig from "@/lib/ag-grid-config";
import importHistoryService, {
  ImportHistoryRecord,
  ImportHistoryQueryParams,
} from "@/services/domain/records/import-history/import-history-service";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Download, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { getHeaderColumnWidth } from "@/utils/ag-grid-column-width-utils";

interface ImportHistoryClientGridProps {
  pageSize?: number;
}

export default function ImportHistoryClientGrid({
  pageSize = 50,
}: ImportHistoryClientGridProps) {
  const [rowData, setRowData] = useState<ImportHistoryRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const router = useRouter();
  
  // Grid Options
  const gridOptions = useMemo<GridOptions<ImportHistoryRecord>>(() => ({
    rowModelType: "clientSide",
    ...agGridConfig.clientSideDefaults,
    ...agGridConfig.performanceConfig,
    theme: agGridConfig.themes.quartzCustom,
    paginationPageSize: pageSize,
    paginationPageSizeSelector: [10, 20, 50, 100, 200],
    headerHeight: agGridConfig.performanceConfig.headerHeight || 42,
    rowHeight: 48,
    enableCellTextSelection: true,
    domLayout: 'normal' as const,
  }), [pageSize]);

  // 列定义
  const columnDefs = useMemo<ColDef<ImportHistoryRecord>[]>(() => [
    {
      field: "id",
      headerName: "批次号",
      filter: "agTextColumnFilter",
      width: getHeaderColumnWidth("批次号"),
      pinned: "left",
    },
    {
      field: "fileName",
      headerName: "文件名",
      filter: "agTextColumnFilter",
      width: getHeaderColumnWidth("文件名"),
    },
    {
      field: "fileSize",
      headerName: "文件大小",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("文件大小"),
      valueFormatter: (params) => {
        if (!params.value) return "";
        const size = params.value;
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
        return `${(size / (1024 * 1024)).toFixed(2)} MB`;
      }
    },
    {
      field: "importDate",
      headerName: "导入时间",
      valueFormatter: (params) =>
        params.value ? new Date(params.value).toLocaleString() : "",
      filter: "agDateColumnFilter",
      width: getHeaderColumnWidth("导入时间", { isSpecialCase: true, specialCaseMinWidth: 160}),
    },
    {
      field: "processingTime",
      headerName: "处理时长",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("处理时长"),
      valueFormatter: (params) => {
        if (!params.value) return "";
        const seconds = params.value;
        if (seconds < 60) return `${seconds}秒`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds}秒`;
      }
    },
    {
      headerName: "导入人",
      valueGetter: (params: ValueGetterParams<ImportHistoryRecord>) =>
        params.data?.importUser?.username || "系统",
      filter: "agTextColumnFilter",
      width: getHeaderColumnWidth("导入人"),
    },
    {
      field: "totalRecords",
      headerName: "总记录数",
      type: "numericColumn",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("总记录数"),
    },
    {
      field: "createdCount",
      headerName: "新增",
      type: "numericColumn",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("新增"),
    },
    {
      field: "updatedCount",
      headerName: "更新",
      type: "numericColumn",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("更新"),
    },
    {
      headerName: "跳过",
      tooltipValueGetter: () => "所有被跳过的记录总数(含用户手动跳过和系统自动跳过)",
      type: "numericColumn",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("跳过"),
      valueGetter: (params: ValueGetterParams<ImportHistoryRecord>) => {
        // 计算跳过数 = 总记录数 - 新增 - 更新 - 失败
        const data = params.data;
        if (!data) return 0;
        const total = data.totalRecords || 0;
        const created = data.createdCount || 0;
        const updated = data.updatedCount || 0;
        const failed = data.failedCount || 0;
        return Math.max(0, total - created - updated - failed);
      }
    },
    {
      field: "failedCount",
      headerName: "失败",
      tooltipValueGetter: () => "处理失败的记录总数",
      type: "numericColumn",
      filter: "agNumberColumnFilter",
      width: getHeaderColumnWidth("失败"),
    },
    {
      field: "status",
      headerName: "处理状态",
      filter: "agTextColumnFilter",
      width: getHeaderColumnWidth("处理状态", { isSpecialCase: true }),
      cellRenderer: (params: ICellRendererParams<ImportHistoryRecord>) => {
        const status = params.value;
        switch (status) {
          case "completed": return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">已完成</Badge>;
          case "processing": return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">处理中</Badge>;
          case "pending": return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">等待处理</Badge>;
          case "failed": return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">失败</Badge>;
          case "partial": return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">部分成功</Badge>;
          default: return <Badge variant="outline">未知</Badge>;
        }
      },
    },
    {
      headerName: "操作",
      filter: false,
      sortable: false,
      resizable: false,
      pinned: "right",
      width: 100,
      cellStyle: { display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0' },
      cellRenderer: (params: ICellRendererParams<ImportHistoryRecord>) => {
        if (!params.data) return null;
        const record = params.data;
        
        return (
          <div className="flex items-center justify-center gap-1">
            <Button variant="outline" size="icon" asChild title="查看详情" className="hover:bg-accent/80 hover:text-accent-foreground p-0.5 h-6 w-6">
              <Link href={`/records/import-history/${record.id}`}>
                <Eye className="h-3.5 w-3.5" />
              </Link>
            </Button>
          </div>
        );
      },
    },
  ], [toast]);

  // 默认列配置
  const defaultColDef = useMemo<ColDef>(() => ({
    ...agGridConfig.defaultColDef,
    minWidth: 80,
  }), []);

  // 数据加载
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // CHANGE: [2025-06-19] 修复字段名，使用camelCase命名的服务方法和参数
        // 循环获取所有页面数据，确保客户端拥有完整数据集用于本地分页和过滤
        const allData: ImportHistoryRecord[] = [];
        let currentPage = 1;
        let hasMoreData = true;
        const pageSize = 1000; // 每次获取1000条，减少请求次数
        
        while (hasMoreData) {
          const queryParams: ImportHistoryQueryParams = { 
            page: currentPage, 
            pageSize: pageSize
          };
          
          const response = await importHistoryService.getImportHistoryRecords(queryParams);
          
          // 将当前页数据添加到总数据中
          allData.push(...response.results);
          
          // 检查是否还有更多数据
          const totalPages = Math.ceil(response.count / pageSize);
          hasMoreData = currentPage < totalPages;
          
          console.log(`[ImportHistoryClientGrid] 已获取第${currentPage}页，共${totalPages}页，当前总数据: ${allData.length}`);
          
          currentPage++;
        }
        
        console.log("[ImportHistoryClientGrid] 所有导入历史数据加载完成，总数:", allData.length);
        setRowData(allData);
        
      } catch (error: any) {
        console.error("获取导入历史失败:", error);
        
        toast({ 
          title: "加载失败", 
          description: "无法获取导入历史，请稍后重试。", 
          variant: "destructive" 
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [router, toast]);

  // Grid Ready 事件
  const onGridReady = useCallback((params: GridReadyEvent<ImportHistoryRecord>) => {
    console.log("Grid ready");
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!rowData.length) {
    return (
      <div className="text-center py-8 flex flex-col items-center justify-center text-muted-foreground">
        <AlertTriangle className="h-10 w-10 mb-2" />
        <h3 className="font-semibold">暂无导入记录</h3>
        <p>尚未进行过Excel导入操作。</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <AgGridReact<ImportHistoryRecord>
        rowData={rowData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        gridOptions={gridOptions}
        onGridReady={onGridReady}
        rowNumbers={true}
      />
    </div>
  );
} 