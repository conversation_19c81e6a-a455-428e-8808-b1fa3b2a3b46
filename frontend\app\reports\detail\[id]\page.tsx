"use client"

import { useParams } from "next/navigation"
import { ReportDetailPage } from "@/components/domain/reports/pages/report-detail/report-detail-page"

export default function ReportDetailPageWrapper() {
  const params = useParams()
  // 确保 id 是字符串类型，处理可能的数组情况
  const id = Array.isArray(params.id) ? params.id[0] : (params.id as string)

  // 如果没有有效的 id，显示错误
  if (!id || typeof id !== 'string') {
    return <div className="text-center py-8 text-red-500">无效的标识符</div>
  }

  // 直接使用业务标识符
  return <ReportDetailPage issueNumber={id} />
}
