---
description: 指令示例:"请重新全面了解项目状态和下一步任务。"Description:规范 AI 代理如何系统性地解读 AgentReadme 文件夹中的文档，以准确理解项目上下文、定位当前任务并遵循既定协作流程的规则集。该规则集旨在通过标准化的信息获取流程，确保 AI 代理能够高效、准确地融入项目开发。
globs: 
alwaysApply: false
---
# 以下规则旨在指导 AI 代理如何利用 AgentReadme 文件夹及其内容来理解项目、定位任务并进行有效协作

Agent 描述 (Description):
*规范 AI 代理如何系统性地解读 AgentReadme 文件夹中的文档，以准确理解项目上下文、定位当前任务并遵循既定协作流程的规则集。该规则集旨在通过标准化的信息获取流程，确保 AI 代理能够高效、准确地融入项目开发。*

指令示例 (Example Invocation Command):
@readme-driven-workflow *请重新全面了解项目状态和下一步任务。*

1. **首要入口 (条件性)**: 如果 AI 代理对当前项目的文档结构、协作流程**缺乏上下文或不确定**时，**必须**首先完整阅读并深刻理解根目录下的 @AgentReadme/AgentReadme.md 文件。这是理解整个项目文档结构、文件角色、预期内容和协作流程的基础。**如果 AI 已有相关上下文，则可跳过此强制步骤**。忽略此步骤（在缺乏上下文时）将导致后续信息查找和任务定位的低效或错误。

2. **建立上下文**: 在理解 @AgentReadme/AgentReadme.md 后，根据其指引，按需查阅 @AgentReadme/planning_and_requirements/project_vision_and_roadmap.md 获取项目高层目标、方向和架构概览，以及查阅 @AgentReadme/planning_and_requirements/project_requirements.md 获取详细的功能/业务需求（如果存在且任务需要）。

3. **核心计划源**: 将 @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 文件视为查找**当前开发阶段计划、任务状态、完成历史和遗留问题**的核心信息来源。

4. **聚焦当前任务**: 在 @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 文件内部，AI 应**重点关注"区域二：当前阶段核心任务与状态"**。优先识别此区域内标记为 `[ ]` (待办) 或 `[>]` (进行中) 的任务，作为当前需要处理的主要工作。

5. **核对最新状态 (双重检查)**: 在基于区域二的任务采取具体编程行动之前：
    * **必须查阅** @AgentReadme/planning_and_requirements/ai_dev_checkpoint.md 的**最新记录**，以获取最新的微观动态和即时指令 **(如同 AI 的当前操作指令/短期记忆)**。
    * **强烈建议重新读取** @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 的**区域二和区域三**，以刷新对当前任务列表和状态的整体认知，确认任务信息未在后台更新。

6. **遵循规程**: 如果识别出的任务涉及到特定的操作流程（例如测试、部署、代码规范检查等），应查阅 @AgentReadme/guides/ 目录下的相关指南文档（如 @AgentReadme/guides/testing_guide.md）并遵循其中的步骤。

7. **信息自获取优先**: AI 应优先尝试通过阅读上述规则中指定的文档（如 @AgentReadme/AgentReadme.md, @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 等）来独立获取所需信息、理解上下文和定位任务。**避免**在没有先尝试查阅文档的情况下，直接向用户询问本应记录在文档中的信息。仅在文档信息确实缺失、信息冲突或存在严重歧义时，才向用户寻求澄清。

8. **明确"下一步"**: AI 的核心目标之一是，结合从 @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md (区域二) 和 @AgentReadme/planning_and_requirements/ai_dev_checkpoint.md (最新记录) 获取的信息，清晰地识别出**当前最合理的可执行编程任务**，并主动向用户提出或确认这个"下一步"计划。

9. **结构感知**: AI 在读取 @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 时，应利用其内部的区域划分（区域一至六）来结构化地理解信息，例如，知道区域四是历史记录，区域三是待办/暂缓等。

10. **适应性**: 虽然这些规则提供了主要框架，但 AI 仍需具备一定的灵活性，以应对文档可能存在的轻微不一致或特殊情况。
