# PaddleOCR 服务化迁移方案

## 1. 问题背景

当前系统中，`archive_processing/utils/ocr_utils.py` 中的 PaddleOCR 引擎在多进程环境下（如 Celery workers）被每个子进程独立加载。这导致了两个核心问题：

- **内存占用过高**: PaddleOCR 模型较大，每个进程都加载一份会导致总内存使用量激增（`模型大小 * 进程数`），存在严重的内存溢出（OOM）风险。此问题与 `AgentReadme/question_temporarily_stored/issue_celery_worker_oom_sigkill.md` 中记录的现象高度相关。
- **启动与扩展缓慢**: 模型初始化过程耗时较长，导致每个 Celery worker 启动缓慢，影响了服务的快速部署和弹性扩展能力。

## 2. 解决方案：OCR 微服务化

为了从根本上解决上述问题，我们提议将 PaddleOCR 功能剥离出来，封装成一个独立的、可通过网络调用的微服务。

**核心思想**:

- **服务化**: 将重量级的 OCR 功能作为一个独立的、可复用的服务运行。
- **单一实例**: OCR 模型在专用服务启动时加载一次，并常驻内存，由所有请求共享。
- **API驱动**: 主应用（Django/Celery）通过 HTTP API 请求与 OCR 服务通信，而不是在进程内直接加载模型。

**预期收益**:

- **内存高效**: 无论主应用启动多少个 worker 进程，OCR 模型在内存中仅存在一份。
- **快速启动**: 主应用 worker 无需再进行耗时的模型加载，可实现秒级启动。
- **关注点分离**: OCR 的复杂逻辑与主业务逻辑解耦，使系统架构更清晰、更易于维护和独立升级。
- **弹性伸缩**: 未来可以根据 OCR 服务的负载情况，独立地对其进行扩展。

## 3. 技术选型

- **Web 框架**: **FastAPI**。因其高性能、原生异步支持、自动生成API文档等优点，非常适合构建此类计算密集型服务的API。
- **容器化**: **Docker**。将 OCR 服务及其所有依赖打包成一个独立的、可移植的容器镜像。
- **服务编排**: **Docker Compose**。更新现有的 `docker-compose.yml` 文件，将新的 OCR 服务加入到项目服务栈中，并管理服务间的依赖和通信。
- **API 客户端**: 在主应用中，使用 **requests** 或 **httpx** 库来调用 OCR 服务的 API。

## 4. 实施阶段与计划

### 阶段一：创建独立的 OCR 微服务

1. **创建目录结构**:
    - 在项目根目录下创建新目录 `ocr_service/`。

2. **编写 FastAPI 应用 (`ocr_service/main.py`)**:
    - 使用 FastAPI 创建一个 Web 应用实例。
    - 实现一个 `POST /ocr` 的 API 端点，用于接收图片文件 (`UploadFile`)。
    - 利用 FastAPI 的应用生命周期事件 (`@app.on_event("startup")`)，在服务启动时加载 PaddleOCR 模型，并将其存储在全局变量或应用状态中。
    - `/ocr` 端点调用预加载的模型实例执行识别，并将识别结果以 JSON 格式返回。

3. **定义依赖 (`ocr_service/requirements.txt`)**:
    - 创建 `requirements.txt` 文件，列出必要的 Python 依赖，包括：

        ```依赖文件
        fastapi
        uvicorn[standard]
        python-multipart
        paddlepaddle
        paddleocr
        numpy
        Pillow
        ```

### 阶段二：容器化 OCR 服务

1. **编写 Dockerfile (`ocr_service/Dockerfile`)**:
    - 基于官方 Python 镜像 (e.g., `python:3.9-slim`)。
    - 设置工作目录。
    - 复制 `requirements.txt` 并安装依赖。
    - 复制 `ocr_service/` 下的所有应用代码到镜像中。
    - 暴露服务端口 (e.g., `EXPOSE 8001`)。
    - 定义容器启动命令: `CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]`。

### 阶段三：服务编排与集成

1. **更新 `docker-compose.yml`**:
    - 在 `services` 节点下新增一个 `ocr_service` 的定义。
    - 配置 `build` 上下文指向 `./ocr_service` 目录。
    - 为便于调试，可映射端口到宿主机，如 `ports: ["8001:8001"]`。服务间的内部通信将直接使用服务名和容器端口，例如 `http://ocr_service:8001`。
    - 设置 `restart: always` 策略。
    - 在 `web` (Django) 服务中，添加 `depends_on: [ocr_service]`，确保 OCR 服务在主应用之前启动。

### 阶段四：重构主应用代码

1. **目标文件**: `archive_processing/utils/ocr_utils.py`。
2. **重构内容**:
    - **废弃 `init_paddle_ocr`**: 此函数在主应用中不再需要，可以移除或标记为废弃。
    - **修改 `run_paddle_basic` 和 `run_paddle_enhanced`**:
        - 移除 `paddle_engine` 参数。
        - 函数内部实现将变更为：使用 `requests` 库，将传入的 `image` 对象编码后，通过 `multipart/form-data` 格式向 `http://ocr_service:8001/ocr` 发起 POST 请求。
        - 处理 API 响应，包括成功（解析 JSON 数据）和失败（处理 HTTP 错误、网络异常等）的情况。
    - **更新调用链**:
        - 修改 `perform_basic_ocr` 和 `perform_enhanced_ocr` 函数，移除获取和传递 `paddle_engine` 的逻辑。
        - 确保所有上层调用都适配新的函数签名。

## 5. 风险与预案

- **网络开销**:
  - **风险**: API 调用会引入网络延迟，相比进程内调用更慢。
  - **预案**: 对于 OCR 这种本身就耗时较长的计算密集型任务，网络延迟（通常在毫秒级）的影响可以忽略不计。此架构的收益远大于开销。
- **服务健壮性**:
  - **风险**: OCR 服务可能因故宕机或无响应。
  - **预案**: 在 `ocr_utils.py` 的客户端代码中必须实现健壮的错误处理逻辑，包括：
    - 设置合理的请求超时 (`timeout`)。
    - 考虑实现简单的重试机制（例如使用 `requests` 的 `Retry` 适配器）。
    - 记录详细的错误日志，便于快速定位问题。
    - 在 OCR 服务内部也应添加健康检查端点 (`/health`)。
- **部署复杂性**:
  - **风险**: 引入了新的服务，增加了系统部署和维护的复杂度。
  - **预案**: 借助 Docker 和 Docker Compose，新增服务的复杂性被有效管理。标准化的日志输出和健康检查是确保可维护性的关键。
