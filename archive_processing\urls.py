from django.urls import path, include
from .views import (
    ProcessingTaskViewSet, UploadedFileViewSet
)
from rest_framework.routers import DefaultRouter

# CHANGE: [2024-03-28] 添加上传视图的URL路由 #AFM-5
# CHANGE: [2025-07-30] 重构为ViewSet，统一管理processing-tasks和uploaded-files资源
app_name = 'archive_processing' # 定义应用命名空间

router = DefaultRouter()
# 注册 ProcessingTaskViewSet
router.register(r'processing-tasks', ProcessingTaskViewSet, basename='processing-task')
# 注册 UploadedFileViewSet，替代旧的PDFUploadView
router.register(r'uploaded-files', UploadedFileViewSet, basename='uploaded-file')

urlpatterns = [
    # 文件上传相关 - 使用 POST 方法 (此路由已废弃，功能由uploaded-files ViewSet的create方法提供)
    # path('upload/', PDFUploadView.as_view(), name='pdf-upload'),
        
    # 包含由router自动生成的所有URL
    path('', include(router.urls)),
]
