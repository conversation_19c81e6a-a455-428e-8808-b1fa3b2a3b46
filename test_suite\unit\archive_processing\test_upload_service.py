import os
import uuid
import tempfile
import hashlib
from unittest.mock import patch, MagicMock, mock_open, ANY

from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.exceptions import ValidationError
from django.conf import settings
from django.contrib.auth.models import User

# 根据项目的测试结构，修改导入路径
from archive_processing.services.upload_service import UploadService
from archive_processing.models import UploadedFile


class UploadServiceTestCase(TestCase):
    """测试 UploadService 的功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建一个临时目录模拟 MEDIA_ROOT/uploads
        self.temp_dir = tempfile.mkdtemp()
        self.uploads_dir = os.path.join(self.temp_dir, "uploads")
        self.media_root_patcher = patch('django.conf.settings.MEDIA_ROOT', self.temp_dir)
        self.media_root_patcher.start()
        
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='password123'
        )
        
        # 创建一个有效的 PDF 文件用于测试
        # 注意：这是一个最小的有效 PDF 文件的内容，用于测试
        self.valid_pdf_content = b'%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj 2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj 3 0 obj<</Type/Page/MediaBox[0 0 3 3]/Parent 2 0 R/Resources<<>>>>endobj\nxref\n0 4\n0000000000 65535 f\n0000000010 00000 n\n0000000053 00000 n\n0000000102 00000 n\ntrailer<</Size 4/Root 1 0 R>>\nstartxref\n149\n%%EOF\n'
        self.valid_pdf_file = SimpleUploadedFile(
            "valid_document.pdf", 
            self.valid_pdf_content, 
            content_type="application/pdf"
        )
        
        # 创建一个无效的 PDF 文件用于测试
        self.invalid_pdf_content = b'This is not a valid PDF file content.'
        self.invalid_pdf_file = SimpleUploadedFile(
            "invalid_document.pdf", 
            self.invalid_pdf_content, 
            content_type="application/pdf"
        )
        
        # 创建一个非 PDF 文件用于测试
        self.non_pdf_file = SimpleUploadedFile(
            "document.txt", 
            b"This is a text file", 
            content_type="text/plain"
        )
        
        # 保存原始的最大文件大小设置，但不在setUp中修改它
        self.orig_max_size = UploadService.MAX_FILE_SIZE
        
        # 准备大文件测试数据，但不修改MAX_FILE_SIZE
        self.large_file_content = b'0' * (self.orig_max_size + 1000)  # 比最大尺寸大1000字节
        self.large_file = SimpleUploadedFile(
            "large_document.pdf", 
            self.large_file_content, 
            content_type="application/pdf"
        )
    
    def tearDown(self):
        """测试后的清理工作"""
        # 恢复原始 MAX_FILE_SIZE
        UploadService.MAX_FILE_SIZE = self.orig_max_size
        # 停止 patch
        self.media_root_patcher.stop()
        # 可以在这里添加临时文件的清理逻辑
        
    def test_validate_file_with_valid_file(self):
        """测试验证有效文件"""
        # 场景: 提供一个有效的PDF文件
        # 预期结果: 不抛出异常，验证通过
        try:
            UploadService.validate_file(self.valid_pdf_file)
        except ValidationError:
            self.fail("validate_file() 对有效文件抛出了 ValidationError 异常!")
    
    def test_validate_file_with_invalid_extension(self):
        """测试验证扩展名无效的文件"""
        # 场景: 提供一个非PDF文件
        # 预期结果: 抛出 ValidationError 异常
        with self.assertRaises(ValidationError):
            UploadService.validate_file(self.non_pdf_file)
    
    def test_validate_file_with_large_file(self):
        """测试验证超过大小限制的文件"""
        # 场景: 提供一个超过大小限制的文件
        # 只在这个测试中临时修改MAX_FILE_SIZE
        temp_max_size = 1024  # 1KB
        UploadService.MAX_FILE_SIZE = temp_max_size
        
        # 创建一个超过临时限制的文件
        test_large_file = SimpleUploadedFile(
            "test_large.pdf", 
            b'0' * (temp_max_size + 100),  # 比临时限制大100字节
            content_type="application/pdf"
        )
        
        # 预期结果: 抛出 ValidationError 异常
        with self.assertRaises(ValidationError):
            UploadService.validate_file(test_large_file)
    
    def test_validate_file_with_empty_file(self):
        """测试验证空文件对象"""
        # 场景: 提供一个空的文件对象
        # 预期结果: 抛出 ValidationError 异常
        with self.assertRaises(ValidationError):
            UploadService.validate_file(None)
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', True)
    @patch('archive_processing.services.upload_service.fitz.open')
    def test_validate_pdf_content_with_valid_pdf(self, mock_fitz_open):
        """测试验证有效PDF内容"""
        # 模拟 PyMuPDF 的行为
        mock_doc = MagicMock()
        mock_doc.page_count = 1
        mock_doc.load_page.return_value = MagicMock()  # 模拟页面对象
        mock_doc.close = MagicMock()  # 模拟 close 方法
        mock_fitz_open.return_value = mock_doc
        
        # 场景: 提供一个有效的PDF文件路径
        # 预期结果: 不抛出异常，验证通过
        try:
            UploadService.validate_pdf_content("valid_document.pdf")
        except ValidationError:
            self.fail("validate_pdf_content() 对有效 PDF 文件抛出了 ValidationError 异常!")
        
        # 验证 fitz.open 和 close 被调用
        mock_fitz_open.assert_called_once()
        mock_doc.close.assert_called_once()
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', True)
    @patch('archive_processing.services.upload_service.fitz.open')
    def test_validate_pdf_content_with_invalid_pdf(self, mock_fitz_open):
        """测试验证无效PDF内容"""
        # 模拟 PyMuPDF 抛出异常
        mock_fitz_open.side_effect = Exception("Invalid PDF file")
        
        # 场景: 提供一个无效的PDF文件路径
        # 预期结果: 抛出 ValidationError 异常
        with self.assertRaises(ValidationError):
            UploadService.validate_pdf_content("invalid_document.pdf")
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', False)
    def test_validate_pdf_content_without_pymupdf(self):
        """测试在没有 PyMuPDF 的情况下验证 PDF 内容"""
        # 场景: PyMuPDF 不可用
        # 预期结果: 不抛出异常，但记录警告
        try:
            UploadService.validate_pdf_content("document.pdf")
        except ValidationError:
            self.fail("validate_pdf_content() 在 PyMuPDF 不可用时抛出了 ValidationError 异常!")
    
    def test_calculate_file_hash(self):
        """测试文件哈希计算功能"""
        # 创建一个临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        test_content = b'test content for hash calculation'
        temp_file.write(test_content)
        temp_file.close()
        
        try:
            # 计算预期的哈希值
            sha256_hash = hashlib.sha256()
            sha256_hash.update(test_content)
            expected_hash = sha256_hash.hexdigest()
            
            # 场景: 计算文件哈希值
            # 预期结果: 返回正确的SHA256哈希值
            actual_hash = UploadService.calculate_file_hash(temp_file.name)
            self.assertEqual(actual_hash, expected_hash, "计算的哈希值与预期不符")
        finally:
            # 清理临时文件
            os.unlink(temp_file.name)
    
    def test_calculate_file_hash_with_nonexistent_file(self):
        """测试计算不存在文件的哈希值"""
        # 场景: 提供一个不存在的文件路径
        # 预期结果: 抛出 IOError 异常
        with self.assertRaises(IOError):
            UploadService.calculate_file_hash("nonexistent_file.txt")
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', False)  # 避免 PDF 验证
    @patch('archive_processing.services.upload_service.UploadService.validate_file')  # 避免文件验证
    @patch('archive_processing.services.upload_service.UploadService.calculate_file_hash')  # 模拟哈希计算
    @patch('archive_processing.models.UploadedFile.objects.create')  # 模拟数据库创建
    @patch('os.path.exists', return_value=False) # 模拟目录不存在，强制调用makedirs
    @patch('os.makedirs')  # 模拟创建目录
    @patch('builtins.open', new_callable=mock_open)  # 模拟文件打开和写入
    @patch('uuid.uuid4') # 模拟 UUID 生成
    def test_save_uploaded_file(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_create, mock_hash, mock_validate):
        """测试保存上传文件功能"""
        # 场景: 保存有效的上传文件
        # 预期结果: 创建文件记录并返回 UploadedFile 实例
        box_number = "BOX-123"
        test_uuid = uuid.uuid4() # 生成一个预期的UUID
        mock_uuid4.return_value = test_uuid # 让服务内部也用这个UUID
        expected_saved_path = os.path.join(self.uploads_dir, f"{test_uuid}.pdf")
        
        # 模拟哈希计算返回固定值
        mock_hash.return_value = "mocked_hash_value"
        
        # 模拟创建数据库记录
        mock_uploaded_file = MagicMock(spec=UploadedFile)
        mock_uploaded_file.file_id = test_uuid
        mock_uploaded_file.original_name = "valid_document.pdf"
        mock_uploaded_file.file_size = len(self.valid_pdf_content)
        mock_uploaded_file.assigned_box_number = box_number
        mock_uploaded_file.uploader = self.test_user
        mock_uploaded_file.file_hash = "mocked_hash_value"
        mock_uploaded_file.saved_path = expected_saved_path
        mock_create.return_value = mock_uploaded_file
        
        # 调用方法
        uploaded_file_record = UploadService.save_uploaded_file(
            self.valid_pdf_file, 
            box_number, 
            self.test_user.id
        )
        
        # 验证结果
        self.assertIsInstance(uploaded_file_record, MagicMock, "返回值应该是模拟的 UploadedFile 实例")
        self.assertEqual(uploaded_file_record.original_name, "valid_document.pdf", "原始文件名不匹配")
        self.assertEqual(uploaded_file_record.file_size, len(self.valid_pdf_content), "文件大小不匹配")
        self.assertEqual(uploaded_file_record.assigned_box_number, box_number, "盒号不匹配")
        self.assertEqual(uploaded_file_record.uploader, self.test_user, "上传用户不匹配")
        self.assertIsNotNone(uploaded_file_record.file_hash, "未计算文件哈希值")
        
        # 验证目录创建 - 现在应该总是被调用，因为 mock_exists 返回 False
        mock_makedirs.assert_called_once_with(self.uploads_dir, exist_ok=True)
        
        # 验证文件写入
        mock_file.assert_called_once_with(expected_saved_path, 'wb+')
        # Verify write was called on the file handle
        mock_file().write.assert_called()
        
        # 验证哈希计算
        mock_hash.assert_called_once_with(expected_saved_path)
        
        # 验证 create 方法被调用
        mock_create.assert_called_once()
        
        # 验证文件是否已保存到磁盘（mock_exists 在这里不合适，因为我们模拟它返回False）
        # self.assertTrue(os.path.exists(uploaded_file_record.saved_path))
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', True)
    @patch('archive_processing.services.upload_service.UploadService.validate_file')  # 避免文件验证
    @patch('archive_processing.services.upload_service.UploadService.validate_pdf_content')
    @patch('archive_processing.services.upload_service.UploadService.calculate_file_hash')  # 模拟哈希计算
    @patch('archive_processing.models.UploadedFile.objects.create')  # 模拟数据库创建
    @patch('os.path.exists', return_value=False) # 模拟目录不存在
    @patch('os.makedirs')  # 模拟创建目录
    @patch('builtins.open', new_callable=mock_open)  # 模拟文件打开和写入
    @patch('uuid.uuid4') # 模拟 UUID 生成
    def test_save_uploaded_file_with_pdf_validation(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_create, mock_hash, mock_validate_pdf, mock_validate):
        """测试保存上传文件时的 PDF 验证功能"""
        # 场景: 保存有效的上传文件，并验证其 PDF 内容
        # 预期结果: 创建文件记录并返回 UploadedFile 实例
        box_number = "BOX-123"
        test_uuid = uuid.uuid4() # 生成一个预期的UUID
        mock_uuid4.return_value = test_uuid # 让服务内部也用这个UUID
        expected_saved_path = os.path.join(self.uploads_dir, f"{test_uuid}.pdf")
        
        # 模拟哈希计算返回固定值
        mock_hash.return_value = "mocked_hash_value"
        
        # 模拟创建数据库记录
        mock_uploaded_file = MagicMock(spec=UploadedFile)
        mock_uploaded_file.file_id = test_uuid
        mock_uploaded_file.original_name = "valid_document.pdf"
        mock_uploaded_file.file_size = len(self.valid_pdf_content)
        mock_uploaded_file.assigned_box_number = box_number
        mock_uploaded_file.uploader = None
        mock_uploaded_file.file_hash = "mocked_hash_value"
        mock_create.return_value = mock_uploaded_file
        
        # 调用方法
        uploaded_file_record = UploadService.save_uploaded_file(
            self.valid_pdf_file, 
            box_number
        )
        
        # 验证结果
        mock_validate_pdf.assert_called_once_with(expected_saved_path)
        self.assertEqual(uploaded_file_record.file_hash, "mocked_hash_value", "文件哈希值不匹配")
        
        # 验证文件写入
        mock_file.assert_called_once_with(expected_saved_path, 'wb+')
        mock_file().write.assert_called()
        
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', True)
    @patch('archive_processing.services.upload_service.UploadService.validate_file')  # 避免文件验证
    @patch('archive_processing.services.upload_service.UploadService.validate_pdf_content')
    @patch('archive_processing.models.UploadedFile.objects.create')  # 模拟数据库创建，但不应该被调用
    @patch('os.remove')  # 模拟删除临时文件
    @patch('os.path.exists', return_value=False) # 模拟目录不存在
    @patch('os.makedirs')  # 模拟创建目录
    @patch('builtins.open', new_callable=mock_open)  # 模拟文件打开和写入
    @patch('uuid.uuid4') # 模拟 UUID 生成
    def test_save_uploaded_file_with_invalid_pdf(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_remove, mock_create, mock_validate_pdf, mock_validate):
        """测试保存无效 PDF 文件时的行为"""
        # 场景: 保存一个 PDF 文件，但验证失败
        # 预期结果: 抛出 ValidationError 异常，不创建数据库记录，删除临时文件
        box_number = "BOX-123"
        test_uuid = uuid.uuid4() # 生成一个预期的UUID
        mock_uuid4.return_value = test_uuid # 让服务内部也用这个UUID
        expected_saved_path = os.path.join(self.uploads_dir, f"{test_uuid}.pdf")
        
        # 模拟 PDF 验证失败
        validation_error = ValidationError("Invalid PDF structure")
        mock_validate_pdf.side_effect = validation_error
        
        # 调用方法并预期异常
        with self.assertRaises(ValidationError) as context:
            UploadService.save_uploaded_file(
                self.valid_pdf_file, 
                box_number
            )
        
        # 验证结果
        # 验证抛出了正确的异常
        self.assertEqual(context.exception, validation_error, "抛出的异常与模拟的验证错误不一致")
        
        # 验证文件写入被调用
        mock_file.assert_called_once_with(expected_saved_path, 'wb+')
        mock_file().write.assert_called()
        
        # 验证文件被删除
        mock_remove.assert_called_once_with(expected_saved_path)
        
        # 验证数据库记录未创建
        mock_create.assert_not_called()
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', False)  # 避免PDF验证
    @patch('archive_processing.services.upload_service.UploadService.validate_file')  # 避免文件验证
    @patch('archive_processing.services.upload_service.UploadService.calculate_file_hash')  # 模拟哈希计算
    @patch('archive_processing.models.UploadedFile.objects.create')  # 模拟数据库创建
    @patch('django.contrib.auth.models.User.objects.get')
    @patch('os.path.exists', return_value=False) # 模拟目录不存在
    @patch('os.makedirs')  # 模拟创建目录
    @patch('builtins.open', new_callable=mock_open)  # 模拟文件打开和写入
    @patch('uuid.uuid4') # 模拟 UUID 生成
    def test_save_uploaded_file_with_nonexistent_user(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_user_get, mock_create, mock_hash, mock_validate):
        """测试提供不存在的用户ID时保存文件的行为"""
        # 场景: 保存文件时提供了不存在的用户ID
        # 预期结果: 创建文件记录，但不关联用户
        box_number = "BOX-123"
        nonexistent_user_id = 999  # 假设此ID的用户不存在
        test_uuid = uuid.uuid4() # 生成一个预期的UUID
        mock_uuid4.return_value = test_uuid # 让服务内部也用这个UUID
        expected_saved_path = os.path.join(self.uploads_dir, f"{test_uuid}.pdf")
        
        # 模拟User.objects.get抛出DoesNotExist异常
        from django.contrib.auth.models import User
        mock_user_get.side_effect = User.DoesNotExist("User does not exist")
        
        # 模拟哈希计算和数据库创建
        mock_hash.return_value = "mocked_hash_value"
        mock_uploaded_file = MagicMock(spec=UploadedFile)
        mock_uploaded_file.uploader = None  # 应该没有关联用户
        mock_create.return_value = mock_uploaded_file
        
        # 调用方法
        uploaded_file_record = UploadService.save_uploaded_file(
            self.valid_pdf_file, 
            box_number, 
            nonexistent_user_id
        )
        
        # 验证结果
        mock_user_get.assert_called_once_with(pk=nonexistent_user_id)
        
        # 验证创建记录时使用了正确的参数
        mock_create.assert_called_once()
        call_kwargs = mock_create.call_args[1]  # 获取关键字参数
        self.assertIsNone(call_kwargs.get('uploader'), "Uploader should be None when user does not exist")
    
    @patch('archive_processing.services.upload_service.PYMUPDF_AVAILABLE', False)  # 避免PDF验证
    @patch('archive_processing.services.upload_service.UploadService.validate_file')  # 避免文件验证
    @patch('archive_processing.services.upload_service.UploadService.calculate_file_hash') # 模拟哈希计算
    @patch('archive_processing.models.UploadedFile.objects.create')
    @patch('os.remove')  # 模拟删除临时文件
    @patch('os.path.exists', return_value=False) # 模拟目录不存在
    @patch('os.makedirs')  # 模拟创建目录
    @patch('builtins.open', new_callable=mock_open)  # 模拟文件打开和写入
    @patch('uuid.uuid4') # 模拟 UUID 生成
    def test_save_uploaded_file_with_db_error(self, mock_uuid4, mock_file, mock_makedirs, mock_exists, mock_remove, mock_create, mock_hash, mock_validate):
        """测试数据库错误时的行为"""
        # 场景: 写入临时文件成功，但数据库创建记录失败
        # 预期结果: 抛出数据库错误，删除临时文件
        box_number = "BOX-123"
        test_uuid = uuid.uuid4() # 生成一个预期的UUID
        mock_uuid4.return_value = test_uuid # 让服务内部也用这个UUID
        expected_saved_path = os.path.join(self.uploads_dir, f"{test_uuid}.pdf")
        
        # 模拟哈希计算
        mock_hash.return_value = "mocked_hash_value"
        
        # 模拟数据库错误
        db_error = Exception("Database connection failed")
        mock_create.side_effect = db_error
        
        # 调用方法并预期异常
        with self.assertRaises(Exception) as context:
            UploadService.save_uploaded_file(
                self.valid_pdf_file, 
                box_number
            )
        
        # 验证结果
        # 验证抛出了正确的异常
        self.assertEqual(context.exception, db_error, "抛出的异常与模拟的数据库错误不一致")
        
        # 验证文件写入被调用
        mock_file.assert_called_once_with(expected_saved_path, 'wb+')
        mock_file().write.assert_called()
        
        # 验证临时文件已被清理
        mock_exists.return_value = True  # 现在假设该文件存在，这样才能测试删除操作
        # 重新调用方法后回到异常处理中
        with self.assertRaises(Exception):
            UploadService.save_uploaded_file(
                self.valid_pdf_file, 
                box_number
            )
        mock_remove.assert_called_with(expected_saved_path) 