# Excel导入：冲突处理阶段状态机与交互优化需求 (V3.2)

**文档版本**: 1.4 (进一步优化前端UI行为和用户引导)
**创建日期**: 2025-06-01
**现在日期**： 2025-06-02
**关联 Epic/Feature**: Excel导入功能优化 - 精细化会话管理

## 1. 背景与目标

当前Excel导入流程中，冲突处理阶段的状态管理和用户并发操作的控制有待增强。本次重构旨在通过引入更细致的后端会话状态，优化前端UI驱动逻辑，以实现以下目标：

* 更精确地反映用户在冲突处理环节的每一个操作意图和会话的实际进展。
* 增强并发场景下的状态透明度和操作的独占性控制。
* 提升用户体验，使流程状态转换更自然、反馈更清晰。
* 确保最终执行导入操作的用户被准确记录。

## 2. 核心需求：状态机重构与交互调整

### 2.1. 后端会话状态 (`ImportSession.status`) 重新定义

以下为新的状态定义和转换流程，将取代部分现有状态和逻辑。

1. **`ANALYSIS_COMPLETE` (`"analyzed"`)**
    * **语义**: 后端数据分析服务已完成对Excel文件的处理，所有原始分析结果（包括冲突、差异等）已持久化。
    * **`processing_user`**: 在此状态下，`processing_user` 字段应为 `null`。
    * **自动转换**: 当分析流程完全结束且结果就绪后，后端服务 (`ImportSessionManager.analyze_session`) 应**自动地、事务性地**将会话状态从 `ANALYSIS_COMPLETE` 更新为 `CONFLICT_RESOLUTION_STARTED`。
    * **前端行为**: (此部分描述前端如何响应 `ANALYSIS_COMPLETE`) 当轮询到会话状态为 `ANALYSIS_COMPLETE` 后，前端UI步骤将切换到"处理冲突"阶段 (`derivedCurrentStep = 'confirm'`)。实际的数据获取（包括加载提示如紫色进度条）将在 `'confirm'` UI步骤的渲染逻辑中，当检测到详细分析数据 (`analysisStats`) 尚未加载时触发。

2. **`CONFLICT_RESOLUTION_STARTED` (`"conflict_resolution_started"`)** - *新状态*
    * **语义**: 系统已正式进入冲突处理阶段。这是后端状态流转的一个环节，标志着分析结果可供前端获取。
    * **`processing_user`**: 保持为 `null`。
    * **前端行为**: (此部分描述前端如何响应 `CONFLICT_RESOLUTION_STARTED`，如果能观察到的话) 后端从 `ANALYSIS_COMPLETE` 自动转换到此状态。前端的主要逻辑是在观察到 `ANALYSIS_COMPLETE` 后即进入 `'confirm'` UI步骤，并在该步骤内按需获取数据。如果前端在进入 `'confirm'` 步骤后，轮询到的状态已变为 `CONFLICT_RESOLUTION_STARTED`，且数据尚未加载，其行为与上述响应 `ANALYSIS_COMPLETE` 时一致，即在 `'confirm'` UI步骤内获取数据并显示加载提示。步骤指示器应点亮"处理冲突"。
    * **下一步**: 用户在前端UI上发起"开始/继续处理冲突"（例如点击统一的"处理冲突记录"按钮），前端**统一调用后端的 `BeginActiveConflictProcessingView` API**，尝试进入 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态。

3. **`CONFLICT_RESOLUTION_IN_PROGRESS` (`"conflict_resolution_in_progress"`)** - *新状态*
    * **语义**: 某个用户当前正在**活动地、交互地**处理此会话的冲突。这是一个**严格的独占状态**。
    * **`processing_user`**: **必须**被设置为当前发起状态转换（即通过 `BeginActiveConflictProcessingView` 成功获取处理权）的用户的ID。
    * **触发**: 当用户通过前端UI（例如点击统一的"处理冲突记录"按钮）发起操作时，前端调用 `BeginActiveConflictProcessingView` API。该API会进行原子性检查和状态更新。
    * **UI表现**:
        * 当轮询到此状态时：
            * 如果 `processing_user` 是当前登录用户：
                * 前端UI应该允许用户打开或继续在冲突处理模态框内操作。
                * **当用户实际打开并保持冲突处理模态框活动时，前端才应启动并维持心跳发送机制。** 关闭模态框（非提交）时应停止心跳。
            * 如果 `processing_user` 不是当前登录用户：
                * 此时，"处理冲突记录"按钮**仍然可以点击**。
                * 当用户点击该按钮时，前端**再次调用 `BeginActiveConflictProcessingView` API**。后端将根据原处理用户的心跳状态决定：
                    * 若原处理用户心跳未超时，API将返回特定错误（例如409 Conflict，包含当前处理者信息）。前端应据此显示一个**临时的、非阻塞式提示**（如alert、toast），例如："会话当前由用户 [processing_user.username] 处理中，请稍候。" 用户关闭提示后，UI应恢复原状，按钮仍可再次尝试点击。
                    * 若原处理用户心跳已超时，API将成功完成"接管"（当前用户成为新的`processing_user`），前端据此打开冲突处理模态框并启动心跳。
    * **心跳机制**: 处于此状态且为当前 `processing_user` 的用户，其前端应**主动定期调用心跳API** (`SessionHeartbeatView`)，以更新 `ImportSession.last_heartbeat_at`。

4. **`CONFLICT_RESOLUTION_PENDING` (`"conflict_resolution_pending"`)** - *新状态*
    * **语义**: 用户之前曾进入 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态，但后来通过 `PendActiveConflictProcessingView` API 主动关闭了冲突处理界面，或者因为其他用户通过 `BeginActiveConflictProcessingView` 被动接管了一个已超时的会话（原处理者超时）。
    * **`processing_user`**: **应被清空 (`null`)**。
    * **触发**:
        * 从 `CONFLICT_RESOLUTION_IN_PROGRESS`：当用户在前端关闭冲突处理模态框（非提交时），前端调用 `PendActiveConflictProcessingView` API。
        * 由 `BeginActiveConflictProcessingView` 内部逻辑触发：当一个用户尝试处理一个被其他用户占用的 `IN_PROGRESS` 会话，且检测到原处理用户心跳超时时，新用户接管后，原会话逻辑上经历了"无人处理"的阶段。
    * **前端UI表现**: 与 `CONFLICT_RESOLUTION_STARTED` 状态一致，允许任何有权限的用户点击统一的"处理冲突记录"按钮尝试通过 `BeginActiveConflictProcessingView` API获取处理权。

5. **`CONFLICT_RESOLUTION_COMPLETED` (`"conflict_resolution_completed"`)** - *新状态*
    * **语义**: 用户已在前端提交了所有冲突的解决方案，并且后端已成功验证、处理并应用了这些用户决策。最终待导入的数据集已准备就绪。
    * **`processing_user`**: **应被清空 (`null`)**。交互式冲突处理阶段已结束。
    * **触发**: 当用户处于 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态（且是合法的 `processing_user`）并点击"应用解决方案并导入"（或类似）按钮时，前端调用 `confirmImport` API (`ExcelImportConfirmView`)。后端在成功处理完这些决策后，设置此状态，并将发起API调用的用户记录到后续创建的 `ImportLog` 的 `import_user` 字段。
    * **自动转换**: 此状态完成后，后端 (`ImportSessionManager.confirm_import` 方法内部) 应**自动地、事务性地**将会话状态转换为 `IMPORT_QUEUED`。

6. **导入执行与完成状态**
    * `IMPORT_QUEUED` (`"queued"`)
    * `IMPORT_START` (`"import_start"`)
    * `IMPORT_IN_PROGRESS` (`"importing"`)
    * `IMPORT_COMPLETED_SUCCESSFULLY` (`"completed_successfully"`)
    * `IMPORT_COMPLETED_WITH_ERRORS` (`"completed_with_errors"`)

7. **终止与错误状态**
    * `ERROR` (`"error"`)
    * `CANCELLED` (`"cancelled"`)
    * `FINALIZED` (`"finalized"`)

8. **移除旧状态**
    * 旧的 `CONFLICT_RESOLUTION` (其值为 `"processing"`) 状态应从后端 `ImportSessionStatus` 枚举和所有相关逻辑中移除。

### 2.2. `processing_user` 和最终导入用户 (`ImportLog.import_user`) 的记录

* **`ImportSession.processing_user`**:
  * **核心职责**: 专门用于标记在 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态下，当前**活动的、独占的、正在进行交互式冲突解决操作的用户**。
  * **生命周期**:
    * 在通过 `BeginActiveConflictProcessingView` 进入 `CONFLICT_RESOLUTION_IN_PROGRESS` 时被设置。
    * 在通过 `PendActiveConflictProcessingView` (用户主动暂停) 或 `ExcelImportConfirmView` (用户提交决策) 或 `BeginActiveConflictProcessingView` (其他用户接管超时会话) 离开 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态时被清空为 `null`。
    * 在所有其他状态下，此字段应为 `null`。
* **`ImportLog.import_user`** (ForeignKey to User, nullable - 现有字段):
  * **核心职责**: 记录**最终是谁点击了"确认并开始导入"的按钮**（或等效的最终提交动作），从而触发了从冲突解决完成到实际数据导入流程的那个用户。此用户是对本次导入的最终内容和决策负责的人。
  * **设置时机**: 当用户通过 `confirmImport` API (`ExcelImportConfirmView`) 提交最终的导入决策，后端在处理此请求并将状态设置为 `CONFLICT_RESOLUTION_COMPLETED` 后，准备创建 `ImportLog` 实例时，将当前API调用者（即 `request.user`）赋给此字段。

### 2.3. "接管"逻辑的调整

* **用户指示**: "`你完全可以把接管逻辑删除。只看状态是不是正在处理，如果是则禁用模态框就行`"。此前的理解是移除用户界面的显式"安全接管"按钮。
* **实现 (被动式接管)**:
  * 后端移除了 `TakeoverSessionView` API 及其相关URL配置。
  * 前端移除了 `useExcelImportSession` hook 中的 `takeoverImport` 函数及相关UI。
  * **新的"接管"逻辑是被动式地内置在 `BeginActiveConflictProcessingView` 中**：当一个用户尝试处理一个已经是 `IN_PROGRESS` 状态的会话时，如果该会话原处理用户的心跳已超时，则当前请求用户可以成功获得处理权（即成为新的 `processing_user`，会话状态保持或更新为 `IN_PROGRESS`）。

### 2.4. 前端UI驱动、步骤指示器与轮询过渡

* **步骤指示器 (固定6步)**:
    1. "选择文件" (`select`)
    2. "上传文件" (`upload`)
    3. "数据分析" (`analyze`)
    4. "处理冲突" (`confirm`)
    5. "正在导入" (`importing`)
    6. "导入报告" (`completed`)

* **前端 `derivedCurrentStep` 映射后端状态 (主要变化点)**:
  * `'analyze'`: 对应后端 `ANALYSIS_IN_PROGRESS`。
    * 当后端为 `ANALYSIS_COMPLETE` 时，前端UI将步骤切换到 `'confirm'`。分析进度条可显示100%。
  * `'confirm'`: 对应后端 `ANALYSIS_COMPLETE`, `CONFLICT_RESOLUTION_STARTED`, `CONFLICT_RESOLUTION_IN_PROGRESS`, `CONFLICT_RESOLUTION_PENDING`。
    * 当轮询到会话状态为 `ANALYSIS_COMPLETE` (前端据此进入 `'confirm'` UI步骤) 或后续的 `CONFLICT_RESOLUTION_STARTED`, `IN_PROGRESS`, `PENDING` 时，若前端本地尚无 `analysisStats` 和 `conflictRecordsInternal`，则**必须在 `'confirm'` UI步骤的渲染逻辑中先调用 `getAnalysisResult()` 获取这些数据，并显示加载进度提示（例如紫色进度条）。**
    * **统一入口按钮 (“处理冲突记录”)**: 此按钮在会话处于以上三个状态时**始终对用户可见且原则上可点击**。
      * 点击后，前端统一调用 `BeginActiveConflictProcessingView` API。
      * **前端根据API响应进行UI更新与用户引导**:\
        * **成功拿到处理权**: 前端打开冲突处理模态框，并启动心跳机制。
        * **他人处理中且未超时**: 前端应显示**临时的、用户可关闭的提示信息**，如 "用户 [API返回的处理者名称] 正在处理中，请稍后或刷新。" UI主体不应因此永久改变。
        * **会话状态已改变/操作不再有效** (例如，会话已被取消、完成、或被他人更快地接管): API应返回明确的错误信息和最新的会话状态。前端应提示用户会话状态已更新，并**引导用户适应新的UI状态** (例如，如果会话已转到导入完成，则刷新到报告页；如果会话被他人接管，则提示并禁用操作)。
        * **其他错误**: 前端显示通用或特定的错误提示，并可能建议用户刷新。
    * **轮询到 `IN_PROGRESS` 时的UI提示**: 如果轮询到会话是 `IN_PROGRESS` 且 `processing_user` 不是当前用户，前端主要UI区域（非模态框本身）**不应主动持久显示"他人处理中"的提示来改变布局**。这类信息应在用户尝试交互（点击按钮）时通过API响应来按需、临时地提供。

* **前端轮询过渡处理 (从分析到冲突处理)**:
  * 当前端轮询时，会话状态从 `ANALYSIS_IN_PROGRESS` 更新为 `ANALYSIS_COMPLETE` (`"analyzed"`):
    1. 前端逻辑上视为分析完成，分析进度条可显示100%。
    2. `setDerivedCurrentStep('confirm')`。
    3. 在 `renderStep()` 的 `case 'confirm':` 中，如果 `analysisStats` 尚未加载，则触发 `getAnalysisResult()` API调用及相应的加载进度UI（紫色进度条）。

### 2.5. 前端交互与后端API调用 (已更新以反映被动超时和统一入口)

* **开始/继续/接管处理冲突 (统一入口)**:
  * 触发时机: 用户在前端UI点击统一的"处理冲突记录"按钮（此时会话可能处于 `STARTED`, `PENDING`, 或 `IN_PROGRESS` 状态）。
  * API: `POST /api/excel-import/session/{session_id}/begin-active-conflict-processing/` (视图: `BeginActiveConflictProcessingView`)
  * 后端原子操作 (详见 `BeginActiveConflictProcessingView` 逻辑):
    1. 获取会话，基本校验（存在、活跃、非终态）。如果会话不存在或状态不允许操作，API直接返回明确错误（如404, 400, 409）和最新的会话信息（如果存在），**前端据此引导用户（例如，提示会话不存在并建议刷新；提示状态已变更并更新UI到新状态对应界面）**。
    2. **If `status` is `STARTED` or `PENDING`**: 转为 `IN_PROGRESS`, 设置当前用户为 `processing_user`, 初始化心跳。API返回成功。
    3. **If `status` is `IN_PROGRESS`**:
        * If `processing_user` is current user: 更新心跳。API返回成功。
        * If `processing_user` is other user: 检查心跳是否超时。
            * If timed out: "接管" - 转为 `IN_PROGRESS`, 设置当前用户为 `processing_user`, 初始化心跳。API返回成功。
            * If not timed out: API返回特定错误（如409 Conflict），指明当前处理者。API返回失败。
    4. 其他状态：API返回错误。
  * 前端行为: **完全依赖API的响应**。成功则打开模态框并启动心跳；失败则根据错误类型和后端返回的会话信息显示友好提示，并**智能地引导用户刷新或适应UI到最新的会话状态**。

* **搁置冲突处理 (进入 `PENDING`)**:
  * 触发时机: 用户从 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态下（且是当前 `processing_user`），点击模态框的"取消"或关闭按钮。
  * API: `POST /api/excel-import/session/{session_id}/pend-active-conflict-processing/` (视图: `PendActiveConflictProcessingView`)
  * 后端操作: 将会话状态从 `CONFLICT_RESOLUTION_IN_PROGRESS` 更新为 `CONFLICT_RESOLUTION_PENDING`，并将 `processing_user` 清空为 `null`。

* **提交导入决策 (`confirmImport` API)**:
  * 触发时机: 用户在冲突处理模态框内（此时会话应为 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态且当前用户是 `processing_user`）点击"应用解决方案并导入"。
  * API: 调用 `confirmImport` API (`ExcelImportConfirmView`)
  * 后端操作 (在API或其调用的异步任务中):
    1. **严格校验**: 会话状态必须是 `CONFLICT_RESOLUTION_IN_PROGRESS`，且 `request.user` 必须是 `ImportSession.processing_user`。**如果校验失败，API返回明确错误，前端提示用户（例如"您已不是当前处理者，请刷新"或"会话状态已改变"）。**
    2. 验证用户决策。
    3. 将会话状态设置为 `CONFLICT_RESOLUTION_COMPLETED`。
    4. 清空 `ImportSession.processing_user` 为 `null`。
    5. 事务性地将会话状态转换为 `IMPORT_QUEUED`，并触发实际的导入任务。
    6. 在创建 `ImportLog` 时，将发起 `confirmImport` API调用的用户（即 `request.user`）作为 `import_user`。

## 3. 对现有代码的预期影响 (已更新)

* **后端**:
  * `archive_records/models.py`: (已完成) 修改 `ImportSessionStatus` 枚举；更新 `ImportSession._VALID_TRANSITIONS`。
  * `archive_records/views.py`:
    * 移除 `TakeoverSessionView`。
    * **大幅重构 `BeginActiveConflictProcessingView`** 以实现统一入口和被动式超时接管逻辑。
    * 实现 `PendActiveConflictProcessingView`。
    * 修改 `ExcelImportConfirmView` (或其调用的服务) 的前置状态校验。
    * 简化 `GetActiveImportSessionView`，使其仅如实返回状态。
  * `archive_records/services/import_session_manager.py`:
    * 修改 `confirm_import` 方法的前置状态校验逻辑和 `ImportLog.import_user` 赋值。
    * 确保 `analyze_session` 方法在 `ANALYSIS_COMPLETE` 后自动、事务性地转换为 `CONFLICT_RESOLUTION_STARTED`。
    * (可选) 可能需要辅助方法被 `BeginActiveConflictProcessingView` 调用以处理心跳超时和状态转换的原子操作。
  * `archive_records/tasks.py`:
    * **移除 `process_heartbeat_timeouts_task` Celery任务。**
    * `process_excel_import_confirmation_task` 确保 `ImportLog.import_user` 被正确传递和使用，并在任务开始时正确处理会话状态 (`IMPORT_QUEUED` -> `IMPORT_START`) 及 `processing_user` (应为None或系统用户)。
* **前端**: (如原文所述，但需适配新的 `BeginActiveConflictProcessingView` 统一入口行为)

## 4. 未明确但需考虑的点 (已更新)

* **心跳机制**: 对于 `CONFLICT_RESOLUTION_IN_PROGRESS` 状态，前端应主动发送心跳。**超时判断和处理将由 `BeginActiveConflictProcessingView` 在用户尝试操作时被动执行。**
* **错误处理与用户引导**: (进一步强调) 前端必须能够处理来自后端API的各种错误响应（包括会话状态已变更、不再是处理者、操作不被允许等），并向用户提供**清晰、友好、具有引导性的提示信息**，帮助用户理解当前状况并进行正确操作（如刷新页面、重新开始流程等）。避免让用户基于过时的UI状态进行无效操作。
* **权限**: (无变化) 谁有权限将搁置的 (`PENDING`) 会话重新激活为 `IN_PROGRESS`？目前设计为任何有权限访问导入功能的用户都可以尝试通过 `BeginActiveConflictProcessingView`。

## 5. 实施计划建议 (已更新)

**阶段〇：准备工作** (已基本完成)

1. 模型字段确认与添加 (后端)
2. API设计与契约定义 (前后端共同，基于本次更新的视图逻辑)

**阶段一：后端核心逻辑修改** (已大部分完成，待整合 `BeginActiveConflictProcessingView` 的新逻辑)

1. 更新 `ImportSession` 模型 (models.py - 已完成)
2. **实现状态自动转换逻辑** (services/import_session_manager.py):
    * `ANALYSIS_COMPLETE` -> `CONFLICT_RESOLUTION_STARTED` (在 `analyze_session` 末尾实现 - 已完成)
    * `CONFLICT_RESOLUTION_COMPLETED` -> `IMPORT_QUEUED` (在 `confirm_import` 内部实现 - 已完成)
3. **重构/实现API视图** (views.py):
    * **重构 `BeginActiveConflictProcessingView`** (核心：统一入口、被动超时接管)。
    * 实现 `PendActiveConflictProcessingView` (已完成)。
4. **修改现有API视图/服务** (views.py, services/import_session_manager.py):
    * `ExcelImportConfirmView` / `ImportSessionManager.confirm_import`: 调整前置状态校验 (已完成)。
    * 移除 `TakeoverSessionView` (已完成)。
    * 简化 `GetActiveImportSessionView` (已完成)。
5. **移除心跳处理Celery任务** (`tasks.py` - 已完成)。

**阶段二：前端Hook层适配** (`frontend/hooks/useExcelImportSession.ts`)

1. 更新 `ImportSessionStatusEnum`。
2. 移除 `takeoverImport` 相关逻辑。
3. 确保 `beginActiveConflictProcessing` (或类似名称的函数) 调用 `BeginActiveConflictProcessingView` API，并能**处理其所有可能的成功/失败响应及附带的会话数据/错误信息**，以供UI层决策。
4. 保留 `pendActiveConflictProcessing` API调用函数。
5. **心跳启动/停止逻辑**: 确保心跳仅在冲突处理模态框为当前用户打开并活动时启动，关闭时停止。
6. 调整 `confirmImport` API调用，增加对API调用失败（如因状态校验失败）的处理和用户提示。
7. 调整 `fetchSystemActiveSession` (或轮询逻辑) 以正确处理从 `GetActiveImportSessionView` 获取的会话信息，并用于更新UI状态的显示。

**阶段三：前端UI组件适配与交互实现** (`frontend/components/.../excel-import-with-conflict-resolution.tsx`)

1. **`useEffect` (处理 `activeSessionInfo` 变化)**:
    * 适配新的后端状态名。
    * 主要用于数据展示和步骤指示器更新。
2. **`renderStep()` 函数修改**:
    * **`case 'confirm':`** (对应"处理冲突"步骤):
        * "处理冲突记录"按钮：**始终保持可点击状态**（除非整个会话已进入终态或后续不可操作阶段，由外层逻辑控制）。
        * 点击按钮后：调用hook中的 `beginActiveConflictProcessing`。
            * **API成功响应**: 打开冲突处理模态框，启动心跳。
            * **API失败响应**: **根据后端返回的错误类型和（可能有的）最新会话数据，向用户显示清晰的、非阻塞的提示（如toast, inline message）**。例如：
                * "用户X正在处理中，请稍后再试。" (可选择提供刷新按钮)
                * "会话已被取消，请重新上传文件。" (可选择提供导航按钮)
                * "您的操作与当前会话状态冲突，已自动刷新。" (并实际刷新数据)
        * **UI提示**: **移除**在 `IN_PROGRESS` 状态下、非处理用户轮询时，主要UI区域持久显示"他人处理中"的提示。此类信息应仅在用户尝试交互并收到API反馈时按需、临时显示。
3. **关闭冲突处理模态框 (非提交)**:
    * 调用 `pendActiveConflictProcessing` hook 函数。
4. **提交导入决策 (从模态框)**:
    * 调用 `confirmImport` hook 函数。API调用失败时（例如，因状态已改变或不再是处理者），模态框应给出明确提示，并**可能需要关闭模态框、强制刷新会话数据并引导用户到正确的UI状态**。
5. **步骤指示器**: (无变化)

**阶段四：全面测试与优化** (无变化)

1. 单元测试
2. 集成测试
3. 用户体验 (UX) 测试
4. 性能与日志
