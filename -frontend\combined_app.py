import streamlit as st
import requests
import os
import pandas as pd
from datetime import datetime
from requests.auth import HTTPBasicAuth

# API基础URL配置
DJANGO_API_BASE_URL = os.getenv("BACKEND_API_URL", "http://web:8000/api")

# 档案流管理系统API地址
# 注意：旧的import-excel端点已被弃用，现在使用新的两阶段导入流程
EXCEL_IMPORT_ANALYZE_URL = f"{DJANGO_API_BASE_URL}/archive-records/excel-import/analyze/"
EXCEL_IMPORT_CONFIRM_URL = f"{DJANGO_API_BASE_URL}/archive-records/excel-import/confirm/"
PDF_UPLOAD_URL = f"{DJANGO_API_BASE_URL}/archive-processing/upload/"
LEDGER_URL = f"{DJANGO_API_BASE_URL}/archive-records/list/"

# 报告发放系统API地址
ISSUE_FORM_URL = f"{DJANGO_API_BASE_URL}/report-issuing/issue-forms/"
ISSUE_RECORD_URL = f"{DJANGO_API_BASE_URL}/report-issuing/issue-records/"
AVAILABLE_RECORDS_URL = (
    f"{DJANGO_API_BASE_URL}/report-issuing/issue-forms/available_records/"
)


# 辅助函数：处理API请求
def api_request(url, method="get", data=None, files=None, params=None):
    """统一处理API请求，包括错误处理和响应解析"""
    # CHANGE: [2025-04-24] 添加认证信息，使用管理员账号
    # 在debug模式下，无需认证即可访问API，但为确保可靠性，我们依然添加认证
    auth = HTTPBasicAuth("admin", "!@#$qwer")  # 修改默认密码为!@#$qwer

    headers = {}

    try:
        if method.lower() == "get":
            response = requests.get(url, headers=headers, params=params, auth=auth)
        elif method.lower() == "post":
            response = requests.post(
                url, headers=headers, json=data, files=files, auth=auth
            )
        elif method.lower() == "put":
            response = requests.put(url, headers=headers, json=data, auth=auth)
        elif method.lower() == "patch":
            response = requests.patch(url, headers=headers, json=data, auth=auth)
        elif method.lower() == "delete":
            response = requests.delete(url, headers=headers, auth=auth)

        response.raise_for_status()
        return response.json() if response.content else None
    except requests.exceptions.RequestException as e:
        st.error(f"API请求错误: {e}")
        if hasattr(e, "response") and e.response is not None:
            try:
                error_detail = e.response.json()
                st.error(f"服务器返回: {error_detail}")
            except:
                st.error(f"服务器状态码: {e.response.status_code}")
        return None


# 页面配置
st.set_page_config(page_title="档案流管理与报告发放系统", layout="wide")
st.title("档案流管理与报告发放系统")

# 创建会话状态变量 - 用于台账发放管理
if "selected_records" not in st.session_state:
    st.session_state.selected_records = []
if "issue_type" not in st.session_state:
    st.session_state.issue_type = "single"  # 默认发放1份
if "filtered_records" not in st.session_state:
    st.session_state.filtered_records = None
if "show_create_form" not in st.session_state:
    st.session_state.show_create_form = False
if "create_form_data" not in st.session_state:
    st.session_state.create_form_data = {
        "issue_date": datetime.now().strftime("%Y-%m-%d"),
        "receiver_name": "",
        "receiver_unit": "",
        "receiver_phone": "",
        "notes": "",
    }
# 待发放列表 - 累积添加不同发放类型的记录
if "pending_issue_records" not in st.session_state:
    st.session_state.pending_issue_records = (
        []
    )  # 格式: [{id: xx, issue_type: xx, record_data: {}}]
# 记录ID到发放类型的映射，用于固定记录的发放类型，避免随筛选切换而变化
if "record_issue_type_map" not in st.session_state:
    st.session_state.record_issue_type_map = {}  # 格式: {record_id: issue_type}

# 添加到session_state初始化部分
if "bypass_filter" not in st.session_state:
    st.session_state.bypass_filter = False

# 添加到session_state初始化
if "records_df" not in st.session_state:
    st.session_state.records_df = None

# 在会话状态初始化部分，添加选择更改回调需要的状态变量
if "checkbox_changed" not in st.session_state:
    st.session_state.checkbox_changed = False

# 定义回调函数 - 用于台账发放管理
def on_issue_type_change():
    """当发放类型改变时，仅更新issue_type，不清空记录"""
    # 注意：只修改issue_type，不清空filtered_records，这样可以保持表格不刷新
    # 用户需要点击加载按钮时才会根据新的类型加载数据
    pass  # 不执行任何操作，只是作为回调占位符


def toggle_create_form():
    """切换创建发放单表单的显示状态"""
    st.session_state.show_create_form = not st.session_state.show_create_form


def load_available_records():
    """根据选择的发放类型加载可用的档案记录"""
    issue_type = st.session_state.issue_type
    
    # 判断是否使用绕过模式
    if st.session_state.bypass_filter:
        st.warning("⚠️ 绕过模式已启用，直接从台账加载数据，忽略发放条件")
        
        try:
            # 直接从台账API加载所有数据
            params = {"page_size": 100}
            response = requests.get(
                LEDGER_URL, params=params, auth=HTTPBasicAuth("admin", "!@#$qwer")
            )
            
            st.write(f"请求URL: {response.url}")
            
            if response.status_code == 200:
                records = response.json()
                
                with st.expander("台账API响应详情", expanded=False):
                    st.json(records)
                
                if (
                    records
                    and isinstance(records, dict)
                    and "results" in records
                ):
                    records_list = records["results"]
                    st.session_state.filtered_records = records_list
                    st.success(f"【绕过模式】从台账加载了 {len(records_list)} 条记录")
                elif isinstance(records, list):
                    st.session_state.filtered_records = records
                    st.success(f"【绕过模式】从台账加载了 {len(records)} 条记录")
                else:
                    st.warning("台账API返回了意外的数据格式")
                    st.session_state.filtered_records = []
            else:
                st.error(f"台账API请求失败: {response.status_code}")
                st.session_state.filtered_records = []
                
        except Exception as e:
            st.error(f"绕过模式加载记录时出错: {str(e)}")
            import traceback
            st.code(traceback.format_exc())
            st.session_state.filtered_records = []
        
        return
    
    # 正常模式 - 使用发放API
    params = {"issue_type": issue_type, "page_size": 100}  # 使用page_size代替limit
    
    # 显示查询参数
    st.write(f"查询参数: {params}")

    with st.spinner(f"正在加载符合'{issue_type}'条件的档案记录..."):
        try:
            # 直接使用requests库调用，获取更详细的响应信息
            auth = HTTPBasicAuth("admin", "!@#$qwer")
            response = requests.get(AVAILABLE_RECORDS_URL, params=params, auth=auth)
            
            # 显示请求URL（帮助调试）
            st.write(f"请求URL: {response.url}")
            
            # 检查响应状态码
            if response.status_code != 200:
                st.error(f"API请求错误: {response.status_code}")
                try:
                    error_detail = response.json()
                    st.error(f"服务器返回: {error_detail}")
                except Exception:
                    st.error(f"无法解析错误响应: {response.text}")
                st.session_state.filtered_records = []
                return
            
            # 解析响应数据
            try:
                records = response.json()
                st.success("成功获取API响应")
                
                # 显示响应内容（调试用）
                with st.expander("API响应详情（点击展开）"):
                    st.json(records)
                
                # 解析不同格式的响应
                if records and isinstance(records, dict) and "results" in records:
                    # 分页响应格式
                    records_list = records["results"]
                    total_count = records.get("count", len(records_list))
                    st.session_state.filtered_records = records_list
                    st.success(
                        f"找到 {total_count} 条符合条件的档案记录，显示 {len(records_list)} 条"
                    )
                
                elif isinstance(records, list):
                    # 列表响应格式
                    st.session_state.filtered_records = records
                    st.success(f"找到 {len(records)} 条符合条件的档案记录")
                
                elif isinstance(records, dict) and "data" in records:
                    # 嵌套数据响应格式
                    if isinstance(records["data"], list):
                        st.session_state.filtered_records = records["data"]
                        st.success(f"找到 {len(records['data'])} 条符合条件的档案记录")
                else:
                    # 意外格式
                    st.warning("API返回了意外的数据格式，无法解析为档案记录列表")
                    st.session_state.filtered_records = []
                
                # 如果没有找到记录
                if not st.session_state.filtered_records:
                    st.warning(f"没有找到符合'{issue_type}'条件的可发放档案记录")
                    
                    # 提供建议
                    st.info(
                        "提示: 开启「高级选项」中的「绕过后端发放条件」功能，可以直接使用台账数据，绕开发放条件限制"
                    )
                
            except ValueError as e:
                st.error(f"解析API响应时出错: {str(e)}")
                st.error(f"响应内容: {response.text[:500]}...")
                st.session_state.filtered_records = []
                
        except Exception as e:
            st.error(f"加载记录时发生错误: {str(e)}")
            import traceback
            st.code(traceback.format_exc())
            st.session_state.filtered_records = []


# 创建主标签页
main_tabs = st.tabs(["档案流管理", "报告发放"])

# ============ 档案流管理标签页 ============
with main_tabs[0]:
    st.header("档案流管理")

    # 创建四个标签页，增加了"台账报告发放"标签
    tabs = st.tabs(["Excel台账导入", "PDF处理", "档案台账查看", "台账报告发放"])

    # --- 1. Excel 台账导入 ---
    with tabs[0]:
        st.subheader("Excel 台账导入")
        uploaded_excel_file = st.file_uploader(
            "选择 Excel 台账文件 (.xls 或 .xlsx)",
            type=["xlsx", "xls"],
            key="excel_uploader",
        )

        if uploaded_excel_file is not None:
            st.write(f"已选择文件: {uploaded_excel_file.name}")
            if st.button("上传并导入台账", key="import_excel_button"):
                files = {
                    "file": (
                        uploaded_excel_file.name,
                        uploaded_excel_file.getvalue(),
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    )
                }

                # 发送API请求（使用新的两阶段导入流程）
                try:
                    st.info("正在启动分析（新的两阶段导入流程）...")
                    st.warning("⚠️ 注意：此处使用的是旧版UI，建议使用新的前端界面进行两阶段Excel导入")
                    response = requests.post(EXCEL_IMPORT_ANALYZE_URL, files=files)
                    response.raise_for_status()

                    response_data = response.json()
                    if response_data.get("success"):
                        st.success(
                            f"分析启动成功: {response_data.get('data', {}).get('message', '无详细信息')}"
                        )
                        st.info("分析完成后请到前端界面进行冲突处理和最终确认")
                        st.json(response_data.get("data", {}))
                    else:
                        st.error(
                            f"后端返回分析失败: {response_data.get('error', '未知错误')}"
                        )
                        if isinstance(response_data.get("error"), dict):
                            st.json(response_data["error"])

                except requests.exceptions.RequestException as e:
                    st.error(f"分析请求失败: {e}")
                    if e.response is not None:
                        st.error(f"后端服务器状态码: {e.response.status_code}")
                        try:
                            error_detail = e.response.json()
                            st.json(error_detail)
                        except ValueError:
                            st.text(e.response.text)
                    else:
                        st.error(f"无法连接到后端服务: {EXCEL_IMPORT_ANALYZE_URL}")
                except Exception as e:
                    st.error(f"处理分析时发生未知错误: {e}")

    # --- 2. PDF 文件上传与处理 ---
    with tabs[1]:
        st.subheader("PDF 文件上传与处理")

        uploaded_pdf_file = st.file_uploader(
            "选择 PDF 文件 (.pdf)", type=["pdf"], key="pdf_uploader"
        )
        assigned_box_number = st.text_input(
            "指定物理盒号", key="box_number_input", placeholder="例如: BOX-001"
        )

        if uploaded_pdf_file is not None and assigned_box_number:
            st.write(f"已选择 PDF 文件: {uploaded_pdf_file.name}")
            st.write(f"指定盒号: {assigned_box_number}")

            if st.button("上传 PDF 并启动处理", key="upload_pdf_button"):
                files = {
                    "file": (
                        uploaded_pdf_file.name,
                        uploaded_pdf_file.getvalue(),
                        "application/pdf",
                    )
                }
                data = {"assigned_box_number": assigned_box_number}

                try:
                    st.info("正在发送 PDF 上传请求...")
                    response = requests.post(PDF_UPLOAD_URL, files=files, data=data)
                    response.raise_for_status()

                    response_data = response.json()
                    if response_data.get("success"):
                        st.success(
                            f"上传成功: {response_data.get('data', {}).get('message', '无详细信息')}"
                        )
                        st.write(
                            f"文件 ID: {response_data.get('data', {}).get('file_id')}"
                        )
                        st.write(
                            f"任务 ID: {response_data.get('data', {}).get('task_id')}"
                        )
                        st.write(
                            f"任务初始状态: {response_data.get('data', {}).get('task_status')}"
                        )
                        st.info("实际应用中，后台任务会自动执行处理、归档和记录更新。")
                    elif response_data.get("warning"):
                        st.warning(
                            f"部分成功: {response_data.get('data', {}).get('message', '无详细信息')}"
                        )
                        st.write(
                            f"文件 ID: {response_data.get('data', {}).get('file_id')}"
                        )
                        st.json(response_data.get("warning"))
                    else:
                        st.error(
                            f"后端返回上传失败: {response_data.get('error', '未知错误')}"
                        )
                        if isinstance(response_data.get("error"), dict):
                            st.json(response_data["error"])

                except requests.exceptions.RequestException as e:
                    st.error(f"PDF 上传请求失败: {e}")
                    if e.response is not None:
                        st.error(f"后端服务器状态码: {e.response.status_code}")
                        try:
                            error_detail = e.response.json()
                            st.json(error_detail)
                        except ValueError:
                            st.text(e.response.text)
                    else:
                        st.error(f"无法连接到后端服务: {PDF_UPLOAD_URL}")
                except Exception as e:
                    st.error(f"处理 PDF 上传时发生未知错误: {e}")

    # --- 3. 档案台账显示 ---
    with tabs[2]:
        st.subheader("档案台账查看")

        if "ledger_data" not in st.session_state:
            st.session_state.ledger_data = None

        if st.button("刷新台账数据", key="fetch_ledger_button"):
            try:
                st.info("正在获取台账数据...")
                params = {"page_size": 1000}
                response = requests.get(LEDGER_URL, params=params)
                response.raise_for_status()

                fetched_data = response.json()
                if isinstance(fetched_data, dict) and "results" in fetched_data:
                    data_list = fetched_data["results"]
                    django_service_base_url = "http://localhost:8000"
                    for item in data_list:
                        if item.get("archive_url") and item["archive_url"].startswith(
                            "/media/"
                        ):
                            relative_url = item["archive_url"]
                            item["archive_url"] = (
                                f"{django_service_base_url}{relative_url}"
                            )

                    st.session_state.ledger_data = data_list
                    st.success(
                        f"台账数据获取成功！(共 {fetched_data.get('count', len(data_list))} 条)"
                    )
                elif isinstance(fetched_data, list):
                    django_service_base_url = "http://localhost:8000"
                    for item in fetched_data:
                        if item.get("archive_url") and item["archive_url"].startswith(
                            "/media/"
                        ):
                            relative_url = item["archive_url"]
                            item["archive_url"] = (
                                f"{django_service_base_url}{relative_url}"
                            )
                    st.session_state.ledger_data = fetched_data
                    st.success(f"台账数据获取成功！(共 {len(fetched_data)} 条)")
                else:
                    st.warning("获取到的数据格式未知，无法展示。")
                    st.json(fetched_data)
                    st.session_state.ledger_data = None

            except requests.exceptions.RequestException as e:
                st.error(f"获取台账数据失败: {e}")
                if e.response is not None:
                    st.error(f"后端服务器状态码: {e.response.status_code}")
                    try:
                        error_detail = e.response.json()
                        st.json(error_detail)
                    except ValueError:
                        st.text(e.response.text)
                else:
                    st.error(f"无法连接到后端服务: {LEDGER_URL}")
                st.session_state.ledger_data = None
            except Exception as e:
                st.error(f"处理台账数据时发生未知错误: {e}")
                st.session_state.ledger_data = None

        if st.session_state.ledger_data is not None:
            if len(st.session_state.ledger_data) > 0:
                st.dataframe(
                    st.session_state.ledger_data,
                    column_config={
                        "archive_url": st.column_config.LinkColumn(
                            "档案链接",
                            help="点击打开归档的 PDF 文件 (如果存在)",
                            display_text="打开链接",
                        )
                    },
                )
            else:
                st.info("当前台账中没有记录。")
        else:
            st.info("点击 '刷新台账数据' 按钮来加载记录。")

    # --- 4. 台账报告发放 ---
    with tabs[3]:
        st.subheader("档案台账发放管理")

        # 先确保已加载档案台账数据
        if (
            "ledger_data" not in st.session_state
            or st.session_state.ledger_data is None
        ):
            st.warning("请先在「档案台账查看」标签页加载台账数据")
            if st.button("立即加载台账数据", key="load_ledger_for_issue"):
                try:
                    st.info("正在获取台账数据...")
                    params = {"page_size": 1000}
                    response = requests.get(LEDGER_URL, params=params)
                    response.raise_for_status()

                    fetched_data = response.json()
                    if isinstance(fetched_data, dict) and "results" in fetched_data:
                        data_list = fetched_data["results"]
                        django_service_base_url = "http://localhost:8000"
                        for item in data_list:
                            if item.get("archive_url") and item[
                                "archive_url"
                            ].startswith("/media/"):
                                relative_url = item["archive_url"]
                                item["archive_url"] = (
                                    f"{django_service_base_url}{relative_url}"
                                )

                        st.session_state.ledger_data = data_list
                        st.success(
                            f"台账数据获取成功！(共 {fetched_data.get('count', len(data_list))} 条)"
                        )
                        st.rerun()
                    elif isinstance(fetched_data, list):
                        django_service_base_url = "http://localhost:8000"
                        for item in fetched_data:
                            if item.get("archive_url") and item[
                                "archive_url"
                            ].startswith("/media/"):
                                relative_url = item["archive_url"]
                                item["archive_url"] = (
                                    f"{django_service_base_url}{relative_url}"
                                )
                        st.session_state.ledger_data = fetched_data
                        st.success(f"台账数据获取成功！(共 {len(fetched_data)} 条)")
                        st.rerun()
                    else:
                        st.warning("获取到的数据格式未知，无法展示。")
                        st.json(fetched_data)
                except Exception as e:
                    st.error(f"获取台账数据失败: {e}")

        # 已有台账数据，显示选择界面
        else:
            # 发放类型选择
            st.write("### 步骤1: 选择发放类型")
            issue_type = st.radio(
                "选择发放类型",
                options=["发放1份", "发放全部"],
                horizontal=True,
                index=0 if st.session_state.issue_type == "single" else 1,
                key="issue_type_radio_filter",
                on_change=on_issue_type_change,
            )

            # 更新会话状态中的发放类型
            st.session_state.issue_type = (
                "single" if issue_type == "发放1份" else "all_remaining"
            )

            # 发放类型说明
            if st.session_state.issue_type == "single":
                st.info(
                    "📄 **发放1份**: 仅筛选可执行'第一次发放'的档案记录，系统会自动发放1份。选择此类型不会显示已有'第一次发放'记录的档案。"
                )
            else:
                st.info(
                    "📦 **发放全部**: 仅筛选可执行'剩余份数发放'的档案记录，系统会自动计算应发放的剩余份数（至少1份）。选择此类型不会显示已发满所有份数的档案。"
                )

            # 添加高级选项（折叠面板）
            with st.expander("高级选项", expanded=False):
                st.write("##### 调试与排查工具")

                # 添加绕过过滤条件的选项
                bypass_filter = st.checkbox(
                    "绕过后端发放条件（直接使用台账数据）",
                    value=st.session_state.bypass_filter,
                    key="bypass_filter_checkbox",
                )

                # 更新会话状态
                if bypass_filter != st.session_state.bypass_filter:
                    st.session_state.bypass_filter = bypass_filter
                    st.success(
                        "已更新发放条件设置"
                        + ("【已绕过】" if bypass_filter else "【标准模式】")
                    )

                # 如果绕过模式已启用，显示警告
                if st.session_state.bypass_filter:
                    st.warning(
                        """
                    ⚠️ **注意**: 绕过模式已启用
                    
                    - 系统将直接从台账加载数据，而不通过发放API
                    - 此模式下可能显示不符合发放条件的记录
                    - 仅用于测试，不建议在生产环境使用
                    """
                    )

                # 解释可能的问题
                st.info(
                    """
                **常见问题排查:**
                1. **无可发放档案**: 如果系统中没有符合条件的档案记录，结果会为空
                2. **发放状态问题**: 确保档案记录处于可发放状态
                3. **发放份数限制**: 檔案的总发放份数设置可能限制了结果
                """
                )

                # API端点检查
                st.write("**API端点配置:**")
                api_base = st.text_input("修改API基础地址", value=DJANGO_API_BASE_URL)
                if api_base != DJANGO_API_BASE_URL:
                    st.warning(f"API基础地址已临时修改为: {api_base}")

                # 创建一个临时测试查询
                st.write("**自定义测试查询:**")

                test_issue_type = st.selectbox(
                    "发放类型参数",
                    options=["single", "all_remaining", "all"],
                    index=0 if st.session_state.issue_type == "single" else 1,
                )

                test_other_params = st.text_input(
                    "附加参数 (格式: param1=value1&param2=value2)", value=""
                )

                # 执行测试查询按钮
                if st.button("执行测试查询", key="test_custom_query"):
                    test_url = (
                        f"{api_base}/report-issuing/issue-forms/available_records/"
                    )
                    params = {"issue_type": test_issue_type}

                    # 处理附加参数
                    if test_other_params:
                        for param_pair in test_other_params.split("&"):
                            if "=" in param_pair:
                                key, value = param_pair.split("=", 1)
                                params[key.strip()] = value.strip()

                    st.write(f"测试URL: {test_url}")
                    st.write(f"测试参数: {params}")

                    try:
                        response = requests.get(
                            test_url,
                            params=params,
                            auth=HTTPBasicAuth("admin", "!@#$qwer"),
                        )

                        st.write(f"完整URL: {response.url}")
                        st.write(f"状态码: {response.status_code}")

                        if response.status_code == 200:
                            result = response.json()

                            # 使用代码块而不是嵌套expander显示响应
                            st.write("#### 测试查询响应:")
                            st.code(
                                str(result)[:1000]
                                + ("..." if len(str(result)) > 1000 else ""),
                                language="json",
                            )

                            # 添加更多响应详情按钮
                            if st.button("显示完整响应", key="show_full_response"):
                                st.json(result)

                            if isinstance(result, dict) and "results" in result:
                                if result["results"]:
                                    st.success(f"找到 {len(result['results'])} 条记录!")

                                    # 提供选项使用这些结果
                                    if st.button("使用这些测试结果"):
                                        st.session_state.filtered_records = result[
                                            "results"
                                        ]
                                st.rerun()
                            else:
                                st.warning("API返回了空结果列表")
                        elif isinstance(result, list):
                            if result:
                                st.success(f"找到 {len(result)} 条记录!")

                                # 提供选项使用这些结果
                                if st.button("使用这些测试结果"):
                                    st.session_state.filtered_records = result
                                    st.rerun()
                            else:
                                st.warning("API返回了空列表")
                        else:
                            st.error("API返回了意外的数据格式")
                    except Exception as e:
                        st.error(f"测试查询出错: {str(e)}")
                        import traceback

                        st.code(traceback.format_exc())

            # 创建发放单的表单（点击"创建发放单"按钮时展示）
            if st.session_state.get("show_create_form", False):
                st.subheader("创建发放单")
                with st.form("issue_form_basic_info"):
                    # 添加条目级领取人说明
                    st.info(
                        """
    **领取人信息说明**:
    1. 这里填写的领取人信息作为发放单的全局默认值
    2. 对于每个条目，系统会：
       - 自动判断是"第一次"还是"第二次"发放
       - 将领取人信息复制到对应档案记录的first_*/second_*字段
    3. 条目也可以单独设置领取人信息（发放单详情页）
"""
                    )

                    col1, col2 = st.columns(2)
                    with col1:
                        issue_date = st.date_input("发放日期", value=datetime.now())
                        receiver_name = st.text_input(
                            "领取人姓名", placeholder="请输入领取人姓名"
                        )
                    with col2:
                        receiver_unit = st.text_input(
                            "领取人单位", placeholder="请输入领取人单位"
                        )
                        receiver_phone = st.text_input(
                            "联系电话", placeholder="请输入联系电话"
                        )
                    notes = st.text_area("备注", placeholder="可选，请输入备注信息")

                    # 显示待发放记录数量
                    st.info(
                        f"将创建包含 {len(st.session_state.pending_issue_records)} 条记录的发放单"
                    )

                    submit_button = st.form_submit_button("确认创建发放单")
                    if submit_button:
                        # 检查必填项
                        if not receiver_name or not receiver_unit:
                            st.error("领取人姓名和领取单位为必填项！")
                        elif not st.session_state.pending_issue_records:
                            st.error("待发放列表为空，请先添加记录！")
                        else:
                            # 保存表单数据 - 修复日期格式
                            form_data = {
                                "issue_date": issue_date.strftime(
                                    "%Y-%m-%dT00:00:00Z"
                                ),  # 使用ISO格式
                                "receiver_name": receiver_name,
                                "receiver_unit": receiver_unit,
                                "receiver_phone": receiver_phone,
                                "notes": notes,
                            }

                            # 创建发放单
                            with st.spinner("正在创建发放单..."):
                                # 调试显示请求数据
                                if st.checkbox(
                                    "显示请求数据", key="debug_form_data", value=False
                                ):
                                    st.write("请求数据:")
                                    st.json(form_data)

                                result = api_request(
                                    ISSUE_FORM_URL, method="post", data=form_data
                                )
                                if result and "id" in result:
                                    form_id = result["id"]

                                    # 准备批量添加条目的数据
                                    items_data = []
                                    for (
                                        record
                                    ) in st.session_state.pending_issue_records:
                                        items_data.append(
                                            {
                                                "archive_record": record["id"],
                                                "issue_type": record["issue_type"],
                                                "remarks": notes,
                                            }
                                        )

                                    # 批量添加条目
                                    batch_url = f"{ISSUE_FORM_URL}{form_id}/add_items/"
                                    batch_result = api_request(
                                        batch_url,
                                        method="post",
                                        data={"items": items_data},
                                    )

                                    if batch_result and "added" in batch_result:
                                        st.success(
                                            f"发放单创建成功！添加了 {batch_result['added']} 条记录。"
                                        )
                                        st.info(f"发放单ID: {form_id}")

                                        # 重置状态
                                        st.session_state.pending_issue_records = []
                                        st.session_state.show_create_form = False
                                        st.rerun()
                                    else:
                                        st.error("添加档案记录到发放单失败")
                                else:
                                    st.error("创建发放单失败，返回内容异常：")
                                    st.json(result)

                        # 取消按钮 - 返回到记录选择界面
                        if st.button("取消", key="cancel_create_form"):
                            st.session_state.show_create_form = False
                            st.rerun()

            # 未展示创建表单时，显示台账表和选择界面
            else:
                # 显示当前已添加的待发放记录
                if st.session_state.pending_issue_records:
                    st.write("### 当前待发放列表")
                    st.success(
                        f"已添加 {len(st.session_state.pending_issue_records)} 条记录到待发放列表"
                    )
                    st.info(
                        "⚠️ 待发放列表**仅存在于当前会话**中，提交后才会创建草稿状态的发放单。草稿状态的发放单可在【报告发放】-【发放单管理】中查看和编辑。"
                    )

                    # 转换为DataFrame用于显示
                    pending_df = pd.DataFrame(
                        [
                            record.get("record_data", {})
                            for record in st.session_state.pending_issue_records
                        ]
                    )

                    # 添加发放类型显示列
                    pending_df["发放方式"] = [
                        "发放1份" if record["issue_type"] == "single" else "发放全部"
                        for record in st.session_state.pending_issue_records
                    ]

                    # 选择要显示的列
                    display_cols = ["发放方式", "委托编号", "项目名称", "委托单位"]
                    available_cols = [
                        col for col in display_cols if col in pending_df.columns
                    ]

                    # 显示待发放列表
                    st.dataframe(
                        pending_df[
                            available_cols if available_cols else pending_df.columns
                        ],
                        use_container_width=True,
                        hide_index=True,
                    )

                    # 添加单条删除功能
                    st.write("移除待发放记录:")

                    # 为每条记录创建删除按钮
                    cols = st.columns(3)
                    for i, record in enumerate(st.session_state.pending_issue_records):
                        col_idx = i % 3
                        record_data = record.get("record_data", {})
                        display_text = f"{i+1}. {record_data.get('委托编号', 'N/A')} ({record_data.get('项目名称', '')[:20]}...)"
                        with cols[col_idx]:
                            if st.button(f"🗑️ {display_text}", key=f"del_rec_{i}"):
                                # 删除此条目
                                st.session_state.pending_issue_records.pop(i)
                                st.rerun()

                    # 清空待发放列表按钮
                    if st.button("清空待发放列表"):
                        st.session_state.pending_issue_records = []
                        st.rerun()

                    # 创建发放单按钮
                    if st.button("创建发放单", key="create_form_from_pending"):
                        if not st.session_state.pending_issue_records:
                            st.error("待发放列表为空！")
                        else:
                            # 只设置标志，不使用st.rerun()，这样后续代码会继续执行
                            st.session_state.show_create_form = True

                # 步骤2: 加载可用于发放的档案记录
                st.write("### 步骤2: 选择档案记录")
                st.info(
                    "选择发放类型后，系统将自动筛选符合条件的档案记录。勾选或取消勾选记录会自动添加或移除到待发放列表。"
                )

                # 添加诊断按钮
                col1, col2, col3 = st.columns(3)
                with col1:
                    # 正常加载可用档案记录按钮
                    if st.button(
                        "加载符合条件的可发放档案记录", key="load_available_records"
                    ):
                        load_available_records()
                with col2:
                    # 添加测试按钮 - 加载全部档案记录（无过滤）
                    if st.button(
                        "加载所有档案记录（测试用）", key="load_all_records_test"
                    ):
                        try:
                            st.info("正在加载所有档案记录（忽略发放条件）...")
                            # 尝试从台账API加载，而不是从发放API
                            response = requests.get(
                                LEDGER_URL,
                                params={"page_size": 100},
                                auth=HTTPBasicAuth("admin", "!@#$qwer"),
                            )

                            st.write(f"请求URL: {response.url}")

                            if response.status_code == 200:
                                records = response.json()
                                with st.expander("API响应详情（点击展开）"):
                                    st.json(records)

                                if (
                                    records
                                    and isinstance(records, dict)
                                    and "results" in records
                                ):
                                    records_list = records["results"]
                                    st.session_state.filtered_records = records_list
                                    st.success(f"找到 {len(records_list)} 条档案记录")

                                    # 分析记录发放状态
                                    st.write("### 发放状态分析")

                                    # 创建计数器
                                    status_counts = {
                                        "未发放记录": 0,  # 适合single
                                        "部分发放记录": 0,  # 适合all_remaining
                                        "全部发放完毕": 0,  # 不符合任何条件
                                        "无法确定状态": 0,  # 缺少关键字段
                                    }

                                    # 创建示例记录列表
                                    examples = {k: [] for k in status_counts.keys()}

                                    # 分析每条记录
                                    for record in records_list:
                                        # 提取关键字段（兼容不同字段名）
                                        first_issue = (
                                            record.get(
                                                "first_issue_copies",
                                                record.get("已发放份数", 0),
                                            )
                                            or 0
                                        )
                                        total_issue = (
                                            record.get("total_issue_copies", 3) or 3
                                        )

                                        # 判断状态
                                        if first_issue == 0:
                                            status = "未发放记录"  # 未发放，适合single
                                        elif (
                                            first_issue > 0
                                            and first_issue < total_issue
                                        ):
                                            status = "部分发放记录"  # 部分发放，适合all_remaining
                                        elif first_issue >= total_issue:
                                            status = "全部发放完毕"  # 全部发放，不适合任何条件
                                        else:
                                            status = "无法确定状态"  # 缺少关键字段

                                        # 更新计数
                                        status_counts[status] += 1

                                        # 保存示例(最多5个)
                                        if len(examples[status]) < 5:
                                            examples[status].append(
                                                {
                                                    "id": record.get("id"),
                                                    "委托编号/报告编号": record.get(
                                                        "委托编号",
                                                        record.get(
                                                            "report_number", "未知"
                                                        ),
                                                    ),
                                                    "已发放份数": first_issue,
                                                    "总发放份数": total_issue,
                                                    "原始记录": record,
                                                }
                                            )

                                    # 显示分析结果
                                    st.write("#### 发放状态分布")
                                    for status, count in status_counts.items():
                                        # 添加指示符号
                                        if status == "未发放记录":
                                            indicator = (
                                                "✅ 适合 [发放1份] 和 [发放全部]"
                                            )
                                        elif status == "部分发放记录":
                                            indicator = "✅ 适合 [发放全部]"
                                        elif status == "全部发放完毕":
                                            indicator = "❌ 不符合任何发放条件"
                                        else:
                                            indicator = "⚠️ 请检查字段"

                                        st.write(
                                            f"**{status}**: {count} 条 {indicator}"
                                        )

                                    # 显示详细示例
                                    st.write("#### 各状态示例记录")
                                    for status, record_examples in examples.items():
                                        if record_examples:
                                            # 使用折叠面板展示示例，但不使用expander
                                            if st.checkbox(
                                                f"查看 {status} 示例",
                                                key=f"show_example_{status}",
                                                value=False,
                                            ):
                                                st.write(f"##### {status} 示例:")
                                                for i, ex in enumerate(record_examples):
                                                    st.write(f"示例 {i+1}:")
                                                    st.json(ex)

                                    # 根据分析结果给出建议
                                    st.write("#### 问题诊断与建议")
                                    if (
                                        status_counts["未发放记录"] == 0
                                        and st.session_state.issue_type == "single"
                                    ):
                                        st.error(
                                            "⚠️ 没有未发放的记录，所以 [发放1份] 模式找不到任何符合条件的记录"
                                        )
                                        st.info(
                                            "建议: 尝试切换到 [发放全部] 模式，或添加新的未发放记录"
                                        )

                                    if (
                                        status_counts["部分发放记录"] == 0
                                        and st.session_state.issue_type
                                        == "all_remaining"
                                    ):
                                        st.error(
                                            "⚠️ 没有部分发放的记录，所以 [发放全部] 模式找不到任何符合条件的记录"
                                        )
                                        st.info(
                                            "建议: 尝试切换到 [发放1份] 模式，或创建一些部分发放的记录"
                                        )

                                    # 添加临时解决方案按钮
                                    st.write("#### 临时解决方案")
                                    st.info(
                                        "您可以临时忽略发放条件，直接使用所有档案记录"
                                    )

                                    # 根据当前模式和状态推荐操作
                                    if (
                                        st.session_state.issue_type == "single"
                                        and status_counts["未发放记录"] == 0
                                    ):
                                        if status_counts["部分发放记录"] > 0:
                                            if st.button(
                                                "切换到 [发放全部] 模式",
                                                key="switch_to_all_remaining",
                                            ):
                                                st.session_state.issue_type = (
                                                    "all_remaining"
                                                )
                                                st.rerun()

                                    elif (
                                        st.session_state.issue_type == "all_remaining"
                                        and status_counts["部分发放记录"] == 0
                                    ):
                                        if status_counts["未发放记录"] > 0:
                                            if st.button(
                                                "切换到 [发放1份] 模式",
                                                key="switch_to_single",
                                            ):
                                                st.session_state.issue_type = "single"
                                                st.rerun()

                                elif isinstance(records, list):
                                    st.session_state.filtered_records = records
                                    st.success(f"找到 {len(records)} 条档案记录")
                                else:
                                    st.warning("API返回了意外的数据格式")
                            else:
                                st.error(f"请求失败: {response.status_code}")
                        except Exception as e:
                            st.error(f"测试加载时出错: {str(e)}")
                            import traceback

                            st.code(traceback.format_exc())
                with col3:
                    # 显示调试信息按钮
                    if st.button("检查API端点", key="check_api_endpoints"):
                        st.write("API端点信息:")
                        st.code(f"档案台账URL: {LEDGER_URL}")
                        st.code(f"发放记录URL: {AVAILABLE_RECORDS_URL}")
                        st.write("当前参数:")
                        st.code(f"发放类型: {st.session_state.issue_type}")

                        # 检查是否有台账数据
                        if (
                            "ledger_data" in st.session_state
                            and st.session_state.ledger_data
                        ):
                            st.success(
                                f"台账中有 {len(st.session_state.ledger_data)} 条记录"
                            )
                        else:
                            st.warning("未加载台账数据，请先加载台账")

                # 已加载记录，显示选择界面
                if st.session_state.filtered_records is not None:
                    # 确保filtered_records是列表且不为空
                    if not isinstance(st.session_state.filtered_records, list):
                        st.error("加载的记录格式不正确，应为列表")
                        st.write(f"当前类型: {type(st.session_state.filtered_records)}")
                        st.json(st.session_state.filtered_records)
                        st.session_state.filtered_records = []

                    if not st.session_state.filtered_records:
                        st.warning("没有找到符合条件的可发放档案记录")
                    else:
                        # 将筛选后的记录转为DataFrame
                        try:
                            # 初始化或更新records_df
                            if st.session_state.filtered_records is not None:
                                # 如果是初次加载或记录有变化，才创建新的DataFrame
                                if st.session_state.records_df is None or len(
                                    st.session_state.records_df) != len(st.session_state.filtered_records):
                                    
                                    # 创建新的DataFrame
                                    records_df = pd.DataFrame(st.session_state.filtered_records)
                                    
                                    # 添加选择列和预发放份数列（如果不存在）
                                    if "选择" not in records_df.columns:
                                        records_df["选择"] = False
                                    if "预发放份数" not in records_df.columns:
                                        records_df["预发放份数"] = ""
                                    
                                    # 确保必要的列存在
                                    if "已发放份数" not in records_df.columns:
                                        if "first_issue_copies" in records_df.columns:
                                            records_df["已发放份数"] = records_df["first_issue_copies"]
                                        elif "issued_copies" in records_df.columns:
                                            records_df["已发放份数"] = records_df["issued_copies"]
                                        else:
                                            records_df["已发放份数"] = 0
                                    
                                    if ("委托编号" not in records_df.columns and 
                                        "commission_number" in records_df.columns):
                                        records_df["委托编号"] = records_df["commission_number"]
                                    
                                    # 保持之前已选择的状态
                                    if st.session_state.records_df is not None:
                                        # 提取之前的选择状态
                                        old_selections = {}
                                        for i, row in st.session_state.records_df.iterrows():
                                            if row.get("选择", False):
                                                old_selections[row["id"]] = {
                                                    "选择": True,
                                                    "预发放份数": row.get("预发放份数", ""),
                                                    "issue_type": st.session_state.record_issue_type_map.get(
                                                        row["id"], st.session_state.issue_type)
                                                }
                                        
                                        # 应用之前的选择状态到新DataFrame
                                        for i, row in records_df.iterrows():
                                            record_id = row["id"]
                                            if record_id in old_selections:
                                                records_df.at[i, "选择"] = True
                                                records_df.at[i, "预发放份数"] = old_selections[record_id]["预发放份数"]
                                    
                                    # 保存到会话状态
                                    st.session_state.records_df = records_df
                                else:
                                    # 使用已有的DataFrame，避免重新创建
                                    records_df = st.session_state.records_df
                                
                                # 刷新预发放份数和选择状态
                                # 标记当前已在待发放列表中的记录
                                for i, row in records_df.iterrows():
                                    record_id = row["id"]
                                    
                                    # 检查是否在待发放列表中
                                    in_pending_list = False
                                    matching_record = None
                                    
                                    for record in st.session_state.pending_issue_records:
                                        if record["id"] == record_id:
                                            in_pending_list = True
                                            matching_record = record
                                            break
                                    
                                    # 更新选择状态和预发放份数
                                    if in_pending_list and matching_record:
                                        # 标记为已选择
                                        records_df.at[i, "选择"] = True
                                        
                                        # 设置预发放份数
                                        issue_type = matching_record["issue_type"]
                                        issued_copies = row.get("已发放份数", 0) or 0
                                        
                                        if issue_type == "single":
                                            records_df.at[i, "预发放份数"] = "1份"
                                        else:
                                            total_copies = row.get("total_issue_copies", 3) or 3
                                            remaining = max(1, total_copies - issued_copies)
                                            records_df.at[i, "预发放份数"] = f"全部({remaining}份)"
                                        
                                        # 记录当前的发放类型绑定
                                        st.session_state.record_issue_type_map[record_id] = issue_type
                                    elif not in_pending_list:
                                        # 如果不在待发放列表中，确保显示为未选择状态
                                        records_df.at[i, "选择"] = False
                                        records_df.at[i, "预发放份数"] = ""
                                        
                                        # 如果在映射中，移除
                                        if record_id in st.session_state.record_issue_type_map:
                                            del st.session_state.record_issue_type_map[record_id]
                                
                                # 显示数据列
                                st.write("### 可发放档案记录")
                                st.write(f"共找到 {len(records_df)} 条可发放记录")
                                
                                # 设置列顺序和格式
                                fixed_columns = [
                                    "选择",
                                    "预发放份数",
                                    "已发放份数",
                                    "委托编号",
                                ]
                                priority_columns = ["项目名称", "委托单位"]
                                
                                # 构建最终显示列顺序
                                all_columns = list(records_df.columns)
                                display_columns = []
                                
                                # 确保固定列按照指定顺序显示在最前面
                                for col in fixed_columns:
                                    if col in all_columns:
                                        display_columns.append(col)
                                
                                # 其他高优先级列放在固定列之后
                                for col in priority_columns:
                                    if col in all_columns and col not in display_columns:
                                        display_columns.append(col)
                                
                                # 其他可能有用的字段
                                other_useful_columns = [
                                    "report_number",
                                    "archive_number",
                                    "sample_number",
                                    "title",
                                    "client_name",
                                    "project_name",
                                    "price",
                                ]
                                for col in other_useful_columns:
                                    if col in all_columns and col not in display_columns:
                                        display_columns.append(col)
                                
                                # 剩余列
                                remaining_columns = [
                                    col
                                    for col in all_columns
                                    if col not in display_columns and col != "id"
                                ]
                                
                                # ID字段放在最后
                                if "id" in all_columns and "id" not in display_columns:
                                    remaining_columns.append("id")
                                
                                # 最终显示顺序
                                final_column_order = display_columns + remaining_columns
                                
                                # CHANGE: [2025-04-25] 强制重排 DataFrame 列顺序以固定显示
                                # 在调用 st.data_editor 之前强制重排 DataFrame 列顺序
                                records_df = records_df[final_column_order]
                                
                                # 创建列配置
                                column_config = {}
                                
                                # 为固定列创建固定宽度配置
                                column_config["选择"] = st.column_config.CheckboxColumn(
                                    "选择", 
                                    help="勾选添加，取消勾选移除",
                                    width=50,
                                    required=True,
                                    pinned="left",  # 添加pinned参数，固定在左侧
                                )
                                column_config["预发放份数"] = st.column_config.TextColumn(
                                    "预发放份数", 
                                    help="将发放的份数",
                                    width=100,
                                    disabled=True,
                                    pinned="left",  # 添加pinned参数，固定在左侧
                                )
                                column_config["已发放份数"] = st.column_config.NumberColumn(
                                    "已发放份数", 
                                    help="已经发放的份数",
                                    width=100,
                                    disabled=True,
                                    pinned="left",  # 添加pinned参数，固定在左侧
                                )
                                column_config["委托编号"] = st.column_config.TextColumn(
                                    "委托编号", 
                                    help="委托编号信息",
                                    width=100,
                                    disabled=True,
                                    pinned="left",  # 添加pinned参数，固定在左侧
                                )
                                
                                # 为主要信息列创建配置
                                for col in ["项目名称", "委托单位"]:
                                    if col in records_df.columns:
                                        column_config[col] = st.column_config.TextColumn(
                                            col, 
                                            help=f"{col}信息",
                                            width="medium",
                                            disabled=True,
                                        )
                                
                                # 其他列使用默认配置 - 确保所有列都有配置
                                for col in final_column_order:
                                    if (
                                        col not in column_config
                                        and col in records_df.columns
                                    ):
                                        column_config[col] = st.column_config.Column(
                                            col, disabled=True
                                        )
                                
                                # 使用固定方法生成key以确保一致性
                                form_key = f"editor_{len(records_df)}"
                                
                                # 修改Streamlit表格的回调函数
                                def on_data_editor_change():
                                    """当data_editor中的选择框状态改变时触发"""
                                    st.session_state.checkbox_changed = True
                                    # 不需要其他代码，只需设置一个标志，通知后续代码处理变更

                                # 使用data_editor显示记录
                                edited_records = st.data_editor(
                                    records_df,
                                    key=form_key,
                                    column_config=column_config,
                                    use_container_width=True,
                                    num_rows="fixed",
                                    disabled=list(set(records_df.columns) - {"选择"}),
                                    hide_index=True,
                                    column_order=final_column_order,
                                    on_change=on_data_editor_change  # 添加回调函数
                                )
                                
                                # 检测选择状态变化并立即处理
                                if edited_records is not None and "选择" in edited_records.columns:
                                    # 如果checkbox被更改了
                                    if st.session_state.checkbox_changed:
                                        # 重置标志，防止重复处理
                                        st.session_state.checkbox_changed = False
                                        
                                        # 处理选择状态变化
                                        changes_made = False
                                        for i, row in edited_records.iterrows():
                                            record_id = row["id"]
                                            is_selected = bool(row["选择"])
                                            
                                            # 获取当前记录在待发放列表中的状态
                                            in_pending_list = any(
                                                r["id"] == record_id
                                                for r in st.session_state.pending_issue_records
                                            )
                                            
                                            # 勾选了记录但不在待发放列表
                                            if is_selected and not in_pending_list:
                                                changes_made = True
                                                # 获取发放类型和已发放份数
                                                issue_type = st.session_state.issue_type
                                                issued_copies = row.get("已发放份数", 0) or 0
                                                
                                                # 计算预发放份数
                                                if issue_type == "single":
                                                    displayed_text = "1份"
                                                else:
                                                    total_copies = row.get("total_issue_copies", 3) or 3
                                                    remaining = max(1, total_copies - issued_copies)
                                                    displayed_text = f"全部({remaining}份)"
                                                
                                                # 立即更新预发放份数显示 - 直接修改显示的DataFrame
                                                records_df.at[i, "预发放份数"] = displayed_text
                                                st.session_state.records_df.at[i, "预发放份数"] = displayed_text
                                                
                                                # 提取显示数据
                                                display_data = {}
                                                for key in [
                                                    "委托编号",
                                                    "项目名称",
                                                    "委托单位",
                                                    "title",
                                                    "client_name",
                                                    "project_name",
                                                ]:
                                                    if key in row and not pd.isna(row[key]):
                                                        display_data[key] = row[key]
                                                
                                                # 添加到待发放列表
                                                st.session_state.pending_issue_records.append(
                                                    {
                                                        "id": record_id,
                                                        "issue_type": issue_type,
                                                        "record_data": display_data,
                                                    }
                                                )
                                                
                                                # 记录发放类型绑定
                                                st.session_state.record_issue_type_map[record_id] = issue_type
                                            
                                            # 取消勾选但仍在待发放列表
                                            elif not is_selected and in_pending_list:
                                                changes_made = True
                                                # 清空预发放份数显示
                                                records_df.at[i, "预发放份数"] = ""
                                                st.session_state.records_df.at[i, "预发放份数"] = ""
                                                
                                                # 从待发放列表中移除
                                                st.session_state.pending_issue_records = [
                                                    r
                                                    for r in st.session_state.pending_issue_records
                                                    if r["id"] != record_id
                                                ]
                                                
                                                # 从映射中移除
                                                if record_id in st.session_state.record_issue_type_map:
                                                    del st.session_state.record_issue_type_map[record_id]
                                        
                                        # 保存更新后的状态 - 同时更新表格和会话状态
                                        st.session_state.records_df = records_df.copy()
                                        
                                        if changes_made:
                                            # 使用rerun强制更新显示
                                            st.rerun()
                                            
                                            # 显示成功消息 - 由于rerun，这条消息不会显示，但会在刷新后显示
                                            # st.success("✅ 已更新选择状态")
                        except Exception as e:
                            st.error(f"处理数据时出错: {str(e)}")
                            import traceback
                            st.code(traceback.format_exc())

# ============ 报告发放标签页 ============
with main_tabs[1]:
    st.header("报告发放系统")

    # 创建标签页
    issue_tabs = st.tabs(["发放单管理", "更新说明"])

    # Tab 1: 发放单管理
    with issue_tabs[0]:
        st.subheader("发放功能已升级")

        # 提示信息
        st.info(
            """
        ### 📢 报告发放功能已升级
        
        为了提供更好的用户体验，报告发放功能已经升级并整合到「档案流管理」-「台账报告发放」中。
        
        **新功能特点**:
        - ✅ 勾选档案自动添加到待发放列表
        - ✅ 取消勾选自动移除
        - ✅ 支持批量操作
        - ✅ 更直观的工作流程
        """
        )

        # 引导用户跳转到新功能
        if st.button("前往使用新的报告发放功能", type="primary"):
            st.session_state["_tabs_default_index"] = 0  # 切换到第一个主标签页
            st.rerun()

            st.markdown("---")

        # 迁移期兼容：显示现有发放单列表
        st.subheader("已有发放单")
        st.write("您可以查看并管理现有的发放单")

        # 获取发放单列表
        if st.button("查看已有发放单列表"):
            response = api_request(ISSUE_FORM_URL)
            # 处理分页响应
            if response and isinstance(response, dict) and "results" in response:
                st.session_state.issue_forms = response["results"]
            else:
                st.session_state.issue_forms = response

            if st.session_state.issue_forms:
                # 创建数据表格
                df = pd.DataFrame(st.session_state.issue_forms)
                if not df.empty:
                    # 选择要显示的列
                    display_columns = [
                        "number",
                        "issue_date",
                        "receiver_name",
                        "receiver_unit",
                        "status_display",
                        "created_at",
                    ]
                    rename_map = {
                        "number": "单号",
                        "issue_date": "发放日期",
                        "receiver_name": "领取人",
                        "receiver_unit": "领取单位",
                        "status_display": "状态",
                        "created_at": "创建时间",
                    }

                    # 确保所有必要的列都存在
                    df_display = pd.DataFrame()
                    for col in display_columns:
                        if col in df.columns:
                            df_display[rename_map.get(col, col)] = df[col]

                    # 显示表格
                    st.dataframe(df_display, use_container_width=True)
                else:
                    st.info("未找到发放单")
            else:
                st.info("暂无发放单数据")

    # Tab 2: 更新说明
    with issue_tabs[1]:
        st.subheader("功能更新说明")

        st.markdown(
            """
        ### 报告发放功能升级说明
        
        #### 为什么升级?
        原有的报告发放流程相对复杂，需要多次切换界面和点击多个按钮。新版本简化了操作流程，使其更加直观和高效。
        
        #### 新流程如何使用?
        1. 进入「档案流管理」-「台账报告发放」标签页
        2. 选择发放类型（发放1份或发放全部）
        3. 加载符合条件的档案记录
        4. 勾选需要添加的记录（自动添加到待发放列表）
        5. 点击"创建发放单"按钮
        6. 填写领取人信息并确认
        
        #### 原有发放单如何处理?
        原有的发放单依然可以通过本页面的"发放单管理"标签查看和管理。后续版本将完全统一到新的流程中。
        """
        )

        # 添加跳转按钮
        if st.button("我已了解，立即使用新功能", type="primary"):
            st.session_state["_tabs_default_index"] = 0  # 切换到第一个主标签页
            st.rerun()


# 添加辅助函数
def toggle_selection(index):
    """切换记录的选择状态"""
    if index in st.session_state.selected_indices:
        st.session_state.selected_indices.remove(index)
    else:
        st.session_state.selected_indices.append(index)
