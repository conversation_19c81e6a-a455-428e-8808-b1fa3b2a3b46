#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR Model Download Script
Enhanced download script to resolve Docker build network connection issues
"""

import os
import time
import sys
import requests
import logging
from paddleocr import PaddleOCR


def download_with_retry(lang, max_retries=5, initial_delay=5):
    """
    Download PaddleOCR models with retry mechanism and exponential backoff
    
    Args:
        lang: Language code (e.g., 'en', 'ch')
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay time (seconds)
    """
    delay = initial_delay
    
    for attempt in range(max_retries):
        try:
            print(f'🔄 Attempting to download models (attempt {attempt + 1}/{max_retries})...')
            
            # CHANGE: [2025-07-29] 确保下载脚本与业务代码使用的模型版本一致
            # 一次性初始化，传入所有需要的参数和语言
            ocr = PaddleOCR(
                ocr_version="PP-OCRv4",  # 必须与 ocr_utils.py 中的版本一致
                lang=lang,
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            
            print(f'✅ Models for lang="{lang}" downloaded successfully!')
            return True
            
        except requests.exceptions.ChunkedEncodingError as e:
            print(f'❌ Network connection interrupted ({lang.upper()}): {str(e)}')
        except requests.exceptions.Timeout as e:
            print(f'❌ Download timeout ({lang.upper()}): {str(e)}')
        except Exception as e:
            print(f'❌ Download failed ({lang.upper()}): {str(e)}')
        
        if attempt < max_retries - 1:
            print(f'⏳ Retrying in {delay} seconds...')
            time.sleep(delay)
            delay = min(delay * 2, 60)  # Exponential backoff, max 60 seconds
        else:
            print(f'💥 Models for lang="{lang}" failed after {max_retries} attempts')
            return False
    
    return False


def main():
    """Main function"""
    print('=' * 60)
    print('🚀 Starting PaddleOCR model download...')
    print('=' * 60)
    
    # Set environment variables to optimize download
    os.environ['CURL_CA_BUNDLE'] = ''  # Avoid SSL issues
    
    # Set log level to reduce output
    logging.getLogger('ppocr').setLevel(logging.ERROR)
    
    # CHANGE: [2025-07-29] 调整下载逻辑，因为一次初始化会下载所有需要的模型
    # 我们主要使用 'ch' (中英混合) 模型，它已经包含了英文。
    # 为了确保覆盖所有可能的基础模型，我们仍然可以指定下载它。
    
    # PaddleOCR v4 的 'ch' 模型已经包含了英文，所以只需要下载 'ch' 即可。
    # 'en' 模型是独立的。这里我们指定 'ch'，它会附带下载依赖的英文基础。
    lang_to_download = 'ch'
    
    print(f'\n📦 Preparing to download models for language setting: {lang_to_download}...')
    if download_with_retry(lang_to_download):
        print('\n' + '=' * 60)
        print('🎉 All required PaddleOCR models downloaded successfully!')
        return 0
    else:
        print('\n' + '=' * 60)
        print(f'⚠️ Model download failed for language setting: {lang_to_download}')
        print('Application will attempt to download missing models at runtime')
        return 0  # 仍然返回0，不中断构建流程
    

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code) 