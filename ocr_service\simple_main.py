"""
简化版 OCR 服务 - 紧急修复用
"""
import os
from fastapi import FastAPI

# 简单的配置
class SimpleSettings:
    app_name = "OCR Service"
    app_version = "1.0.0"
    debug = False

settings = SimpleSettings()

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="简化版 OCR 微服务"
)

@app.get("/")
async def root():
    """根端点"""
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running (simplified)",
        "message": "OCR 服务正在运行（简化版）"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "ocr-service",
        "version": settings.app_version,
        "message": "服务运行正常"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
