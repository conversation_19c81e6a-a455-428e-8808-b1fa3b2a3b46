# Generated by Django 5.1.11 on 2025-06-21 15:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0018_remove_archiverecord_archive_rec_archive_455fa1_idx_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='archiverecord',
            name='archive_status',
            field=models.CharField(blank=True, choices=[('pending', '待处理'), ('archive_splitting_in_progress', '档案拆分处理中'), ('pending_report_splitting', '报告待拆分处理'), ('report_splitting_in_progress', '报告拆分处理中'), ('archived', '已归档'), ('failed', '处理失败')], default='pending', max_length=50, null=True, verbose_name='报告归档状态'),
        ),
    ]
