"use client"

import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Server, HardDrive, Database } from "lucide-react"

export function SystemStatus() {
  // 模拟系统状态数据
  const systemStatus = {
    processingEngine: {
      status: "operational",
      load: 65,
      message: "正常运行中",
    },
    storageSystem: {
      status: "operational",
      usedSpace: 1256,
      totalSpace: 5000,
      usagePercentage: 25.12,
    },
    queueSystem: {
      status: "operational",
      pendingTasks: 18,
      processingRate: "3.5个/分钟",
      estimatedTime: "35分钟",
    },
    databaseSystem: {
      status: "operational",
      connections: 24,
      responseTime: "45ms",
    },
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Server className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">处理引擎</span>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            运行中
          </Badge>
        </div>
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span>当前负载</span>
            <span className="font-medium">{systemStatus.processingEngine.load}%</span>
          </div>
          <Progress value={systemStatus.processingEngine.load} className="h-2" />
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <HardDrive className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">存储系统</span>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            正常
          </Badge>
        </div>
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span>存储使用</span>
            <span className="font-medium">
              {systemStatus.storageSystem.usedSpace} GB / {systemStatus.storageSystem.totalSpace} GB (
              {systemStatus.storageSystem.usagePercentage.toFixed(1)}%)
            </span>
          </div>
          <Progress value={systemStatus.storageSystem.usagePercentage} className="h-2" />
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Database className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">队列系统</span>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            正常
          </Badge>
        </div>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>待处理任务</span>
            <span className="font-medium">{systemStatus.queueSystem.pendingTasks}个</span>
          </div>
          <div className="flex justify-between">
            <span>处理速率</span>
            <span className="font-medium">{systemStatus.queueSystem.processingRate}</span>
          </div>
          <div className="flex justify-between">
            <span>预计完成时间</span>
            <span className="font-medium">{systemStatus.queueSystem.estimatedTime}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
