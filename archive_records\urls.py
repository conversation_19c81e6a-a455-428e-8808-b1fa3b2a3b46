"""
档案记录URL配置模块

CHANGE: [2025-06-04] 完全重构为DRF ViewSet统一设计
- 所有功能均使用ViewSet和Router自动路由
- 创建ExcelImportSessionViewSet统一管理Excel导入功能
- 移除所有手动URL配置，实现完全自动化路由
- 保持API向后兼容性

设计原则:
1. 完全使用DRF ViewSet - 所有功能通过Router自动生成路由
2. 复杂业务流程使用ViewSet action - 保持RESTful设计
3. 统一管理 - 按功能域划分ViewSet
4. 向后兼容 - 保持原有API端点路径不变
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    # 核心资源管理ViewSet
    ArchiveRecordViewSet,
    ImportLogViewSet,
    ChangeLogViewSet,
    ChangeOrderViewSet,
    ChangeOrderItemViewSet,
    ChangeOrderAttachmentViewSet,
    # Excel导入管理ViewSet
    ExcelImportSessionViewSet,
)

# ==========================================
# DRF Router 完全自动路由生成
# ==========================================
router = DefaultRouter()

# 档案记录管理 - 完整的CRUD + 自定义业务操作
router.register(r"records", ArchiveRecordViewSet, basename="record")
# Router自动生成的标准CRUD路由:
# GET    /api/archive-records/records/                     -> list()
# POST   /api/archive-records/records/                     -> create()
# GET    /api/archive-records/records/{id}/                -> retrieve()
# PUT    /api/archive-records/records/{id}/                -> update()
# PATCH  /api/archive-records/records/{id}/                -> partial_update()
# DELETE /api/archive-records/records/{id}/                -> destroy()
#
# 自定义@action装饰器方法 (需要在ArchiveRecordViewSet中定义):
# GET    /api/archive-records/records/ag-grid/             -> @action ag_grid_list()
# GET    /api/archive-records/records/{id}/history/        -> @action history()
# GET    /api/archive-records/records/{id}/compare/        -> @action compare()
# POST   /api/archive-records/records/{id}/rollback/       -> @action rollback()

# 导入日志管理 - 只读操作
router.register(r"import-logs", ImportLogViewSet, basename="importlog")
# Router自动生成的路由:
# GET    /api/archive-records/import-logs/                 -> list()
# GET    /api/archive-records/import-logs/{id}/            -> retrieve()
#
# 自定义action:
# GET    /api/archive-records/import-logs/{id}/records/    -> @action imported_records()

# 变更日志管理 - 只读操作
router.register(r"change-logs", ChangeLogViewSet, basename="changelog")
# Router自动生成的路由:
# GET    /api/archive-records/change-logs/                 -> list()
# GET    /api/archive-records/change-logs/{id}/            -> retrieve()

# 更改单管理 - 完整的业务流程
router.register(r"change-orders", ChangeOrderViewSet, basename="changeorder")
# Router自动生成的标准CRUD路由:
# GET    /api/archive-records/change-orders/               -> list()
# POST   /api/archive-records/change-orders/               -> create()
# GET    /api/archive-records/change-orders/{id}/          -> retrieve()
# PUT    /api/archive-records/change-orders/{id}/          -> update()
# PATCH  /api/archive-records/change-orders/{id}/          -> partial_update()
# DELETE /api/archive-records/change-orders/{id}/          -> destroy()
#
# 自定义业务流程action:
# POST   /api/archive-records/change-orders/{id}/submit/   -> @action submit()
# POST   /api/archive-records/change-orders/{id}/approve/  -> @action approve()
# POST   /api/archive-records/change-orders/{id}/reject/   -> @action reject()

# Excel导入会话管理 - 完整的导入流程管理
router.register(r"excel-import", ExcelImportSessionViewSet, basename="excelimport")
# Router自动生成的标准CRUD路由:
# GET    /api/archive-records/excel-import/                -> list() (获取所有导入会话)
# POST   /api/archive-records/excel-import/                -> create() (创建新导入会话)
# GET    /api/archive-records/excel-import/{id}/           -> retrieve() (获取特定会话详情)
# PUT    /api/archive-records/excel-import/{id}/           -> update() (更新会话)
# PATCH  /api/archive-records/excel-import/{id}/          -> partial_update() (部分更新会话)
# DELETE /api/archive-records/excel-import/{id}/          -> destroy() (删除会话)
#
# 自定义Excel导入业务流程action（新的两阶段导入流程）:
# POST   /api/archive-records/excel-import/analyze/        -> @action analyze() (开始分析)
# POST   /api/archive-records/excel-import/confirm/        -> @action confirm() (确认导入)
# GET    /api/archive-records/excel-import/{id}/analysis-result/ -> @action analysis_result() (获取分析结果)
# POST   /api/archive-records/excel-import/cancel/         -> @action cancel() (取消导入)
# GET    /api/archive-records/excel-import/active-session/ -> @action active_session() (获取活跃会话)
# POST   /api/archive-records/excel-import/{id}/heartbeat/ -> @action heartbeat() (会话心跳)
# POST   /api/archive-records/excel-import/{id}/acknowledge/ -> @action acknowledge() (确认结果)
# POST   /api/archive-records/excel-import/{id}/begin-conflict-processing/ -> @action begin_conflict_processing()
# POST   /api/archive-records/excel-import/{id}/pend-conflict-processing/ -> @action pend_conflict_processing()

# ==========================================
# 完全自动化URL配置
# ==========================================
urlpatterns = [
    # 包含所有DRF Router自动生成的路由
    path("", include(router.urls)),
]

# ==========================================
# 现代化API设计原则说明 (2025-06-19)
# ==========================================
"""
🎯 现代化API设计原则实施指南:

1. **RESTful资源关系**:
   - 使用 detail=True 表示资源间的层级关系
   - URL路径明确表达资源归属: /sessions/{id}/analysis-result/
   - 避免将资源ID作为查询参数传递

2. **一致的错误处理**:
   - 统一的错误响应格式包含 error_code 字段
   - 语义化的HTTP状态码 (404, 400, 409, 500)
   - 详细的错误日志记录

3. **类型安全**:
   - 后端ViewSet明确的参数验证
   - 前端TypeScript接口严格定义
   - API响应格式标准化

4. **文档化**:
   - 详细的docstring说明API用途
   - Swagger集成自动生成API文档
   - 代码注释说明设计决策

5. **向后兼容**:
   - 保持现有API端点不变
   - 渐进式升级API设计
   - 版本化支持(未来考虑)

示例对比:
❌ 旧设计: GET /api/.../analysis-result/?import_session_id=xxx
✅ 新设计: GET /api/.../excel-import/{session_id}/analysis-result/

好处:
- 更清晰的资源关系表达
- 更好的缓存支持
- 更符合RESTful约定
- 更容易进行API版本管理
"""
