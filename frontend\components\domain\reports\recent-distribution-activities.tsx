"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { <PERSON><PERSON>ircle, Clock, Alert<PERSON>riangle, Lock, Unlock, Paperclip, Trash2 } from "lucide-react"

interface Activity {
  id: number
  userName: string
  userInitials: string
  action: string
  target: string
  time: string
  status: "creating" | "draft" | "locked" | "issued" | "deleted"
}

export function RecentDistributionActivities() {
  const [activities, setActivities] = useState<Activity[]>([])

  useEffect(() => {
    const activitiesData: Activity[] = [
      {
        id: 1,
        userName: "张三",
        userInitials: "张",
        action: "发放了报告",
        target: "REP-2023-0042",
        time: "10分钟前",
        status: "issued",
      },
      {
        id: 2,
        userName: "李四",
        userInitials: "李",
        action: "创建了新的发放单",
        target: "REP-2023-0045",
        time: "30分钟前",
        status: "creating",
      },
      {
        id: 3,
        userName: "王五",
        userInitials: "王",
        action: "锁定了发放单",
        target: "REP-2023-0038",
        time: "1小时前",
        status: "locked",
      },
      {
        id: 4,
        userName: "赵六",
        userInitials: "赵",
        action: "更新了草稿",
        target: "REP-2023-0040",
        time: "2小时前",
        status: "draft",
      },
      {
        id: 5,
        userName: "钱七",
        userInitials: "钱",
        action: "删除了发放单",
        target: "REP-2023-0037",
        time: "3小时前",
        status: "deleted",
      },
    ]

    setActivities(activitiesData)
  }, [])

  const getStatusBadge = (status: Activity["status"]) => {
    switch (status) {
      case "creating":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            创建中
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            草稿
          </Badge>
        )
      case "locked":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            已锁定
          </Badge>
        )
      case "issued":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            已发放
          </Badge>
        )
      case "deleted":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            已删除
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  if (activities.length === 0) {
    return <div className="p-4 text-center text-muted-foreground">加载中...</div>
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>最近发放活动</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-4">
              <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                {activity.userInitials}
              </div>
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm">
                    <span className="font-medium">{activity.userName}</span> {activity.action}{" "}
                    <span className="font-medium">{activity.target}</span>
                  </p>
                  {getStatusBadge(activity.status)}
                </div>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
