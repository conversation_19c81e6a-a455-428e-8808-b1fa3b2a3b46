from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from django.utils import timezone
from archive_records.models import (
    ArchiveRecord, ChangeLogBatch, 
    RecordChangeLog, FieldChangeLog
)
from archive_records.services.excel_import import ExcelImportService
from test_suite.unit.archive_records.base_version_test import BaseVersionTest


class VersionRollbackTest(BaseVersionTest):
    """测试版本回滚功能"""
    
    def setUp(self):
        super().setUp()
        
        # 创建示例记录并设置统一编号等于委托编号
        self.record = ArchiveRecord.objects.create(
            sample_number="S001",
            commission_number="C001",
            unified_number="C001",  # 统一编号等于委托编号
            project_name="原始项目名称",
            client_unit="原始客户单位",
            conclusion="原始结论"
        )
        
        # 创建初始版本(版本1)
        self._create_initial_version(self.record)
        
        # 创建第二个版本(版本2)
        self._create_update_version(
            self.record,
            version_number=2,
            changes={
                'project_name': "版本2项目名称",
                'conclusion': "版本2结论"
            },
            reason="第一次更新"
        )
        
        # 创建第三个版本(当前版本)
        self._create_update_version(
            self.record,
            version_number=3,
            changes={
                'project_name': "版本3项目名称",
                'client_unit': "版本3客户单位",
                'conclusion': "版本3结论"
            },
            reason="第二次更新"
        )
    
    def test_rollback_to_previous_version(self):
        """测试回滚到上一个版本"""
        url = reverse('record_rollback', args=[self.record.id, 2])
        
        # 执行回滚到版本2
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('message', response.data)
        self.assertIn('new_version', response.data)
        
        # 验证新版本创建成功
        new_version = response.data['new_version']
        self.assertEqual(new_version, 4)  # 应该是第4个版本
        
        # 验证记录已回滚
        updated_record = ArchiveRecord.objects.get(id=self.record.id)
        self.assertEqual(updated_record.project_name, "版本2项目名称")
        self.assertEqual(updated_record.conclusion, "版本2结论")
        self.assertEqual(updated_record.client_unit, "原始客户单位")  # 回滚后应该是原始值，而不是版本3的值
    
    def test_rollback_with_commission_number_change(self):
        """测试回滚委托编号同时更新统一编号"""
        # 创建一个更新了委托编号的版本
        self._create_update_version(
            self.record,
            version_number=4,
            changes={
                'commission_number': "C002",
                'unified_number': "C002"  # 统一编号也相应更新
            },
            reason="更新委托编号"
        )
        
        # 验证委托编号和统一编号已更新
        self.record.refresh_from_db()
        self.assertEqual(self.record.commission_number, "C002")
        self.assertEqual(self.record.unified_number, "C002")
        
        # 回滚到版本1，该版本委托编号为C001
        url = reverse('record_rollback', args=[self.record.id, 1])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        
        # 验证委托编号和统一编号都回滚
        self.record.refresh_from_db()
        self.assertEqual(self.record.commission_number, "C001")
        self.assertEqual(self.record.unified_number, "C001")
        self.assertEqual(self.record.unified_number, self.record.commission_number)
    
    def test_rollback_creates_proper_version(self):
        """测试回滚操作创建正确的版本记录"""
        # 测试回滚到版本1
        url = reverse('record_rollback', args=[self.record.id, 1])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        
        # 验证创建了新版本
        versions = RecordChangeLog.objects.filter(record=self.record).order_by('version_number')
        self.assertEqual(versions.count(), 4)  # 原来3个版本 + 1个回滚版本
        
        # 获取最新版本（回滚创建的版本）
        rollback_version = versions.last()
        
        # 验证回滚版本的属性
        self.assertEqual(rollback_version.version_number, 4)
        self.assertEqual(rollback_version.change_type, 'rollback')
        self.assertTrue(rollback_version.is_rollback)
        self.assertEqual(rollback_version.rollback_source_version, 1)
        
        # 验证记录内容回滚成功
        self.record.refresh_from_db()
        self.assertEqual(self.record.project_name, "原始项目名称")
        self.assertEqual(self.record.client_unit, "原始客户单位")
        self.assertEqual(self.record.conclusion, "原始结论") 