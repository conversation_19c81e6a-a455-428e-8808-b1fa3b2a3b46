import { useState, useRef, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"
import { uploadPdfService, type UploadableFile } from "@/services/domain/archive/upload/upload-pdf-service"
import { cn } from "@/lib/utils"

const CONCURRENT_UPLOADS = 4; // 并发上传数量

export interface UploadableFileWithProgress extends UploadableFile {
  progress?: number;
}

export function usePdfUploader() {
    const [files, setFiles] = useState<UploadableFileWithProgress[]>([])
    const [useParallel, setUseParallel] = useState(false)
    const [chunkSize, setChunkSize] = useState("20")
    const [batchSize, setBatchSize] = useState("5")
    const { toast } = useToast()
    const fileInputRef = useRef<HTMLInputElement>(null)
  
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files) {
        const newFiles = Array.from(e.target.files)
          .filter((file) => file.name.endsWith(".pdf"))
          .map((file) => ({
            id: `${file.name}-${file.lastModified}-${file.size}`,
            file,
            boxNumber: "",
            status: "pending" as const,
          }))
  
        const uniqueNewFiles = newFiles.filter(
          (nf) => !files.some((ef) => ef.id === nf.id)
        )
  
        setFiles((prev) => [...prev, ...uniqueNewFiles])
  
        if (uniqueNewFiles.length !== Array.from(e.target.files).length) {
          toast({
            title: "部分文件已处理",
            description: "已忽略非PDF格式或重复的文件。",
            variant: "default",
          })
        }
      }
      e.target.value = "" // Reset input to allow re-selecting the same file
    }
  
    const handleSelectFileClick = () => {
      fileInputRef.current?.click()
    }
  
    const handleBoxNumberChange = (id: string, value: string) => {
      setFiles((prev) =>
        prev.map((f) => (f.id === id ? { ...f, boxNumber: value } : f))
      )
    }
  
    const handleRemoveFile = (id: string) => {
      setFiles((prev) => prev.filter((f) => f.id !== id))
    }
  
    const handleClearAll = () => {
      setFiles([])
    }
  
    const uploadFile = useCallback(async (fileToUpload: UploadableFileWithProgress) => {
        const response = await uploadPdfService(
            fileToUpload.file,
            fileToUpload.boxNumber,
            useParallel,
            (progress) => {
                setFiles((prev) =>
                  prev.map((f) => {
                    if (f.id !== fileToUpload.id) return f;
                    
                    // 当上传进度达到100%时，立即切换到"处理中"状态
                    if (progress.percentCompleted === 100) {
                      return { ...f, status: "processing", progress: 100 };
                    }
                    
                    return { ...f, progress: progress.percentCompleted };
                  })
                );
            }
        );

        // 收到服务器响应，更新为最终状态
        if (response.success) {
            setFiles((prev) =>
                prev.map((f) =>
                    f.id === fileToUpload.id ? { ...f, status: "success" } : f
                )
            )
        } else {
            setFiles((prev) =>
                prev.map((f) =>
                    f.id === fileToUpload.id
                        ? { ...f, status: "error", error: response.error || "上传失败" }
                        : f
                )
            )
        }
    }, [useParallel]);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault()
  
      const filesToUpload = files.filter((f) => f.status === "pending")
      const incompleteFiles = filesToUpload.filter((f) => !f.boxNumber.trim())
  
      if (incompleteFiles.length > 0) {
        toast({
          title: "信息不完整",
          description: `有 ${incompleteFiles.length} 个文件缺少档案物理盒号。`,
          variant: "destructive",
        })
        return
      }
  
      if (useParallel && parseInt(batchSize, 10) > parseInt(chunkSize, 10)) {
        toast({
          title: "配置错误",
          description: "单次加载页数不能大于页面分块大小。",
          variant: "destructive",
        })
        return
      }
  
      if (filesToUpload.length === 0) {
        toast({
          title: "没有待上传的文件",
          description: "请选择文件或清空已完成的列表后重试。",
          variant: "destructive",
        })
        return
      }
  
      setFiles((prev) =>
        prev.map((f) =>
          f.status === "pending" ? { ...f, status: "uploading" } : f
        )
      )

      const uploadQueue = [...filesToUpload];

      const worker = async () => {
          while(uploadQueue.length > 0) {
              const fileToWorkOn = uploadQueue.shift();
              if(fileToWorkOn) {
                  await uploadFile(fileToWorkOn);
              }
          }
      }

      const workers = Array(CONCURRENT_UPLOADS).fill(null).map(() => worker());
      
      await Promise.all(workers)
  
      toast({
        title: "批量上传处理完成",
        description: "请在文件列表中查看各文件的上传结果。",
      })
    }
  
    const isUploading = files.some((f) => f.status === "uploading")
    const pendingFiles = files.filter((f) => f.status === "pending")
    const isReadyToUpload =
      pendingFiles.length > 0 && pendingFiles.every((f) => f.boxNumber.trim())
    const incompleteFiles = pendingFiles.filter((f) => !f.boxNumber.trim())

    return {
        files,
        useParallel,
        setUseParallel,
        chunkSize,
        setChunkSize,
        batchSize,
        setBatchSize,
        fileInputRef,
        handleFileChange,
        handleSelectFileClick,
        handleBoxNumberChange,
        handleRemoveFile,
        handleClearAll,
        handleSubmit,
        isUploading,
        isReadyToUpload,
        incompleteFiles
    }
} 