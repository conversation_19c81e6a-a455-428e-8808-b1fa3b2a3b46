# Operation Document: Configure Celery Beat for Daily Cleanup of Expired Sessions

## 📋 Change Summary

**Purpose**: To set up Celery Beat for periodically running a task that cleans up expired import sessions. This ensures that system resources are reclaimed and the database is kept tidy.
**Scope**:

- `requirements.txt`: Added `django-celery-beat`.
- `archive_flow_manager/settings.py`: Added `django_celery_beat` to `INSTALLED_APPS`.
- `archive_records/tasks.py`: Added a new Celery task `run_cleanup_expired_sessions_task`.
- `docker-compose.yml`: Added a new `beat` service for Celery Beat.
**Associated**: Corresponds to task "二.1 定时任务配置" in `remaining_excel_import_refactor_plan.md`.

## 🔧 Operation Steps

### ⚙️ OP-001: Add Dependency

**Precondition**: `django-celery-beat` is not listed in `requirements.txt`.
**Operation**: Added `django-celery-beat>=2.5.0` to `requirements.txt`.
**Postcondition**: Dependency for Celery Beat scheduling via database is added.

### ⚙️ OP-002: Configure Django App

**Precondition**: `django-celery-beat` is not in `INSTALLED_APPS`.
**Operation**: Added `'django_celery_beat'` to the `INSTALLED_APPS` list in `archive_flow_manager/settings.py`.
**Postcondition**: Django is configured to recognize `django-celery-beat` and its models. Database migrations will be needed.

### ✏️ OP-003: Create Celery Task for Cleanup

**Precondition**: No specific Celery task exists for cleaning *expired* sessions (as opposed to *cancelled* ones).
**Operation**: Created a new shared Celery task `run_cleanup_expired_sessions_task` in `archive_records/tasks.py`. This task instantiates `ImportSessionManager` and calls its `cleanup_expired_sessions(user_id=None)` method.
**Postcondition**: A Celery task is available to be scheduled by Celery Beat for cleaning expired sessions.

### 🐳 OP-004: Add Celery Beat Service to Docker Compose

**Precondition**: `docker-compose.yml` does not have a service definition for Celery Beat.
**Operation**: Added a new service named `beat` to `docker-compose.yml`.

- It uses the same build context and environment variables as the `worker` service.
- The command is set to `celery -A archive_flow_manager beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler`.
- It depends on `redis`, `db`, and `web` services.
**Postcondition**: The Docker environment is configured to run a Celery Beat process.

### 🗄️ OP-005: Database Migrations (Implicit)

**Precondition**: `django-celery-beat` tables are not in the database.
**Operation**: The `web` service in `docker-compose.yml` already includes `python manage.py migrate` in its startup command. This will create the necessary tables for `django-celery-beat` when the services are next (re)started.
**Postcondition**: Database schema will be updated to support `django-celery-beat`.

### 📖 OP-006: Provide Instructions for Admin Configuration

**Precondition**: Celery Beat is set up to run, and the cleanup task is defined.
**Operation**: Formulated instructions for the user to configure a periodic task in the Django Admin interface to run `archive_records.tasks.run_cleanup_expired_sessions_task` daily at 2 AM using a Crontab schedule.
**Postcondition**: User has guidance on how to schedule the task via Django Admin.

## 📝 Change Details

1. **`requirements.txt`**: Added `django-celery-beat>=2.5.0`.
2. **`archive_flow_manager/settings.py`**: Added `'django_celery_beat'` to `INSTALLED_APPS`.
3. **`archive_records/tasks.py`**: Introduced `run_cleanup_expired_sessions_task`:

    ```python
    from .services.import_session_manager import ImportSessionManager

    @shared_task(name="archive_records.tasks.run_cleanup_expired_sessions_task")
    def run_cleanup_expired_sessions_task():
        logger.info("定时任务开始：清理过期的导入会话...")
        try:
            manager = ImportSessionManager()
            result = manager.cleanup_expired_sessions(user_id=None)
            logger.info(f"定时任务：清理过期会话完成。结果: {result}")
            return result
        except Exception as e:
            logger.error(f"定时任务：清理过期会话时发生错误: {e}", exc_info=True)
            return {"status": "error", "reason": str(e)}
    ```

4. **`docker-compose.yml`**: Added the `beat` service definition:

    ```yaml
      beat:
        build: .
        container_name: archive-flow-beat
        command: >
          sh -c "celery -A archive_flow_manager beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler"
        volumes:
          - .:/app
        environment: # ... (same as worker)
        depends_on:
          - redis
          - db
          - web
        networks:
          - app-network
        restart: unless-stopped
    ```

## ✅ Verification Results

**Method**: Code modification and logical review.
**Results**: All necessary code and configuration changes for setting up Celery Beat with `django-celery-beat` to run a daily cleanup task have been implemented. The system is now prepared for scheduling this task.
**Problems**: The actual scheduling of the task needs to be done manually via the Django Admin interface by the user after the services are deployed/restarted.
**Solutions**: Clear instructions for Django Admin configuration have been provided to the user.
