# Operation Document: 为异步Excel确认导入添加进度跟踪

## 📋 Change Summary

**Purpose**: 在之前异步化的Excel确认导入流程中，实现详细的进度跟踪功能，以便前端可以展示导入操作的实时进展。
**Scope**:

- `archive_records/services/excel_import.py`: 修改核心导入服务以接收并更新`ImportSession`模型的进度字段。
- `archive_records/services/import_session_manager.py`: 修改会话管理服务以初始化进度字段并传递`ImportSession`实例。
- (`archive_records/tasks.py` 无需直接修改进度逻辑，但其调用的服务层已更新)。
**Associated**: 用户希望能够跟踪异步导入的进度，并将其整合到现有的进度显示机制中。

## 🔧 Operation Steps

### 📊 OP-001: 分析与设计进度跟踪方案

**Precondition**: Excel确认导入已通过Celery实现异步化。
**Operation**:

1. 确定使用现有的 `ImportSession` 模型中的 `record_count` (总记录数 - 本次导入步骤), `current_record` (已处理记录数), 和 `progress` (百分比) 字段来存储确认导入阶段的进度。
2. 设计方案：
    a.  在 `ImportSessionManager._execute_import_with_resolutions` 方法开始执行实际导入（调用 `ExcelImportService`）之前，获取本次确认导入步骤将处理的实际记录总数，并用此数量更新 `ImportSession` 的 `record_count` 字段，同时将 `current_record` 和 `progress` 重置为0。
    b.  修改 `ExcelImportService.import_from_file` 及其内部调用的 `_process_dataframe` 方法，使其接受一个可选的 `ImportSession` 实例作为参数。
    c.  在 `ExcelImportService._process_dataframe` 方法的记录处理循环中（例如，在每个批次处理完成后），如果传入了 `ImportSession` 实例，则更新其 `current_record` 和 `progress` 字段，并保存到数据库。
    d.  前端轮询现有的 `ExcelImportAnalysisProgressView` (或类似视图) 即可获取这些更新后的进度信息，只需根据 `ImportSession.status` 判断当前进度属于哪个阶段（分析或导入）。
**Postcondition**: 进度跟踪方案设计完成。

### ✏️ OP-002: 修改代码实现进度跟踪

**Precondition**: 方案已设计。
**Operation**:

1. **修改 `archive_records/services/excel_import.py`**:
    - `ExcelImportService.import_from_file` 方法签名添加 `import_session: Optional[ImportSession] = None` 参数，并将其传递给 `_process_dataframe`。
    - `ExcelImportService._process_dataframe` 方法签名添加 `import_session: Optional[ImportSession] = None` 参数。在其主处理循环的每个批次完成后，如果 `import_session` 非空，则计算并更新 `import_session.current_record` 和 `import_session.progress`，然后调用 `import_session.save()`。
2. **修改 `archive_records/services/import_session_manager.py`**:
    - 在 `ImportSessionManager._execute_import_with_resolutions` 方法中，在确定了实际要处理的数据集 `df_to_process` 后、调用 `self.import_service.import_from_file` 之前：
        - 设置 `db_session.record_count = len(df_to_process)`。
        - 设置 `db_session.current_record = 0`。
        - 设置 `db_session.progress = 0.0`。
        - 调用 `db_session.save()`。
        - 在调用 `self.import_service.import_from_file` 时，将 `db_session` 作为 `import_session` 参数传递。
**Postcondition**: 后端代码修改完成，以支持在异步确认导入期间跟踪并更新进度。

## 📝 Change Details

### CH-001: 修改 `ExcelImportService`

**File**: `archive_records/services/excel_import.py`
**Key Changes**:
- `import_from_file` 方法新增 `import_session`可选参数。
- `_process_dataframe` 方法新增 `import_session`可选参数。
- `_process_dataframe` 内部增加逻辑：在每个批次处理后，如果 `import_session` 有效，则更新其 `current_record` 和 `progress` 并保存。

### CH-002: 修改 `ImportSessionManager`

**File**: `archive_records/services/import_session_manager.py`
**Key Changes**:
- 在 `_execute_import_with_resolutions` 中，调用 `ExcelImportService.import_from_file` 之前，重置/设置 `ImportSession` 实例的 `record_count`, `current_record`, `progress` 字段以反映当前导入步骤的范围。
- 将 `ImportSession` 实例传递给 `ExcelImportService.import_from_file`。

**Rationale**: 这些修改使得在Celery任务执行冗长的Excel导入操作时，能够分阶段地将进度信息持久化到数据库，从而允许前端通过API轮询来展示一个动态的进度条或百分比。
**Potential Impact**:

- 增加了导入过程中的数据库写操作（用于更新进度）。对于非常大的文件和非常频繁的进度更新（如果批次设置过小），可能需要关注性能。当前的批次更新应能平衡此问题。
- 前端需要能够正确地解释 `ImportSession` 在不同状态下的进度值。

## ✅ Verification Results

**Method**: 代码审查和逻辑推断。
**Results**: 该方案应能有效地实现异步导入的进度跟踪。
**Problems**: 需前端配合才能完整验证用户体验。需关注高并发或超大数据量下进度更新的性能。
**Solutions**: 告知用户前端需要适配显示新阶段的进度。建议在压力测试中观察进度更新的性能影响。
