# 详细工作计划与日志 (Detailed Work Plan & Log)

## 区域一：文档指南与项目概览 (Document Guide & Project Overview)

## 说明

本文档用于跟踪**当前主要开发阶段**的核心任务列表及其状态。**由于历史提交信息不规范，本文档也承担部分关键已完成任务的记录功能，以提供必要的上下文**。它比高层路线图更具体，但比开发检查点 (`ai_dev_checkpoint.md`) 更宏观。

**状态标记**:

* `[ ]` To Do (待办)
* `[>]` In Progress (进行中)
* `[x]` Done (已完成)
* `[-]` On Hold / Deferred (暂缓/推迟)
* `[!]` Blocked (受阻)

### 文档体系协作说明

重要说明：*(本部分说明本文档如何与其他规划文档协同工作)*

1. **`planning_and_requirements/project_vision_and_roadmap.md` (项目愿景与路线图)**:
    * **角色**: 这是最高层级的文档，定义项目的**"为什么"**（业务目标、核心价值）和**长期的"什么"**（主要功能模块、大致阶段划分、关键架构决策）。它应该保持简洁，聚焦战略方向，避免陷入具体任务细节。
    * **协同**:
        * `detailed_work_plan_and_log.md` 的 **区域一 (文档指南与项目概览)** 会引用或链接到 `project_vision_and_roadmap.md`，从中提取核心目标和流程作为本详细计划的上下文。
        * `detailed_work_plan_and_log.md` 的 **区域六 (项目级信息与风险)** 可能会包含一些从路线图文档中提炼出的、贯穿整个项目的风险或关键信息。

2. **`planning_and_requirements/detailed_work_plan_and_log.md` (详细工作计划与日志 - 本文档)**:
    * **角色**: 这是**承上启下**的核心文档，负责将高层路线图分解为**可执行的、详细的"什么"和"如何"**。它跟踪当前阶段的具体任务、记录已完成的工作、管理待办事项，并存档历史计划。它是项目执行层面的主要参考。
    * **协同**:
        * **区域二 (当前阶段核心任务与状态)** 是当前开发活动的焦点，详细说明**现在正在做什么**。
        * **区域三 (近期规划、遗留与暂缓任务)** 管理**接下来要做什么**以及需要稍后处理的任务。
        * **区域四 (详细完成历史记录)** 提供了比路线图更详细的**"做完了什么"**的记录。
        * **区域五 (特定历史计划归档)** 存档了**过去某个重要部分是如何规划的**。

3. **`planning_and_requirements/ai_dev_checkpoint.md` (AI 开发检查点)**:
    * **角色**: 这是最底层的、**高频更新**的日志，记录**每次迭代的**'如何做'——具体的进展、遇到的问题、下一步的小计划。它非常注重时效性和细节。
    * **核心流程**: 文档遵循"查看-执行-分析-计划-更新文档-提交"的迭代循环，详细记录每次迭代的完成内容和后续计划。
    * **协同**:
        * **信息流动**: `ai_dev_checkpoint.md` 记录了迭代的微观进展。这些信息需要在特定时点同步到 `detailed_work_plan_and_log.md` 以将微观进展整合到项目的完整计划与历史记录中。
        * **提交准备时的同步要求**: **在做提交准备时**，为确保文档状态与代码提交一致，必须完成以下文档更新：
            1. **更新检查点文档 (`AgentReadme/planning_and_requirements/ai_dev_checkpoint.md`)**: 准确记录本次迭代完成的工作和确定的下一步计划。
            2. **更新核心计划文档 (`AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md`)**:
                * **同步任务状态与细节**:  
                    * 更新本文件 **区域二** 中对应父任务的整体状态 (`[>]` 或 `[x]`)。
                    * 将迭代中新发现的、为完成该父任务所必需的**子任务或步骤**，根据其完成情况，作为子项添加到区域二的父任务下（例如，标记为 `[ ]` 或 `[x]`）。
                * **添加历史记录**: 将 `ai_dev_checkpoint.md` 中记录的、在本次迭代中完成的**独立小任务或步骤**（非区域二任务的子项）直接添加到本文件 **区域四**。
                * **添加新条目**: 将 `ai_dev_checkpoint.md` 中识别出的需长期跟踪的遗留问题或新近期规划添加到本文件 **区域三**。
            3. **更新其他相关文档 (若适用)**:
                * 测试日志 (`AgentReadme/testing_logs/` 目录下的相关文件)
                * 特定功能计划 (`AgentReadme/active_feature_plans/` 目录下的相关文件)

4. **`guides/testing_guide.md` (测试指南)**: (位于 `guides/` 目录)
    * **角色**: 这是一个**专题文档**，专门说明如何进行测试（环境设置、命令、策略）。
    * **协同**: 当 `detailed_work_plan_and_log.md` 中的任务涉及到测试时（尤其是在区域二或区域四的完成记录中），可以简单地**引用或链接**到 `testing_guide.md` 中的相关部分，避免在计划文档中重复测试细节。

---

## 总体项目目标与核心流程 (Project Goal & Core Workflow)

**目标**: 开发一个档案流管理系统，支持用户上传 PDF 文件并指定预归档物理盒号，后台自动处理（分割、提取统一编号），与现有档案台账系统关联，最终完成物理归档。

**核心流程 (更新 v3 - 加入严格预检查)**:

1. **上传**: 用户通过 `PDFUploadView` 逐个上传 PDF 文件，每次上传**必须指定**该 PDF 对应的物理盒号 (`assigned_box_number`)。
2. **保存与任务创建**: `UploadService` 验证文件，保存到临时存储，并为**每次上传**创建一个 `UploadedFile` 记录（存储文件信息和指定的 `assigned_box_number`）和**一个**关联的 `ProcessingTask` 记录（存储任务参数，包括 `assigned_box_number`）。
3. **触发独立任务**: `TaskService` (或信号) 将**每个** `ProcessingTask` 的 `task_id` 提交给 Celery 队列，触发一个独立的 `process_pdf_task` 作业。
4. **Celery 并行处理**: 多个 Celery Worker 进程**并行地**从队列中获取任务，每个 Worker 独立执行一个 `process_pdf_task(task_id)` 实例。
5. **信息提取 (`process_pdf_task` 内部)**:
    * 获取当前 `task_id` 对应的 `ProcessingTask` 和 `UploadedFile` 记录，包括 PDF 文件路径和 `assigned_box_number`。
    * 实例化 `PdfProcessingService`。
    * 调用 `pdf_service.process_pdf_for_splitting_info(pdf_path, target_text)` 获取处理结果 `result_dto` (包含 `split_points`, `unified_numbers`, `stats`)。
6. **严格预检查 (`process_pdf_task` 内部)**:
    * **在文件操作前**，检查 `PdfProcessingService` 返回结果的完整性：
        * 是否所有部分都识别出 `unified_number`？
        * (如果都识别出) 调用 `record_update_service.check_records_exist` 检查所有 `unified_number` 是否在数据库中有记录？
    * 如果**任何检查失败**，记录详细错误，标记任务失败，**中止后续所有文件操作**。
7. **物理分割与归档 (`process_pdf_task` 内部，仅当预检查通过)**:
    * 调用 `pdf_utils.calculate_part_ranges(...)` 计算页面范围。
    * 循环处理每个 `part_range`：
        * 生成临时输出路径。
        * 调用 `pdf_utils.create_temp_pdf_for_single_archive(...)` 将页面写入临时文件。
        * 调用 `FileStorageService.archive_single_archive_pdf(...)` (接口已通过集成测试初步验证) 将临时文件移动到最终归档路径，并获取最终路径。
        * 收集归档结果 `archived_files: List[(final_path, unified_number)]`。
8. **关联与状态更新 (`process_pdf_task` 内部，仅当预检查通过)**:
    * 调用 `processing_report_utils.create_result_summary(...)` 生成报告。
    * 循环处理 `archived_files` 列表。
    * 对于每个成功归档的部分 `(final_path, unified_number)`：
        * 调用 `record_update_service.update_archive_record(...)` (接口已通过集成测试初步验证) 更新数据库记录状态、URL 和盒号。
    * `process_pdf_task` 根据整体处理结果更新 `ProcessingTask` 的最终状态和 `result_data`。
9. **状态查询与重试**: 用户可通过 API (`TaskStatusView`, `ProcessingResultView` - 待实现) 查询各任务进度和结果，通过 API (`RetryProcessingView` - 待实现) 对处理失败的**特定任务**进行重试。

---

## 区域二：当前阶段核心任务与状态 (Current Phase Core Tasks & Status)

(*本区域列出当前开发迭代正在积极处理的核心任务。当本区域所有任务完成后，应从区域三选择下一批任务迁移至此。*)

### Phase 3 主要任务与状态

#### 1.增强系统健壮性与反馈 (#AFM-Robustness)

* `[x]` **修复任务并发处理机制** (#AFM-Task-Concurrency)
  * ✓ 实现数据库事务锁防止任务重复执行
  * ✓ 测试并确认修复是否解决了状态冲突问题
  * ✓ 优化装饰器顺序与数据模型字段引用

* `[ ]` **解决 Celery Worker OOM 问题**: #AFM-Celery-OOM (详见 [问题描述](AgentReadme/question_temporarily_stored/issue_celery_worker_oom_sigkill.md))
  * *(根本原因：并行 OCR 任务内存超限导致 Worker 被 SIGKILL)*

* `[ ]` **错误处理优化**
  * 在 `tasks.py` 和相关服务中添加更细致的错误捕获和日志记录
  * 实现任务状态反馈前端展示
  * 实现处理结果前端展示
  * 实现基本任务重试机制

* `[x]` **修复任务并发处理机制**: #AFM-Task-Concurrency
  * `[x]` 实现数据库事务锁防止任务重复执行。
  * `[x]` 测试并确认修复是否解决了状态冲突问题。
  * `[x]` 优化装饰器顺序，确保 Celery 任务被正确注册
  * `[x]` 修复数据模型字段引用，使用模型自带的时间戳字段
  * `[ ]` 完善错误处理逻辑。
  * `[ ]` 实现任务状态反馈前端展示。
  * `[ ]` 实现处理结果前端展示。
  * `[ ]` 实现基本任务重试机制。
  
* `[ ]` **大型PDF处理效率提升**: #AFM-32
  * `[ ]` 实现PDF处理并行化架构 (Celery Chord)
  * `[ ]` 创建SubTask数据模型
  * `[ ]` 规划主任务拆分与子任务协调机制

#### 2.增强 Streamlit 演示前端 (#AFM-Demo-UI-Update)

* `[>] (P1)` **演示功能优先实现**: #AFM-Demo
  * 目标：确保演示系统能够稳定运行，优先实现核心演示功能。
* `[x]` **实现 Streamlit 演示前端 (基础)** (`-frontend` 目录): #AFM-Demo-Frontend
  * `[x]` 创建 Streamlit 脚本 (`demo_app.py`)。
  * `[x]` 添加 Excel 和 PDF 上传控件。
  * `[x]` 实现调用后端 API (`/api/import-excel/`, `/api/upload/`) (已修复 URL 和权限问题)。
  * `[x]` 添加依赖 (`streamlit`, `requests`)。
* `[x]` **配置后端 CORS**: #AFM-Demo-CORS
  * `[x]` 修改 Django `settings.py` 允许 Streamlit 来源。
* `[x]` **修复运行环境问题**: #AFM-Runtime-Fix
  * `[x]` 搭建 Docker 环境解决 Celery 启动和基础库依赖 (OpenCV, GLib) 问题。
  * `[x]` 添加兼容性路由解决前端 URL 404 问题 (查看 ai_operation_logs/20250418_excel_upload_fix.md)。
  * `[x]` 修复 Celery 任务参数传递问题，使其与函数定义匹配 (查看 ai_operation_logs/20250418_celery_task_param_fix.md)。
  * `[x]` 修复 PDF 处理服务参数传递问题 (查看 ai_operation_logs/20250418_celery_task_param_fix.md 相关讨论)。
  * `[x]` 修复 PDF 处理流程中函数调用参数错误 (create_temp_pdf..., create_result_summary...)。
  * `[x]` 修复 PDF 处理流程中 UnboundLocalError 问题。
  * `[x]` 升级 Dockerfile 基础镜像至 python:3.11。
  * `[x]` 修复 docker-compose.yml 中前端环境变量配置。
* `[x]` **准备测试数据**: #AFM-Demo-Data
  * `[x]` 准备符合格式的 Excel 台账文件。
  * `[x]` 准备包含对应统一编号的 PDF 文件。
* `[x]` **验证端到端流程**: #AFM-Demo-E2E
  * `[x]` 验证 Excel 上传与数据库记录填充。
  * `[x]` 验证 PDF 上传、处理（含 PaddleOCR 正常运行）与文件归档。
  * `[x]` (暂缓/下一步) 验证数据库记录更新（需先运行迁移）。
* `[x]` **增强 Streamlit 前端 (新)**: #AFM-Demo-UI-Update
  * `[x]` 添加档案台账显示区域（含文件链接功能）。
  * `[ ]` 实现动态刷新机制。
* **[-] API 端点实现** (P1 待办 - 暂缓): #AFM-12, #AFM-14
  * `[-]` **完善文件上传 API (`PDFUploadView`)**:
    * `[-]` 验证现有逻辑是否满足全部需求 (盒号传递等)。
    * `[-]` 确保文件验证逻辑健壮 (类型、大小限制等)。
    * `[-]` 实现必要的权限检查。
    * `[-]` 完善错误处理与响应 (返回清晰错误信息)。
    * `[-]` 改进成功响应 (提供任务 ID 和初始状态)。
  * `[-]` **实现任务状态 API (`TaskStatusView`)**:
    * `[-]` 实现通过 Task ID 查询任务状态和进度。
    * `[-]` 考虑支持轮询或更高级状态更新机制 (待定)。
    * `[-]` 设计并实现结构化的状态响应 JSON。
  * `[-]` **实现处理结果 API (`ProcessingResultView`)**:
    * `[-]` 实现通过 Task ID 查询详细处理结果 (分割点、编号、统计等)。
    * `[-]` 提供处理报告文本内容的接口。
    * `[-]` (后续) 可能需要提供分割后 PDF 的下载链接。
  * `[-]` **实现任务重试 API (`RetryProcessingView`)**:
    * `[-]` 实现通过 Task ID 触发失败任务的重试逻辑。
    * `[-]` 考虑是否允许用户在重试时调整参数 (待定)。
    * `[-]` 确保重试任务与原任务的关联。
* **[-] 前端交互优化** (暂缓):
  * `[-]` 审视并统一所有 API 的响应格式，确保简洁清晰。
  * `[-]` 确保所有用户可见的错误消息友好且包含指引。
  * `[-]` 根据需要实现 CORS 配置。

---

## 区域三：近期规划、遗留与暂缓任务 (Near-Term Plan, Legacy & Deferred Tasks)

(*本区域管理计划在当前阶段核心任务完成后接续进行的工作、从之前阶段遗留下的任务、或暂时推迟的任务。当区域二任务清空时，将从此区域选择任务迁移至区域二。*)

### Phase 2 遗留任务 (需要在 Phase 3 或之后处理)

* **[ ] PDF 处理器重构收尾**:
  * `[ ]` **最终清理 `pdf_processor_usefull.py`**: (原标记 🔄) 处理或移除 `benchmark_pdf_processing` 等剩余辅助函数。确认无任何代码依赖此文件后，删除或重命名。 #AFM-29
  * `[ ]` **完善单元测试**: (原标记 P2 待办) 为 `FileStorageService` 添加单元测试，覆盖归档和路径生成的边缘情况。(确认 `tasks.py` 调用 `get_temp_directory()` 已完成 ✅) #AFM-29

### 暂缓/待定任务 (需后续评估)

* **[-] 其他待办**:
  * `[-]` 实现 `ArchiveIntegrationService` (原标记 P1 待办 - 需确认是否还需要，或已被覆盖) #AFM-13
  * `[-]` 下载与权限管理API (原标记 P2 待办 - 归入 Phase 4?) #AFM-待定
  * `[-]` 基础设施与维护 (原标记 P2 待办 - 归入 Phase 5?) #AFM-17
    * `[-]` Celery 配置优化
    * `[-]` 错误处理审视
    * `[-]` 备份策略实现

### 未来优化项 (Future Optimizations)

* **[ ] Excel导入功能全面优化与增强**: #AFM-Excel-Import-Enhance
  * `[ ]` **现状分析与计划制定**: 详细分析当前Excel导入功能的问题，并制定分阶段优化计划。参考文档：[Excel导入功能现状分析与增强计划](AgentReadme/design/excel_import_enhancements.md)
  * `[ ]` (P0) 核心功能修复与健壮性提升 (事务管理、字段校验)
  * `[ ]` (P1) 用户体验与可维护性优化 (错误报告、预检机制、配置灵活性)
  * `[ ]` (P2) 性能与高级功能 (性能调优、高级数据处理、自动重试)

* **[ ] 档案记录处理流程实现**: #AFM-Record-Process
  * `[ ]` **设计条目处理策略**:
    * `[ ]` 实现四类条目分类逻辑（新条目、内容一致重复条目、已归档差异条目、未归档差异条目）
    * `[ ]` 添加已归档条目保护机制，防止意外修改
    * `[ ]` 实现条目差异识别和比较功能
  * `[ ]` **差异汇总与即时确认界面**:
    * `[ ]` 设计差异汇总表，显示各类条目数量统计
    * `[ ]` 实现未归档差异条目详情导出功能
    * `[ ]` 开发整体决策界面（接受全部/拒绝全部按钮）
    * `[ ]` 实现二次确认机制，清晰展示操作影响
  * `[ ]` **批次管理系统**:
    * `[ ]` 设计批次数据模型，关联Excel原文件和处理结果
    * `[ ]` 实现多维度批次查询功能（操作人员、日期范围、处理结果）
    * `[ ]` 开发批次详情展示页面
  * `[ ]` **完善导入日志**:
    * `[ ]` 扩展现有日志模型，增加决策记录和详细统计
    * `[ ]` 实现每条记录的处理结果查询功能
    * `[ ]` 添加元数据记录（操作用户、客户端信息等）
  * `[ ]` **性能优化**:
    * `[ ]` 优化Excel数据导入查重入库流程，实现批量查询
    * `[ ]` 添加数据库索引优化，提高查询效率
    * `[ ]` 实现大批量数据处理的分组处理机制
  * `[ ]` **端到端测试与验证**:
    * `[ ]` 编写完整的端到端测试，验证各类条目处理路径
    * `[ ]` 用户体验测试与优化

* **[ ] 已归档记录正式更改管理**: #AFM-Archive-Change
  * `[ ]` **基础数据模型**:
    * `[ ]` 设计并实现更改单模型（ChangeOrder）
    * `[ ]` 设计并实现变更详情模型（ChangeDetail）
    * `[ ]` 扩展ArchiveRecord模型，添加正式变更次数统计方法
    * `[ ]` 实现更改单与档案条目的关联机制
  * `[ ]` **更改单创建功能**:
    * `[ ]` 开发更改单创建界面，支持多条目选择
    * `[ ]` 实现已归档条目筛选和校验机制
    * `[ ]` 开发字段编辑组件，支持显示原值和输入新值
    * `[ ]` 实现更改单预览与打印功能
  * `[ ]` **更改单提交与执行**:
    * `[ ]` 开发变更执行机制，支持批量更新
    * `[ ]` 实现变更数据的完整记录
    * `[ ]` 实现扫描件上传与关联功能
    * `[ ]` 添加更改单提交确认流程
  * `[ ]` **更改单管理功能**:
    * `[ ]` 开发更改单列表界面
    * `[ ]` 实现更改单查询与筛选功能
    * `[ ]` 开发更改单详情查看页面
    * `[ ]` 实现更改单删除与回滚功能
  * `[ ]` **权限控制**:
    * `[ ]` 开发更改单相关权限管理
    * `[ ]` 实现不同操作的权限检查机制
    * `[ ]` 添加操作审计日志

* **[ ] 操作错误修正机制**: #AFM-Error-Correction
  * `[ ]` **错误修正界面**:
    * `[ ]` 在条目详情页添加"错误修正"按钮
    * `[ ]` 实现原位编辑解锁机制
    * `[ ]` 开发简易错误说明录入功能（可选）
  * `[ ]` **修正记录跟踪**:
    * `[ ]` 实现修正操作的全程跟踪
    * `[ ]` 设计修正记录的数据结构
    * `[ ]` 开发修正记录自动生成功能
  * `[ ]` **权限与控制**:
    * `[ ]` 实现错误修正的权限控制
    * `[ ]` 开发操作频率限制机制
    * `[ ]` 添加重要字段修正的特殊处理

* **[ ] 操作追溯与历史记录系统**: #AFM-Change-History
  * `[ ]` **变更记录基础服务**:
    * `[ ]` 设计并实现RecordChange模型
    * `[ ]` 梳理并定义完整的变更来源分类
    * `[ ]` 开发通用的变更记录服务
    * `[ ]` 实现高效的数据库索引策略
  * `[ ]` **变更捕获机制**:
    * `[ ]` 开发模型变更自动捕获组件
    * `[ ]` 实现不同来源变更的标记机制
    * `[ ]` 开发字段级变更比较功能
  * `[ ]` **历史查询功能**:
    * `[ ]` 开发条目变更历史选项卡界面
    * `[ ]` 实现多维度筛选功能
    * `[ ]` 开发变更详情可视化组件
    * `[ ]` 实现变更历史导出功能
  * `[ ]` **安全与审计**:
    * `[ ]` 实现历史记录的完整性保护
    * `[ ]` 开发历史查询的权限控制
    * `[ ]` 添加查询操作的审计日志功能

* **[ ] 重构 Grid 状态本地存储逻辑**: #AFM-Refactor-GridStorage
  * `[ ]` 将档案台账页面直接操作 localStorage 的逻辑迁移到专用的 `gridStateService`。
  * `[ ]` 目标是统一存储访问、提高代码清晰度和可维护性。
  * `[ ]` [详细计划](AgentReadme/active_feature_plans/refactor_grid_state_storage.md)

* `[ ]` **事务处理优化**: 根据健壮性评估，优化事务处理机制。 #AFM-Trans-Opt
  * `[ ]` 实现批处理或分段事务，缓解长事务风险
  * `[ ]` 细化异常处理，区分不同类型的错误
  * `[ ]` 添加失败时的文件资源清理机制
  * `[ ]` 增强并发控制，添加锁超时设置防止死锁
  * `[ ]` 审查事务嵌套点，确保行为一致

---

## 区域四：详细完成历史记录 (Detailed Completion History)

(*本区域为已完成任务的归档区，记录完成的任务及子任务的详细信息。这比路线图更具细节，用于保留项目历史记录和关键开发决策。*)

### 系统健壮性提升

* `[x]` **修复process_pdf_task依赖问题 (2025.04.22)**: #AFM-refactor-atomic
  * 问题诊断:
    * 发现移除`process_pdf_task`后，应用启动时出现导入错误
    * 识别影响文件包括`views.py`和相关测试文件
  * 修复方案:
    * 更新`views.py`中的导入语句，使用`dispatch_pdf_processing`替代`process_pdf_task`
    * 保留导入以确保与测试文件的兼容性，尽管实际未直接使用该函数
    * 更新测试文件中`@patch`装饰器的模拟对象和相关断言
  * 代码改进:
    * 添加清晰的注释说明变更原因和当前代码状态
    * 确保测试文件与实际代码逻辑保持一致
  * 详细操作记录: `AgentReadme/ai_operation_logs/20250422_215047_fix_process_pdf_task_deps.md`

* `[x]` **PDF处理全部或无入库机制实现 (2025.04.25)**: #AFM-all-or-nothing
  * 完成子任务完整性验证:
    * 实现 `verify_all_subtasks_completed` 函数，验证所有子任务状态
    * 在第三阶段处理开始前添加子任务完整性验证
  * 实现事务一致性保证:
    * 将所有数据库更新操作包装在单一事务中
    * 确保任何处理失败都会触发整个事务回滚
  * 增强错误处理:
    * 添加详细的错误日志记录
    * 优化错误消息格式，提供清晰的问题定位信息
  * 完成测试验证:
    * 测试用例验证了事务完整性
    * 模拟了子任务验证失败、处理结果数量不一致、归档失败、数据库更新失败等场景
  * 健壮性评估结果：
    * 总体健壮性良好（评分：8/10）
    * 完整的事务隔离和严格的全部或无原则
    * 存在长事务风险和并发控制改进空间

* `[x]` **修复任务并发处理机制 (2025.04.20)**: #AFM-Task-Concurrency
  * 诊断问题：同一任务ID被多个Celery worker处理，导致状态冲突
  * 问题根源：缺少任务级锁机制，数据库事务未正确使用
  * 实现方案：
    * 使用数据库事务锁防止任务重复执行
    * 修复Celery装饰器顺序确保任务正确注册
    * 修复数据模型字段引用，使用模型自带时间戳字段
    * 删除views.py中重复的任务提交代码
  * 测试验证：所有修复通过本地和容器环境测试，解决状态冲突问题
  * 详细操作记录：`AgentReadme/ai_operation_logs/20250420_task_concurrency_fix.md`

### Phase 3: API与用户交互完善

#### 演示功能实现

* **[Bug修复]** 修复任务并发处理机制 #AFM-Task-Concurrency
  * ✅ 删除 `views.py` 中的重复 Celery 任务提交代码
  * ✅ 在 `process_pdf_task` 中添加 `@transaction.atomic` 装饰器和 `select_for_update()` 行级锁
  * ✅ 修复装饰器顺序问题（确保 `@shared_task` 在最外层）
  * ✅ 修复模型字段问题（移除对不存在的 `started_at` 字段的引用）
  * ✅ 完善操作日志记录，详见 `AgentReadme/ai_operation_logs/20250420_task_concurrency_fix.md`

* **[需求与设计]** 分析用户反馈，澄清并优化业务流程。
* **[需求与设计]** 确定核心架构：分离上传与处理，一个上传触发一个独立 Celery 任务，依靠 Celery Workers 实现任务间并行。
* **[基础]** 设置项目结构与基础配置。 #AFM-1
* **[模型]** 定义核心模型 (`UploadedFile`, `ProcessingTask`) 并应用迁移。 #AFM-2, #AFM-11
* **[上传]** 实现基础上传 API (`PDFUploadView`, Serializer) 和服务 (`UploadService` 基础)。 #AFM-5, #AFM-16
* **[任务]** 实现基础任务创建服务 (`TaskService`)。 #AFM-5, #AFM-16
* **[任务]** 实现 `process_pdf_task` 的基础框架 (状态更新、重试)。 #AFM-7
* **[工具]** 实现 API 响应标准化包装器。 #AFM-18
* **[逻辑]** 澄清 `assigned_box_number` 的处理逻辑和作用域（与单个任务绑定）。
* **[语法]** 修复 PDF 处理代码中的 Python 语法错误。
* **[计划]** 创建并持续更新详细的PDF处理器重构计划文档。 #AFM-29
* **[计划]** 确定并记录了使用 Celery Chord 实现PDF内部并行处理的优化策略 (待实施)。 #AFM-32
* **[服务]** 实现 `record_update_service.check_records_exist` 函数。✅ #AFM-Req1
* **[测试]** 修复早期测试问题。 #AFM-15, #AFM-6, #AFM-28
* **[测试]** 修复 PDF 文件操作测试用例，添加更有效的 PDF 测试文件生成逻辑。 #AFM-13, #AFM-28
* **[测试]** 完成文件归档与记录更新协同工作的集成测试 (`test_archiving_and_record_update_workflow.py`)。
* **[测试]** 完善测试环境，解决OpenMP运行时错误，添加详细测试指南文档。 #AFM-TestEnv
* **[测试]** 改进PDF处理集成测试，使用命名元组和具体属性的模拟对象，提高测试稳定性。 #AFM-TestStability
* **[Bug修复]** 修复 `create_result_summary` 函数在特定环境下（推测为 Windows + Python 3.11）写入报告标题失败的问题，采用预构建字符串再写入的方案。 #AFM-BugFix-ReportWrite

* **[Bug修复]** 添加兼容性路由解决前端 URL 404 问题。✅ #AFM-Runtime-Fix
* **[Bug修复]** 修复 Celery 任务 (`process_pdf_task`) 参数传递错误。✅ #AFM-Runtime-Fix
* **[Bug修复]** 修复 `PdfProcessingService` 初始化参数过滤问题。✅ #AFM-Runtime-Fix
* **[Bug修复]** 修复 `process_pdf_task` 中因变量初始化位置不当导致的 `UnboundLocalError`。✅ #AFM-Runtime-Fix
* **[Bug修复]** 修复 `process_pdf_task` 中对 `create_temp_pdf_for_single_archive` 和 `create_result_summary` 函数的参数调用错误。✅ #AFM-Runtime-Fix
* **[环境]** 升级 Dockerfile 基础镜像至 `python:3.11`，解决 PaddleOCR 导入问题。✅ #AFM-Runtime-Fix
* **[环境]** 修复 `docker-compose.yml` 中 `frontend` 服务的 `BACKEND_API_URL` 环境变量配置。✅ #AFM-Runtime-Fix

* **[后端API]** 创建 `ArchiveRecordSerializer` (含所有字段)、`ArchiveRecordListView` 和 URL 路由 (`/api/archive-records/list/`) 用于获取台账列表。✅ #AFM-Demo-UI-Update
* **[前端]** 在 Streamlit 演示应用中添加"查看档案台账"区域，实现数据刷新和显示。✅ #AFM-Demo-UI-Update
* **[前端]** 解决 Streamlit 台账分页显示不全的问题（通过 `page_size` 参数获取所有数据）。✅ #AFM-Demo-UI-Update
* **[前端]** 实现 Streamlit 台账中 `archive_url` 列的可点击文件链接功能。✅ #AFM-Demo-UI-Update
* **[前端]** 修复 Streamlit 文件链接指向 Docker 内部地址导致浏览器 404 的问题（改用 `localhost:8000`）。✅ #AFM-Demo-UI-Update

* **[环境]** 修复 Docker 环境中的 OCR 引擎问题，解决 PaddleOCR 类型注解"Variable not allowed in type expression"错误，使用 TYPE_CHECKING 和 Any 类型优化类型检查，保持代码功能不变。详细记录见 `AgentReadme/ai_operation_logs/20250420_174124_docker_ocr_fix.md`。✅ #AFM-Fix-Docker-OCR

* **[重构] PDF 处理器核心重构完成**: #AFM-8, #AFM-9, #AFM-10, #AFM-29, #AFM-31
  * ✅ 创建 `file_storage_service.py`，迁移文件存储、路径和备份相关功能。
  * ✅ 创建 `security_service.py`，迁移安全和访问控制相关功能。
  * ✅ 创建 `progress_tracking_service.py`，迁移进度跟踪和通知功能。
  * ✅ 创建 `image_utils.py`，迁移图像处理相关功能（图像预处理、增强、哈希计算）。
  * ✅ 创建 `system_utils.py`，迁移系统工具函数（CPU类型检测、时间格式化）。
  * ✅ 创建 `ocr_utils.py`，迁移核心OCR执行逻辑（引擎初始化、基础/增强OCR调用控制）。
  * ✅ 完善 `text_utils.py`，迁移文本处理逻辑 (预处理、匹配、提取、规范化、选择)。
  * ✅ 创建 `pdf_utils.py`，迁移PDF物理分割的底层操作 (范围计算、部分写入)。
  * ✅ 创建 `PdfProcessingService`服务，迁移并重构原`PDFProcessor`核心信息提取逻辑 (消除副作用)。
  * ✅ 在原`pdf_processor_usefull.py`文件中标记废弃类和方法，清理已迁移代码 (初步清理，剩余见区域三)。
  * ✅ 创建`processing_report_utils.py`，迁移并重构结果摘要生成逻辑。
  * ✅ **实现编排逻辑**: 初步重构 `process_pdf_task` (tasks.py) 完成，包含严格预检查逻辑。 #AFM-31, #AFM-32, #AFM-Req1-Strict
  * ✅ **实现文件归档**: `FileStorageService.archive_single_archive_pdf` 已实现并通过初步审查。
  * ✅ **调整报告工具**: 解决写入 Bug，并根据测试用例调整标题。
* **[重构] 替换 PyPDF2 为 PyMuPDF (#AFM-Refactor-PDFLib)**:
  * `[x]` 更新核心 PDF 处理文件 (pdf_utils.py, processing_report_utils.py, upload_service.py)
  * `[x]` 更新测试文件和 mock
  * `[x]` 更新 `requirements.txt`，移除 PyPDF2 依赖
  * `[x]` 更新 Docker 环境支持 PyMuPDF 和其他依赖:
    * `[x]` 添加 Poppler 依赖用于 pdf2image
    * `[x]` 添加 MuPDF 依赖用于 PyMuPDF
    * `[x]` 添加其他构建和图像处理相关依赖 (build-essential, libffi-dev, libjpeg-dev, zlib1g-dev)

* **[Bug分析]** 诊断并确认多个Celery worker同时处理同一任务ID的问题，制定使用数据库事务锁的解决方案。详细计划见 `AgentReadme/ai_operation_logs/20250420_184806_task_concurrency_fix_plan.md`。✅ #AFM-Task-Concurrency

### 任务处理重构与并行化 (2025.04.22) #AFM-Refactor-Parallel

* `[x]` **完成task中串行任务的原子化重构**:
  * 将单一大型函数拆分为多个职责明确的小函数。
  * 优化事务管理，避免长时间锁定数据库。
  * 增强错误处理与日志记录。
* `[x]` **实现task并行chord子任务模式**:
  * 设计三阶段并行处理模型：OCR识别、结果汇总、统一处理。
  * 实现基于Celery的chord任务链，确保子任务顺序执行。
  * 添加子任务状态管理和结果聚合逻辑。
* `[x]` **实现全部可处理验证机制**:
  * 新增 `validate_all_parts_processable` 函数用于入库前最终验证。
  * 修改串行和并行处理流程，集成验证步骤。
  * 确保"全部文件正常才能入库，任一不正常都不能入库"的要求。
* `[x]` **修复页码索引转换问题**:
  * 统一PyMuPDF (0-based) 与其他组件 (1-based) 的索引。
* `[x]` **解决报告生成问题**:
  * 修复并行模式下报告生成失败的问题。
* `[x]` **代码结构与质量改进**:
  * 修复代码结构问题，优化错误处理和日志记录。
  * 添加状态一致性检查辅助函数。
* `[x]` **性能优化**:
  * 实现任务执行错开调度，减少数据库锁冲突。
  * 优化临时文件管理和事务隔离。

---

## 区域五：特定历史计划归档 (Archived Feature Plans)

*(本区域用于存放指向已完成或归档的特定功能/特性详细规划文档的链接，作为历史参考。实际的详细计划文档应位于 `AgentReadme/completed_feature_plans/` 目录下。)*

* **PDF 处理器重构**: [请在此处添加指向 `AgentReadme/completed_feature_plans/feature_plan_pdf_refactoring.md` 的链接]
* ... *(未来可在此添加其他已归档计划的链接)*

---

## 区域六：项目级信息与风险 (Project-Level Information & Risks)

## 总体项目风险 (Overall Project Risks)

* **PDF 处理健壮性**: 新的 `PdfProcessingService` 和 `pdf_utils` 对异常 PDF 的处理能力和准确率有待在集成测试和实际使用中验证。
* **测试覆盖率**: 需要持续扩展测试覆盖范围，包括边缘情况的处理。
* **(延后)** Celery Chord 并行化引入的复杂性。

---
