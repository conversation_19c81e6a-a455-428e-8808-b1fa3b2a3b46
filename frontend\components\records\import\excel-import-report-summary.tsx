/**
 * Excel导入报告摘要组件
 * 
 * 简洁清晰地展示Excel导入操作的四个阶段统计：
 * 1. 文件解析阶段
 * 2. 条目分析阶段
 * 3. 导入执行阶段
 * 4. 总结
 */

import React from 'react';
import { 
  CheckCircle2, 
  AlertTriangle, 
  AlertCircle, 
  Info,
  FileText,
  Search,
  Database,
  BarChart3
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ==================== 接口定义 ====================

export interface DetailedErrorItem {
  row?: number;                    // Excel原始行号 (从1开始计数)
  field?: string;                  // 问题字段名
  message: string;                 // 主要错误信息
  commissionNumber?: string;      // 委托编号（如果有的话）
  sampleNumber?: string;          // 样品编号（如果有的话）
}

export interface ImportDetailedReport {
  errors?: readonly DetailedErrorItem[]; 
  importTaskProcessingErrors?: readonly DetailedErrorItem[]; 
}

export interface ImportReportData {
  // --- 元数据 ---
  fileName?: string;
  fileSize?: number;
  fileHash?: string;
  batchNumber: string;
  status: 'completed' | 'partial' | 'failed';
  errorMessage?: string;
  detailedReport?: ImportDetailedReport;
  importDate?: string;
  processingTime?: number;
  createdBy?: {
    id: number;
    username: string;
  } | null;
  importUser?: {
    id: number;
    username: string;
  } | null;

  // --- 文件解析阶段 ---
  analysisTotalRowsRead?: number;
  analysisSuccessfullyParsedRows?: number;
  analysisFailedRows?: number;

  // --- 条目分析阶段 ---
  analysisFoundNewCount?: number;
  analysisFoundUpdateCount?: number;
  analysisSkippedIdentical?: number;

  // --- 用户决策阶段 ---
  userDecisionSkippedUpdateCount?: number;
  userDecisionConfirmedUpdateCount?: number;

  // --- 导入执行阶段 ---
  importTaskTotalRecordsSubmitted?: number;
  importTaskCreatedCount?: number;
  importTaskUpdatedCount?: number;
  importTaskUnchangedCount?: number;
  importTaskProcessedSuccessfullyCount?: number;
  importTaskFailedCount?: number;

  // --- 总结 ---
  overallTotalInitialRecords?: number;
  overallFinalCreatedCount?: number;
  overallFinalUpdatedCount?: number;
  overallSkippedTotal?: number;
  overallFailedTotal?: number;
  overallProcessedSuccessfullyTotal?: number;
  
  importLogId?: string | null;
}

export interface ExcelImportReportSummaryProps {
  reportData: ImportReportData | null;
  excelColumnNameMapping?: Record<string, string>; 
}

// ==================== 辅助函数 ====================

/**
 * 渲染错误详情，优先显示委托编号
 */
function renderErrorItem(error: DetailedErrorItem, index: number): React.ReactNode {
  let identifier = '';
  
  if (error.commissionNumber) {
    identifier = `委托编号: ${error.commissionNumber}`;
  } else {
    identifier = `错误记录${index + 1}`;
  }

  return (
    <div key={index} className="flex items-start gap-2 p-2 bg-red-50 rounded border-l-4 border-red-400">
      <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
      <div className="flex-1 text-sm">
        <span className="font-medium text-red-800">{identifier}: </span>
        <span className="text-red-600">{error.message}</span>
        {!error.commissionNumber && (
          <div className="text-xs text-gray-500 mt-1">
            注: 此记录可能缺少委托编号或在解析委托编号前就出现了错误
          </div>
        )}
      </div>
    </div>
  );
}

// ==================== 统计卡片组件 ====================

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: number;
  subtitle?: string;
  variant?: 'default' | 'success' | 'warning' | 'error';
}

const StatCard: React.FC<StatCardProps> = ({ 
  icon, 
  title, 
  value, 
  subtitle, 
  variant = 'default' 
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'warning':
        return 'bg-amber-50 border-amber-200 text-amber-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className={`p-4 rounded-lg border ${getVariantStyles()}`}>
      <div className="flex items-center gap-3">
        {icon}
        <div className="flex-1">
          <div className="text-2xl font-bold">{value}</div>
          <div className="text-sm font-medium">{title}</div>
          {subtitle && <div className="text-xs opacity-75">{subtitle}</div>}
        </div>
      </div>
    </div>
  );
};

// ==================== 主组件 ====================

const ExcelImportReportSummary: React.FC<ExcelImportReportSummaryProps> = ({
  reportData
}) => {
  // 空状态处理
  if (!reportData) {
    return (
      <div className="text-center p-8">
        <Info className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <p className="text-lg font-medium text-muted-foreground">报告数据加载中...</p>
        <p className="text-sm text-muted-foreground mt-2">请稍候，系统正在整理导入结果</p>
      </div>
    );
  }

  // 确定导入结果状态
  const isCompletelySuccessful = reportData.status === 'completed' && (reportData.overallFailedTotal ?? 0) === 0;
  const isPartiallySuccessful = reportData.status === 'partial' || 
    (reportData.status === 'completed' && (reportData.overallFailedTotal ?? 0) > 0);
  const isFailed = reportData.status === 'failed';

  // 渲染顶层状态
  const renderStatusAlert = () => {
    if (isCompletelySuccessful) {
      return (
        <Alert className="bg-green-50 border-green-300">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          <AlertTitle className="text-green-800">导入成功完成</AlertTitle>
          <AlertDescription className="text-green-700">
            所有 {reportData.overallTotalInitialRecords ?? 0} 条记录均已妥善处理
          </AlertDescription>
        </Alert>
      );
    } else if (isPartiallySuccessful) {
      return (
        <Alert className="bg-amber-50 border-amber-300">
          <AlertTriangle className="h-5 w-5 text-amber-600" />
          <AlertTitle className="text-amber-800">部分成功</AlertTitle>
          <AlertDescription className="text-amber-700">
            {reportData.overallProcessedSuccessfullyTotal ?? 0} 条成功处理，
            {reportData.overallFailedTotal ?? 0} 条处理失败
          </AlertDescription>
        </Alert>
      );
    } else {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle>导入失败</AlertTitle>
          <AlertDescription>
            导入过程遇到严重错误，{reportData.overallFailedTotal ?? 0} 条记录处理失败
          </AlertDescription>
        </Alert>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* 状态概览 */}
      {renderStatusAlert()}

      {/* 文件信息 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <FileText className="h-4 w-4" />
            文件信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">文件名</div>
              <div className="text-sm break-all">{reportData.fileName || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">文件大小</div>
              <div>
                {reportData.fileSize
                  ? reportData.fileSize < 1024
                    ? `${reportData.fileSize} B`
                    : reportData.fileSize < 1024 * 1024
                    ? `${(reportData.fileSize / 1024).toFixed(2)} KB`
                    : `${(reportData.fileSize / (1024 * 1024)).toFixed(2)} MB`
                  : 'N/A'
                }
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">文件哈希</div>
              <div className="font-mono text-sm break-all">{reportData.fileHash || 'N/A'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 导入信息 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Database className="h-4 w-4" />
            导入信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">导入状态</div>
              <div>
                <Badge variant="outline" className={cn(
                  reportData.status === 'completed' ? 'bg-green-50 text-green-700 border-green-200' :
                  reportData.status === 'partial' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                  reportData.status === 'failed' ? 'bg-red-50 text-red-700 border-red-200' :
                  ''
                )}>
                  {reportData.status === 'completed' ? '完成' :
                   reportData.status === 'partial' ? '部分成功' :
                   reportData.status === 'failed' ? '失败' :
                   reportData.status || 'N/A'}
                </Badge>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">导入时间</div>
              <div>{reportData.importDate ? new Date(reportData.importDate).toLocaleString() : 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">处理时长</div>
              <div>
                {reportData.processingTime
                  ? reportData.processingTime < 60
                    ? `${reportData.processingTime.toFixed(1)} 秒`
                    : `${Math.floor(reportData.processingTime / 60)} 分 ${Math.round(reportData.processingTime % 60)} 秒`
                  : 'N/A'
                }
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">创建导入人</div>
              <div>{reportData.createdBy?.username || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">执行导入人</div>
              <div>{reportData.importUser?.username || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">导入批次号</div>
              <div>{reportData.batchNumber || 'N/A'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 阶段一：文件解析阶段 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-4 w-4 text-blue-600" />
            文件解析阶段
          </CardTitle>
          <CardDescription>Excel文件读取与基础数据解析</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <StatCard
              icon={<FileText className="h-5 w-5 text-gray-600" />}
              title="总行数"
              value={reportData.analysisTotalRowsRead ?? 0}
            />
            <StatCard
              icon={<CheckCircle2 className="h-5 w-5 text-green-600" />}
              title="解析成功"
              value={reportData.analysisSuccessfullyParsedRows ?? 0}
              variant="success"
            />
            <StatCard
              icon={<AlertCircle className="h-5 w-5 text-red-600" />}
              title="解析失败"
              value={reportData.analysisFailedRows ?? 0}
              variant="error"
            />
          </div>

          {/* 解析错误详情 */}
          {(reportData.analysisFailedRows ?? 0) > 0 && reportData.detailedReport?.errors && (
            <div className="mt-4">
              <h5 className="text-sm font-medium text-red-700 mb-2">解析失败详情:</h5>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {reportData.detailedReport.errors.slice(0, 5).map((error, index) => 
                  renderErrorItem(error, index)
                )}
                {reportData.detailedReport.errors.length > 5 && (
                  <div className="text-sm text-gray-500 text-center py-2">
                    还有 {reportData.detailedReport.errors.length - 5} 条错误...
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 阶段二：条目分析阶段 */}
      <Card className="border-orange-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2 text-orange-800">
            <Search className="h-5 w-5" />
            条目分析阶段
          </CardTitle>
          <CardDescription>数据去重与更新检测</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <StatCard
              icon={<CheckCircle2 className="h-5 w-5 text-blue-600" />}
              title="识别为新记录数"
              value={reportData.analysisFoundNewCount || 0}
              variant="default"
            />
            <StatCard
              icon={<AlertTriangle className="h-5 w-5 text-amber-600" />}
              title="识别可更新记录数"
              value={reportData.analysisFoundUpdateCount || 0}
              variant="warning"
            />
            <StatCard
              icon={<Info className="h-5 w-5 text-gray-600" />}
              title="识别为内容相同的记录数"
              value={reportData.analysisSkippedIdentical || 0}
            />
          </div>
        </CardContent>
      </Card>

      {/* 阶段三：用户决策阶段 */}
      <Card className="border-purple-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2 text-purple-800">
            <BarChart3 className="h-5 w-5" />
            用户决策阶段
          </CardTitle>
          <CardDescription>用户对重复记录的处理决策</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <StatCard
              icon={<Info className="h-5 w-5 text-amber-600" />}
              title="可更新记录总数"
              value={reportData.analysisFoundUpdateCount || 0}
              subtitle="系统识别的可更新记录总数"
              variant="warning"
            />
            <StatCard
              icon={<CheckCircle2 className="h-5 w-5 text-green-600" />}
              title="确认更新记录数"
              value={reportData.userDecisionConfirmedUpdateCount || 0}
              subtitle="用户确认要更新的记录数"
              variant="success"
            />
            <StatCard
              icon={<AlertCircle className="h-5 w-5 text-gray-600" />}
              title="跳过更新记录数"
              value={reportData.userDecisionSkippedUpdateCount || 0}
              subtitle="用户选择跳过的更新记录数"
            />
          </div>
        </CardContent>
      </Card>

      {/* 阶段四：导入执行阶段 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Database className="h-4 w-4 text-green-600" />
            导入执行阶段
          </CardTitle>
          <CardDescription>数据库操作与记录保存</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <StatCard
              icon={<CheckCircle2 className="h-5 w-5 text-green-600" />}
              title="成功创建"
              value={reportData.importTaskCreatedCount ?? 0}
              variant="success"
            />
            <StatCard
              icon={<CheckCircle2 className="h-5 w-5 text-blue-600" />}
              title="成功更新"
              value={reportData.importTaskUpdatedCount ?? 0}
              variant="default"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <StatCard
              icon={<Info className="h-5 w-5 text-gray-600" />}
              title="执行时无变化"
              value={reportData.importTaskUnchangedCount ?? 0}
              subtitle="系统跳过"
            />
            <StatCard
              icon={<AlertCircle className="h-5 w-5 text-red-600" />}
              title="执行失败"
              value={reportData.importTaskFailedCount ?? 0}
              variant="error"
            />
          </div>

          {/* 执行错误详情 */}
          {(reportData.importTaskFailedCount ?? 0) > 0 && reportData.detailedReport?.importTaskProcessingErrors && (
            <div className="mt-4">
              <h5 className="text-sm font-medium text-red-700 mb-2">执行失败详情:</h5>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {reportData.detailedReport.importTaskProcessingErrors.slice(0, 5).map((error, index) => 
                  renderErrorItem(error, index)
                )}
                {reportData.detailedReport.importTaskProcessingErrors.length > 5 && (
                  <div className="text-sm text-gray-500 text-center py-2">
                    还有 {reportData.detailedReport.importTaskProcessingErrors.length - 5} 条错误...
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 阶段五：总结 */}
      <Card className="border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            导入总结
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            {/* 成功统计 */}
            <div className="space-y-3">
              <h5 className="font-medium text-green-700">成功处理</h5>
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-green-800">
                  {reportData.overallProcessedSuccessfullyTotal ?? 0}
                </div>
                <div className="text-sm text-green-600 mt-1">条记录妥善处理</div>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                <div>• 创建: {reportData.overallFinalCreatedCount ?? 0} 条</div>
                <div>• 更新: {reportData.overallFinalUpdatedCount ?? 0} 条</div>
                <div>• 跳过: {reportData.overallSkippedTotal ?? 0} 条</div>
              </div>
            </div>

            {/* 失败统计 */}
            <div className="space-y-3">
              <h5 className="font-medium text-red-700">处理失败</h5>
              <div className="bg-red-50 p-4 rounded-lg text-center">
                <div className="text-3xl font-bold text-red-800">
                  {reportData.overallFailedTotal ?? 0}
                </div>
                <div className="text-sm text-red-600 mt-1">条记录处理失败</div>
              </div>
              {(reportData.overallFailedTotal ?? 0) > 0 && (
                <div className="text-xs text-gray-600 space-y-1">
                  <div>• 解析失败: {reportData.analysisFailedRows ?? 0} 条</div>
                  <div>• 执行失败: {reportData.importTaskFailedCount ?? 0} 条</div>
                </div>
              )}
            </div>
          </div>

          {/* 整体成功率 */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="text-center">
              <div className="text-sm text-blue-700">整体成功率</div>
              <div className="text-2xl font-bold text-blue-800">
                {(() => {
                  if (!reportData.overallTotalInitialRecords || reportData.overallTotalInitialRecords === 0) {
                    return '0%';
                  }
                  
                  const successCount = reportData.overallProcessedSuccessfullyTotal ?? 0;
                  const totalCount = reportData.overallTotalInitialRecords;
                  
                  // 检查是否有详细错误记录
                  const analysisErrors = reportData.detailedReport?.errors?.length ?? 0;
                  const taskErrors = reportData.detailedReport?.importTaskProcessingErrors?.length ?? 0;
                  const overallFailed = reportData.overallFailedTotal ?? 0;
                  const hasErrors = analysisErrors > 0 || taskErrors > 0 || overallFailed > 0;
                  
                  const successRate = (successCount / totalCount) * 100;
                  
                  // 如果有错误记录，绝对不显示100%
                  if (hasErrors) {
                    // 使用 Math.floor 确保有错误时永远不会舍入到100%
                    return `${Math.floor(successRate * 10) / 10}%`;
                  }
                  
                  // 严格100%才显示100%，否则显示更精确的数值
                  if (successCount === totalCount) {
                    return '100%';
                  } else {
                    return `${Math.round(successRate * 10) / 10}%`;  // 保留一位小数
                  }
                })()}
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {reportData.overallProcessedSuccessfullyTotal ?? 0} / {reportData.overallTotalInitialRecords ?? 0} 条记录
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExcelImportReportSummary; 