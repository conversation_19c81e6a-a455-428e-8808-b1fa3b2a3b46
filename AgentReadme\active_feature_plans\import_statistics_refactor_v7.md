# 文档：导入流程统计字段调整与排查计划 (V7)

**目标**:

1. 解决导入流程中，"导入阶段"进度条直接显示100%而不是从0%开始的问题。
2. 全面优化导入日志 (`ImportLog`) 的统计字段，以提供清晰、准确、分阶段的导入过程数据。

---

**一、`ImportLog` 模型字段最终调整记录 (`archive_records/models.py`)**

以下是 `ImportLog` 模型中与统计相关的最终字段定义。所有旧的、无前缀或与此列表不符的统计字段均已从模型中移除。**执行数据库迁移 (`makemigrations` 和 `migrate`) 是应用这些更改的前提。**

1. **分析阶段统计 (`analysis_`)**:
    * `analysis_total_rows_read`: `IntegerField` - 分析阶段读取Excel有效行数。
    * `analysis_failed_rows`: `IntegerField` - 分析阶段识别错误的行数。
    * `analysis_skipped_identical`: `IntegerField` - 分析阶段识别为内容相同的记录数。
    * `analysis_found_new_count`: `IntegerField` - 分析阶段识别为新记录数。
    * `analysis_found_update_count`: `IntegerField` - 分析阶段识别可更新记录数。
    * `analysis_successfully_parsed_rows`: `IntegerField` - 分析阶段成功解析的行数 (计算得出: `analysis_total_rows_read - analysis_failed_rows`)。

2. **用户决策阶段统计 (`user_decision_`)**:
    * `user_decision_skipped_update_count`: `IntegerField` - 用户决策-跳过更新记录数 (针对分析出的"可更新"记录)。
    * `user_decision_confirmed_update_count`: `IntegerField` - 用户决策-确认更新记录数 (针对分析出的"可更新"记录)。
    * *(用户不对分析出的"新记录"做决策，它们会自动进入导入Task)*

3. **导入Task执行阶段统计 (`import_task_`)**:
    * `import_task_total_records_submitted`: `IntegerField` - 导入Task-提交处理总记录数。
    * `import_task_created_count`: `IntegerField` - 导入Task-成功创建记录数。
    * `import_task_updated_count`: `IntegerField` - 导入Task-成功更新记录数。
    * `import_task_unchanged_count`: `IntegerField` - 导入Task-执行时发现与DB内容相同数。
    * `import_task_processed_successfully_count`: `IntegerField` - 导入Task-处理成功总计数 (创建+更新+内容相同)。
    * `import_task_failed_count`: `IntegerField` - 导入Task-处理失败记录数。

4. **总体概览统计 (`overall_`)** - 直接镜像各阶段核心数据，方便顶层报告：
    * `overall_total_initial_records`: `IntegerField` (值来源: `analysis_total_rows_read`)
    * `overall_user_decision_skipped_updates`: `IntegerField` (值来源: `user_decision_skipped_update_count`)
    * `overall_final_created_count`: `IntegerField` (值来源: `import_task_created_count`)
    * `overall_final_updated_count`: `IntegerField` (值来源: `import_task_updated_count`)

5. **总体计算型汇总统计 (`overall_..._total`)** - 基于各阶段详细数据计算：
    * `overall_skipped_by_system_total`: `IntegerField` (计算: `analysis_skipped_identical + import_task_unchanged_count`)
    * `overall_skipped_total`: `IntegerField` (计算: `overall_skipped_by_system_total + user_decision_skipped_update_count`)
    * `overall_processed_successfully_total`: `IntegerField` (计算: `import_task_processed_successfully_count + user_decision_skipped_update_count + analysis_skipped_identical`)
    * `overall_failed_total`: `IntegerField` (计算: `analysis_failed_rows + import_task_failed_count`)

6. **其他相关字段**: `batch_number`, `file_name`, `file_size`, `file_hash`, `import_user`, `import_date`, `status`, `error_log`, `processing_time`, `detailed_report` 保持不变。

---

**二、后端服务层和任务层的主要逻辑变更摘要**  

* **`ExcelConflictAnalyzer.analyze_dataframe`**: 返回的 `stats` 字典包含 `total`, `new`, `update`, `identical`, `error` 键，这些是分析阶段各项计数的原始来源。
* **Celery Task (`process_excel_import_confirmation_task` in `tasks.py`)**:
  * 接收新增的 `analysis_stats_json` 和 `user_decision_stats_json` 参数。
  * 解析这两个JSON，并将包含原始分析统计（如 `total_analyzed`, `analysis_new` 等）和用户决策统计（如 `skipped_update_count`, `confirmed_update_count`）的字典传递给 `ImportSessionManager._execute_import_with_resolutions`。
* **`ImportSessionManager.confirm_import`**:
  * 接收新的 `analysis_stats_dict` 和 `user_decision_stats_dict` 参数。
  * 序列化这些字典并传递给Celery任务。
* **`ImportSessionManager._execute_import_with_resolutions`**:
  * 接收新的 `analysis_phase_stats` 和 `user_decision_phase_stats` 字典参数。
  * **导入阶段初始化**: 正确设置 `ImportSession` 的 `status` 为 `IMPORT_START`，`progress` 为 `0.0`（当有数据处理时）。若无数据处理，则直接设为完成状态。
  * **`ImportLog` 填充**: 在方法末尾，使用接收到的各阶段统计数据和 `ExcelImportService` 返回的Task执行结果，全面填充 `ImportLog` 对象的所有新命名的统计字段。
  * `detailed_report['summary_execution_phase']` 也已更新，使用新的字段名。
* **`ExcelImportService._update_import_log_with_results`**:
  * 此方法现在仅负责将其内部（即 `_process_dataframe`）产生的统计数据更新到传入 `ImportLog` 对象的 `import_task_` 前缀字段中。
* **`ExcelImportService._process_dataframe`**:
  * 在开始处理数据时，如果 `ImportSession.status` 为 `IMPORT_START`，则将其更新为 `IMPORT_IN_PROGRESS`。进度计算针对本阶段的0-100%。

---

**三、前端排查与调整计划**

为确保与后端更改完全兼容并正确展示信息，前端需要进行以下排查和调整：

1. **【API 数据准备与提交】**
    * **任务1.1: 缓存分析阶段统计**
        * **排查点**: 当前端调用后端API获取文件分析结果时（该API最终调用 `ImportSessionManager.analyze_session`）。
        * **确认/调整**: 确保前端正确接收并**临时存储/缓存**后端返回的分析统计数据（即 `stats` 字典，包含 `total`, `new`, `update`, `identical`, `error` 等原始计数）。这组数据将在后续"确认导入"时提交给后端。
    * **任务1.2: 汇总用户决策统计**
        * **排查点**: 用户在冲突解决界面进行操作时（针对"可更新"的记录选择"确认更新"或"跳过更新"）。
        * **确认/调整**: 前端必须在用户完成所有决策后，汇总生成以下两个统计值：
            * `skipped_update_count`: 用户选择"跳过更新"的记录总数。
            * `confirmed_update_count`: 用户选择"确认更新"的记录总数。
    * **任务1.3: 更新"确认导入"API的请求体**
        * **排查点**: 用户点击"确认导入"按钮，前端调用后端API触发实际导入流程时。
        * **确认/调整**: API请求体中，除了 `session_id` 和 `resolutions` 列表，**必须新增**以下两个参数，并确保其结构和键名符合后端Celery任务的期望：
            1. `analysis_stats` (对象/字典):
                * `total_analyzed`: (值来自 任务1.1 中缓存的 `stats['total']`)
                * `analysis_errors`: (值来自 任务1.1 中缓存的 `stats['error']`)
                * `analysis_identical`: (值来自 任务1.1 中缓存的 `stats['identical']`)
                * `analysis_new`: (值来自 任务1.1 中缓存的 `stats['new']`)
                * `analysis_update`: (值来自 任务1.1 中缓存的 `stats['update']`)
            2. `user_decision_stats` (对象/字典):
                * `skipped_update_count`: (值来自 任务1.2 中汇总的计数)
                * `confirmed_update_count`: (值来自 任务1.2 中汇总的计数)
        * **优先级**: 高 (否则后端统计不准确)

2. **【导入过程监控与进度显示】**
    * **任务2.1: 处理 `IMPORT_START` 状态**
        * **排查点**: 前端轮询 `ImportSession` 状态的逻辑。
        * **确认/调整**: 当轮询结果显示 `session.status` 为 `ImportSessionStatus.IMPORT_START` (字符串值为 `"import_start"`) 且 `session.progress` 为 `0.0` 时，前端UI应：
            * 将主进度条（或导入阶段专用进度条）明确重置或初始化为0%。
            * 显示 `session.status_message` (例如 "准备开始导入 X 条数据...")，清晰指示新阶段的开始。
    * **任务2.2: 处理 `IMPORT_IN_PROGRESS` 状态**
        * **排查点**: 前端轮询 `ImportSession` 状态的逻辑。
        * **确认/调整**: 当 `session.status` 为 `ImportSessionStatus.IMPORT_IN_PROGRESS` (字符串值为 `"importing"`) 时，根据 `session.progress` (0-100) 更新进度条，并显示 `session.status_message`。
    * **优先级**: 高 (影响用户体验和进度准确性)

3. **【最终导入结果与日志展示】**
    * **任务3.1: 更新所有 `ImportLog` 统计字段的引用**
        * **排查点**: 前端所有显示导入历史、导入日志详情、或任何涉及 `ImportLog` 统计数据的界面和组件。
        * **确认/调整**: 将所有对旧的、无前缀或前缀不一致的 `ImportLog` 统计字段的引用，全部更新为使用本文档第一部分"`ImportLog` 模型字段最终调整记录"中列出的**新字段名**。
        * 特别注意，如果前端之前依赖 `ImportLog.detailed_report.summary_execution_phase`，现在该summary内部的键名也已全面更新，需要同步修改。
    * **任务3.2: (可选但推荐) 调整统计数据显示布局**
        * **排查点**: 显示导入日志统计的UI部分。
        * **确认/调整**: 考虑是否需要重新组织UI布局，以更好地、分阶段地展示更细致的统计数据。
    * **优先级**: 高 (否则统计数据显示错误或不完整)

---

**四、后端API视图调整提醒**

* 调用 `ImportSessionManager.confirm_import()` 的API视图，必须从前端请求中正确接收 `analysis_stats` 和 `user_decision_stats`，并将其作为 `analysis_stats_dict` 和 `user_decision_stats_dict` 参数传递给 `confirm_import` 方法。
  * 传递给 `analysis_stats_dict` 的字典，其键名需要是从前端传来的 `analysis_stats` 中的键 (如 `total_analyzed`) 到 `ExcelConflictAnalyzer` 原始 `stats` 键 (如 `total`) 的映射，或者确保前端直接提交Celery Task期望的键（目前Celery Task已适配 `total_analyzed` 等键）。

---

**五、测试验证**

* **任务5.1: 后端单元/集成测试**：更新测试用例以验证新的 `ImportLog` 统计字段是否按预期计算和填充。
* **任务5.2: 前后端联合测试**:
  * 执行一次完整的导入流程。
  * **验证点1**: 前端提交"确认导入"时，API请求中是否正确包含了 `analysis_stats` 和 `user_decision_stats`。
  * **验证点2**: 导入过程中，通过前端轮询观察 `ImportSession` 的 `status`, `progress`, `status_message` 是否按预期平滑过渡。
  * **验证点3**: 导入完成后，查看数据库中的 `ImportLog` 记录，逐条核对所有新统计字段的值是否与实际导入情况和定义的计算逻辑一致。
  * **验证点4**: 前端导入日志详情页面是否正确显示了所有新的统计字段。
* **任务5.3: 边界条件测试**:
  * 测试无数据导入（`len(df_to_process) == 0`）的情况。
  * 测试全部是新记录的情况。
  * 测试全部是待更新记录，且用户全部选择跳过的情况。
  * 测试全部是待更新记录，且用户全部选择确认更新的情况。
  * 测试包含分析阶段错误和导入Task阶段错误的情况。

---

此文档记录了主要的字段变更和需要前后端协调的排查点。
