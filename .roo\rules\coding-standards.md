---
description: 
globs: 
alwaysApply: false
---
# 编码标准与规范

## 基本规范

1. **注释与文档**
   - 函数和类文档字符串必须使用简体中文
   - 内部注释必须使用简体中文
   - 解释"为什么"而不是"做什么"

   ```python
   def get_archive_by_id(archive_id):
       """根据ID获取档案信息"""
       # 先验证ID格式是否正确
       if not validate_id(archive_id):
           return {'success': False, 'error': '无效的档案ID'}
       # ...其他逻辑
   ```

2. **命名规范**
   - 变量/函数名: 使用英文，遵循`snake_case`命名法
   - 类名: 使用英文，遵循`PascalCase`命名法
   - 常量: 使用英文，遵循`UPPER_CASE`命名法

3. **响应格式**
   - 所有API响应必须使用统一格式:
   
   ```python
   # 成功响应
   {'success': True, 'data': result}
   # 错误响应
   {'success': False, 'error': str(e)}
   ```

## 代码修改规范

1. **变更标记**
   - 所有代码修改必须添加变更标记，格式如下:
   
   ```python
   # CHANGE: [YYYY-MM-DD] 变更描述 #问题号
   ```

2. **TODO标记**
   - 所有未完成工作必须添加TODO标记，并包含优先级:
   
   ```python
   # TODO: [优先级] 描述
   ```

   优先级定义:
   - P0: 必须解决的问题
   - P1: 应该解决的问题
   - P2: 推荐的最佳实践
   - P3: 时间允许时的可选增强

## 安全实践

1. **输入验证**
   - 验证所有外部输入
   - 在函数入口点检查参数
   - 默认拒绝，明确添加权限
   - 保护敏感数据，不记录密码或密钥

2. **错误处理**
   - 为所有外部调用设置超时
   - 实现重试机制
   - 优雅降级
   - 隔离失败，防止级联错误

## 性能准则

1. **批量操作**: 支持批量处理
2. **分页**: 大数据集使用分页
3. **缓存**: 频繁访问的数据实现缓存
4. **异步处理**: 耗时操作使用异步处理

