django>=5.1,<5.2
djangorestframework>=3.14.0
djangorestframework-simplejwt>=5.0.0
djangorestframework-camel-case>=1.4.2
django-allauth>=0.57.0
dj-rest-auth[with_social]>=5.0.0
pdf2image>=1.16.3
pytesseract>=0.3.10
Pillow>=10.0.0
ImageHash>=4.3.1
opencv-python>=4.8.0
PyMuPDF>=1.19.0

paddlepaddle
paddleocr

# mkl

django-filter>=23.1
django-cors-headers>=4.1.0
drf-yasg>=1.21.7

pandas>=2.0.0
openpyxl>=3.1.2
xlrd>=2.0.1
numpy>=1.24.0

python-dateutil>=2.8.2

prettytable>=3.6.0

# Celery and Message Broker
celery>=5.3.6,<6.0
redis>=5.0.3,<6.0 # For Celery broker/backend
django-celery-beat>=2.5.0 # For scheduling periodic tasks

# Test Dependencies
reportlab>=4.0.0 # Required for generating dummy PDFs in integration tests
pytest-django>=4.5.0
pytest-mock>=3.13.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0

werkzeug

# Database
psycopg2-binary>=2.9.0,<3.0.0
