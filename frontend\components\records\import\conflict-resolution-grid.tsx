import React, { useEffect, useRef, useMemo, useState } from 'react';

// 移除CSS文件导入，使用Theming API
// import 'ag-grid-enterprise/styles/ag-grid.css';
// import 'ag-grid-enterprise/styles/ag-theme-alpine.css';

// Import AG Grid modules
import {
  ClientSideRowModelModule,
  GridApi,
  GridOptions,
  IDetailCellRendererParams,
  ModuleRegistry,
  RowApiModule,
  ValidationModule,
  GridReadyEvent,
  ValueFormatterParams,
  ValueGetterParams,
  ColDef,
  ICellRendererParams,
} from "ag-grid-enterprise";

import {
  ColumnMenuModule,
  ColumnsToolPanelModule,
  ContextMenuModule,
  MasterDetailModule,
  ServerSideRowModelModule,
} from "ag-grid-enterprise";

import { themeQuartz } from "ag-grid-enterprise";
import { AgGridReact } from 'ag-grid-react';

// CHANGE: [2025-06-19] 移除所有snake_case到camelCase的转换逻辑
// 现在后端API直接返回camelCase字段，包括fieldDisplayName中文显示名称

// 创建自定义主题对象
const myTheme = themeQuartz.withParams({
  browserColorScheme: "light",
  headerFontSize: 14
});

// Import business related types
import {
  ConflictRecord,
  ConflictResolutionAction,
  FieldDifference
} from '@/services/domain/records/import/excel-import-service';

// Register required AG Grid modules
ModuleRegistry.registerModules([
  RowApiModule,
  ClientSideRowModelModule,
  ColumnsToolPanelModule,
  MasterDetailModule,
  ColumnMenuModule,
  ContextMenuModule,
  ServerSideRowModelModule,
  ...(process.env.NODE_ENV !== "production" ? [ValidationModule] : []),
]);

// Define types
export type ConflictFilterType = 'all' | 'new' | 'update' | 'identical';

// Extend conflict record type with user-selected action
interface ConflictRecordWithAction extends ConflictRecord {
  action: ConflictResolutionAction;
}

// Component props type
export interface ConflictResolutionGridProps {
  /** Conflict records list */
  conflicts: ConflictRecordWithAction[];
  /** Callback when resolution changes */
  onResolutionChange: (commissionNumber: string, excelRowNumber: number, newAction: ConflictResolutionAction) => void;
  /** Callback when row selection changes */
  onSelectionChanged?: (selectedRowIds: string[]) => void;
  /** Current filter type from parent */
  currentFilter?: ConflictFilterType;
  /** Callback when grid is ready, passing the GridApi instance */
  onGridReadyCallback?: (api: GridApi) => void;
}

// Helper function to format display values
const formatDisplayValue = (value: any): string => {
  if (value === null || typeof value === 'undefined') return '-';
  if (typeof value === 'boolean') return value ? '是' : '否';
  if (value instanceof Date || (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value))) {
    try { return new Date(value).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }); } catch (e) { /* fall */ }
  }
  if (typeof value === 'object') { const jsonString = JSON.stringify(value, null, 2); return jsonString.length > 300 ? jsonString.substring(0, 297) + "..." : jsonString; }
  const stringValue = String(value);
  return stringValue.length > 300 ? stringValue.substring(0, 297) + "..." : stringValue;
};

/**
 * CHANGE: [2025-06-02] 完全移除遮罩逻辑，修复多选框条件显示问题
 * - 删除所有overlay相关代码和API调用
 * - 修复rowSelection条件逻辑，确保响应式更新
 * - 简化grid配置和事件处理
 */
export function ConflictResolutionGrid({
  conflicts,
  onResolutionChange,
  onSelectionChanged,
  currentFilter = 'all',
  onGridReadyCallback,
}: ConflictResolutionGridProps) {
  // References
  const gridRef = useRef<AgGridReact>(null);
  const gridApiRef = useRef<GridApi | null>(null);
  
  // CHANGE: [2025-06-02] 简化状态管理
  const [isGridReady, setIsGridReady] = useState(false);
  
  // CHANGE: [2025-06-02] 添加调试信息
  useEffect(() => {
    console.log(`[ConflictResolutionGrid] 🔍 Filter: ${currentFilter}, Total: ${conflicts.length}`);
    console.log(`[ConflictResolutionGrid] 🔍 Grid Ready: ${isGridReady}, Grid API: ${!!gridApiRef.current}`);
  }, [currentFilter, conflicts.length, isGridReady]);

  // CHANGE: [2025-06-02] 简化column definitions
  const columnDefs = useMemo<ColDef[]>(() => [
    {
      headerName: '序号',
      field: '__rowNumber',
      width: 100,
      minWidth: 100,
      maxWidth: 100,
      pinned: 'left',
      sortable: false,
      filter: false,
      resizable: false,
      suppressHeaderMenuButton: true,
      cellClass: 'ag-row-number-cell',
      headerClass: 'ag-header-center',
      valueGetter: (params: ValueGetterParams) => {
        if (params.node && !params.node.detail) {
          let masterRowIndex = 0;
          let found = false;
          const currentNodeId = params.node.id;
          
          try {
            params.api.forEachNodeAfterFilterAndSort((node) => {
              if (!node.detail) {
                if (node.id === currentNodeId) {
                  found = true;
                  throw new Error('found');
                }
                masterRowIndex++;
              }
            });
          } catch (e) {
            // 遍历被"中断"
          }
          
          return found ? masterRowIndex + 1 : 1;
        }
        return '';
      },
      cellStyle: {
        backgroundColor: '#f8f8f8',
        color: '#666',
        textAlign: 'center',
        fontWeight: '500',
        borderRight: '1px solid #ddd'
      }
    },
    {
      headerName: "委托编号",
      field: "commissionNumber",
      sortable: true,
      filter: true,
      flex: 1,
      minWidth: 120,
      cellRenderer: 'agGroupCellRenderer',
      cellRendererParams: {
        suppressCount: true,
        suppressDoubleClickExpand: false,
        innerRenderer: (params: ICellRendererParams) => params.value,
      }
    },
    { 
      field: 'conflictType', 
      headerName: '冲突类型',
      minWidth: 120,
      filter: 'agTextColumnFilter',
      flex: 1,
      cellRenderer: (params: any) => {
        const type = params.value;
        if (!type) return null;
        const typeLabels: Record<string, { label: string, className: string }> = {
          new: { label: '新记录', className: 'px-2 py-0.5 bg-green-100 text-green-800 rounded text-xs font-medium' },
          update: { label: '可更新', className: 'px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs font-medium' },
          identical: { label: '完全相同', className: 'px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs font-medium' }
        };
        const typeInfo = typeLabels[type] || { label: '未知', className: 'px-2 py-0.5 bg-gray-100 text-gray-800 rounded text-xs font-medium' };
        return <span className={typeInfo.className}>{typeInfo.label}</span>;
      }
    },
    {
      headerName: '差异数',
      valueGetter: (params: any) => params.data?.fieldDifferences?.length || 0,
      minWidth: 100,
      flex: 1,
      cellRenderer: (params: any) => {
        const count = params.value;
        if (count === 0) {
          return <span className="text-xs text-gray-500">无字段差异</span>;
        }
        return <span className="text-xs font-medium">{count} 个字段差异</span>;
      }
    },
    { 
      field: 'action', 
      headerName: '建议操作',
      flex: 1,
      minWidth: 180,
      cellRenderer: (params: any) => {
        if (!params.data) return null;
        const recordData = params.data as ConflictRecordWithAction;
        const { conflictType, action: currentAction } = recordData;
        
        if (conflictType === 'new') {
          return (
            <div className="h-full flex items-center justify-center">
              <div className="px-3 py-1 bg-green-50 text-green-700 rounded text-xs font-medium whitespace-nowrap">
                新记录 (将创建)
              </div>
            </div>
          );
        }
        
        if (conflictType === 'identical') {
          return (
            <div className="h-full flex items-center justify-center">
              <div className="px-3 py-1 bg-blue-50 text-blue-700 rounded text-xs font-medium whitespace-nowrap">
                无差异 (将跳过)
              </div>
            </div>
          );
        }
        
        if (conflictType === 'update') {
          const availableActions = {
              [ConflictResolutionAction.UPDATE]: '更新现有记录',
              [ConflictResolutionAction.SKIP]: '跳过此导入值',
          };
          return (
            <div className="h-full flex items-center justify-end px-2">
              <select 
                className="ag-conflict-action-select w-full max-w-[180px] px-2 py-0 text-xs bg-white rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none border-0 shadow-inner"
                style={{
                  border: '1px solid #d1d5db',
                  backgroundColor: '#ffffff',
                  height: '24px',
                  margin: '2px 0',
                  lineHeight: '22px'
                }}
                value={currentAction}
                onChange={(e) => { 
                  const newAction = e.target.value as ConflictResolutionAction; 
                  onResolutionChange(recordData.commissionNumber, recordData.excelRowNumber, newAction); 
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {Object.entries(availableActions).map(([value, label]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
            </div>
          );
        }
        return (
          <div className="h-full flex items-center justify-center">
            <div className="text-xs text-gray-500">未知操作</div>
          </div>
        );
      }
    }
  ], [onResolutionChange]);

  // CHANGE: [2025-06-02] 完全重构grid options，移除所有overlay逻辑，修复rowSelection条件
  const gridOptions = useMemo<GridOptions>(() => {
    console.log(`[ConflictResolutionGrid] 🎯 Building gridOptions, currentFilter: ${currentFilter}`);
    
    return {
      // 基本配置
      columnDefs,
      rowData: conflicts,
      defaultColDef: {
        flex: 1,
        sortable: true,
        filter: true,
        resizable: true,
      },
      
      // CHANGE: [2025-06-04] 添加AG Grid官方推荐的无障碍性配置
      // 参考: https://www.ag-grid.com/javascript-data-grid/accessibility/
      ensureDomOrder: true,                     // 确保DOM元素顺序与视觉顺序一致
      suppressColumnVirtualisation: true,      // 禁用列虚拟化，确保所有列都在DOM中
      suppressGroupRowsSticky: true,           // 禁用组行粘性，避免DOM顺序混乱
      // 注意：我们使用分页而不是禁用行虚拟化来平衡性能和无障碍性
      
      // 本地化配置
      localeText: {
        loadingOoo: '正在加载数据...',
        noRowsToShow: '没有找到匹配的数据',
        page: '页',
        more: '更多',
        to: '到',
        of: '共',
        next: '下一页',
        last: '最后一页',
        first: '第一页',
        previous: '上一页',
        filterOoo: '筛选...',
        applyFilter: '应用筛选',
        clearFilter: '清除筛选',
        resetFilter: '重置筛选',
        // CHANGE: [2025-06-04] 添加无障碍性相关的本地化文本
        ariaRowCount: '总行数',
        ariaColumnCount: '总列数',
        ariaLabelColumnHeaders: '列标题',
        ariaLabelColumnGroupHeaders: '列组标题',
        ariaLabelGridCell: '网格单元格',
        ariaLabelRowSelection: '行选择',
      },
      
      // 行模型配置
      rowModelType: 'clientSide',
      
      // CHANGE: [2025-06-02] 修复行选择配置，确保条件正确响应
      rowSelection: currentFilter === 'update' ? {
        mode: 'multiRow' as const,
        checkboxes: true,
        headerCheckbox: true,
      } : undefined,
      
      // Master detail配置
      masterDetail: true,
      detailCellRendererParams: {
        detailGridOptions: {
          columnDefs: [
            { 
              // CHANGE: [2025-06-19] 直接使用后端返回的fieldDisplayName字段
              field: 'fieldDisplayName', // 后端返回camelCase: fieldDisplayName (中文显示名称)
              headerName: '字段', 
              flex: 2, 
              minWidth: 150, 
              sortable: true, 
              filter: true
            },
            { 
              field: 'existingValue', 
              headerName: '数据库现有值', 
              flex: 3, 
              minWidth: 200, 
              cellClass: 'font-mono text-xs',
              valueFormatter: (params: ValueFormatterParams) => formatDisplayValue(params.value)
            },
            { 
              field: 'importedValue', 
              headerName: 'Excel导入值', 
              flex: 3, 
              minWidth: 200, 
              cellClass: 'font-mono text-xs',
              valueFormatter: (params: ValueFormatterParams) => formatDisplayValue(params.value),
              cellClassRules: {
                'text-red-700 font-semibold bg-red-50': (params: any) => {
                  const diff = params.data;
                  if (!diff) return false;
                  return formatDisplayValue(diff.existingValue) !== formatDisplayValue(diff.importedValue);
                }
              }
            }
          ],
          defaultColDef: {
            flex: 1,
            sortable: true,
            filter: true,
            resizable: true
          },
        },
        getDetailRowData: (params: any) => {
          params.successCallback(params.data.fieldDifferences || []);
        },
      } as IDetailCellRendererParams,
      
      // 布局配置
      rowHeight: 32,
      headerHeight: 32,
      pagination: true,
      paginationPageSize: 15,
      paginationPageSizeSelector: [15, 30, 50, 100],
      suppressPaginationPanel: false,
      domLayout: 'normal',
      theme: myTheme,
      animateRows: true,
      suppressFocusAfterRefresh: true,
      
      // CHANGE: [2025-06-02] 简化onGridReady，移除所有overlay相关逻辑
      onGridReady: (params: GridReadyEvent) => {
        console.log("[ConflictResolutionGrid] 🚀 Grid ready, data length:", conflicts.length);
        gridApiRef.current = params.api;
        setIsGridReady(true);
        
        // 基本设置
        requestAnimationFrame(() => {
          if (params.api && !params.api.isDestroyed()) {
            console.log("[ConflictResolutionGrid] 📏 Sizing columns to fit");
            params.api.sizeColumnsToFit();
            
            const rowCount = params.api.getDisplayedRowCount();
            console.log("[ConflictResolutionGrid] 📊 Displayed row count:", rowCount);
            
            // 展开第一行（如果有差异）
            if (rowCount > 0) {
              setTimeout(() => {
                for (let i = 0; i < Math.min(rowCount, 3); i++) {
                  const rowNode = params.api.getDisplayedRowAtIndex(i);
                  if (rowNode && rowNode.data && rowNode.data.fieldDifferences && rowNode.data.fieldDifferences.length > 0) {
                    if (rowNode.isExpandable()) { 
                      console.log("[ConflictResolutionGrid] 📖 Expanding row", i);
                      rowNode.setExpanded(true);
                    }
                    break;
                  }
                }
              }, 100);
            }
          }
        });
        
        // 调用父组件回调
        if (onGridReadyCallback) {
          onGridReadyCallback(params.api);
        }
      },
      
      // 其他配置
      getRowId: (params: any) => `${params.data.commissionNumber}-${params.data.excelRowNumber}`,
      isRowMaster: (dataItem: any) => dataItem && dataItem.fieldDifferences && dataItem.fieldDifferences.length > 0,
      
      // CHANGE: [2025-06-02] 简化选择变更处理
      onSelectionChanged: () => {
        if (onSelectionChanged && gridApiRef.current && !gridApiRef.current.isDestroyed()) {
          if (currentFilter === 'update') {
            const selectedNodes = gridApiRef.current.getSelectedNodes();
            const selectedIds = selectedNodes.map(node => `${node.data.commissionNumber}-${node.data.excelRowNumber}`);
            console.log("[ConflictResolutionGrid] ✅ Selection changed, selected IDs:", selectedIds);
            onSelectionChanged(selectedIds);
          } else {
            console.log("[ConflictResolutionGrid] 🚫 Clearing selection for non-update filter");
            onSelectionChanged([]);
          }
        }
      },
    };
  }, [columnDefs, conflicts, currentFilter, onResolutionChange, onGridReadyCallback, onSelectionChanged]);

  // CHANGE: [2025-06-02] 处理选择状态
  useEffect(() => {
    if (isGridReady && gridApiRef.current && !gridApiRef.current.isDestroyed()) {
      console.log("[ConflictResolutionGrid] 🎯 Selection effect triggered, filter:", currentFilter);
      
      // 只在非update筛选时清空选择
      if (currentFilter !== 'update') {
        const selectedNodes = gridApiRef.current.getSelectedNodes();
        if (selectedNodes.length > 0) {
          console.log("[ConflictResolutionGrid] 🔄 Clearing selection for non-update filter");
          gridApiRef.current.deselectAll();
        }
      }
    }
  }, [currentFilter, isGridReady]);

  // CHANGE: [2025-06-02] 使用AG Grid原生筛选功能
  useEffect(() => {
    if (isGridReady && gridApiRef.current && !gridApiRef.current.isDestroyed()) {
      console.log("[ConflictResolutionGrid] 🔍 Applying AG Grid filter:", currentFilter);
      
      // 按照AG Grid官方文档使用setFilterModel
      let filterModel = null;
      if (currentFilter && currentFilter !== 'all') {
        filterModel = {
          conflictType: {
            type: 'equals',
            filter: currentFilter,
          },
        };
      }
      
      console.log("[ConflictResolutionGrid] 📋 Filter model:", filterModel);
      gridApiRef.current.setFilterModel(filterModel);
      
      // 添加调试信息
      setTimeout(() => {
        if (gridApiRef.current && !gridApiRef.current.isDestroyed()) {
          const displayedRowCount = gridApiRef.current.getDisplayedRowCount();
          console.log("[ConflictResolutionGrid] ✅ After filter applied, displayed count:", displayedRowCount);
        }
      }, 100);
    }
  }, [currentFilter, isGridReady]);

  // CHANGE: [2025-06-02] 处理rowData更新
  useEffect(() => {
    if (isGridReady && gridApiRef.current && !gridApiRef.current.isDestroyed()) {
      console.log("[ConflictResolutionGrid] 🔄 Updating rowData, new count:", conflicts.length);
      
      // 手动设置rowData确保更新
      gridApiRef.current.setGridOption('rowData', conflicts);
      
      // 添加调试信息
      setTimeout(() => {
        if (gridApiRef.current && !gridApiRef.current.isDestroyed()) {
          const displayedRowCount = gridApiRef.current.getDisplayedRowCount();
          console.log("[ConflictResolutionGrid] ✅ After rowData update, displayed count:", displayedRowCount);
          
          console.log("[ConflictResolutionGrid] 📊 数据统计 - 原始数据:", conflicts.length, "筛选后显示:", displayedRowCount);
        }
      }, 100);
    }
  }, [conflicts, isGridReady]);

  // CHANGE: [2025-06-02] 专门处理rowSelection配置的动态更新
  useEffect(() => {
    if (isGridReady && gridApiRef.current && !gridApiRef.current.isDestroyed()) {
      console.log("[ConflictResolutionGrid] 📝 Updating rowSelection config, currentFilter:", currentFilter);
      
      // 动态更新rowSelection配置
      const newRowSelection = currentFilter === 'update' ? {
        mode: 'multiRow' as const,
        checkboxes: true,
        headerCheckbox: true,
      } : undefined;
      
      gridApiRef.current.setGridOption('rowSelection', newRowSelection);
      
      setTimeout(() => {
        if (gridApiRef.current && !gridApiRef.current.isDestroyed()) {
          console.log("[ConflictResolutionGrid] ✅ RowSelection updated, should show checkboxes:", currentFilter === 'update');
        }
      }, 50);
    }
  }, [currentFilter, isGridReady]);

  // 添加自定义样式
  useEffect(() => {
    const styleId = 'ag-grid-custom-styles';
    if (!document.getElementById(styleId)) {
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.innerHTML = `
        .ag-row-number-cell {
          background-color: #f8f8f8 !important;
          color: #666 !important;
          text-align: center !important;
          font-weight: 500 !important;
          border-right: 1px solid #ddd !important;
        }
        
        .ag-theme-alpine .ag-paging-panel,
        .ag-theme-quartz .ag-paging-panel {
          height: 30px !important;
          min-height: 30px !important;
          align-items: center;
        }
      `;
      document.head.appendChild(styleElement);
    }

    return () => {
      const styleElement = document.getElementById(styleId);
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, []);

  return (
    <div 
      style={{ 
        width: '100%', 
        height: '100%',
        minHeight: '250px',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <AgGridReact
        ref={gridRef}
        gridOptions={gridOptions}
        theme={myTheme}
      />
    </div>
  );
}

export default ConflictResolutionGrid; 