# 临时 Streamlit 演示前端

## 用途

此目录包含一个使用 Streamlit 构建的**临时**前端应用程序。

其主要目的是为了快速搭建一个用户界面，用于**演示和测试**核心后端功能，包括：

1.  上传 Excel 格式的档案台账。
2.  上传 PDF 文件并指定物理盒号，触发后端处理流程。

**注意：** 这个前端非常基础，仅用于演示目的，并非最终的用户界面。

## 重要：临时权限变更

**为了方便本次演示，以下后端接口已被临时设置为允许匿名访问（无需登录）：**

*   **Excel 导入接口**: `/api/archive-records/import-excel/`
*   **PDF 上传接口**: `/api/archive-processing/upload/`

**在演示完成后，务必恢复这些接口的权限设置：**

1.  修改 `archive_records/views.py` 文件中 `ExcelImportView` 的 `permission_classes` 改回 `[IsAuthenticated]`。
2.  修改 `archive_processing/views.py` 文件中 `PDFUploadView` 的 `permission_classes` 改回 `[IsAuthenticated]`。

## 设置与运行

1.  **前提**: 确保您的 Python 环境已安装 pip。
2.  **安装依赖**: 在命令行/终端中，导航到此 `-frontend` 目录下，然后运行：
    ```bash
    pip install -r requirements.txt
    ```
3.  **启动后端**: 确保 Django 后端开发服务器正在运行（通常在 `http://localhost:8000`），并且**已重新启动**以应用权限变更。
4.  **启动前端**: 在命令行/终端中，仍然在此 `-frontend` 目录下，运行：
    ```bash
    streamlit run demo_app.py
    ```
5.  **访问**: 您的浏览器应该会自动打开 Streamlit 应用（通常在 `http://localhost:8501`）。

## 可用演示应用

本目录包含以下Streamlit演示应用：

1. **demo_app.py**: 主要演示应用，提供档案台账导入和PDF处理功能。
   ```bash
   streamlit run demo_app.py
   ```

2. **issue_demo.py**: 报告发放功能演示应用，提供发放单管理、详情操作和记录查询功能。
   ```bash
   streamlit run issue_demo.py
   ```
   
   该应用包含三个主要功能区：
   - **发放单管理**: 创建新发放单和查看发放单列表
   - **发放单详情与操作**: 添加档案条目和执行状态流转操作(锁定、确认、归档等)
   - **发放记录查询**: 查询历史发放记录及其详情

## 清理步骤

当不再需要这个临时演示前端时，可以按以下步骤清理：

1.  **删除此目录**: 直接删除整个 `-frontend` 目录。
2.  **卸载依赖 (可选)**: 如果您是在全局或后端环境中安装的依赖，可以运行 `pip uninstall streamlit requests -y` 来卸载它们。
3.  **恢复后端权限 (重要!)**: 编辑后端 Django 项目的 `archive_records/views.py` 和 `archive_processing/views.py` 文件，将 `ExcelImportView` 和 `PDFUploadView` 的 `permission_classes` 改回 `[IsAuthenticated]`。
4.  **移除后端 CORS 配置 (可选)**: 编辑后端 Django 项目的 `settings.py` 文件，从 `CORS_ALLOWED_ORIGINS` 列表中移除 `http://localhost:8501` 和 `http://127.0.0.1:8501`。 