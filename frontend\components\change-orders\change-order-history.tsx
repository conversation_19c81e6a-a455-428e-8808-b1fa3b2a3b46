"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ChangeOrderHistoryProps {
  changeOrder: any
}

export function ChangeOrderHistory({ changeOrder }: ChangeOrderHistoryProps) {
  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "creating":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            正在创建
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            草稿
          </Badge>
        )
      case "locked":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            已锁定
          </Badge>
        )
      case "confirmed":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            已确认
          </Badge>
        )
      case "archived":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            已归档
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card>
      <CardContent className="pt-6">
        {changeOrder.history?.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">时间</th>
                  <th className="text-left py-3 px-4">操作</th>
                  <th className="text-left py-3 px-4">状态</th>
                  <th className="text-left py-3 px-4">操作人</th>
                  <th className="text-left py-3 px-4">原因</th>
                </tr>
              </thead>
              <tbody>
                {changeOrder.history.map((item: any) => (
                  <tr key={item.id} className="border-b">
                    <td className="py-3 px-4">{item.timestamp}</td>
                    <td className="py-3 px-4">
                      {(() => {
                        switch (item.action) {
                          case "create":
                            return "创建"
                          case "update":
                            return "更新"
                          case "update_records":
                            return "更新记录"
                          case "lock":
                            return "锁定"
                          case "unlock":
                            return "解锁"
                          case "confirm":
                            return "确认"
                          case "archive":
                            return "归档"
                          case "revert_to_draft":
                            return "转为草稿"
                          default:
                            return item.action
                        }
                      })()}
                    </td>
                    <td className="py-3 px-4">{getStatusBadge(item.status)}</td>
                    <td className="py-3 px-4">{item.user}</td>
                    <td className="py-3 px-4">{item.reason}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-10 text-muted-foreground">
            <p>暂无操作历史</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
