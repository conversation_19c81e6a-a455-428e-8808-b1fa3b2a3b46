# Operation Document: Refine Row Processing Exception Handling in Excel Import

## 📋 Change Summary

**Purpose**: To comprehensively address the "You can't execute queries until the end of the 'atomic' block." error during Excel imports by refining the main row processing exception handler in `_process_dataframe`.
**Scope**: Modified the `_process_dataframe` method in `archive_records/services/excel_import.py`.
**Associated**: Final step in debugging the 500 error reported by the user, ensuring all identified paths for broken transactions due to unhandled database errors are covered.

## 🔧 Operation Steps

### 📊 OP-001: Analyze Final Potential Path for Transaction Error

**Precondition**: Previous fixes addressed `django.db.Error` handling within nested operations like `existing_record.save()` and `_safe_bulk_create()`. However, the error might still occur if a `django.db.Error` originates from other parts of the main row processing `try` block and is caught by its generic `except Exception`.
**Operation**:
    1. Reviewed the main `try...except` block that wraps the processing for each row within a batch (the `try` at L358, and the `except Exception as e:` at L575 in the user's version of the file).
    2. Identified that an operation like `_get_next_version_number()` (called around L497, outside the most nested `try` for `save()`) could raise a `django.db.Error`.
    3. If such an error occurred, it would be caught by the generic `except Exception as e:` at L575, which, prior to this fix, would log the error but not re-throw a `django.db.Error`. This would break the transaction for the batch.
**Postcondition**: Pinpointed the main row processing exception handler as the final area needing refinement for `django.db.Error` propagation.

### ✏️ OP-002: Refactor Main Row Processing Exception Handler

**Precondition**: The main `try-except` block for row processing (L358-L588) uses a single `except Exception as e:` which could mask `django.db.Error` and prevent transaction rollback.
**Operation**:
    1. Restructured the exception handling for the main row processing `try` block:
        - Added a new, leading `except django.db.Error as db_row_err:`. This block logs the specific database error and critically re-throws (`raise`) it. This ensures any DB error from operations like `_get_next_version_number` or other DB calls not caught by more nested handlers will now correctly trigger a transaction rollback for the batch.
        - Added an `except ValueError as val_err:`. This catches data validation or conversion errors (e.g., from `_process_row`), logs them, and allows the import to continue to the next row (as these typically don't break the transaction).
        - The final `except Exception as e:` now acts as a catch-all for other unexpected non-DB, non-ValueError exceptions for that row, logging them and allowing the import to continue to the next row.
    2. Updated logging messages for clarity and ensured `current_commission_number_for_error_reporting` and `current_sample_number_for_error_reporting` are used in log messages where appropriate.
**Postcondition**: `django.db.Error` exceptions occurring anywhere within the primary `try` block for row processing are now correctly re-thrown, ensuring batch transaction integrity. `ValueError` exceptions are handled separately without stopping the batch. Other exceptions are also logged per row.

## 📝 Change Details

### CH-001: Restructure Row Processing Exception Handling

**File**: `archive_records/services/excel_import.py`
**Method**: `_process_dataframe` (around lines 575-588 of the user's version)
**Before** (simplified relevant part):

```python
# ... inside for row in batch_df.iterrows():
                    try:
                        # ... all row processing logic, including calls to _process_row,
                        # _get_next_version_number, and the try/except for existing_record.save() ...
                    except Exception as e: # Catches all errors from the try block above
                        # Logs error, appends to error_records, and continues to next row.
                        # Problem: If 'e' is a django.db.Error from _get_next_version_number, etc.,
                        # the transaction is broken, but not rolled back here.
                        error_records.append({ ... })
# ...
```

**After** (simplified relevant part):

```python
# ... inside for row in batch_df.iterrows():
                    try:
                        # ... all row processing logic ...
                    except django.db.Error as db_row_err: # New: Catch DB errors first
                        logger.error(f"处理行数据时发生数据库错误 ...: {db_row_err}", exc_info=True)
                        error_records.append({ ... })
                        raise # CRITICAL: Re-throw to roll back batch transaction
                    except ValueError as val_err: # New: Catch ValueErrors (e.g., from _process_row)
                        logger.warning(f"处理行数据时发生数据验证/处理错误 ...: {val_err}", exc_info=False)
                        error_records.append({ ... })
                        # Continue to next row, transaction okay
                    except Exception as e: # Catches other non-DB, non-ValueError errors
                        logger.error(f"处理单行数据时发生未预料的非数据库/非验证错误 ...: {e}", exc_info=True)
                        error_records.append({ ... })
                        # Continue to next row
# ...
```

**Rationale**: This hierarchical exception handling ensures that `django.db.Error` exceptions take precedence and cause the batch transaction to roll back, which is essential for preventing the "can't execute queries in a broken transaction" error. `ValueError` (common for data issues) allows the row to be skipped without failing the batch. Other errors are also logged per row.

## ✅ Verification Results

**Method**: Code review and logical analysis. This change completes the strategy of ensuring all `django.db.Error` paths within the batch processing `transaction.atomic()` block lead to the error being re-thrown.
**Results**: With this and previous changes, the application should now be robust against the reported transaction error. Any underlying database errors during import will be properly logged, and the import process will fail at the correct level without attempting further operations on a broken transaction.
**Problems**: None anticipated for this specific refinement.
**Solutions**: N/A.
