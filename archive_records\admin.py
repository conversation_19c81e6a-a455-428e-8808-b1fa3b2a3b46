from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    ArchiveRecord,
    ImportLog,
    ChangeLogBatch,
    RecordChangeLog,
    FieldChangeLog,  # 添加新模型的导入
    ChangeOrder,
    ChangeOrderItem
)

from django.http import HttpResponseRedirect

# Register your models here.


@admin.register(ChangeLogBatch)
class ChangeLogBatchAdmin(admin.ModelAdmin):
    list_display = (
        "batch_id",
        "change_source_display",
        "change_reason_short",
        "changed_at",
        "changed_by_name",
        "affected_records_count",
        "actions_column",
    )
    list_filter = ("change_source", "changed_at", "changed_by")
    search_fields = ("batch_id", "change_reason", "changed_by__username")
    readonly_fields = (
        "batch_id",
        "changed_at",
        "affected_records_count",
        "summary",
        "client_ip",
        "user_agent",
    )
    fieldsets = (
        (
            "基本信息",
            {
                "fields": (
                    "batch_id",
                    "change_source",
                    "change_reason",
                    "changed_at",
                    "changed_by",
                )
            },
        ),
        ("变更统计", {"fields": ("affected_records_count", "summary")}),
        (
            "客户端信息",
            {
                "fields": ("client_ip", "user_agent"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("changed_by")

    def change_source_display(self, obj):
        return obj.get_change_source_display()

    change_source_display.short_description = "变更来源"

    def change_reason_short(self, obj):
        if obj.change_reason and len(obj.change_reason) > 50:
            return f"{obj.change_reason[:50]}..."
        return obj.change_reason or "-"

    change_reason_short.short_description = "变更原因"

    def changed_by_name(self, obj):
        if obj.changed_by:
            full_name = (
                f"{obj.changed_by.first_name} {obj.changed_by.last_name}".strip()
            )
            return full_name or obj.changed_by.username
        return "-"

    changed_by_name.short_description = "变更用户"

    def actions_column(self, obj):
        """显示操作链接"""
        view_url = (
            reverse("admin:archive_records_recordchangelog_changelist")
            + f"?batch__id__exact={obj.id}"
        )
        return format_html(
            '<a href="{}" class="button" style="background-color:#417690;color:white;padding:2px 5px;border-radius:3px;text-decoration:none;font-size:12px;">查看变更记录</a>',
            view_url,
        )

    actions_column.short_description = "操作"


@admin.register(RecordChangeLog)
class RecordChangeLogAdmin(admin.ModelAdmin):
    list_display = (
        "record_display",
        "version_number",
        "change_type_display",
        "changed_at",
        "changed_by_name",
        "changed_fields_count",
        "rollback_info",
        "actions_column",
    )
    list_filter = (
        "change_type",
        "is_rollback",
        "changed_at",
        "changed_by",
        "batch__change_source",
    )
    search_fields = (
        "record__sample_number",
        "record__commission_number",
        "batch__batch_id",
        "notes",
    )
    raw_id_fields = ("record", "batch")
    readonly_fields = (
        "version_number",
        "change_type",
        "is_rollback",
        "rollback_source_version",
        "changed_at",
        "record_before",
        "record_after",
    )
    fieldsets = (
        (
            "基本信息",
            {
                "fields": (
                    "record",
                    "batch",
                    "version_number",
                    "change_type",
                    "changed_by",
                    "changed_at",
                )
            },
        ),
        (
            "回滚信息",
            {
                "fields": ("is_rollback", "rollback_source_version"),
                "classes": ("collapse",),
            },
        ),
        (
            "变更详情",
            {
                "fields": ("changed_fields_count", "notes"),
            },
        ),
        (
            "详细数据",
            {
                "fields": ("record_before", "record_after"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("record", "batch", "changed_by")
        )

    def record_display(self, obj):
        """显示记录标识，带链接"""
        if obj.record:
            record_url = reverse(
                "admin:archive_records_archiverecord_change", args=[obj.record.id]
            )
            return format_html('<a href="{}">{}</a>', record_url, str(obj.record))
        return "-"

    record_display.short_description = "档案记录"

    def change_type_display(self, obj):
        """带颜色的变更类型显示"""
        colors = {
            "create": "#28a745",  # 绿色
            "update": "#17a2b8",  # 蓝绿色
            "delete": "#dc3545",  # 红色
            "rollback": "#6c757d",  # 灰色
        }
        color = colors.get(obj.change_type, "#212529")
        return format_html(
            '<span style="color:{};">{}</span>', color, obj.get_change_type_display()
        )

    change_type_display.short_description = "变更类型"

    def changed_by_name(self, obj):
        """显示变更人信息"""
        details = obj.get_changed_by_details()
        if details:
            return details.get("full_name")
        return "-"

    changed_by_name.short_description = "变更人"

    def rollback_info(self, obj):
        """显示回滚信息"""
        if obj.is_rollback and obj.rollback_source_version:
            return f"回滚到版本 {obj.rollback_source_version}"
        return "-"

    rollback_info.short_description = "回滚信息"

    def actions_column(self, obj):
        """显示操作链接"""
        view_changes_url = (
            reverse("admin:archive_records_fieldchangelog_changelist")
            + f"?record_change__id__exact={obj.id}"
        )
        view_history_url = (
            reverse("admin:archive_records_recordchangelog_changelist")
            + f"?record__id__exact={obj.record.id}"
        )

        return format_html(
            '<a href="{}" class="button" style="background-color:#417690;color:white;padding:2px 5px;border-radius:3px;text-decoration:none;font-size:12px;margin-right:5px;">查看字段变更</a>'
            '<a href="{}" class="button" style="background-color:#28a745;color:white;padding:2px 5px;border-radius:3px;text-decoration:none;font-size:12px;">查看历史版本</a>',
            view_changes_url,
            view_history_url,
        )

    actions_column.short_description = "操作"


@admin.register(FieldChangeLog)
class FieldChangeLogAdmin(admin.ModelAdmin):
    list_display = (
        "field_label",
        "record_info",
        "change_action_display",
        "field_importance_display",
        "old_value_display",
        "new_value_display",
        "version_info",
    )
    list_filter = (
        "change_action",
        "field_importance",
        "field_name",
        "record_change__change_type",
    )
    search_fields = (
        "field_name",
        "field_label",
        "old_value",
        "new_value",
        "record_change__record__sample_number",
    )
    raw_id_fields = ("record_change",)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related(
                "record_change", "record_change__record", "record_change__batch"
            )
        )

    def record_info(self, obj):
        """显示记录信息"""
        if obj.record_change and obj.record_change.record:
            record_url = reverse(
                "admin:archive_records_archiverecord_change",
                args=[obj.record_change.record.id],
            )
            return format_html(
                '<a href="{}">{}</a>', record_url, str(obj.record_change.record)
            )
        return "-"

    record_info.short_description = "档案记录"

    def version_info(self, obj):
        """显示版本信息"""
        if obj.record_change:
            return f"版本 {obj.record_change.version_number}"
        return "-"

    version_info.short_description = "版本"

    def change_action_display(self, obj):
        """带颜色的变更动作显示"""
        colors = {
            "added": "#28a745",  # 绿色
            "modified": "#17a2b8",  # 蓝绿色
            "removed": "#dc3545",  # 红色
            "unchanged": "#6c757d",  # 灰色
        }
        color = colors.get(obj.change_action, "#212529")
        return format_html(
            '<span style="color:{};">{}</span>', color, obj.get_change_action_display()
        )

    change_action_display.short_description = "变更动作"

    def field_importance_display(self, obj):
        """带标记的字段重要性显示"""
        badges = {
            "critical": (
                '<span style="background-color:#dc3545;color:white;padding:2px 5px;'
                'border-radius:3px;font-size:12px;">关键</span>'
            ),
            "important": (
                '<span style="background-color:#ffc107;color:#212529;padding:2px 5px;'
                'border-radius:3px;font-size:12px;">重要</span>'
            ),
            "normal": (
                '<span style="background-color:#6c757d;color:white;padding:2px 5px;'
                'border-radius:3px;font-size:12px;">普通</span>'
            ),
        }
        return format_html(badges.get(obj.field_importance, "普通"))

    field_importance_display.short_description = "重要性"

    def old_value_display(self, obj):
        """旧值显示，截断长文本"""
        if obj.old_value is None:
            return "-"
        if len(obj.old_value) > 50:
            return f"{obj.old_value[:50]}..."
        return obj.old_value

    old_value_display.short_description = "旧值"

    def new_value_display(self, obj):
        """新值显示，截断长文本"""
        if obj.new_value is None:
            return "-"
        if len(obj.new_value) > 50:
            return f"{obj.new_value[:50]}..."
        return obj.new_value

    new_value_display.short_description = "新值"


# 扩展现有ArchiveRecord管理界面，添加版本历史查看功能
class RecordChangeLogInline(admin.TabularInline):
    model = RecordChangeLog
    fk_name = "record"
    extra = 0
    max_num = 5  # 只显示最近5个版本
    readonly_fields = (
        "version_number",
        "change_type",
        "changed_at",
        "changed_by",
        "changed_fields_count",
        "view_details",
    )
    fields = (
        "version_number",
        "change_type",
        "changed_at",
        "changed_by",
        "changed_fields_count",
        "view_details",
    )
    ordering = ("-version_number",)

    def view_details(self, obj):
        """查看详情链接"""
        if obj.id:
            url = reverse("admin:archive_records_recordchangelog_change", args=[obj.id])
            return format_html('<a href="{}">查看详情</a>', url)
        return "-"

    view_details.short_description = "操作"

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


# 扩展现有ArchiveRecord的管理
@admin.register(ArchiveRecord)
class ArchiveRecordAdmin(admin.ModelAdmin):
    # 保留原有的配置...

    inlines = [RecordChangeLogInline]  # 添加版本历史内联显示

    actions = ["view_version_history"]

    def view_version_history(self, request, queryset):
        """查看选中记录的版本历史"""
        if queryset.count() == 1:
            record = queryset.first()
            url = (
                reverse("admin:archive_records_recordchangelog_changelist")
                + f"?record__id__exact={record.id}"
            )
            return HttpResponseRedirect(url)
        else:
            self.message_user(request, "请选择单条记录查看版本历史")

    view_version_history.short_description = "查看版本历史"


class ChangeOrderItemInline(admin.TabularInline):
    model = ChangeOrderItem
    extra = 0
    readonly_fields = ['archive_record', 'record_before', 'planned_changes', 'changes_count',
                      'is_executed', 'execution_time', 'execution_result', 'execution_message',
                      'record_change_log']
    can_delete = False
    max_num = 0
    
    def has_add_permission(self, request, obj=None):
        return False

@admin.register(ChangeOrder)
class ChangeOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'status', 'created_by', 'created_at', 'execution_time', 
                    'executed_by', 'records_count', 'changes_count', 'is_rollback_source']
    list_filter = ['status', 'created_at', 'is_deleted', 'is_rollback_source']
    search_fields = ['order_number', 'change_reason', 'notes']
    readonly_fields = ['order_number', 'created_at', 'updated_at', 'records_count', 
                       'changes_count', 'execution_time', 'executed_by', 'change_batch']
    fieldsets = [
        ('基本信息', {
            'fields': ['order_number', 'status', 'created_by', 'created_at', 'updated_at']
        }),
        ('业务信息', {
            'fields': ['change_reason', 'notes', 'records_count', 'changes_count']
        }),
        ('执行信息', {
            'fields': ['execution_time', 'executed_by', 'change_batch']
        }),
        ('附件信息', {
            'fields': ['attachment', 'attachment_name', 'attachment_type']
        }),
        ('回滚信息', {
            'fields': ['is_rollback_source', 'rollback_order']
        }),
    ]
    inlines = [ChangeOrderItemInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新创建的更改单
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(ChangeOrderItem)
class ChangeOrderItemAdmin(admin.ModelAdmin):
    list_display = ['change_order', 'archive_record', 'changes_count', 'is_executed', 'execution_time']
    list_filter = ['is_executed', 'execution_time']
    search_fields = ['change_order__order_number', 'archive_record__unified_number']
    readonly_fields = ['change_order', 'archive_record', 'record_before', 'planned_changes', 
                      'changes_count', 'is_executed', 'execution_time', 'execution_result', 
                      'execution_message', 'record_change_log']
    
    def has_add_permission(self, request):
        return False
        