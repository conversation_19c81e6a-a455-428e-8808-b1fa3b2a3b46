# PDF处理流程的前端交互与API设计文档 (V8 - Final)

## 1. 概述与范围界定

本文档旨在为 **PDF处理全流程的监控界面** 设计一个统一、高效且可维护的前端用户界面与相应的后端API。本文档反映了最终确定的**"任务作为状态追踪器，最后原子化入库"**的核心架构。

### 1.1 开发环境与技术栈

**开发环境**:

- **容器化部署**: 项目采用Docker容器化部署，通过`docker-compose.yml`管理多服务架构
- **后端框架**: Django + Django REST Framework
- **前端框架**: Next.js + TypeScript + AG Grid
- **数据库**: PostgreSQL (容器化部署)
- **任务队列**: Celery + Redis
- **文件存储**: 本地存储 + 静态文件服务

**关键技术特性**:

- **API命名约定**: 项目集成了`djangorestframework-camel-case`包，实现前后端命名风格的自动转换

### 1.2 核心目标

- **清晰监控**: 提供直观的界面，用于追踪上传批次从始至终的完整处理状态。
- **数据一致性**: 采用健壮的后端设计，保证数据模型的完整与一致。
- **平滑集成**: 确保新功能的设计不仅能与现有业务（如台账编辑、更改追溯）无缝衔接，还能健壮地修复因自身架构调整而对这些业务造成的连带影响。

### 1.3 重要范围界定与连带调整

本次设计的核心是**新的PDF处理监控页面**。然而，为实现其数据模型而做的架构决策（特别是引入外键关联），将对现有功能产生直接影响。因此，以下**连带调整**也属于本次设计的范围：

1. **前端PDF处理监控页面设计**: 适配新需求，并整合部分原有页面功能。
2. **档案台账页面的在位编辑修复**: 由于数据库模型调整，需要对台账页面的编辑功能进行修复性设计。
3. **变更记录系统的兼容性改造**: 同样因为数据库模型调整，需确保`ChangeLog`系统能够继续追踪所有相关变更。

## 2. 功能需求详述

前端界面将以**三个独立的Tab页签**形式组织，分别对应不同的处理状态。所有视图均采用**AG Grid Master-Detail**模式。

### 2.1 "处理中" 视图 (Tab 1)

- **功能**: 实时监控正在进行中的任务。
- **监控主体**: 监控的核心是**文件处理流程**。在技术实现上，每个`UploadedFile`都对应一个`ProcessingTask`，该任务记录了处理的动态信息（如状态和进度）。
- **Master行**: 展示`ProcessingTask` (第一阶段：档案拆分) 的总体进度。一行代表一个已上传文件的处理工作流。
- **Detail行**: 展示该主任务下所有`ReportSplittingTask` (第二阶段：报告分割) 的独立进度。

### 2.2 "已完成" 视图 (Tab 2)

- **功能**: 展示所有成功完成处理的上传批次 (`ProcessingTask` 状态为 'finalized')。
- **核心操作**:
  - 提供**编辑档案盒号**的功能。此操作在Master行上进行，修改后，其下所有Detail行关联的档案盒号信息应自动更新。
- **链接**: 提供指向原始PDF (`UploadedFile`)、独立档案PDF (`ArchiveRecord.archive_url`)、仅报告版PDF (`ArchiveRecord.report_url`)的下载或查看链接。

### 2.3 "处理失败" 视图 (Tab 3)

- **功能**: 管理所有处理失败的批次，提供诊断信息和恢复操作。
- **核心操作 (在Master行或Detail行上)**:
  - **智能重试按钮**:
    - 若`ProcessingTask`失败，则重试整个第一阶段流程。
    - 若某个`ReportSplittingTask`失败，则仅针对该任务进行重试。
  - **重新上传按钮**: 允许用户为这个失败的批次重新上传一个修正后的原始PDF文件。

## 3. 核心架构与API设计

### 3.1 数据模型：任务作为状态追踪器

- **核心原则**: `ArchiveRecord` 表仅在所有流程成功结束后才被写入。处理过程中的状态和进度由`ProcessingTask`和`ReportSplittingTask`两张表追踪。
  1. **`UploadedFile`**: 存储上传的原始文件和用户指定的`archive_box_number`。
     - **新增 `status` 字段**: 用于软删除和状态管理。
       - `active`: '活跃' - 正常、有效的上传记录。
       - `deleted`: '已删除' - 由用户通过"重新上传"操作废弃的记录。
       - `superseded`: (预留) 未来可用于需要区分处理的"版本替换"业务。
  2. **`ProcessingTask`**: 追踪第一阶段（档案拆分）的总体进度。
  3. **`ReportSplittingTask`**: 追踪第二阶段（报告分割）中每个独立档案的进度。
  4. **`ArchiveRecord`**: 存储最终的、已归档的业务数据，并通过`source_file`外键回溯到`UploadedFile`。

### 3.2 API设计

1. **数据获取 (用于监控页面)**:
    - `GET /api/uploaded-files/`: 获取上传文件列表，作为Master行数据源。**默认仅查询 `status='active'` 的记录**。API内部会关联查询对应的`ProcessingTask`以获取实时状态和进度。
    - `GET /api/processing-tasks/{task_id}/report-splitting-tasks/`: 获取指定主任务下的所有第二阶段子任务列表 (Detail行数据)。
2. **核心操作**:
    - `PATCH /api/uploaded-files/{file_id}/`: 用于编辑档案盒号。
    - `POST /api/tasks/{task_id}/retry/`: 统一的智能重试API。后端根据任务ID判断其类型（第一阶段或第二阶段）并触发相应的重试流程。
    - `POST /api/uploaded-files/{old_file_id}/re-upload/`: **重新上传API**。用于修正一个处理失败的文件。后端在一个事务中完成以下操作：
        1. 将`old_file_id`对应的`UploadedFile`状态置为`deleted`。
        2. 创建新的`UploadedFile`和`ProcessingTask`，开始新的处理流程。
        3. 异步触发后台任务，清理`old_file_id`关联的所有物理文件。

### 3.3 覆盖逻辑说明

- **普通上传**: 如果用户针对同一个业务实体（由`unified_number`标识）进行多次独立的上传（非"重新上传"），系统会创建多个并存的、状态均为`active`的`UploadedFile`记录。当后续处理完成并更新到`ArchiveRecord`时，新的文件链接 (`archive_url`, `report_url`) 将会覆盖旧的链接。

### 3.4 API命名约定说明

如1.1节所述，项目已集成`djangorestframework-camel-case`包，实现了前后端命名风格的无缝转换：

- **前端发送**: `{"archiveBoxNumber": "BOX-001"}`
- **后端接收**: `{'archive_box_number': 'BOX-001'}`
- **开发优势**: 前后端开发者可以各自使用符合各自语言习惯的命名风格，无需额外的转换工作

## 4. 连带调整与修复方案

### 4.1 档案台账页面的在位编辑修复设计

- **背景**: **引入外键并删除`ArchiveRecord.archive_box_number`字段后，导致原台账页面(`records/ledger/page.tsx`)的在位编辑功能不可用。**
- **修复方案**:
  - **统一API入口**: 在位编辑**继续使用**现有的 `PATCH /api/archive-records/{record_id}/` API。
  - **后端智能分发**: 在该API的后端实现中，增加一个"智能分发器"。
    - 当检测到请求修改的是`archive_box_number`时，通过`record.source_file`外键找到并更新`UploadedFile.archive_box_number`。
    - 对其他字段的编辑，则按原逻辑执行。
- **设计目的**: 此方案在修复功能的同时，预留了权限扩展点，为未来实现细粒度权限控制打下坚实基础。

### 4.2 变更记录系统的兼容性改造

- **背景**: 原`ChangeLog`系统仅能追踪对`ArchiveRecord`的修改。当修改档案盒号的操作转移到`UploadedFile`后，该变更将脱离审计。
- **修复方案**:
  - 采用 **Django ContentType 框架**，将 `ChangeLog` 模型改造为使用**通用外键 (GenericForeignKey)**，以便能同时追踪对`UploadedFile`的修改。
- **评估**:
  - 此方案可以将对`UploadedFile`的修改也纳入同一套审计系统，是解决此类问题的行业标准。
  - 适配难度为**中低**，主要工作量在于调整展示变更历史的API和前端逻辑。

---
*文档版本 V8 (Final)，根据最终讨论共识修订*  

---

## 5. 详细实施计划 (分层)

本节将根据 v8 版设计文档，制定一个清晰、分层的实施计划，以指导后续的开发工作。

### 5.1 数据层 (Data Layer) - 地基

**目标**: 建立支持两阶段处理流程和新监控界面的数据库模型。

- **任务 2.1: 调整 `ArchiveRecord` 模型** (`archive_records/models.py`)
  - **2.1.1**: 定义新的 `archive_status` 枚举/choices，包含以下状态：
    - `'待处理'` (Pending)
    - `'档案拆分处理中'` (Splitting in Progress)
    - `'报告待拆分处理'` (Pending Seal Splitting)
    - `'报告拆分处理中'` (Seal Splitting in Progress)
    - `'已归档'` (Archived)
    - `'处理失败'` (Failed)
  - **2.1.2**: 添加 `seal_split_status` 字段 (CharField)，用于追踪第二阶段具体状态。
  - **2.1.3**: 添加 `report_only_url` 字段 (URLField)，用于存储仅报告版PDF的链接。

- **任务 2.2: 创建 `SealSplittingTask` 模型** (`archive_processing/models.py`)
  - **2.2.1**: 根据设计文档创建新模型，包含 `archive_record` (外键)、`source_pdf_path`、`status`、`output_pdf_path` 等核心字段。

- **任务 2.3: 生成并应用数据库迁移**
  - **2.3.1**: 运行 `makemigrations` 为 `archive_records` 和 `archive_processing` 应用生成新的迁移文件。
  - **2.3.2**: 运行 `migrate` 将所有模型变更应用到数据库。

### 5.2 服务层 (Service Layer) - 核心逻辑

**目标**: 封装两个处理阶段的核心业务逻辑。

- **任务 2.4: 修复 `record_update_service`**
  - **2.4.1**: 修改 `update_archive_record` 函数逻辑。在第一阶段成功后，应将 `ArchiveRecord.archive_status` 设置为 `'报告待拆分处理'`，而不是 `'已归档'`。

- **任务 2.5: 创建 `seal_splitting_service`**
  - **2.5.1**: 创建新文件 `archive_processing/services/seal_splitting_service.py`。
  - **2.5.2**: 在该服务中实现 `process_seal_splitting` 函数，封装MA章检测、PDF分割、文件存储等核心算法。
  - **2.5.3**: 该函数在成功后，负责将 `ArchiveRecord.archive_status` 更新为最终的 `'已归档'` 状态。

### 5.3 任务/控制层 (Task/Control Layer) - 流程编排

**目标**: 使用Celery任务编排两个处理阶段的自动化流程。

- **任务 2.6: 修改第一阶段任务 (`process_pdf_serial_task`)**
  - **2.6.1**: 在 `archive_processing/tasks.py` 中，修改该任务。
  - **2.6.2**: 当第一阶段成功处理完一个 `ArchiveRecord` 后，在原有的 `update_archive_record` 调用后，增加一步：**为该 `ArchiveRecord` 创建一个新的 `SealSplittingTask` 记录**，并将其状态设置为 `pending`，从而自动触发第二阶段的处理。

- **任务 2.7: 创建第二阶段任务 (`process_seal_splitting_task`)**
  - **2.7.1**: 在 `archive_processing/tasks.py` 中创建一个新的Celery任务。
  - **2.7.2**: 此任务负责接收 `SealSplittingTask` 的ID，调用 `seal_splitting_service` 执行处理，并处理成功或失败后的状态更新。

### 5.4 API/视图层 (API/View Layer) - 对外接口

**目标**: 提供前端所需的数据接口，并修复现有API的兼容性。

- **任务 2.8: 修复档案台账在位编辑API**
  - **2.8.1**: 在处理 `PATCH /api/archive-records/records/{record_id}/` 请求的视图中，实现"智能分发器"逻辑，将对 `archiveBoxNumber` 的修改正确路由到 `UploadedFile` 模型。

- **任务 2.9: 开发新监控页面的API**
  - **2.9.1**: 创建新的 `serializers.py` 和 `views.py` 用于 `UploadedFile` 和 `ProcessingTask`。
  - **2.9.2**: 实现 `GET /api/uploaded-files/` 等核心数据获取端点。
  - **2.9.3**: 实现 `PATCH /api/uploaded-files/{file_id}/` 等核心操作端点。

### 5.5 前端层 (Frontend Layer) - 用户界面

**目标**: 构建用户可见的监控界面，并修复受影响的旧功能。

- **任务 2.10: 开发PDF处理监控页面**
  - **2.10.1**: 创建新的页面组件，包含"处理中"、"已完成"、"处理失败"三个Tab。
  - **2.10.2**: 实现AG Grid Master-Detail视图，分别调用新API获取并展示数据。
  - **2.10.3**: 实现编辑盒号、智能重试、重新上传等核心交互功能。

- **任务 2.11: 修复档案台账在位编辑功能**
  - **2.11.1**: 在 `records/ledger/page.tsx` 中，确保对档案盒号的在位编辑能够正确调用 `PATCH .../{record_id}/` API并发送 `archiveBoxNumber` 键。由于后端的智能分发，前端逻辑可能无需大的改动。

---
*文档版本 V8 (Final)，根据最终讨论共识修订*  
