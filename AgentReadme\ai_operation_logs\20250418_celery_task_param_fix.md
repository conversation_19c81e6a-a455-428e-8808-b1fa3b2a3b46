# Celery任务参数不匹配问题修复

## 问题现象

在上传PDF文件到系统时，任务成功创建但Celery队列处理失败，显示以下错误：

```
process_pdf_task() takes 2 positional arguments but 5 were given
```

错误详情：
```
ERROR 2025-04-18 02:33:57,042 [archive_processing.services.task_service] 发送任务 48b0d2b8-5542-433f-af01-e316889da915 到队列失败: process_pdf_task() takes 2 positional arguments but 5 were given
Traceback (most recent call last):
  File "/app/archive_processing/services/task_service.py", line 58, in create_task
    process_pdf_task.delay(task_record.task_id, uploaded_file.file_id, target_text, user_id)
  File "/usr/local/lib/python3.10/site-packages/celery/app/task.py", line 444, in delay
    return self.apply_async(args, kwargs)
  File "/usr/local/lib/python3.10/site-packages/celery/app/task.py", line 566, in apply_async
    check_arguments(*(args or ()), **(kwargs or {}))
TypeError: process_pdf_task() takes 2 positional arguments but 5 were given
```

## 问题原因分析

经过代码分析，发现了以下不匹配问题：

1. 在`archive_processing/services/task_service.py`中，`create_task`方法调用Celery任务时传递了4个参数：
   ```python
   process_pdf_task.delay(task_record.task_id, uploaded_file.file_id, target_text, user_id)
   ```

2. 而在`archive_processing/tasks.py`中，`process_pdf_task`函数的定义只接受2个参数：
   ```python
   @shared_task(bind=True, max_retries=3, default_retry_delay=60)
   def process_pdf_task(self, task_id):
       """Celery 任务：异步处理上传的 PDF 文件，并关联档案记录 (Refactored)"""
       # 函数体...
   ```

3. `process_pdf_task`函数已重构为只使用`task_id`参数，从数据库中获取任务信息，不再直接接收其他参数。


## 解决方案

修改`archive_processing/services/task_service.py`中的代码，使其只传递`task_id`参数：

```python
# 修改前
process_pdf_task.delay(task_record.task_id, uploaded_file.file_id, target_text, user_id)

# 修改后
process_pdf_task.delay(task_record.task_id)
```

同时添加了详细的注释说明原因：

```python
# CHANGE: [2025-04-18] 修复参数不匹配问题
# 之前我们传递了多个参数，但process_pdf_task只接受两个参数(self, task_id)
# 由于我们已经在数据库存储了所有必要信息，只需传递task_id即可
```

这种修改是基于现有代码结构的设计原则：任务信息已经存储在数据库的`ProcessingTask`表中，包括与上传文件的关联、处理参数等。Celery任务只需要接收`task_id`，然后从数据库中获取其他必要信息。

## 影响范围

此修改不会影响系统的功能和数据流，只是修正了参数传递方式，使其与实际函数定义匹配：

1. 对任务创建过程的影响：
   - 修复了任务入队失败的问题
   - 不改变原有业务逻辑和数据存储结构

2. 对任务处理的影响：
   - 所有必要的参数仍然可以从数据库中获取
   - 不影响任务处理的功能实现

## 测试结果

在本地开发环境中确认修复有效：
- 参数传递方式正确匹配
- 文件上传和处理流程正常工作

## 未来优化建议

1. **参数验证优化**：
   - 考虑在`process_pdf_task.delay()`调用处添加参数验证
   - 添加单元测试确保任务参数与函数定义一致

2. **错误处理增强**：
   - 对队列任务失败提供更详细的错误报告
   - 考虑在开发环境中实现任务参数检查的预警机制

3. **文档更新**：
   - 更新相关API文档，明确Celery任务的参数要求
   - 在任务函数上添加更详细的参数说明 