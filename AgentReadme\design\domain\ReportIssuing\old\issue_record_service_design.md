# IssueRecordService 数据服务层设计文档

## 📂 服务依赖关系

### 架构定位

```mermaid
graph TD
    BS[IssueBusinessService<br/>统一发放业务服务] --> TS[IssueTransactionService<br/>事务服务层]
    TS --> IRS[IssueRecordService<br/>数据服务层]
    TS --> ARS[ArchiveRecordService<br/>档案数据服务]
    TS --> AS[AuditService<br/>审计服务]
    
    subgraph "服务边界"
        IRS
        direction TB
        IRS_NOTE["✅ 只操作 IssueRecord 模型<br/>✅ 纯粹的数据服务层<br/>❌ 不处理跨服务协调<br/>❌ 不处理档案状态同步"]
    end
    
    style IRS fill:#e1f5fe
    style TS fill:#fff8e1
    style IRS_NOTE fill:#f9f9f9
    
    %% 说明：所有跨服务协调都通过 TransactionService 进行
    %% BusinessService 整合发放单、发放记录、档案状态的完整业务流程
    %% TransactionService 负责数据一致性和跨模型同步逻辑
```

### 依赖说明

- **IssueRecordService**: 纯粹数据服务，专注 IssueRecord CRUD 操作
- **IssueBusinessService**: 统一发放业务服务，整合发放单、发放记录、档案状态的完整业务流程
- **IssueTransactionService**: 事务协调层，负责跨服务协调和数据一致性，包含所有跨模型同步逻辑
- **ArchiveRecordService**: 档案数据服务，由事务层调用处理档案状态同步

### 服务边界原则

1. **单一职责**: 数据服务层只管理自己的模型，不做跨服务协调
2. **领域边界**: 业务服务层专注自己领域的业务逻辑，不跨领域调用
3. **事务协调**: 所有跨服务协调都由 TransactionService 统一处理
4. **纯粹数据操作**: 数据服务层专注 CRUD，不处理业务逻辑

## 📋 文档信息

- **文档名称**: IssueRecordService 数据服务层设计
- **版本**: v1.0
- **创建日期**: 2025-06-08
- **最后更新**: 2025-06-08 (架构统一优化)
- **相关文档**:
  - [发放单模块服务层架构设计](./service_layer_architecture_design.md)
  - [发放单生命周期设计 V2](./issue_form_lifecycle_design_v2.md)

## 🎯 设计目标

### 主要目标

1. **纯数据操作**: 专注于IssueRecord和ArchiveRecord的纯数据CRUD操作
2. **状态同步职责**: 确保IssueRecord与ArchiveRecord之间的状态一致性
3. **边界清晰**: 与业务逻辑计算和审计功能明确分离
4. **高性能**: 提供高效的数据查询和批量操作接口

### 解决的问题

- **职责混乱**: 原有服务混合了数据操作、业务逻辑和审计功能
- **发放状态同步**: 发放记录与档案记录的发放状态同步机制不完善
- **验证分散**: 数据验证逻辑分散在各处，缺乏统一管理
- **性能问题**: 缺乏针对性的批量操作和查询优化

## 🏗️ 架构定位

### 在整体架构中的位置

```mermaid
graph TB
    subgraph "业务服务层"
        A[IssueBusinessService<br/>统一发放业务服务]
    end
    
    subgraph "事务服务层"
        B[IssueTransactionService<br/>跨模型事务协调]
    end
    
    subgraph "数据服务层"
        C[IssueRecordService<br/>发放记录数据操作]
        D[ArchiveRecordService<br/>档案记录操作]
        E[IssueFormService<br/>发放单操作]
    end
    
    subgraph "审计服务层"
        F[AuditService<br/>统一审计管理]
    end
    
    subgraph "数据模型层"
        G[(IssueRecord)]
        H[(ArchiveRecord)]
        I[(IssueForm)]
    end
    
    A -.->|只读操作| C
    A -.->|只读操作| D
    A -.->|只读操作| E
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    C --> G
    D --> H
    E --> I
    
    style C fill:#e1f5fe
    style B fill:#fff8e1
    
    %% 关系说明
    %% A -.-> C/D/E: 业务层可只读访问各数据服务
    %% A -> B: 业务层所有写入操作都通过事务层
    %% B -> C/D/E: 事务层协调各数据服务的写入操作
    %% B -> F: 事务层调用审计服务记录操作
    %% 跨模型同步逻辑完全在事务层B中实现
```

### 职责边界

- ✅ **负责**: IssueRecord 的完整CRUD操作
- ✅ **负责**: 基础数据格式验证和引用完整性检查  
- ✅ **负责**: 发放记录的批量软删除管理
- ❌ **不负责**: 直接操作 ArchiveRecord 模型
- ❌ **不负责**: 档案状态同步（由事务层统一处理）
- ❌ **不负责**: 跨模型同步逻辑（由事务层统一处理）
- ❌ **不负责**: 业务逻辑计算（发放类型、份数计算等，由业务层处理）
- ❌ **不负责**: 审计记录创建和管理
- ❌ **不负责**: 跨模型业务规则验证

## 📚 服务详细设计

### 核心接口定义

```python
class IssueRecordService:
    """
    发放记录数据服务类
    
    专注于发放记录的纯数据操作，遵循单一职责原则。
    发放记录采用简单模型：存在即表示已发放，通过批量软删除管理撤销发放。
    不处理档案状态同步，由业务层负责多服务协调。
    """
```

## 📝 方法分类详细设计

### 1. 核心创建方法

```python
def create_issue_records_for_form(self, issue_form, records_data) -> List[IssueRecord]:
    """
    为发放单批量创建发放记录
    
    接收预处理的记录数据，执行纯数据创建操作。
    业务逻辑计算（如发放类型、份数）由业务层完成后传入。
    
    Args:
        issue_form: 发放单对象
        records_data: 预处理的发放记录数据列表
        
    Returns:
        List[IssueRecord]: 创建的发放记录列表
        
    Note:
        - 数据格式已在业务层验证
        - 自动处理序号分配
        - 档案状态同步由业务层处理
    """
    
def create_single_issue_record(self, issue_record_data) -> IssueRecord:
    """
    创建单个发放记录
    
    Args:
        issue_record_data: 完整的发放记录数据
        
    Returns:
        IssueRecord: 创建的发放记录对象
        
    Note:
        - 纯数据创建操作，档案状态同步由业务层处理
    """
    
def _create_issue_record_instance(self, validated_data) -> IssueRecord:
    """
    内部方法：创建发放记录实例
    
    Args:
        validated_data: 已验证的数据字典
        
    Returns:
        IssueRecord: 新建的发放记录对象
    """
```

### 2. 查询方法

```python
def get_issue_record(self, record_id, include_deleted=False) -> IssueRecord:
    """
    根据ID获取发放记录
    
    Args:
        record_id: 发放记录ID
        include_deleted: 是否包含已删除记录
        
    Returns:
        IssueRecord: 发放记录对象
        
    Raises:
        IssueRecord.DoesNotExist: 记录不存在时
    """
    
def get_issue_records_by_form(self, issue_form_id, include_deleted=False) -> QuerySet:
    """
    获取指定发放单的所有发放记录
    
    Args:
        issue_form_id: 发放单ID
        include_deleted: 是否包含已删除记录
        
    Returns:
        QuerySet: 发放记录查询集
    """
    
def get_issue_records_by_archive(self, archive_record_id, include_deleted=False) -> QuerySet:
    """
    获取指定档案记录的所有发放记录
    
    Args:
        archive_record_id: 档案记录ID
        include_deleted: 是否包含已删除记录
        
    Returns:
        QuerySet: 发放记录查询集
    """
    
def list_issue_records(self, filters=None, page=1, page_size=20) -> dict:
    """
    分页查询发放记录列表
    
    Args:
        filters: 查询过滤条件
        page: 页码
        page_size: 每页数量
        
    Returns:
        dict: 分页结果 {'records': [...], 'total': count, 'page': page, 'page_size': page_size}
    """
    
def search_issue_records(self, search_params) -> QuerySet:
    """
    根据搜索条件查询发放记录
    
    Args:
        search_params: 搜索参数字典
        
    Returns:
        QuerySet: 符合条件的发放记录查询集
    """
```

### 3. 更新方法

```python
def update_issue_record(self, record_id, update_data) -> IssueRecord:
    """
    更新发放记录信息
    
    Args:
        record_id: 发放记录ID
        update_data: 更新数据字典
        
    Returns:
        IssueRecord: 更新后的发放记录对象
        
    Note:
        - 仅更新基本数据字段（如备注、发放份数等）
        - 不涉及状态字段，发放记录存在即表示已发放
        - 如更新发放份数，需通过 ArchiveRecordService 重新计算档案状态
    """
    
def batch_update_issue_records(self, record_ids, update_data) -> List[IssueRecord]:
    """
    批量更新发放记录
    
    Args:
        record_ids: 记录ID列表
        update_data: 统一的更新数据
        
    Returns:
        List[IssueRecord]: 更新后的发放记录列表
        
    Note:
        - 适用于批量修改备注、发放份数等基本信息
        - 避免频繁的数据库单条更新操作
    """
```

### 4. 删除管理

```python
def batch_soft_delete_records(self, record_ids, reason=None) -> List[IssueRecord]:
    """
    批量软删除发放记录（批量撤销发放）
    
    Args:
        record_ids: 发放记录ID列表
        reason: 撤销原因
        
    Returns:
        List[IssueRecord]: 撤销后的发放记录列表
        
    Note:
        - 设置 is_deleted=True 和删除时间
        - 这是撤销发放的主要方式
        - 支持单个或批量撤销（传入单个ID的列表即可）
        - 档案状态同步由业务层处理
    """
```

### 6. 基础统计查询

```python
def get_active_issue_count_by_form(self, issue_form_id) -> int:
    """
    获取发放单的有效发放记录数量
    
    Args:
        issue_form_id: 发放单ID
        
    Returns:
        int: 有效发放记录数量（不包含已撤销）
    """
```

### 7. 关联数据查询

```python
def get_archive_issue_history(self, archive_record_id) -> QuerySet:
    """
    获取档案记录的发放历史
    
    Args:
        archive_record_id: 档案记录ID
        
    Returns:
        QuerySet: 按时间倒序的发放记录查询集
        
    Note:
        - 包含已撤销的记录，由调用方决定是否过滤
        - 主要用于历史查看，不用于业务逻辑计算
    """
```

### 8. 基础数据验证

```python
def validate_issue_record_data_format(self, data) -> dict:
    """
    验证发放记录数据格式的正确性
    
    Args:
        data: 待验证的数据字典
        
    Returns:
        dict: 验证结果 {'is_valid': bool, 'errors': [...]}
        
    Note:
        - 仅验证数据格式：字段类型、必填项等
        - 不包含业务规则验证，不验证外键引用
    """
    
def check_issue_record_exists(self, record_id) -> bool:
    """
    检查发放记录是否存在
    
    Args:
        record_id: 发放记录ID
        
    Returns:
        bool: 存在返回True，否则False
    """
```

### 9. 内部工具方法

```python
def _build_issue_record_data(self, base_data, computed_data) -> dict:
    """
    构建发放记录数据
    
    Args:
        base_data: 基础数据字典
        computed_data: 计算后的数据字典
        
    Returns:
        dict: 完整的发放记录数据
    """
    
def _get_next_sequence_number(self, issue_form_id) -> int:
    """
    获取下一个序号
    
    Args:
        issue_form_id: 发放单ID
        
    Returns:
        int: 下一个可用序号
    """
    
def _format_issue_record_response(self, issue_record) -> dict:
    """
    格式化发放记录响应数据
    
    Args:
        issue_record: 发放记录对象
        
    Returns:
        dict: 格式化的响应数据
    """
```

## 🔄 典型调用流程

### 1. 发放记录创建流程

```mermaid
sequenceDiagram
    participant BS as IssueBusinessService
    participant TS as IssueTransactionService
    participant IRS as IssueRecordService
    participant ARS as ArchiveRecordService
    
    BS->>BS: 业务验证和发放类型计算
    BS->>TS: 发放单发放请求(含计算结果)
    TS->>TS: 创建审计批次
    TS->>IRS: create_issue_records_for_form(records_data)
    IRS->>IRS: _create_issue_record_instance()
    IRS->>IRS: _get_next_sequence_number()
    IRS-->>TS: 返回创建的发放记录
    TS->>TS: 跨模型同步逻辑
    TS->>ARS: update_archive_issue_status(archive_id, issue_data)
    ARS->>ARS: 更新档案发放状态
    ARS-->>TS: 返回更新结果
    TS->>TS: 完成审计记录
    TS-->>BS: 提交事务，返回完整结果
```

### 2. 撤销发放流程

```mermaid
sequenceDiagram
    participant BS as IssueBusinessService
    participant TS as IssueTransactionService
    participant IRS as IssueRecordService
    participant ARS as ArchiveRecordService
    
    BS->>BS: 业务验证和权限检查
    BS->>TS: 撤销发放请求
    TS->>TS: 创建审计批次
    TS->>IRS: batch_soft_delete_records(record_ids, reason)
    IRS->>IRS: _mark_as_deleted()
    IRS-->>TS: 返回撤销的发放记录
    TS->>TS: 档案状态重新计算逻辑
    TS->>ARS: recalculate_archive_issue_status(archive_ids)
    ARS->>ARS: 重新计算档案发放状态
    ARS-->>TS: 返回更新结果
    TS->>TS: 完成审计记录
    TS-->>BS: 提交事务，返回完整结果
```

## 📊 性能考量

### 1. 查询优化

- **索引策略**: 在 `issue_form_id`, `archive_record_id`, `created_at` 等字段上建立索引
- **批量操作**: 提供批量查询和更新接口，减少数据库往返次数
- **分页查询**: 大数据量查询支持分页，避免内存溢出

### 2. 数据服务层优化

- **纯数据操作**: 专注CRUD操作，避免复杂业务逻辑计算
- **批量操作**: 提供批量删除等接口，减少数据库往返次数
- **查询优化**: 合理使用查询集，避免不必要的数据加载
- **简单统计**: 仅提供基础统计，复杂计算由业务层处理

### 3. 数据一致性

- **事务协调**: 数据一致性由上层事务服务保证
- **原子操作**: 单个服务内的操作保持原子性
- **错误处理**: 提供清晰的错误信息便于上层处理
- **数据完整性**: 确保单个模型的数据完整性和约束

## 🧪 测试策略

### 1. 单元测试

```python
class TestIssueRecordService:
    """IssueRecordService 单元测试"""
    
    def test_create_issue_record_success(self):
        """测试成功创建发放记录"""
        
    def test_archive_service_coordination(self):
        """测试与档案服务的协调机制"""
        
    def test_validate_data_format(self):
        """测试数据格式验证"""
        
    def test_batch_operations(self):
        """测试批量操作性能"""
        
    def test_service_boundary_compliance(self):
        """测试服务边界遵守情况"""
```

### 2. 集成测试

- **服务协调测试**: 验证与 ArchiveRecordService 的正确协调机制
- **并发发放测试**: 验证在并发发放场景下数据一致性
- **批量操作测试**: 验证批量发放和批量撤销的性能表现
- **撤销恢复测试**: 验证发放记录撤销和恢复的完整流程
- **服务边界测试**: 确保不直接操作 ArchiveRecord 模型

### 3. 边界测试

- **数据边界**: 测试极限数据量的处理能力
- **异常场景**: 测试各种异常情况的处理（如数据库连接断开）
- **发放额度边界**: 测试超出档案记录可发放份数的边界情况
- **并发撤销**: 测试同时撤销同一档案记录多个发放记录的场景

## ✅ 设计优势

### 1. **职责纯粹**

- 专注于数据操作，不混杂业务逻辑和审计功能
- 清晰的方法分类，便于理解和维护

### 2. **高性能**

- 提供批量操作接口，优化数据库访问
- 合理的查询策略和索引设计

### 3. **服务边界清晰**

- 通过依赖注入与 ArchiveRecordService 协调，不越权操作其他模型
- 维护单一职责原则，专注于 IssueRecord 数据操作
- 支持发放记录的撤销和恢复，通过服务协调维护数据一致性

### 4. **可测试性**

- 纯函数设计，便于单元测试
- 清晰的输入输出，易于验证正确性

### 5. **可扩展性**

- 模块化设计，便于功能扩展
- 标准化接口，便于其他服务复用

## 🚀 未来优化方向

### 1. 性能优化

- 引入查询缓存机制
- 优化批量操作的SQL生成
- 考虑读写分离策略

### 2. 监控和诊断

- 添加关键操作的性能监控
- 提供数据一致性检查工具
- 增加详细的操作日志

### 3. 扩展功能

- 支持更复杂的查询条件
- 提供数据导出和导入功能
- 支持历史数据归档

---

**文档维护**: 本文档应随着代码实现和需求变化及时更新，确保设计与实现的一致性。
