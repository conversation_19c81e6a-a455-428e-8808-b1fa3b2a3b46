"use client"

import { TablePageLayout } from "@/components/common/table-page-layout"
import { Button } from "@/components/ui/button"
import { Upload } from "lucide-react"
import Link from "next/link"
import { PageAction } from "@/components/common/page-header"
import ImportHistoryClientGrid from "@/components/records/import-history/import-history-client-grid"

export default function ImportHistoryPage() {
  // 定义操作按钮数组
  const actions: PageAction[] = [
    {
      label: "导入新文件",
      icon: <Upload className="h-4 w-4" />,
      href: "/records/import",
      variant: "outline"
    }
  ]

  return (
    <TablePageLayout
      title="导入历史"
      subtitle="查看所有Excel文件导入的历史记录和处理结果"
      actions={actions}
    >
      <div className="h-full">
        {/* 使用 ImportHistoryClientGrid 组件代替 ImportHistory 组件 */}
        <ImportHistoryClientGrid pageSize={10} />
      </div>
    </TablePageLayout>
  )
} 