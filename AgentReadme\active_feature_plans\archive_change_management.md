# 档案记录正式更改管理功能设计

## 功能概述

档案记录正式更改管理功能允许用户对已归档的档案记录进行规范化的变更操作。此功能包括创建更改单、指定变更内容、执行变更以及记录变更历史。通过此功能，系统确保对已归档记录的任何变更都是可追踪和可审计的。

## 业务需求

1. **基础数据管理**:
   - 支持创建、查看、修改和删除更改单
   - 支持为更改单添加多条变更详情
   - 支持一个更改单涉及多个档案记录

2. **变更管理**:
   - 支持选择已归档的档案记录进行变更
   - 支持指定要变更的具体字段和新值
   - 提供变更原因说明功能
   - 可选上传相关附件（如变更依据的扫描件）

3. **执行与记录**:
   - 提供变更执行功能
   - 变更执行后自动记录变更历史
   - 更新档案记录的变更次数计数
   - 保持完整的变更前后状态记录

4. **查询与展示**:
   - 支持按状态、创建人、时间等条件查询更改单
   - 支持查看更改单详情和变更内容
   - 提供变更前后对比功能
   - 支持查看特定档案记录的变更历史

## 设计原则

此功能设计遵循系统核心数据流架构（参见 `AgentReadme/framework_docs/core_data_flow_architecture.md`），具体应用为：

```flow
ChangeOrder → ChangeDetail → ChangeLogBatch → ArchiveRecord → RecordChangeLog
```

## 数据模型设计

### ChangeOrder (更改单)

```python
class ChangeOrder(models.Model):
    """更改单模型，用于管理对已归档记录的正式变更申请"""
    
    # 基本信息
    id = models.AutoField(primary_key=True)
    order_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    title = models.CharField(max_length=200, verbose_name="标题")
    description = models.TextField(verbose_name="变更说明")
    
    # 状态信息
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('executed', '已执行'),
        ('canceled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # 创建信息
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, 
                                   related_name='created_change_orders')
    created_at = models.DateTimeField(auto_now_add=True)
    
    # 执行信息
    executed_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, 
                                   blank=True, related_name='executed_change_orders')
    executed_at = models.DateTimeField(null=True, blank=True)
    execution_notes = models.TextField(blank=True, null=True)
    
    # 相关文件
    attached_file = models.FileField(upload_to='change_orders/', null=True, blank=True)
    
    # 批次关联
    change_batch = models.ForeignKey(ChangeLogBatch, on_delete=models.SET_NULL, 
                                    null=True, blank=True, related_name='change_orders')
```

### ChangeDetail (变更详情)

```python
class ChangeDetail(models.Model):
    """变更详情模型，记录更改单中每个字段的变更信息"""
    
    id = models.AutoField(primary_key=True)
    change_order = models.ForeignKey(ChangeOrder, on_delete=models.CASCADE, 
                                    related_name='change_details')
    archive_record = models.ForeignKey(ArchiveRecord, on_delete=models.CASCADE, 
                                      related_name='change_details')
    
    # 字段信息
    field_name = models.CharField(max_length=100)
    field_label = models.CharField(max_length=100)
    old_value = models.TextField(blank=True, null=True)
    new_value = models.TextField(blank=True, null=True)
    
    # 变更原因
    change_reason = models.TextField(blank=True, null=True)
    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## API接口设计

### 更改单管理接口

```api
GET    /api/change-orders/                # 获取更改单列表
POST   /api/change-orders/                # 创建更改单
GET    /api/change-orders/{id}/           # 获取更改单详情
PUT    /api/change-orders/{id}/           # 更新更改单
DELETE /api/change-orders/{id}/           # 删除更改单
POST   /api/change-orders/{id}/execute/   # 执行更改单
POST   /api/change-orders/{id}/cancel/    # 取消更改单
GET    /api/change-orders/changeable-fields/  # 获取可变更字段列表
```

### 变更详情接口

```api
GET    /api/change-details/                       # 获取变更详情列表
POST   /api/change-details/                       # 创建变更详情
PUT    /api/change-details/{id}/                  # 更新变更详情
DELETE /api/change-details/{id}/                  # 删除变更详情
POST   /api/change-details/batch-create/          # 批量创建变更详情
GET    /api/change-details/by-change-order/       # 按更改单获取分组的变更详情
```

## 业务流程

### 创建更改单流程

1. 用户创建新的更改单，填写标题和描述
2. 用户选择要变更的档案记录
3. 系统显示可变更的字段列表
4. 用户指定要变更的字段、新值和变更原因
5. 系统创建更改单和变更详情记录
6. 更改单状态为"草稿"

### 执行更改单流程

1. 用户选择一个草稿状态的更改单
2. 用户点击"执行"按钮
3. 系统执行以下操作（在一个事务中）：
   - 创建变更批次(ChangeLogBatch)
   - 从变更详情提取数据
   - 应用变更到对应的档案记录
   - 创建记录变更日志(RecordChangeLog)
   - 更新更改单状态为"已执行"
   - 关联更改单和变更批次

### 查询更改单流程

1. 用户根据条件（状态、创建人、时间等）查询更改单
2. 系统返回符合条件的更改单列表
3. 用户点击查看特定更改单详情
4. 系统显示更改单基本信息和变更详情
5. 用户可以查看变更前后对比

## 服务实现

### ChangeOrderService

```python
class ChangeOrderService:
    """更改单服务，处理更改单的创建、执行等业务逻辑"""
    
    def create_change_order(self, title, description, created_by, changes_data=None, attached_file=None):
        """创建更改单"""
        # 实现创建更改单逻辑...
    
    def execute_change_order(self, change_order_id, user, execution_notes=None):
        """执行更改单，将变更应用到档案记录"""
        # 实现执行更改单逻辑...
    
    def cancel_change_order(self, change_order_id, user, cancel_reason=None):
        """取消更改单"""
        # 实现取消更改单逻辑...
    
    def get_changeable_fields(self):
        """获取可变更的字段列表"""
        # 实现获取可变更字段逻辑...
```

### ChangeSourceInterface实现

```python
class ChangeOrderSource(ChangeSourceInterface):
    """更改单作为变更来源的实现"""
    
    def __init__(self, change_order):
        self.change_order = change_order
    
    def get_source_type(self):
        return 'change_order'
    
    def get_source_id(self):
        return str(self.change_order.id)
    
    def get_change_details(self):
        return self.change_order.change_details.all()
    
    def get_executor(self):
        return self.change_order.executed_by
    
    def get_execution_context(self):
        return {
            'title': self.change_order.title,
            'description': self.change_order.description,
            'execution_notes': self.change_order.execution_notes
        }
    
    def update_status(self, status, batch_id=None):
        self.change_order.status = status
        if batch_id and status == 'executed':
            self.change_order.change_batch_id = batch_id
            self.change_order.executed_at = timezone.now()
        self.change_order.save()
```

## 用户界面设计

### 更改单列表页面

- 显示更改单列表，包括标题、状态、创建人、创建时间等信息
- 提供筛选和搜索功能
- 提供创建新更改单的入口
- 显示每个更改单涉及的记录数量和字段数量

### 更改单创建/编辑页面

- 基本信息区：标题、描述、附件上传
- 档案记录选择区：可搜索和选择已归档记录
- 变更详情编辑区：选择字段、填写新值、说明变更原因
- 变更预览区：显示变更前后对比

### 更改单详情页面

- 显示更改单基本信息
- 显示变更详情列表，按档案记录分组
- 提供执行/取消操作按钮
- 显示执行状态和历史

## 注意事项与约束

1. **数据一致性**：
   - 所有变更操作必须在事务中执行
   - 确保变更批次和变更日志的完整性

2. **字段约束**：
   - 某些关键字段可能不允许变更（如统一编号）
   - 某些字段可能有格式或业务规则约束

3. **性能考虑**：
   - 大量变更的情况下，可能需要分批处理
   - 变更历史查询可能需要优化

## 未来扩展

1. **审批流程**：
   - 可添加审批环节，增加状态"待审批"、"已批准"
   - 添加审批人、审批意见等字段

2. **变更统计与分析**：
   - 统计不同类型变更的频率
   - 分析变更模式和趋势

3. **批量操作优化**：
   - 支持批量选择记录和字段
   - 优化大批量变更的性能
