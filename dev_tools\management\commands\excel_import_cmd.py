"""
Excel档案记录导入命令

此命令用于从Excel文件批量导入档案记录到数据库。支持以下功能：
1. 自动创建导入用户（如果不存在）
2. 导入Excel数据到档案记录系统
3. 以表格形式显示导入结果统计
4. 错误日志记录和摘要显示

用法:
    python manage.py excel_import_cmd <excel_file> [--username <username>] [--limit <num>]

参数:
    excel_file          Excel文件的路径（必需）
    --username          执行导入的用户名（可选，默认为'admin'）
    --limit             显示导入记录的数量（可选，默认为10条）

示例:
    # 使用默认用户名('admin')导入数据
    python manage.py excel_import_cmd data.xlsx
    
    # 使用测试数据导入
    python manage.py excel_import_cmd test_suite/test_files/excel/valid_data.xlsx

    # 使用指定用户名导入数据
    python manage.py excel_import_cmd data.xlsx --username john_doe
    
    # 显示更多导入记录
    python manage.py excel_import_cmd data.xlsx --limit 20

注意事项:
    1. Excel文件必须符合预定义的模板格式
    2. 如果指定的用户不存在，将自动创建该用户
    3. 导入过程会显示实时进度和错误信息
    
文件命名说明:
    此文件与业务逻辑中的 excel_import.py 服务类配合使用，是命令行接口。
"""

import os
import time
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import connection
from archive_records.services.excel_import import ExcelImportService
from archive_records.models import ArchiveRecord
from prettytable import PrettyTable

class Command(BaseCommand):
    help = '从Excel文件导入档案记录'

    def add_arguments(self, parser):
        parser.add_argument('excel_file', type=str, help='Excel文件的路径')
        parser.add_argument('--username', type=str, default='admin', help='执行导入的用户名')
        # 新增参数：限制显示的记录数
        parser.add_argument('--limit', type=int, default=10, help='显示的记录数量')

    def handle(self, *args, **options):
        # 美化输出标题
        self.stdout.write(self.style.SUCCESS('='*80))
        self.stdout.write(self.style.SUCCESS('档案记录Excel导入工具'))
        self.stdout.write(self.style.SUCCESS('='*80))
        
        excel_file = options['excel_file']
        username = options['username']
        limit = options['limit']  # 新增参数
        
        # 文件验证
        if not os.path.exists(excel_file):
            self.stdout.write(self.style.ERROR(f'错误: 文件不存在 - {excel_file}'))
            return
            
        # 显示文件信息
        file_size = os.path.getsize(excel_file) / 1024  # KB
        self.stdout.write(f'文件路径: {excel_file}')
        self.stdout.write(f'文件大小: {file_size:.2f} KB')

        try:
            # 获取或创建用户
            user, created = User.objects.get_or_create(
                username=username,
                defaults={'is_staff': True}
            )
            if created:
                user.set_password('admin123')  # 设置默认密码
                user.save()
                self.stdout.write(self.style.SUCCESS(f'创建新用户: {username} (密码: admin123)'))
            else:
                self.stdout.write(f'使用现有用户: {username}')

            # 导入数据（计时）
            start_time = time.time()
            self.stdout.write(self.style.SUCCESS('\n开始导入数据...'))
            
            service = ExcelImportService()
            import_log = service.import_from_file(excel_file, user=user)
            
            # 显示导入时间
            elapsed_time = time.time() - start_time
            self.stdout.write(self.style.SUCCESS(f'导入完成! 耗时: {elapsed_time:.2f} 秒\n'))

            # 以表格形式显示导入结果统计
            stats_table = PrettyTable()
            stats_table.field_names = ["状态", "总记录数", "成功记录数", "失败记录数", "处理时间(秒)"]
            stats_table.add_row([
                import_log.status,
                import_log.total_records,
                import_log.success_records,
                import_log.failed_records,
                f"{import_log.processing_time:.2f}"
            ])
            self.stdout.write(str(stats_table))
            
            # 显示错误日志摘要
            if import_log.error_log:
                error_lines = import_log.error_log.split('\n')
                total_errors = len(error_lines)
                
                self.stdout.write(self.style.WARNING(f'\n错误日志摘要 ({min(5, total_errors)}/{total_errors}):'))
                for i, error in enumerate(error_lines[:5]):
                    self.stdout.write(f' - {error}')
                    
                if total_errors > 5:
                    self.stdout.write(f' - ... 还有 {total_errors - 5} 个错误 ...')
                    self.stdout.write(self.style.WARNING('使用 --verbosity 2 参数可查看完整错误日志'))

            # 显示导入的记录（如果有的话）
            if import_log.success_records > 0:
                self.display_imported_records(limit)
                
            # 结束信息
            self.stdout.write('\n' + '-'*80)
            self.stdout.write(self.style.SUCCESS(f'导入批次号: {import_log.batch_number}'))
            self.stdout.write(self.style.SUCCESS('可在管理界面查看更多数据: python manage.py runserver'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'导入失败: {str(e)}'))
            
    def display_imported_records(self, limit):
        """以表格形式显示导入的记录"""
        records = ArchiveRecord.objects.all().order_by('-created_at')[:limit]
        
        if not records:
            return
            
        self.stdout.write(self.style.SUCCESS(f'\n成功导入的记录 (显示前 {len(records)} 条):'))
        
        # 创建表格
        table = PrettyTable()
        table.field_names = ["委托编号", "样品编号", "统一编号", "工程名称", "委托单位", "委托日期"]
        table.align = "l"  # 左对齐
        table.max_width = 30  # 限制列宽度
        
        # 添加数据行
        for record in records:
            # 截断过长的文本
            project_name = (record.project_name[:27] + '...') if record.project_name and len(record.project_name) > 30 else record.project_name
            client_unit = (record.client_unit[:27] + '...') if record.client_unit and len(record.client_unit) > 30 else record.client_unit
            commission_date = record.commission_datetime.strftime('%Y-%m-%d') if record.commission_datetime else 'N/A'
            
            table.add_row([
                record.sample_number,
                record.commission_number,
                record.unified_number,
                project_name,
                client_unit,
                commission_date
            ])
        
        # 打印表格
        self.stdout.write(str(table))
        
        # 如果有更多记录，显示提示
        total_count = ArchiveRecord.objects.count()
        if total_count > limit:
            self.stdout.write(f'(显示 {limit}/{total_count} 条记录。使用 --limit 参数可查看更多)') 