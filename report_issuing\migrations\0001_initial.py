# Generated by Django 5.1.8 on 2025-05-16 12:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('archive_records', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='IssueForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=100, unique=True, verbose_name='发放单编号')),
                ('issue_date', models.DateTimeField(verbose_name='发放日期')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='领取人姓名')),
                ('receiver_unit', models.CharField(max_length=200, verbose_name='领取单位')),
                ('receiver_phone', models.CharField(max_length=50, verbose_name='领取人电话')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('locked', '锁定'), ('confirmed', '已确认'), ('archived', '已归档'), ('deleted', '已删除')], default='draft', max_length=20, verbose_name='状态')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('confirmation_file', models.FileField(blank=True, null=True, upload_to='issue_confirmations/%Y/%m/', verbose_name='签字确认单')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注说明')),
                ('deletion_reason', models.TextField(blank=True, verbose_name='删除原因')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_issue_forms', to=settings.AUTH_USER_MODEL, verbose_name='删除人')),
                ('issuer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='issued_forms', to=settings.AUTH_USER_MODEL, verbose_name='发放人')),
            ],
            options={
                'verbose_name': '发放单',
                'verbose_name_plural': '发放单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='IssueFormItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('copies', models.PositiveIntegerField(verbose_name='发放份数')),
                ('receiver_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='领取人姓名')),
                ('receiver_unit', models.CharField(blank=True, max_length=200, null=True, verbose_name='领取单位')),
                ('receiver_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='领取人电话')),
                ('issue_type', models.CharField(choices=[('single', '发放1份'), ('all_remaining', '发放全部')], default='single', max_length=20, verbose_name='发放数量类型')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('archive_record', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='issue_items', to='archive_records.archiverecord', verbose_name='档案记录')),
                ('issue_form', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='report_issuing.issueform', verbose_name='发放单')),
            ],
            options={
                'verbose_name': '发放单条目',
                'verbose_name_plural': '发放单条目',
            },
        ),
        migrations.CreateModel(
            name='IssueRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('issue_type', models.CharField(choices=[('first', '第一次发放'), ('second', '第二次发放')], default='first', max_length=20, verbose_name='发放次数类型')),
                ('issue_date', models.DateTimeField(verbose_name='发放时间')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='领取人姓名')),
                ('receiver_unit', models.CharField(max_length=200, verbose_name='领取单位')),
                ('receiver_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='领取人电话')),
                ('copies', models.PositiveIntegerField(default=1, verbose_name='发放份数')),
                ('source', models.CharField(choices=[('issue_form', '发放单生成'), ('manual_create', '手动创建'), ('manual_update', '手动更新')], default='manual_create', max_length=30, verbose_name='记录来源')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('legacy_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='旧记录ID')),
                ('archive_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issue_records', to='archive_records.archiverecord', verbose_name='档案记录')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_issue_records', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('issue_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='issue_records', to='report_issuing.issueform', verbose_name='关联发放单')),
                ('issuer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='issued_records', to=settings.AUTH_USER_MODEL, verbose_name='发放人')),
            ],
            options={
                'verbose_name': '发放记录',
                'verbose_name_plural': '发放记录',
            },
        ),
        migrations.CreateModel(
            name='IssueRecordHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('create', '创建'), ('update', '更新'), ('delete', '删除'), ('restore', '恢复'), ('form_delete', '发放单删除')], max_length=20, verbose_name='操作类型')),
                ('before_data', models.JSONField(blank=True, null=True, verbose_name='修改前数据')),
                ('after_data', models.JSONField(blank=True, null=True, verbose_name='修改后数据')),
                ('operation_time', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
                ('operation_reason', models.TextField(blank=True, verbose_name='操作原因')),
                ('issue_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history_records', to='report_issuing.issuerecord', verbose_name='发放记录')),
                ('operator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
            ],
            options={
                'verbose_name': '发放记录历史',
                'verbose_name_plural': '发放记录历史',
                'ordering': ['-operation_time'],
            },
        ),
        migrations.AddIndex(
            model_name='issueform',
            index=models.Index(fields=['number'], name='report_issu_number_7cd132_idx'),
        ),
        migrations.AddIndex(
            model_name='issueform',
            index=models.Index(fields=['status'], name='report_issu_status_5537a3_idx'),
        ),
        migrations.AddIndex(
            model_name='issueform',
            index=models.Index(fields=['issue_date'], name='report_issu_issue_d_b99c6e_idx'),
        ),
        migrations.AddIndex(
            model_name='issueform',
            index=models.Index(fields=['is_deleted'], name='report_issu_is_dele_0996fb_idx'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['issue_form', 'archive_record'], name='report_issu_issue_f_c91954_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='issueformitem',
            unique_together={('issue_form', 'archive_record')},
        ),
        migrations.AddIndex(
            model_name='issuerecord',
            index=models.Index(fields=['archive_record', 'issue_type', 'is_active'], name='report_issu_archive_67357c_idx'),
        ),
        migrations.AddIndex(
            model_name='issuerecord',
            index=models.Index(fields=['issue_form'], name='report_issu_issue_f_d900cf_idx'),
        ),
        migrations.AddIndex(
            model_name='issuerecord',
            index=models.Index(fields=['is_deleted'], name='report_issu_is_dele_9ab372_idx'),
        ),
        migrations.AddIndex(
            model_name='issuerecordhistory',
            index=models.Index(fields=['issue_record', 'operation_time'], name='report_issu_issue_r_6a0092_idx'),
        ),
        migrations.AddIndex(
            model_name='issuerecordhistory',
            index=models.Index(fields=['operator', 'operation_time'], name='report_issu_operato_17af75_idx'),
        ),
    ]
