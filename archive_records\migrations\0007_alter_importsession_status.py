# Generated by Django 5.1.8 on 2025-06-01 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0006_importsession_details_importsession_new_status_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='importsession',
            name='status',
            field=models.CharField(choices=[('select', '选择文件'), ('upload', '文件上传'), ('analysis_start', '分析开始'), ('analyzing', '分析中'), ('analyzed', '分析完成'), ('conflict_resolution_started', '冲突处理已开始'), ('conflict_resolution_in_progress', '冲突处理中'), ('conflict_resolution_pending', '冲突处理暂停'), ('conflict_resolution_completed', '冲突处理完成'), ('queued', '排队等待导入'), ('import_start', '导入开始'), ('importing', '导入中'), ('completed_successfully', '成功完成导入'), ('completed_with_errors', '完成但有错误'), ('error', '出错'), ('cancelled', '已取消'), ('finalized', '已最终处理')], default='select', max_length=50),
        ),
    ]
