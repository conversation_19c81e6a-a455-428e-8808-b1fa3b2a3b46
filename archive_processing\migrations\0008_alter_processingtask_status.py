# Generated by Django 5.1.11 on 2025-06-30 16:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_processing', '0007_alter_processingtask_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='processingtask',
            name='status',
            field=models.CharField(choices=[('pending', '待处理'), ('queued', '已入队'), ('processing', '处理中'), ('chunking', '正在分块'), ('processing_parallel', '并行处理中'), ('aggregating', '汇总处理中'), ('completed', '已完成'), ('completed_without_report', '已完成(报告未分离)'), ('failed_validation', '验证失败'), ('failed', '处理失败')], db_index=True, default='pending', max_length=100, verbose_name='任务状态'),
        ),
    ]
