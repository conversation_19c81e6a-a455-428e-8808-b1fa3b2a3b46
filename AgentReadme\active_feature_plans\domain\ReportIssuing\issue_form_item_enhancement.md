# 发放单数据一致性增强设计文档

## 📋 目录

1. [变更概述](#1-变更概述)
2. [模型设计](#2-模型设计)
3. [数据库迁移](#3-数据库迁移)
4. [业务逻辑调整](#4-业务逻辑调整)
5. [API接口调整](#5-api接口调整)
6. [数据一致性处理](#6-数据一致性处理)
7. [实施计划](#7-实施计划)

---

## 1. 变更概述

### 1.1 变更目标

建立IssueFormItem、IssueRecord和ArchiveRecord之间的数据一致性，确保发放单归档时能正确同步相关信息，并在发放单删除时能妥善处理相关记录。

### 1.2 核心业务规则

1. **归档时同步**：仅当IssueFormItem状态为"归档"时，才触发向IssueRecord和ArchiveRecord的数据同步
2. **软删除事务性处理**：发放单被软删除时，相关的IssueRecord要正确标记删除，ArchiveRecord要清理发放信息
3. **历史记录完整性**：各自的历史模型能正确记录所有变更信息，包括删除原因和操作上下文
4. **模型重构**：简化IssueRecord和IssueRecordHistory模型，移除source字段，用action字段替代operation_type

### 1.3 相关文档

- [发放记录模型重构需求文档](./issue_record_model_refactoring.md) - 详细的模型变更和函数修改需求

### 1.4 业务需求

- **数据一致性**：确保IssueFormItem、IssueRecord、ArchiveRecord三者数据同步
- **事务性处理**：保证同步操作的原子性，避免数据不一致
- **历史记录**：完整保留所有变更历史，支持问题追溯和数据恢复
- **统一同步服务**：通过统一的同步服务处理所有数据同步场景

### 1.3 影响范围

- **数据模型**：IssueFormItem 模型字段变更
- **业务逻辑**：发放单创建、更新、删除逻辑
- **数据库操作**：ArchiveRecord 字段同步逻辑
- **API接口**：发放相关接口响应调整

---

## 2. 模型设计

### 2.1 增强后的 IssueFormItem 模型

```python
# models/issue_form_item.py
class IssueFormItem(models.Model):
    """发放单项目 - 增强版"""
    
    # 原有字段
    issue_form = models.ForeignKey(IssueForm, on_delete=models.CASCADE, verbose_name="发放单")
    archive_record = models.ForeignKey(ArchiveRecord, on_delete=models.CASCADE, verbose_name="档案记录")
    copies = models.PositiveIntegerField(verbose_name="发放份数")
    
    # 新增字段
    first = models.BooleanField(default=False, verbose_name="是否为第一次发放")
    second = models.BooleanField(default=False, verbose_name="是否为第二次发放")
    
    # 元数据和约束
    class Meta:
        db_table = 'issue_form_item'
        verbose_name = "发放单项目"
        verbose_name_plural = "发放单项目"
        constraints = [
            # 确保每个档案在同一发放单中只能有一条记录
            models.UniqueConstraint(
                fields=['issue_form', 'archive_record'],
                name='unique_issue_form_archive'
            ),
            # 确保first和second不能同时为True
            models.CheckConstraint(
                check=~(models.Q(first=True) & models.Q(second=True)),
                name='not_both_first_and_second'
            ),
            # 确保first和second至少有一个为True
            models.CheckConstraint(
                check=models.Q(first=True) | models.Q(second=True),
                name='must_be_first_or_second'
            )
        ]
        indexes = [
            models.Index(fields=['archive_record', 'first']),
            models.Index(fields=['archive_record', 'second']),
            models.Index(fields=['issue_form', 'first']),
            models.Index(fields=['issue_form', 'second']),
        ]
    
    def clean(self):
        """模型验证"""
        super().clean()
        
        # 验证first和second字段的逻辑
        if self.first and self.second:
            raise ValidationError("不能同时标记为第一次发放和第二次发放")
        
        if not self.first and not self.second:
            raise ValidationError("必须标记为第一次发放或第二次发放")
        
        # 验证发放次序的业务逻辑
        if self.archive_record_id:
            self._validate_issue_sequence()
    
    def _validate_issue_sequence(self):
        """验证发放次序"""
        archive = self.archive_record
        
        if self.first:
            # 第一次发放验证
            if archive.first_issue_copies and archive.first_issue_copies > 0:
                # 检查是否已有其他有效的第一次发放记录
                existing_first = IssueFormItem.objects.filter(
                    archive_record=archive,
                    first=True,
                    issue_form__is_active=True,
                    issue_form__is_deleted=False
                ).exclude(pk=self.pk).exists()
                
                if existing_first:
                    raise ValidationError("该档案已有第一次发放记录")
        
        elif self.second:
            # 第二次发放验证
            if archive.second_issue_copies and archive.second_issue_copies > 0:
                # 检查是否已有其他有效的第二次发放记录
                existing_second = IssueFormItem.objects.filter(
                    archive_record=archive,
                    second=True,
                    issue_form__is_active=True,
                    issue_form__is_deleted=False
                ).exclude(pk=self.pk).exists()
                
                if existing_second:
                    raise ValidationError("该档案已有第二次发放记录")
            
            # 验证是否有第一次发放记录
            has_first_issue = (
                archive.first_issue_copies and archive.first_issue_copies > 0 and
                IssueFormItem.objects.filter(
                    archive_record=archive,
                    first=True,
                    issue_form__is_active=True,
                    issue_form__is_deleted=False
                ).exists()
            )
            
            if not has_first_issue:
                raise ValidationError("第二次发放前必须有第一次发放记录")
    
    def save(self, *args, **kwargs):
        """保存时同步更新ArchiveRecord"""
        self.clean()
        super().save(*args, **kwargs)
        
        # 同步更新ArchiveRecord的发放字段
        self._sync_archive_record()
    
    def _sync_archive_record(self):
        """同步更新ArchiveRecord的发放相关字段"""
        archive = self.archive_record
        issue_form = self.issue_form
        
        if self.first:
            # 更新第一次发放信息
            archive.first_issue_copies = self.copies
            archive.first_issue_datetime = issue_form.issue_date
            archive.first_issue_person = issue_form.issue_person
            archive.first_receiver_name = issue_form.receiver_name
            archive.first_receiver_unit = issue_form.receiver_unit
            archive.first_receiver_phone = issue_form.receiver_phone
        
        elif self.second:
            # 更新第二次发放信息
            archive.second_issue_copies = self.copies
            archive.second_issue_datetime = issue_form.issue_date
            archive.second_issue_person = issue_form.issue_person
            archive.second_receiver_name = issue_form.receiver_name
            archive.second_receiver_unit = issue_form.receiver_unit
            archive.second_receiver_phone = issue_form.receiver_phone
        
        archive.save(update_fields=[
            'first_issue_copies', 'first_issue_datetime', 'first_issue_person',
            'first_receiver_name', 'first_receiver_unit', 'first_receiver_phone',
            'second_issue_copies', 'second_issue_datetime', 'second_issue_person',
            'second_receiver_name', 'second_receiver_unit', 'second_receiver_phone'
        ])
    
    @property
    def issue_type(self):
        """返回发放类型"""
        if self.first:
            return 'first'
        elif self.second:
            return 'second'
        return None
    
    def __str__(self):
        issue_type = "第一次" if self.first else "第二次"
        return f"{self.issue_form.form_number} - {self.archive_record.unified_number} ({issue_type}发放{self.copies}份)"
```

### 2.2 相关模型调整

```python
# models/issue_form.py
class IssueForm(models.Model):
    """发放单模型 - 无需变更，但需要调整相关方法"""
    
    def get_first_issue_items(self):
        """获取第一次发放的项目"""
        return self.issueformitem_set.filter(first=True)
    
    def get_second_issue_items(self):
        """获取第二次发放的项目"""
        return self.issueformitem_set.filter(second=True)
    
    def get_issue_summary(self):
        """获取发放汇总信息"""
        first_items = self.get_first_issue_items()
        second_items = self.get_second_issue_items()
        
        return {
            'first_issue': {
                'count': first_items.count(),
                'total_copies': sum(item.copies for item in first_items)
            },
            'second_issue': {
                'count': second_items.count(),
                'total_copies': sum(item.copies for item in second_items)
            }
        }
    
    def delete(self, *args, **kwargs):
        """删除发放单时清理ArchiveRecord的相关字段"""
        # 清理第一次发放记录
        for item in self.get_first_issue_items():
            self._clear_archive_first_issue(item.archive_record)
        
        # 清理第二次发放记录
        for item in self.get_second_issue_items():
            self._clear_archive_second_issue(item.archive_record)
        
        super().delete(*args, **kwargs)
    
    def _clear_archive_first_issue(self, archive_record):
        """清理档案的第一次发放记录"""
        archive_record.first_issue_copies = None
        archive_record.first_issue_datetime = None
        archive_record.first_issue_person = None
        archive_record.first_receiver_name = None
        archive_record.first_receiver_unit = None
        archive_record.first_receiver_phone = None
        archive_record.save(update_fields=[
            'first_issue_copies', 'first_issue_datetime', 'first_issue_person',
            'first_receiver_name', 'first_receiver_unit', 'first_receiver_phone'
        ])
    
    def _clear_archive_second_issue(self, archive_record):
        """清理档案的第二次发放记录"""
        archive_record.second_issue_copies = None
        archive_record.second_issue_datetime = None
        archive_record.second_issue_person = None
        archive_record.second_receiver_name = None
        archive_record.second_receiver_unit = None
        archive_record.second_receiver_phone = None
        archive_record.save(update_fields=[
            'second_issue_copies', 'second_issue_datetime', 'second_issue_person',
            'second_receiver_name', 'second_receiver_unit', 'second_receiver_phone'
        ])
```

---

## 3. 数据库迁移

### 3.1 迁移脚本

```python
# migrations/xxxx_add_first_second_to_issueformitem.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('your_app', 'previous_migration'),
    ]

    operations = [
        # 添加新字段
        migrations.AddField(
            model_name='issueformitem',
            name='first',
            field=models.BooleanField(default=False, verbose_name='是否为第一次发放'),
        ),
        migrations.AddField(
            model_name='issueformitem',
            name='second',
            field=models.BooleanField(default=False, verbose_name='是否为第二次发放'),
        ),
        
        # 数据迁移：根据现有数据设置first/second标志
        migrations.RunPython(
            code=migrate_existing_data,
            reverse_code=migrations.RunPython.noop,
        ),
        
        # 添加约束
        migrations.AddConstraint(
            model_name='issueformitem',
            constraint=models.CheckConstraint(
                check=models.Q(models.Q(('first', True), ('second', False)) | models.Q(('first', False), ('second', True))),
                name='must_be_first_or_second',
            ),
        ),
        migrations.AddConstraint(
            model_name='issueformitem',
            constraint=models.CheckConstraint(
                check=~(models.Q(first=True) & models.Q(second=True)),
                name='not_both_first_and_second',
            ),
        ),
        
        # 添加索引
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['archive_record', 'first'], name='issueformitem_archive_first_idx'),
        ),
        migrations.AddIndex(
            model_name='issueformitem',
            index=models.Index(fields=['archive_record', 'second'], name='issueformitem_archive_second_idx'),
        ),
    ]

def migrate_existing_data(apps, schema_editor):
    """迁移现有数据，设置first/second标志"""
    IssueFormItem = apps.get_model('your_app', 'IssueFormItem')
    ArchiveRecord = apps.get_model('your_app', 'ArchiveRecord')
    
    for item in IssueFormItem.objects.all():
        archive = item.archive_record
        
        # 根据ArchiveRecord的发放记录判断发放次序
        if archive.first_issue_copies and not archive.second_issue_copies:
            # 只有第一次发放记录
            item.first = True
            item.second = False
        elif archive.first_issue_copies and archive.second_issue_copies:
            # 有第一次和第二次发放记录，需要判断当前项目是哪一次
            # 这里需要根据具体业务逻辑判断，可能需要比较发放时间、份数等
            
            # 简单策略：如果份数匹配第一次发放，则标记为第一次
            if item.copies == archive.first_issue_copies:
                item.first = True
                item.second = False
            elif item.copies == archive.second_issue_copies:
                item.first = False
                item.second = True
            else:
                # 如果份数都不匹配，默认标记为第一次（需要后续手动调整）
                item.first = True
                item.second = False
        else:
            # 没有发放记录或只有第二次发放记录（异常情况）
            item.first = True  # 默认标记为第一次
            item.second = False
        
        item.save()
```

### 3.2 数据验证脚本

```python
# management/commands/validate_issue_form_items.py
from django.core.management.base import BaseCommand
from django.db import transaction
from your_app.models import IssueFormItem, ArchiveRecord

class Command(BaseCommand):
    help = '验证IssueFormItem的first/second字段设置是否正确'
    
    def handle(self, *args, **options):
        inconsistencies = []
        
        for item in IssueFormItem.objects.all():
            archive = item.archive_record
            issues = []
            
            if item.first:
                # 验证第一次发放
                if not archive.first_issue_copies:
                    issues.append("标记为第一次发放但ArchiveRecord无第一次发放数据")
                elif archive.first_issue_copies != item.copies:
                    issues.append(f"发放份数不匹配：IssueFormItem={item.copies}, ArchiveRecord={archive.first_issue_copies}")
            
            elif item.second:
                # 验证第二次发放
                if not archive.second_issue_copies:
                    issues.append("标记为第二次发放但ArchiveRecord无第二次发放数据")
                elif archive.second_issue_copies != item.copies:
                    issues.append(f"发放份数不匹配：IssueFormItem={item.copies}, ArchiveRecord={archive.second_issue_copies}")
                
                # 验证是否有第一次发放
                if not archive.first_issue_copies:
                    issues.append("第二次发放但无第一次发放记录")
            
            if issues:
                inconsistencies.append({
                    'item_id': item.id,
                    'archive_number': archive.unified_number,
                    'issue_form': item.issue_form.form_number,
                    'issues': issues
                })
        
        if inconsistencies:
            self.stdout.write(self.style.WARNING(f"发现 {len(inconsistencies)} 个不一致的记录："))
            for item in inconsistencies:
                self.stdout.write(f"  IssueFormItem #{item['item_id']} ({item['archive_number']}):")
                for issue in item['issues']:
                    self.stdout.write(f"    - {issue}")
        else:
            self.stdout.write(self.style.SUCCESS("所有IssueFormItem记录都是一致的"))
```

---

## 4. 业务逻辑调整

### 4.1 发放单创建逻辑

```python
# services/issue_form_service.py
class IssueFormService:
    
    def create_issue_form(self, form_data, selected_archives):
        """创建发放单 - 增强版"""
        with transaction.atomic():
            # 创建发放单
            issue_form = IssueForm.objects.create(**form_data)
            
            # 创建发放单项目
            for archive_info in selected_archives:
                archive_record = ArchiveRecord.objects.get(id=archive_info['archive_record_id'])
                issue_mode = archive_info['issue_mode']
                copies = archive_info['copies']
                
                # 确定发放类型
                is_first, is_second = self._determine_issue_type(archive_record, issue_mode, copies)
                
                # 创建发放单项目
                IssueFormItem.objects.create(
                    issue_form=issue_form,
                    archive_record=archive_record,
                    copies=copies,
                    first=is_first,
                    second=is_second
                )
            
            return issue_form
    
    def _determine_issue_type(self, archive_record, issue_mode, copies):
        """确定发放类型（第一次或第二次）"""
        has_first_issue = bool(archive_record.first_issue_copies)
        has_second_issue = bool(archive_record.second_issue_copies)
        
        if issue_mode == 'single':
            # 发放1份，通常是第一次发放
            if has_first_issue:
                # 如果已有第一次发放，检查是否为数据修复场景
                has_valid_first_record = IssueFormItem.objects.filter(
                    archive_record=archive_record,
                    first=True,
                    issue_form__is_active=True,
                    issue_form__is_deleted=False
                ).exists()
                
                if not has_valid_first_record:
                    # 数据修复：重新创建第一次发放记录
                    return True, False
                else:
                    raise ValueError("该档案已有第一次发放记录")
            else:
                return True, False  # 第一次发放
        
        elif issue_mode == 'remaining':
            # 发放剩余份数
            if not has_first_issue:
                # 第一次发放全部
                return True, False
            else:
                # 第二次发放剩余
                if has_second_issue:
                    raise ValueError("该档案已有第二次发放记录")
                return False, True
        
        raise ValueError(f"无效的发放模式: {issue_mode}")
    
    def delete_issue_form(self, issue_form_id):
        """删除发放单并清理相关数据"""
        with transaction.atomic():
            issue_form = IssueForm.objects.get(id=issue_form_id)
            
            # 清理ArchiveRecord的发放信息
            for item in issue_form.issueformitem_set.all():
                if item.first:
                    self._clear_archive_first_issue(item.archive_record)
                elif item.second:
                    self._clear_archive_second_issue(item.archive_record)
            
            # 删除发放单（级联删除项目）
            issue_form.delete()
```

### 4.2 数据同步服务

```python
# services/archive_sync_service.py
class ArchiveSyncService:
    
    def sync_archive_from_issue_items(self, archive_record):
        """从IssueFormItem同步ArchiveRecord的发放信息"""
        
        # 获取有效的发放记录
        first_item = IssueFormItem.objects.filter(
            archive_record=archive_record,
            first=True,
            issue_form__is_active=True,
            issue_form__is_deleted=False
        ).first()
        
        second_item = IssueFormItem.objects.filter(
            archive_record=archive_record,
            second=True,
            issue_form__is_active=True,
            issue_form__is_deleted=False
        ).first()
        
        # 同步第一次发放信息
        if first_item:
            issue_form = first_item.issue_form
            archive_record.first_issue_copies = first_item.copies
            archive_record.first_issue_datetime = issue_form.issue_date
            archive_record.first_issue_person = issue_form.issue_person
            archive_record.first_receiver_name = issue_form.receiver_name
            archive_record.first_receiver_unit = issue_form.receiver_unit
            archive_record.first_receiver_phone = issue_form.receiver_phone
        else:
            # 清空第一次发放信息
            archive_record.first_issue_copies = None
            archive_record.first_issue_datetime = None
            archive_record.first_issue_person = None
            archive_record.first_receiver_name = None
            archive_record.first_receiver_unit = None
            archive_record.first_receiver_phone = None
        
        # 同步第二次发放信息
        if second_item:
            issue_form = second_item.issue_form
            archive_record.second_issue_copies = second_item.copies
            archive_record.second_issue_datetime = issue_form.issue_date
            archive_record.second_issue_person = issue_form.issue_person
            archive_record.second_receiver_name = issue_form.receiver_name
            archive_record.second_receiver_unit = issue_form.receiver_unit
            archive_record.second_receiver_phone = issue_form.receiver_phone
        else:
            # 清空第二次发放信息
            archive_record.second_issue_copies = None
            archive_record.second_issue_datetime = None
            archive_record.second_issue_person = None
            archive_record.second_receiver_name = None
            archive_record.second_receiver_unit = None
            archive_record.second_receiver_phone = None
        
        archive_record.save()
        
        return {
            'has_first': first_item is not None,
            'has_second': second_item is not None,
            'synced_fields': [
                'first_issue_copies', 'first_issue_datetime', 'first_issue_person',
                'first_receiver_name', 'first_receiver_unit', 'first_receiver_phone',
                'second_issue_copies', 'second_issue_datetime', 'second_issue_person',
                'second_receiver_name', 'second_receiver_unit', 'second_receiver_phone'
            ]
        }
    
    def check_consistency(self, archive_record):
        """检查ArchiveRecord与IssueFormItem的数据一致性"""
        inconsistencies = []
        
        # 检查第一次发放一致性
        first_item = IssueFormItem.objects.filter(
            archive_record=archive_record,
            first=True,
            issue_form__is_active=True,
            issue_form__is_deleted=False
        ).first()
        
        if first_item and archive_record.first_issue_copies != first_item.copies:
            inconsistencies.append({
                'type': 'first_issue_copies_mismatch',
                'archive_value': archive_record.first_issue_copies,
                'item_value': first_item.copies,
                'item_id': first_item.id
            })
        
        if not first_item and archive_record.first_issue_copies:
            inconsistencies.append({
                'type': 'orphaned_first_issue_data',
                'archive_value': archive_record.first_issue_copies,
                'item_value': None
            })
        
        # 检查第二次发放一致性
        second_item = IssueFormItem.objects.filter(
            archive_record=archive_record,
            second=True,
            issue_form__is_active=True,
            issue_form__is_deleted=False
        ).first()
        
        if second_item and archive_record.second_issue_copies != second_item.copies:
            inconsistencies.append({
                'type': 'second_issue_copies_mismatch',
                'archive_value': archive_record.second_issue_copies,
                'item_value': second_item.copies,
                'item_id': second_item.id
            })
        
        if not second_item and archive_record.second_issue_copies:
            inconsistencies.append({
                'type': 'orphaned_second_issue_data',
                'archive_value': archive_record.second_issue_copies,
                'item_value': None
            })
        
        # 检查发放顺序一致性
        if second_item and not first_item and archive_record.second_issue_copies:
            inconsistencies.append({
                'type': 'missing_first_issue',
                'message': '存在第二次发放但缺少第一次发放记录'
            })
        
        return {
            'is_consistent': len(inconsistencies) == 0,
            'inconsistencies': inconsistencies
        }
```

---

## 5. API接口调整

### 5.1 发放单列表接口调整

```python
# serializers/issue_form_serializers.py
class IssueFormItemSerializer(serializers.ModelSerializer):
    """发放单项目序列化器 - 增强版"""
    
    issue_type = serializers.CharField(read_only=True)
    archive_unified_number = serializers.CharField(source='archive_record.unified_number', read_only=True)
    archive_project_name = serializers.CharField(source='archive_record.project_name', read_only=True)
    
    class Meta:
        model = IssueFormItem
        fields = [
            'id', 'copies', 'first', 'second', 'issue_type',
            'archive_unified_number', 'archive_project_name'
        ]

class IssueFormSerializer(serializers.ModelSerializer):
    """发放单序列化器 - 增强版"""
    
    items = IssueFormItemSerializer(source='issueformitem_set', many=True, read_only=True)
    issue_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = IssueForm
        fields = [
            'id', 'form_number', 'issue_date', 'issue_person',
            'receiver_name', 'receiver_unit', 'receiver_phone',
            'items', 'issue_summary'
        ]
    
    def get_issue_summary(self, obj):
        """获取发放汇总信息"""
        return obj.get_issue_summary()
```

### 5.2 发放操作接口调整

```python
# views/issue_form_views.py
class CreateIssueFormView(generics.CreateAPIView):
    """创建发放单接口 - 增强版"""
    
    def post(self, request, *args, **kwargs):
        try:
            form_data = request.data.get('form_data', {})
            selected_archives = request.data.get('selected_archives', [])
            
            # 验证数据
            self._validate_create_request(selected_archives)
            
            # 创建发放单
            service = IssueFormService()
            issue_form = service.create_issue_form(form_data, selected_archives)
            
            # 收集警告信息
            warnings = self._collect_warnings(selected_archives)
            
            return Response({
                'success': True,
                'data': {
                    'issue_form_id': issue_form.id,
                    'form_number': issue_form.form_number,
                    'created_items': len(selected_archives),
                    'warnings': warnings
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def _validate_create_request(self, selected_archives):
        """验证创建请求"""
        for archive_info in selected_archives:
            archive_id = archive_info.get('archive_record_id')
            issue_mode = archive_info.get('issue_mode')
            copies = archive_info.get('copies')
            
            if not all([archive_id, issue_mode, copies]):
                raise ValueError("缺少必要的档案信息")
            
            archive = ArchiveRecord.objects.get(id=archive_id)
            
            # 使用现有的验证逻辑
            service = ReportDetailService()
            validation_result = service.validate_issue_operation(archive, issue_mode, copies)
            
            if not validation_result.get('valid'):
                raise ValueError(f"档案 {archive.unified_number} 验证失败")
    
    def _collect_warnings(self, selected_archives):
        """收集警告信息"""
        warnings = []
        
        for archive_info in selected_archives:
            archive_id = archive_info['archive_record_id']
            archive = ArchiveRecord.objects.get(id=archive_id)
            
            # 检查是否为数据修复操作
            service = ReportDetailService()
            consistency_check = service.check_record_consistency(archive)
            
            if not consistency_check['is_consistent']:
                warnings.append(f"档案 {archive.unified_number}：检测到数据异常，已进行修复")
        
        return warnings
```

---

## 6. 数据一致性处理

### 6.1 一致性检查命令

```python
# management/commands/check_issue_consistency.py
from django.core.management.base import BaseCommand
from django.db import transaction
from your_app.models import ArchiveRecord, IssueFormItem
from your_app.services import ArchiveSyncService

class Command(BaseCommand):
    help = '检查并修复ArchiveRecord与IssueFormItem的数据一致性'
    
    def add_arguments(self, parser):
        parser.add_argument('--fix', action='store_true', help='自动修复数据不一致问题')
        parser.add_argument('--archive-id', type=int, help='检查特定档案记录')
    
    def handle(self, *args, **options):
        fix_mode = options['fix']
        archive_id = options.get('archive_id')
        
        if archive_id:
            archives = ArchiveRecord.objects.filter(id=archive_id)
        else:
            archives = ArchiveRecord.objects.all()
        
        sync_service = ArchiveSyncService()
        total_checked = 0
        total_inconsistent = 0
        total_fixed = 0
        
        for archive in archives:
            total_checked += 1
            consistency_result = sync_service.check_consistency(archive)
            
            if not consistency_result['is_consistent']:
                total_inconsistent += 1
                self.stdout.write(
                    self.style.WARNING(f"档案 {archive.unified_number} 数据不一致:")
                )
                
                for issue in consistency_result['inconsistencies']:
                    self.stdout.write(f"  - {issue['type']}: {issue}")
                
                if fix_mode:
                    try:
                        with transaction.atomic():
                            sync_result = sync_service.sync_archive_from_issue_items(archive)
                            total_fixed += 1
                            self.stdout.write(
                                self.style.SUCCESS(f"  已修复档案 {archive.unified_number}")
                            )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"  修复失败: {e}")
                        )
        
        # 输出统计结果
        self.stdout.write(f"\n检查完成:")
        self.stdout.write(f"  总共检查: {total_checked} 个档案")
        self.stdout.write(f"  发现不一致: {total_inconsistent} 个")
        if fix_mode:
            self.stdout.write(f"  成功修复: {total_fixed} 个")
        else:
            self.stdout.write("  使用 --fix 参数可自动修复数据不一致问题")
```

### 6.2 定期同步任务

```python
# tasks/sync_tasks.py
from celery import shared_task
from django.db import transaction
from your_app.models import ArchiveRecord
from your_app.services import ArchiveSyncService

@shared_task
def sync_archive_issue_data():
    """定期同步ArchiveRecord与IssueFormItem的数据"""
    
    sync_service = ArchiveSyncService()
    processed = 0
    errors = 0
    
    # 只同步有发放记录的档案
    archives_with_issues = ArchiveRecord.objects.filter(
        models.Q(first_issue_copies__isnull=False) |
        models.Q(second_issue_copies__isnull=False)
    )
    
    for archive in archives_with_issues:
        try:
            with transaction.atomic():
                consistency_result = sync_service.check_consistency(archive)
                
                if not consistency_result['is_consistent']:
                    sync_service.sync_archive_from_issue_items(archive)
                    processed += 1
        
        except Exception as e:
            errors += 1
            # 记录错误日志
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"同步档案 {archive.unified_number} 失败: {e}")
    
    return {
        'processed': processed,
        'errors': errors,
        'message': f"同步完成，处理 {processed} 个档案，{errors} 个错误"
    }
```

### 6.3 档案历史记录(ArchiveRecord History)处理

#### 历史记录机制

- **变更记录**: `ArchiveRecord` 的所有变更都通过 `RecordChangeLog` 和 `ChangeLogBatch` 模型进行记录。
- **变更来源**: `ChangeLogBatch` 的 `change_source` 字段用于记录变更的业务来源，例如：
  - `'issueform_archive'`: 变更来源于发放单归档。
  - `'issueform_delete'`: 变更来源于发放单删除。

#### 前端查询需求

- **查询接口**: 前端查询 `ArchiveRecord` 的历史记录时，API应返回一个列表，每一项代表一次变更。
- **数据内容**: 每一条历史记录都必须包含：
  1. **变更前后的完整状态**: 提供完整的 `record_before` 和 `record_after` 的JSON对象，以便前端直接对比展示。
  2. **变更来源 (`change_source`)**: 明确告知前端这次变更是由哪个业务动作触发的。

**示例API响应**:

```json
[
  {
    "version": 2,
    "changed_at": "2023-10-27T10:00:00Z",
    "change_source": "issueform_archive",
    "change_source_display": "发放单归档，报告正式发放",
    "operator": "admin",
    "record_before": { "status": "pending", "first_issue_copies": null, ... },
    "record_after": { "status": "issued", "first_issue_copies": 10, ... }
  },
  {
    "version": 1,
    "changed_at": "2023-10-26T08:00:00Z",
    "change_source": "manual_edit",
    "change_source_display": "手动编辑",
    "operator": "user1",
    "record_before": { "project_name": "项目A", ... },
    "record_after": { "project_name": "项目A-1", ... }
  }
]
```

---

## 7. 实施计划

> **实施环境**: 新系统，无历史数据，可直接进行模型变更

### 📋 实施检查清单

#### 🔧 第一阶段：模型和数据库变更

- [ ] **IssueFormItem模型更新** (`report_issuing/models.py`)
  - [ ] 添加 `first` 和 `second` 布尔字段
  - [ ] 添加模型验证方法 `clean()` 和 `_validate_issue_sequence()`
  - [ ] 添加数据同步方法 `save()` 和 `_sync_archive_record()`
  - [ ] 添加属性方法 `issue_type_display` 和 `issue_sequence`
  - [ ] 更新 `Meta` 类的约束和索引

- [ ] **IssueForm模型增强** (`report_issuing/models.py`)
  - [ ] 添加 `get_first_issue_items()` 方法
  - [ ] 添加 `get_second_issue_items()` 方法
  - [ ] 添加 `get_issue_summary()` 方法
  - [ ] 更新 `delete()` 方法处理清理逻辑
  - [ ] 添加 `_clear_archive_first_issue()` 和 `_clear_archive_second_issue()` 方法

- [ ] **创建数据库迁移文件**
  - [ ] 生成迁移文件: `python manage.py makemigrations report_issuing`
  - [ ] 执行迁移: `python manage.py migrate`
  - [ ] 验证数据库结构正确

#### 🔄 第二阶段：数据同步服务

- [ ] **创建ArchiveSyncService** (`report_issuing/services/archive_sync_service.py`)
  - [ ] 实现 `sync_archive_from_issue_items()` 方法
  - [ ] 实现 `check_consistency()` 方法
  - [ ] 实现 `fix_consistency_issues()` 方法
  - [ ] 实现 `get_sync_statistics()` 方法

- [ ] **数据一致性检查工具** (可选)
  - [ ] 创建管理命令 `check_issue_consistency.py`
  - [ ] 测试命令: `python manage.py check_issue_consistency --stats`

#### 🔧 第三阶段：业务逻辑调整

- [ ] **IssueFormService更新** (`report_issuing/services/issue_form_service.py`)
  - [ ] 更新 `create_issue_form()` 方法支持first/second逻辑
  - [ ] 更新 `_determine_issue_type()` 方法
  - [ ] 更新 `add_form_items()` 方法
  - [ ] 测试发放单创建流程

- [ ] **发放验证逻辑**
  - [ ] 验证第一次发放不能重复
  - [ ] 验证第二次发放必须有第一次发放
  - [ ] 测试各种异常场景

#### 📡 第四阶段：API接口调整

- [ ] **序列化器更新** (`report_issuing/serializers.py`)
  - [ ] 更新 `IssueFormItemSerializer` 添加 `first`、`second`、`issue_type_display` 字段
  - [ ] 更新 `IssueFormSerializer` 添加 `issue_summary` 字段
  - [ ] 测试序列化器数据输出

- [ ] **视图逻辑调整** (`report_issuing/views.py`)
  - [ ] 更新创建发放单的视图逻辑
  - [ ] 添加发放类型验证
  - [ ] 更新错误处理
  - [ ] 测试API接口响应

#### 🧪 第五阶段：测试验证

- [ ] **单元测试**
  - [ ] 测试IssueFormItem模型约束和验证
  - [ ] 测试IssueForm模型方法
  - [ ] 测试ArchiveSyncService各方法
  - [ ] 测试序列化器和视图

- [ ] **集成测试**
  - [ ] 测试完整的发放单创建流程
  - [ ] 测试第一次和第二次发放场景
  - [ ] 测试数据同步机制
  - [ ] 测试异常情况处理

- [ ] **功能验证**
  - [ ] 前端发放单创建功能
  - [ ] 档案记录发放信息同步
  - [ ] 发放历史记录完整性
  - [ ] 数据一致性检查

#### 📦 第六阶段：部署和监控

- [ ] **代码审查**
  - [ ] 代码质量检查
  - [ ] 性能优化检查
  - [ ] 安全性检查

- [ ] **生产部署**
  - [ ] 数据库迁移执行
  - [ ] 代码部署
  - [ ] 功能验证
  - [ ] 监控设置

#### 🔍 可选：运维工具

- [ ] **数据维护命令** (推荐保留)
  - [ ] `validate_issue_form_items.py` - 验证发放项目数据
  - [ ] `check_issue_consistency.py` - 一致性检查和修复
  - [ ] 设置定期检查任务

### ⚠️ 重点关注项

1. **模型约束验证**: 确保数据库约束正确工作
2. **同步机制测试**: 重点测试ArchiveRecord与IssueFormItem的同步
3. **API兼容性**: 确保前端接口不受影响
4. **性能监控**: 关注新增索引和查询的性能表现

### 🎯 里程碑检查点

- **M1**: 模型变更完成，数据库迁移成功
- **M2**: 同步服务实现完成，基本功能测试通过
- **M3**: 业务逻辑调整完成，发放流程正常
- **M4**: API接口调整完成，前后端对接成功
- **M5**: 全面测试完成，准备生产部署
- **M6**: 生产部署完成，系统稳定运行

---

## 📝 总结

### ✅ 主要改进

1. **明确发放次序**：通过 `first` 和 `second` 字段明确区分发放次数
2. **数据一致性**：建立ArchiveRecord与IssueFormItem的自动同步机制
3. **约束保证**：数据库层面的约束确保数据完整性
4. **新系统优势**：无历史数据包袱，可直接实施最优设计

### 🎯 技术优势

- **数据追溯性**：每个发放记录都有明确的次序标识
- **一致性保证**：自动同步机制确保数据一致性
- **实时同步**：模型保存时自动更新相关字段
- **性能优化**：合理的索引设计提升查询效率

### 🔧 实施优势

- **直接变更**：新系统无需复杂的数据迁移策略
- **完整测试**：单元测试和集成测试覆盖
- **运维工具**：可选的一致性检查和监控工具
- **标准化流程**：清晰的实施检查清单

### 📋 实施状态跟踪

当前已完成的工作：

- [x] **模型设计完成**：IssueFormItem和IssueForm模型代码
- [x] **同步服务完成**：ArchiveSyncService实现
- [x] **运维工具完成**：数据一致性检查命令（可选）

待完成工作：

- [ ] **数据库迁移**：生成并执行迁移文件
- [ ] **业务逻辑调整**：更新Service层代码
- [ ] **API接口调整**：更新序列化器和视图
- [ ] **测试验证**：单元测试和集成测试
- [ ] **生产部署**：最终部署和监控

这个设计确保了IssueFormItem模型的增强既保持了业务逻辑的正确性，又充分利用了新系统的优势，提供了强大的数据一致性保障。
