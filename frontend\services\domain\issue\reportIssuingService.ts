import apiClient, { type ApiResponse } from '@/lib/apiClient'; // 使用新的Axios客户端

export interface IssueFormData {
  id: number; // 数据库ID，现在保持为number类型以匹配后端
  issueNumber: string; // 业务标识符 - 发放单编号 (后端 issue_number -> camelCase)
  createdAt: string; // 创建日期 (后端 created_at -> camelCase)
  issueDate: string; // 发放日期 (后端 issue_date -> camelCase)
  receiverUnit: string; // 领取单位 (后端 receiver_unit -> camelCase)
  receiverName?: string; // 领取人姓名 (后端 receiver_name -> camelCase)
  receiverPhone?: string; // 领取人电话 (后端 receiver_phone -> camelCase)
  status: 'draft' | 'locked' | 'issued' | 'deleted'; // 状态
  statusDisplay?: string; // 状态显示名称 (后端 status_display -> camelCase)
  issuer?: number; // 发放人ID
  issuerUsername?: string; // 发放人用户名 (后端 issuer_username -> camelCase)
  confirmationFile?: string; // 确认单文件 (后端 confirmation_file -> camelCase)
  notes?: string; // 备注
  items?: IssueFormItem[]; // 发放条目
  updatedAt?: string; // 更新时间 (后端 updated_at -> camelCase)
  isDeleted?: boolean; // 是否删除 (后端 is_deleted -> camelCase)
  deletedBy?: number; // 删除人 (后端 deleted_by -> camelCase)
  deletedAt?: string; // 删除时间 (后端 deleted_at -> camelCase)
  deletionReason?: string; // 删除原因 (后端 deletion_reason -> camelCase)
}

export interface IssueFormItem {
  id?: number;
  archiveRecordId: number; // 档案记录ID (后端 archive_record_id -> camelCase)
  unifiedNumber: string; // 统一编号 (后端 unified_number -> camelCase)
  sampleName: string; // 样品名称 (后端 sample_name -> camelCase)
  copies: number; // 份数
  first: boolean; // 正本
  second: boolean; // 副本
  receiverName?: string; // 领取人姓名 (后端 receiver_name -> camelCase)
  receiverUnit?: string; // 领取单位 (后端 receiver_unit -> camelCase)
  receiverPhone?: string; // 领取人电话 (后端 receiver_phone -> camelCase)
  notes?: string; // 备注
}

export interface IssuableArchive {
  id: number
  unifiedNumber: string    // 后端 unified_number -> camelCase
  sampleNumber: string     // 后端 sample_number -> camelCase
  projectName: string      // 后端 project_name -> camelCase
  projectNumber: string    // 后端 project_number -> camelCase
  projectLocation: string  // 后端 project_location -> camelCase
  clientName: string       // 后端 client_name -> camelCase
  clientUnit: string       // 后端 client_unit -> camelCase
  commissionDatetime: string // 后端 commission_datetime -> camelCase
  totalIssueCopies: number   // 后端 total_issue_copies -> camelCase
  firstIssueCopies: number   // 后端 first_issue_copies -> camelCase
  firstIssueDatetime?: string // 后端 first_issue_datetime -> camelCase
  firstIssuePerson?: string   // 后端 first_issue_person -> camelCase
  firstReceiverName?: string  // 后端 first_receiver_name -> camelCase
  firstReceiverPhone?: string // 后端 first_receiver_phone -> camelCase
 
  secondIssueDatetime?: string // 后端 second_issue_datetime -> camelCase
  secondIssuePerson?: string   // 后端 second_issue_person -> camelCase
  secondReceiverName?: string  // 后端 second_receiver_name -> camelCase
  secondReceiverPhone?: string // 后端 second_receiver_phone -> camelCase
  secondIssueCopies: number    // 后端 second_issue_copies -> camelCase
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ListParams {
  ordering?: string;
  search?: string;
  created_at_after?: string;
  created_at_before?: string;
  status_filter?: 'active' | 'issued' | 'all';
}

export interface IssuableArchivesParams {
  offset?: number
  limit?: number
  search?: string
  unifiedNumber?: string
  sampleNumber?: string
  clientUnit?: string
  clientName?: string
  commissionDatetimeStart?: string
  commissionDatetimeEnd?: string
  projectNumber?: string
  projectName?: string
  projectLocation?: string
}

// Helper to build query string
const buildQueryString = (params: Record<string, string | undefined>): string => {
  const usp = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined) {
      usp.append(key, params[key]!);
    }
  }
  const queryString = usp.toString();
  return queryString ? `?${queryString}` : '';
};

export const getIssueForms = async (): Promise<PaginatedResponse<IssueFormData>> => {
  const endpoint = '/api/report-issuing/issue-forms/';
  const response = await apiClient.get<PaginatedResponse<IssueFormData>>(endpoint);
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to fetch issue forms');
  }
  return response.data;
};

export const getIssueForm = async (issueNumber: string): Promise<IssueFormData> => {
  const endpoint = `/api/report-issuing/issue-forms/${issueNumber}/`;
  const response = await apiClient.get<IssueFormData>(endpoint);
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to fetch issue form');
  }
  return response.data;
};

export const getIssuableArchives = async (params: IssuableArchivesParams): Promise<PaginatedResponse<IssuableArchive>> => {
  const queryString = new URLSearchParams(
    Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== '')
      .map(([key, value]) => [key, String(value)])
  ).toString()

  const endpoint = `/api/report-issuing/issue-forms/issuable-archives/?${queryString}`
  const response = await apiClient.get<PaginatedResponse<IssuableArchive>>(endpoint)
  
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to fetch issuable archives')
  }
  
  return response.data
}

export const createIssueForm = async (data: Partial<IssueFormData>): Promise<IssueFormData> => {
  const endpoint = '/api/report-issuing/issue-forms/';
  const response = await apiClient.post<IssueFormData>(endpoint, data);
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to create issue form');
  }
  return response.data;
};

export const updateIssueForm = async (issueNumber: string, data: Partial<IssueFormData>): Promise<IssueFormData> => {
  const endpoint = `/api/report-issuing/issue-forms/${issueNumber}/`;
  const response = await apiClient.patch<IssueFormData>(endpoint, data);
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to update issue form');
  }
  return response.data;
};

export const deleteIssueForm = async (issueNumber: string): Promise<void> => {
  const endpoint = `/api/report-issuing/issue-forms/${issueNumber}/`;
  const response = await apiClient.delete(endpoint);
  if (!response.success) {
    throw new Error(response.error || 'Failed to delete issue form');
  }
};

export const updateIssueFormNotes = async (issueNumber: string, notes: string): Promise<void> => {
  const endpoint = `/api/report-issuing/issue-forms/${issueNumber}/`;
  const response = await apiClient.patch(endpoint, { notes });
  if (!response.success) {
    throw new Error(response.error || 'Failed to update notes');
  }
};

export const uploadIssueFormReceipt = async (issueNumber: string, file: File): Promise<void> => {
  const formData = new FormData();
  formData.append('confirmation_file', file);
  
  const endpoint = `/api/report-issuing/issue-forms/${issueNumber}/upload-receipt/`;
  const response = await apiClient.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to upload receipt');
  }
};

export const getReceiptFileUrl = (issueNumber: string): string => {
  return `/api/report-issuing/issue-forms/${issueNumber}/receipt/`;
};

export const updateArchiveTotalCopies = async (
  archiveIds: number | number[], 
  totalCopies: number
): Promise<{ updatedCount: number; totalRequested: number; errors: any[] }> => {
  const endpoint = `/api/report-issuing/issue-forms/update-total-copies/`;
  
  // 统一转换为数组格式
  const idsArray = Array.isArray(archiveIds) ? archiveIds : [archiveIds];
  
  const response = await apiClient.patch<{ updated_count: number; total_requested: number; errors?: any[] }>(endpoint, { 
    archive_ids: idsArray,
    total_issue_copies: totalCopies 
  });
  
  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to update archive total copies');
  }
  
  return {
    updatedCount: response.data.updated_count,
    totalRequested: response.data.total_requested,
    errors: response.data.errors || []
  };
}; 