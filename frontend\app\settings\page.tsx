"use client"

import type React from "react"

import { useState } from "react"
import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Save } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export default function SettingsPage() {
  const { toast } = useToast()
  const [isSaving, setIsSaving] = useState(false)

  const [generalSettings, setGeneralSettings] = useState({
    systemName: "档案流管理系统",
    companyName: "示例公司",
    logo: "/logo.png",
    defaultPageSize: "10",
    dateFormat: "YYYY-MM-DD",
    timeFormat: "HH:mm:ss",
  })

  const [archiveSettings, setArchiveSettings] = useState({
    boxNumberPrefix: "BOX-",
    autoGenerateBoxNumber: true,
    requireApprovalForArchive: true,
    maxPdfFileSize: "20",
    allowedFileTypes: "pdf",
  })

  const [notificationSettings, setNotificationSettings] = useState({
    enableEmailNotifications: true,
    enableSystemNotifications: true,
    notifyOnNewChangeOrder: true,
    notifyOnArchiveComplete: true,
    notifyOnReportIssue: true,
    dailySummary: false,
    emailFooter: "此邮件由系统自动发送，请勿回复。",
  })

  const handleGeneralChange = (field: string, value: string | boolean) => {
    setGeneralSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleArchiveChange = (field: string, value: string | boolean) => {
    setArchiveSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleNotificationChange = (field: string, value: string | boolean) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      // 实际应用中，这里会调用API保存设置
      // const response = await fetch('/api/settings', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     general: generalSettings,
      //     archive: archiveSettings,
      //     notification: notificationSettings,
      //   }),
      // });
      // if (!response.ok) throw new Error('保存失败');

      // 模拟保存延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "设置已保存",
        description: "系统设置已成功更新",
      })
    } catch (error) {
      toast({
        title: "保存失败",
        description: "保存设置时发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <PageTitle title="系统设置" subtitle="配置系统参数和选项" />

      <form onSubmit={handleSaveSettings}>
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList>
            <TabsTrigger value="general">基本设置</TabsTrigger>
            <TabsTrigger value="archive">归档设置</TabsTrigger>
            <TabsTrigger value="notification">通知设置</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>基本设置</CardTitle>
                <CardDescription>配置系统的基本参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="system-name">系统名称</Label>
                    <Input
                      id="system-name"
                      value={generalSettings.systemName}
                      onChange={(e) => handleGeneralChange("systemName", e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="company-name">公司名称</Label>
                    <Input
                      id="company-name"
                      value={generalSettings.companyName}
                      onChange={(e) => handleGeneralChange("companyName", e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="logo">系统Logo</Label>
                    <div className="flex items-center gap-4">
                      <img
                        src={generalSettings.logo || "/placeholder.svg"}
                        alt="Logo"
                        className="h-10 w-auto border rounded p-1"
                      />
                      <Button type="button" variant="outline" size="sm">
                        更换Logo
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="default-page-size">默认分页大小</Label>
                      <Select
                        value={generalSettings.defaultPageSize}
                        onValueChange={(value) => handleGeneralChange("defaultPageSize", value)}
                      >
                        <SelectTrigger id="default-page-size">
                          <SelectValue placeholder="选择分页大小" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="10">10条/页</SelectItem>
                          <SelectItem value="20">20条/页</SelectItem>
                          <SelectItem value="50">50条/页</SelectItem>
                          <SelectItem value="100">100条/页</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="date-format">日期格式</Label>
                      <Select
                        value={generalSettings.dateFormat}
                        onValueChange={(value) => handleGeneralChange("dateFormat", value)}
                      >
                        <SelectTrigger id="date-format">
                          <SelectValue placeholder="选择日期格式" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="YYYY年MM月DD日">YYYY年MM月DD日</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="archive">
            <Card>
              <CardHeader>
                <CardTitle>归档设置</CardTitle>
                <CardDescription>配置档案归档相关参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="box-number-prefix">盒号前缀</Label>
                    <Input
                      id="box-number-prefix"
                      value={archiveSettings.boxNumberPrefix}
                      onChange={(e) => handleArchiveChange("boxNumberPrefix", e.target.value)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-generate-box-number">自动生成盒号</Label>
                    <Switch
                      id="auto-generate-box-number"
                      checked={archiveSettings.autoGenerateBoxNumber}
                      onCheckedChange={(checked) => handleArchiveChange("autoGenerateBoxNumber", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="require-approval">归档需要审批</Label>
                    <Switch
                      id="require-approval"
                      checked={archiveSettings.requireApprovalForArchive}
                      onCheckedChange={(checked) => handleArchiveChange("requireApprovalForArchive", checked)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="max-file-size">最大PDF文件大小 (MB)</Label>
                    <Input
                      id="max-file-size"
                      type="number"
                      value={archiveSettings.maxPdfFileSize}
                      onChange={(e) => handleArchiveChange("maxPdfFileSize", e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="allowed-file-types">允许的文件类型</Label>
                    <Input
                      id="allowed-file-types"
                      value={archiveSettings.allowedFileTypes}
                      onChange={(e) => handleArchiveChange("allowedFileTypes", e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">用逗号分隔多个文件类型，例如：pdf,jpg,png</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notification">
            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
                <CardDescription>配置系统通知和邮件提醒</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-email">启用邮件通知</Label>
                    <Switch
                      id="enable-email"
                      checked={notificationSettings.enableEmailNotifications}
                      onCheckedChange={(checked) => handleNotificationChange("enableEmailNotifications", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-system">启用系统通知</Label>
                    <Switch
                      id="enable-system"
                      checked={notificationSettings.enableSystemNotifications}
                      onCheckedChange={(checked) => handleNotificationChange("enableSystemNotifications", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="notify-change-order">更改单创建通知</Label>
                    <Switch
                      id="notify-change-order"
                      checked={notificationSettings.notifyOnNewChangeOrder}
                      onCheckedChange={(checked) => handleNotificationChange("notifyOnNewChangeOrder", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="notify-archive">归档完成通知</Label>
                    <Switch
                      id="notify-archive"
                      checked={notificationSettings.notifyOnArchiveComplete}
                      onCheckedChange={(checked) => handleNotificationChange("notifyOnArchiveComplete", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="notify-report">报告发放通知</Label>
                    <Switch
                      id="notify-report"
                      checked={notificationSettings.notifyOnReportIssue}
                      onCheckedChange={(checked) => handleNotificationChange("notifyOnReportIssue", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="daily-summary">每日摘要邮件</Label>
                    <Switch
                      id="daily-summary"
                      checked={notificationSettings.dailySummary}
                      onCheckedChange={(checked) => handleNotificationChange("dailySummary", checked)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email-footer">邮件页脚</Label>
                    <Textarea
                      id="email-footer"
                      value={notificationSettings.emailFooter}
                      onChange={(e) => handleNotificationChange("emailFooter", e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-6 flex justify-end">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
