---
description: 当用户提出要变更下一步计划或近期规划时（例如，“我们下一步不做A了，先做B”或“把任务X的优先级提前”），此规则指导 AI 更新检查点文档，并分析该变更对核心计划文档 (`detailed_work_plan_and_log.md`) 的潜在影响，在用户确认后再执行修改
globs: 
alwaysApply: false
---
# 规则：处理计划变更 (handle-plan-change)

## 描述：当用户明确提出要变更下一步或近期计划时，此规则指导 AI 更新检查点文档，并分析该变更对核心计划文档 (`detailed_work_plan_and_log.md`) 的潜在影响，在用户确认后再执行修改

当用户明确提出要变更下一步计划或近期规划时（例如，“我们下一步不做A了，先做B”或“把任务X的优先级提前”），AI 代理应遵循以下流程：

1. **确认变更指令**: 复述用户的变更请求，确保理解一致。例如：“好的，确认一下，您希望将下一步计划改为'执行B任务'，对吗？”

2. **提议更新检查点 (`ai_dev_checkpoint.md`)**:
    * **提议**修改 @AgentReadme/planning_and_requirements/ai_dev_checkpoint.md 中的"下一步计划"部分，以反映用户的最新指令 **(这将直接设定 AI 当前的工作焦点)**。

3. **询问对核心计划的影响**:
    * 明确询问用户："这个计划变更是否也影响 @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 中**区域二（当前任务）或区域三（遗留/暂缓任务）的任务列表、状态或优先级**？"

4. **如果用户确认影响核心计划**:
    * **必须先读取** @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md 的**区域二和区域三**的当前内容。
    * 基于读取的内容和用户的变更指令，**进行影响分析**。例如："了解。如果我们将任务B移到区域二并设为当前进行中，根据依赖关系，这可能会影响任务C的启动时间。或者，如果我们将任务X移到区域三并标记为暂缓，那么依赖它的任务Y可能需要被阻塞。" （AI 应尝试基于可获得信息进行分析，若无法分析则直接提议修改并提醒用户检查）。
    * **将影响分析结果（如果有）和具体的修改建议**（例如，"建议将任务B添加到区域二列表顶部，标记为`[>]`，并将任务C的依赖说明更新"）呈现给用户。

5. **获取用户确认**:
    * 无论是只修改检查点，还是同时修改检查点和核心计划文档（基于步骤4的分析和建议），都**必须**在执行任何修改前，将**所有计划的修改内容**清晰地呈现给用户，并**获得用户的明确批准**。
