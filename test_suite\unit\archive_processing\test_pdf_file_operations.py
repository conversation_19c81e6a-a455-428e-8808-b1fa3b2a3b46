import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.conf import settings

from archive_processing.utils.pdf_processor_usefull import (
    PDFProcessor, 
    generate_archive_storage_path,
    generate_archive_filename
)

class PDFFileOperationsTest(TestCase):
    """测试PDF文件操作和路径处理功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建一个临时目录作为测试工作目录
        self.temp_dir = tempfile.mkdtemp()
        self.temp_archive_dir = os.path.join(self.temp_dir, 'archives')
        os.makedirs(self.temp_archive_dir, exist_ok=True)
        
        # 直接设置Django设置，而不是使用patch
        # Django会在每个测试后自动恢复原始设置
        self._original_media_root = getattr(settings, 'MEDIA_ROOT', None)
        settings.MEDIA_ROOT = self.temp_dir
        
        # 动态添加测试所需的设置
        settings.ARCHIVE_STORAGE_LOCATION = self.temp_archive_dir
        settings.ARCHIVE_STORAGE_ROOT = self.temp_archive_dir
        
        # 创建一个测试PDF文件
        self.test_pdf_path = os.path.join(self.temp_dir, 'test.pdf')
        with open(self.test_pdf_path, 'wb') as f:
            # 创建一个最小但有效的PDF文件
            f.write(b'%PDF-1.4\n'
                   b'1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n'
                   b'2 0 obj<</Type/Pages/Count 1/Kids[3 0 R]>>endobj\n'
                   b'3 0 obj<</Type/Page/MediaBox[0 0 595 842]/Parent 2 0 R/Resources<<>>>>endobj\n'
                   b'xref\n0 4\n0000000000 65535 f\n0000000015 00000 n\n0000000061 00000 n\n0000000111 00000 n\n'
                   b'trailer<</Size 4/Root 1 0 R>>\n'
                   b'startxref\n183\n%%EOF\n')
    
    def tearDown(self):
        """清理测试环境"""
        # 恢复原始设置
        if self._original_media_root is not None:
            settings.MEDIA_ROOT = self._original_media_root
        
        # 删除测试设置
        if hasattr(settings, 'ARCHIVE_STORAGE_LOCATION'):
            delattr(settings, 'ARCHIVE_STORAGE_LOCATION')
        if hasattr(settings, 'ARCHIVE_STORAGE_ROOT'):
            delattr(settings, 'ARCHIVE_STORAGE_ROOT')
        
        # 清理临时目录
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"临时目录清理失败: {e}")
    
    def test_generate_archive_storage_path(self):
        """测试归档存储路径生成功能"""
        # 测试有效的统一编号
        valid_number = 'TEST-2023-12345'
        path = generate_archive_storage_path(valid_number)
        self.assertIsNotNone(path, "归档路径不应为None")
        self.assertTrue(os.path.exists(path), "归档路径应该被创建")
        
        # 测试None统一编号
        none_path = generate_archive_storage_path(None)
        self.assertIsNotNone(none_path, "即使统一编号为None，归档路径也不应为None")
        
        # 测试空字符串统一编号
        empty_path = generate_archive_storage_path('')
        self.assertIsNotNone(empty_path, "即使统一编号为空，归档路径也不应为None")
        
        # 测试含特殊字符的统一编号
        special_number = 'TEST*/?.pdf'
        special_path = generate_archive_storage_path(special_number)
        self.assertIsNotNone(special_path, "含特殊字符的统一编号也应生成有效路径")
    
    def test_generate_archive_filename(self):
        """测试归档文件名生成功能"""
        # 测试使用统一编号生成
        filename1 = generate_archive_filename(unified_number='TEST-2023-12345')
        self.assertEqual(filename1, 'TEST-2023-12345.pdf', "生成的文件名应基于统一编号")
        
        # 测试使用原始路径生成
        filename2 = generate_archive_filename(original_path='/path/to/original.pdf')
        self.assertTrue(filename2.endswith('.pdf'), "生成的文件名应保留扩展名")
        
        # 测试无参数情况
        filename3 = generate_archive_filename()
        self.assertTrue(filename3.endswith('.pdf'), "即使无参数，也应生成有效文件名")
    
    @patch('archive_processing.utils.pdf_processor_usefull.PDFProcessor._split_pdf')
    def test_pdf_split_path_handling(self, mock_split_pdf):
        """测试PDF分割的路径处理"""
        # 模拟_split_pdf方法的返回值
        mock_split_pdf.return_value = [
            (os.path.join(self.temp_dir, 'part1.pdf'), 'TEST-2023-12345'),
            (os.path.join(self.temp_dir, 'part2.pdf'), 'TEST-2023-67890'),
        ]
        
        # 创建处理器实例
        processor = PDFProcessor()
        
        # 调用process_pdf方法，这会触发内部的_split_pdf调用
        result = processor.process_pdf(self.test_pdf_path, target_text='测试')
        
        # 验证结果
        self.assertEqual(len(result), 2, "应返回两个分割结果")
        self.assertIsInstance(result[0][0], str, "文件路径应为字符串")
        self.assertIsInstance(result[0][1], str, "统一编号应为字符串")
    
    def test_path_construction(self):
        """直接测试路径构造逻辑"""
        # 模拟生成归档目录
        archive_dir = generate_archive_storage_path('TEST-2023-12345')
        
        # 获取settings中的ARCHIVE_STORAGE_LOCATION
        archive_storage_location = getattr(settings, 'ARCHIVE_STORAGE_LOCATION', None)
        self.assertIsNotNone(archive_storage_location, "ARCHIVE_STORAGE_LOCATION不应为None")
        
        # 尝试构造路径
        try:
            archive_dir_path = Path(archive_storage_location) / archive_dir
            self.assertTrue(isinstance(archive_dir_path, Path), "构造的路径应为Path对象")
        except Exception as e:
            self.fail(f"路径构造失败: {e}")
        
        # 测试archive_dir为None的情况
        with patch('archive_processing.utils.pdf_processor_usefull.generate_archive_storage_path', return_value=None):
            unified_number = 'TEST-2023-12345'
            
            # 从PDFProcessor中提取相关代码并直接测试
            try:
                # 检查None情况的处理
                archive_dir = None
                if archive_dir is None:
                    archive_dir = unified_number
                
                # 尝试构造路径
                archive_dir_path = Path(archive_storage_location) / archive_dir
                self.assertTrue(isinstance(archive_dir_path, Path), "即使archive_dir为None，构造的路径也应为Path对象")
            except Exception as e:
                self.fail(f"处理archive_dir为None的情况失败: {e}") 