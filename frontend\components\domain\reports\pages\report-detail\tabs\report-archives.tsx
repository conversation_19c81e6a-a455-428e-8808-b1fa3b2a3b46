"use client"

import React, { useState, useMemo } from "react"
import { Search, FileText, Download, Printer, Eye } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// CHANGE: [2024-07-31] AG Grid imports (Restore standard import and add module)
import { AgGridReact } from 'ag-grid-react'; // Restore standard import
// 完全从企业版导入所有需要的接口和模块
import { 
  AllEnterpriseModule,
  ColDef, 
  ICellRendererParams
} from 'ag-grid-enterprise'; 
// CHANGE: [YYYY-MM-DD] Remove old CSS theme files for v33 theming
// import 'ag-grid-community/styles/ag-grid.css'; // Core grid CSS
// import 'ag-grid-community/styles/ag-theme-alpine.css'; // Theme

// 单元格样式 - 文本溢出显示省略号
const cellStyles: React.CSSProperties = {
  padding: "8px 12px",
  verticalAlign: "middle",
  whiteSpace: "nowrap",
  overflow: "hidden",
  textOverflow: "ellipsis"
}

// CHANGE: [2025-06-16] 修改组件接口，移除设置总份数功能，ReportArchives只负责展示档案
interface ReportArchivesProps {
  selectedRecords: any[]
}

// Custom Cell Renderer for Actions Column
const ActionsCellRenderer: React.FC<ICellRendererParams> = (props) => {
  const record = props.data; // The row data
  // TODO: [P1] Implement full DropdownMenu functionality for actions
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          操作
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => console.log("View Details:", record.id)}
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          查看详情
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => console.log("Download Archive:", record.id)}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          下载档案
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => console.log("Print Archive:", record.id)}
          className="flex items-center gap-2"
        >
          <Printer className="h-4 w-4" />
          打印档案
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Custom Cell Renderer for Badge Status
const StatusBadgeCellRenderer: React.FC<ICellRendererParams> = ({ value, colDef }) => {
  if (!value) return null;
  
  let badgeClass = "";
  const field = colDef?.field;

  if (field === 'archiveStatus') {
    badgeClass = value === "已归档"
      ? "bg-green-50 text-green-700 border-green-200"
      : value === "归档中"
        ? "bg-blue-50 text-blue-700 border-blue-200"
        : "bg-yellow-50 text-yellow-700 border-yellow-200";
  } else if (field === 'distributionStatus') {
    badgeClass = value === "未发放"
      ? "bg-gray-50 text-gray-700 border-gray-200"
      : value === "已发放一次"
        ? "bg-blue-50 text-blue-700 border-blue-200"
        : "bg-green-50 text-green-700 border-green-200";
  }

      return (
      <Badge variant="outline" className={cn(badgeClass, "whitespace-normal")}>
        {value}
      </Badge>
    );
  };

// CHANGE: [2025-06-16] 移除设置总份数相关的状态和功能
export function ReportArchives({ selectedRecords }: ReportArchivesProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [archivesActiveTab, setArchivesActiveTab] = useState("main")
  // AG Grid API引用
  const [archiveGridApi, setArchiveGridApi] = useState<any>(null)

  // AG Grid 多选配置
  const rowSelection = useMemo(() => { 
    return { 
      mode: 'multiRow' as const
    };
  }, [])

  // AG Grid准备就绪
  const handleArchiveGridReady = (event: any) => {
    setArchiveGridApi(event.api)
  }

  // 将选中的报告条目转换为档案条目
  const archiveItems = selectedRecords.map((record) => {
    // 为每个档案条目生成模拟数据
    const commissionId = `WTD-${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}-${new Date().getFullYear()}`
    const sampleId = `YP-${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}`

    // 随机生成归档状态
    const archiveStatuses = ["已归档", "待归档", "归档中"]
    const archiveStatus = archiveStatuses[Math.floor(Math.random() * archiveStatuses.length)]

    // 随机生成发放状态
    const distributionStatuses = ["未发放", "已发放一次", "已发放两次"]
    const distributionStatus = distributionStatuses[Math.floor(Math.random() * distributionStatuses.length)]

    // 生成委托信息
    const commissioners = ["张三", "李四", "王五", "赵六"]
    const commissioner = commissioners[Math.floor(Math.random() * commissioners.length)]
    const commissionUnits = ["北京建筑公司", "上海工程集团", "广州设计院", "深圳建设有限公司"]
    const commissionUnit = commissionUnits[Math.floor(Math.random() * commissionUnits.length)]

    // 生成发放信息
    const firstDistDate =
      distributionStatus !== "未发放"
        ? `${new Date().getFullYear()}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, "0")}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, "0")}`
        : ""
    const secondDistDate =
      distributionStatus === "已发放两次"
        ? `${new Date().getFullYear()}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, "0")}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, "0")}`
        : ""

    // 生成人员信息
    const distributors = ["张经理", "李主管", "王工程师"]
    const firstDistributor =
      distributionStatus !== "未发放" ? distributors[Math.floor(Math.random() * distributors.length)] : ""
    const secondDistributor =
      distributionStatus === "已发放两次" ? distributors[Math.floor(Math.random() * distributors.length)] : ""

    // 生成电话信息
    const generatePhone = () =>
      `1${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(8, "0")}`
    const firstDistPhone = distributionStatus !== "未发放" ? generatePhone() : ""
    const secondDistPhone = distributionStatus === "已发放两次" ? generatePhone() : ""

    // 生成领取人信息
    const recipients = ["陈客户", "刘代表", "孙经理"]
    const firstRecipient =
      distributionStatus !== "未发放" ? recipients[Math.floor(Math.random() * recipients.length)] : ""
    const secondRecipient =
      distributionStatus === "已发放两次" ? recipients[Math.floor(Math.random() * recipients.length)] : ""

    // 生成领取人电话
    const firstRecipientPhone = distributionStatus !== "未发放" ? generatePhone() : ""
    const secondRecipientPhone = distributionStatus === "已发放两次" ? generatePhone() : ""

    return {
      id: record.id,
      commissionId: commissionId,
      unifiedId: commissionId,
      sampleId: sampleId,
      archiveStatus: archiveStatus,
      distributionStatus: distributionStatus,
      commissionUnit: commissionUnit,
      commissioner: commissioner,
      projectName: record.title,
      firstDistDate: firstDistDate,
      firstDistributor: firstDistributor,
      firstDistPhone: firstDistPhone,
      firstRecipient: firstRecipient,
      firstRecipientPhone: firstRecipientPhone,
      secondDistDate: secondDistDate,
      secondDistributor: secondDistributor,
      secondDistPhone: secondDistPhone,
      secondRecipient: secondRecipient,
      secondRecipientPhone: secondRecipientPhone,
    }
  })

  // 筛选档案
  const filteredArchives = archiveItems.filter(
    (archive) =>
      searchQuery === "" ||
      archive.commissionId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      archive.sampleId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      archive.projectName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      archive.commissionUnit.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // AG Grid Column Definitions
  const columnDefs: ColDef[] = [
    { headerName: "委托编号", field: "commissionId", width: 150, pinned: 'left' }, // 固定在左侧
    { headerName: "统一编号", field: "unifiedId", width: 150 },
    { headerName: "样品编号", field: "sampleId", width: 120 },
    { headerName: "归档状态", field: "archiveStatus", width: 120, cellRenderer: StatusBadgeCellRenderer },
    { headerName: "发放状态", field: "distributionStatus", width: 120, cellRenderer: StatusBadgeCellRenderer },
    { headerName: "委托单位", field: "commissionUnit", width: 180, tooltipField: "commissionUnit" }, // 长文本提示
    { headerName: "委托人", field: "commissioner", width: 100 },
    { headerName: "工程名称", field: "projectName", width: 180, tooltipField: "projectName" },
    { headerName: "第一次发放时间", field: "firstDistDate", width: 150 },
    { headerName: "发放人", field: "firstDistributor", width: 100 },
    { headerName: "发放人电话", field: "firstDistPhone", width: 120 },
    { headerName: "领取人", field: "firstRecipient", width: 100 },
    { headerName: "领取人电话", field: "firstRecipientPhone", width: 120 },
    { headerName: "第二次发放时间", field: "secondDistDate", width: 150 },
    { headerName: "发放人 ", field: "secondDistributor", width: 100 }, // 注意表头空格以区分
    { headerName: "发放人电话 ", field: "secondDistPhone", width: 120 }, // 注意表头空格以区分
    { headerName: "领取人 ", field: "secondRecipient", width: 100 }, // 注意表头空格以区分
    { headerName: "领取人电话 ", field: "secondRecipientPhone", width: 120 }, // 注意表头空格以区分
    { 
      headerName: "操作", 
      field: "actions", 
      width: 100, 
      cellRenderer: ActionsCellRenderer, // 使用自定义渲染器
      pinned: 'right', // 固定在右侧
      sortable: false, // 操作列通常不可排序
      filter: false,   // 操作列通常不可过滤
    }
  ];

  // Default ColDef for AG Grid (applies to all columns unless overridden)
  const defaultColDef: ColDef = {
    sortable: true,
    filter: true,
    resizable: true,
    suppressHeaderMenuButton: true, // 更正：使用 suppressHeaderMenuButton 替代 suppressMenu
    floatingFilter: true, // 在表头下方显示筛选框
    // wrapText: true, // 暂时移除以排查问题
    // autoHeight: true, // 暂时移除以排查问题
  };
  // TODO: [P2] 将搜索逻辑迁移到 AG Grid 的 QuickFilter 或 API筛选

  return (
    <div className="h-full w-full overflow-hidden">
      <Card className="h-full shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="p-2.5 h-full flex flex-col">
          {/* 卡片标题和计数 */}
          <div className="flex items-center justify-between mb-1.5">
            <h3 className="text-lg font-medium">
              档案列表 (AG Grid)
            </h3>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                档案条目: {archiveItems.length} 项
              </Badge>
            </div>
          </div>

          <div className="relative flex-1 min-h-0">
            <Tabs 
              value={archivesActiveTab} 
              onValueChange={setArchivesActiveTab}
              className="h-full"
            >
              <TabsList className="hidden">
                <TabsTrigger value="main"></TabsTrigger>
              </TabsList>
              
              <TabsContent 
                value="main" 
                className="absolute inset-0 overflow-hidden border rounded-md"
                style={{ margin: 0 }}
              >
                <div className="h-full flex flex-col">
                  {/* 搜索栏 - 固定在顶部 */}
                  <div className="p-2 border-b bg-gray-50 flex items-center gap-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                      <Input
                        placeholder="搜索委托编号、样品编号或工程名称... (当前通过外部筛选)"
                        className="pl-9 h-9"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </div>
                  
                  {/* AG Grid Integration */}
                  <div className="flex-1 ag-theme-alpine" style={{ height: '100%', width: '100%' }}> {/* 确保父容器占满剩余空间 */}
                    {filteredArchives.length === 0 && searchQuery !== "" ? ( // 仅当搜索后无结果时显示
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                          <p className="text-gray-500">根据当前搜索条件，暂无档案记录</p>
                        </div>
            </div>
          ) : (
                      <AgGridReact
                        rowData={filteredArchives}
                        columnDefs={columnDefs}
                        defaultColDef={defaultColDef}
                        modules={[
                          AllEnterpriseModule,
                        ]} 
                        pagination={true}         // 开启分页
                        paginationPageSize={20}   // 每页20条
                        domLayout='normal'        // 使用 'normal' 布局, AG Grid 会处理滚动
                        suppressPaginationPanel={false} // 显示分页控件
                        animateRows={true}          // 行动画
                        rowSelection={rowSelection} // 多选配置
                        onGridReady={handleArchiveGridReady} // Grid准备就绪回调
                        // getRowHeight={() => 50} // 可选：固定行高
                        // onGridReady={(params) => params.api.sizeColumnsToFit()} // 可选：列宽自适应容器
                        // TODO: [P3] 对于大数据量，后续考虑服务器端数据源 (SSRM)
                      />
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
        </div>
      </CardContent>
    </Card>
    </div>
  )
}
