# 操作文档：修复Celery定期任务名称不匹配问题

## 📋 变更摘要

**目的**: 修复Celery Beat调度器中cleanup-stuck-processing-tasks任务名称配置错误
**范围**: archive_flow_manager/settings.py
**关联**: Celery定期任务配置

## 🔧 操作步骤

### 📊 OP-001: 分析错误原因

**前置条件**: Celery worker出现KeyError错误
**操作**: 分析错误日志，发现任务名称不匹配

- 错误信息显示Celery寻找任务：`'archive_processing.tasks.cleanup.cleanup_stuck_tasks'`
- 实际定义的任务名称：`'archive_processing.cleanup_stuck_tasks'`
**后置条件**: 确定了问题根本原因

### ✏️ OP-002: 修复任务名称配置

**前置条件**: 已确定问题原因
**操作**: 更新settings.py中CELERY_BEAT_SCHEDULE配置
**后置条件**: 任务名称配置与实际定义一致

## 📝 变更详情

### CH-001: 更新定期任务配置

**文件**: `archive_flow_manager/settings.py`
**变更前**:

```python
'cleanup-stuck-processing-tasks': {
    'task': 'archive_processing.tasks.cleanup.cleanup_stuck_tasks',
    'schedule': 900.0, # 每15分钟执行一次 (900秒)
    'options': {
        'expires': 600, # 任务10分钟后过期
    }
}
```

**变更后**:

```python
'cleanup-stuck-processing-tasks': {
    'task': 'archive_processing.cleanup_stuck_tasks',
    'schedule': 900.0, # 每15分钟执行一次 (900秒)
    'options': {
        'expires': 600, # 任务10分钟后过期
    }
}
```

**变更理由**: 使任务名称与cleanup.py中@shared_task装饰器定义的名称一致
**潜在影响**: 修复后定期任务将能正常执行，不再出现KeyError

## ✅ 验证结果

**方法**:

1. 检查cleanup.py中的任务定义
2. 验证settings.py中的配置修改
3. 确认其他定期任务配置正确性

**结果**:

- 成功修复任务名称不匹配问题
- 验证了process_finalized_sessions_task配置正确
- 无其他类似问题

**问题**: 无
**解决方案**: 不适用

## 📋 总结

✅ 已完成工作：

- 分析并确定了Celery任务名称不匹配的根本原因
- 修复了settings.py中cleanup-stuck-processing-tasks的任务名称配置
- 验证了其他定期任务配置的正确性

📈 下一步：

- 重启Celery worker和beat服务以应用配置更改
- 监控定期任务的执行情况

⚠️ 已知问题：

- 需要重启服务才能使配置生效
