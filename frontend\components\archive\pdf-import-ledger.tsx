"use client"

import React, { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect } from "react"
import { AgGridReact } from "ag-grid-react"
import {
  ColDef,
  GridReadyEvent,
  GridApi,
  IDetailCellRendererParams,
  ICellRendererParams,
} from "ag-grid-community"
import "ag-grid-community/styles/ag-theme-quartz.css"

import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Download,
  Eye,
  Loader2,
  RefreshCw,
  Trash2,
} from "lucide-react"
import agGridConfig from "@/lib/ag-grid-config"
import { toast } from "@/hooks/use-toast"

// 数据类型定义
interface ProcessingTask {
  id: number
  file_name: string
  uploader_username: string
  created_at: string
  status: "queued" | "processing" | "completed" | "failed"
  file_id: number;
}

interface ArchiveRecord {
  id: number
  unified_number: string
  title: string
  archive_url: string
  report_url: string
}

// 模拟数据
const mockTasks: ProcessingTask[] = [
    { id: 1, file_name: "项目A-2023年度报告.pdf", uploader_username: "张三", created_at: "2023-11-15 14:30", status: "completed", file_id: 101 },
    { id: 2, file_name: "技术规范文档-v2.pdf", uploader_username: "李四", created_at: "2023-11-16 09:15", status: "processing", file_id: 102 },
    { id: 3, file_name: "财务报表-2023-Q3.pdf", uploader_username: "王五", created_at: "2023-11-16 11:22", status: "failed", file_id: 103 },
    { id: 4, file_name: "产品设计稿-初版.pdf", uploader_username: "赵六", created_at: "2023-11-17 10:05", status: "queued", file_id: 104 },
];

const mockRecords: { [key: number]: ArchiveRecord[] } = {
    1: [
        { id: 1001, unified_number: "DA-2023-001", title: "项目A报告-部分1", archive_url: "#", report_url: "#" },
        { id: 1002, unified_number: "DA-2023-002", title: "项目A报告-部分2", archive_url: "#", report_url: "#" },
    ],
};


const statusBadgeMap = {
  queued: (
    <Badge variant="outline">
      <Clock className="mr-1 h-3 w-3" />
      待处理
    </Badge>
  ),
  processing: (
    <Badge variant="secondary">
      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
      处理中
    </Badge>
  ),
  completed: (
    <Badge variant="outline" className="bg-green-100 text-green-800">
      <CheckCircle2 className="mr-1 h-3 w-3" />
      已完成
    </Badge>
  ),
  failed: (
    <Badge variant="destructive">
      <AlertCircle className="mr-1 h-3 w-3" />
      失败
    </Badge>
  ),
}

const ActionsCellRenderer: React.FC<ICellRendererParams<ProcessingTask>> = ({ data, api }) => {
  if (!data) return null

  const handleRetry = async () => {
    toast({ title: `正在重试任务 #${data.id}... (模拟)` })
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    toast({ title: "操作成功", description: `任务 #${data.id} 已重新加入队列。` });
    api.refreshServerSide({ route: [data.id.toString()], purge: true });
  };

  const handleReUpload = async () => {
    toast({ title: "功能待实现", description: "重新上传功能需要UI支持。" })
  };

  return (
    <div className="flex items-center gap-2">
      {data.status === 'failed' && (
        <Button variant="outline" size="sm" onClick={handleRetry}>
          <RefreshCw className="mr-1 h-3 w-3" />
          重试
        </Button>
      )}
      <Button variant="outline" size="sm" onClick={handleReUpload}>
        <Trash2 className="mr-1 h-3 w-3" />
        重新上传
      </Button>
    </div>
  )
}

interface PdfImportLedgerProps {
  activeTab: string;
}

export function PdfImportLedger({ activeTab }: PdfImportLedgerProps) {
  const [gridApi, setGridApi] = useState<GridApi | null>(null)

  useEffect(() => {
    if (gridApi) {
        const filteredData = mockTasks.filter(task => {
            if (activeTab === 'processing') return ['processing', 'queued'].includes(task.status);
            if (activeTab === 'completed') return task.status === 'completed';
            if (activeTab === 'failed') return task.status === 'failed';
            return true;
        });
        gridApi.setGridOption('rowData', filteredData);
    }
  }, [activeTab, gridApi]);

  const defaultColDef = useMemo<ColDef>(() => ({
    ...agGridConfig.defaultColDef,
    flex: 1,
  }), [])
  
  const columnDefs = useMemo<ColDef<ProcessingTask>[]>(() => [
    {
      headerName: "任务ID",
      field: "id",
      maxWidth: 100,
      cellRenderer: "agGroupCellRenderer",
    },
    { headerName: "文件名", field: "file_name", minWidth: 250 },
    { headerName: "上传用户", field: "uploader_username", maxWidth: 150 },
    { headerName: "上传时间", field: "created_at", maxWidth: 200 },
    {
      headerName: "状态",
      field: "status",
      maxWidth: 120,
      cellRenderer: (params: ICellRendererParams<ProcessingTask>) =>
        params.value ? statusBadgeMap[params.value as keyof typeof statusBadgeMap] : null,
    },
    {
        headerName: "操作",
        pinned: "right",
        cellRenderer: ActionsCellRenderer,
        minWidth: 220,
      },
  ], [])

  const detailCellRendererParams = useMemo<any>(() => {
    return {
      detailGridOptions: {
        ...agGridConfig.clientSideDefaults,
        columnDefs: [
          { headerName: "档案ID", field: "id", maxWidth: 100 },
          { headerName: "统一编号", field: "unified_number" },
          { headerName: "题名", field: "title", minWidth: 300 },
          {
            headerName: '操作',
            pinned: 'right',
            cellRenderer: (params: ICellRendererParams<ArchiveRecord>) => {
              if(!params.data) return null;
              return (
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={params.data.archive_url} target="_blank" rel="noopener noreferrer"><Eye className="mr-1 h-3 w-3"/>档案</a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={params.data.report_url} target="_blank" rel="noopener noreferrer"><Download className="mr-1 h-3 w-3"/>报告</a>
                  </Button>
                </div>
              )
            }
          }
        ],
        defaultColDef: {
          ...agGridConfig.defaultColDef,
          flex: 1,
        },
      },
      getDetailRowData: (params: any) => {
        // 使用模拟数据
        const records = mockRecords[params.data.id] || [];
        params.successCallback(records);
      },
    }
  }, [])

  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api)
  }, [])

  const gridStyle = { height: "100%", width: "100%" };
  const initialData = mockTasks.filter(task => ['processing', 'queued'].includes(task.status));

  return (
    <div className="h-full w-full ag-theme-quartz" style={gridStyle}>
      <AgGridReact
        rowData={initialData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        rowModelType="clientSide"
        onGridReady={onGridReady}
        masterDetail={true}
        detailCellRendererParams={detailCellRendererParams}
        {...agGridConfig.clientSideDefaults}
      />
    </div>
  )
}
