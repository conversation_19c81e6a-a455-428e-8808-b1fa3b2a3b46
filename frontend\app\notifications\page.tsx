"use client"

import { useState, useEffect } from "react"
import { PageTitle } from "@/components/page-title"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Pagination } from "@/components/ui/pagination"
import { AlertCircle, CheckCircle, Info, Search, Trash, XCircle } from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"

interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  read: boolean
  createdAt: string
  link?: string
}

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 10

  // 模拟获取通知数据
  useEffect(() => {
    // 实际应用中，这里会从API获取通知数据
    // const fetchNotifications = async () => {
    //   const response = await fetch('/api/notifications');
    //   const data = await response.json();
    //   setNotifications(data);
    // };
    // fetchNotifications();

    // 模拟数据
    const mockNotifications: Notification[] = [
      {
        id: "1",
        title: "新的更改单",
        message: "用户 张三 创建了一个新的更改单，请审核",
        type: "info",
        read: false,
        createdAt: "2023-12-20 10:30",
        link: "/records/AR-2023-0001/change-order/1",
      },
      {
        id: "2",
        title: "归档任务完成",
        message: "PDF文件 'XYZ-2023-001-环评报告.pdf' 已成功归档",
        type: "success",
        read: false,
        createdAt: "2023-12-19 15:45",
        link: "/archive/tasks/TASK-2023-001",
      },
      {
        id: "3",
        title: "导入失败",
        message: "Excel文件 '2023年12月台账.xlsx' 导入失败，请检查文件格式",
        type: "error",
        read: false,
        createdAt: "2023-12-18 09:20",
        link: "/import/history/IMP-2023-006",
      },
      {
        id: "4",
        title: "发放单待确认",
        message: "发放单 FH-2023-0005 已创建，等待确认发放",
        type: "warning",
        read: true,
        createdAt: "2023-12-17 14:10",
        link: "/reports/FH-2023-0005",
      },
      {
        id: "5",
        title: "系统更新",
        message: "系统将于今晚22:00-23:00进行维护更新，请提前保存工作",
        type: "info",
        read: true,
        createdAt: "2023-12-16 11:25",
      },
      {
        id: "6",
        title: "用户权限变更",
        message: "您的系统权限已更新，请刷新页面查看最新权限",
        type: "info",
        read: true,
        createdAt: "2023-12-15 16:30",
      },
      {
        id: "7",
        title: "报告发放完成",
        message: "发放单 FH-2023-0004 的报告已全部发放完成",
        type: "success",
        read: true,
        createdAt: "2023-12-14 13:20",
        link: "/reports/FH-2023-0004",
      },
      {
        id: "8",
        title: "更改单被拒绝",
        message: "您提交的更改单 CO-2023-0015 已被拒绝，原因：缺少必要的审批文件",
        type: "error",
        read: true,
        createdAt: "2023-12-13 11:05",
        link: "/records/AR-2023-0002/change-order/15",
      },
      {
        id: "9",
        title: "台账导入完成",
        message: "Excel文件 '2023年11月台账.xlsx' 已成功导入，共处理 45 条记录",
        type: "success",
        read: true,
        createdAt: "2023-12-12 09:40",
        link: "/import/history/IMP-2023-005",
      },
      {
        id: "10",
        title: "系统备份提醒",
        message: "系统将于今晚进行例行数据备份，备份期间系统可能会短暂不可用",
        type: "warning",
        read: true,
        createdAt: "2023-12-11 17:15",
      },
      {
        id: "11",
        title: "新用户注册",
        message: "新用户 李四 已注册系统，请及时分配权限",
        type: "info",
        read: true,
        createdAt: "2023-12-10 10:00",
        link: "/users/10",
      },
      {
        id: "12",
        title: "密码即将过期",
        message: "您的密码将在 7 天后过期，请及时修改密码",
        type: "warning",
        read: true,
        createdAt: "2023-12-09 08:30",
        link: "/profile",
      },
    ]

    setNotifications(mockNotifications)
  }, [])

  // 过滤和分页
  useEffect(() => {
    let filtered = [...notifications]

    // 按标签过滤
    if (activeTab === "unread") {
      filtered = filtered.filter((n) => !n.read)
    }

    // 按类型过滤
    if (typeFilter !== "all") {
      filtered = filtered.filter((n) => n.type === typeFilter)
    }

    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter((n) => n.title.toLowerCase().includes(term) || n.message.toLowerCase().includes(term))
    }

    // 计算总页数
    setTotalPages(Math.max(1, Math.ceil(filtered.length / itemsPerPage)))

    // 如果当前页超出范围，重置为第一页
    if (currentPage > Math.ceil(filtered.length / itemsPerPage)) {
      setCurrentPage(1)
    }

    // 分页
    const startIndex = (currentPage - 1) * itemsPerPage
    const paginatedNotifications = filtered.slice(startIndex, startIndex + itemsPerPage)

    setFilteredNotifications(paginatedNotifications)
  }, [notifications, activeTab, typeFilter, searchTerm, currentPage])

  const handleMarkAllAsRead = async () => {
    // 实际应用中，这里会调用API将所有通知标记为已读
    // const response = await fetch('/api/notifications/mark-all-read', {
    //   method: 'POST',
    // });
    // if (response.ok) {
    //   setNotifications(notifications.map(n => ({ ...n, read: true })));
    // }

    // 模拟标记为已读
    setNotifications(notifications.map((n) => ({ ...n, read: true })))
  }

  const handleMarkAsRead = async (id: string) => {
    // 实际应用中，这里会调用API将通知标记为已读
    // const response = await fetch(`/api/notifications/${id}/mark-read`, {
    //   method: 'POST',
    // });
    // if (response.ok) {
    //   setNotifications(notifications.map(n => n.id === id ? { ...n, read: true } : n));
    // }

    // 模拟标记为已读
    setNotifications(notifications.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const handleClearAll = async () => {
    // 实际应用中，这里会调用API清空所有通知
    // const response = await fetch('/api/notifications/clear-all', {
    //   method: 'DELETE',
    // });
    // if (response.ok) {
    //   setNotifications([]);
    // }

    // 模拟清空通知
    setNotifications([])
  }

  const getIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info className="h-5 w-5 text-blue-500" />
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 格式化时间为"x分钟前"、"x小时前"等
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
    } catch (error) {
      return dateString
    }
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <PageTitle title="通知中心" subtitle="查看和管理系统通知" />
        <div className="flex gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" onClick={handleMarkAllAsRead}>
              全部标为已读
            </Button>
          )}
          <Button variant="outline" onClick={handleClearAll} className="text-red-500">
            <Trash className="mr-2 h-4 w-4" />
            清空通知
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>通知列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="unread">
                  未读
                  {unreadCount > 0 && <span className="ml-1 text-xs">({unreadCount})</span>}
                </TabsTrigger>
              </TabsList>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索通知..."
                    className="pl-8 w-full sm:w-[200px]"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-full sm:w-[150px]">
                    <SelectValue placeholder="通知类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="info">信息</SelectItem>
                    <SelectItem value="success">成功</SelectItem>
                    <SelectItem value="warning">警告</SelectItem>
                    <SelectItem value="error">错误</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <TabsContent value="all" className="m-0">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-40 text-muted-foreground">暂无通知</div>
              ) : (
                <div className="space-y-1 border rounded-md">
                  {filteredNotifications.map((notification) => {
                    const content = (
                      <div
                        className={`flex items-start gap-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                          !notification.read ? "bg-blue-50 dark:bg-blue-900/20" : ""
                        }`}
                        onClick={() => !notification.read && handleMarkAsRead(notification.id)}
                      >
                        <div className="flex-shrink-0 mt-1">{getIcon(notification.type)}</div>
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                            <h5 className={`text-sm font-medium ${!notification.read ? "font-semibold" : ""}`}>
                              {notification.title}
                            </h5>
                            <span className="text-xs text-muted-foreground">{formatTime(notification.createdAt)}</span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                        </div>
                        {!notification.read && (
                          <div className="flex-shrink-0 w-2 h-2 rounded-full bg-blue-500 mt-2" aria-hidden="true" />
                        )}
                      </div>
                    )

                    return (
                      <div key={notification.id} className="border-b last:border-0">
                        {notification.link ? <Link href={notification.link}>{content}</Link> : content}
                      </div>
                    )
                  })}
                </div>
              )}
            </TabsContent>

            <TabsContent value="unread" className="m-0">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-40 text-muted-foreground">暂无未读通知</div>
              ) : (
                <div className="space-y-1 border rounded-md">
                  {filteredNotifications.map((notification) => {
                    const content = (
                      <div
                        className="flex items-start gap-4 p-4 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <div className="flex-shrink-0 mt-1">{getIcon(notification.type)}</div>
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                            <h5 className="text-sm font-semibold">{notification.title}</h5>
                            <span className="text-xs text-muted-foreground">{formatTime(notification.createdAt)}</span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                        </div>
                        <div className="flex-shrink-0 w-2 h-2 rounded-full bg-blue-500 mt-2" aria-hidden="true" />
                      </div>
                    )

                    return (
                      <div key={notification.id} className="border-b last:border-0">
                        {notification.link ? <Link href={notification.link}>{content}</Link> : content}
                      </div>
                    )
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={setCurrentPage} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
