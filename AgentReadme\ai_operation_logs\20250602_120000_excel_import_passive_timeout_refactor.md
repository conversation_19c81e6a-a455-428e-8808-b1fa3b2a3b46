# Operation Document: Excel Import Conflict Resolution - Passive Timeout Refactor

## 📋 Change Summary

**Purpose**: Refactor the Excel import conflict resolution state machine to use a passive heartbeat timeout mechanism and update related API views. This replaces the active Celery-based timeout task.
**Scope**:

- `archive_records/models.py` (status enum, transitions - already mostly done, re-verified)
- `archive_records/views.py` (Update `GetActiveImportSessionView`, significantly refactor `BeginActiveConflictProcessingView`)
- `archive_records/services/import_session_manager.py` (Update `confirm_import` pre-conditions, ensure `analyze_session` correctly triggers `CONFLICT_RESOLUTION_STARTED`)
- `archive_records/tasks.py` (Remove `process_heartbeat_timeouts_task`)
**Associated**: `AgentReadme/active_feature_plans/excel_import_conflict_resolution_state_refactor_v3.2.md` (This log reflects updates to this plan)

## 🔧 Operation Steps

### 📊 OP-001: Analyze and Confirm Refactoring Plan

**Precondition**: V3.2 plan existed with an active Celery-based timeout task.
**Operation**: Discussed and confirmed a shift to a passive timeout handling mechanism, primarily managed within `BeginActiveConflictProcessingView`. Clarified API call sequences and frontend interaction expectations.
**Postcondition**: Agreed plan to remove Celery task for heartbeats and consolidate timeout logic into the API view that attempts to start/resume conflict processing.

### ✏️ OP-002: Modify `ImportSessionManager.confirm_import`

**Precondition**: `confirm_import` allowed submissions from `STARTED`, `PENDING`, or `IN_PROGRESS` states.
**Operation**:

- Modified `archive_records/services/import_session_manager.py`.
- Restricted `confirm_import` to only accept submissions when the session status is strictly `CONFLICT_RESOLUTION_IN_PROGRESS`.
- Ensured validation that `request.user` is the `processing_user` when confirming from `IN_PROGRESS`.
**Postcondition**: `confirm_import` has stricter pre-conditions.
**File**: `archive_records/services/import_session_manager.py`
**Change Details**:

```python
# In ImportSessionManager.confirm_import:
# ...
                # CHANGE: [2025-06-02] 严格要求确认导入的前置状态必须是 CONFLICT_RESOLUTION_IN_PROGRESS
                if db_session.status != ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS:
                    logger.error(
                        f"[Mgr.Confirm] Session {session_id} status is {db_session.status}, expected {ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS}."
                    )
                    return {
                        "success": False,
                        "error": f"导入会话状态错误({db_session.get_status_display()})，期望状态为'冲突处理中'。请先开始处理冲突。",
                        "error_type": "invalid_session_state_for_confirm",
                    }
                
                # 验证提交用户是否为当前处理用户
                if db_session.processing_user != user:
                    logger.warning(f"[Mgr.Confirm] User {user.username} attempted to confirm session {session_id} which is being processed by {db_session.processing_user.username if db_session.processing_user else 'None'}.")
                    return {
                        "success": False,
                        "error": f"您不是此会话的当前处理用户 ('{db_session.processing_user.username if db_session.processing_user else '未知'}')，无法提交决策。",
                        "error_type": "confirm_by_non_processing_user",
                    }
# ...
```

### ✏️ OP-003: Modify `ImportSessionManager.analyze_session`

**Precondition**: `analyze_dataframe` (called by `analyze_session`) set status to `ANALYSIS_COMPLETE`.
**Operation**:

- Modified `archive_records/services/import_session_manager.py`.
- Added logic at the end of `analyze_session` method, after `analyze_dataframe` successfully completes and sets session status to `ANALYSIS_COMPLETE`.
- This new logic atomically transitions the session status from `ANALYSIS_COMPLETE` to `CONFLICT_RESOLUTION_STARTED` and records a `SessionOperation`.
**Postcondition**: `analyze_session` now ensures the session moves to `CONFLICT_RESOLUTION_STARTED` automatically after successful analysis.
**File**: `archive_records/services/import_session_manager.py`
**Change Details**: (Code snippet for this was provided and confirmed in conversation, added at the end of `analyze_session`)

### ✏️ OP-004: Remove Celery Task for Heartbeat Timeouts

**Precondition**: `process_heartbeat_timeouts_task` existed in `archive_records/tasks.py`.
**Operation**: Deleted the `process_heartbeat_timeouts_task` Celery task from `archive_records/tasks.py`.
**Postcondition**: Active scanning for heartbeat timeouts via Celery is removed.
**File**: `archive_records/tasks.py`

### ✏️ OP-005: Refactor `GetActiveImportSessionView`

**Precondition**: `GetActiveImportSessionView` might have had logic to alter session state.
**Operation**:

- Modified `archive_records/views.py`.
- Simplified `GetActiveImportSessionView` to only fetch and return the current state of the active session without attempting to modify it based on heartbeats. Timeout logic is moved.
**Postcondition**: `GetActiveImportSessionView` is read-only regarding session state.
**File**: `archive_records/views.py`

### ✏️ OP-006: Refactor `BeginActiveConflictProcessingView` for Unified Entry and Passive Timeout

**Precondition**: `BeginActiveConflictProcessingView` primarily handled starting new conflict resolution.
**Operation**:

- Modified `archive_records/views.py`.
- Transformed `BeginActiveConflictProcessingView.post` into a unified entry point.
- It now handles:
    1. Starting processing for sessions in `CONFLICT_RESOLUTION_STARTED` or `CONFLICT_RESOLUTION_PENDING`.
    2. Allowing the current `processing_user` to continue (effectively a heartbeat refresh) if the session is `IN_PROGRESS`.
    3. Allowing a new user to "take over" a session in `IN_PROGRESS` if the current `processing_user`'s heartbeat has timed out. This involves checking `last_heartbeat_at` against `SESSION_HEARTBEAT_TIMEOUT_MINUTES`.
    4. Rejecting attempts if another user is actively processing (heartbeat current).
- All state changes are performed atomically within a transaction.
**Postcondition**: `BeginActiveConflictProcessingView` now centrally manages the logic for a user to start or resume conflict processing, including passive handling of timed-out sessions.
**File**: `archive_records/views.py`
**Change Details**: (The implemented view code reflects this logic, including checks for `session.is_active()`, current status, current processing user, and heartbeat timeout).

## 📝 Change Details

(Key code snippets are included in the Operation Steps above. Full diffs for each file would be extensive but the changes adhere to the logic described.)

## ✅ Verification Results

**Method**: Code review and discussion of logic. Unit and integration tests are pending.
**Results**: Backend logic updated to support passive timeout handling and unified conflict processing entry.
**Problems**: Initial confusion regarding modifications to `excel_conflict_analyzer.py` was clarified.
**Solutions**: Clarified that `excel_conflict_analyzer.py` only had a minor addition for state transition post-analysis, and was not subject to large-scale code duplication.

## 🚀 Next Steps for Frontend

- Update `frontend/hooks/useExcelImportSession.ts` and relevant UI components (e.g., `excel-import-with-conflict-resolution.tsx`).
- Ensure the "Process Conflicts" button now always calls the refactored `BeginActiveConflictProcessingView` API.
- Frontend UI should react to the API response from `BeginActiveConflictProcessingView`:
  - On success: Open conflict modal, update session info.
  - On failure (e.g., another user actively processing, session not actionable): Display appropriate alerts/messages to the user and refresh UI if needed based on `current_status` provided in error response.
- Heartbeat calls from the frontend should continue when a user is actively in the conflict resolution modal (`IN_PROGRESS` state for them).
- The `GetActiveImportSessionView` API will provide the current session state; frontend should use this to determine initial UI rendering, but the action of starting/resuming processing is now fully arbitrated by `BeginActiveConflictProcessingView`.
