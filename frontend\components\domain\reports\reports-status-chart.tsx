"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

export function ReportsStatusChart() {
  // 模拟数据 - 实际应用中应从API获取
  const total = 1284
  const data = [
    { name: "未发放", value: 384, color: "#f59e0b" },
    { name: "部分发放", value: 578, color: "#3b82f6" },
    { name: "全部发放", value: 322, color: "#10b981" },
  ]

  // 计算百分比
  const getPercentage = (value: number) => {
    return Math.round((value / total) * 100)
  }

  // 获取颜色
  const getColor = (name: string) => {
    if (name === "未发放") return "rgb(245, 158, 11)" // amber-500
    if (name === "部分发放") return "rgb(59, 130, 246)" // blue-500
    return "rgb(16, 185, 129)" // emerald-500 (全部发放)
  }

  return (
    <Card className="col-span-1 lg:col-span-2">
      <CardHeader>
        <CardTitle>发放状态分布</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {data.map((item) => (
          <div key={item.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{item.name}</span>
              <span className="text-sm font-medium">
                {item.value}/{total} ({getPercentage(item.value)}%)
              </span>
            </div>
            <Progress
              value={getPercentage(item.value)}
              className="h-2"
              style={{ "--indicator-color": getColor(item.name) } as React.CSSProperties}
            />
          </div>
        ))}
      </CardContent>
      <CardFooter>
        <Link href="/reports/management" className="text-sm font-medium flex items-center hover:underline">
          查看详细统计 <ArrowRight className="ml-1 h-4 w-4" />
        </Link>
      </CardFooter>
    </Card>
  )
}
