# 操作日志：修复 tasks.py 中的 FieldError

## 📅 日期
2025-04-16

## 📋 变更摘要
**目的**: 修复在运行 `process_pdf_task` 集成测试时，由于业务代码 `tasks.py` 中 `select_related` 使用错误字段名 (`uploaded_file` 而非 `file`) 导致的 `FieldError`。
**范围**: `archive_processing/tasks.py`
**关联**: #AFM-15 (测试相关)

## 🔧 操作步骤

### 📊 OP-001: 分析测试失败原因
**前置条件**: 集成测试 `test_precheck_success_full_workflow` 失败，错误包括 `AssertionError` 和 `FieldError: Invalid field name(s) given in select_related: 'uploaded_file'`。
**操作**: 分析 pytest 输出的错误日志和堆栈跟踪，定位到 `tasks.py` 第 35 行的 `ProcessingTask.objects.select_related('uploaded_file')` 调用是导致 `FieldError` 的直接原因。该错误阻止了任务正常执行，进而导致最终状态断言失败。
**后置条件**: 明确了业务代码中的 `select_related` 错误是导致测试失败的根本原因。

### ✏️ OP-002: 修改 tasks.py
**前置条件**: `tasks.py` 第 35 行使用了 `select_related('uploaded_file')`。
**操作**: 使用 `edit_file` 工具将 `tasks.py` 第 35 行的代码修改为 `task = ProcessingTask.objects.select_related('file').get(task_id=task_id)`。
**后置条件**: `tasks.py` 中 `select_related` 使用了正确的字段名 `file`。

## 📝 变更详情

### CH-001: 修正 select_related 字段名
**文件**: `archive_processing/tasks.py`
**变更前**:
```python
    try:
        task = ProcessingTask.objects.select_related('uploaded_file').get(task_id=task_id)
    except ProcessingTask.DoesNotExist:
```
**变更后**:
```python
    try:
        # CHANGE: [2024-07-26] 修正 select_related 使用的字段名为 'file'
        task = ProcessingTask.objects.select_related('file').get(task_id=task_id)
    except ProcessingTask.DoesNotExist:
```
**理由**: `ProcessingTask` 模型中关联 `UploadedFile` 的外键字段名为 `file`，而非 `uploaded_file`。
**潜在影响**: 无负面影响，修复了代码中的明显错误。

## ✅ 验证结果
**方法**: 重新运行集成测试 `pytest test_suite/integration/archive_processing/test_tasks.py -k test_precheck_success_full_workflow -v`。
**预期结果**: `FieldError` 消失，测试通过（假设没有其他问题）。
**实际结果**: (待测试运行后填写)

## 📌 问题与解决方案
**问题**: (待测试运行后填写)
**解决方案**: (待测试运行后填写) 