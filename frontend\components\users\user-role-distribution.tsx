"use client"
import { useTheme } from "next-themes"
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON>r, Toolt<PERSON> } from "recharts"

const data = [
  { name: "系统管理员", value: 8, color: "#0ea5e9" },
  { name: "档案管理员", value: 42, color: "#10b981" },
  { name: "报告发放员", value: 77, color: "#f59e0b" },
]

export function UserRoleDistribution() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            paddingAngle={2}
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            labelLine={true}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              borderColor: isDark ? "#374151" : "#e5e7eb",
              color: isDark ? "#f9fafb" : "#111827",
            }}
            formatter={(value: number) => [`${value} 用户`, "数量"]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
