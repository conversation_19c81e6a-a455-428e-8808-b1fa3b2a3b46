# PDF处理全流程重构 - 最终实施计划与设计蓝图 (V-ULTIMATE)

## 1. 概述与核心原则

本文档基于[ReportSplittingService_Integration_Design.md](./ReportSplittingService_Integration_Design.md)的详细设计，提供PDF处理全流程重构的高层架构规划。

**⚠️ 重要说明**: 具体的技术实现细节、接口设计和业务规则请参考[ReportSplittingService_Integration_Design.md](./ReportSplittingService_Integration_Design.md)文档。

**核心原则**:

- **强制业务规则**: **每个档案PDF都必须包含报告**，这是不可违背的业务规则。如果档案PDF中未找到报告，整个处理流程必须失败。
- **两步处理流程**: 对每个档案执行"归档（档案+报告）→ 数据库更新"的两步处理流程。
- **模式支持**: 系统支持**串行**和**并行**两种处理模式，由前端上传时通过参数控制。
- **参数简化**: 清理冗余参数（如assigned_box_number），采用直接参数传递方式。

## 2. 最终架构与工作流

系统采用"线性检查，原子入库"的核心思想，并支持串行和并行两种处理模式。

### 2.1 核心处理单元 (针对单个独立档案)

这是系统中处理**每一个独立档案**的最小、不可分割的工作单元。

**⚠️ 强制性业务规则**: 每个档案都必须包含报告，如果未找到报告页面范围，整个处理失败。

```mermaid
graph TD
    A[开始处理单个档案] --> B[ReportRecognitionService识别报告页面范围];
    B -- 成功 --> C[validate_all_parts_processable扩展版];
    C --> D{"档案分割<br>(生成archive_temp_path)"};
    D -- 成功 --> E{"报告分割<br>(生成report_temp_path)"};
    E -- 成功 --> F[两步处理流程];
    F --> G["第1步: archive_single_archive_report_pdf<br>(同时归档档案+报告)"];
    G -- 成功 --> H["第2步: update_archive_record<br>(更新数据库记录)"];
    H --> I[处理成功];
    B -- 未找到报告 --> J[业务规则失败];
    D -- 失败 --> J;
    E -- 失败 --> J;
    G -- 失败 --> J;
    H -- 失败 --> J;
    J --> K[处理失败];
```

### 2.2 总体工作流

```mermaid
graph TD
    subgraph "用户操作"
        A[上传PDF<br>含参数 use_parallel, chunk_size...]
    end

    subgraph "API网关 (views.py)"
        B[POST /api/uploaded-files/]
    end

    subgraph "服务层 (services.py)"
        C[UploadService<br>创建UploadedFile, ProcessingTask]
        D[Task Dispatcher<br>dispatch_pdf_processing]
    end

    subgraph "任务队列 (Celery)"
        E{串行模式?}
        F[process_pdf_serial_task]
        G[process_pdf_parallel_task]
    end
    
    subgraph "并行处理"
        H[process_pdf_chunk_task]
    end

    A --> B;
    B --> C;
    C --> D;
    D --> E;
    E -- 是 --> G;
    E -- 否 --> F;
    G -- 创建子任务 --> H;

    F -- "循环执行[核心处理单元]" --> I((数据库));
    H -- "在其块内循环执行[核心处理单元]" --> I;
```

### 2.3 两种处理模式详述

- **串行模式**: 单个Celery任务 (`process_pdf_serial_task`) 循环遍历所有识别出的独立档案，并在循环体内完整地执行上述"核心处理单元"四步流程。
- **并行模式**: 一个协调任务将大的PDF拆分成多个"块"，为每个块启动一个并行的Worker (`process_pdf_chunk_task`)。每个Worker在自己的"块"内执行与串行模式相同的逻辑。

## 3. 前端界面设计与功能需求

前端界面将采用**仪表盘驱动的专用台账页面**架构，为用户提供从宏观概览到微观管理的无缝体验。

### 3.1 档案仪表盘 (`/archive/page.tsx`) - 新增

- **UI**: 作为档案归档模块的入口，此页面是一个信息聚合的仪表盘。
- **布局**: 采用卡片式布局，展示核心统计数据和快速访问链接。
- **核心功能**:
  - **顶部统计卡片**: 实时展示"今日/本月归档量"、"今日/本月归档盒数"、"处理异常数"和"归档完成率"等关键指标。
  - **快速访问卡片**: 提供两个醒目的入口：
    - **"上传PDF文件"**: 引导用户至 `/archive/upload` 页面。
    - **"PDF导入台账"**: 引导用户至 `/archive/ledger` 页面，即PDF处理任务的监控中心。
  - **最近活动与系统状态**: 集成 `RecentArchiveActivities` 和 `SystemStatus` 组件，提供动态信息流。

### 3.2 PDF导入台账页面 (`/archive/ledger/page.tsx`) - 核心监控中心

此页面是PDF处理任务的监控和管理中心，围绕一个功能强大的AG Grid表格构建。

- **UI**: 采用 `TablePageLayout` 标准化页面布局，集成了统计、过滤和核心表格。
- **布局**:
  - **顶部统计卡片**: 以`StatusCardGroup`组件展示"待处理"、"处理中"、"已完成"、"失败"的任务总数。
  - **状态过滤标签页**: 提供"全部"、"待处理"、"处理中"、"已完成"、"失败"五个标签页，用于快速筛选下方表格中的任务。
  - **核心表格**: 使用 `ProcessingTaskGrid` 组件（基于AG Grid实现）来展示任务列表。

- **表格功能 (`ProcessingTaskGrid.tsx`)**:
  - **智能视图模式**:
    - **"已完成"视图**: 采用**Master-Detail模式**。Master行展示批次信息，展开后通过API查询并显示该批次最终成功创建的所有`ArchiveRecord`条目列表。
    - **其他视图 (处理中/失败等)**: 采用**扁平化表格**。每一行直接代表一个`ProcessingTask`。
  - **动态列**: 根据所选的状态标签页，动态显示或隐藏"进度"、"失败原因"等相关列。
  - **丰富单元格渲染**: 使用自定义组件（如`Badge`, `Progress`）优化状态和数据的显示效果。

### 3.3 核心交互功能

- **档案和报告链接**: 在"已完成"视图的**Detail行**中，为每个`ArchiveRecord`提供指向档案PDF和报告PDF的下载链接。
- **重试与删除**: 在表格的操作列中，为失败的任务提供"重试"按钮，并为所有任务预留了"删除"按钮的交互位置。
- **处理状态跟踪**: 实时显示处理进度和错误信息。

## 4. 最终数据模型定义

基于现有实现，在原有模型基础上进行完善，**不进行大幅重构**：

- **`ArchiveRecord`**:
  - `archive_status`: `CharField(max_length=2000, null=True, blank=True)`。**保持现有实现**，无枚举限制，允许灵活写入。PDF流程在最终入库时写入`'已归档'`。
  - `source_file`: `ForeignKey` to `UploadedFile`。**已实现**。
  - `archive_url`, `report_url`: `URLField`。**已实现**。

- **`UploadedFile`**:
  - **需要添加** `status`: `CharField` with choices (`active`, `deleted`)。**当前缺失此字段**。
  - 其他字段保持现有实现不变。

- **`ProcessingTask`**:
  - `status`: `CharField` with choices。**已实现**，保持现有的STATUS_CHOICES不变。
  - 其他字段保持现有实现不变。

- **`PDFChunkTask`**: **已存在且实现完整**。
  - `status`: `CharField` with choices (`pending`, `processing`, `completed`, `failed`)。**已正确实现**。
  - 保持现有实现不变。

- **`ReportSplittingTask`**: **此模型被废弃和删除**。

## 5. 服务层设计

**⚠️ 详细设计请参考**: [ReportSplittingService_Integration_Design.md](./ReportSplittingService_Integration_Design.md)

### 5.1 核心服务组件

- **`ReportRecognitionService`**: 负责CMA章识别和报告页面范围确定
  - 核心方法: `identify_report_ranges(pdf_path, parts_info)`
  - 职责: 在档案页面范围内检测CMA章位置，生成report_page_range

- **`ReportSplittingService`**: 负责按页面范围物理分割报告PDF
  - 核心方法: `split_report_by_page_range(source_pdf_path, output_report_path, report_page_range)`
  - 职责: 专注PDF物理分割，不负责识别逻辑

- **`FileStorageService`**: 负责文件归档和URL生成
  - 核心方法: `archive_single_archive_report_pdf(unified_number, archive_temp_path, report_temp_path)`
  - 删除方法: ~~`archive_single_archive_pdf`~~ (已废弃)

- **`RecordUpdateService`**: 负责数据库记录更新
  - **核心方法**: `update_archive_record(unified_number, archive_file_path, report_file_path, task_id)`
  - **职责**: 更新Archive记录，支持档案和报告路径，通过task_id关联原始PDF文件

### 5.2 参数清理

- ✅ **已删除**: `assigned_box_number`参数在所有函数中
- ✅ **简化**: 采用直接参数传递，避免复杂JSON结构
- ✅ **追溯性**: 通过task_id直接关联原始PDF文件

## 6. API 最终设计

- **`POST /api/archive-processing/uploaded-files/`**: **通用文件上传API**。
  - **实现**: 由`UploadedFileViewSet`的`create`动作提供。
  - **替代**: 此路由已替代旧的`POST /api/uploaded-files/`和独立的`PDFUploadView`。

- **`GET /api/archive-processing/processing-tasks/`**: **统一的任务列表获取API**。用于"处理中"、"处理失败"和"已完成"视图的Master行。
  - **URL参数**: 通过`?status=...`来筛选。
    - `?status=processing`: 获取"处理中"列表 (后端逻辑应包含`queued`和`processing`两种状态)。
    - `?status=failed`: 获取"处理失败"列表（后端逻辑: `file__status='active', status='failed'`）。
    - `?status=completed`: 获取"已完成"列表。
  - **返回**: 包含`ProcessingTask`及其关联的`UploadedFile`信息的扁平化列表。

- **`GET /api/archive-processing/processing-tasks/{task_id}/records/`**: **"已完成"视图的Detail行API**。
  - **功能**: 获取指定已完成任务所最终入库的所有`ArchiveRecord`。

- **`POST /api/archive-processing/uploaded-files/{old_file_id}/re-upload/`**: **健壮的重新上传API**。
  - **实现**: 由`UploadedFileViewSet`的`re_upload`自定义动作提供。
  - **后端逻辑**:
    - **1. 数据库事务**: 保证数据一致性，将以下操作捆绑执行。
    - **2. 标记旧文件**: 将`old_file_id`对应的`UploadedFile.status`置为`deleted`。
    - **3. 创建新记录**: 为新上传的文件创建一个全新的`UploadedFile`和`ProcessingTask`记录。
    - **4. 周期性清理**: **不再**依赖即时触发的异步任务。一个独立的、周期性的"垃圾回收"任务会扫描所有`status='deleted'`的记录，并安全地删除其物理文件，然后将记录状态更新为`purged`。

- **`POST /api/archive-processing/processing-tasks/{task_id}/retry/`**: **统一的智能重试API**。
  - **实现**: 由`ProcessingTaskViewSet`的`retry`自定义动作提供。
  - **后端逻辑**: **不创建新记录**。找到对应的`ProcessingTask`，检查其关联文件的`status`是否为`active`，然后将其状态从`failed`重置为`queued`。

## 7. 实施后的系统变化

### 7.1 处理流程优化

- **强制业务规则**: 系统现在强制要求每个档案都包含报告
- **职责分离**: 报告识别和分割功能独立，便于维护和测试
- **错误处理**: 多层清理机制确保无文件泄漏
- **数据追溯**: 通过task_id可以追溯到原始PDF文件

### 7.2 接口简化

- **参数清理**: 移除了assigned_box_number等冗余参数
- **函数签名明确**: 直接参数传递，避免复杂数据结构
- **两步处理**: 归档和数据库更新分离，提高可靠性

## 8. 分层实施计划

### 8.1 第一阶段: 数据层与清理 ✅ **已完成**

- **任务 1.1**: **完善模型字段** ✅ **已完成**。
  - 1.1.1: **`archive_processing/models.py`**: ✅ **已完成**
    - ✅ **已添加** `UploadedFile.status` 字段：`CharField(max_length=20, choices=[('active', '活跃'), ('deleted', '已删除')], default='active')`
    - ✅ **已确认** `ProcessingTask.status` 字段已正确实现（已存在，无需修改）
    - ✅ **已确认** `PDFChunkTask.status` 字段已正确实现（已存在，无需修改）
    - ✅ **已删除** `ReportSplittingTask` 模型（通过迁移自动删除）
  - 1.1.2: **`archive_records/models.py`**: ✅ **已确认**
    - ✅ **已确认** `ArchiveRecord.archive_status` 字段保持现有实现（已存在，无需修改）
    - ✅ **已确认** `ArchiveRecord.source_file` 外键已正确实现（已存在，无需修改）
    - ✅ **已确认** `ArchiveRecord.archive_url` 和 `report_url` 字段已存在（已存在，无需修改）

- **任务 1.2**: **清理服务层导入错误** ✅ **已完成**。
  - 1.2.1: ✅ **已确认** 无导入错误，所有相关文件导入正常

- **任务 1.3**: **执行数据库迁移** ✅ **已完成**。
  - 1.3.1: ✅ **已完成** `docker-compose exec web python manage.py makemigrations archive_processing archive_records`
  - 1.3.2: ✅ **已完成** `docker-compose exec web python manage.py migrate`
  - 1.3.3: ✅ **已验证** `docker-compose exec web python manage.py check` 无错误

### 8.2 第二阶段: 服务层与任务层重构 ✅ **已完成**

- **任务 2.1**: **创建报告分割相关服务** ✅ **已完成**
  - 2.1.1: ✅ **已创建** `ReportRecognitionService` - 负责CMA章识别和报告页面范围确定
  - 2.1.2: ✅ **已简化** `ReportSplittingService` - 专注PDF物理分割，移除复杂识别逻辑
  
- **任务 2.2**: **重构核心服务接口** ✅ **已完成**
  - 2.2.1: ✅ **已重构** `FileStorageService.archive_single_archive_report_pdf()` - 同时归档档案和报告
  - 2.2.2: ✅ **已更新** `update_archive_record()` - 支持档案和报告路径，通过task_id关联
  - 2.2.3: ✅ **已删除** 废弃的`archive_single_archive_pdf()`方法
  
- **任务 2.3**: **重构核心处理流程** ✅ **已完成**
  - 2.3.1: ✅ **已扩展** `validate_all_parts_processable()` - 支持档案分割+报告分割的三步验证
  - 2.3.2: ✅ **已集成** 报告识别服务到串行和并行处理流程
  - 2.3.3: ✅ **已实现** 两步处理流程：归档（档案+报告）→ 数据库更新
  - 2.3.4: ✅ **已清理** `assigned_box_number`参数和向后兼容代码

### 8.3 第三阶段: API与连带影响修复

- **任务 3.1**: **实现新的`ProcessingTask` API** (`GET /api/processing-tasks/`)。
- **任务 3.2**: **修复"在位编辑"API** (`PATCH /api/archive-records/{record_id}/`)。
- **任务 3.3**: **实现`re-upload`和`retry` API**。

### 8.4 第四阶段: 前端界面开发

- **任务 4.1**: **开发PDF处理监控页面**。
  - 4.1.1: 实现"处理中"视图的扁平化AG Grid表格。
  - 4.1.2: 实现"处理失败"视图的扁平化AG Grid表格。
  - 4.1.3: 实现"已完成"视图的Master-Detail模式AG Grid表格。
- **任务 4.2**: **集成核心操作功能**。
  - 4.2.1: 实现重试和重新上传按钮。
  - 4.2.2: 实现档案盒号在位编辑功能。
  - 4.2.3: 实现档案和报告PDF链接功能。

---
*文档版本 V-ULTIMATE-Aligned (与ReportSplittingService_Integration_Design.md保持一致)*  
*更新日期: 2025-06-29*  
*变更: 基于re-upload API的最新实现，更新了数据模型和API设计章节，增加了purged状态和周期性清理的描述。*
*变更: 2025-07-29 - 根据前端的实际UI实现，重构了第3节"前端界面设计与功能需求"，以准确反映"仪表盘+专用台账页面"的架构。*
