# PDF印章检测与报告分割方案 (最终版)

## 1. 核心规则与挑战

### 1.1 最终黄金规则

经过多轮深入探讨，最终确定以下唯一核心规则：

- **一份报告，多个MA章**: 整个PDF档案只包含 **一份** 目标报告。这份报告中可能有多页盖有MA章。
- **起始页定义**: 该报告的起始页，**必定是所有MA章页面中，页码最小的那一页**。

### 1.2 方案推论

基于上述黄金规则，我们可以得出一个至关重要的推论：
**当我们从PDF的第1页开始顺序向后查找时，我们遇到的第一个含有MA章的页面，就是报告的起始页。**
这使得我们的方案可以被极致简化。

### 1.3 关键特征与干扰项

- **MA章**: **唯一、绝对的核心特征**。
- **其他所有印章**: **统一视为可忽略的干扰项**。无需任何特殊处理，特征匹配算法会自动过滤。

### 1.4 技术挑战与环境约束

- **印章覆盖文字**: MA章大概率会覆盖在文字之上。
- **部署环境**: 服务器 **没有GPU**，所有计算必须在CPU上高效完成。

## 2. 最终检测流程

我们采用一套不能再简化的线性检测流程，它最高效且完全符合业务规则。

### 阶段一：从前向后，顺序检测

- 从PDF的 **第1页** 开始，一页一页地顺序向后检测。
- 在每一页上，执行我们标准、可靠的MA章识别模块：
    1. **颜色筛选 (粗筛)**: 快速提取页面上所有的红色区域作为"候选区"。这是保证性能的关键。
    2. **特征匹配 (精筛)**: 对每一个"候选区"的原始图像，使用 **SIFT/ORB** 算法与干净的MA章模板进行特征匹配。

### 阶段二：找到即停，立即决策

- **一旦在任何页面上成功匹配到了MA章，立即停止所有后续的检测工作。**
- 该页面即被确认为最终的 **目标页**。
- 如果完整遍历所有页面后仍未找到MA章，则报告"未找到"。

## 3. 关键技术细节与问答

### Q1: 模板样本如何准备？

**答：** 需要 **3-5张** 独立的、清晰的、纯粹的 **`[MA]`图形** 截图。

- **只要`[MA]`图形**: 必须排除下方的认证编号和有效期。
- **必须"干净"**: 模板本身不能覆盖任何文字。

### Q2: 为什么"颜色筛选"作为第一步仍然必要？

**答：这是方案的性能基石。** 如果直接在整页上进行特征匹配，如同"大海捞针"，在CPU上将耗时数分钟。颜色筛选能在毫秒级内将搜索范围缩小99.9%，让方案变得高效可行。

### Q3: 为什么不需要过滤人名章等其他干扰项？

**答：因为MA章特征匹配算法本身就是最完美的过滤器。** 一个"人名章"的特征与"MA章"的特征截然不同，算法在匹配时会自然给出"不匹配"的结果，无需我们做任何额外工作。

### Q4: 印章覆盖在文字上，方案能应对吗？

**答：可以。** 这正是我们选择 **特征匹配（SIFT/ORB）** 的核心原因。该算法能有效忽略背景文字干扰，通过匹配部分未被遮挡的关键特征点完成识别。

### Q5: 匹配前需要对图像做颜色清理吗？

**答：不需要，甚至应该避免。** 强行清理会破坏印章本身的图形特征，属于好心办坏事。让算法直接处理包含干扰的原始图像效果最好。
