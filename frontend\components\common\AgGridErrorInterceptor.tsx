"use client"

import { useEffect } from 'react'

interface AgGridErrorInterceptorProps {
  // 可选：是否输出拦截器激活消息
  showActivationMessage?: boolean
  // 可选：是否启用拦截器，优先级高于环境变量
  enabled?: boolean
}

/**
 * AG Grid 许可证错误拦截器组件
 * 拦截控制台中的AG Grid许可证相关错误消息
 * 
 * 环境变量:
 * - NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT: 设置为 "false" 可禁用拦截
 * - NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG: 设置为 "true" 可在拦截时输出调试信息
 */
export function AgGridErrorInterceptor({ 
  showActivationMessage = false,
  enabled
}: AgGridErrorInterceptorProps) {
  useEffect(() => {
    // 检查环境变量是否禁用了拦截器
    const isEnabledByEnv = process.env.NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT !== 'false'
    // props的enabled参数优先级高于环境变量
    const shouldEnable = enabled !== undefined ? enabled : isEnabledByEnv
    
    // 如果明确禁用，则不激活拦截器
    if (!shouldEnable) return
    
    // IIFE (Immediately Invoked Function Expression) to avoid polluting the global scope
    (function() {
      // 保存原始的 console.error 函数
      const originalConsoleError = console.error

      // 定义AG Grid许可证错误相关的关键词
      const agGridErrorKeywords = [
        "AG Grid Enterprise License",
        "Invalid License Key",
        "ag-grid.com/licensing",
        "Your license key is not valid",
        "*****************************", // 添加星号行，AG Grid常用于错误格式化
        "License Key",
        "validateLicense",
        "centerPadAndOutput", // 拦截格式化函数
        "padAndOutput" // 拦截格式化函数
      ]

      // 重写 console.error 函数
      console.error = function(...args) {
        // 将所有参数转换为字符串，以便进行检查
        const message = args.map(arg => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg)
            } catch (e) {
              return String(arg)
            }
          }
          return String(arg)
        }).join(' ')

        // 检查消息中是否包含任何AG Grid许可证错误关键词
        const isAgGridLicenseError = agGridErrorKeywords.some(keyword => message.includes(keyword))

        // 如果不是AG Grid许可证错误，则调用原始的 console.error
        if (!isAgGridLicenseError) {
          originalConsoleError.apply(console, args)
        } 
        // 如果是AG Grid错误且debug模式开启，则输出提示
        else if (process.env.NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG === 'true') {
          originalConsoleError("拦截到AG Grid许可证错误，已被屏蔽")
        }
      }
      
      // 如果配置了显示激活消息，则输出
      if (showActivationMessage && process.env.NODE_ENV === 'development') {
        originalConsoleError("AG Grid 许可证错误拦截器已激活")
      }
    })()
  }, [showActivationMessage, enabled]) // 仅在依赖项变化时重新运行

  // 这是一个纯功能组件，不需要渲染任何内容
  return null
}

export default AgGridErrorInterceptor 