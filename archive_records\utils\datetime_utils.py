import os
import logging
from datetime import datetime, timezone as dt_timezone, timedelta
from typing import Any, Optional, Union
import pandas as pd
import pytz
from django.utils import timezone

logger = logging.getLogger(__name__)

class TimezoneHandler:
    """
    现代化的时区处理工具类
    
    设计原则:
    1. Excel时间始终视为本地时间 (Asia/Shanghai, UTC+8)
    2. 数据库时间始终以UTC存储
    3. 所有转换都是显式的，不依赖系统默认设置
    4. 提供清晰的错误处理和日志记录
    """
    
    # 常量定义 - 使用固定偏移避免历史时区问题
    LOCAL_TIMEZONE = dt_timezone(timedelta(hours=8))  # 固定UTC+8，避免pytz历史时区问题
    UTC_TIMEZONE = dt_timezone.utc
    
    # 默认比较容忍度(秒)
    DEFAULT_TOLERANCE_SECONDS = 1.0
    
    @classmethod
    def parse_excel_datetime(cls, excel_value: Any) -> Optional[datetime]:
        """
        解析Excel时间值为本地时区感知的datetime对象
        
        Args:
            excel_value: Excel中的时间值(字符串、数字或datetime对象)
            
        Returns:
            本地时区感知的datetime对象，如果解析失败则返回None
            
        Raises:
            ValueError: 当时间值格式无效时
        """
        if excel_value is None or pd.isna(excel_value):
            return None
            
        try:
            # 使用pandas统一解析各种时间格式
            parsed_dt = pd.to_datetime(excel_value)
            
            # 转换为Python datetime对象
            if hasattr(parsed_dt, 'to_pydatetime'):
                python_dt = parsed_dt.to_pydatetime()
            else:
                python_dt = parsed_dt
            
            # 移除任何现有的时区信息(pandas可能错误添加)
            if python_dt.tzinfo is not None:
                logger.debug(f"移除pandas添加的时区信息: {python_dt.tzinfo}")
                python_dt = python_dt.replace(tzinfo=None)
            
            # 按本地时区标记 - 使用replace而不是localize避免历史时区问题
            local_aware_dt = python_dt.replace(tzinfo=cls.LOCAL_TIMEZONE)
            
            logger.debug(f"Excel时间解析: {excel_value} -> {local_aware_dt}")
            return local_aware_dt
            
        except Exception as e:
            logger.error(f"Excel时间解析失败: {excel_value}, 错误: {e}")
            raise ValueError(f"无效的时间格式: {excel_value}")
    
    @classmethod
    def convert_to_utc(cls, local_dt: datetime) -> datetime:
        """
        将本地时区的datetime转换为UTC时间
        
        Args:
            local_dt: 本地时区感知的datetime对象
            
        Returns:
            UTC时区的datetime对象
        """
        if local_dt is None:
            return None
            
        if local_dt.tzinfo is None:
            # 如果是naive时间，假设为本地时间
            local_dt = local_dt.replace(tzinfo=cls.LOCAL_TIMEZONE)
        
        utc_dt = local_dt.astimezone(cls.UTC_TIMEZONE)
        logger.debug(f"时区转换: {local_dt} -> {utc_dt}")
        return utc_dt
    
    @classmethod
    def convert_to_local(cls, utc_dt: datetime) -> datetime:
        """
        将UTC时间转换为本地时区时间
        
        Args:
            utc_dt: UTC时区的datetime对象
            
        Returns:
            本地时区感知的datetime对象
        """
        if utc_dt is None:
            return None
            
        if utc_dt.tzinfo is None:
            # 如果是naive时间，假设为UTC
            utc_dt = utc_dt.replace(tzinfo=cls.UTC_TIMEZONE)
        
        local_dt = utc_dt.astimezone(cls.LOCAL_TIMEZONE)
        logger.debug(f"UTC转本地: {utc_dt} -> {local_dt}")
        return local_dt
    
    @classmethod
    def compare_datetime_values(
        cls, 
        excel_value: Any, 
        db_value: Any, 
        field_name: str = "unknown",
        tolerance_seconds: Optional[float] = None
    ) -> bool:
        """
        比较Excel时间值与数据库时间值
        
        处理逻辑:
        1. Excel时间 -> 本地时区感知时间
        2. 数据库时间 -> 本地时区感知时间  
        3. 在本地时区进行比较
        
        Args:
            excel_value: Excel中的时间值
            db_value: 数据库中的时间值(UTC)
            field_name: 字段名称，用于日志
            tolerance_seconds: 比较容忍度(秒)，None表示使用默认值
            
        Returns:
            bool: 两个时间是否在容忍度内相等
        """
        # 处理空值
        if pd.isna(excel_value) and pd.isna(db_value):
            return True
        if pd.isna(excel_value) or pd.isna(db_value):
            return False
        
        # 获取容忍度
        if tolerance_seconds is None:
            tolerance_seconds = float(
                os.environ.get('DATETIME_COMPARISON_TOLERANCE_SECONDS', cls.DEFAULT_TOLERANCE_SECONDS)
            )
        
        try:
            # 解析Excel时间为本地时区感知时间
            excel_local = cls.parse_excel_datetime(excel_value)
            if excel_local is None:
                logger.warning(f"Excel时间解析失败: {excel_value}")
                return False
            
            # 处理数据库时间
            db_dt = pd.to_datetime(db_value)
            if hasattr(db_dt, 'to_pydatetime'):
                db_dt = db_dt.to_pydatetime()
            
            # 确保数据库时间正确标记为UTC，然后转换为本地时间
            if db_dt.tzinfo is None:
                # Naive时间，假设为UTC
                db_utc = db_dt.replace(tzinfo=cls.UTC_TIMEZONE)
            else:
                # 已有时区信息，转换为UTC
                db_utc = db_dt.astimezone(cls.UTC_TIMEZONE)
            
            # 转换数据库时间为本地时间进行比较
            db_local = cls.convert_to_local(db_utc)
            
            # 计算时间差
            time_diff_seconds = abs((excel_local - db_local).total_seconds())
            is_equal = time_diff_seconds <= tolerance_seconds
            
            # 详细日志
            logger.info(f"[时间比较] 字段: {field_name}")
            logger.info(f"[时间比较] Excel原始: {excel_value}")
            logger.info(f"[时间比较] Excel本地: {excel_local}")
            logger.info(f"[时间比较] 数据库原始: {db_value}")
            logger.info(f"[时间比较] 数据库UTC: {db_utc}")
            logger.info(f"[时间比较] 数据库本地: {db_local}")
            logger.info(f"[时间比较] 时间差: {time_diff_seconds:.2f}秒")
            logger.info(f"[时间比较] 容忍度: {tolerance_seconds}秒")
            logger.info(f"[时间比较] 结果: {'相等' if is_equal else '不相等'}")
            
            return is_equal
            
        except Exception as e:
            logger.error(f"时间比较失败 - 字段: {field_name}, Excel: {excel_value}, DB: {db_value}, 错误: {e}")
            # 发生错误时，保守地返回False
            return False

# 向后兼容的别名
class DateTimeComparisonUtil:
    """向后兼容的工具类别名"""
    
    @staticmethod
    def preprocess_excel_datetime(excel_value: Any) -> Any:
        """预处理Excel时间值"""
        return TimezoneHandler.parse_excel_datetime(excel_value)
    
    @staticmethod
    def compare_datetime_values(excel_value: Any, db_value: Any, field_name: str = "unknown") -> bool:
        """比较时间值"""
        return TimezoneHandler.compare_datetime_values(excel_value, db_value, field_name) 