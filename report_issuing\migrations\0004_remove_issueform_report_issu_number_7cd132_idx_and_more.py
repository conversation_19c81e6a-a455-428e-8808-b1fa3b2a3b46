# Generated by Django 5.1.8 on 2025-06-09 02:06

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report_issuing', '0003_remove_issueformitem_issue_type_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='issueform',
            name='report_issu_number_7cd132_idx',
        ),
        migrations.RenameField(
            model_name='issueform',
            old_name='number',
            new_name='issue_number',
        ),
        migrations.AddIndex(
            model_name='issueform',
            index=models.Index(fields=['issue_number'], name='report_issu_issue_n_878033_idx'),
        ),
    ]
