# Operation Document: 拦截AG Grid许可证控制台错误

## 📋 Change Summary

**Purpose**: 为用户提供一个JavaScript代码片段，用于在开发环境中拦截并屏蔽AG Grid Enterprise因许可证无效而产生的`console.error`消息，以减少调试干扰。
**Scope**: 该方案影响浏览器全局的`console.error`行为，但仅在开发环境下建议使用。
**Associated**: 用户请求。

## 🔧 Operation Steps

### 📊 OP-001: 分析需求

**Precondition**: 用户在Next.js项目中使用AG Grid Enterprise，并在开发控制台中看到大量许可证无效的错误。
**Operation**: 理解用户希望在开发阶段隐藏这些特定错误，以便更清晰地调试其他问题。
**Postcondition**: 明确了解决方案是提供一个JavaScript脚本来覆盖`console.error`。

### ✏️ OP-002: 设计并提供JavaScript代码片段

**Precondition**: 已理解需求。
**Operation**:

1. 编写一个JavaScript IIFE (Immediately Invoked Function Expression) 以避免污染全局作用域。
2. 在该IIFE中，保存对原始`console.error`的引用。
3. 定义一个包含AG Grid许可证错误特征关键词的数组。
4. 重写`console.error`函数：
    * 新函数接收所有参数。
    * 将参数转换为单个字符串消息。
    * 检查该消息是否包含预定义的AG Grid许可证错误关键词。
    * 如果包含，则不执行任何操作（错误被拦截）。
    * 如果不包含，则使用保存的原始`console.error`引用来打印错误。
5. 建议用户将此代码片段放置在Next.js应用客户端代码的早期加载位置（如`_app.tsx`或全局`layout.tsx`的`useEffect`中），确保在AG Grid初始化前执行。
6. 添加了在开发模式下打印一条消息以确认拦截器已激活的建议。
**Postcondition**: 用户获得了一个可用的JavaScript代码片段，并得到了如何在Next.js项目中使用的指导。

### 🧪 OP-003: 解释和警告

**Precondition**: 代码片段已提供。
**Operation**: 提醒用户该方法仅适用于开发环境，不应在生产环境中使用，以免屏蔽掉其他重要错误。强调了在开发模式下才激活拦截器的重要性。
**Postcondition**: 用户理解了此方法的适用场景和潜在风险。

## 📝 Change Details

### CH-001: 提供JavaScript错误拦截脚本

**File**: 不直接修改项目文件，提供的是一个可由用户添加到其项目中的代码片段。
**Before**:
N/A (用户项目中没有此拦截逻辑)

**After (提供的代码片段)**:

```javascript
// IIFE (Immediately Invoked Function Expression) to avoid polluting the global scope
(function() {
  // 保存原始的 console.error 函数
  const originalConsoleError = console.error;

  // 定义AG Grid许可证错误相关的关键词
  const agGridErrorKeywords = [
    "AG Grid Enterprise License",
    "Invalid License Key",
    "ag-grid.com/licensing"
  ];

  // 重写 console.error 函数
  console.error = function(...args) {
    // 将所有参数转换为字符串，以便进行检查
    const message = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          return JSON.stringify(arg);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    }).join(' ');

    // 检查消息中是否包含任何AG Grid许可证错误关键词
    const isAgGridLicenseError = agGridErrorKeywords.some(keyword => message.includes(keyword));

    // 如果不是AG Grid许可证错误，则调用原始的 console.error
    if (!isAgGridLicenseError) {
      originalConsoleError.apply(console, args);
    }
    // 如果是AG Grid许可证错误，则不执行任何操作 (即拦截掉)
  };

  // （可选）可以加一条日志说明拦截器已激活，最好在开发模式下
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
    console.log("AG Grid 许可证错误拦截器已激活。");
  }
})();
```

**Rationale**: 用户要求屏蔽特定的控制台错误以改善开发体验。
**Potential Impact**: 如果关键词匹配过于宽泛或错误地在生产环境中使用，可能会隐藏掉非AG Grid许可证相关的、但包含相似关键词的重要错误。

## ✅ Verification Results

**Method**: 用户将在其开发环境中集成此脚本并观察`console.error`的行为。
**Results**: 预期AG Grid许可证相关的错误将不再显示在控制台中。
**Problems**: 无。
**Solutions**: 无。

## 🤖 Priority Levels

- **P0**: 解决用户提出的问题。
