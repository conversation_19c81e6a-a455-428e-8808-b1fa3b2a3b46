import { useState, useCallback, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import excelImportService from '@/services/domain/records/import/excel-import-service';
import { useActiveSessionNotification } from '@/contexts/domain/records/import/ExcelImportSessionGuard';
import type {
  ActiveImportSessionResponseData,
  SessionInfoData,
  AnalysisProgressData,
  ExcelAnalysisResultPayload,
  ConflictResolution,
  ImportReportData,
  ExcelAnalysisStartupInfoData,
  CancelImportSuccessData,
  ConfirmImportApiResponse,
  ExcelAnalysisStats,
  UserDecisionStats,
} from '@/services/domain/records/import/excel-import-service';
import { useToast } from '@/components/ui/use-toast';



// Corresponds to backend ImportSessionStatus enum
export enum ImportSessionStatusEnum {
  SELECT = "select",
  UPLOAD = "upload",
  ANALYSIS_START = "analysis_start",
  ANALYSIS_IN_PROGRESS = "analyzing",
  ANALYSIS_COMPLETE = "analyzed",
  CONFLICT_RESOLUTION_STARTED = "conflict_resolution_started",
  CONFLICT_RESOLUTION_IN_PROGRESS = "conflict_resolution_in_progress", 
  CONFLICT_RESOLUTION_PENDING = "conflict_resolution_pending",
  CONFLICT_RESOLUTION_COMPLETED = "conflict_resolution_completed",
  IMPORT_QUEUED = "queued",
  IMPORT_START = "import_start",
  IMPORT_IN_PROGRESS = "importing",
  IMPORT_COMPLETED_SUCCESSFULLY = "completed_successfully",
  IMPORT_COMPLETED_WITH_ERRORS = "completed_with_errors",
  FINALIZED = "finalized",
  CANCELLED = "cancelled",
  ERROR = "error",
}

// Key fields to compare if session ID is the same, to decide if a re-render is needed.
const KEY_FIELDS_FOR_SESSION_COMPARISON: (keyof SessionInfoData)[] = [
  'status',
  'progress',
  'fileName',
  'recordCount',
  'conflictCount',
  'errorMessage',
  'updatedAt',
  'processingUser',
  'lastActivity',
];

export interface UseExcelImportSessionReturn {
  activeSessionInfo: SessionInfoData | null;
  isLoadingSession: boolean;
  errorLoadingSession: string | null;
  isSubmitting: boolean;
  finalImportResults: ImportReportData | null;
  acknowledgeResults: (sessionId: string) => Promise<boolean>;

  // 并发控制和会话安全相关的状态标识
  canCurrentUserTakeoverSession: boolean | null; // 当前用户是否可以安全接管会话（并发控制）

  fetchSystemActiveSession: (isSilent?: boolean) => Promise<void>;
  startNewImport: (
    file: File,
    sheetName?: string | number,
    onUploadProgress?: (progress: number) => void
  ) => Promise<string | null>;
  cancelCurrentImport: (sessionId: string) => Promise<boolean>;
  getAnalysisResult: (sessionId: string) => Promise<ExcelAnalysisResultPayload | null>;
  confirmImport: (
    sessionId: string, 
    resolutions: ConflictResolution[],
    analysisStats?: ExcelAnalysisStats,
    userDecisionStats?: UserDecisionStats
  ) => Promise<{ success: boolean; message?: string } | null>;
  // CHANGE: [2025-06-01] 移除takeoverImport，添加新的冲突处理API函数
  beginActiveConflictProcessing: (sessionId: string) => Promise<boolean>;
  pendActiveConflictProcessing: (sessionId: string) => Promise<boolean>;
  resetImportState: () => void;
}

// Define polling intervals
const NORMAL_POLLING_INTERVAL = 2500; // ms
const FAST_POLLING_INTERVAL = 1000;   // ms, when nearing completion

// CHANGE: [2025-05-16] Heartbeat interval configuration
const HEARTBEAT_INTERVAL_MS = 60 * 1000; // 60 seconds

/**
 * @hook useExcelImportSession
 * Manages the state and operations for the Excel import process, including two-stage import (analyze then confirm),
 * session management, conflict resolution, progress polling, and heartbeat for active sessions.
 *
 * @returns {UseExcelImportSessionReturn} An object containing session information, user permissions,
 * loading/error states, submission status, and functions to interact with the import session.
 */
export function useExcelImportSession(): UseExcelImportSessionReturn {
  const { toast } = useToast(); // CHANGE: 初始化 toast
  const { data: session, status: sessionStatus } = useSession(); // CHANGE: 使用 NextAuth session
  const { showActiveSessionModal } = useActiveSessionNotification(); // 统一的模态框管理

  const [activeSessionInfo, setActiveSessionInfo] = useState<SessionInfoData | null>(null);
  const [finalImportResults, setFinalImportResults] = useState<ImportReportData | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState<boolean>(true);
  const [errorLoadingSession, setErrorLoadingSession] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // 并发控制和会话安全相关的状态标识
  const [canCurrentUserTakeoverSession, setCanCurrentUserTakeoverSession] = useState<boolean | null>(null);
  
  // State for specific polling types
  const [isPollingAnalysis, setIsPollingAnalysis] = useState<boolean>(false); // For ANALYSIS_IN_PROGRESS
  const [isPollingImportConfirm, setIsPollingImportConfirm] = useState<boolean>(false); // For IMPORT_QUEUED/START/IN_PROGRESS
  
  const prevActiveSessionRef = useRef<SessionInfoData | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  // CHANGE: [2025-06-14] 现代化架构：移除手动interval管理，useEffect自动处理清理
  const isMountedRef = useRef<boolean>(false);

  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activeSessionIdForHeartbeat = useRef<string | null>(null);

  const activeSessionInfoRef = useRef(activeSessionInfo);
  useEffect(() => {
    activeSessionInfoRef.current = activeSessionInfo;
  }, [activeSessionInfo]);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // CHANGE: [2025-06-14] 现代化轮询管理 - 递归轮询模式
  useEffect(() => {
    if (!isPollingAnalysis) return;
    
    console.log('[调试] useEffect启动分析轮询');
    
    let timeoutId: NodeJS.Timeout | null = null;
    
    const pollAnalysis = async () => {
      if (!isMountedRef.current || !isPollingAnalysis) return;
      
      console.log('[调试] 执行分析轮询请求');
      await fetchSystemActiveSessionRef.current?.(true);
      
      // 递归调用 - 继续下一次轮询
      if (isMountedRef.current && isPollingAnalysis) {
        timeoutId = setTimeout(pollAnalysis, NORMAL_POLLING_INTERVAL);
      }
    };
    
    // 启动第一次轮询
    timeoutId = setTimeout(pollAnalysis, NORMAL_POLLING_INTERVAL);
    
    // 清理函数 - React 确保执行
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  }, [isPollingAnalysis]);

  useEffect(() => {
    if (!isPollingImportConfirm) return;
    
    console.log('[调试] useEffect启动导入轮询');
    
    let timeoutId: NodeJS.Timeout | null = null;
    
    const pollImport = async () => {
      if (!isMountedRef.current || !isPollingImportConfirm) return;
      
      console.log('[调试] 执行导入轮询请求');
      await fetchSystemActiveSessionRef.current?.(true);
      
      // 递归调用 - 继续下一次轮询
      if (isMountedRef.current && isPollingImportConfirm) {
        // 根据进度动态调整轮询间隔
        const currentSession = activeSessionInfoRef.current;
        const interval = (currentSession?.progress || 0) > 80 ? FAST_POLLING_INTERVAL : NORMAL_POLLING_INTERVAL;
        timeoutId = setTimeout(pollImport, interval);
      }
    };
    
    // 启动第一次轮询
    const currentSession = activeSessionInfoRef.current;
    const interval = (currentSession?.progress || 0) > 80 ? FAST_POLLING_INTERVAL : NORMAL_POLLING_INTERVAL;
    timeoutId = setTimeout(pollImport, interval);
    
    // 清理函数 - React 确保执行
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  }, [isPollingImportConfirm]);

  // CHANGE: [2025-06-14] 调试：监听页面可见性变化，当页面重新变为可见时重新检查活跃会话
  // 监听页面可见性变化，当页面重新变为可见时重新检查活跃会话
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isMountedRef.current) {
        // 页面重新变为可见，重新检查活跃会话
        if (sessionStatus === 'authenticated' && fetchSystemActiveSessionRef.current) {
          fetchSystemActiveSessionRef.current(true);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [sessionStatus]);

  const fetchSystemActiveSessionRef = useRef<((isSilent?: boolean) => Promise<void>) | null>(null);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
      activeSessionIdForHeartbeat.current = null;
    }
  }, []);

  // CHANGE: [2025-06-14] 现代化架构：移除stopAllPolling，直接使用状态设置

  const startHeartbeat = useCallback((sessionId: string) => {
    stopHeartbeat();
    activeSessionIdForHeartbeat.current = sessionId;

    const sendAndScheduleHeartbeat = async () => {
      if (!isMountedRef.current || activeSessionIdForHeartbeat.current !== sessionId) {
        stopHeartbeat();
        return;
      }
      
      try {
        const result = await excelImportService.sendHeartbeat(sessionId);
        if (!isMountedRef.current || activeSessionIdForHeartbeat.current !== sessionId) {
          return stopHeartbeat();
        }

        if (!result.success) {
          console.warn(`心跳失败，会话 ${sessionId}: ${result.message}，停止心跳。`);
          stopHeartbeat();
          if (fetchSystemActiveSessionRef.current) {
            fetchSystemActiveSessionRef.current(true);
          }
        }
      } catch (error) {
        console.error(`发送心跳时出错，会话 ${sessionId}:`, error);
        stopHeartbeat();
        if (fetchSystemActiveSessionRef.current) {
          fetchSystemActiveSessionRef.current(true);
        }
      }
    };

    sendAndScheduleHeartbeat();
    heartbeatIntervalRef.current = setInterval(sendAndScheduleHeartbeat, HEARTBEAT_INTERVAL_MS);
  }, [stopHeartbeat]);

  const fetchSystemActiveSession = useCallback(async (isSilent: boolean = false): Promise<void> => {
    if (!isMountedRef.current) return;

    if (!isSilent) {
      setIsLoadingSession(true);
      setErrorLoadingSession(null);
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    try {
      const response = await excelImportService.getActiveImportSession();
      if (!isMountedRef.current) return;

      if (response.hasActiveSession && response.sessionInfo) {
        const newSessionInfo = response.sessionInfo;
        const prevSession = prevActiveSessionRef.current;

        // 检查是否需要更新状态
        // 1. 如果没有之前的会话，直接更新
        // 2. 如果会话ID不同，直接更新（新会话）
        // 3. 如果会话ID相同，但关键字段有变化，则更新
        const shouldUpdate = !prevSession || 
          prevSession.sessionId !== newSessionInfo.sessionId ||
          KEY_FIELDS_FOR_SESSION_COMPARISON.some(field => 
            prevSession[field] !== newSessionInfo[field]
          );

        if (shouldUpdate) {
          setActiveSessionInfo(newSessionInfo);
          setCanCurrentUserTakeoverSession(response.canTakeover ?? null);
          prevActiveSessionRef.current = newSessionInfo;

          // CHANGE: [2025-01-16] 改进模态框触发条件 - 添加本人会话判断，避免对自己的会话弹窗
          // 当会话ID不等于前端保存的ID时触发，但排除特定状态和本人会话
          const prevSessionId = prevSession?.sessionId;
          const hasSessionIdChanged = prevSessionId !== newSessionInfo.sessionId;
          
          // 排除的状态：选择、上传、取消、错误、finalized等不需要模态框的状态
          const excludedStatuses = [
            'select', 'upload', 'finalized', 'error', 'cancelled'
          ];
          
          // 检查是否为本人创建的会话
          const isOwnSession = session?.user?.id && newSessionInfo.createdBy?.id && 
                              String(newSessionInfo.createdBy.id) === String(session.user.id);
          
          const shouldShowModal = hasSessionIdChanged && 
                                 !excludedStatuses.includes(newSessionInfo.status) &&
                                 !isOwnSession; // 如果是本人会话，则不显示模态框
          
          if (shouldShowModal) {
            console.log('[调试] 触发模态框:', {
              hasSessionIdChanged,
              prevSessionId,
              newSessionId: newSessionInfo.sessionId,
              status: newSessionInfo.status,
              excludedStatuses,
              isOwnSession
            });
            showActiveSessionModal(newSessionInfo);
          } else if (hasSessionIdChanged && isOwnSession) {
            // 如果是本人会话，记录日志但不弹窗
            console.log('[调试] 检测到本人会话，不显示模态框:', {
              sessionId: newSessionInfo.sessionId,
              status: newSessionInfo.status,
              createdBy: newSessionInfo.createdBy?.username
            });
          }

          // 管理心跳 - 只在冲突处理阶段需要心跳
          const needsHeartbeat = newSessionInfo.status === ImportSessionStatusEnum.CONFLICT_RESOLUTION_IN_PROGRESS;
          if (newSessionInfo.sessionId && needsHeartbeat) {
            startHeartbeat(newSessionInfo.sessionId);
          } else {
            stopHeartbeat();
          }

          // CHANGE: [2025-06-14] 现代化轮询管理 - 只设置状态，实际轮询由useEffect管理
          console.log('[调试] 检查分析轮询:', {
            status: newSessionInfo.status,
            isAnalysisPollingStatus: isAnalysisPollingStatus(newSessionInfo.status),
            currentlyPolling: isPollingAnalysis
          });
          
          if (isAnalysisPollingStatus(newSessionInfo.status)) {
            if (!isPollingAnalysis) {
              console.log('[调试] 启动分析轮询');
              setIsPollingAnalysis(true);
            }
          } else {
            console.log('[调试] 停止分析轮询');
            setIsPollingAnalysis(false);
          }

          console.log('[调试] 检查导入轮询:', {
            status: newSessionInfo.status,
            isImportExecutionStatus: isImportExecutionStatus(newSessionInfo.status),
            currentlyPolling: isPollingImportConfirm
          });

          if (isImportExecutionStatus(newSessionInfo.status)) {
            if (!isPollingImportConfirm) {
              console.log('[调试] 启动导入轮询');
              setIsPollingImportConfirm(true);
            }
          } else {
            console.log('[调试] 停止导入轮询');
            setIsPollingImportConfirm(false);
          }

          // 处理最终结果
          if (newSessionInfo.status === ImportSessionStatusEnum.IMPORT_COMPLETED_SUCCESSFULLY ||
              newSessionInfo.status === ImportSessionStatusEnum.IMPORT_COMPLETED_WITH_ERRORS) {
            if (newSessionInfo.importResultsSummary) {
              setFinalImportResults(newSessionInfo.importResultsSummary);
            }
          }
        }
      } else {
        // 没有活跃会话
        setActiveSessionInfo(null);
        setCanCurrentUserTakeoverSession(null);
        setFinalImportResults(null);
        prevActiveSessionRef.current = null;
        stopHeartbeat();
        // 现代化方式：直接设置状态停止轮询
        setIsPollingAnalysis(false);
        setIsPollingImportConfirm(false);
      }
    } catch (error: any) {
      if (!isMountedRef.current || error.name === 'AbortError') return;
      
      const errorMessage = error.message || '获取活跃会话失败';
      console.error('获取活跃会话时出错:', error);
      setErrorLoadingSession(errorMessage);
    } finally {
      if (isMountedRef.current && !isSilent) {
        setIsLoadingSession(false);
      }
    }
  }, [isPollingAnalysis, isPollingImportConfirm, startHeartbeat, stopHeartbeat, showActiveSessionModal, session?.user?.id]);

  fetchSystemActiveSessionRef.current = fetchSystemActiveSession;

  const startNewImport = useCallback(
    async (
      file: File,
      sheetName?: string | number,
      onUploadProgress?: (progress: number) => void
    ): Promise<string | null> => {
      if (!isMountedRef.current) return null;

      setIsSubmitting(true);
      setErrorLoadingSession(null);

      try {
        const importOptions = sheetName !== undefined ? { sheetName: sheetName } : {};
        const result = await excelImportService.analyzeExcelFile(file, importOptions, onUploadProgress);
        
        if (!isMountedRef.current) return null;

        await fetchSystemActiveSession(true);
        return result.importSessionId;
      } catch (error: any) {
        if (!isMountedRef.current) return null;
        
        const errorMessage = error.message || '启动导入失败';
        setErrorLoadingSession(errorMessage);
        toast({ 
          title: "导入失败", 
          description: errorMessage, 
          variant: "destructive" 
        });
        return null;
      } finally {
        if (isMountedRef.current) {
          setIsSubmitting(false);
        }
      }
    },
    [fetchSystemActiveSession, toast]
  );

  const cancelCurrentImport = useCallback(
    async (sessionId: string): Promise<boolean> => {
      if (!isMountedRef.current) return false;

      setIsSubmitting(true);
      setErrorLoadingSession(null);

      try {
        const expectedSessionId = activeSessionInfoRef.current?.sessionId;
        await excelImportService.cancelImport(sessionId, expectedSessionId);
        
        if (!isMountedRef.current) return false;

        toast({ title: "操作成功", description: "导入已取消" });
        await fetchSystemActiveSession(true);
        return true;
      } catch (error: any) {
        if (!isMountedRef.current) return false;
        
        const errorMessage = error.message || '取消导入失败';
        setErrorLoadingSession(errorMessage);
        toast({ 
          title: "取消失败", 
          description: errorMessage, 
          variant: "destructive" 
        });
        return false;
      } finally {
        if (isMountedRef.current) {
          setIsSubmitting(false);
        }
      }
    },
    [fetchSystemActiveSession, toast]
  );

  const getAnalysisResult = useCallback(
    async (sessionId: string): Promise<ExcelAnalysisResultPayload | null> => {
      if (!isMountedRef.current) return null;

      try {
        return await excelImportService.getAnalysisResult(sessionId);
      } catch (error: any) {
        console.error('获取分析结果时出错:', error);
        setErrorLoadingSession(error.message || '获取分析结果失败');
        return null;
      }
    },
    []
  );

  const confirmImport = useCallback(
    async (
      sessionId: string, 
      resolutions: ConflictResolution[],
      analysisStats?: ExcelAnalysisStats, 
      userDecisionStats?: UserDecisionStats
    ): Promise<{ success: boolean; message?: string } | null> => {
      if (!isMountedRef.current) return null;

      setIsSubmitting(true);
      setErrorLoadingSession(null);
      
      // 停止心跳和轮询
      stopHeartbeat();
      setIsPollingAnalysis(false); // 现代化方式：通过状态控制轮询

      // 获取最新会话状态
      await fetchSystemActiveSession(true); 
      if (!isMountedRef.current) return null;

      const currentSession = activeSessionInfoRef.current;
      if (!currentSession || currentSession.sessionId !== sessionId) {
        setErrorLoadingSession("导入会话已失效或已更改，请刷新页面。");
        setIsSubmitting(false);
        return null;
      }

      const validStatuses = [
        ImportSessionStatusEnum.ANALYSIS_COMPLETE,
        ImportSessionStatusEnum.CONFLICT_RESOLUTION_STARTED,
        ImportSessionStatusEnum.CONFLICT_RESOLUTION_IN_PROGRESS,
        ImportSessionStatusEnum.CONFLICT_RESOLUTION_PENDING,
        ImportSessionStatusEnum.CONFLICT_RESOLUTION_COMPLETED
      ];

      if (!validStatuses.includes(currentSession.status as ImportSessionStatusEnum)) {
        setErrorLoadingSession(`会话状态 (${currentSession.status}) 不正确，无法确认导入。`);
        setIsSubmitting(false);
        return null;
      }

      try {
        const apiResponse = await excelImportService.confirmImport(
          sessionId, 
          resolutions, 
          analysisStats, 
          userDecisionStats
        );
        
        if (!isMountedRef.current) return null;

        if (apiResponse.success) {
          await fetchSystemActiveSession(true);
          setIsPollingImportConfirm(true);
          
          const successMessage = '导入任务已成功提交处理。';
          return { success: true, message: successMessage }; 
        } else {
          const errorMsg = typeof apiResponse.error === 'string' 
            ? apiResponse.error 
            : apiResponse.error?.message || '确认导入会话失败。';
          setErrorLoadingSession(errorMsg);
          return { success: false, message: errorMsg }; 
        }
      } catch (error: any) {
        if (!isMountedRef.current || error.name === 'AbortError') return null;
        
        const errorMsg = error.message || '确认导入时发生意外错误。';
        setErrorLoadingSession(errorMsg);
        return { success: false, message: errorMsg };
      } finally {
        if (isMountedRef.current) {
          setIsSubmitting(false);
        }
      }
    }, 
    [fetchSystemActiveSession, stopHeartbeat]
  );

  const acknowledgeResults = useCallback(async (sessionId: string): Promise<boolean> => {
    if (!isMountedRef.current) return false;

    setIsSubmitting(true);
    setErrorLoadingSession(null);

    try {
      const expectedSessionId = activeSessionInfoRef.current?.sessionId;
      const response = await excelImportService.acknowledgeImportResults(sessionId, expectedSessionId);
      
      if (response.success) {
        toast({ title: "操作成功", description: response.data?.message || "导入结果已确认。" });
        await fetchSystemActiveSession(true);
        return true;
      } else {
        const errorMessage = typeof response.error === 'string' 
          ? response.error 
          : (response.error as any)?.message || "确认导入结果失败。";
        setErrorLoadingSession(errorMessage);
        toast({ title: "操作失败", description: errorMessage, variant: "destructive" });
        return false;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "确认结果时发生网络错误。";
      setErrorLoadingSession(errorMessage);
      toast({ title: "网络错误", description: errorMessage, variant: "destructive" });
      return false;
    } finally {
      if (isMountedRef.current) {
        setIsSubmitting(false);
      }
    }
  }, [fetchSystemActiveSession, toast]);

  // CHANGE: [2025-05-30] 修正类型安全的状态检查函数
  // 根据正确的状态流转设计：只有 finalized 是真正的终态
  const isTerminalStatus = (status: string): boolean => {
    return status === ImportSessionStatusEnum.FINALIZED;
  };

  const isAnalysisPollingStatus = (status: string): boolean => {
    const analysisStatuses = [
      ImportSessionStatusEnum.UPLOAD,
      ImportSessionStatusEnum.ANALYSIS_START,
      ImportSessionStatusEnum.ANALYSIS_IN_PROGRESS,
      ImportSessionStatusEnum.ANALYSIS_COMPLETE
    ];
    return analysisStatuses.includes(status as ImportSessionStatusEnum);
  };

  const isImportExecutionStatus = (status: string): boolean => {
    const executionStatuses = [
      ImportSessionStatusEnum.IMPORT_QUEUED,
      ImportSessionStatusEnum.IMPORT_START,
      ImportSessionStatusEnum.IMPORT_IN_PROGRESS
    ];
    return executionStatuses.includes(status as ImportSessionStatusEnum);
  };

  // CHANGE: [2025-06-01] 移除takeoverImport，添加新的冲突处理API函数
  const beginActiveConflictProcessing = useCallback(async (sessionId: string): Promise<boolean> => {
    if (!isMountedRef.current) return false;

    setIsSubmitting(true);
    setErrorLoadingSession(null);

    try {
      const response = await excelImportService.beginActiveConflictProcessing(sessionId);
      
      if (response.success) {
        toast({ title: "操作成功", description: response.data?.message || "开始主动冲突处理。" });
        await fetchSystemActiveSession(true);
        return true;
      } else {
        const errorMessage = typeof response.error === 'string' 
          ? response.error 
          : (response.error as any)?.message || "开始主动冲突处理失败。";
        setErrorLoadingSession(errorMessage);
        toast({ title: "操作失败", description: errorMessage, variant: "destructive" });
        return false;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "开始主动冲突处理时发生网络错误。";
      setErrorLoadingSession(errorMessage);
      toast({ title: "网络错误", description: errorMessage, variant: "destructive" });
      return false;
    } finally {
      if (isMountedRef.current) {
        setIsSubmitting(false);
      }
    }
  }, [fetchSystemActiveSession, toast]);

  const pendActiveConflictProcessing = useCallback(async (sessionId: string): Promise<boolean> => {
    if (!isMountedRef.current) return false;

    setIsSubmitting(true);
    setErrorLoadingSession(null);

    try {
      const response = await excelImportService.pendActiveConflictProcessing(sessionId);
      
      if (response.success) {
        toast({ title: "操作成功", description: response.data?.message || "挂起主动冲突处理。" });
        await fetchSystemActiveSession(true);
        return true;
      } else {
        const errorMessage = typeof response.error === 'string' 
          ? response.error 
          : (response.error as any)?.message || "挂起主动冲突处理失败。";
        setErrorLoadingSession(errorMessage);
        toast({ title: "操作失败", description: errorMessage, variant: "destructive" });
        return false;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "挂起主动冲突处理时发生网络错误。";
      setErrorLoadingSession(errorMessage);
      toast({ title: "网络错误", description: errorMessage, variant: "destructive" });
      return false;
    } finally {
      if (isMountedRef.current) {
        setIsSubmitting(false);
      }
    }
  }, [fetchSystemActiveSession, toast]);

  const resetImportState = useCallback(() => {
    setActiveSessionInfo(null);
    setFinalImportResults(null);
    setErrorLoadingSession(null);
    setCanCurrentUserTakeoverSession(null);
    prevActiveSessionRef.current = null;
    stopHeartbeat();
    // 现代化清理：直接设置状态停止轮询
    setIsPollingAnalysis(false);
    setIsPollingImportConfirm(false);
  }, [stopHeartbeat]);

  // 初始化时获取活跃会话
  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      fetchSystemActiveSession();
    }
  }, [sessionStatus, fetchSystemActiveSession]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      stopHeartbeat();
      // 现代化清理：直接设置状态停止轮询
      setIsPollingAnalysis(false);
      setIsPollingImportConfirm(false);
    };
  }, [stopHeartbeat]);

  return {
    activeSessionInfo,
    isLoadingSession,
    errorLoadingSession,
    isSubmitting,
    finalImportResults,
    canCurrentUserTakeoverSession,
    fetchSystemActiveSession,
    startNewImport,
    cancelCurrentImport,
    getAnalysisResult,
    confirmImport,
    acknowledgeResults,
    beginActiveConflictProcessing,
    pendActiveConflictProcessing,
    resetImportState,
  };
} 