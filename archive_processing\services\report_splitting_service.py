"""
PDF报告分割服务（简化版）

此服务负责按指定页面范围物理分割报告PDF。
职责简化：不再负责CMA章识别，只负责按给定页面范围分割。

核心功能：
- 接收页面范围参数
- 从源PDF中提取指定页面
- 生成报告PDF文件

技术说明：
- CMA章识别功能已移至 ReportRecognitionService
- 本服务专注于PDF物理分割操作
"""

import logging
import os
import time
from typing import Optional, List, Dict, Any

from django.conf import settings

# 导入现有工具模块
from archive_processing.utils import pdf_utils

logger = logging.getLogger(__name__)


class ReportSplittingService:
    """
    报告分割服务：负责按页面范围物理分割报告PDF
    职责简化：不负责CMA章识别，只负责按给定页面范围分割
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """初始化服务，加载配置参数"""
        self.config = config or {}
        
        # 从配置或settings加载参数（简化后只需要基本配置）
        self.timeout_seconds = self.config.get('timeout_seconds', getattr(settings, 'REPORT_SPLITTING_TIMEOUT', 300))
        
        logger.info(f"ReportSplittingService 初始化完成（简化版本）")

    def split_report_by_page_range(
        self, 
        source_pdf_path: str,
        output_report_path: str,
        report_page_range: List[int]  # [起始页, 结束页]
    ) -> Dict[str, Any]:
        """
        按照指定页面范围分割报告PDF
        
        Args:
            source_pdf_path: 源PDF路径（已分割的档案PDF）
            output_report_path: 输出报告PDF路径
            report_page_range: 报告页码范围 [起始页, 结束页]（0-based，相对于档案PDF）
            
        Returns:
            {
                'success': bool,
                'report_start_page': int,     # 报告起始页
                'report_end_page': int,       # 报告结束页
                'output_path': str,           # 输出路径（如果成功）
                'processing_time': float,     # 处理耗时
                'error_message': str,         # 错误信息（如果失败）
            }
        """
        start_page, end_page = report_page_range
        start_time = time.time()
        
        try:
            # 验证输入
            if not os.path.exists(source_pdf_path):
                raise FileNotFoundError(f"源PDF文件不存在: {source_pdf_path}")
            
            # 转换为1-based页码（pdf_utils使用1-based）
            start_page_1based = start_page + 1
            end_page_1based = end_page + 1
            
            logger.info(f"开始分割报告PDF: 源文件={source_pdf_path}, "
                       f"页面范围=[{start_page_1based}, {end_page_1based}], "
                       f"输出文件={output_report_path}")
            
            # 执行PDF页面分割
            success = pdf_utils.create_temp_pdf_for_single_archive(
                source_pdf_path, 
                start_page_1based, 
                end_page_1based, 
                output_report_path
            )
            
            processing_time = time.time() - start_time
            
            if success and os.path.exists(output_report_path):
                logger.info(f"报告PDF分割成功，耗时: {processing_time:.2f}秒")
                return {
                    'success': True,
                    'report_start_page': start_page,
                    'report_end_page': end_page,
                    'output_path': output_report_path,
                    'processing_time': processing_time,
                    'error_message': None
                }
            else:
                error_msg = "PDF页面分割失败"
                logger.error(error_msg)
                return {
                    'success': False,
                    'report_start_page': None,
                    'report_end_page': None,
                    'output_path': None,
                    'processing_time': processing_time,
                    'error_message': error_msg
                }
                
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"分割过程异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'report_start_page': None,
                'report_end_page': None,
                'output_path': None,
                'processing_time': processing_time,
                'error_message': error_msg
            }
    
