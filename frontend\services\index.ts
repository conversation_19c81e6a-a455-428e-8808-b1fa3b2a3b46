// 前端服务和类型导出

// 从Excel导入服务导出类型
export {
  default as excelImportService,
  ExcelImportService,
  type ExcelImportOptions,
  type ExcelAnalysisResultPayload,
  type ConflictRecord,
  type FieldDifference,
  type ConflictResolution, 
  type AnalysisResult,
  type ExcelAnalysisStartupInfoData,
  ConflictResolutionAction,
  type ImportReportData,
  type ConfirmImportApiResponse,
  type SessionInfoData,
  type ActiveImportSessionResponseData,
  type CancelImportSuccessData,
  type AnalysisProgressData,
  type ExcelAnalysisStats,
  type UserDecisionStats,
  type DetailedErrorItem,
  type ImportDetailedReport
} from './domain/records/import/excel-import-service';

// 从档案记录服务导出类型和服务
export {
  default as archiveRecordService,
  ArchiveRecordService,
  type ArchiveRecord,
  type ArchiveRecordListItem,
  type ArchiveRecordQueryParams,
  type ArchiveRecordPaginatedResponse
} from './domain/records/archive-record-service';

// 从导入历史服务导出
export {
  default as importHistoryService,
  ImportHistoryService,
  type ImportHistoryRecord,
  type ImportHistoryDetailRecord,
  type ImportHistoryQueryParams
} from './domain/records/import-history/import-history-service';

// 添加其他服务和类型导出 