"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Eye, EyeOff, Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

const userFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: "用户名至少需要2个字符",
    })
    .max(30, {
      message: "用户名不能超过30个字符",
    }),
  displayName: z
    .string()
    .min(2, {
      message: "显示名称至少需要2个字符",
    })
    .max(30, {
      message: "显示名称不能超过30个字符",
    }),
  email: z.string().email({
    message: "请输入有效的电子邮件地址",
  }),
  password: z.string().min(8, {
    message: "密码至少需要8个字符",
  }),
  role: z.string().min(1, {
    message: "请选择角色",
  }),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
})

type UserFormValues = z.infer<typeof userFormSchema>

const defaultValues: Partial<UserFormValues> = {
  username: "",
  displayName: "",
  email: "",
  password: "",
  role: "",
  isActive: true,
  notes: "",
}

export function UserForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema) as any,
    defaultValues,
  })

  async function onSubmit(data: UserFormValues) {
    setIsSubmitting(true)

    try {
      // 这里应该是实际的API调用来创建用户
      console.log("创建用户:", data)

      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "用户创建成功",
        description: `用户 ${data.username} 已成功创建`,
      })

      // 创建成功后返回用户列表页面
      router.push("/users")
    } catch (error) {
      console.error("创建用户失败:", error)
      toast({
        title: "创建用户失败",
        description: "创建用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>用户名</FormLabel>
                <FormControl>
                  <Input placeholder="请输入用户名" {...field} />
                </FormControl>
                <FormDescription>用户登录系统时使用的唯一标识符</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="displayName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>显示名称</FormLabel>
                <FormControl>
                  <Input placeholder="请输入显示名称" {...field} />
                </FormControl>
                <FormDescription>用户在系统中显示的名称</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>电子邮箱</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="请输入电子邮箱" {...field} />
                </FormControl>
                <FormDescription>用于系统通知和密码重置</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>密码</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showPassword ? "text" : "password"} placeholder="请输入密码" {...field} />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      <span className="sr-only">{showPassword ? "隐藏密码" : "显示密码"}</span>
                    </Button>
                  </div>
                </FormControl>
                <FormDescription>密码至少需要8个字符</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>角色</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择角色" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="admin">系统管理员</SelectItem>
                    <SelectItem value="archivist">档案管理员</SelectItem>
                    <SelectItem value="reporter">报告发放员</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>决定用户在系统中的权限级别</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>账户状态</FormLabel>
                <FormDescription>启用后用户可以立即登录系统</FormDescription>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备注</FormLabel>
              <FormControl>
                <Textarea placeholder="请输入关于此用户的任何附加信息" className="resize-none" {...field} />
              </FormControl>
              <FormDescription>可选的附加信息</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.push("/users")}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            创建用户
          </Button>
        </div>
      </form>
    </Form>
  )
}
