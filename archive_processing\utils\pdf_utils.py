import logging
import os
from typing import List, Tuple, Optional
# CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 #AFM-Refactor-PDFLib
import fitz  # PyMuPDF
from ..dto.pdf_dtos import PDFPartRange

logger = logging.getLogger(__name__)

# CHANGE: [2024-08-01] 添加获取PDF页数的函数 #AFM-refactor-fix
def get_pdf_page_count(pdf_path: str) -> int:
    """获取PDF文件的总页数
    
    Args:
        pdf_path: PDF文件路径
        
    Returns:
        PDF文件的总页数
        
    Raises:
        FileNotFoundError: 如果PDF文件不存在
        Exception: 如果读取PDF文件时发生错误
    """
    try:
        doc = fitz.open(pdf_path)
        page_count = doc.page_count
        doc.close()
        return page_count
    except FileNotFoundError:
        logger.error(f"PDF文件不存在: {pdf_path}")
        raise
    except Exception as e:
        logger.error(f"读取PDF文件'{pdf_path}'页数时发生错误: {str(e)}", exc_info=True)
        raise

# CHANGE: [2024-08-15] 重写calculate_part_ranges函数以修复边界条件问题 #AFM-fix-page-boundaries
def calculate_part_ranges(split_points: List[int], total_pages: int) -> List[PDFPartRange]:
    """根据分割点（从0开始的页码）计算每个部分的页面范围（包含起始和结束页）。

    Args:
        split_points: 分割点页码列表 (0-based)。例如 [10, 25] 表示在第10页和第25页开始新的部分。
                      分割点是 *新部分的第一页*，所以分割发生在指定页之前。
        total_pages: PDF的总页数。

    Returns:
        一个包含 PDFPartRange 对象的列表，每个对象代表一个部分的起始和结束页码 (0-based, inclusive)。
    """
    if total_pages <= 0:
        logger.warning(f"无法为页数为 {total_pages} 的PDF计算部分范围")
        return []
        
    # 确保分割点是唯一的，并且按升序排序
    unique_split_points = sorted(list(set(split_points)))
    
    # 过滤掉无效的分割点（小于0或大于等于总页数）
    valid_split_points = [p for p in unique_split_points if 0 <= p < total_pages]
    
    # 确保第一页总是作为分割点
    if 0 not in valid_split_points:
        valid_split_points.insert(0, 0)
    
    # 计算每个部分的范围
    parts = []
    for i in range(len(valid_split_points)):
        start_page = valid_split_points[i]
        # 如果是最后一个分割点，结束页是最后一页，否则是下一个分割点的前一页
        end_page = total_pages - 1 if i == len(valid_split_points) - 1 else valid_split_points[i+1] - 1
        
        # 确保结束页不小于起始页（避免空部分）
        if end_page >= start_page:
            parts.append(PDFPartRange(start_page=start_page, end_page=end_page))
    
    if not parts and total_pages > 0:
        # 如果没有有效的分割点，创建包含整个文档的单个部分
        parts.append(PDFPartRange(start_page=0, end_page=total_pages - 1))
    
    logger.debug(f"计算得到的部分范围 (页数={total_pages}, 分割点={split_points}): {parts}")
    return parts


# CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 实现 PDF 页面提取 #AFM-Refactor-PDFLib
def create_temp_pdf_for_single_archive(original_pdf_path: str, start_page: int, end_page: int, output_path: str) -> bool:
    """为单个逻辑档案创建临时的 PDF 文件，包含原始 PDF 的指定页面范围。

    Args:
        original_pdf_path: 原始 PDF 文件的路径。
        start_page: 起始页码 (1-based, inclusive)。
        end_page: 结束页码 (1-based, inclusive)。
        output_path: 输出 PDF 文件的路径。

    Returns:
        True 如果成功写入，False 如果发生错误。
    """
    if start_page < 1 or end_page < start_page:
        logger.error(f"Invalid page range: start={start_page}, end={end_page}")
        return False

    doc = None
    new_doc = None
    try:
        # 打开原始PDF文件
        doc = fitz.open(original_pdf_path)
        total_pages = doc.page_count

        # 检查页码范围是否超出文档页数
        if end_page > total_pages:
            logger.warning(f"End page {end_page} exceeds total pages {total_pages}. Adjusting to {total_pages}.")
            end_page = total_pages

        # 创建新的PDF文档
        new_doc = fitz.open()

        # 将指定页面范围复制到新文档
        # PyMuPDF使用0-indexed页码，所以需要将1-indexed页码转换为0-indexed
        new_doc.insert_pdf(doc, from_page=start_page-1, to_page=end_page-1)

        if new_doc.page_count == 0:
            logger.warning(f"No pages were added to the document for range {start_page}-{end_page} from {original_pdf_path}. Output file '{output_path}' will not be created.")
            return False

        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:  # 检查output_dir是否不为空（即不是根目录）
            os.makedirs(output_dir, exist_ok=True)

        # 保存新文档
        new_doc.save(output_path)
        
        logger.info(f"Successfully wrote pages {start_page}-{end_page} from '{os.path.basename(original_pdf_path)}' to '{output_path}' ({new_doc.page_count} pages).")
        return True

    except FileNotFoundError:
        logger.error(f"Original PDF file not found: {original_pdf_path}")
        return False
    except Exception as e:
        logger.error(f"Failed to write PDF part ({start_page}-{end_page}) from \'{original_pdf_path}\' to \'{output_path}\': {e}", exc_info=True)
        # 尝试清理可能损坏的输出文件
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
                logger.info(f"Cleaned up partially written file: {output_path}")
            except OSError as remove_error:
                logger.error(f"Failed to clean up partially written file \'{output_path}\': {remove_error}")
        return False
    finally:
        # 确保文档对象被正确关闭，释放资源
        if new_doc:
            new_doc.close()
        if doc:
            doc.close() 