# Generated by Django 5.1.11 on 2025-06-21 12:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_processing', '0001_initial'),
        ('archive_records', '0016_remove_importconflictdetail_differences_json'),
    ]

    operations = [
        migrations.AddField(
            model_name='archiverecord',
            name='source_file',
            field=models.ForeignKey(blank=True, help_text='关联到此记录的原始上传文件（如PDF）', null=True, on_delete=django.db.models.deletion.SET_NULL, to='archive_processing.uploadedfile', verbose_name='关联的源文件'),
        ),
    ]
