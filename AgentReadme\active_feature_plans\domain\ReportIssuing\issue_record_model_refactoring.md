# 发放记录模型重构需求文档

## 📋 文档概述

**目的**: 重构IssueRecord和IssueRecordHistory模型，简化字段设计，优化历史记录机制
**变更类型**: 模型字段删除、新增，不向后兼容
**影响范围**: 模型、服务层、序列化器、API、测试代码

## 📝 业务需求

1. **简化数据模型**:
    * 移除 `IssueRecord` 中的 `source` 字段，其功能由历史记录中的 `action` 字段替代。
    * 移除 `IssueRecordHistory` 中的 `operation_type` 字段，用更具业务含义的 `action` 字段来描述变更原因。
2. **增强数据追溯能力**:
    * 通过 `IssueRecordHistory.action` 字段，精确记录每一次变更的业务场景（如：`发放单归档创建`, `手动台账修改`）。
3. **统一历史记录机制**:
    * 统一 `ChangeLogBatch` 的 `change_source` 枚举值，使其能够覆盖发放单归档和删除的场景。
4. **保证数据一致性**:
    * 在发放单归档、删除或手动修改发放信息时，确保 `IssueRecord` 和 `ArchiveRecord` 的数据同步。
5. **非兼容性更新**:
    * 本次重构为破坏性变更，所有使用了旧字段（`source`, `operation_type`）的代码都需要进行更新。

---

## 🎯 模型变更需求

### 1. IssueRecord 模型变更

#### 移除字段

* ❌ **`source`** - 完全删除记录来源字段
  * 原枚举值: `('issue_form', '发放单生成')`, `('manual_create', '手动创建')`, `('manual_update', '手动更新')`
  * 替代方案: 通过IssueRecordHistory的action字段追溯来源

#### 保留字段

* ✅ 所有其他业务字段保持不变
* ✅ `issue_form` 关联字段保留
* ✅ `is_active`, `is_deleted` 状态字段保留

### 2. IssueRecordHistory 模型变更

#### 移除字段

* ❌ **`operation_type`** - 删除CRUD操作类型字段
  * 原枚举值: `('create', '创建')`, `('update', '更新')`, `('delete', '删除')`, `('restore', '恢复')`, `('form_delete', '发放单删除')`

#### 新增字段

* ✅ **`action`** - 业务动作字段，替代operation_type
  * 新枚举值:

    ```python
    action = models.CharField(
        max_length=50,
        choices=[
            ('created_from_form_archive', '发放单归档创建'),
            ('updated_by_manual_edit', '手动台账修改'),
            ('deleted_by_form_deletion', '发放单删除'),
            ('restored_by_admin', '管理员恢复'),
            ('copies_adjusted_manually', '手动调整份数'),
            ('receiver_info_updated', '接收人信息更新'),
        ],
        verbose_name="业务动作"
    )
    ```

### 3. ArchiveRecord 历史记录统一

#### ChangeLogBatch.change_source 枚举调整

```python
change_source = models.CharField(choices=[
    ('excel_import', 'Excel导入'),
    ('manual_edit', '手动编辑'),
    ('api_update', 'API更新'),
    ('system', '系统操作'),
    ('batch_operation', '批量操作'),
    ('manual_rollback', '手动回滚'),
    ('issueform_archive', '发放单归档，报告正式发放'),  # 发放单归档
    ('issueform_delete', '发放单删除'),              # 发放单删除
])
```

---

## 🔧 关联函数修改需求

### 1. 序列化器修改

#### IssueRecordSerializer (`report_issuing/serializers.py`)

**需要修改的字段**:

* ❌ 移除 `'source'` 从fields列表
* ❌ 移除 `source_display = serializers.ReadOnlyField(source='get_source_display')`
* ❌ 移除 `'source'`, `'source_display'` 从Meta.fields
* ❌ 移除 `'source'` 从read_only_fields

#### IssueRecordHistorySerializer (`report_issuing/serializers.py`)

**需要修改的字段**:

* ❌ 移除 `'operation_type'`, `'operation_type_display'`
* ❌ 移除 `operation_type_display = serializers.ReadOnlyField(source='get_operation_type_display')`
* ✅ 新增 `'action'`, `'action_display'`
* ✅ 新增 `action_display = serializers.ReadOnlyField(source='get_action_display')`

#### IssueRecordHistoryDataSerializer (`report_issuing/serializers.py`)

**需要修改的字段**:

* ❌ 移除 `'source'` 从Meta.fields

### 2. 视图修改

#### IssueRecordViewSet (`report_issuing/views.py`)

**需要修改的部分**:

* ❌ 移除 `filterset_fields` 中的 `"source"`
* 功能影响: 前端无法再通过source字段进行过滤

### 3. 服务层修改

#### IssueRecordService (`report_issuing/services/issue_record_service.py`)

**需要修改的方法**:

##### `create_issue_record` 方法

* ❌ 移除 `source='manual_create'` 设置
* ✅ 修改历史记录创建:

  ```python
  # 原代码
  operation_type='create'
  
  # 新代码  
  action='created_from_form_archive'  # 或其他适当的action
  ```

##### `update_issue_record` 方法

* ❌ 移除 `source='manual_update'` 设置
* ❌ 移除 `'source': old_record.source` 历史数据记录
* ❌ 移除 `'source': new_record.source` 历史数据记录
* ✅ 修改历史记录创建:

  ```python
  # 原代码
  operation_type='update'
  
  # 新代码
  action='updated_by_manual_edit'
  ```

##### `delete_issue_record` 方法

* ✅ 修改历史记录创建:

  ```python
  # 原代码
  operation_type='delete'
  
  # 新代码
  action='deleted_by_form_deletion'  # 或根据删除上下文选择
  ```

##### `handle_issue_form_deletion` 方法

* ❌ 移除所有 `record.source == 'issue_form'` 判断逻辑
* ❌ 移除所有 `record.source == 'manual_update'` 判断逻辑
* ✅ 修改历史记录创建:

  ```python
  # 原代码
  operation_type='form_delete'
  
  # 新代码
  action='deleted_by_form_deletion'
  ```

##### `create_form_issue_records` 方法

* ❌ 移除 `'source': 'issue_form'` 设置
* ❌ 移除 `'source': old_record.source` 历史数据记录
* ✅ 修改历史记录创建:

  ```python
  # 原代码
  operation_type='create'
  
  # 新代码
  action='created_from_form_archive'
  ```

### 4. 测试代码修改

#### 单元测试修改

##### `test_suite/unit/report_issuing/test_issue_record_service.py`

**需要修改的断言**:

* ❌ 移除所有 `self.assertEqual(record.source, 'manual_create')` 类似断言
* ❌ 移除所有 `operation_type='create'` 类似设置
* ❌ 移除所有 `source='issue_form'` 类似设置
* ✅ 替换为对应的action断言:

  ```python
  # 原断言
  self.assertEqual(history.first().operation_type, 'create')
  
  # 新断言
  self.assertEqual(history.first().action, 'created_from_form_archive')
  ```

##### `test_suite/unit/report_issuing/test_issue_form_service.py`

* ❌ 移除 `source='issue_form'` 设置
* ❌ 移除 `source='manual_create'` 设置

#### 集成测试修改

##### `test_suite/integration/report_issuing/test_api_issue_record.py`

* ❌ 移除 `source='issue_form'` 设置
* ❌ 移除 `self.assertEqual(new_record.source, 'manual_update')` 断言
* ❌ 移除 `operation_type='delete'`, `operation_type='create'` 设置
* ❌ 移除 `self.assertEqual(response.data[0]['operation_type'], 'create')` 断言

##### `test_suite/integration/report_issuing/test_api_issue_form.py`

* ❌ 移除 `source='manual_create'` 设置

### 5. 前端代码修改

#### TypeScript接口修改 (`frontend/components/records/record-history.tsx`)

* ❌ 移除 `sourceType: "ISSUE_FORM"` 相关逻辑
* ❌ 需要确认前端是否使用了source字段进行过滤或显示

---

## 🗂️ 文件清单

### 需要修改的核心文件

1. **模型文件**:
   * `report_issuing/models.py` - 模型字段定义
   * `report_issuing/migrations/` - 需要生成新的迁移文件

2. **序列化器**:
   * `report_issuing/serializers.py`

3. **视图**:
   * `report_issuing/views.py`

4. **服务层**:
   * `report_issuing/services/issue_record_service.py`

5. **测试文件**:
   * `test_suite/unit/report_issuing/test_issue_record_service.py`
   * `test_suite/unit/report_issuing/test_issue_form_service.py`
   * `test_suite/integration/report_issuing/test_api_issue_record.py`
   * `test_suite/integration/report_issuing/test_api_issue_form.py`

6. **前端文件**:
   * `frontend/components/records/record-history.tsx`

---

## ⚠️ 注意事项

### 1. 破坏性变更

* 此次重构为破坏性变更，不向后兼容
* 前端API调用需要相应调整
* 数据库需要重新初始化或提供数据转换脚本

### 2. 业务逻辑调整

* 原有通过source字段判断记录来源的逻辑需要重新设计
* 历史记录查询逻辑需要适配新的action字段

### 3. 测试覆盖

* 所有相关测试用例都需要更新
* 需要增加新的action字段相关测试

### 4. 文档更新

* API文档需要更新字段说明
* 业务流程文档需要更新相关描述

---

## 🚀 实施计划

### Phase 1: 模型和迁移

1. 修改模型定义
2. 生成并执行迁移文件
3. 更新相关的Django管理界面

### Phase 2: 后端代码更新

1. 更新序列化器
2. 更新服务层逻辑
3. 更新视图和API

### Phase 3: 测试代码更新

1. 更新单元测试
2. 更新集成测试
3. 执行完整测试套件

### Phase 4: 前端适配

1. 更新TypeScript接口
2. 调整相关组件逻辑
3. 前后端联调测试

### Phase 5: 文档和部署

1. 更新API文档
2. 更新业务流程文档
3. 部署到测试环境验证
