# 操作文档：Celery任务并发处理问题修复计划

## 📋 变更摘要

**目的**: 修复任务重复执行问题并设计子任务并行处理架构
**范围**: 文件处理任务并发控制机制
**关联问题**: 任务ID被多个worker同时处理导致状态冲突

## 🔧 操作步骤

### 📊 OP-001: 问题分析与根本原因确认

**前置条件**: 已有日志显示同一任务ID被两个worker处理
**操作**:

1. 分析日志中的时间线和状态变化
2. 确认问题根本原因为缺乏任务级别的锁机制
**后置条件**: 问题根源已明确，可以制定修复方案

### ✏️ OP-002: 实施事务锁方案

**前置条件**: 已确认使用数据库事务锁解决方案
**操作**:

1. 修改`process_pdf_task`函数，添加事务和`select_for_update()`锁定
2. 调整任务状态检查和更新机制
3. 确保事务范围仅包含状态更新，不包含长时间处理
**后置条件**: 单个任务不会被多个worker同时处理

### 🧪 OP-003: 测试并发处理保护机制

**前置条件**: 事务锁方案代码已实现
**操作**:

1. 设计并发测试用例模拟多个worker访问
2. 验证锁机制能否有效防止重复处理
3. 确认Celery重试机制仍能正常工作
**后置条件**: 确认并发问题已解决且不影响重试功能

### 📊 OP-004: 设计子任务并行处理架构

**前置条件**: 单任务并发问题已解决
**操作**:

1. 设计SubTask数据模型
2. 规划任务拆分和状态管理逻辑
3. 设计主任务与子任务协调机制
**后置条件**: 子任务并行处理架构设计完成

## 📝 变更详情

### CH-001: 添加任务级别事务锁

**文件**: `archive_processing/tasks.py`
**修改前**:

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_pdf_task(self, task_id):
    try:
        task = ProcessingTask.objects.select_related('file').get(task_id=task_id)
    except ProcessingTask.DoesNotExist:
        logger.error(f"任务处理失败：未找到 Task ID 为 {task_id} 的任务记录。")
        return {'success': False, 'error': f'Task not found: {task_id}'}
    
    if task.status not in ['queued', 'failed']:
        logger.warning(f"任务 {task_id} 状态为 {task.status}，跳过处理。")
        return {'success': True, 'message': f'Task {task_id} already processed or in progress.'}

    task.status = 'processing'
    task.save(update_fields=['status'])
```

**修改后**:

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_pdf_task(self, task_id):
    try:
        with transaction.atomic():
            # 使用select_for_update()锁定记录，防止其他事务同时修改
            task = ProcessingTask.objects.select_for_update().select_related('file').get(task_id=task_id)
            
            if task.status not in ['queued', 'failed']:
                logger.warning(f"任务 {task_id} 状态为 {task.status}，跳过处理。")
                return {'success': True, 'message': f'Task {task_id} already processed or in progress.'}
                
            # 在事务内更新状态
            task.status = 'processing'
            task.save(update_fields=['status'])
            # 事务结束时锁释放，但状态已更新
    except ProcessingTask.DoesNotExist:
        logger.error(f"任务处理失败：未找到 Task ID 为 {task_id} 的任务记录。")
        return {'success': False, 'error': f'Task not found: {task_id}'}
```

**原理**: 使用数据库事务和行级锁确保同一时间只有一个worker能处理特定任务ID

### CH-002: SubTask数据模型设计(未来实现)

**文件**: `archive_processing/models.py`
**内容**:

```python
class SubTask(models.Model):
    """PDF分割子任务"""
    subtask_id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    parent_task = models.ForeignKey(ProcessingTask, on_delete=models.CASCADE, related_name='subtasks')
    start_page = models.IntegerField()
    end_page = models.IntegerField()
    status = models.CharField(max_length=40, choices=STATUS_CHOICES, default='pending')
    result_data = models.JSONField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "处理子任务"
        verbose_name_plural = verbose_name
        ordering = ['start_page']
        indexes = [
            models.Index(fields=['parent_task', 'status']),
        ]
```

**原理**: 扩展数据模型支持任务分割和并行处理

## ✅ 验证结果

**方法**:

- 单元测试模拟并发任务处理
- 集成测试验证锁机制在实际环境中的效果

**预期结果**:

- 同一任务不会被多个worker同时处理
- 状态更新不再出现冲突
- Celery重试机制仍能正常工作

**风险与缓解**:

- 事务锁可能增加数据库负载：通过限制锁定范围仅包含状态更新来缓解
- 长时间运行的任务可能导致锁时间过长：确保事务范围最小化
