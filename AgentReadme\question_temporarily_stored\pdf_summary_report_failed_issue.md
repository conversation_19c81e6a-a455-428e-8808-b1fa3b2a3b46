# PDF处理摘要报告生成失败问题

## 状态

已解决

## 问题描述

在PDF处理流程中，所有档案分割、归档和数据库更新操作均成功完成，但最终摘要报告生成失败。

## 错误日志

```text
2025-04-21 22:02:50 ERROR 2025-04-21 14:02:50,558 [archive_processing.utils.processing_report_utils] 无法创建摘要：缺少有效的处理结果 DTO 或统计信息。
2025-04-21 22:02:50 ERROR 2025-04-21 14:02:50,558 [archive_processing.tasks] 任务 441ab909-9d69-4e68-8216-8570cbdc4739: 生成报告失败（create_result_summary 返回 None）
```

## 原因分析

在`process_pdf_with_ocr_results_task`函数中，调用`generate_summary_report`时传递了`None`作为`splitting_info_dto`参数：

```python
summary_path = generate_summary_report(
    task_id, pdf_path, None, archived_files_list, status_update_results
)
```

`processing_report_utils.create_result_summary`函数需要有效的处理结果DTO来生成摘要，但并行处理模式中缺少此信息。

## 影响范围

- 所有实际档案处理功能（分割、归档、数据库更新）正常完成
- 仅摘要报告生成失败，不影响主要功能
- 任务状态仍正确标记为`completed`

## 可能的修复方向

1. 在并行处理流程中保存和传递`splitting_info_dto`
2. 修改`create_result_summary`函数，使其在缺少DTO时仍能生成基本报告
3. 或在`process_pdf_with_ocr_results_task`中重新构造一个简化的DTO用于报告生成
