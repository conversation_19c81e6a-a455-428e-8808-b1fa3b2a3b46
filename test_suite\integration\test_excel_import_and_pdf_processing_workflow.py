import os
import logging
import time
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from archive_records.models import ArchiveRecord
from archive_records.services.excel_import import ExcelImportService
from archive_records.services.archive_status_service import ArchiveStatusService
from test_suite.utils.test_helpers import get_test_file_path
# CHANGE: [2024-04-18] 添加测试设置导入 #AFM-13
from test_suite.utils.test_settings import apply_test_settings, cleanup_test_files
import pandas as pd
from django.utils import timezone
from typing import List, Tuple, Optional, Dict
from django.db import connections, close_old_connections, transaction

# CHANGE: [2024-04-18] 更新导入方式，使用PDFProcessor代替已移除的split_multiple_pdfs #AFM-13
from archive_processing.utils.pdf_processor_usefull import PDFProcessor
from archive_processing.services.record_update_service import update_archive_record, generate_file_url
from archive_processing.models import UploadedFile

# CHANGE: [2024-03-29] 添加文件日志记录 #AFM-26
# --- 日志配置 ---
LOG_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'output', 'integration_logs'))
os.makedirs(LOG_DIR, exist_ok=True)
# CHANGE: [2024-04-16] 优化日志文件名以包含更多信息 #AFM-59
log_filename = os.path.join(LOG_DIR, f"excel_pdf_workflow_test_{time.strftime('%Y%m%d_%H%M%S')}.log")

logger = logging.getLogger('excel_pdf_integration_test')
logger.setLevel(logging.INFO)

# 文件处理器
fh = logging.FileHandler(log_filename, encoding='utf-8')
fh.setLevel(logging.INFO)
# CHANGE: [2024-04-16] 优化日志格式以包含更详细信息 #AFM-59
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s')
fh.setFormatter(formatter)
logger.addHandler(fh)

# 控制台处理器 (默认关闭，仅在需要调试时启用)
# ch = logging.StreamHandler()
# ch.setLevel(logging.INFO)
# ch.setFormatter(formatter)
# logger.addHandler(ch)
# --- 结束日志配置 ---

# CHANGE: [2024-04-15] Use TransactionTestCase instead of TestCase to avoid transaction issues #AFM-56
class IntegrationTest(TransactionTestCase):
    # CHANGE: [2024-04-15] 禁用Django的事务测试，避免SQLite锁表问题 #AFM-56
    # Django测试默认在单个事务中运行，这会导致SQLite在多步操作时锁表
    # 设置为非事务模式，每个数据库操作都会真实提交
    reset_sequences = True  # 确保数据库序列在每次测试后重置

    def setUp(self):
        # CHANGE: [2024-04-15] Close old connections before test setup #AFM-55
        close_old_connections()
        
        # CHANGE: [2024-04-18] 应用测试配置设置 #AFM-13
        apply_test_settings()
        logger.info("已应用测试配置设置")
        
        # CHANGE: [2024-04-16] 确保先删除已存在的测试用户 #AFM-58
        User.objects.filter(username='testuser').delete()
        
        self.user = User.objects.create_user(username='testuser', password='12345')
        self.excel_service = ExcelImportService()
        self.status_service = ArchiveStatusService()
        
        # 测试文件路径设置
        self.excel_file_path = get_test_file_path('excel', 'test_excel_import_and_pdf_processing.xls')
        self.pdf_file_path = get_test_file_path('pdf', 'test_excel_import_and_pdf_processing.pdf')

    def tearDown(self):
        # CHANGE: [2024-04-18] 清理测试文件 #AFM-13
        cleanup_test_files()
        logger.info("已清理测试文件")
        
        # CHANGE: [2024-04-15] Close connections after test is complete #AFM-55
        close_old_connections()
        
    def test_excel_import_and_pdf_processing(self):
        """测试Excel导入和PDF处理的完整流程"""
        # CHANGE: [2024-04-15] Close connections at the beginning of the test #AFM-55
        close_old_connections()
        logger.info("===== 开始测试Excel导入和PDF处理流程 =====")
        
        # 1. 导入Excel数据
        logger.info("[第一步] 导入Excel数据...")
        import_log = self.excel_service.import_from_file(
            self.excel_file_path,
            user=self.user
        )
        
        # CHANGE: [2024-04-15] Close connections after Excel import #AFM-55
        close_old_connections()
        
        # 导入可能部分成功或全部失败，但测试应该继续
        logger.info(f"导入状态: {import_log.status}")
        logger.info(f"总记录数: {import_log.total_records}")
        logger.info(f"成功记录数: {import_log.success_records}")
        logger.info(f"失败记录数: {import_log.failed_records}")
        
        # 验证数据导入是否成功
        self.assertTrue(import_log.success_records > 0, "Excel导入失败，没有成功导入任何记录")
        
        # 只有在有成功导入的记录时才测试PDF处理
        if import_log.success_records > 0:
            # 检查导入后数据库中的记录
            all_records = ArchiveRecord.objects.all()
            logger.info(f"数据库中实际存在的记录数: {all_records.count()}")
            
            # CHANGE: [2024-04-15] Close connections before PDF processing #AFM-55
            close_old_connections()
            
            # 2. 实际处理PDF文件
            logger.info("[第二步] 处理PDF文件...")
            
            # 验证测试文件存在
            self.assertTrue(os.path.exists(self.pdf_file_path), f"测试PDF文件不存在: {self.pdf_file_path}")
            
            # CHANGE: [2024-04-18] 使用PDFProcessor代替split_multiple_pdfs #AFM-13
            # 设置目标文本为"代合同"，可以根据实际需要调整
            target_text = "代合同"
            
            # 处理PDF，获取分割后的文件和统一编号
            logger.info(f"处理PDF: {os.path.basename(self.pdf_file_path)}")
            logger.info(f"目标文本: {target_text}")
            
            try:
                # CHANGE: [2024-04-18] 使用PDFProcessor处理PDF #AFM-13
                processing_kwargs = {
                    'use_paddle_ocr': True,
                    'enable_cache': True,
                    'dpi': 150,
                    'batch_size': 50
                }
                
                # 初始化处理器并处理PDF
                processor = PDFProcessor(**processing_kwargs)
                output_parts_with_numbers = processor.process_pdf(
                    pdf_path=self.pdf_file_path, 
                    target_text=target_text
                )
                
                # 获取处理统计
                stats = processor.get_stats()
                logger.info(f"PDF处理完成，共分割为 {len(output_parts_with_numbers)} 个部分")
                logger.info(f"总页数: {stats.get('total_pages', 0)}, 处理耗时: {stats.get('processing_time', 0):.2f}秒")
                
                # 检查处理结果
                self.assertTrue(len(output_parts_with_numbers) > 0, "PDF处理未生成任何部分")

                # 3. 使用新的服务函数更新档案记录
                logger.info("[第三步] 更新档案记录...")
                
                # 创建一个模拟的UploadedFile记录，用于测试关联
                mock_uploaded_file = UploadedFile.objects.create(
                    original_name="test_pdf_processing.pdf",
                    saved_path=self.pdf_file_path,
                    file_size=12345,
                    archive_box_number="TEST-BOX-001"
                )
                
                # 跟踪更新结果
                successful_updates = 0
                failed_updates = 0
                records_not_found = 0
                updated_numbers = []
                
                # 循环处理每个分割后的部分
                for file_path, unified_number in output_parts_with_numbers:
                    if unified_number and file_path:
                        # 使用record_update_service更新记录
                        update_result = update_archive_record(
                            unified_number=unified_number,
                            file_path=file_path,
                            user_id=self.user.id,
                            source_file_id=mock_uploaded_file.file_id
                        )
                        
                        if update_result.get('success'):
                            successful_updates += 1
                            updated_numbers.append(unified_number)
                            logger.info(f"成功更新记录: {unified_number}")
                        elif update_result.get('status') == 'not_found':
                            records_not_found += 1
                            logger.warning(f"未找到记录: {unified_number}")
                        else:
                            failed_updates += 1
                            logger.error(f"更新记录失败: {unified_number}, 错误: {update_result.get('error')}")
                
                # 记录更新结果
                logger.info(f"记录更新结果: 成功={successful_updates}, 失败={failed_updates}, 未找到={records_not_found}")
                self.assertTrue(successful_updates > 0, "未能成功更新任何档案记录")

                # 4. 验证状态更新
                logger.info("[第四步] 验证数据库状态...")
                
                # 查询数据库中已归档的记录数量
                archived_records_count = ArchiveRecord.objects.filter(archive_status='已归档').count()
                logger.info(f"数据库中已归档的记录数: {archived_records_count}")
                
                # 验证至少有一些记录被更新为已归档
                self.assertTrue(archived_records_count > 0, "没有任何记录被标记为已归档")
                
                # 验证数据库中的记录与PDF中识别到的统一编号匹配，并且包含归档信息
                matched_count = 0
                for unified_number in updated_numbers:
                    record = ArchiveRecord.objects.filter(unified_number=unified_number).first()
                    self.assertIsNotNone(record, f"数据库中未找到统一编号为 {unified_number} 的记录")
                    if record:
                        self.assertEqual(record.archive_status, '已归档',
                                         f"统一编号为{unified_number}的记录状态不是 '已归档'")
                        # 验证其他字段
                        self.assertIsNotNone(record.archive_url, f"记录 {unified_number} 缺少 archive_url")
                        self.assertIsNotNone(record.archive_datetime, f"记录 {unified_number} 缺少 archive_datetime")
                        self.assertEqual(record.archive_person, self.user.username, f"记录 {unified_number} 的归档人不是 {self.user.username}")
                        self.assertIsNotNone(record.source_file, f"记录 {unified_number} 未关联源文件")
                        self.assertEqual(record.source_file.archive_box_number, "TEST-BOX-001", f"记录 {unified_number} 的盒号不正确")
                        matched_count += 1

                # 打印测试结果
                logger.info(f"PDF处理和状态更新测试完成，共验证 {matched_count} 条记录已正确归档")
                
                # CHANGE: [2024-04-15] Close connections after PDF processing #AFM-55
                close_old_connections()
            
            except Exception as e:
                logger.error(f"PDF处理或状态更新过程中发生错误: {e}", exc_info=True)
                # CHANGE: [2024-04-15] Close connections on exception #AFM-55
                close_old_connections()
                # 为了确保测试能看到详细错误，重新抛出异常
                raise
                
        else:
            logger.warning("没有成功导入的记录，跳过PDF处理测试")
            self.fail("Excel导入失败，无法继续测试") 