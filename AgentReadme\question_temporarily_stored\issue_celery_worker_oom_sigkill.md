# 问题描述：Celery Worker 因 OOM 被 SIGKILL 强制终止

**关联日志时间**: 2025-04-22 10:53:58 及之后

**现象**:

在执行三阶段并行 PDF 处理任务 (特别是 `process_pdf_ocr_task` 子任务) 时，多个 Celery Worker 子进程 (ForkPoolWorker) 异常退出，日志显示 `exited with 'signal 9 (SIGKILL)'`。

**直接后果**:

1. Celery 主进程报告 `WorkerLostError('Worker exited prematurely: signal 9 (SIGKILL) Job: Z.')`。
2. 由于 OCR 子任务是 Chord 的依赖，子任务失败导致 Chord 失败，抛出 `ChordError("Dependency X raised WorkerLostError(...)")`。
3. 在 Celery 内部处理 Chord 异常的回调路径中，尝试向 Redis 后端记录失败状态时，因 `task_id` 意外为 `None` 而引发 `ValueError: task_id must not be empty. Got None instead.`。

**根本原因**:

并行执行的 `process_pdf_ocr_task` 子任务（特别是其中的 PaddleOCR 识别步骤）消耗了大量内存，超出了操作系统或容器为 Worker 进程分配的内存限制，导致系统 OOM Killer 介入并强制终止 (SIGKILL) 这些进程。

**影响**:

PDF 处理任务中断，无法完成。系统稳定性受影响。

**建议解决方案 (优先级排序)**:

1. **减小 PDF 分块大小**: 在调用 `process_pdf_three_phase_coordinator_task` 时，减小 `chunk_size` 参数 (例如从 20 减至 10 或 5)，降低单个 OCR 任务的内存峰值。
2. **降低 Worker 并发数**: 减少启动 Worker 时设置的 `--concurrency` (或 `-c`) 参数值，减少同时运行的内存密集型 OCR 任务数量。
3. **设置资源限制与监控**:
    * 为 Worker 容器设置明确的内存限制 (`memory limit`)。
    * 加强对 Worker 内存使用情况的监控。
4. **优化 OCR 任务内存使用**:
    * 分析 `process_pdf_ocr_task` 内部逻辑，特别是 PaddleOCR 配置、图像预处理和内存释放环节，减少内存占用。
5. **启用 Worker 子进程重启**:
    * 使用 `--max-tasks-per-child=N` 让子进程定期重启以释放内存。
    * 谨慎使用 `--max-memory-per-child=M` 限制单个子进程内存。
6. **增加资源或水平扩展**:
    * 增加 Worker 机器/容器的可用内存。
    * 将 Worker 分散到更多机器上运行（水平扩展）。

**待办事项**:

* [ ] 评估并实施上述建议中的一个或多个方案。
* [ ] 监控调整后的系统稳定性和资源使用情况。
