---
description: 触发指令示例: "帮我编写commit","我想进行commit","准备提交"。当用户请求生成 commit message 或表示准备提交时，此规则指导 AI 严格遵循"分析->计划->更新文档->生成Commit Message"的流程。
globs: 
alwaysApply: false
---
# 规则：提交前文档驱动流程 (commit-and-update-docs)

## 描述：确保在生成 Commit Message 或执行提交前，开发状态被准确分析，下一步计划被确认，并且所有相关文档（检查点、核心计划、测试日志等）都已根据实际情况更新。严格遵循 "分析 -> 计划 -> 更新文档 -> 提交" 的顺序。对于用户已自行 Commit 的情况，则进行事后状态同步

## 触发指令示例: "帮我编写commit", "我想进行commit", "准备提交", "请生成 commit message"

## 背景与原理: @AgentReadme/planning_and_requirements/ai_dev_checkpoint.md 文件是 AI 与开发者共享的"短期记忆"，包含"最近提交"和"下一步计划"。遵循 "查看->执行->分析->计划->更新文档->提交" 的迭代流程至关重要。此规则是该流程后半段（分析、计划、更新文档、提交）的具体实现，强制在生成 Commit Message 前完成所有文档更新，确保 Commit 的准确性和文档驱动的有效性

## --- 主流程：处理"准备提交"或"请求生成 Commit Message" ---

当用户**表达准备提交代码的意图** (例如 "我想进行 commit", "准备提交") 或**请求 AI 生成 commit message** (例如 "帮我编写 commit") 时，AI 代理应执行以下流程：

1. **AI 执行"分析"**:
    * **动作**: AI 读取 `ai_dev_checkpoint.md` 的"下一步计划"（本次迭代目标），并结合当前代码变更（或向用户询问），对比分析本次迭代**实际完成的工作**与聚焦的任务。
    * **内容**: 识别偏差、新增内容、部分完成情况。检查聚焦任务是否完成；如果完成但主任务未结束，识别主任务的下一个逻辑步骤。
    * *(可选输出)*: 可以向用户简要汇报分析结果。

2. **AI 执行"计划"**:
    * **动作**: 基于步骤 1 的分析结果和项目整体目标 (参考 `detailed_work_plan_and_log.md` 区域二/三)，确定下一个迭代周期的计划。
    * **内容**:
        * 通常情况: 聚焦于完成当前区域二任务的下一个步骤（可能是新识别的子任务）。
        * 区域二清空时: 回顾区域三，选择下一批任务并提议迁移至区域二。
    * **提议**: **清晰地向用户提议这个"下一步计划"**。

3. **用户确认"下一步计划"**:
    * **动作**: AI **必须**等待用户对步骤 2 中提议的"下一步计划"进行明确的同意或修正。
    * **要求**: **此步骤只确认计划，不涉及文档内容。**

4. **AI 提议"更新文档"**:
    * **前置条件**: 步骤 3 中的"下一步计划"**必须已被用户确认**。
    * **动作**: AI 基于步骤 1 的分析结果和步骤 3 确认的下一步计划，汇总并**提议**对以下所有相关文档进行更新：
        * **检查点 (`ai_dev_checkpoint.md`)**:
            * 在"最近提交"部分记录步骤 1 分析出的**实际完成内容**。
            * 在"下一步计划"部分写入步骤 3 **已确认的**下一步计划。
        * **核心计划 (`detailed_work_plan_and_log.md`)**:
            * **提议具体修改**:
                * 对于区域二中的主要任务，提议更新其状态 (`[>]` 或 `[x]`)，并提议将迭代中发现的必要的**子任务（无论完成与否）**作为子项添加到父任务下，并标记其状态。
                * 对于迭代中完成的**独立小任务**，提议将其直接添加到区域四的历史记录中。
                * 对于分析或新计划产生的长期条目，提议添加到区域三。
                * **任务迁移 (如果步骤2确定需要)**: 提议将选定的任务从区域三移动到区域二。
        * **测试日志 (`testing_logs/`)** (如果适用)。
        * **特定功能计划 (`active_feature_plans/`)** (如果适用)。
    * **呈现**: **清晰地向用户呈现所有提议的文档修改内容**。

5. **用户确认"文档更新"**:
    * **动作**: AI **必须**等待用户对步骤 4 中提议的**所有文档更新内容**进行明确的同意或修正。
    * **要求**: **此步骤确认具体的文档修改。**

6. **AI 执行"更新文档"**:
    * **动作**: 根据步骤 5 中用户确认的结果，AI **实际修改**所有相关文件。

7. **AI 执行"提交" (生成 Commit Message)**:
    * **前置条件**: 步骤 6 中的文档更新**必须已成功执行**。
    * **动作**: AI 基于步骤 6 **刚刚更新并确认**的 `ai_dev_checkpoint.md` 中的"最近提交"条目内容，生成能够准确、简洁地反映本次提交所完成任务的 commit message。
    * **输出**: 将生成的 commit message 提供给用户。
