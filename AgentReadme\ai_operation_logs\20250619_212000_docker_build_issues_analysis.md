# 操作文档: Docker构建过程中文乱码和网络连接问题分析

## 📋 变更概要

**目的**: 解决Docker构建过程中出现的中文字符乱码和网络连接问题
**范围**: Dockerfile, docker-compose.yml, Windows编码设置
**关联**: 项目部署环境优化

## 🔧 操作步骤

### 📊 OP-001: 问题分析

**前置条件**: Docker构建失败，出现中文乱码和网络连接错误
**操作**: 分析构建日志，识别两个主要问题：

1. 中文字符显示乱码："PaddleOCR模型下载完"
2. 网络连接失败：无法解析 registry-1.docker.io 域名
**后置条件**: 明确了问题根因和解决思路

### ✏️ OP-002: 修复Dockerfile中文支持

**前置条件**: Dockerfile缺少中文字符集配置
**操作**:

- 添加UTF-8环境变量配置
- 安装locales包
- 配置UTF-8 locale支持
**后置条件**: Docker容器内支持中文字符正常显示

### ✏️ OP-003: 配置docker-compose.yml DNS设置

**前置条件**: 容器无法解析外部域名
**操作**:

- 添加全局DNS配置模板
- 为所有服务配置可靠的DNS服务器
- 使用Google DNS和国内DNS服务器
**后置条件**: 容器网络连接问题得到解决

### ✏️ OP-004: 创建Windows编码设置脚本

**前置条件**: Windows PowerShell控制台编码问题
**操作**: 创建 `scripts/set_windows_encoding.ps1` 脚本
**后置条件**: 提供Windows环境下的编码问题解决方案

## 📝 变更详情

### CH-001: Dockerfile UTF-8支持配置

**文件**: `Dockerfile`
**变更前**:

```dockerfile
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
```

**变更后**:

```dockerfile
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 添加中文字符集支持和locale设置
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8
```

**理由**: 解决容器内中文字符显示乱码问题
**潜在影响**: 提高容器内应用的国际化支持

### CH-002: docker-compose.yml DNS配置

**文件**: `docker-compose.yml`
**变更前**: 使用默认DNS设置
**变更后**: 添加了全局DNS配置和每个服务的DNS设置

**理由**: 解决容器无法连接到Docker Hub registry的问题
**潜在影响**: 改善容器网络连接稳定性

### CH-003: Windows编码设置脚本

**文件**: `scripts/set_windows_encoding.ps1`
**变更**: 新增文件
**理由**: 为Windows用户提供控制台编码问题的解决方案
**潜在影响**: 改善Windows环境下的开发体验

## ✅ 验证结果

**方法**:

1. 测试Docker镜像构建过程
2. 验证中文字符正常显示
3. 检查网络连接问题解决情况

**预期结果**:

- 构建过程中中文字符正确显示
- 能够成功拉取Docker镜像
- 网络连接稳定

**遗留问题**:

- 需要实际测试网络连接改善效果
- Windows用户需要手动执行编码设置脚本

## 🔧 问题根因总结

### 问题1: 中文乱码

**原因**:

- Docker容器默认不支持中文字符集
- 缺少UTF-8 locale配置
- Python输出编码设置不当
- Windows PowerShell控制台编码问题

**解决方案**:

- 在Dockerfile中配置UTF-8环境变量
- 安装并配置locales包
- 为Windows用户提供编码设置脚本

### 问题2: 网络连接问题

**原因**:

- DNS解析失败：无法解析 registry-1.docker.io
- 网络连接超时
- 可能的防火墙或代理问题

**解决方案**:

- 配置可靠的DNS服务器（Google DNS + 国内DNS）
- 为所有Docker服务添加DNS设置
- 使用多个DNS服务器作为备选

## 📋 后续建议

1. **测试验证**: 重新构建Docker镜像，验证问题是否解决
2. **文档更新**: 在README中添加Windows用户的编码设置说明
3. **网络优化**: 如果问题持续，考虑使用Docker Hub镜像加速器
4. **监控改进**: 添加构建过程监控，及时发现类似问题

## 💡 最佳实践

1. **国际化支持**: 所有Docker镜像都应配置UTF-8支持
2. **网络配置**: 为容器服务配置可靠的DNS服务器
3. **开发环境**: 为不同操作系统提供相应的环境配置脚本
4. **问题排查**: 建立系统性的日志分析和问题排查流程
