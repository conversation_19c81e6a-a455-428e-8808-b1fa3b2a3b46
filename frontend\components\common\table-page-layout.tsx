"use client"

import React, { ReactNode } from "react"
import { PageHeader, PageAction } from "@/components/common/page-header"

interface TablePageLayoutProps {
  /** 页面标题 */
  title: string
  /** 页面副标题/描述 */
  subtitle?: string
  /** 操作按钮数组 */
  actions?: PageAction[]
  /** 筛选器组件 */
  filter?: ReactNode
  /** 子内容（通常是表格组件） */
  children: ReactNode
  /** 子内容外层容器的类名 */
  contentClassName?: string
  /** 顶部间距 */
  topMargin?: string
  /** 是否显示头部底部边框 */
  showHeaderBorder?: boolean
  /** 状态卡片内容 */
  statusCards?: ReactNode
  /** 固定标签栏内容 */
  fixedTabs?: ReactNode
}

/**
 * 表格页面专用布局组件
 *
 * 提供与PageLayout一致的页面结构，但更好地处理数据表格的滚动行为
 * 固定标题和筛选器区域，表格内容区域可滚动
 */
export function TablePageLayout({
  title,
  subtitle,
  actions,
  filter,
  children,
  contentClassName,
  topMargin,
  showHeaderBorder = true,
  statusCards,
  fixedTabs,
}: TablePageLayoutProps) {
  return (
    <div className="flex flex-col h-full">
      {/* 使用共享的PageHeader组件 */}
      <PageHeader
        title={title}
        subtitle={subtitle}
        actions={actions}
        showHeaderBorder={showHeaderBorder}
        className={topMargin}
        statusCards={statusCards}
        fixedTabs={fixedTabs}
      />
      
      {/* 筛选器区域 - 如果提供 */}
      {filter && (
        <div className="flex-none mb-4">
          {filter}
        </div>
      )}
      
      {/* 表格内容区域 - 占据剩余空间，内部滚动 */}
      <div className={`flex-1 overflow-hidden ${contentClassName || ""}`}>
        {children}
      </div>
    </div>
  )
} 