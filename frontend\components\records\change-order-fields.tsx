"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Plus, Trash } from "lucide-react"
import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ChangeOrderFieldsProps {
  record: any
  onChange: (changes: { field: string; oldValue: string; newValue: string }[]) => void
}

export function ChangeOrderFields({ record, onChange }: ChangeOrderFieldsProps) {
  const [changes, setChanges] = useState<{ field: string; oldValue: string; newValue: string }[]>([])

  // 可编辑字段列表
  const editableFields = [
    { id: "title", label: "标题", value: record.title || "" },
    { id: "projectName", label: "项目名称", value: record.projectName || "" },
    { id: "projectType", label: "项目类型", value: record.projectType || "" },
    { id: "description", label: "描述", value: record.description || "" },
    { id: "keywords", label: "关键词", value: record.keywords || "" },
    { id: "department", label: "部门", value: record.department || "" },
    { id: "author", label: "编制人", value: record.author || "" },
    { id: "reviewedBy", label: "审核人", value: record.reviewedBy || "" },
    { id: "approvedBy", label: "批准人", value: record.approvedBy || "" },
    { id: "approvalDate", label: "批准日期", value: record.approvalDate || "" },
    { id: "expiryDate", label: "有效期至", value: record.expiryDate || "" },
    { id: "confidentialLevel", label: "保密级别", value: record.confidentialLevel || "" },
    { id: "remarks", label: "备注", value: record.remarks || "" },
  ]

  // 当前已选择的字段ID列表
  const selectedFieldIds = changes.map((change) => change.field)

  // 添加一个新的字段更改
  const addChange = () => {
    // 找到第一个未被选择的字段
    const availableField = editableFields.find((field) => !selectedFieldIds.includes(field.id))
    if (availableField) {
      const newChange = {
        field: availableField.id,
        oldValue: availableField.value,
        newValue: availableField.value,
      }
      setChanges([...changes, newChange])
    }
  }

  // 删除一个字段更改
  const removeChange = (index: number) => {
    const newChanges = [...changes]
    newChanges.splice(index, 1)
    setChanges(newChanges)
  }

  // 更新字段选择
  const updateField = (index: number, fieldId: string) => {
    const field = editableFields.find((f) => f.id === fieldId)
    if (field) {
      const newChanges = [...changes]
      newChanges[index] = {
        field: fieldId,
        oldValue: field.value,
        newValue: field.value,
      }
      setChanges(newChanges)
    }
  }

  // 更新新值
  const updateNewValue = (index: number, value: string) => {
    const newChanges = [...changes]
    newChanges[index].newValue = value
    setChanges(newChanges)
  }

  // 当更改列表变化时，通知父组件
  useEffect(() => {
    onChange(changes)
  }, [changes, onChange])

  return (
    <Card>
      <CardHeader>
        <CardTitle>更改字段</CardTitle>
        <CardDescription>选择需要修改的字段并指定新值</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {changes.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground mb-4">尚未选择任何要修改的字段</p>
            <Button type="button" onClick={addChange}>
              <Plus className="mr-2 h-4 w-4" />
              添加字段
            </Button>
          </div>
        ) : (
          <>
            {changes.map((change, index) => (
              <div key={index} className="grid gap-4 sm:grid-cols-[1fr,1fr,1fr,auto]">
                <div className="space-y-2">
                  <Label>字段</Label>
                  <Select value={change.field} onValueChange={(value) => updateField(index, value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择字段" />
                    </SelectTrigger>
                    <SelectContent>
                      {editableFields.map((field) => (
                        <SelectItem
                          key={field.id}
                          value={field.id}
                          disabled={selectedFieldIds.includes(field.id) && change.field !== field.id}
                        >
                          {field.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>原值</Label>
                  <Input value={change.oldValue} disabled />
                </div>
                <div className="space-y-2">
                  <Label>新值</Label>
                  <Input value={change.newValue} onChange={(e) => updateNewValue(index, e.target.value)} />
                </div>
                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeChange(index)}
                    className="text-red-500 hover:text-red-600 hover:bg-red-50"
                  >
                    <Trash className="h-4 w-4" />
                    <span className="sr-only">删除</span>
                  </Button>
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={addChange}
              disabled={selectedFieldIds.length === editableFields.length}
            >
              <Plus className="mr-2 h-4 w-4" />
              添加更多字段
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
