import logging
import os
import random
import json
from typing import Optional
from datetime import timed<PERSON><PERSON>

from celery import shared_task
from django.utils import timezone
from django.db import transaction
from django.db.utils import OperationalError
from django.core.cache import cache
from django.conf import settings

from .models import ImportSession, ImportSessionStatus, SessionOperation, ImportConflictDetail, ImportLog

logger = logging.getLogger(__name__)

# REMOVE: [2025-05-28] cleanup_cancelled_session_task is obsolete.
# Its functionality is superseded by ImportSessionManager._ensure_session_status_is_current
# and the new process_finalized_sessions_task for resource cleaning.
# @shared_task(bind=True, max_retries=3, default_retry_delay=60)
# def cleanup_cancelled_session_task(self, session_id: str):
#     # ... (entire old implementation commented out or removed)
#     pass

# REMOVE: [2025-05-28] run_cleanup_expired_sessions_task is obsolete.
# The functionality of ImportSessionManager.cleanup_expired_sessions (which it called)
# is now integrated into ImportSessionManager._ensure_session_status_is_current (on-demand status update).
# @shared_task(name="archive_records.tasks.run_cleanup_expired_sessions_task")
# def run_cleanup_expired_sessions_task():
#     # ... (entire old implementation commented out or removed)
#     pass

# CHANGE: [2025-05-18] 新增Celery任务用于异步处理Excel导入确认
@shared_task(bind=True, max_retries=3, default_retry_delay=120, name="archive_records.tasks.process_excel_import_confirmation_task")
def process_excel_import_confirmation_task(self, session_id: str, resolutions_json: str, user_id: int, 
                                         analysis_stats_json: Optional[str] = None, 
                                         user_decision_stats_json: Optional[str] = None):
    """
    异步处理Excel导入的确认阶段。
    analysis_stats_json: 包含分析阶段统计数据的JSON字符串。
                         e.g., {"total_analyzed": T, "analysis_errors": E, "analysis_identical": I, "analysis_new": N, "analysis_update": U}
    user_decision_stats_json: 包含用户决策统计的JSON字符串。
                              e.g., {"skipped_update_count": S_u, "confirmed_update_count": C_u}
    """
    logger.info(f"Celery异步导入确认任务开始：会话ID {session_id}, 用户ID {user_id}")
    if analysis_stats_json: logger.info(f"[CeleryTask.Confirm] Raw analysis_stats_json received: {analysis_stats_json}")
    else: logger.warning("[CeleryTask.Confirm] analysis_stats_json is None or empty.")
    if user_decision_stats_json: logger.info(f"[CeleryTask.Confirm] Raw user_decision_stats_json received: {user_decision_stats_json}")
    else: logger.warning("[CeleryTask.Confirm] user_decision_stats_json is None or empty.")
    
    from django.contrib.auth import get_user_model
    from .services.import_session_manager import ImportSessionManager

    User = get_user_model()
    session_orm_object = None
    user = None

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"Celery任务 {self.request.id}: 用户ID {user_id} 不存在。任务无法继续。")
        try:
            session_to_fail = ImportSession.objects.get(session_id=session_id)
            session_to_fail.status = ImportSessionStatus.ERROR
            session_to_fail.error_message = f"任务启动失败：用户ID {user_id} 不存在。"
            session_to_fail.save(update_fields=['status', 'error_message', 'updated_at'])
        except ImportSession.DoesNotExist:
            logger.error(f"Celery任务 {self.request.id}: 尝试将会话 {session_id} 标记为错误时，会话未找到。")
        except Exception as e_sess_fail:
            logger.error(f"Celery任务 {self.request.id}: 更新会话 {session_id} 为错误状态时失败: {e_sess_fail}")
        return {"status": "error", "reason": f"User with ID {user_id} not found.", "session_id": session_id}

    session_manager = ImportSessionManager()

    try:
        try:
            raw_session = ImportSession.objects.get(session_id=session_id)
        except ImportSession.DoesNotExist:
            logger.error(f"Celery任务 {self.request.id}: 原始导入会话 {session_id} 未找到。任务终止。")
            return {"status": "error", "reason": "Import session not found.", "session_id": session_id}
        
        session_orm_object = session_manager._ensure_session_status_is_current(raw_session)

        if not session_orm_object:
            logger.error(f"Celery任务 {self.request.id}: 导入会话 {session_id} 在状态更新后无效或不存在。任务终止。")
            return {"status": "error", "reason": "Import session became invalid after status check.", "session_id": session_id}

        if session_orm_object.status != ImportSessionStatus.IMPORT_QUEUED:
            logger.warning(f"Celery任务 {self.request.id}: 会话 {session_id} 状态为 {session_orm_object.status}，不是 {ImportSessionStatus.IMPORT_QUEUED}。可能任务已处理或状态异常。任务终止。")
            return {"status": "skipped", "reason": f"Session status is {session_orm_object.status}, expected {ImportSessionStatus.IMPORT_QUEUED}.", "session_id": session_id}

        session_orm_object.status = ImportSessionStatus.IMPORT_START 
        session_orm_object.processing_user = None
        session_orm_object.progress = 5.0  
        session_orm_object.current_record = 0 
        session_orm_object.updated_at = timezone.now()
        session_orm_object.save(update_fields=['status', 'processing_user', 'progress', 'current_record', 'updated_at'])
        
        SessionOperation.objects.create(
            session=session_orm_object,
            operation_type='import_start_async',
            user=user, 
            old_status=ImportSessionStatus.IMPORT_QUEUED,
            new_status=session_orm_object.status,
            details={'message': 'Celery任务开始执行导入数据准备'}
        )

        try:
            resolutions_list_from_json = json.loads(resolutions_json)
        except json.JSONDecodeError as json_err:
            logger.error(f"Celery任务 {self.request.id}: 解析 resolutions_json 失败 for session {session_id}: {json_err}", exc_info=True)
            session_orm_object.status = ImportSessionStatus.ERROR
            session_orm_object.error_message = f"内部错误：无法解析导入决策数据 ({str(json_err)[:100]})"
            session_orm_object.updated_at = timezone.now()
            session_orm_object.save(update_fields=['status', 'error_message', 'updated_at'])
            raise Exception("Invalid resolutions format received by Celery task.") from json_err

        conflict_details_queryset = ImportConflictDetail.objects.filter(session=session_orm_object).order_by('excel_row_number')
        # CHANGE: [2025-06-20] 修复DRF序列化器重构后的to_dict()方法调用
        # 直接构建_execute_import_with_resolutions需要的字典结构
        conflict_details_data_for_exec = []
        for cd in conflict_details_queryset:
            conflict_details_data_for_exec.append({
                'commission_number': cd.commission_number,
                'conflict_type': cd.conflict_type,
                'excel_row_number': cd.excel_row_number,
                'existing_record_pk': cd.existing_record_pk,
            })

        resolution_map_for_exec = {}
        for res_item in resolutions_list_from_json:
            cn = res_item.get('commission_number')
            act = res_item.get('action')
            # ... (logic for updating ImportConflictDetail.user_resolution if needed) ...
            if cn and act:
                resolution_map_for_exec[cn] = act

        analysis_stats_from_json = json.loads(analysis_stats_json) if analysis_stats_json else {}
        user_decision_stats_from_json = json.loads(user_decision_stats_json) if user_decision_stats_json else {}
        
        final_analysis_stats = {
            "total_rows_read": analysis_stats_from_json.get("total_rows_read", 0),
            "failed_rows": analysis_stats_from_json.get("failed_rows", 0),
            "skipped_identical": analysis_stats_from_json.get("skipped_identical", 0),
            "found_new_count": analysis_stats_from_json.get("found_new_count", 0), 
            "found_update_count": analysis_stats_from_json.get("found_update_count", 0) 
        }
        final_user_decision_stats = {
            "skipped_update_count": user_decision_stats_from_json.get("skipped_update_count", 0),
            "confirmed_update_count": user_decision_stats_from_json.get("confirmed_update_count", 0)
        }

        import_log_instance = session_manager._execute_import_with_resolutions(
            db_session=session_orm_object, # Pass the ORM object
            user=user,
            conflict_records_data=conflict_details_data_for_exec, 
            resolution_map=resolution_map_for_exec,
            analysis_phase_stats=final_analysis_stats, 
            user_decision_phase_stats=final_user_decision_stats
        )

        if not import_log_instance or not hasattr(import_log_instance, 'status'):
            logger.error(f"Celery任务 {self.request.id}: _execute_import_with_resolutions 未返回有效 ImportLog 对象 for session {session_id}")
            session_orm_object.status = ImportSessionStatus.ERROR
            session_orm_object.error_message = "导入执行内部错误，未能生成导入日志。"
            session_orm_object.updated_at = timezone.now()
            session_orm_object.save(update_fields=['status', 'error_message', 'updated_at'])
            raise Exception("Import log generation failed internally.")

        # 根据 ImportLog 的状态决定最终会话状态 (NEW LOGIC FOR NEW STATES)
        if import_log_instance.status == 'completed':
            session_orm_object.status = ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY
            duration_minutes = getattr(settings, 'IMPORT_SUCCESS_DISPLAY_MINUTES', 5)
            session_orm_object.error_message = None # Clear previous errors if successful
        elif import_log_instance.status in ['failed', 'partial']:
            session_orm_object.status = ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS
            duration_minutes = getattr(settings, 'IMPORT_WITH_ERRORS_DISPLAY_MINUTES', 30)
            session_orm_object.error_message = f"导入完成但有错误/部分成功。详情见导入日志ID: {import_log_instance.id}"
        else: # Should not happen if ImportLog.status is one of the defined choices
            session_orm_object.status = ImportSessionStatus.ERROR 
            duration_minutes = getattr(settings, 'ERROR_SESSION_DISPLAY_MINUTES', 15)
            session_orm_object.error_message = f"导入日志状态未知 ({import_log_instance.status})，会话标记为错误。日志ID: {import_log_instance.id}"

        session_orm_object.import_log = import_log_instance
        if session_orm_object.status in [ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY, ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS]:
            session_orm_object.results_display_expires_at = timezone.now() + timedelta(minutes=duration_minutes)
        
        session_orm_object.processing_user = None 
        session_orm_object.last_heartbeat_at = None 
        session_orm_object.updated_at = timezone.now()
        
        update_fields_final = ['status', 'import_log', 'error_message', 'processing_user', 'last_heartbeat_at', 'updated_at', 'results_display_expires_at']
        session_orm_object.save(update_fields=list(set(update_fields_final)))
        
        SessionOperation.objects.create(
            session=session_orm_object,
            operation_type='async_import_finalized',
            user=user,
            new_status=session_orm_object.status,
            details={
                'import_log_id': str(import_log_instance.id) if import_log_instance else None,
                'final_log_status': import_log_instance.status if import_log_instance else 'unknown'
            }
        )
        logger.info(f"Celery任务 {self.request.id}: 会话 {session_id} 异步导入处理完成，最终状态: {session_orm_object.status}")
        return {"status": "success", "session_id": str(session_id), "import_log_id": str(import_log_instance.id if import_log_instance else None)}

    except OperationalError as oe_exc:
        logger.warning(f"Celery任务 {self.request.id} (会话 {session_id}): 发生数据库操作错误: {oe_exc}。将在 {self.default_retry_delay}s 后重试 ({self.request.retries + 1}/{self.max_retries})。", exc_info=True)
        if session_orm_object and session_orm_object.status != ImportSessionStatus.ERROR:
            session_orm_object.status = ImportSessionStatus.IMPORT_QUEUED 
            session_orm_object.error_message = f"导入时临时数据库操作错误，系统将自动重试: {str(oe_exc)[:200]}"
            session_orm_object.save(update_fields=['status', 'error_message', 'updated_at'])
        raise self.retry(exc=oe_exc, countdown=int(random.uniform(self.default_retry_delay / 2, self.default_retry_delay) * (self.request.retries + 1)))
    except Exception as e:
        logger.error(f"Celery任务 {self.request.id} (会话 {session_id}): 发生未知严重错误: {e}", exc_info=True)
        if session_orm_object: 
            try:
                session_orm_object.status = ImportSessionStatus.ERROR
                session_orm_object.error_message = f"异步导入任务执行失败: {str(e)[:1000]}"
                session_orm_object.save(update_fields=['status', 'error_message', 'updated_at'])
                SessionOperation.objects.create(
                    session=session_orm_object,
                    operation_type='async_import_failed', 
                    user=user, 
                    new_status=ImportSessionStatus.ERROR,
                    details={'error': str(e)[:500]}
                )
            except Exception as e_save:
                logger.error(f"Celery任务 {self.request.id}: 更新会话 {session_id} 状态为ERROR时再次失败: {e_save}", exc_info=True)
        return {"status": "error", "reason": str(e), "session_id": str(session_id)}

# (如果需要，在这里定义新的 process_finalized_sessions_task)

# CHANGE: [2025-05-28] 新增 process_finalized_sessions_task 用于统一清理 FINALIZED 状态的会话资源
@shared_task(bind=True, max_retries=2, default_retry_delay=300, name="archive_records.tasks.process_finalized_sessions_task")
def process_finalized_sessions_task(self):
    """
    定期任务：处理所有状态为 FINALIZED 且尚未清理资源的会话。
    执行物理文件删除、缓存清理等操作，并标记 cleaned_up_at 字段。
    """
    logger.info(f"[ProcessFinalizedSessions] 开始执行 FINALIZED 会话资源清理任务 (任务ID: {self.request.id})")
    
    try:
        # 查询所有 FINALIZED 状态且未清理的会话
        finalized_sessions = ImportSession.objects.filter(
            status=ImportSessionStatus.FINALIZED,
            cleaned_up_at__isnull=True
        ).order_by('updated_at')  # 按更新时间排序，优先处理较早的会话
        
        total_sessions = finalized_sessions.count()
        if total_sessions == 0:
            logger.info("[ProcessFinalizedSessions] 没有找到需要清理的 FINALIZED 会话。")
            return {"status": "success", "processed_count": 0, "message": "No sessions to clean"}
        
        logger.info(f"[ProcessFinalizedSessions] 找到 {total_sessions} 个需要清理的 FINALIZED 会话。")
        
        processed_count = 0
        error_count = 0
        
        for session in finalized_sessions:
            try:
                logger.info(f"[ProcessFinalizedSessions] 开始清理会话 {session.session_id} 的资源...")
                
                # 1. 清理物理文件
                files_deleted = []
                if session.file_path and os.path.exists(session.file_path):
                    try:
                        os.remove(session.file_path)
                        files_deleted.append(session.file_path)
                        logger.info(f"[ProcessFinalizedSessions] 已删除文件: {session.file_path}")
                    except OSError as e:
                        logger.warning(f"[ProcessFinalizedSessions] 删除文件 {session.file_path} 失败: {e}")
                
                # 2. 清理缓存数据 (冲突记录缓存)
                cache_keys_deleted = []
                cache_key_conflicts = f'excel_import_conflicts:{session.session_id}'
                if cache.get(cache_key_conflicts):
                    cache.delete(cache_key_conflicts)
                    cache_keys_deleted.append(cache_key_conflicts)
                    logger.info(f"[ProcessFinalizedSessions] 已清理缓存键: {cache_key_conflicts}")
                
                # 3. 可选：清理相关的 ImportConflictDetail 记录 (如果策略是删除而非保留)
                # 当前策略：保留 ImportConflictDetail 记录用于审计，不删除
                conflict_details_count = ImportConflictDetail.objects.filter(session=session).count()
                logger.info(f"[ProcessFinalizedSessions] 会话 {session.session_id} 有 {conflict_details_count} 条冲突详情记录 (保留用于审计)")
                
                # 4. 标记清理完成
                with transaction.atomic():
                    # 重新获取并锁定会话，确保状态一致性
                    session_to_mark = ImportSession.objects.select_for_update().get(pk=session.pk)
                    
                    # 再次确认状态仍为 FINALIZED (防止并发修改)
                    if session_to_mark.status != ImportSessionStatus.FINALIZED:
                        logger.warning(f"[ProcessFinalizedSessions] 会话 {session.session_id} 状态已变为 {session_to_mark.status}，跳过清理标记。")
                        continue
                    
                    session_to_mark.cleaned_up_at = timezone.now()
                    session_to_mark.save(update_fields=['cleaned_up_at', 'updated_at'])
                    
                    # 记录清理操作
                    SessionOperation.objects.create(
                        session=session_to_mark,
                        operation_type='system_resource_cleanup',
                        user=None,  # 系统操作
                        old_status=ImportSessionStatus.FINALIZED,
                        new_status=ImportSessionStatus.FINALIZED,  # 状态不变
                        details={
                            'files_deleted': files_deleted,
                            'cache_keys_deleted': cache_keys_deleted,
                            'conflict_details_preserved': conflict_details_count,
                            'cleanup_timestamp': timezone.now().isoformat()
                        }
                    )
                
                processed_count += 1
                logger.info(f"[ProcessFinalizedSessions] 会话 {session.session_id} 资源清理完成。")
                
            except ImportSession.DoesNotExist:
                logger.warning(f"[ProcessFinalizedSessions] 会话 {session.session_id} 在处理过程中被删除，跳过。")
                continue
            except Exception as e_session:
                error_count += 1
                logger.error(f"[ProcessFinalizedSessions] 清理会话 {session.session_id} 时发生错误: {e_session}", exc_info=True)
                # 继续处理下一个会话，不因单个会话错误而中断整个任务
                continue
        
        logger.info(f"[ProcessFinalizedSessions] 资源清理任务完成。处理成功: {processed_count}, 错误: {error_count}, 总计: {total_sessions}")
        
        return {
            "status": "success",
            "processed_count": processed_count,
            "error_count": error_count,
            "total_sessions": total_sessions
        }
        
    except Exception as e:
        logger.error(f"[ProcessFinalizedSessions] 执行资源清理任务时发生严重错误: {e}", exc_info=True)
        # 对于定期任务，通常不需要重试，记录错误即可
        # 如果需要重试，可以 raise self.retry(exc=e)
        return {
            "status": "error",
            "error_message": str(e),
            "processed_count": 0
        }