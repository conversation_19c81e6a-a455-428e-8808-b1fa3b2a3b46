from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from archive_records.models import (
    ArchiveRecord, ChangeLogBatch, 
    RecordChangeLog, FieldChangeLog
)
from archive_records.services.excel_import import ExcelImportService

class BaseVersionTest(TestCase):
    """版本测试基类，提供共用方法"""
    
    def setUp(self):
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='12345'
        )
        
        # 创建API客户端并认证
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 创建导入服务实例
        self.import_service = ExcelImportService()
    
    def _create_initial_version(self, record):
        """创建初始版本记录
        
        Args:
            record: 档案记录实例
            
        Returns:
            RecordChangeLog: 创建的版本记录
        """
        batch = ChangeLogBatch.objects.create(
            change_source='manual_edit',
            change_reason='创建初始版本',
            changed_by=self.user
        )
        
        # 记录初始状态（所有字段）
        record_after = {}
        for field in record._meta.fields:
            if not field.primary_key and field.name != 'id':
                record_after[field.name] = str(getattr(record, field.name))
        
        change_log = RecordChangeLog.objects.create(
            batch=batch,
            record=record,
            version_number=1,
            change_type='create',
            record_before=None,
            record_after=record_after,
            changed_fields_count=len(record_after)
        )
        
        # 为每个字段创建变更日志
        field_logs = []
        for field, value in record_after.items():
            field_logs.append(FieldChangeLog(
                record_change=change_log,
                field_name=field,
                field_label=self.import_service._get_field_label(field),
                old_value=None,
                new_value=value,
                field_importance=self.import_service._get_field_importance(field)
            ))
        
        FieldChangeLog.objects.bulk_create(field_logs)
        
        return change_log
    
    def _create_update_version(self, record, version_number, changes, reason):
        """创建更新版本记录
        
        Args:
            record: 档案记录实例
            version_number: 版本号
            changes: 字段变更字典 {字段名: 新值}
            reason: 变更原因
            
        Returns:
            RecordChangeLog: 创建的版本记录
        """
        batch = ChangeLogBatch.objects.create(
            change_source='manual_edit',
            change_reason=reason,
            changed_by=self.user
        )
        
        # 记录变更前的完整状态（所有字段）
        record_before = {}
        for field in record._meta.fields:
            if not field.primary_key and field.name != 'id':
                record_before[field.name] = str(getattr(record, field.name))
        
        # 只更新指定的字段
        for field, value in changes.items():
            setattr(record, field, value)
        record.save()
        
        # 记录变更后的完整状态（所有字段）
        record_after = {}
        for field in record._meta.fields:
            if not field.primary_key and field.name != 'id':
                record_after[field.name] = str(getattr(record, field.name))
        
        # 创建变更日志
        change_log = RecordChangeLog.objects.create(
            batch=batch,
            record=record,
            version_number=version_number,
            change_type='update',
            record_before=record_before,
            record_after=record_after,
            changed_fields_count=len(changes)
        )
        
        # 创建字段变更日志
        field_logs = []
        for field, new_value in changes.items():
            field_logs.append(FieldChangeLog(
                record_change=change_log,
                field_name=field,
                field_label=self.import_service._get_field_label(field),
                old_value=record_before.get(field, ''),
                new_value=new_value,
                field_importance=self.import_service._get_field_importance(field)
            ))
        
        FieldChangeLog.objects.bulk_create(field_logs)
        
        return change_log 