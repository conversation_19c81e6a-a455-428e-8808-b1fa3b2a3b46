"use client"

import { useEffect, useState } from 'react'

interface ClientOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * ClientOnly组件
 * 
 * 解决服务端渲染和客户端渲染不一致导致的hydration错误
 * 特别适用于日期、时间等在服务端和客户端可能不同的动态内容
 * 
 * @param children 仅在客户端渲染的内容
 * @param fallback 可选的服务端渲染占位内容
 */
export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return fallback
  }

  return <>{children}</>
}

export default ClientOnly 