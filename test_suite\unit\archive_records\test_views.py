"""
API视图测试模块
"""
import os
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from django.contrib.auth.models import User
from archive_records.models import ArchiveRecord, ImportLog
from test_suite.utils.test_helpers import get_test_file_path
from archive_records.services import ExcelImportService

class ExcelImportViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(username='testuser', password='12345')
        self.client.force_authenticate(user=self.user)
        
        self.test_file_path = get_test_file_path('excel', 'valid_data.xlsx')

    def test_excel_import(self):
        """测试Excel文件导入"""
        self.assertTrue(
            os.path.exists(self.test_file_path),
            f"测试文件不存在: {self.test_file_path}"
        )
        
        with open(self.test_file_path, 'rb') as file:
            response = self.client.post(
                reverse('import_excel'),
                {'file': file},
                format='multipart'
            )
        
        # API应该总是返回200，即使导入失败
        self.assertEqual(response.status_code, 200)
        self.assertIn('import_log', response.data)
        
        # 打印导入结果（方便调试）
        import_log = response.data['import_log']
        print(f"\n导入状态: {import_log['status']}")
        print(f"总记录数: {import_log['total_records']}")
        print(f"成功记录数: {import_log['success_records']}")
        print(f"失败记录数: {import_log['failed_records']}") 