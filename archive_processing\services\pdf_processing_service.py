"""
提供 PDF 处理的核心服务，专注于信息提取（分割点、统一编号）。
不负责物理文件的分割或归档。
"""

import logging
import time
import gc
import fitz # PyMuPDF
from typing import Optional, Dict, List, Tuple, Callable
from PIL import Image

from django.conf import settings

# 导入 DTOs
from archive_processing.dto.processing_dtos import ProcessingResultDto, ProcessingStats
from archive_processing.dto.pdf_dtos import PDFPageImage, PDFPartRange
from archive_processing.dto.result_dtos import UnifiedNumberResult

# 导入工具模块
from archive_processing.utils import ocr_utils, text_utils, image_utils, system_utils

logger = logging.getLogger(__name__)

class PdfProcessingService:
    """处理 PDF 文件以提取分割信息和统一编号的服务类。"""

    def __init__(self, config: Optional[Dict] = None):
        """初始化服务，加载配置。"""
        self.config = config or {}
        # 从配置或 settings 加载参数，提供默认值
        self.dpi = int(self.config.get('dpi', getattr(settings, 'PDF_PROCESSING_DPI', 150)))
        self.crop_ratio = float(self.config.get('crop_ratio', getattr(settings, 'PDF_CROP_RATIO', 0.2)))
        self.batch_size = int(self.config.get('batch_size', getattr(settings, 'PDF_BATCH_SIZE', 5)))
        self.case_sensitive = self.config.get('case_sensitive', getattr(settings, 'PDF_TEXT_CASE_SENSITIVE', False))
        self.ignore_punctuation = self.config.get('ignore_punctuation', getattr(settings, 'PDF_TEXT_IGNORE_PUNCTUATION', True))
        self.use_paddle_ocr = self.config.get('use_paddle_ocr', getattr(settings, 'PDF_USE_PADDLE_OCR', True))

        self.cpu_type = self.config.get('cpu_optimization') or system_utils.detect_cpu_type()
        self.max_attempts = int(self.config.get('max_attempts', getattr(settings, 'PDF_MAX_ATTEMPTS', 4)))
        
        # 不在此处初始化 OCR 引擎或缓存，在处理方法中按需进行
        self.paddle_ocr = None 

        
        logger.info(f"PdfProcessingService 初始化完成，配置: DPI={self.dpi}, CropRatio={self.crop_ratio}, BatchSize={self.batch_size}, UsePaddle={self.use_paddle_ocr}")

    def process_pdf_for_splitting_info(
        self,
        pdf_path: str,
        target_text: str,
        progress_callback: Optional[Callable] = None
    ) -> ProcessingResultDto:
        """
        处理 PDF，查找分割点并识别关联的统一编号。
        返回包含处理结果的 DTO，但不执行物理文件分割。

        Args:
            pdf_path: 要处理的 PDF 文件路径。
            target_text: 用于查找分割点的目标文本。
            progress_callback: 进度回调函数。

        Returns:
            ProcessingResultDto: 包含处理结果的数据对象。
        """
        gc.collect()
        start_time = time.time()
        
        # 初始化本次处理的状态和结果变量
        stats = ProcessingStats(total_pages=0, processed_pages=0, matched_pages=0, processing_time=0, memory_usage={})

        # 不再需要临时实例变量来存储结果
        # split_points = []
        # unified_numbers = {}    
        # recognition_stats = {}  
        # self.unified_numbers = {} 
        # self.recognition_stats = {} 
        # self.stats = stats 

        doc = None
        try:
            # 1. 按需初始化 PaddleOCR 引擎
            if self.use_paddle_ocr:
                if not self.paddle_ocr: # 延迟初始化
                     self.paddle_ocr = ocr_utils.init_paddle_ocr(use_paddle=True)
                     if self.paddle_ocr is None:
                         self.use_paddle_ocr = False # 更新标志
            else:
                 self.paddle_ocr = None # 确保引擎实例为空

            # 2. 打开 PDF 并获取总页数
            logger.info(f"开始处理 PDF: {pdf_path}")
            doc = fitz.open(pdf_path)
            stats.total_pages = len(doc)
            # 不再需要更新 self.stats.total_pages

            # 3. 查找分割点并识别编号 (调用重构后的方法)
            split_points, unified_numbers, recognition_stats, total_processed_pages, total_matched_pages = self._find_split_points(doc, target_text, progress_callback)
            
            # 4. 更新 stats 对象
            stats.processed_pages = total_processed_pages
            stats.matched_pages = total_matched_pages

            logger.info(f"查找分割点完成，找到 {len(split_points)} 个分割点。")
            logger.info(f"识别到 {len(unified_numbers)} 个统一编号。")
            if recognition_stats:
                 logger.info(f"识别方法统计: {recognition_stats}")

            # 5. 计算最终统计数据
            stats.processing_time = time.time() - start_time
            if stats.processing_time > 0 and stats.total_pages > 0:
                stats.pages_per_second = stats.total_pages / stats.processing_time
            else:
                stats.pages_per_second = 0
            logger.info(f"PDF 信息提取完成，总耗时: {system_utils.format_time(stats.processing_time)}, 平均速度: {stats.pages_per_second:.2f} 页/秒")

            # 6. 返回成功结果 DTO
            return ProcessingResultDto(
                success=True,
                split_points=split_points,
                unified_numbers=unified_numbers,
                stats=stats,
                recognition_stats=recognition_stats, # 传递识别方法统计
                error_message=None 
            )

        except Exception as e:
            logger.error(f"PDF 处理失败: {e}", exc_info=True)
            processing_time_on_error = time.time() - start_time
            stats.processing_time = processing_time_on_error # 记录出错时的耗时
            # 返回失败结果 DTO
            return ProcessingResultDto(
                success=False,
                split_points=[],
                unified_numbers={}, 
                stats=stats, # 返回处理到出错前的统计信息
                error_message=str(e)
            )
        finally:
            # 清理资源
            if doc:
                doc.close()
                doc = None
            # 不再需要重置实例属性
            # self.unified_numbers = {}
            # self.recognition_stats = {}
            # self.stats = None 
            gc.collect()

    # --- 以下为从 PDFProcessor 迁移过来的辅助方法 (已基本重构) ---
    
    def _find_split_points(self, 
                           doc: fitz.Document, 
                           target_text: str,
                           progress_callback: Optional[Callable] = None
                           ) -> Tuple[List[int], Dict[int, Optional[str]], Dict[str, int], int, int]:
        """
        查找PDF中包含目标文本的页面，并聚合识别结果和统计。
        
        Args:
            doc: PDF文档对象
            target_text: 目标文本
            progress_callback: 进度回调函数。
        
        Returns:
            元组: (all_split_points, all_unified_numbers, all_recognition_stats, 
                  total_processed_pages, total_matched_pages)
        """
        all_split_points = []
        all_unified_numbers = {}
        all_recognition_stats = {}
        total_processed_pages = 0
        total_matched_pages = 0
        
        # 从文档获取总页数
        total_pages = len(doc)
        batch_size = self.batch_size
        
        try:
            # 处理目标文本
            processed_target = text_utils.preprocess_text(target_text, self.case_sensitive, self.ignore_punctuation)
            logger.info(f"查找目标文本: '{target_text}', 处理后: '{processed_target}'")
            
            # processing_start = time.time() # 总处理时间由上层计算
            
            for batch_start in range(0, total_pages, batch_size):
                batch_end = min(batch_start + batch_size, total_pages)
                
                logger.info(f"处理批次: 页面 {batch_start+1}-{batch_end} / {total_pages}")
                
                # 调用重构后的 _process_page_batch，接收返回的元组
                batch_points, batch_unified_numbers, batch_recognition_stats, processed_in_batch, matched_in_batch = self._process_page_batch(
                    doc, 
                    batch_start, 
                    batch_end, 
                    processed_target
                )
                
                # 聚合结果
                all_split_points.extend(batch_points)
                all_unified_numbers.update(batch_unified_numbers) # 合并字典
                total_processed_pages += processed_in_batch
                total_matched_pages += matched_in_batch
                
                # 合并识别方法统计增量
                for method, increment in batch_recognition_stats.items():
                    all_recognition_stats[method] = all_recognition_stats.get(method, 0) + increment
                
                if progress_callback:
                    progress_callback(batch_end, total_pages)
                
                # 在批次循环后触发垃圾回收
                gc.collect()
                
            logger.info(f"查找过程完成，共处理 {total_processed_pages} 页，匹配 {total_matched_pages} 页，找到 {len(all_split_points)} 个分割点")
            return all_split_points, all_unified_numbers, all_recognition_stats, total_processed_pages, total_matched_pages
            
        except Exception as e:
            logger.error(f"查找分割点失败: {e}", exc_info=True)
            # 返回当前已处理的结果和空值，让上层判断如何处理
            # 或者直接向上抛出异常
            raise # 让 process_pdf_for_splitting_info 捕获并返回错误 DTO

    def _process_page_batch(self, 
                           doc: fitz.Document, 
                           start_page: int, 
                           end_page: int, 
                           processed_target: str
                           ) -> Tuple[List[int], Dict[int, Optional[str]], Dict[str, int], int, int]:
        """
        处理一批PDF页面，返回批次结果（分割点、编号、统计）和处理统计。
        
        Args:
            doc: PDF文档对象
            start_page: 开始页码
            end_page: 结束页码
            processed_target: 处理后的目标文本
        
        Returns:
            元组: (batch_split_points, batch_unified_numbers, batch_recognition_stats, 
                  processed_in_batch, matched_in_batch)
        """
        batch_split_points = []
        batch_unified_numbers = {}
        batch_recognition_stats = {}
        processed_in_batch = 0
        matched_in_batch = 0
        
        batch_start_time = time.time()
        # total_pages = self.stats.total_pages # 不再需要从 self.stats 读取

        for i, page_num in enumerate(range(start_page, end_page)):
            # progress = (i + 1) / batch_size * 100
            # elapsed = time.time() - batch_start_time
            # pages_per_sec = (i + 1) / elapsed if elapsed > 0 else 0
            # 移除进度显示逻辑，可以在更高层实现
            
            processed_in_batch += 1 # 记录处理的页数
            
            try:
                page_image_dto = self._get_page_crop(doc, page_num)
                if not page_image_dto or not page_image_dto.image:
                    logger.warning(f"无法获取或裁剪第 {page_num+1} 页图像，跳过")
                    continue
                    
                page_image = page_image_dto.image

                # 执行基础OCR (调用 ocr_utils)
                ocr_text = ocr_utils.perform_basic_ocr(
                    image=page_image,
                    engine="auto",
                    paddle_engine=self.paddle_ocr,
                    use_paddle=self.use_paddle_ocr,
                    page_num=page_num,
                    allow_tesseract_fallback=False  # 禁用Tesseract回退，确保精度
                )
                
                # 处理识别文本 (调用 text_utils)
                processed_ocr = text_utils.preprocess_text(ocr_text, self.case_sensitive, self.ignore_punctuation)
                
                # 检查文本匹配 (调用 text_utils)
                is_match, match_type = text_utils.is_text_match(processed_ocr, processed_target)
                
                if is_match:
                    is_confirmed_match = False
                    if match_type == "exact":
                        is_confirmed_match = True
                        logger.info(f"在第{page_num+1}页完全匹配到目标文本")
                    elif match_type == "fuzzy":
                        logger.info(f"在第{page_num+1}页发现潜在匹配，进行深度检测...")
                        # 调用实例方法 _perform_deep_detection
                        if self._perform_deep_detection(page_image, processed_target):
                            is_confirmed_match = True
                            logger.info(f"深度检测确认匹配")
                        else:
                            logger.info(f"深度检测未确认匹配")
                    
                    # 如果确认匹配，记录分割点并尝试识别编号
                    if is_confirmed_match:
                        batch_split_points.append(page_num)
                        matched_in_batch += 1 # 记录匹配的页数
                        
                        # 调用重构后的增强识别方法，接收返回结果
                        best_number, stats_increment = self._enhance_unified_number_recognition(
                            page_num, ocr_text, page_image, processed_target
                        )
                        
                        # 将结果存入批次字典
                        if best_number is not None:
                            batch_unified_numbers[page_num] = best_number
                        # 合并识别方法统计增量
                        for method, increment in stats_increment.items():
                             batch_recognition_stats[method] = batch_recognition_stats.get(method, 0) + increment

            except Exception as page_err:
                 logger.error(f"处理第 {page_num+1} 页时出错: {page_err}", exc_info=True)
                 # 严格模式：OCR失败时中断任务，不继续处理
                 raise
            finally:
                 # 确保每次循环后清理图像对象 (如果需要)
                 page_image = None
                 # 避免在内部循环频繁GC，可在 find_split_points 的批次循环后调用
                 # gc.collect() 

        # 更新处理统计 (不再修改 self.stats)
        # processed_in_batch = end_page - start_page # 已在循环中累加
        # matched_in_batch = len(batch_split_points) # 已在循环中累加
        
        # 计算和显示批处理速度 (移到日志)
        batch_elapsed = time.time() - batch_start_time
        if batch_elapsed > 0:
            batch_speed = processed_in_batch / batch_elapsed
            logger.info(f"批次 {start_page+1}-{end_page} 处理完成，耗时: {system_utils.format_time(batch_elapsed)}, 速度: {batch_speed:.2f} 页/秒, 匹配: {matched_in_batch} 页")
        else:
             logger.info(f"批次 {start_page+1}-{end_page} 处理完成 (耗时 < 0)，匹配: {matched_in_batch} 页")

        # 返回批次聚合结果
        return batch_split_points, batch_unified_numbers, batch_recognition_stats, processed_in_batch, matched_in_batch

    def _enhance_unified_number_recognition(self, 
                                           page_num: int, 
                                           original_ocr: str, 
                                           original_image: Image.Image,
                                           target_text: str) -> Tuple[Optional[str], Dict[str, int]]:
        """增强统一编号识别，返回最佳编号和统计增量。"""
        logger.debug(f"开始对第 {page_num+1} 页进行增强编号识别...")
        results: List[UnifiedNumberResult] = []
        stats_increment = {}

        # 1. 尝试从基础OCR结果提取
        basic_number = text_utils.extract_unified_number(original_ocr, target_text)
        results.append(UnifiedNumberResult(basic_number, "基础识别"))
        
        # 2. 尝试使用增强OCR识别
        enhanced_results = ocr_utils.perform_enhanced_ocr(
            image=original_image, 
            use_paddle=self.use_paddle_ocr, 
            paddle_engine=self.paddle_ocr, # 使用实例属性
            target_text=target_text,
            extract_number=True, 
            max_attempts=self.max_attempts, 
            collect_all_results=True,
            allow_tesseract_fallback=False  # 禁用Tesseract回退，确保精度
        )

        
        # 3. 将增强结果添加到候选列表
        results.extend(enhanced_results)
        
        # 4. 对所有结果进行排序和选择 (调用 text_utils)
        best_number = text_utils.choose_best_number(results)
        
        # 5. 如果选出最佳编号，计算统计增量
        if best_number:
            final_methods = [r.method for r in results if r.number == best_number]
            if final_methods:
                counted_methods = set()
                for method in final_methods:
                    if method not in counted_methods:
                        stats_increment[method] = stats_increment.get(method, 0) + 1
                        counted_methods.add(method)
            else: 
                # 理论上不应发生，但保留处理
                stats_increment["未知增强方法"] = stats_increment.get("未知增强方法", 0) + 1
        
        # 清理 page_image 引用 (帮助 GC)
        page_image = None 
        
        # 返回结果
        return best_number, stats_increment

    def _get_page_crop(self, doc: fitz.Document, page_num: int) -> Optional[PDFPageImage]:
        """获取PDF页面的裁剪区域图像 (基本不变)"""
        page = None 
        try:
            page = doc.load_page(page_num) 
            rect = page.rect
            # 使用实例属性 crop_ratio, dpi
            crop_height = rect.height * self.crop_ratio
            crop_rect = fitz.Rect(rect.x0, rect.y0, rect.x1, rect.y0 + crop_height)
            zoom = self.dpi / 72.0
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat, clip=crop_rect, alpha=False) 
            
            if pix.n != 3: 
                 logger.warning(f"Pixmap for page {page_num+1} is not RGB (components={pix.n}). Attempting conversion.")
                 try:
                     rgb_pix = fitz.Pixmap(fitz.csRGB, pix.irect, False) 
                     rgb_pix.copy(pix, pix.irect) 
                     pix = rgb_pix
                 except Exception as conv_err:
                     logger.error(f"Failed to convert pixmap on page {page_num+1} to RGB: {conv_err}")
                     return None # 返回 None 表示失败

            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            return PDFPageImage(page_num=page_num, image=img)
            
        except Exception as e:
            logger.error(f"获取第{page_num+1}页裁剪图像失败: {e}", exc_info=True)
            return None # 返回 None 表示失败
        finally:
            if page: 
                 page = None 
            pix = None

    def _perform_deep_detection(self, image: Image.Image, target_text: str) -> bool:
        """执行深度检测 (基本不变，调用 ocr_utils)"""
        return ocr_utils.perform_enhanced_ocr(
            image=image,
            use_paddle=self.use_paddle_ocr,
            paddle_engine=self.paddle_ocr,
            target_text=target_text,
            extract_number=False,
            max_attempts=self.max_attempts,
            collect_all_results=False,
            allow_tesseract_fallback=False  # 禁用Tesseract回退，确保精度
        )


# --- END PdfProcessingService ---
