"use client"

import type React from "react"

import { useState } from "react"
import { Calendar, User, Building, Phone, FileText, Clock, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { type ReportDistribution } from "../../../../../../hooks/domain/issue/use-report-distribution"

// 格式化日期时间的辅助函数
function formatDateTime(date: Date | string | null | undefined): string {
  if (!date) return ""
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return date.toString()

    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    }).format(dateObj)
  } catch (error) {
    return date.toString()
  }
}

interface ReportBasicInformationProps {
  reportDistribution: ReportDistribution
  isNewReport: boolean
  isEditable: boolean
  canPrint: boolean
  isProcessingReport: boolean
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  printReport: () => void
}

export function ReportBasicInformation({
  reportDistribution,
  isNewReport,
  isEditable,
  canPrint,
  isProcessingReport,
  handleInputChange,
  printReport,
}: ReportBasicInformationProps) {
  // 发放日期相关的状态和函数已删除，因为发放日期由后端自动生成

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 发放单信息卡片 */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="bg-gray-50 border-b py-2">
            <CardTitle className="flex items-center text-lg font-medium text-gray-800">
              <FileText className="mr-2 h-5 w-5 text-gray-600" />
              发放单信息
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* 发放单ID (隐藏) */}
              <div className="hidden">
                <Label className="flex items-center text-gray-500 mb-2">
                  <FileText className="mr-2 h-4 w-4" />
                  发放单ID
                </Label>
                <div className="pl-6 font-medium max-w-[300px] truncate">
                  {reportDistribution.id}
                </div>
              </div>

              {/* 发放单业务编号 */}
              <div>
                <Label htmlFor="issueNumber" className="flex items-center text-gray-500 mb-2">
                  <FileText className="mr-2 h-4 w-4" />
                  发放单编号
                </Label>
                <div className="pl-6">
                  <Input
                    id="issueNumber"
                    name="issueNumber"
                    value={reportDistribution.issueNumber || ""}
                    readOnly
                    className="font-medium bg-gray-100 cursor-not-allowed"
                  />
                </div>
              </div>

              {/* 创建人 */}
              <div>
                <Label className="flex items-center text-gray-500 mb-2">
                  <User className="mr-2 h-4 w-4" />
                  创建人
                </Label>
                <div className="pl-6 font-medium">{reportDistribution.createdBy}</div>
              </div>

              {/* 创建时间 */}
              <div>
                <Label className="flex items-center text-gray-500 mb-2">
                  <Calendar className="mr-2 h-4 w-4" />
                  创建时间
                </Label>
                <div className="pl-6 font-medium">{formatDateTime(reportDistribution.createdAt)}</div>
              </div>

              {/* 发放时间 - 由系统自动生成 */}
              {reportDistribution.issuedAt && (
                <div>
                  <Label className="flex items-center text-gray-500 mb-2">
                    <Clock className="mr-2 h-4 w-4" />
                    发放时间
                  </Label>
                  <div className="pl-6 font-medium">{formatDateTime(reportDistribution.issuedAt)}</div>
                </div>
              )}

              {/* 发放人 */}
              {reportDistribution.issuedBy && (
                <div>
                  <Label className="flex items-center text-gray-500 mb-2">
                    <User className="mr-2 h-4 w-4" />
                    发放人
                  </Label>
                  <div className="pl-6 font-medium">{reportDistribution.issuedBy}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 领取人信息卡片 */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="bg-gray-50 border-b py-2">
            <CardTitle className="flex items-center text-lg font-medium text-gray-800">
              <User className="mr-2 h-5 w-5 text-gray-600" />
              领取人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* 领取单位 */}
              <div>
                <Label htmlFor="receiverUnit" className="flex items-center text-gray-500 mb-2">
                  <Building className="mr-2 h-4 w-4" />
                  领取单位 <span className="text-red-500 ml-1">*</span>
                </Label>
                <div className="pl-6">
                  {isEditable ? (
                    <Input
                      id="receiverUnit"
                      name="receiverUnit"
                      placeholder="请输入领取单位"
                      value={reportDistribution.receiverUnit || ""}
                      onChange={handleInputChange}
                      required
                      className="focus:ring-2 focus:ring-blue-500"
                    />
                  ) : (
                    <div className="font-medium">{reportDistribution.receiverUnit}</div>
                  )}
                </div>
              </div>

              {/* 领取人 */}
              <div>
                <Label htmlFor="receiverName" className="flex items-center text-gray-500 mb-2">
                  <User className="mr-2 h-4 w-4" />
                  领取人 <span className="text-red-500 ml-1">*</span>
                </Label>
                <div className="pl-6">
                  {isEditable ? (
                    <Input
                      id="receiverName"
                      name="receiverName"
                      placeholder="请输入领取人姓名"
                      value={reportDistribution.receiverName || ""}
                      onChange={handleInputChange}
                      required
                      className="focus:ring-2 focus:ring-blue-500"
                    />
                  ) : (
                    <div className="font-medium">{reportDistribution.receiverName}</div>
                  )}
                </div>
              </div>

              {/* 联系电话 */}
              <div>
                <Label htmlFor="receiverPhone" className="flex items-center text-gray-500 mb-2">
                  <Phone className="mr-2 h-4 w-4" />
                  联系电话 <span className="text-red-500 ml-1">*</span>
                </Label>
                <div className="pl-6">
                  {isEditable ? (
                    <Input
                      id="receiverPhone"
                      name="receiverPhone"
                      placeholder="请输入联系电话"
                      value={reportDistribution.receiverPhone || ""}
                      onChange={handleInputChange}
                      required
                      className="focus:ring-2 focus:ring-blue-500"
                    />
                  ) : (
                    <div className="font-medium">{reportDistribution.receiverPhone}</div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 备注信息卡片 */}
      <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
        <CardHeader className="bg-gray-50 border-b py-2">
          <CardTitle className="flex items-center text-lg font-medium text-gray-800">
            <Info className="mr-2 h-5 w-5 text-gray-600" />
            备注信息
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div>
            <Label htmlFor="notes" className="flex items-center text-gray-500 mb-2">
              <FileText className="mr-2 h-4 w-4" />
              备注
            </Label>
            <div className="pl-6">
              {isEditable ? (
                <Textarea
                  id="notes"
                  name="notes"
                  placeholder="请输入备注信息（可选）"
                  value={reportDistribution.notes || ""}
                  onChange={handleInputChange}
                  rows={4}
                  className="focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <div className="min-h-[100px] p-3 bg-gray-50 rounded-md">
                  {reportDistribution.notes || "无备注"}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 打印按钮 */}
      {canPrint && (
        <div className="flex justify-end">
          <Button
            onClick={printReport}
            disabled={isProcessingReport}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isProcessingReport ? "处理中..." : "打印发放单"}
          </Button>
        </div>
      )}
    </div>
  )
}
