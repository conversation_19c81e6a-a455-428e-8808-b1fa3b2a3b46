import { QueryClient } from '@tanstack/react-query'

// 创建QueryClient实例，配置默认选项
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 数据被认为是新鲜的时间（5分钟）
      staleTime: 5 * 60 * 1000,
      // 缓存时间（10分钟）
      gcTime: 10 * 60 * 1000,
      // 窗口重新获得焦点时不自动重新获取
      refetchOnWindowFocus: false,
      // 网络重新连接时不自动重新获取
      refetchOnReconnect: false,
      // 重试次数
      retry: 1,
      // 重试延迟
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // 变更重试次数
      retry: 1,
    },
  },
}) 