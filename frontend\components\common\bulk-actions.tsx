"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ChevronDown, Loader2 } from "lucide-react"
import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

interface Action {
  label: string
  icon?: React.ReactNode
  action: () => Promise<void>
  variant?: "default" | "destructive"
}

interface BulkActionsProps {
  selectedIds: string[]
  actions: Action[]
  disabled?: boolean
  className?: string
}

export function BulkActions({ selectedIds, actions, disabled = false, className }: BulkActionsProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentActionIndex, setCurrentActionIndex] = useState<number | null>(null)
  const { toast } = useToast()

  const handleAction = async (action: Action, index: number) => {
    if (selectedIds.length === 0) {
      toast({
        title: "未选择项目",
        description: "请至少选择一个项目进行操作",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setCurrentActionIndex(index)

    try {
      await action.action()
    } catch (error) {
      toast({
        title: "操作失败",
        description: "执行批量操作时发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setCurrentActionIndex(null)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={className} disabled={disabled || isProcessing || selectedIds.length === 0}>
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              处理中...
            </>
          ) : (
            <>
              批量操作 ({selectedIds.length})
              <ChevronDown className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {actions.map((action, index) => (
          <DropdownMenuItem
            key={index}
            onClick={() => handleAction(action, index)}
            disabled={isProcessing}
            className={`flex items-center ${action.variant === "destructive" ? "text-red-600" : ""}`}
          >
            {action.icon && <span className="mr-2">{action.icon}</span>}
            <span>{action.label}</span>
            {isProcessing && currentActionIndex === index && <Loader2 className="ml-2 h-3 w-3 animate-spin" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
