# AG Grid 集中配置使用指南

## 目录

1. [文档说明与适用范围](#1-文档说明与适用范围)
2. [集中配置系统概述](#2-集中配置系统概述)
3. [初始化与全局配置](#3-初始化与全局配置)
   3.1 [初始化和许可证管理] (#31-初始化和许可证管理)
   3.2 [配置对象导入与使用] (#32-配置对象导入与使用)
4. [行模型选择与配置](#4-行模型选择与配置)
   4.1 [客户端行模型 (Client-Side Row Model)](#41-客户端行模型-client-side-row-model)
   4.2 [服务器端行模型 (Server-Side Row Model)](#42-服务器端行模型-server-side-row-model)
5. [表格原位编辑功能](#5-表格原位编辑功能)
6. [混合模式与多表格场景](#6-混合模式与多表格场景)
7. [性能优化最佳实践](#7-性能优化最佳实践)
8. [错误拦截功能](#8-错误拦截功能)
9. [主题系统与自定义主题](#9-主题系统与自定义主题)
   9.1 [主题配置概述](#91-主题配置概述)
   9.2 [新版 Theming API 使用方法](#92-新版-theming-api-使用方法)
   9.3 [从传统方式迁移到新版主题系统](#93-从传统方式迁移到新版主题系统)
   9.4 [主题自定义扩展](#94-主题自定义扩展)
   9.5 [主题最佳实践与常见问题](#95-主题最佳实践与常见问题)
   9.6 [故障排除](#96-故障排除)
10. [常见问题解答（FAQ）](#10-常见问题解答faq)
11. [维护与更新](#11-维护与更新)

> **提示**：本文档专注于项目特定的AG Grid集中配置系统。如需了解AG Grid的基本集成知识和应用经验，请参考[AG Grid集成与应用指南](./ag_grid_integration_guide.md)。

## 1. 文档说明与适用范围

本文档说明了项目中的 AG Grid 集中配置体系，以及如何在组件中正确使用这些配置。

## 2. 集中配置系统概述

AG Grid 集中配置系统位于 `frontend/lib/ag-grid-config.ts`，它提供了：

- 集中的许可证管理
- 统一的模块注册
- 可复用的默认配置对象
- 标准化的主题和UI配置
- 集成的错误拦截处理
- 性能优化默认配置

## 3. 初始化与全局配置

### 3.1 初始化和许可证管理

```typescript
import { initializeAgGrid } from '@/lib/ag-grid-config';

// 应用入口处调用一次
initializeAgGrid();
```

这个函数会自动完成以下工作：

- 设置 AG Grid Enterprise 许可证密钥
- 注册所有必要的 AG Grid 模块
- 设置错误拦截，屏蔽无效许可证相关错误
- 在开发环境中提供诊断日志（如果启用了调试）

**注意**：`initializeAgGrid()` 只应在应用入口点（如 `client-layout.tsx`）调用一次。该函数内置了防重复初始化机制，但仍应避免在多个位置调用。

### 3.2 配置对象导入和使用

```typescript
import agGridConfig from '@/lib/ag-grid-config';

// 使用预定义的默认列定义
const defaultColDef = useMemo(() => ({
  ...agGridConfig.defaultColDef,
  // 可以添加或覆盖组件特定的设置
}), []);

// 使用预定义的侧边栏配置
const sideBarConfig = useMemo(() => agGridConfig.sideBarConfig, []);

// 使用主题名称
<div className={agGridConfig.themes.quartz}>
  <AgGridReact {...props} />
</div>
```

## 4. 行模型选择与配置

### 4.1 客户端行模型 (Client-Side Row Model)

客户端行模型适用于以下场景：

- 数据量较小（通常少于10,000行）
- 所有数据都需要一次性加载到浏览器中
- 需要在客户端执行本地筛选、排序和分组

#### 使用客户端行模型配置

```typescript
import agGridConfig from '@/lib/ag-grid-config';
import { AgGridReact } from 'ag-grid-react';
import { useMemo, useState } from 'react';

export function ClientSideGridExample() {
  // 表格数据
  const [rowData, setRowData] = useState([
    { id: 1, name: '张三', age: 25 },
    { id: 2, name: '李四', age: 30 },
    { id: 3, name: '王五', age: 35 }
  ]);
  
  // 列定义
  const columnDefs = useMemo(() => [
    { field: 'id', headerName: '编号' },
    { field: 'name', headerName: '姓名' },
    { field: 'age', headerName: '年龄' }
  ], []);
  
  // 使用集中配置的默认列定义
  const defaultColDef = useMemo(() => agGridConfig.defaultColDef, []);
  
  return (
    <div className={agGridConfig.themes.quartz} style={{ height: 400 }}>
      <AgGridReact
        // 客户端模式配置
        rowData={rowData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        
        // 使用客户端模式默认配置
        pagination={agGridConfig.clientSideDefaults.pagination}
        paginationPageSize={agGridConfig.clientSideDefaults.paginationPageSize}
        
        // 可选：明确指定客户端模式（默认就是客户端模式，可省略）
        rowModelType="clientSide"
        
        // 应用性能优化配置
        {...agGridConfig.performanceConfig}
      />
    </div>
  );
}
```

### 4.2 服务器端行模型 (Server-Side Row Model)

服务器端行模型适用于以下场景：

- 数据量大（10,000行以上）
- 需要按需加载数据（滚动加载/分页）
- 筛选和排序在服务器端执行
- 需要与后端API交互获取数据

#### 服务器端模型缓存配置

服务器端模型使用缓存策略来优化数据加载和用户体验。当前的默认配置为：

```typescript
serverSideDefaults: {
    cacheBlockSize: 200,    // 每次请求加载的行数
    maxBlocksInCache: 50,   // 最大缓存块数 (200 * 50 = 10000条记录)
    debug: process.env.NODE_ENV === 'development',
    rowBuffer: 10           // 缓存行数，提高滚动性能
}
```

这意味着：

- 每次从服务器请求200行数据
- 最多缓存50个数据块，总共10000行
- 当超出最大缓存限制时，最旧的数据块将被丢弃
- 滚动时会保持10行的缓冲区以提高滚动流畅度

**调整缓存大小注意事项**：

- 减小`cacheBlockSize`可以减少每次请求的数据量，但会增加请求频率
- 增加`maxBlocksInCache`可以缓存更多数据，但会增加内存占用
- 对于带宽有限但内存充足的环境，可以减小`cacheBlockSize`并增加`maxBlocksInCache`
- 对于带宽充足但内存有限的环境，可以增加`cacheBlockSize`并减小`maxBlocksInCache`

#### 使用服务器端行模型配置

```typescript
import agGridConfig from '@/lib/ag-grid-config';
import { AgGridReact } from 'ag-grid-react';
import { useMemo, useCallback } from 'react';
import { GridReadyEvent, IServerSideDatasource } from 'ag-grid-enterprise';

export function ServerSideGridExample() {
  // 列定义
  const columnDefs = useMemo(() => [
    { field: 'id', headerName: '编号' },
    { field: 'name', headerName: '姓名' },
    { field: 'age', headerName: '年龄' }
  ], []);
  
  // 使用集中配置的默认列定义
  const defaultColDef = useMemo(() => agGridConfig.defaultColDef, []);
  
  // 创建服务器端数据源
  const createServerSideDatasource = useCallback((): IServerSideDatasource => {
    return {
      getRows: async (params) => {
        try {
          // 实现获取数据的逻辑...
          const response = await fetch('/api/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(params.request),
          });
          
          const result = await response.json();
          
          params.success({
            rowData: result.rows,
            rowCount: result.lastRow
          });
        } catch (error) {
          params.fail();
        }
      }
    };
  }, []);
  
  // Grid就绪事件处理
  const onGridReady = useCallback((params: GridReadyEvent) => {
    const datasource = createServerSideDatasource();
    params.api.setGridOption('serverSideDatasource', datasource);
  }, [createServerSideDatasource]);
  
  return (
    <div className={agGridConfig.themes.quartz} style={{ height: 400 }}>
      <AgGridReact
        // 服务器端模式配置
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        
        // 指定服务器端行模型
        rowModelType="serverSide"
        
        // 服务器端模式特定配置
        cacheBlockSize={agGridConfig.serverSideDefaults.cacheBlockSize}
        maxBlocksInCache={agGridConfig.serverSideDefaults.maxBlocksInCache}
        
        // 应用性能优化配置
        {...agGridConfig.performanceConfig}
        
        // 绑定Grid就绪事件
        onGridReady={onGridReady}
      />
    </div>
  );
}
```

## 5. 表格原位编辑功能

AG Grid 提供了强大的单元格编辑功能，可以让用户直接在表格中编辑数据。本项目实现了可控的原位编辑功能，通过外部按钮控制表格的编辑状态，避免意外编辑的同时提供良好的用户体验。

### 1. 实现原位编辑功能

首先，需要设置默认列定义中的编辑相关配置：

```typescript
const defaultColDef = useMemo<ColDef>(() => ({
  // ... 其他配置 ...
  
  // 编辑器相关配置
  editable: false, // 默认不可编辑，由编辑模式按钮控制
  cellEditor: 'agTextCellEditor', // 使用默认文本编辑器
  cellEditorPopup: false, // 内嵌编辑器而非弹出式
  
  // 停止编辑时的验证和更新
  valueSetter: (params) => {
    // 如果值没有变化，直接返回
    if (params.newValue === params.oldValue) {
      return false;
    }
    
    // 设置新值
    params.data[params.colDef.field || ''] = params.newValue;
    
    // 在这里可以添加更新到服务器的逻辑
    console.log('值已更新:', {
      field: params.colDef.field,
      oldValue: params.oldValue,
      newValue: params.newValue
    });
    
    return true; // 返回true表示值已更新
  }
}), []);
```

### 2. 添加控制编辑模式的按钮

在表格界面添加一个按钮来控制编辑模式：

```tsx
<Button
  variant={editMode ? "destructive" : "outline"}
  size="sm"
  onClick={toggleEditMode}
>
  {editMode ? "关闭更正编辑" : "开启更正编辑"}
</Button>
```

### 3. 实现切换编辑模式的功能

```typescript
// 表格编辑模式状态
const [editMode, setEditMode] = useState(false);

// 切换表格编辑模式
const toggleEditMode = useCallback(() => {
  setEditMode(prevMode => !prevMode);
  
  if (gridApi) {
    // 更新所有列的editable属性
    const newEditable = !editMode;
    const colDefs = gridApi.getColumnDefs();
    
    if (colDefs) {
      // 更新所有列定义的editable属性
      const updatedColDefs = colDefs.map((colDef: any) => {
        // 如果是列组，更新子列
        if (colDef.children) {
          return {
            ...colDef,
            children: colDef.children.map((child: any) => {
              // 可以在这里设置某些字段不可编辑
              if (child.field === 'id' || child.field === 'created_at') {
                return {
                  ...child,
                  editable: false // 这些字段始终不可编辑
                };
              }
              return {
                ...child,
                editable: newEditable
              };
            })
          };
        }
        
        // 普通列 - 对于某些关键字段，始终设置为不可编辑
        if (colDef.field === 'id' || colDef.field === 'created_at') {
          return {
            ...colDef,
            editable: false
          };
        }
        
        // 其他普通列
        return {
          ...colDef,
          editable: newEditable
        };
      });
      
      // 应用更新后的列定义
      gridApi.setGridOption('columnDefs', updatedColDefs);
      
      // 通知用户
      toast({
        title: newEditable ? "编辑模式已启用" : "编辑模式已禁用",
        description: newEditable ? "点击单元格可以编辑内容" : "表格已恢复为只读模式",
        duration: 3000
      });
    }
  }
}, [gridApi, editMode, toast]);
```

### 4. 处理单元格编辑事件

为了完成数据的更新流程，需要处理单元格编辑结束的事件：

```typescript
// 单元格编辑开始事件处理
const onCellEditingStarted = useCallback((params: any) => {
  console.log('开始编辑单元格:', params);
}, []);

// 单元格编辑结束事件处理
const onCellEditingStopped = useCallback((params: any) => {
  // 如果值没有变化，不执行任何操作
  if (params.newValue === params.oldValue) {
    return;
  }
  
  // 记录原始值和行节点引用，用于失败时恢复
  const originalValue = params.oldValue;
  const rowNode = params.node;
  const field = params.column.colId;
  
  // 准备更新数据
  const updateData = {
    id: params.data.id,
    field: field,
    value: params.newValue,
    oldValue: originalValue
  };
  
  // 调用API更新数据
  (async () => {
    try {
      // 显示加载状态
      toast({
        title: "正在保存",
        description: `正在保存 ${params.column.colDef.headerName} 字段的更改`,
        duration: 2000
      });
      
      // 构建API请求
      const endpoint = `/api/records/${params.data.id}/`;
      const updatePayload = {
        [params.column.colId]: params.newValue
      };
      
      // 调用API
      const response = await fetchApi(endpoint, {
        method: 'PATCH',
        body: JSON.stringify(updatePayload)
      });
      
      // 处理响应
      if (response.success) {
        toast({
          title: "保存成功",
          description: `${params.column.colDef.headerName} 已更新`,
          duration: 3000
        });
      } else {
        // 如果保存失败，恢复原值 - 只更新失败的单元格
        toast({
          title: "保存失败",
          description: response.error || "无法保存更改，请稍后再试",
          duration: 5000,
          variant: "destructive"
        });
        
        // 单元格级刷新：只恢复失败的单元格值，不刷新服务器数据
        if (rowNode) {
          // 恢复内存中的数据对象
          params.data[field] = originalValue;
          
          // 通知AG Grid刷新这一行的显示，方式1: 只刷新特定单元格
          gridApi?.refreshCells({
            rowNodes: [rowNode],
            columns: [field],
            force: true // 强制刷新，即使值没有变化
          });
          
          // 方式2: 如果需要更新整行
          // gridApi?.getRowNode(String(params.data.id))?.setData({...params.data});
        } else {
          console.warn('找不到行节点，无法刷新单元格');
          // 回退机制 - 如果无法获取行节点，才刷新整个表格
          gridApi?.refreshServerSide({ purge: false });
        }
      }
    } catch (error) {
      console.error('更新数据时出错:', error);
      toast({
        title: "保存失败",
        description: "发生错误，无法保存更改",
        duration: 5000,
        variant: "destructive"
      });
      
      // 同样使用单元格级刷新处理错误情况
      if (rowNode) {
        params.data[field] = originalValue;
        gridApi?.refreshCells({
          rowNodes: [rowNode],
          columns: [field],
          force: true
        });
      } else {
        gridApi?.refreshServerSide({ purge: false });
      }
    }
  })();
}, [gridApi, fetchApi, toast]);
```

### 5. 单元格级刷新机制

针对编辑失败的情况，使用单元格级刷新机制可以提供更好的用户体验和性能：

1. **为什么需要单元格级刷新**:
   - 整表刷新会导致用户当前滚动位置丢失
   - 整表刷新会重新加载所有数据，增加网络负担
   - 整表刷新会导致不必要的界面闪烁
   - 在大型表格中，整表刷新的性能成本很高

2. **单元格级刷新的实现方式**:
   - 保存行节点和字段引用，用于定位具体需要刷新的单元格
   - 使用`refreshCells()`方法指定刷新范围，而不是`refreshServerSide()`

3. **实现步骤**:
   - 在`onCellEditingStopped`处理器中保存行节点引用`const rowNode = params.node;`
   - 保存原始值以便在更新失败时恢复`const originalValue = params.oldValue;`
   - 更新内存中的数据对象`params.data[field] = originalValue;`
   - 使用`refreshCells()`方法定向刷新单元格

4. **回退机制**:
   - 如果无法获取行节点，仍然可以回退到表格刷新`gridApi?.refreshServerSide({ purge: false });`
   - 使用`purge: false`可以保留现有缓存，减少数据刷新量

5. **示例代码**:

```typescript
// 单元格级刷新示例
if (rowNode) {
  // 恢复内存中的数据对象
  params.data[field] = originalValue;
  
  // 方法1: 只刷新特定单元格
  gridApi?.refreshCells({
    rowNodes: [rowNode],       // 指定要刷新的行节点
    columns: [field],          // 指定要刷新的列
    force: true                // 强制刷新
  });
  
  // 方法2: 如果需要刷新整行
  // gridApi?.getRowNode(String(params.data.id))?.setData({...params.data});
} else {
  // 回退机制
  gridApi?.refreshServerSide({ purge: false });
}
```

### 6. 将编辑事件绑定到表格

最后，在表格组件中添加这些事件处理程序：

```tsx
<AgGridReact
  // ... 其他配置 ...
  
  // 编辑相关配置
  stopEditingWhenCellsLoseFocus={true}
  onCellEditingStarted={onCellEditingStarted}
  onCellEditingStopped={onCellEditingStopped}
/>
```

### 7. 限制特定字段的编辑权限

如果需要防止某些字段被编辑，可以在切换编辑模式时特别处理这些字段：

```typescript
// 在toggleEditMode函数中
const updatedColDefs = colDefs.map((colDef: any) => {
  // 如果是列组，更新子列
  if (colDef.children) {
    return {
      ...colDef,
      children: colDef.children.map((child: any) => {
        // 委托编号和统一编号不可编辑
        if (child.field === 'commission_number' || child.field === 'unified_number') {
          return {
            ...child,
            editable: false // 这两个字段始终不可编辑
          };
        }
        return {
          ...child,
          editable: newEditable
        };
      })
    };
  }
  
  // 对于委托编号和统一编号，始终设置为不可编辑
  if (colDef.field === 'commission_number' || colDef.field === 'unified_number') {
    return {
      ...colDef,
      editable: false
    };
  }
  
  // 其他列
  return {
    ...colDef,
    editable: newEditable
  };
});
```

### 8. 自定义编辑器

对于特定字段，可能需要使用自定义编辑器。AG Grid 提供了多种内置编辑器，也支持自定义编辑器：

```typescript
// 在列定义中指定编辑器
{
  field: 'status',
  headerName: '状态',
  // 使用下拉选择编辑器
  cellEditor: 'agSelectCellEditor',
  cellEditorParams: {
    values: ['进行中', '已完成', '已取消', '待处理']
  }
}

// 日期字段使用日期编辑器
{
  field: 'dueDate',
  headerName: '截止日期',
  cellEditor: 'agDateStringCellEditor',
  cellEditorParams: {
    // 日期编辑器参数
  }
}
```

## 6. 混合模式与多表格场景

在同一个应用中，你可以同时使用客户端和服务器端行模型，根据不同表格的需求选择最合适的模式：

- 所有表格共享相同的许可证配置和模块注册（由 `client-layout.tsx` 中的 `initializeAgGrid()` 统一处理）
- 错误拦截在应用入口处统一设置，无需在各个组件中重复处理
- 每个表格组件可以根据自己的需求选择客户端或服务器端模式
- 通用配置（如默认列定义、侧边栏配置）对两种模式都适用

## 7. 性能优化最佳实践

AG Grid 是一个强大而高性能的组件，但处理大量数据时仍需关注性能。以下是提高 AG Grid 性能的关键配置和最佳实践：

### 1. 使用集中配置的性能优化设置

```typescript
// 直接应用所有性能优化设置
<AgGridReact
  {...agGridConfig.performanceConfig}
  // 其他表格配置...
/>

// 或者选择性应用特定设置
<AgGridReact
  rowHeight={agGridConfig.performanceConfig.rowHeight}
  headerHeight={agGridConfig.performanceConfig.headerHeight}
  suppressRowHoverHighlight={agGridConfig.performanceConfig.suppressRowHoverHighlight}
  // 其他表格配置...
/>
```

### 2. 固定行高

固定行高是提升性能的关键因素之一，可以避免行高自动计算的开销：

```typescript
<AgGridReact
  rowHeight={40} // 固定行高可显著提高渲染性能
  headerHeight={42} // 固定表头高度
  // 其他配置...
/>
```

### 3. 虚拟化设置

确保行和列虚拟化始终启用，特别是对于大型表格：

```typescript
<AgGridReact
  suppressRowVirtualisation={false} // 保持行虚拟化启用
  suppressColumnVirtualisation={false} // 保持列虚拟化启用
  // 其他配置...
/>
```

### 4. 缓冲区大小

调整行缓冲区大小可以平衡初始加载速度和滚动流畅度：

```typescript
<AgGridReact
  rowBuffer={10} // 默认值，适合大多数情况
  // 对于超大表格，可以增加到15-20
  // 对于初始加载速度要求高的场景，可以减少到5
/>
```

### 5. 禁用不必要的功能

根据实际需求禁用一些可能影响性能的功能：

```typescript
<AgGridReact
  suppressRowHoverHighlight={true} // 禁用行悬停高亮，提高滚动性能
  suppressAnimationFrame={true} // 极端情况下可禁用动画帧
  suppressColumnMoveAnimation={true} // 极端情况下可禁用列移动动画
/>
```

### 6. 优化单元格渲染器

尽量减少复杂的自定义单元格渲染器，尤其是在大数据量的表格中：

- 使用 Value Formatter 代替简单的 Cell Renderer
- 对于必须使用的 Cell Renderer，确保它们高效且轻量
- 考虑对复杂 Cell Renderer 使用 memoization 技术

### 7. 防止重复初始化

确保 AG Grid 只在应用入口处初始化一次：

```typescript
// 在client-layout.tsx中
useEffect(() => {
  if (typeof window !== 'undefined') {
    initializeAgGrid(); // 仅调用一次
  }
}, []);
```

### 8. 监控和调试性能

启用调试模式来监控 AG Grid 的性能：

```typescript
// 设置环境变量
NEXT_PUBLIC_AG_GRID_DEBUG=true

// 或在组件中启用调试
<AgGridReact
  debug={true}
  // 其他配置...
/>
```

### 9. 大数据集的具体优化

对于超过50,000行的数据集：

- 始终使用服务器端行模型
- 考虑启用垂直滚动条延迟加载 `debounceVerticalScrollbar={true}`
- 增加缓存块大小 `cacheBlockSize={200}`，但不要过大
- 如果可能，细分数据为多个视图或选项卡

### 10. 官方推荐的避免性能陷阱技巧

根据AG Grid官方文档和最佳实践，以下是关键的性能优化技巧：

#### 使用值格式化器替代单元格渲染器

AG Grid官方强烈建议尽量避免使用自定义单元格渲染器，特别是对于数值或日期格式化这类简单任务：

```typescript
// 不推荐 - 使用单元格渲染器
{
  field: 'date',
  cellRenderer: (params) => {
    return new Date(params.value).toLocaleDateString();
  }
}

// 推荐 - 使用值格式化器
{
  field: 'date',
  valueFormatter: (params) => {
    return new Date(params.value).toLocaleDateString();
  }
}
```

值格式化器只在值需要显示时执行，生成的是文本而不是DOM节点，因此性能更高。

#### 避免（不必要的）自动行高与区分 domLayout

AG Grid 提供了两种与"自动高度"相关的配置，但它们作用不同，且都可能影响性能：

1. **动态行高 (`autoHeight` in `ColDef`)**:
    - 通过在**列定义 (`ColDef`)** 中设置 `autoHeight: true`，可以让**行**的高度根据该列单元格的内容动态调整。
    - 这通常需要配合 `wrapText: true` 使用。
    - **性能影响**: 此设置会停止受影响列的列虚拟化，并增加 DOM 监听器，对大数据量表格性能有显著负面影响。官方建议仅在绝对必要的列上使用。
    - **注意**: 我们之前遇到的控制台警告 `invalid gridOptions property 'autoHeight'` 是因为错误地将此属性用作了网格选项。

2. **网格容器自动高度 (`domLayout='autoHeight'` in `gridOptions`)**:
    - 通过在**网格选项 (`gridOptions`)** 中设置 `domLayout: 'autoHeight'`，可以让**整个网格容器**的高度根据其内容（所有行）自动调整。网格会占据所有行所需的垂直空间。
    - **性能影响**: 这同样会增加布局计算的开销，尤其是在行数很多时。

**性能最佳实践**:

- 优先使用**固定行高** (`rowHeight` 属性) 和**标准布局** (`domLayout: 'normal'`)，这是性能最好的方式。
- 仅在确实需要基于内容动态调整**行高**的列上谨慎使用 `autoHeight: true` (在 `ColDef` 中)。
- 仅在需要**网格容器**高度自适应内容时使用 `domLayout: 'autoHeight'` (在 `gridOptions` 中)。

```typescript
// --- 性能最佳方式 --- 
const gridOptions = {
  rowHeight: 40, // 固定行高
  domLayout: 'normal', // 标准布局
  // ... 其他 gridOptions
};
const columnDefs = [
  { field: 'description', wrapText: true }, // 内容可能换行，但行高固定
  // ... 其他 colDefs
];

// --- 需要某列决定动态行高 (谨慎使用) ---
const gridOptions_dynamicRowHeight = {
  // rowHeight: 40, // 不能与 ColDef 的 autoHeight 同时使用固定行高
  domLayout: 'normal', // 容器布局仍可以是 normal
  // ... 其他 gridOptions
};
const columnDefs_dynamicRowHeight = [
  {
    field: 'longTextColumn',
    wrapText: true,
    autoHeight: true, // 行高将由此列决定
  },
  // ... 其他 colDefs
];

// --- 需要网格容器高度自适应 (谨慎使用) ---
const gridOptions_autoLayout = {
  rowHeight: 40, // 行高仍可以是固定的
  domLayout: 'autoHeight', // 网格容器高度自适应
  // ... 其他 gridOptions
};
const columnDefs_autoLayout = [
  { field: 'any' },
  // ... 其他 colDefs
];
```

#### 使用setTimeout非阻塞数据处理

当处理大量数据时，使用setTimeout可以避免主线程阻塞：

```typescript
// 数据处理函数示例
const processData = (params) => {
  setTimeout(() => {
    // 处理大量数据...
    params.success({
      rowData: processedData,
      rowCount: totalCount
    });
  }, 0);
};
```

#### 提供稳定的行ID

为每一行提供一个稳定的ID可以帮助AG Grid更高效地追踪行变化：

```typescript
<AgGridReact
  getRowId={(params) => String(params.data.id)}
  // 其他配置...
/>
```

#### 禁用行变换动画

行变换动画虽然美观但会消耗性能：

```typescript
<AgGridReact
  suppressRowTransform={true}
  // 其他配置...
/>
```

#### 减少注册的属性名检查

该属性 (`suppressPropertyNamesCheck`) 自 AG Grid v33 起已被弃用，并已从项目的中央配置中移除。AG Grid 现在默认会进行属性检查，无需配置此项。

#### 针对不变数据使用immutableData模式

如果你的数据一旦加载就不会变化，可以使用immutableData模式：

```typescript
<AgGridReact
  immutableData={true}
  // 其他配置...
/>
```

#### 使用批量更新

对于频繁更新的数据，使用批处理和事务可以提高性能：

```typescript
// 批量更新示例
gridApi.applyTransaction({
  update: updatedRows,
  add: newRows,
  remove: removedRows
});
```

#### 跳过表头自动调整大小计算

如果经常调整列大小，跳过表头计算可以提高性能：

```typescript
<AgGridReact
  skipHeaderOnAutoSize={true}
  // 其他配置...
/>
```

#### 关于DOM节点数量的警告

来自AG Grid团队的性能建议强调，应该尽量减少DOM节点数量：

> "你应该尽可能少地向UI提供数据以充分享受Web用户界面提供的丰富性。迁移到Web不能保证在不重新架构向UI部署数据的方式的情况下，就能满足你所有现有的需求再加上一些额外需求。"

在实践中，这意味着要避免在每个单元格中创建复杂的DOM结构，特别是对于大数据集。

## 8. 错误拦截功能

集中配置系统内置了 AG Grid 错误拦截功能，主要用于屏蔽许可证相关的错误消息。这个功能在 `initializeAgGrid()` 中自动启用，**不需要**在组件中使用 `<AgGridErrorInterceptor />` 组件。

如果需要控制错误拦截行为，可以使用以下环境变量：

- `NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT`: 设置为 "false" 可禁用拦截
- `NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG`: 设置为 "true" 可在拦截时输出调试信息

## 9. 主题系统与自定义主题

AG Grid v33+ 引入了新的 Theming API，提供了通过 JavaScript 对象配置主题的能力，替代了传统的 CSS 类名方式。本项目在集中配置系统中集成了自定义主题对象，便于所有表格组件统一使用。

### 9.1 主题配置概述

项目的 `ag-grid-config.ts` 中定义了自定义主题对象 `myQuartzTheme`，并通过 `themes.quartzCustom` 导出：

```typescript
// 从企业版库导入主题对象基础
import { themeQuartz } from 'ag-grid-enterprise';

// 定义自定义主题对象，设置字体和字号
export const myQuartzTheme = themeQuartz.withParams({
  browserColorScheme: "light",
  fontFamily: { googleFont: "IBM Plex Sans" }, // 全局字体
  fontSize: 14, // 正文字号
  headerFontSize: 16 // 表头字号
});

// 导出默认的 AG Grid 配置对象
export default {
  // ... 其他配置 ...
  themes: {
    quartz: 'ag-theme-quartz',       // 传统主题 (保留用于兼容)
    quartzDark: 'ag-theme-quartz-dark',
    // ... 其他主题 ...
    quartzCustom: myQuartzTheme      // 自定义主题对象
  },
  // ... 其他配置 ...
};
```

### 9.2 新版 Theming API 使用方法

在组件中使用新版主题系统有两种方式：

#### 方式一：通过 gridOptions 设置主题

```typescript
import agGridConfig from '@/lib/ag-grid-config';
import { AgGridReact } from 'ag-grid-react';
import { useMemo } from 'react';

export function MyGridComponent() {
  // 表格配置
  const gridOptions = useMemo(() => ({
    // 其他配置...
    theme: agGridConfig.themes.quartzCustom, // 使用自定义主题对象
    pagination: true,
    paginationPageSize: 50,
  }), []);

  return (
    <div className="w-full h-[600px]"> {/* 注意：不再需要主题类名 */}
      <AgGridReact
        gridOptions={gridOptions}
        // 其他属性...
      />
    </div>
  );
}
```

#### 方式二：直接传递 theme 属性

```typescript
import agGridConfig from '@/lib/ag-grid-config';
import { AgGridReact } from 'ag-grid-react';

export function MyGridComponent() {
  return (
    <div className="w-full h-[600px]"> {/* 注意：不再需要主题类名 */}
      <AgGridReact
        theme={agGridConfig.themes.quartzCustom} // 直接通过 theme 属性传递
        // 其他属性...
      />
    </div>
  );
}
```

### 9.3 从传统方式迁移到新版主题系统

#### 旧方式 (v32及之前)

```typescript
// 导入 CSS 文件
import 'ag-grid-enterprise/styles/ag-grid.css';
import 'ag-grid-enterprise/styles/ag-theme-quartz.css';

// 在组件中使用类名
<div className={agGridConfig.themes.quartz + " w-full h-[600px]"}>
  <AgGridReact
    // 属性...
  />
</div>
```

#### 新方式 (v33+)

```typescript
// 不再需要导入 CSS 文件

// 在组件中使用主题对象
<div className="w-full h-[600px]">
  <AgGridReact
    theme={agGridConfig.themes.quartzCustom}
    // 属性...
  />
</div>
```

#### 迁移步骤

1. **移除 CSS 导入**：删除所有 AG Grid 相关的 CSS 文件导入语句。
2. **移除主题类名**：从包裹 AgGridReact 的 div 上移除 `ag-theme-xx` 类名（保留其他布局相关的类名）。
3. **应用主题对象**：通过 `theme` 属性或 `gridOptions.theme` 应用自定义主题对象。

### 9.4 主题自定义扩展

如果需要进一步自定义主题参数，可以基于已有的主题对象创建新的自定义主题：

```typescript
import { myQuartzTheme } from '@/lib/ag-grid-config';

// 创建衍生主题
const myCustomTheme = myQuartzTheme.withParams({
  // 覆盖或新增参数
  accentColor: '#FF5733',
  borderRadius: 8
});

// 使用自定义主题
<AgGridReact theme={myCustomTheme} />
```

### 9.5 主题最佳实践与常见问题

1. **统一使用**：在所有表格组件中统一使用 `agGridConfig.themes.quartzCustom`，确保风格一致。
2. **废弃 CSS 文件**：不再需要导入 AG Grid 的 CSS 文件，主题会由 JavaScript 动态注入。
3. **不混用**：避免同时使用传统 `className` 和新版 `theme` 方式，以免发生样式冲突。
4. **兼容性**：如果在同一项目中有旧组件仍使用传统方式，可以设置 `theme: "legacy"` 来让它们继续使用传统主题。
5. **与企业版兼容**：主题对象从 `ag-grid-enterprise` 导入，而不是 `ag-grid-community`，确保与企业版功能兼容。

### 9.6 故障排除

#### Q: 主题应用无效

A: 确保移除了 `className` 中的主题类名，并正确应用了 `theme` 属性。

#### Q: 控制台出现主题相关错误

A: 检查是否同时使用了传统类名和 `theme` 属性，它们不能混用。

#### Q: 字体没有按预期应用

A: 如果使用 Google 字体，可能需要额外加载字体，或设置 `loadThemeGoogleFonts` 选项为 `true`。

#### Q: 表格样式混乱或不统一

A: 确保项目中所有表格都迁移到统一的主题对象，避免不同表格使用不同方式应用主题。

## 10. 常见问题解答（FAQ）

### Q: 如何在生产环境设置许可证密钥？

A: 通过环境变量 `NEXT_PUBLIC_AG_GRID_LICENSE_KEY` 设置密钥。如果未设置，将使用配置文件中的默认值。

### Q: 如何在开发中查看 AG Grid 的日志？

A: 设置环境变量 `NEXT_PUBLIC_AG_GRID_DEBUG=true` 启用调试日志。

### Q: 多个表格组件是否需要多次初始化？

A: 不需要。`initializeAgGrid()` 只需在应用入口处（如 `client-layout.tsx`）调用一次即可。各表格组件只需导入配置对象使用。

### Q: 如何为特定组件自定义配置？

A: 导入基础配置后进行扩展：

```typescript
const customColDef = useMemo(() => ({
  ...agGridConfig.defaultColDef,
  filter: false, // 覆盖默认值
  floatingFilter: false, // 覆盖默认值
  additionalProp: "value" // 添加新属性
}), []);
```

### Q: 如何选择使用客户端模式还是服务器端模式？

A: 基于数据量和使用场景选择：

- 数据量小于10,000行且需要在客户端进行所有操作，选择客户端模式
- 数据量大、需要分页加载或服务器端筛选排序，选择服务器端模式

### Q: 我的表格性能变慢了，应该检查什么？

A: 性能问题常见原因及解决方案：

1. **重复初始化**：确保 `initializeAgGrid()` 只调用一次
2. **自动行高**：设置固定行高 `rowHeight={40}`
3. **复杂Cell Renderers**：使用Value Formatter代替简单的Cell Renderer
4. **未使用虚拟化**：确保 `suppressRowVirtualisation` 和 `suppressColumnVirtualisation` 都设为 false
5. **行数过多**：考虑使用服务器端行模型或分页
6. **DOM过多**：减少复杂的自定义单元格渲染器和DOM数量

### Q: 浏览器内存限制问题如何解决？

A: 即使是64位Chrome也会限制每个标签页的内存使用。解决方案包括：

1. 使用服务器端行模型分批加载数据
2. 分解大表格为多个小表格或使用标签页
3. 减少每行/单元格的DOM复杂度
4. 如果数据确实非常大，考虑使用虚拟化和服务器端处理的组合

### Q: 如何避免在大数据量下滚动性能问题？

A: 滚动优化关键设置：

1. `debounceVerticalScrollbar={true}` - 防抖垂直滚动
2. `suppressRowHoverHighlight={true}` - 禁用行悬停高亮
3. `rowBuffer={15}` - 适当增加行缓冲区
4. `suppressRowTransform={true}` - 禁用行变换动画
5. 确保使用固定行高 `rowHeight={40}`

## 11. 维护与更新

### 更新许可证

当需要更新许可证密钥时，只需修改 `ag-grid-config.ts` 文件中的默认值，或更新环境变量。

### 更新默认配置

当需要更改全局默认设置时，只需更新 `ag-grid-config.ts` 文件中的相应配置对象。
