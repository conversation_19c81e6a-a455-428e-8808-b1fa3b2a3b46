# PDF分割功能增强设计文档

## 1. 功能概述

PDF分割模块负责将包含多个档案的PDF文件按照特定文本标记进行智能分割，并为分割后的文件创建规范化的命名、安全存储和链接生成。本次增强主要添加以下功能：

- 改进文件命名及生成网络链接
- 优化文件存储位置管理
- 实现文件访问权限控制
- 添加URL有效期管理和访问验证
- 建立定期备份机制和文件完整性验证
- 提供处理完成后的通知机制和进度显示
- 支持批量处理多个PDF文件

## 2. 系统架构

### 2.1 模块拆分与原子化设计

遵循单一职责原则，将功能拆分为以下核心服务模块：

```text
archive_processing/
├── services/
│   ├── file_storage_service.py     # 文件存储与命名服务
│   ├── pdf_security_service.py     # 文件安全与访问控制
│   ├── backup_service.py           # 文件备份与完整性验证
│   ├── notification_service.py     # 通知与进度显示
│   └── batch_processing_service.py # 批量处理服务
├── utils（暂时没拆分）/
│   ├── init.py                     # 模块公共接口与导出
│   ├── pdf_processor.py            # PDF文档处理核心实现
│   ├── ocr_engine.py               # OCR引擎管理与文字识别
│   ├── pdf_splitting_handler.py    # PDF文件分割与输出管理
│   ├── image_enhancer.py           # 图像增强与OCR前处理
│   ├── text_analyzer.py            # 文本分析与匹配算法
│   ├── unified_number_parser.py    # 统一编号提取与标准化
│   ├── report_generator.py         # 处理报告与摘要生成
│   ├── filename_generator.py       # 智能文件命名规则实现
│   ├── storage_path_manager.py     # 存储路径结构与管理
│   ├── models.py                   # 数据模型和数据类定义
│   └── cpu_utils.py                # CPU检测和优化相关工具
└── models/
    ├── processing_task.py          # 处理任务模型
    ├── file_backup.py              # 文件备份记录
    └── secure_access.py            # 安全访问记录
```

### 2.2 数据流程

1. **文件上传** → **分割处理** → **安全存储** → **链接生成** → **档案记录更新（状态、URL、归档人员、归档时间）**
2. **访问请求** → **权限验证** → **链接验证** → **文件提供**
3. **定时任务** → **文件完整性检查** → **备份处理**

## 3. 主要功能模块详解

### 3.1 文件命名与存储 (FileStorageService)

#### 命名策略

- 基于统一编号的智能命名：`{unified_number}_{timestamp}.pdf`
- 无统一编号时的备选策略：`unknown_{uuid}_{timestamp}.pdf`

#### 存储结构

- 层级目录：`MEDIA_ROOT/archives/{YYYY}/{MM}/{unified_number_prefix}/`
- 元数据索引：为每个文件生成元数据记录，便于检索

### 3.2 档案记录更新 (ArchiveRecordUpdateService)

#### 归档状态更新

- 更新 `archive_status` 字段为"已归档"
- 设置 `archive_url` 为生成的文件链接

#### 归档元数据更新

- 设置 `archive_datetime` 为当前处理时间
- 记录 `archive_person` 为当前执行操作的用户
- 自动关联其他相关归档元数据

#### 事务处理

- 使用原子事务确保档案记录和文件操作的一致性
- 提供回滚机制处理失败情况
- 记录操作日志用于审计

### 3.3 文件安全与访问控制 (PDFSecurityService)

#### 访问权限控制

- 基于角色的权限控制系统
- 与Django用户权限集成
- 文件级别的访问限制

#### URL有效期与验证

- 签名URL生成机制
- 可配置的链接有效期
- 基于Token的访问验证

### 3.4 备份与文件完整性 (BackupService)

#### 备份策略

- 定期自动备份机制
- 多级备份方案（本地、远程、云存储）
- 增量与全量备份选项

#### 完整性验证

- 基于SHA256的文件哈希
- 定期完整性校验
- 损坏文件自动恢复

### 3.5 通知与进度显示 (NotificationService)

#### 通知机制

- 多渠道通知（邮件、站内信）
- 可自定义的通知模板
- 批量处理状态报告

#### 进度显示

- 实时进度更新（WebSocket/轮询）
- 任务状态跟踪
- 错误与警告通知

### 3.6 批量处理 (BatchProcessingService)

#### 队列管理

- 任务优先级设置
- 并行处理控制
- 失败任务重试机制

#### 性能优化

- 资源使用限制
- 分布式处理选项
- 大文件分块处理

## 4. 数据模型设计

### 4.1 ProcessingTask 模型

```python
class ProcessingTask(models.Model):
    """处理任务记录"""
    task_id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    status = models.CharField(max_length=50, choices=STATUS_CHOICES)
    progress = models.FloatField(default=0.0)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)
    parameters = models.JSONField(default=dict)
    result_summary = models.JSONField(default=dict)
```

### 4.2 FileBackup 模型

```python
class FileBackup(models.Model):
    """文件备份记录"""
    original_file = models.CharField(max_length=255)
    backup_path = models.CharField(max_length=255)
    backup_time = models.DateTimeField(auto_now_add=True)
    backup_type = models.CharField(max_length=50)
    file_hash = models.CharField(max_length=64)
    is_verified = models.BooleanField(default=False)
    last_verified = models.DateTimeField(null=True, blank=True)
```

### 4.3 SecureAccess 模型

```python
class SecureAccess(models.Model):
    """安全访问记录"""
    file_path = models.CharField(max_length=255)
    access_token = models.CharField(max_length=255)
    expires_at = models.DateTimeField()
    created_by = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    allowed_roles = models.JSONField(default=list)
    access_count = models.IntegerField(default=0)
    last_accessed = models.DateTimeField(null=True, blank=True)
```

### 4.4 ArchiveRecord 模型关联

```python
# 现有ArchiveRecord模型中的相关字段
class ArchiveRecord(models.Model):
    # 其他字段...
    
    # 档案链接与状态
    archive_status = models.CharField(max_length=50, verbose_name="报告归档状态", blank=True, null=True)
    archive_url = models.URLField(blank=True, null=True, verbose_name="档案URL链接")
    
    # 归档元数据
    archive_datetime = models.DateTimeField(blank=True, null=True, verbose_name="归档日期", 
                                          help_text="系统记录的归档日期时间")
    archive_person = models.CharField(max_length=100, blank=True, null=True, verbose_name="归档人")
    
    # 其他字段...
```

## 5. API设计

### 5.1 文件处理API

- `POST /api/archive/process-pdf/` - 提交PDF处理任务
  - 额外参数：`operator_id` - 执行归档操作的用户ID
- `GET /api/archive/task/{task_id}/` - 获取任务状态
- `GET /api/archive/task/{task_id}/progress/` - 获取实时进度

### 5.2 文件访问API

- `GET /api/archive/file/{file_id}/secure-link/` - 获取临时安全链接
- `GET /media/secure/{token}/{filename}/` - 通过安全令牌访问文件

### 5.3 管理API

- `POST /api/archive/backup/manual/` - 触发手动备份
- `GET /api/archive/files/integrity-check/` - 检查文件完整性

### 5.4 归档信息API

- `GET /api/archive/records/recent-archived/` - 获取最近归档的记录
- `GET /api/archive/stats/archive-by-user/` - 获取按用户统计的归档数据

## 6. 配置选项

```python
# settings.py中的配置选项

# 文件存储配置
ARCHIVE_STORAGE_ROOT = os.path.join(MEDIA_ROOT, 'archives')
ARCHIVE_BACKUP_ROOT = os.path.join(MEDIA_ROOT, 'backups')

# 安全配置
SECURE_URL_SALT = 'your-secure-salt-here'
SECURE_URL_EXPIRY = 86400  # 默认1天有效期

# 备份配置
AUTO_BACKUP_FREQUENCY = 'daily'  # 'hourly', 'daily', 'weekly'
BACKUP_RETENTION_DAYS = 30

# 批处理配置
MAX_PARALLEL_PROCESSES = 3
TASK_QUEUE_TIMEOUT = 3600  # 单位：秒
```

## 7. 安全考量

- 所有用户输入数据严格验证
- 文件名和路径进行安全编码，防止路径遍历攻击
- 敏感配置信息使用环境变量管理
- 定期安全审计和日志分析

## 8. 性能优化

- 大文件分块处理
- 图像预处理缓存
- 数据库查询优化
- 文件IO操作批量处理

## 8.1 服务间协作

### 8.1 PDF处理与档案记录服务协作

```python
# 伪代码示例：PDF处理完成后更新档案记录
@transaction.atomic
def update_archive_records_after_processing(unified_numbers, output_files, archive_urls, user_id):
    """
    处理完成后更新档案记录信息
    
    Args:
        unified_numbers: Dict[int, str] - 页码到统一编号的映射
        output_files: List[str] - 输出文件路径列表
        archive_urls: List[str] - 生成的档案URL列表
        user_id: int - 执行操作的用户ID
    """
    current_time = timezone.now()
    user = User.objects.get(id=user_id)
    
    # 获取档案状态服务
    status_service = ArchiveStatusService()
    
    # 构建更新数据
    updates = []
    for page, unified_number in unified_numbers.items():
        if page < len(output_files) and page < len(archive_urls):
            updates.append({
                'unified_number': unified_number,
                'archive_url': archive_urls[page],
                'archive_status': '已归档',
                'archive_datetime': current_time,
                'archive_person': user.username
            })
    
    # 批量更新档案记录
    result = status_service.batch_update_archive_records(updates)
    
    return result
```

## 9. 部署与维护

- Docker容器化部署
- 监控与告警设置
- 日志管理策略
- 扩展性规划

## 10. 测试策略

- 单元测试覆盖核心功能
- 集成测试验证模块交互
- 性能测试确认系统承载能力
- 安全测试防范潜在风险

## 11. 管理界面考量

- 添加归档历史查看界面
- 实现按归档人员筛选和统计功能
- 提供归档操作的审计日志查询
