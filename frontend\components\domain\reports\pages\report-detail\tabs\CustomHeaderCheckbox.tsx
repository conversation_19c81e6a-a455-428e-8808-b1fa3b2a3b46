"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { IHeaderParams } from 'ag-grid-enterprise'

interface CustomHeaderCheckboxProps extends IHeaderParams {
  // 从headerComponentParams传入的方法
  selectCurrentPage: (isRowSelectableFn?: (params: any) => boolean) => any
  deselectCurrentPage: () => any
  getCurrentPageSelectionStats: () => { total: number; selected: number; selectable: number }
  isRowSelectable: (params: any) => boolean
}

export const CustomHeaderCheckbox: React.FC<CustomHeaderCheckboxProps> = (props) => {
  const {
    api,
    selectCurrentPage,
    deselectCurrentPage, 
    getCurrentPageSelectionStats,
    isRowSelectable
  } = props


  const [isChecked, setIsChecked] = useState(false)
  const [isIndeterminate, setIsIndeterminate] = useState(false)

  // 更新选择状态
  const updateSelectionState = useCallback(() => {
    // ✅ 增加安全检查，避免在 grid 销毁后调用
    if (!api || api.isDestroyed()) return
    
    try {
      const stats = getCurrentPageSelectionStats()
      
      if (stats.selected === 0) {
        setIsChecked(false)
        setIsIndeterminate(false)
      } else if (stats.selected === stats.selectable) {
        setIsChecked(true)
        setIsIndeterminate(false)
      } else {
        setIsChecked(false)
        setIsIndeterminate(true)
      }
    } catch (error) {
      // 忽略在组件卸载过程中的错误
      console.warn('CustomHeaderCheckbox: updateSelectionState error (likely during unmount):', error)
    }
  }, [api, getCurrentPageSelectionStats])

  // 监听选择变化
  useEffect(() => {
    if (!api) return

    const onSelectionChanged = () => {
      updateSelectionState()
    }

    // 监听选择变化事件
    api.addEventListener('selectionChanged', onSelectionChanged)
    api.addEventListener('paginationChanged', onSelectionChanged)
    
    // 初始化状态
    updateSelectionState()

    return () => {
      // ✅ 检查 grid 是否已被销毁，避免调用已销毁的 grid API
      if (api && !api.isDestroyed()) {
        api.removeEventListener('selectionChanged', onSelectionChanged)
        api.removeEventListener('paginationChanged', onSelectionChanged)
      }
    }
  }, [api, updateSelectionState])

  // 处理点击事件
  const handleClick = useCallback(() => {
    // ✅ 增加安全检查，避免在 grid 销毁后调用
    if (!api || api.isDestroyed()) return
    
    try {
      if (isChecked || isIndeterminate) {
        // 如果当前是选中或半选状态，则取消选择
        deselectCurrentPage()
      } else {
        // 如果当前是未选中状态，则选择当前页
        selectCurrentPage(isRowSelectable)
      }
    } catch (error) {
      console.warn('CustomHeaderCheckbox: handleClick error:', error)
    }
  }, [api, isChecked, isIndeterminate, selectCurrentPage, deselectCurrentPage, isRowSelectable])

  const checkboxRef = useRef<HTMLInputElement>(null)

  // 处理原生 checkbox 的 indeterminate 状态
  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = isIndeterminate
    }
  }, [isIndeterminate])

  return (
    <div className="flex items-center justify-center h-full">
      <input
        ref={checkboxRef}
        type="checkbox"
        checked={isChecked}
        onChange={handleClick}
        className="cursor-pointer w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
        aria-label="选择当前页"
      />
    </div>
  )
} 