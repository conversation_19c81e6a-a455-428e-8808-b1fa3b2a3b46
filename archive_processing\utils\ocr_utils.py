# archive_processing/utils/ocr_utils.py
"""
OCR 工具模块

此模块包含与光学字符识别 (OCR) 执行相关的功能，包括：
1. 初始化 OCR 引擎 (PaddleOCR)
2. 在图像上运行 Tesseract OCR
3. 在图像上运行 PaddleOCR
4. 统一的 OCR 执行入口点
5. 页面级 OCR 和文本提取流程

从 pdf_processor_usefull.py 中的 PDFProcessor 类提取和重构。
"""

import logging
import time
from typing import Optional, List, Tuple, Dict, Union, Any
from PIL import Image
import pytesseract  # Tesseract
import warnings
import numpy as np  # <-- 添加 numpy 导入

# OCR 微服务化：通过 HTTP API 调用
import requests
import io

# OCR 服务配置
OCR_SERVICE_URL = "http://ocr-service:8001"  # Docker 内部服务名
OCR_SERVICE_TIMEOUT = 30.0  # 请求超时时间
OCR_SERVICE_HEALTH_CHECK_TIMEOUT = 5.0  # 健康检查超时时间
# 严格模式配置：零容错，确保精度
OCR_SERVICE_MAX_RETRIES = 3                  # 每次OCR调用的最大重试次数
OCR_SERVICE_RETRY_DELAY = 2.0                # 重试间隔（秒）

# OCR微服务使用微服务内图像处理（现代化，网络效率高）

# 标记 PaddleOCR 通过微服务可用
PADDLE_OCR_AVAILABLE = True  # 假设 OCR 服务可用，实际可用性在运行时检查

# 导入本项目其他工具模块
from . import image_utils
from . import text_utils
from archive_processing.dto.result_dtos import UnifiedNumberResult

logger = logging.getLogger(__name__)


# --- OCR 微服务客户端 ---


class OCRServiceClient:
    """OCR 微服务客户端 (增强版)"""

    def __init__(self):
        self.base_url = OCR_SERVICE_URL
        self.timeout = OCR_SERVICE_TIMEOUT
        self.session = requests.Session()
        self._last_health_check = 0
        self._health_check_interval = 30.0  # 30秒检查一次
        self._is_healthy = None

        # 严格模式：不使用自动重试，由上层逻辑控制重试

    def is_available(self) -> bool:
        """检查 OCR 服务是否可用 (带缓存)"""
        current_time = time.time()

        # 如果最近检查过且结果为健康，直接返回
        if (self._is_healthy is True and
            current_time - self._last_health_check < self._health_check_interval):
            return True

        try:
            response = self.session.get(
                f"{self.base_url}/health",
                timeout=OCR_SERVICE_HEALTH_CHECK_TIMEOUT
            )

            if response.status_code == 200:
                health_data = response.json()
                is_healthy = health_data.get('ocr_engine_ready', False)
            else:
                is_healthy = False

            self._is_healthy = is_healthy
            self._last_health_check = current_time

            if is_healthy:
                logger.debug("OCR 服务健康检查通过")
            else:
                logger.warning("OCR 服务健康检查失败: 引擎未就绪")

            return is_healthy

        except Exception as e:
            logger.warning(f"OCR 服务健康检查失败: {e}")
            self._is_healthy = False
            self._last_health_check = current_time
            return False

    def recognize_basic(self, image: Image.Image) -> str:
        """基础 OCR 识别（严格模式：确保服务响应，允许空结果）"""
        def _recognize_basic():
            # 将图像转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # 准备请求数据
            files = {'file': ('image.png', img_buffer, 'image/png')}
            data = {'mode': 'basic'}

            # 发送请求
            response = self.session.post(
                f"{self.base_url}/ocr",
                files=files,
                data=data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    return result.get('text', '')
                else:
                    error_msg = result.get('error', 'Unknown error')
                    raise Exception(f"OCR 处理失败: {error_msg}")
            elif response.status_code == 429:
                raise Exception("服务繁忙，请稍后重试")
            elif response.status_code >= 500:
                raise Exception(f"服务内部错误: {response.status_code}")
            else:
                raise Exception(f"请求失败: {response.status_code}")

        # 严格模式：确保服务响应，允许空结果
        for attempt in range(1, OCR_SERVICE_MAX_RETRIES + 1):
            try:
                result = _recognize_basic()
                # 服务正常响应（无论结果是否为空）
                if attempt > 1:
                    logger.info(f"基础OCR第{attempt}次尝试成功，服务正常响应")
                else:
                    logger.debug("基础OCR服务正常响应")

                # 记录结果统计（用于调试）
                if result.strip():
                    logger.debug(f"基础OCR识别到内容：{result[:50]}...")
                else:
                    logger.debug("基础OCR未识别到内容（页面可能为空）")

                return result  # 返回结果（可能为空字符串）

            except Exception as e:
                logger.warning(f"基础OCR第{attempt}次尝试失败（服务/网络问题）: {e}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < OCR_SERVICE_MAX_RETRIES:
                logger.info(f"基础OCR等待{OCR_SERVICE_RETRY_DELAY}秒后重试...")
                time.sleep(OCR_SERVICE_RETRY_DELAY)

        # 所有重试都失败 - 服务不可用
        error_msg = f"基础OCR {OCR_SERVICE_MAX_RETRIES}次尝试全部失败，OCR服务不可用"
        logger.error(error_msg)
        raise RuntimeError(error_msg)

    def recognize_enhanced(self, image: Image.Image, max_attempts: int = 4) -> List[str]:
        """增强 OCR 识别（严格模式：确保服务响应指定次数，允许空结果）"""
        def _recognize_enhanced():
            if not image:
                return []

            # 将图像转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # 准备请求数据
            files = {'file': ('image.png', img_buffer, 'image/png')}
            data = {
                'mode': 'enhanced',
                'max_attempts': max_attempts
            }

            # 发送请求
            response = self.session.post(
                f"{self.base_url}/ocr",
                files=files,
                data=data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"增强OCR响应: {result}")

                if result.get('success', False):
                    text_results = result.get('text_results', [])
                    logger.debug(f"text_results类型: {type(text_results)}, 内容: {text_results}")

                    # 严格验证返回数据
                    if text_results is None:
                        raise Exception("服务返回text_results为None")

                    if not isinstance(text_results, list):
                        raise Exception(f"服务返回text_results类型错误: {type(text_results)}, 期望list")

                    # 安全地处理每个结果
                    texts = []
                    for i, text_result in enumerate(text_results):
                        if text_result is None:
                            logger.warning(f"text_results[{i}]为None，使用空字符串")
                            texts.append("")
                        elif isinstance(text_result, dict):
                            texts.append(text_result.get('text', ''))
                        elif isinstance(text_result, str):
                            texts.append(text_result)
                        else:
                            logger.warning(f"text_results[{i}]类型异常: {type(text_result)}，转换为字符串")
                            texts.append(str(text_result))

                    logger.debug(f"增强OCR解析结果: {texts}")
                    return texts
                else:
                    error_msg = result.get('error', 'Unknown error')
                    logger.warning(f"OCR 增强识别失败: {error_msg}")
                    raise Exception(f"OCR 增强处理失败: {error_msg}")
            elif response.status_code == 429:
                logger.warning("OCR 服务繁忙，增强识别请求被限流")
                raise Exception("服务繁忙，请稍后重试")
            elif response.status_code >= 500:
                logger.error(f"OCR 服务内部错误: {response.status_code}")
                raise Exception(f"服务内部错误: {response.status_code}")
            else:
                logger.error(f"OCR 增强识别请求失败: {response.status_code} - {response.text}")
                raise Exception(f"请求失败: {response.status_code}")

        # 严格模式：确保服务响应，允许空结果

        logger.info(f"开始增强OCR识别，要求服务成功响应{max_attempts}次")

        for attempt in range(1, OCR_SERVICE_MAX_RETRIES + 1):
            try:
                results = _recognize_enhanced()

                # 验证服务响应完整性：必须返回指定数量的结果
                if len(results) != max_attempts:
                    logger.warning(f"增强OCR第{attempt}次尝试：期望{max_attempts}个响应，实际获得{len(results)}个")
                    continue

                # 服务响应完整，返回结果（允许空字符串）
                if attempt > 1:
                    logger.info(f"增强OCR第{attempt}次尝试成功，服务正常响应{max_attempts}次")
                else:
                    logger.info(f"增强OCR成功，服务正常响应{max_attempts}次")

                # 记录结果统计（用于调试）
                non_empty_count = len([r for r in results if r.strip()])
                logger.debug(f"增强OCR结果统计：{non_empty_count}/{max_attempts} 个非空结果")

                return results

            except Exception as e:
                logger.warning(f"增强OCR第{attempt}次尝试失败（服务/网络问题）: {e}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < OCR_SERVICE_MAX_RETRIES:
                logger.info(f"增强OCR等待{OCR_SERVICE_RETRY_DELAY}秒后重试...")
                time.sleep(OCR_SERVICE_RETRY_DELAY)

        # 所有重试都失败 - 服务不可用
        error_msg = f"增强OCR {OCR_SERVICE_MAX_RETRIES}次尝试全部失败，OCR服务不可用"
        logger.error(error_msg)
        raise RuntimeError(error_msg)


# 全局 OCR 客户端实例
_ocr_client = OCRServiceClient()


# --- OCR 引擎初始化 ---


def init_paddle_ocr(use_paddle: bool = True) -> Optional[Any]:
    """初始化OCR引擎（微服务模式）。

    Args:
        use_paddle: 是否使用PaddleOCR微服务。

    Returns:
        OCR 客户端实例，如果不可用或初始化失败则返回 None。
    """
    if not use_paddle:
        logger.info("未请求使用PaddleOCR。")
        return None

    try:
        logger.info("检查 OCR 微服务可用性...")

        # 检查 OCR 服务是否可用
        if _ocr_client.is_available():
            logger.info("OCR 微服务连接成功，引擎就绪。")
            return _ocr_client  # 返回客户端实例
        else:
            logger.warning("OCR 微服务不可用。")
            return None

    except Exception as e:
        logger.error(f"OCR 微服务连接失败: {e}", exc_info=True)
        logger.warning("无法连接到 OCR 微服务。")
        return None


# --- OCR 执行函数 ---


def run_tesseract_basic(image: Image.Image, config: str = None) -> str:
    """在单个预处理图像上运行 Tesseract OCR。

    Args:
        image: 经过 Tesseract 优化的预处理图像 (PIL.Image 对象)。
        config: Tesseract 配置字符串。如果为 None，使用默认配置。

    Returns:
        识别出的文本字符串，失败则返回空字符串。
    """
    if not image:
        return ""

    effective_config = config or "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"

    try:
        logger.debug(f"运行 Tesseract (Basic) 使用配置: {effective_config}")
        text = pytesseract.image_to_string(image, config=effective_config)
        result = text.strip()
        logger.debug(f"Tesseract (Basic) 结果片段: {result[:50]}...")
        return result
    except Exception as e:
        logger.error(f"Tesseract OCR (Basic) 失败: {e}")
        return ""


def run_tesseract_enhanced(
    images: List[Image.Image], configs: List[str] = None
) -> List[str]:
    """在多个图像版本上运行 Tesseract OCR (用于增强识别)。

    Args:
        images: 预处理后的图像列表 (PIL.Image 对象)。
        configs: 与图像列表对应的 Tesseract 配置字符串列表。
                 如果为 None，将使用默认配置。

    Returns:
        每个图像识别出的文本列表 (失败则为空字符串)。
    """
    results = []
    if not images:
        return results

    # 如果未提供配置，为每个图像生成默认配置
    if configs is None:
        default_config = "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
        configs = [default_config] * len(images)
    elif len(configs) != len(images):
        logger.warning(
            f"提供的 Tesseract 配置数量 ({len(configs)}) 与图像数量 ({len(images)}) 不匹配。将尝试使用第一个配置。"
        )
        configs = [configs[0]] * len(images)  # 使用第一个配置填充

    for i, (image, config) in enumerate(zip(images, configs)):
        try:
            logger.debug(
                f"运行 Tesseract (增强尝试 {i+1}/{len(images)}) 使用配置: {config}"
            )
            text = pytesseract.image_to_string(image, config=config)
            results.append(text.strip())
            logger.debug(f"Tesseract (增强尝试 {i+1}) 结果片段: {text[:50]}...")
        except Exception as e:
            logger.error(f"Tesseract OCR 增强尝试 {i+1} 失败: {e}")
            results.append("")  # 添加空字符串表示失败

    return results








# [这里将添加 run_ocr_on_image 函数]


# --- 页面级 OCR 流程 ---

# [这里将添加 ocr_page_and_extract 函数]


# --- 带控制逻辑的 OCR 调用 ---


def perform_basic_ocr(
    image: Image.Image,
    engine: str,
    paddle_engine: Optional[Any] = None,
    use_paddle: bool = False,
    config: Optional[str] = None,
    page_num: Optional[int] = None,
    allow_tesseract_fallback: bool = False,
) -> str:
    """执行基础OCR，选择合适的引擎进行识别。

    Args:
        image: 原始 PIL 图像。
        engine: OCR 引擎选择 ("auto", "paddle", "tesseract")。
        paddle_engine: OCR客户端实例。
        use_paddle: 是否允许使用 PaddleOCR。
        config: Tesseract 配置字符串 (可选)。
        page_num: 页码 (用于日志，可选)。
        allow_tesseract_fallback: 是否允许回退到Tesseract，默认False（要求高精度）。

    Returns:
        识别出的文本字符串。
    """
    if not image:
        return ""



    text_result = ""
    try:
        # 自动选择或指定PaddleOCR
        should_try_paddle = (
            (engine.lower() == "auto" or engine.lower() == "paddle")
            and use_paddle
            and paddle_engine is not None
        )

        if should_try_paddle:
            # 直接使用原始图像，微服务内部会进行预处理
            try:
                logger.debug("执行 PaddleOCR 基础识别...")
                text_result = paddle_engine.recognize_basic(image)
                if text_result:
                    logger.debug(f"PaddleOCR 基础识别成功: {text_result[:50]}...")
                else:
                    logger.debug("PaddleOCR 基础识别未返回有效结果")
            except Exception as e:
                logger.error(f"PaddleOCR 基础识别失败: {e}")
                text_result = ""

            # 检查Paddle结果，如果为空且是auto模式，可能回退到Tesseract（如果允许）
            if not text_result and engine.lower() == "auto":
                logger.info("PaddleOCR 未返回有效结果")
                # 继续执行下面的 Tesseract 逻辑（如果允许回退）
                pass
            elif text_result:
                # Paddle 成功 (或指定了Paddle但图像准备失败导致text_result为空)
                pass  # 使用 text_result
            else:
                # 指定了 Paddle，但准备失败或执行失败，返回空
                return ""

        # Tesseract模式 (仅在允许的情况下使用)
        if not text_result and allow_tesseract_fallback and (
            engine.lower() == "tesseract" or engine.lower() == "auto"
        ):
            # 1. 获取 Tesseract 标准预处理图像
            processed_image = image_utils.prepare_standard_image_for_tesseract(image)
            if processed_image:
                # 2. 运行基础 Tesseract OCR
                text_result = run_tesseract_basic(processed_image, config=config)
            else:
                logger.warning(
                    f"无法为 Tesseract 生成预处理图像 {f'(页码:{page_num})' if page_num else ''}"
                )
                text_result = ""  # 图像准备失败



        # 记录日志（降低频率）
        if text_result and page_num and page_num % 200 == 0:
            log_engine = "Paddle" if should_try_paddle and text_result else "Tesseract"
            logger.debug(
                f"第{page_num+1}页的 Basic OCR ({log_engine}) 结果片段: {text_result[:50]}..."
            )

        return text_result

    except Exception as e:
        # 严格模式：不允许静默失败，必须抛出异常中断任务
        logger.error(
            f"执行基础 OCR 失败{f'(页码:{page_num})' if page_num else ''}: {e}",
            exc_info=True,
        )
        raise  # 重新抛出异常，确保任务中断


def perform_enhanced_ocr(
    image: Image.Image,
    use_paddle: bool,
    paddle_engine: Optional[Any],
    target_text: Optional[str] = None,
    extract_number: bool = False,
    max_attempts: int = 4,
    collect_all_results: bool = False,
    tesseract_configs: Optional[List[str]] = None,  # 用于 Tesseract 的配置列表
    allow_tesseract_fallback: bool = False,  # 是否允许回退到Tesseract
) -> Union[bool, List[UnifiedNumberResult]]:
    """执行增强OCR，通过微服务内图像处理提高识别率。

    Args:
        image: 原始 PIL 图像。
        use_paddle: 是否使用 PaddleOCR 微服务。
        paddle_engine: OCR 客户端实例。
        target_text: 用于提前退出的目标文本 (可选)。
        extract_number: 是否尝试提取统一编号。
        max_attempts: 最大尝试次数。
        collect_all_results: 是否收集所有结果 (如果为False，找到target_text即返回True)。
        tesseract_configs: Tesseract 的配置列表 (可选)。
        allow_tesseract_fallback: 是否允许回退到Tesseract，默认False（要求高精度）。

    Returns:
        如果 collect_all_results=False 且找到 target_text，返回 True。
        如果 extract_number=True，返回 UnifiedNumberResult 列表。
        否则返回 False。
    """
    results: List[UnifiedNumberResult] = []
    if not image:
        error_msg = "输入图像为空"
        logger.error(error_msg)
        raise ValueError(error_msg)

    try:
        if use_paddle and paddle_engine is not None:
            # --- PaddleOCR 增强模式（微服务内图像处理）---
            logger.debug("执行 PaddleOCR 增强识别...")

            # 直接发送原始图像到微服务，微服务内生成图像变体
            # recognize_enhanced() 内部已做严格验证，失败时会抛出异常
            ocr_results = paddle_engine.recognize_enhanced(image, max_attempts)

            # 3. 处理结果
            for i, text in enumerate(ocr_results):
                if not text:
                    if extract_number:
                        results.append(
                            UnifiedNumberResult(None, f"PaddleOCR增强_{i+1} (失败)")
                        )
                    continue

                # 处理有效结果
                if target_text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text)
                    method_name = f"PaddleOCR增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        elif allow_tesseract_fallback:
            # --- Tesseract 增强模式 (仅在允许回退时) ---
            # 注意：在当前严格模式配置下，此分支永远不会执行
            logger.warning("PaddleOCR不可用，尝试回退到Tesseract（非严格模式）")

            processed_images = image_utils.get_enhanced_images_for_tesseract(image)
            if not processed_images:
                error_msg = "无法为 Tesseract 生成增强图像列表"
                logger.error(error_msg)
                raise RuntimeError(error_msg)

            # 准备配置
            configs = tesseract_configs or [
                "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
            ] * max_attempts

            # 运行Tesseract OCR
            ocr_texts = run_tesseract_enhanced(
                processed_images[:max_attempts], configs[:max_attempts]
            )

            # 处理结果（非严格模式允许部分失败）
            for i, text in enumerate(ocr_texts):
                if target_text and text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text) if text else None
                    method_name = f"Tesseract增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        else:
            # 严格模式：PaddleOCR不可用且不允许回退
            error_msg = "PaddleOCR不可用且不允许Tesseract回退，严格模式要求高精度"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

        # 返回最终结果
        return results if extract_number else False

    except Exception as e:
        # 严格模式：不允许静默失败，必须抛出异常中断任务
        logger.error(f"执行增强 OCR 失败: {e}", exc_info=True)
        raise  # 重新抛出异常，确保任务中断


# --- 结果验证 ---

# [这里可以考虑添加 validate_and_choose_number 函数]
