"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TablePageLayout } from "@/components/common/table-page-layout"
import { useReportDistribution } from "@/hooks/domain/issue/use-report-distribution"
import { type SelectedRecord } from "@/hooks/domain/issue/useReportListService"
import { ReportDistributionHeader } from "@/components/domain/reports/report-distribution-header"
import { ReportBasicInformation } from "@/components/domain/reports/pages/report-detail/tabs/report-basic-information"
import { ReportList } from "@/components/domain/reports/pages/report-detail/tabs/report-list"
import { OperationHistory } from "@/components/domain/reports/pages/report-detail/tabs/operation-history"
import { ReportArchives } from "@/components/domain/reports/pages/report-detail/tabs/report-archives"

// 修正后的模拟报告数据
const mockReports = [
  {
    id: "REP-2023-001",
    title: "地质勘察报告",
    project: "城市轨道交通项目",
    type: "勘察报告",
    date: "2023-01-15",
    totalCopies: 5, // 总份数
    remainingCopies: 4, // 剩余可发放份数
    distributionCount: 1, // 已发放份数
    firstDistribution: {
      time: "2023-02-10 14:30",
      distributor: "张三",
      distributorPhone: "13800138001",
      recipient: "李四",
      recipientPhone: "13900139001",
      copies: 1, // 第一次发放的份数 (只发放了1份)
    },
    secondDistribution: null, // 还未进行第二次发放
  },
  {
    id: "REP-2023-002",
    title: "环境影响评估",
    project: "城市轨道交通项目",
    type: "评估报告",
    date: "2023-02-20",
    totalCopies: 3,
    remainingCopies: 0,
    distributionCount: 3,
    firstDistribution: {
      time: "2023-03-05 09:15",
      distributor: "赵六",
      distributorPhone: "13600136001",
      recipient: "钱七",
      recipientPhone: "13500135001",
      copies: 3, // 第一次发放全部
    },
    secondDistribution: null,
  },
  {
    id: "REP-2023-003",
    title: "结构设计方案",
    project: "高层建筑项目",
    type: "设计报告",
    date: "2023-03-10",
    totalCopies: 2,
    remainingCopies: 2,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-004",
    title: "施工进度报告",
    project: "高层建筑项目",
    type: "进度报告",
    date: "2023-04-05",
    totalCopies: 4,
    remainingCopies: 0,
    distributionCount: 4,
    // 一次性全部发放完成的情况
    firstDistribution: {
      time: "2023-04-20 11:30",
      distributor: "孙八",
      distributorPhone: "13400134001",
      recipient: "周九",
      recipientPhone: "13300133001",
      copies: 4, // 第一次发放全部
    },
    secondDistribution: null,
  },
  {
    id: "REP-2023-005",
    title: "水文地质报告",
    project: "水利工程项目",
    type: "勘察报告",
    date: "2023-05-12",
    totalCopies: 4,
    remainingCopies: 0,
    distributionCount: 4,
    // 分两次发放完成的情况
    firstDistribution: {
      time: "2023-06-01 10:00",
      distributor: "郑十一",
      distributorPhone: "13100131001",
      recipient: "王十二",
      recipientPhone: "13000130001",
      copies: 1, // 第一次发放1份
    },
    secondDistribution: {
      time: "2023-06-15 14:20",
      distributor: "郑十一",
      distributorPhone: "13100131001",
      recipient: "王十二",
      recipientPhone: "13000130001",
      copies: 3, // 第二次发放剩余全部
    },
  },
  {
    id: "REP-2023-006",
    title: "材料检测报告",
    project: "桥梁工程项目",
    type: "检测报告",
    date: "2023-06-18",
    totalCopies: 6,
    remainingCopies: 6,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-007",
    title: "安全评估报告",
    project: "桥梁工程项目",
    type: "评估报告",
    date: "2023-07-22",
    totalCopies: 3,
    remainingCopies: 3,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-008",
    title: "工程造价分析",
    project: "水利工程项目",
    type: "分析报告",
    date: "2023-08-30",
    totalCopies: 2,
    remainingCopies: 2,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  // 复制数据以测试溢出和滚动条
  {
    id: "REP-2023-001-copy",
    title: "地质勘察报告(副本)",
    project: "城市轨道交通项目",
    type: "勘察报告",
    date: "2023-01-15",
    totalCopies: 5,
    remainingCopies: 4,
    distributionCount: 1,
    firstDistribution: {
      time: "2023-02-10 14:30",
      distributor: "张三",
      distributorPhone: "13800138001",
      recipient: "李四",
      recipientPhone: "13900139001",
      copies: 1,
    },
    secondDistribution: null,
  },
  {
    id: "REP-2023-002-copy",
    title: "环境影响评估(副本)",
    project: "城市轨道交通项目",
    type: "评估报告",
    date: "2023-02-20",
    totalCopies: 3,
    remainingCopies: 0,
    distributionCount: 3,
    firstDistribution: {
      time: "2023-03-05 09:15",
      distributor: "赵六",
      distributorPhone: "13600136001",
      recipient: "钱七",
      recipientPhone: "13500135001",
      copies: 3,
    },
    secondDistribution: null,
  },
  {
    id: "REP-2023-003-copy",
    title: "结构设计方案(副本)",
    project: "高层建筑项目",
    type: "设计报告",
    date: "2023-03-10",
    totalCopies: 2,
    remainingCopies: 2,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-004-copy",
    title: "施工进度报告(副本)",
    project: "高层建筑项目",
    type: "进度报告",
    date: "2023-04-05",
    totalCopies: 4,
    remainingCopies: 0,
    distributionCount: 4,
    firstDistribution: {
      time: "2023-04-20 11:30",
      distributor: "孙八",
      distributorPhone: "13400134001",
      recipient: "周九",
      recipientPhone: "13300133001",
      copies: 4,
    },
    secondDistribution: null,
  },
  {
    id: "REP-2023-005-copy",
    title: "水文地质报告(副本)",
    project: "水利工程项目",
    type: "勘察报告",
    date: "2023-05-12",
    totalCopies: 4,
    remainingCopies: 0,
    distributionCount: 4,
    firstDistribution: {
      time: "2023-06-01 10:00",
      distributor: "郑十一",
      distributorPhone: "13100131001",
      recipient: "王十二",
      recipientPhone: "13000130001",
      copies: 1,
    },
    secondDistribution: {
      time: "2023-06-15 14:20",
      distributor: "郑十一",
      distributorPhone: "13100131001",
      recipient: "王十二",
      recipientPhone: "13000130001",
      copies: 3,
    },
  },
  {
    id: "REP-2023-006-copy",
    title: "材料检测报告(副本)",
    project: "桥梁工程项目",
    type: "检测报告",
    date: "2023-06-18",
    totalCopies: 6,
    remainingCopies: 6,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-007-copy",
    title: "安全评估报告(副本)",
    project: "桥梁工程项目",
    type: "评估报告",
    date: "2023-07-22",
    totalCopies: 3,
    remainingCopies: 3,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
  {
    id: "REP-2023-008-copy",
    title: "工程造价分析(副本)",
    project: "水利工程项目",
    type: "分析报告",
    date: "2023-08-30",
    totalCopies: 2,
    remainingCopies: 2,
    distributionCount: 0,
    firstDistribution: null,
    secondDistribution: null,
  },
]

// 类型定义

// 移除 ReportRecord，直接使用 SelectedRecord

// 错误边界组件
const ErrorBoundary: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ 
  children, 
  fallback = <div className="text-center py-8 text-red-500">发生错误，请刷新页面重试</div> 
}) => {
  try {
    return <>{children}</>
  } catch (error) {
    console.error('ReportDetailPage Error:', error)
    return <>{fallback}</>
  }
}

interface ReportDetailPageProps {
  issueNumber: string
}

export function ReportDetailPage({ issueNumber }: ReportDetailPageProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("basic")
  const [selectedRecords, setSelectedRecords] = useState<SelectedRecord[]>([])
  const [error, setError] = useState<string | null>(null)

  const {
    reportDistribution,
    isLoading,
    isNewReport,
    isSaving,
    isProcessing,
    lastSavedAt,
    hasChanges,
    saveAsDraft,
    lockReport,
    unlockReport,
    issueReport,
    printReport,
    deleteReport,
    handleInputChange,

  } = useReportDistribution({ 
    id: issueNumber, 
    selectedReports: selectedRecords,
  })

  // 检查是否可编辑（创建中或草稿状态）
  const isEditable = reportDistribution?.status === "creating" || reportDistribution?.status === "draft"

  // 检查是否可打印（已锁定、已发放或已删除状态）
  const canPrint = reportDistribution?.status === "locked" || reportDistribution?.status === "issued" || reportDistribution?.status === "deleted"

  // 业务逻辑已移到 ReportList 组件内部

  // getReportStatus 逻辑已移到 ReportList 组件内部

  // 初始化已选报告 - 简化逻辑，直接创建 SelectedRecord
  useEffect(() => {
    let mounted = true
    
    // 只在首次加载且没有已选记录时初始化
    if (selectedRecords.length > 0) {
      return
    }
    
    try {
      // 使用测试数据初始化，转换为 SelectedRecord 格式
      const testReports = mockReports
        .filter(report => report.id.includes("-copy"))
        .map(report => ({
          id: report.id,
          sampleNumber: report.id,
          clientCompany: report.project || "未知公司",
          clientContact: "未知联系人",
          clientDate: report.date || new Date().toISOString().split('T')[0],
          projectNumber: report.id,
          projectName: report.title || "未知项目",
          projectLocation: "未知位置",
          totalCopies: report.totalCopies,
          remainingCopies: report.remainingCopies,
          issueCopies: 1, // 默认发放份数
          firstDistribution: report.firstDistribution,
          secondDistribution: report.secondDistribution,
          distributionCount: report.distributionCount,
        }))
      
      if (mounted) {
        setSelectedRecords(testReports)
      }
    } catch (error) {
      console.error('Error initializing selected records:', error)
      setError('初始化记录时发生错误')
    }

    return () => {
      mounted = false
    }
  }, []) // 移除依赖，只在组件挂载时运行一次



  if (error) {
    return (
      <TablePageLayout
        title="发放单详情"
        subtitle="发生错误"
        actions={[
          {
            label: "返回发放单列表",
            icon: <ArrowLeft className="h-4 w-4" />,
            href: "/reports",
            variant: "outline" as const
          }
        ]}
      >
        <div className="text-center py-8">
          <div className="text-red-500 mb-4">{error}</div>
          <button 
            onClick={() => setError(null)} 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </TablePageLayout>
    )
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2">加载中...</p>
        </div>
      </div>
    )
  }

  if (!reportDistribution) {
    return (
      <TablePageLayout
        title="发放单详情"
        subtitle="未找到发放单"
        actions={[
          {
            label: "返回发放单列表",
            icon: <ArrowLeft className="h-4 w-4" />,
            href: "/reports",
            variant: "outline" as const
          }
        ]}
      >
        <div className="text-center py-8 text-gray-500">未找到发放单数据或无权访问</div>
      </TablePageLayout>
    )
  }

  // 定义页面操作按钮
  const actions = [
    {
      label: "返回发放单列表",
      icon: <ArrowLeft className="h-4 w-4" />,
      onClick: () => router.push("/reports"),
      variant: "outline" as const
    }
  ]

  return (
    <ErrorBoundary>
      <TablePageLayout
        title={isNewReport ? "创建发放单" : "发放单详情"}
        subtitle={!isNewReport ? `发放单 ${reportDistribution.issueNumber}` : ""}
        actions={actions}
        statusCards={
          <ReportDistributionHeader
            report={reportDistribution}
            isNewReport={isNewReport}
            lastSavedAt={lastSavedAt}
            isSaving={isSaving}
            isProcessing={isProcessing}
            canAutoSave={() => true}

            onSaveAsDraft={saveAsDraft}
            onLock={lockReport}
            onUnlock={unlockReport}
            onSubmit={issueReport}
            onPrint={printReport}
            onDelete={deleteReport}
          />
        }
        fixedTabs={
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="reports">报告清单</TabsTrigger>
              <TabsTrigger value="archives">档案列表</TabsTrigger>
              <TabsTrigger value="history">操作历史</TabsTrigger>
            </TabsList>
          </Tabs>
        }
      >
        <Tabs value={activeTab} className="h-full flex flex-col">
          <div className="hidden">
            <TabsList>
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="reports">报告清单</TabsTrigger>
              <TabsTrigger value="archives">档案列表</TabsTrigger>
              <TabsTrigger value="history">操作历史</TabsTrigger>
            </TabsList>
          </div>
          
          <div
            className="flex-1 overflow-hidden"
            style={{ display: activeTab === "basic" ? "block" : "none" }}
          >
            <ErrorBoundary>
              <div className="h-full overflow-y-auto">
                <ReportBasicInformation
                  reportDistribution={reportDistribution}
                  isNewReport={isNewReport}
                  isEditable={isEditable}
                  canPrint={canPrint}
                  isProcessingReport={isProcessing}
                  handleInputChange={handleInputChange}
    
                  printReport={printReport}
                />
              </div>
            </ErrorBoundary>
          </div>
          
          <div
            className="flex-1 overflow-hidden"
            style={{ display: activeTab === "reports" ? "block" : "none" }}
          >
            <ErrorBoundary>
              <div className="h-full">
                <ReportList
                  selectedRecords={selectedRecords}
                  mockReports={mockReports}
                  isEditable={isEditable}
                  setSelectedRecords={setSelectedRecords}
                />
              </div>
            </ErrorBoundary>
          </div>
          
          <div
            className="flex-1 overflow-hidden"
            style={{ display: activeTab === "archives" ? "block" : "none" }}
          >
            <ErrorBoundary>
              <div className="h-full">
                <ReportArchives 
                  selectedRecords={selectedRecords}
                />
              </div>
            </ErrorBoundary>
          </div>
          
          <div
            className="flex-1 overflow-hidden"
            style={{ display: activeTab === "history" ? "block" : "none" }}
          >
            <ErrorBoundary>
              <div className="h-full">
                <OperationHistory history={reportDistribution.history || []} />
              </div>
            </ErrorBoundary>
          </div>
        </Tabs>
      </TablePageLayout>
    </ErrorBoundary>
  )
}
