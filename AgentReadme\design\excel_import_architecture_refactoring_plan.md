# Excel导入架构重构计划

**文档版本**: v1.0  
**创建时间**: 2025-06-02  
**最后更新**: 2025-06-02  
**状态**: 设计阶段

## 📋 概述

本文档描述了Excel导入功能的全面架构重构计划，基于六大业务状态进行组件、Service和Hook的重新设计，以解决当前代码库中的职责模糊、文件过大、维护困难等问题。

## 🎯 重构目标

### 🔧 解决的问题

1. **单文件过大**: `excel-import-with-conflict-resolution.tsx` (1600+ 行)
2. **职责混杂**: Service层和Hook层职责边界不清
3. **状态管理复杂**: UI状态与业务状态耦合严重
4. **维护困难**: 修改一个状态的逻辑需要在多个位置改动
5. **测试困难**: 大文件导致单元测试覆盖困难

### ✨ 期望收益

1. **架构统一**: UI/Hook/Service 三层完全对应六大状态
2. **职责清晰**: 每个状态独立管理，边界明确
3. **维护性提升**: 修改某状态只需关注对应文件
4. **测试友好**: 每个状态可独立测试
5. **代码复用**: 细粒度组件便于跨项目复用

## 📊 当前架构分析

### 🗂️ 现有文件结构

```文件结构
frontend/
├── app/records/import/page.tsx                    # 页面容器 (316行)
├── components/records/import/
│   ├── excel-import-with-conflict-resolution.tsx  # 主业务组件 (1600+ 行)
│   └── conflict-resolution-modal.tsx              # 冲突处理模态框
├── hooks/useExcelImportSession.ts                 # Hook层 (800+ 行)
└── services/excel-import-service.ts               # Service层 (1300+ 行)
```

### ⚠️ 架构问题

#### 📄 **page.tsx 问题**

- **职责边界模糊**: 既管理页面布局，又处理会话状态检查
- **重复状态管理**: 与业务组件都在调用相同的API
- **状态同步复杂**: 需要与业务组件进行状态同步

#### 📦 **excel-import-with-conflict-resolution.tsx 问题**

- **单一文件过大**: 1600+ 行代码难以维护
- **职责过多**: 处理所有六个状态的UI逻辑
- **状态切换复杂**: 大量的条件渲染和状态判断
- **测试困难**: 难以为单一状态编写单元测试

#### 🪝 **useExcelImportSession.ts 问题**

- **职责混杂**: 状态管理 + 心跳逻辑 + API调用 + UI状态
- **代码过长**: 800+ 行难以理解和维护
- **依赖过多**: 与多个不相关的状态耦合

#### 🔧 **excel-import-service.ts 问题**

- **职责过多**: API调用 + 数据转换 + 错误处理 + 重试逻辑
- **文件过大**: 1300+ 行代码
- **缺乏组织**: 所有API混在一个文件中

## 🎯 重构方案

### 📁 **新的文件组织结构**

#### 🎨 **UI组件层 - 基于六大状态**

```文件结构
frontend/components/records/import/
├── index.ts                              # 统一导出
├── excel-import-container.tsx            # 状态路由器和协调器
├── steps/                                # 六大状态组件
│   ├── index.ts                         # 导出所有步骤组件
│   ├── file-selection-step.tsx         # select 状态
│   ├── file-upload-step.tsx            # upload 状态  
│   ├── analysis-step.tsx               # analyze 状态
│   ├── conflict-resolution-step.tsx    # confirm 状态
│   ├── importing-step.tsx              # importing 状态
│   ├── completion-step.tsx             # completed 状态
│   └── cancelled-step.tsx              # cancelled 状态
├── errors/                              # 错误状态组件
│   ├── index.ts                        # 导出所有错误组件
│   ├── operation-error-step.tsx        # operation_error 状态
│   ├── session-error-step.tsx          # session_error 状态
│   └── network-error-step.tsx          # 网络错误状态
├── shared/                              # 共享组件
│   ├── progress-indicator.tsx          # 进度指示器
│   ├── session-info-card.tsx           # 会话信息卡片
│   ├── error-boundary.tsx              # 错误边界
│   ├── error-recovery-actions.tsx      # 错误恢复操作组件
│   └── cancel-confirmation-modal.tsx   # 取消确认模态框
└── modals/                              # 模态框组件
    ├── conflict-resolution-modal.tsx   # 冲突处理模态框
    └── confirmation-modal.tsx          # 确认模态框
```

#### 🪝 **Hook层 - 基于六大状态**

```文件结构
frontend/hooks/excel-import/
├── index.ts                         # 统一导出
├── core/
│   ├── useExcelImportCore.ts       # 核心状态机管理
│   └── useSessionState.ts          # 共享会话状态
├── states/                          # 状态专用Hook
│   ├── useFileSelection.ts         # select 状态Hook
│   ├── useFileUpload.ts            # upload 状态Hook
│   ├── useAnalysis.ts              # analyze 状态Hook
│   ├── useConflictResolution.ts    # confirm 状态Hook
│   ├── useImporting.ts             # importing 状态Hook
│   └── useCompletion.ts            # completed 状态Hook
├── errors/                          # 错误状态专用Hook
│   ├── useOperationError.ts        # operation_error 状态Hook
│   ├── useSessionError.ts          # session_error 状态Hook
│   └── useNetworkError.ts          # network_error 状态Hook
└── shared/                          # 共享Hook
    ├── useHeartbeat.ts             # 心跳管理
    ├── useProgressTracking.ts      # 进度跟踪
    ├── useErrorHandling.ts         # 错误处理
    └── useErrorRecovery.ts         # 错误恢复机制
```

#### 🔧 **Service层 - 基于六大状态**

```文件结构
frontend/services/excel-import/
├── index.ts                          # 统一导出
├── base/
│   ├── base-api-service.ts          # 基础API服务
│   └── types.ts                     # 通用类型定义
├── states/                           # 状态专用服务
│   ├── file-selection-service.ts    # select 状态API
│   ├── file-upload-service.ts       # upload 状态API
│   ├── analysis-service.ts          # analyze 状态API
│   ├── conflict-resolution-service.ts # confirm 状态API
│   ├── importing-service.ts         # importing 状态API
│   └── completion-service.ts        # completed 状态API
└── utils/                            # 工具函数
    ├── retry-logic.ts               # 重试逻辑
    ├── error-handling.ts            # 错误处理
    └── data-transformation.ts       # 数据转换
```

### 🔗 **组件职责定义**

#### 📄 **page.tsx (页面容器)**

```typescript
// 🏠 纯页面级职责
✅ 页面布局、标题、导航 (PageLayout)
✅ 认证状态检查和路由守卫
✅ 页面级错误边界
❌ 不再直接管理导入会话状态
❌ 不再处理会话冲突检查
```

#### 📦 **excel-import-container.tsx (状态路由器)**

```typescript
// 🎯 会话状态管理和路由
✅ 统一的会话状态管理 (useExcelImportCore)
✅ 根据当前状态路由到对应步骤组件
✅ 会话冲突检查和处理
✅ 状态间的转换逻辑
✅ 全局进度指示器
✅ 错误状态处理
```

#### 🧩 **步骤组件 (file-selection-step.tsx 等)**

```typescript
// 🔧 专注单一状态的UI和交互
✅ 只处理自己状态的UI逻辑
✅ 使用对应的专用Hook
✅ 状态完成后触发状态转换
✅ 本地错误处理和用户反馈
```

### 🔄 **数据流设计**

#### 📊 **状态管理流程**

```mermaid
graph LR
    A[用户操作] --> B[步骤组件]
    B --> C[状态专用Hook]
    C --> D[状态专用Service]
    D --> E[API调用]
    E --> F[API响应]
    F --> G[Hook响应处理]
    G --> H[核心状态管理]
    H --> I[状态路由器]
    I --> J[状态转换]
    J --> K[组件重渲染]
    K --> A
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#fce4ec
    style G fill:#e8f5e8
    style H fill:#e3f2fd
    style I fill:#e3f2fd
    style J fill:#f1f8e9
    style K fill:#f3e5f5
```

#### 🎯 **状态转换逻辑**

```typescript
// 核心状态机 (useExcelImportCore)
type ImportState = 
  | 'select' | 'upload' | 'analyze' | 'confirm' | 'importing' | 'completed'
  | 'cancelled' // 取消状态
  | 'operation_error' | 'session_error' | 'network_error';

const stateTransitions: Record<ImportState, ImportState[]> = {
  // 正常业务流程 - 单向流程，不支持回退
  select: ['upload'],
  upload: ['analyze'],
  analyze: ['confirm'],
  confirm: ['importing'],
  importing: ['completed'],
  completed: ['select'], // 支持开始新导入
  
  // 取消状态
  cancelled: ['select'], // 取消后可以重新开始
  
  // 错误状态恢复路径
  operation_error: [
    'select', 'upload', 'analyze', 'confirm', // 导入前错误可恢复到任意状态
    'completed' // 导入后的操作错误（如生成报告失败）可恢复到完成状态
    // 注意：不包含'importing'，因为导入过程错误不能简单重试导入
  ],
  session_error: ['select'], // 会话错误需要用户确认后重新开始
  network_error: [
    'select', 'upload', 'analyze', 'confirm', 'importing', 'completed'
    // 网络恢复后可重试任何状态，包括completed（获取最终报告）
  ]
};

// 取消操作规则 - 定义哪些状态支持取消操作
const cancellableStates: ImportState[] = [
  'upload',    // 上传过程中可以取消
  'analyze',   // 分析过程中可以取消
  'confirm',   // 冲突处理过程中可以取消
  // 注意：importing状态通常不允许取消，避免数据不一致
  // select和completed状态不需要取消
];

// 错误转换规则 - 定义哪些状态可以转换到哪些错误状态
const errorTransitions: Record<ImportState, ImportState[]> = {
  select: ['session_error'], // 选择阶段主要是会话问题
  upload: ['operation_error', 'network_error'], // 上传可能失败或网络问题
  analyze: ['operation_error', 'network_error'], // 分析可能失败或网络问题
  confirm: ['operation_error', 'session_error'], // 冲突处理失败或会话问题
  importing: ['operation_error', 'network_error'], // 导入失败或网络问题
  completed: ['network_error'], // 完成状态主要是获取报告的网络问题
  
  // 取消和错误状态之间一般不互相转换
  cancelled: [],
  operation_error: [],
  session_error: [],
  network_error: []
};

// 取消操作处理
interface CancelContext {
  canCancel: boolean;
  cancelReason?: string;
  cleanupRequired: boolean; // 是否需要清理资源
  confirmRequired: boolean; // 是否需要用户确认
}

const getCancelContext = (currentState: ImportState): CancelContext => {
  if (!cancellableStates.includes(currentState)) {
    return { canCancel: false, cleanupRequired: false, confirmRequired: false };
  }
  
  switch (currentState) {
    case 'upload':
      return {
        canCancel: true,
        cancelReason: '上传将被中断',
        cleanupRequired: true, // 需要清理临时文件
        confirmRequired: true
      };
    case 'analyze':
      return {
        canCancel: true,
        cancelReason: '分析将被中断',
        cleanupRequired: true, // 需要清理分析结果
        confirmRequired: true
      };
    case 'confirm':
      return {
        canCancel: true,
        cancelReason: '冲突处理将被放弃',
        cleanupRequired: false, // 冲突处理可以保留状态
        confirmRequired: true
      };
    default:
      return { canCancel: false, cleanupRequired: false, confirmRequired: false };
  }
};

// 错误类型定义
interface ErrorContext {
  type: 'operation_error' | 'session_error' | 'network_error';
  source: ImportState; // 出错时的原始状态
  message: string;
  recoverable: boolean;
  retryable: boolean;
  suggestions: string[];
  canReturnToSource: boolean; // 是否可以返回到出错的原始状态
}

// 错误恢复策略
const getRecoveryOptions = (errorType: string, source: ImportState): ImportState[] => {
  switch (errorType) {
    case 'operation_error':
      if (source === 'importing') {
        // 导入过程错误：不能重试导入，但可以检查是否有部分成功的结果
        return ['completed', 'select']; // 尝试获取结果或重新开始
      }
      return stateTransitions.operation_error;
      
    case 'session_error':
      // 会话错误需要用户确认，然后强制重新开始
      // UI层会显示alert确认对话框，用户确认后自动跳转到select
      return stateTransitions.session_error;
      
    case 'network_error':
      return stateTransitions.network_error;
      
    default:
      return ['select']; // 默认重新开始
  }
};

// 会话错误处理的特殊逻辑
const handleSessionError = async (errorMessage: string): Promise<void> => {
  // 显示确认对话框（对应当前的alert实现）
  const userConfirmed = window.confirm(
    `会话已过期或发生错误：${errorMessage}\n\n请点击确定重新开始导入流程。`
  );
  
  if (userConfirmed) {
    // 清理当前会话状态
    await clearSessionData();
    // UI会自动更新到select状态（在useExcelImportCore中处理）
  }
  // 如果用户取消，保持在当前错误状态
};

const clearSessionData = async (): Promise<void> => {
  // 清理本地会话数据
  sessionStorage.removeItem('excel-import-session');
  // 通知后端清理会话
  try {
    await fetch('/api/excel-import/clear-session', { method: 'POST' });
  } catch (error) {
    console.warn('Failed to clear server session:', error);
  }
};
```

## 🚀 实施计划

### 📅 **阶段划分**

#### 🎯 **阶段一: Service层重构 (1-2周)**

1. **创建新的Service架构**
   - 建立base-api-service基础类
   - 实现状态专用Service类
   - 迁移现有API调用逻辑

2. **向后兼容**
   - 保持原有API接口不变
   - 逐步替换内部实现
   - 添加完整的单元测试

#### 🪝 **阶段二: Hook层重构 (1-2周)**

1. **创建新的Hook架构**
   - 实现useExcelImportCore核心状态机
   - 创建状态专用Hook
   - 迁移现有状态管理逻辑

2. **渐进式迁移**
   - 先在新建测试页面使用新Hook
   - 验证功能完整性后替换原有Hook
   - 保持现有组件接口不变

#### 🎨 **阶段三: UI组件重构 (2-3周)**

1. **创建步骤组件**
   - 按状态拆分现有大组件
   - 实现excel-import-container路由器
   - 建立共享组件库

2. **逐步替换**
   - 一次替换一个状态组件
   - 保持用户体验一致
   - 完善测试覆盖

#### 📄 **阶段四: 页面容器重构 (1周)**

1. **简化page.tsx职责**
   - 移除会话状态管理逻辑
   - 专注页面布局和认证
   - 集成新的容器组件

### ✅ **验收标准**

#### 🔧 **技术指标**

- [ ] 单个文件不超过300行代码
- [ ] 每个状态组件单元测试覆盖率 > 90%
- [ ] 构建时间不增加超过10%
- [ ] 运行时性能不下降

#### 🎯 **功能指标**

- [ ] 所有现有功能保持不变
- [ ] 用户界面和交互体验一致
- [ ] 错误处理和边界情况覆盖完整
- [ ] 浏览器兼容性保持一致
- [ ] **错误恢复机制**:
  - [ ] operation_error状态下可重试失败的操作
  - [ ] session_error状态下能正确引导用户重新开始
  - [ ] network_error状态下支持网络恢复后的自动重试
  - [ ] 所有错误状态都提供明确的用户指导
- [ ] **错误信息质量**:
  - [ ] 错误消息对用户友好且具体
  - [ ] 提供可操作的解决建议
  - [ ] 技术错误转换为用户可理解的描述

#### 📚 **维护性指标**

- [ ] 新人理解单个状态组件 < 30分钟
- [ ] 修改单个状态的代码变更 < 3个文件
- [ ] 添加新状态的开发时间 < 2天
- [ ] 代码审查时间减少50%

## 🔍 **示例实现**

### 📦 **excel-import-container.tsx 核心逻辑**

```typescript
import { useExcelImportCore } from '@/hooks/excel-import/core/useExcelImportCore';
import { FileSelectionStep } from './steps/file-selection-step';
import { FileUploadStep } from './steps/file-upload-step';
import { CancelledStep } from './steps/cancelled-step';
import { OperationErrorStep } from './errors/operation-error-step';
import { SessionErrorStep } from './errors/session-error-step';
import { NetworkErrorStep } from './errors/network-error-step';
// ... 其他步骤组件

export function ExcelImportContainer() {
  const { 
    currentState, 
    sessionInfo, 
    error, 
    errorContext,
    cancelContext,
    transitionTo,
    cancelOperation,
    recoverFromError
  } = useExcelImportCore();

  const handleCancel = async () => {
    const success = await cancelOperation();
    if (!success) {
      // 处理取消失败的情况
      console.error('Failed to cancel operation');
    }
  };

  const renderCurrentStep = () => {
    switch (currentState) {
      case 'select':
        return <FileSelectionStep onNext={() => transitionTo('upload')} />;
      case 'upload':
        return <FileUploadStep 
          onNext={() => transitionTo('analyze')} 
          onCancel={handleCancel}
        />;
      case 'analyze':
        return <AnalysisStep 
          onNext={() => transitionTo('confirm')}
          onCancel={handleCancel}
        />;
      case 'confirm':
        return <ConflictResolutionStep 
          onNext={() => transitionTo('importing')}
          onCancel={handleCancel}
        />;
      case 'importing':
        return <ImportingStep 
          onNext={() => transitionTo('completed')}
          // 注意：importing状态不提供取消选项
        />;
      case 'completed':
        return <CompletionStep 
          onReset={() => transitionTo('select')}
        />;
      
      // 取消状态
      case 'cancelled':
        return <CancelledStep 
          onRestart={() => transitionTo('select')}
        />;
      
      // 错误状态处理
      case 'operation_error':
        return <OperationErrorStep 
          onRetry={() => recoverFromError()}
          onReset={() => transitionTo('select')}
          onBack={() => transitionTo(errorContext?.source || 'select')}
        />;
      case 'session_error':
        return <SessionErrorStep 
          onReset={() => transitionTo('select')}
        />;
      case 'network_error':
        return <NetworkErrorStep 
          onRetry={() => recoverFromError()}
          onReset={() => transitionTo('select')}
        />;
        
      default:
        return <div>未知状态: {currentState}</div>;
    }
  };

  return (
    <div className="excel-import-container">
      {/* 进度指示器 */}
      <ProgressIndicator 
        currentState={currentState} 
        showCancelButton={cancelContext?.canCancel}
        onCancel={handleCancel}
      />
      
      {/* 会话信息 */}
      {sessionInfo && <SessionInfoCard session={sessionInfo} />}
      
      {/* 当前步骤内容 */}
      {renderCurrentStep()}
    </div>
  );
}
```

### 🪝 **useExcelImportCore.ts 核心状态机**

```typescript
import { useState, useEffect } from 'react';
import { useSessionState } from './useSessionState';

export function useExcelImportCore() {
  const [currentState, setCurrentState] = useState<ImportState>('select');
  const [cancelContext, setCancelContext] = useState<CancelContext | null>(null);
  const { sessionInfo, error, refreshSession } = useSessionState();

  const transitionTo = (newState: ImportState) => {
    // 验证状态转换是否合法
    if (isValidTransition(currentState, newState)) {
      setCurrentState(newState);
    } else {
      console.warn(`Invalid state transition: ${currentState} -> ${newState}`);
    }
  };

  const cancelOperation = async (): Promise<boolean> => {
    const context = getCancelContext(currentState);
    if (!context.canCancel) {
      console.warn(`Cannot cancel from state: ${currentState}`);
      return false;
    }

    // 如果需要用户确认
    if (context.confirmRequired) {
      const confirmed = await showCancelConfirmation(context.cancelReason);
      if (!confirmed) return false;
    }

    try {
      // 执行取消逻辑
      if (context.cleanupRequired) {
        await performCleanup(currentState);
      }
      
      // 调用后端取消API
      await cancelImportSession();
      
      // 转换到取消状态
      setCurrentState('cancelled');
      return true;
    } catch (error) {
      console.error('Cancel operation failed:', error);
      return false;
    }
  };

  const recoverFromError = async () => {
    // 错误恢复逻辑
    if (errorContext) {
      const recoveryOptions = getRecoveryOptions(errorContext.type, errorContext.source);
      // 默认恢复到第一个可用选项
      if (recoveryOptions.length > 0) {
        transitionTo(recoveryOptions[0]);
      }
    }
  };

  // 更新取消上下文
  useEffect(() => {
    const context = getCancelContext(currentState);
    setCancelContext(context);
  }, [currentState]);

  // 根据会话状态自动同步UI状态
  useEffect(() => {
    if (sessionInfo?.status) {
      const uiState = mapSessionStatusToUIState(sessionInfo.status);
      if (uiState !== currentState) {
        // 特殊处理：会话错误需要用户确认
        if (uiState === 'session_error') {
          handleSessionError(sessionInfo.errorMessage || '会话已过期');
        } else {
          setCurrentState(uiState);
        }
      }
    }
  }, [sessionInfo?.status, currentState]);

  return {
    currentState,
    sessionInfo,
    error,
    cancelContext,
    transitionTo,
    cancelOperation,
    recoverFromError,
    refreshSession
  };
}

// 辅助函数
const isValidTransition = (from: ImportState, to: ImportState): boolean => {
  return stateTransitions[from]?.includes(to) || false;
};

const showCancelConfirmation = async (reason?: string): Promise<boolean> => {
  // 显示确认对话框的逻辑
  return window.confirm(`确认取消操作？${reason || ''}`);
};

const performCleanup = async (state: ImportState): Promise<void> => {
  // 根据状态执行相应的清理操作
  switch (state) {
    case 'upload':
      // 清理上传的临时文件
      await cleanupUploadFiles();
      break;
    case 'analyze':
      // 清理分析结果
      await cleanupAnalysisResults();
      break;
    // 其他状态的清理逻辑
  }
};

const cancelImportSession = async (): Promise<void> => {
  // 调用后端取消API
  await fetch('/api/excel-import/cancel', { method: 'POST' });
};

const mapSessionStatusToUIState = (status: string): ImportState => {
  // 将后端状态映射到UI状态
  const statusMap: Record<string, ImportState> = {
    'file_selected': 'select',
    'uploading': 'upload',
    'analyzing': 'analyze',
    'conflict_resolution_pending': 'confirm',
    'importing': 'importing',
    'completed': 'completed',
    'cancelled': 'cancelled',
    'error': 'operation_error'
  };
  return statusMap[status] || 'select';
};
```

### 🧩 **file-upload-step.tsx 步骤组件示例**

```typescript
import { useFileUpload } from '@/hooks/excel-import/states/useFileUpload';

interface FileUploadStepProps {
  onNext: () => void;
  onCancel: () => void;
}

export function FileUploadStep({ onNext, onCancel }: FileUploadStepProps) {
  const {
    uploadProgress,
    uploadFile,
    isUploading,
    error,
    selectedFile
  } = useFileUpload();

  const handleFileSelect = async (file: File) => {
    const success = await uploadFile(file);
    if (success) {
      onNext(); // 上传成功后自动转到下一状态
    }
  };

  return (
    <div className="file-upload-step">
      <h3>上传Excel文件</h3>
      
      {/* 文件选择区域 */}
      <FileDropzone onFileSelect={handleFileSelect} disabled={isUploading} />
      
      {/* 上传进度 */}
      {isUploading && (
        <ProgressBar progress={uploadProgress} />
      )}
      
      {/* 错误提示 */}
      {error && <ErrorAlert message={error} />}
      
      {/* 操作按钮 */}
      <div className="step-actions">
        <Button variant="outline" onClick={onCancel}>
          取消上传
        </Button>
        <Button 
          onClick={() => selectedFile && handleFileSelect(selectedFile)}
          disabled={!selectedFile || isUploading}
        >
          开始上传
        </Button>
      </div>
    </div>
  );
}
```

### 🚫 **cancelled-step.tsx 取消状态组件示例**

```typescript
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface CancelledStepProps {
  onRestart: () => void;
}

export function CancelledStep({ onRestart }: CancelledStepProps) {
  return (
    <div className="cancelled-step">
      <div className="cancelled-header">
        <XCircle className="h-12 w-12 text-orange-500" />
        <h3 className="text-2xl font-semibold text-orange-700">操作已取消</h3>
        <p className="text-gray-600">Excel导入流程已被成功取消</p>
      </div>
      
      <div className="cancelled-content">
        <div className="status-summary">
          <h4 className="font-medium">取消状态说明:</h4>
          <ul className="status-list">
            <li className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>已停止当前操作</span>
            </li>
            <li className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>已清理临时文件</span>
            </li>
            <li className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>会话状态已重置</span>
            </li>
          </ul>
        </div>
        
        <div className="next-steps">
          <h4 className="font-medium">您可以:</h4>
          <ul className="action-list">
            <li>• 重新开始Excel导入流程</li>
            <li>• 检查和修改Excel文件后再次导入</li>
            <li>• 返回到记录管理页面</li>
          </ul>
        </div>
      </div>
      
      <div className="cancelled-actions">
        <Button 
          onClick={onRestart}
          className="restart-button"
          size="lg"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          重新开始导入
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => window.location.href = '/records'}
          className="back-to-records"
        >
          返回记录管理
        </Button>
      </div>
    </div>
  );
}
```

### 🚨 **operation-error-step.tsx 错误状态组件示例**

```typescript
import { useOperationError } from '@/hooks/excel-import/errors/useOperationError';
import { ErrorRecoveryActions } from '../shared/error-recovery-actions';

interface OperationErrorStepProps {
  onRetry: () => void;
  onReset: () => void;
  onBack: () => void;
  onNavigateTo: (state: ImportState) => void;
}

export function OperationErrorStep({ onRetry, onReset, onBack, onNavigateTo }: OperationErrorStepProps) {
  const {
    errorContext,
    isRetrying,
    retryOperation,
    getRecoverySuggestions,
    getAvailableRecoveryOptions
  } = useOperationError();

  const handleRetry = async () => {
    const result = await retryOperation();
    if (result.success) {
      if (result.targetState) {
        onNavigateTo(result.targetState); // 导航到特定状态
      } else {
        onRetry(); // 重试成功，返回原状态
      }
    }
  };

  const recoveryOptions = getAvailableRecoveryOptions();

  return (
    <div className="operation-error-step">
      <div className="error-header">
        <AlertTriangle className="h-8 w-8 text-red-500" />
        <h3 className="text-xl font-semibold text-red-700">操作失败</h3>
        <p className="text-sm text-gray-600">在{getStateDisplayName(errorContext?.source)}阶段发生错误</p>
      </div>
      
      <div className="error-content">
        <p className="error-message">{errorContext?.message}</p>
        
        {/* 特殊情况提示 */}
        {errorContext?.source === 'importing' && (
          <div className="import-error-info">
            <h4>导入过程错误说明:</h4>
            <p>导入过程中发生错误，系统将检查是否有部分数据导入成功。</p>
            <p>如果有部分成功，您可以查看导入结果；如果完全失败，需要重新开始。</p>
          </div>
        )}
        
        {/* 错误详情 */}
        <div className="error-details">
          <p><strong>错误类型:</strong> {errorContext?.type}</p>
          <p><strong>出错阶段:</strong> {getStateDisplayName(errorContext?.source)}</p>
          <p><strong>可恢复:</strong> {errorContext?.recoverable ? '是' : '否'}</p>
          <p><strong>可重试:</strong> {errorContext?.retryable ? '是' : '否'}</p>
        </div>
        
        {/* 恢复建议 */}
        {errorContext?.suggestions && (
          <div className="recovery-suggestions">
            <h4>建议解决方案:</h4>
            <ul>
              {errorContext.suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {/* 智能恢复选项 */}
      <div className="recovery-options">
        <h4>恢复选项:</h4>
        <div className="option-buttons">
          {/* 重试按钮 */}
          {errorContext?.retryable && (
            <Button 
              onClick={handleRetry} 
              disabled={isRetrying}
              className="retry-button"
            >
              {isRetrying ? '重试中...' : '重试操作'}
            </Button>
          )}
          
          {/* 智能恢复选项 */}
          {recoveryOptions.map(option => (
            <Button
              key={option}
              variant="outline"
              onClick={() => onNavigateTo(option)}
              className="recovery-option"
            >
              {getRecoveryButtonText(option, errorContext?.source)}
            </Button>
          ))}
          
          {/* 重新开始 */}
          <Button 
            variant="destructive" 
            onClick={onReset}
            className="reset-button"
          >
            重新开始
          </Button>
        </div>
      </div>
    </div>
  );
}

// 辅助函数
const getStateDisplayName = (state?: ImportState): string => {
  const stateNames = {
    select: '文件选择',
    upload: '文件上传',
    analyze: '数据分析',
    confirm: '冲突处理',
    importing: '数据导入',
    completed: '导入完成'
  };
  return stateNames[state] || '未知';
};

const getRecoveryButtonText = (targetState: ImportState, sourceState?: ImportState): string => {
  if (targetState === 'select') return '重新开始';
  if (targetState === 'completed' && sourceState === 'importing') {
    return '查看导入结果';
  }
  
  const stateActions = {
    upload: '重新上传',
    analyze: '重新分析',
    confirm: '重新处理冲突',
    importing: '重新导入',
    completed: '查看结果'
  };
  
  return stateActions[targetState] || `返回到${getStateDisplayName(targetState)}`;
};
```

### 🔧 **useOperationError.ts 错误状态Hook示例**

```typescript
import { useState, useCallback } from 'react';
import { useErrorRecovery } from '../shared/useErrorRecovery';

export function useOperationError() {
  const [isRetrying, setIsRetrying] = useState(false);
  const { errorContext, clearError, recordError } = useErrorRecovery();

  const retryOperation = useCallback(async (): Promise<boolean> => {
    if (!errorContext?.retryable) return false;
    
    setIsRetrying(true);
    try {
      // 根据errorContext.source决定重试哪个操作
      switch (errorContext.source) {
        case 'upload':
          // 重试文件上传逻辑
          break;
        case 'analyze':
          // 重试分析逻辑
          break;
        case 'importing':
          // 导入过程错误的特殊处理
          // 首先检查是否有部分导入成功的记录
          const importResult = await checkImportStatus();
          if (importResult.hasPartialSuccess) {
            // 如果有部分成功，直接跳转到完成状态查看结果
            return { success: true, targetState: 'completed' };
          } else {
            // 如果完全失败，需要重新开始
            return { success: true, targetState: 'select' };
          }
        case 'completed':
          // 完成状态的错误通常是获取报告失败，可以重试
          await retryGetReport();
          break;
        // ... 其他状态的重试逻辑
      }
      
      clearError();
      return true;
    } catch (error) {
      recordError({
        type: 'operation_error',
        source: errorContext.source,
        message: `重试失败: ${error.message}`,
        recoverable: true,
        retryable: false, // 重试失败后不能再次重试
        suggestions: getFailureRecoveryOptions(errorContext.source),
        canReturnToSource: false
      });
      return false;
    } finally {
      setIsRetrying(false);
    }
  }, [errorContext, clearError, recordError]);

  const getRecoverySuggestions = useCallback(() => {
    const suggestions = [];
    
    if (errorContext?.source === 'upload') {
      suggestions.push('检查文件格式是否正确');
      suggestions.push('确认文件大小不超过限制');
      suggestions.push('尝试使用其他浏览器上传');
    } else if (errorContext?.source === 'analyze') {
      suggestions.push('检查Excel文件内容格式');
      suggestions.push('确认必填字段已填写');
      suggestions.push('检查数据是否存在特殊字符');
    } else if (errorContext?.source === 'importing') {
      suggestions.push('检查是否有部分记录导入成功');
      suggestions.push('查看详细错误日志');
      suggestions.push('联系系统管理员检查服务器状态');
    } else if (errorContext?.source === 'completed') {
      suggestions.push('稍后重试获取报告');
      suggestions.push('检查浏览器是否阻止了下载');
      suggestions.push('清理浏览器缓存后重试');
    }
    
    return suggestions;
  }, [errorContext]);

  const getAvailableRecoveryOptions = useCallback(() => {
    if (!errorContext) return [];
    
    return getRecoveryOptions(errorContext.type, errorContext.source);
  }, [errorContext]);

  return {
    errorContext,
    isRetrying,
    retryOperation,
    getRecoverySuggestions,
    getAvailableRecoveryOptions
  };
}

// 辅助函数
const checkImportStatus = async () => {
  // 检查导入状态的API调用
  // 返回 { hasPartialSuccess: boolean, successCount: number, totalCount: number }
};

const retryGetReport = async () => {
  // 重试获取报告的API调用
};

const getFailureRecoveryOptions = (source: ImportState): string[] => {
  const commonOptions = ['刷新页面重新开始', '联系技术支持'];
  
  switch (source) {
    case 'importing':
      return ['检查导入结果', '下载错误日志', ...commonOptions];
    case 'completed':
      return ['手动下载报告', '查看导入历史', ...commonOptions];
    default:
      return commonOptions;
  }
};
```

## 🔧 **迁移策略**

### 🎯 **无缝切换方案**

1. **并行开发**: 新架构与现有代码并存
2. **特性开关**: 使用环境变量控制新旧架构切换
3. **灰度发布**: 逐步将用户流量切换到新架构
4. **回滚准备**: 保持旧代码一定时间以便紧急回滚

### 📊 **监控指标**

- 页面加载时间
- 用户操作响应时间
- 错误率和崩溃率
- 用户完整流程完成率

## 📚 **文档和培训**

### 📖 **开发文档**

- [ ] 新架构设计文档
- [ ] API接口文档更新
- [ ] 组件使用指南
- [ ] 测试编写指南

### 🎓 **团队培训**

- [ ] 新架构概念讲解
- [ ] 开发工作流培训
- [ ] 代码审查标准更新
- [ ] 故障排查指南

## 🎉 **总结**

这个重构计划将彻底解决当前Excel导入功能的架构问题，通过基于六大状态的组件化设计，实现：

1. **清晰的职责分离**: UI/Hook/Service三层各司其职
2. **可维护的代码结构**: 小文件、单一职责、易于理解
3. **可测试的组件设计**: 每个状态独立测试
4. **可扩展的架构模式**: 便于添加新状态和功能

重构完成后，团队的开发效率将显著提升，代码质量和系统稳定性也将得到保障。
