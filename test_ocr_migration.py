#!/usr/bin/env python3
"""
OCR 微服务迁移测试脚本
测试主应用与 OCR 微服务的集成
"""
import os
import sys
import time
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

# 导入测试模块
from archive_processing.utils import ocr_utils


def create_test_image(text: str = "测试文本 Test Text 123") -> Image.Image:
    """创建测试图像"""
    # 创建白色背景图像
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 绘制文本
    draw.text((10, 30), text, fill='black', font=font)
    
    return img


def test_ocr_client_availability():
    """测试 OCR 客户端可用性"""
    print("🔍 测试 OCR 客户端可用性...")
    
    try:
        # 获取全局客户端
        client = ocr_utils._ocr_client
        
        # 检查服务可用性
        is_available = client.is_available()
        
        if is_available:
            print("✅ OCR 微服务可用")
            return True
        else:
            print("❌ OCR 微服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ OCR 客户端测试异常: {e}")
        return False


def test_init_paddle_ocr():
    """测试 init_paddle_ocr 函数 (微服务模式)"""
    print("\n🔍 测试 init_paddle_ocr 函数...")
    
    try:
        # 测试初始化
        paddle_engine = ocr_utils.init_paddle_ocr(use_paddle=True)
        
        if paddle_engine is not None:
            print("✅ PaddleOCR 初始化成功 (微服务模式)")
            print(f"   引擎类型: {type(paddle_engine).__name__}")
            return True
        else:
            print("❌ PaddleOCR 初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ init_paddle_ocr 测试异常: {e}")
        return False


def test_run_paddle_basic():
    """测试 run_paddle_basic 函数"""
    print("\n🔍 测试 run_paddle_basic 函数...")
    
    try:
        # 初始化引擎
        paddle_engine = ocr_utils.init_paddle_ocr(use_paddle=True)
        if paddle_engine is None:
            print("❌ 无法初始化 PaddleOCR 引擎")
            return False
        
        # 创建测试图像
        test_image = create_test_image("Basic OCR Test 基础识别")
        
        # 执行基础 OCR
        start_time = time.time()
        result_text = ocr_utils.run_paddle_basic(test_image, paddle_engine)
        processing_time = time.time() - start_time
        
        if result_text:
            print("✅ run_paddle_basic 成功")
            print(f"   识别文本: '{result_text}'")
            print(f"   处理时间: {processing_time:.2f}秒")
            return True
        else:
            print("❌ run_paddle_basic 返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ run_paddle_basic 测试异常: {e}")
        return False


def test_run_paddle_enhanced():
    """测试 run_paddle_enhanced 函数"""
    print("\n🔍 测试 run_paddle_enhanced 函数...")
    
    try:
        # 初始化引擎
        paddle_engine = ocr_utils.init_paddle_ocr(use_paddle=True)
        if paddle_engine is None:
            print("❌ 无法初始化 PaddleOCR 引擎")
            return False
        
        # 创建测试图像列表
        test_images = [
            create_test_image("Enhanced OCR Test 增强识别"),
            create_test_image("Second Image 第二张图"),
            create_test_image("Third Image 第三张图")
        ]
        
        # 执行增强 OCR
        start_time = time.time()
        result_texts = ocr_utils.run_paddle_enhanced(test_images, paddle_engine)
        processing_time = time.time() - start_time
        
        if result_texts and len(result_texts) > 0:
            print("✅ run_paddle_enhanced 成功")
            print(f"   结果数量: {len(result_texts)}")
            for i, text in enumerate(result_texts):
                print(f"   结果{i+1}: '{text}'")
            print(f"   处理时间: {processing_time:.2f}秒")
            return True
        else:
            print("❌ run_paddle_enhanced 返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ run_paddle_enhanced 测试异常: {e}")
        return False


def test_perform_basic_ocr():
    """测试 perform_basic_ocr 函数"""
    print("\n🔍 测试 perform_basic_ocr 函数...")
    
    try:
        # 初始化引擎
        paddle_engine = ocr_utils.init_paddle_ocr(use_paddle=True)
        
        # 创建测试图像
        test_image = create_test_image("Perform Basic OCR 执行基础识别")
        
        # 创建缓存
        ocr_cache = {}
        
        # 执行基础 OCR
        start_time = time.time()
        result_text = ocr_utils.perform_basic_ocr(
            image=test_image,
            engine="auto",
            ocr_cache=ocr_cache,
            enable_cache=True,
            paddle_engine=paddle_engine,
            use_paddle=True,
            page_num=0
        )
        processing_time = time.time() - start_time
        
        if result_text:
            print("✅ perform_basic_ocr 成功")
            print(f"   识别文本: '{result_text}'")
            print(f"   处理时间: {processing_time:.2f}秒")
            print(f"   缓存条目: {len(ocr_cache)}")
            return True
        else:
            print("❌ perform_basic_ocr 返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ perform_basic_ocr 测试异常: {e}")
        return False


def test_fallback_to_tesseract():
    """测试回退到 Tesseract 的情况"""
    print("\n🔍 测试回退到 Tesseract...")
    
    try:
        # 创建测试图像
        test_image = create_test_image("Tesseract Fallback Test")
        
        # 创建缓存
        ocr_cache = {}
        
        # 强制使用 Tesseract
        start_time = time.time()
        result_text = ocr_utils.perform_basic_ocr(
            image=test_image,
            engine="tesseract",
            ocr_cache=ocr_cache,
            enable_cache=True,
            paddle_engine=None,
            use_paddle=False,
            page_num=0
        )
        processing_time = time.time() - start_time
        
        if result_text:
            print("✅ Tesseract 回退成功")
            print(f"   识别文本: '{result_text}'")
            print(f"   处理时间: {processing_time:.2f}秒")
            return True
        else:
            print("⚠️ Tesseract 回退返回空结果 (可能是正常的)")
            return True  # Tesseract 可能无法识别测试图像，这是正常的
            
    except Exception as e:
        print(f"❌ Tesseract 回退测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始 OCR 微服务迁移集成测试")
    print("=" * 60)
    
    tests = [
        ("OCR 客户端可用性", test_ocr_client_availability),
        ("init_paddle_ocr 函数", test_init_paddle_ocr),
        ("run_paddle_basic 函数", test_run_paddle_basic),
        ("run_paddle_enhanced 函数", test_run_paddle_enhanced),
        ("perform_basic_ocr 函数", test_perform_basic_ocr),
        ("Tesseract 回退机制", test_fallback_to_tesseract),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 间隔1秒
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！OCR 微服务迁移成功")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查配置和服务状态")
        return 1


if __name__ == "__main__":
    sys.exit(main())
