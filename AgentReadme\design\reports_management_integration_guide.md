# 发放单管理页面前后端对接实施指南

## 1. 引言

本文档旨在指导开发人员完成前端"发放单台账"页面 (`frontend/app/reports/management/page.tsx`) 与后端 Django 应用 `report_issuing` 的数据对接。目标是将前端目前使用的模拟数据替换为通过API从后端获取的真实数据，并实现相关操作的后端交互。

**主要数据交互模式已调整为混合模式**：日期范围筛选由后端处理，返回结果集后，前端 AgGrid 将在此数据集上执行客户端的快速搜索、状态筛选、排序和分页。

## 2. 后端实现步骤

### 2.1. 更新 `IssueForm` 模型权限

**注意：此步骤被用户跳过。** 模型 `IssueForm` 的 `Meta.permissions` 未被修改以添加细粒度的操作权限 (如 `upload_receipt_issue_forms`, `view_receipt_issue_forms`)。因此，后端的权限控制将主要依赖于用户是否已认证 (`permissions.IsAuthenticated`)，而不是具体的模型权限。前端通过 `hasPermission` 进行的细粒度权限检查可能不会按预期工作，除非这些权限字符串有其他定义来源。

在 `report_issuing/models.py` 文件中，找到 `IssueForm` 模型，在其内部的 `Meta` 类中添加或更新 `permissions` 属性，以支持前端操作所需的权限检查。

```python
# report_issuing/models.py

class IssueForm(models.Model):
    # ... (现有字段) ...

    class Meta:
        verbose_name = "发放单"
        verbose_name_plural = "发放单"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['number']),
            models.Index(fields=['status']),
            models.Index(fields=['issue_date']),
            models.Index(fields=['is_deleted']),
        ]
        permissions = [
            ("upload_receipt_issue_forms", "可以上传发放单的确认单"), # Can upload receipt for issue forms
            ("view_receipt_issue_forms", "可以查看发放单的确认单"),   # Can view receipt for issue forms
            # 根据需要添加更多权限，例如:
            # ("change_issueform_notes", "可以修改发放单的备注"),
            # ("print_confirmation_issue_forms", "可以打印发放单的确认单"),
        ]

    # ... (现有方法) ...
```

**注意**: 确保权限描述清晰，并使用中文。

### 2.2. 创建 Serializer (`report_issuing/serializers.py`)

如果该文件不存在，请创建它。定义 `IssueFormListSerializer` 用于序列化 `IssueForm` 数据以供API返回。同时，`IssueFormNotesUpdateSerializer` 用于处理备注更新，`IssueFormReceiptUploadSerializer` 用于处理确认单文件上传。这些 Serializer 的核心结构基本保持不变，因为前端所需的数据字段和操作接口没有根本性改变。

```python
# report_issuing/serializers.py

from rest_framework import serializers
from django.db.models import Count, Sum
from .models import IssueForm
from django.contrib.auth import get_user_model

User = get_user_model()

class IssueFormListSerializer(serializers.ModelSerializer):
    id = serializers.CharField(source='number', read_only=True)
    createdAt = serializers.DateTimeField(source='created_at', read_only=True, format='%Y-%m-%dT%H:%M:%SZ')
    issuedAt = serializers.DateTimeField(source='issue_date', read_only=True, format='%Y-%m-%dT%H:%M:%SZ')
    departmentOrReceiverUnit = serializers.CharField(source='receiver_unit') # 确保前端字段名匹配
    
    issuerName = serializers.SerializerMethodField()
    hasReceipt = serializers.SerializerMethodField()
    itemCount = serializers.SerializerMethodField()
    issuedCopiesCount = serializers.SerializerMethodField()
    
    # 确保前端期望的 receiverName 和 receiverPhone 也被序列化
    # receiverName 在模型中是 receiver_name
    # receiverPhone 在模型中是 receiver_phone

    class Meta:
        model = IssueForm
        fields = [
            'id', 
            'createdAt', 
            'issuedAt', 
            'departmentOrReceiverUnit', 
            'status', 
            'notes',
            'receiver_name', # 直接映射模型字段
            'receiver_phone',# 直接映射模型字段
            'issuerName', 
            'hasReceipt', 
            'itemCount', 
            'issuedCopiesCount',
            # 如果前端也直接使用模型的 issuer, confirmation_file 等字段，也可以包含它们
            # 但通常派生字段更符合前端接口定义
        ]
        read_only_fields = ['id', 'createdAt', 'issuedAt', 'status', 'issuerName', 'hasReceipt', 'itemCount', 'issuedCopiesCount']

    def get_issuerName(self, obj):
        if obj.issuer:
            # 优先使用全名，否则使用用户名
            return obj.issuer.get_full_name() or obj.issuer.username
        return None

    def get_hasReceipt(self, obj):
        return bool(obj.confirmation_file)

    def get_itemCount(self, obj):
        # 这个方法会在每个对象序列化时执行一次查询。
        # 为了性能，如果已经在queryset中annotate了item_count，可以直接访问 obj.item_count
        if hasattr(obj, 'item_count_annotated'):
            return obj.item_count_annotated
        return obj.items.count() # type: ignore

    def get_issuedCopiesCount(self, obj):
        # 类似于 itemCount，如果annotate了，直接访问
        if hasattr(obj, 'issued_copies_count_annotated'):
            return obj.issued_copies_count_annotated
        # 使用 .items.aggregate(total_copies=Sum('copies'))['total_copies'] or 0
        # 需要确保 'items' 是 IssueFormItem 的 related_name
        aggregation = obj.items.aggregate(total_copies=Sum('copies')) # type: ignore
        return aggregation['total_copies'] or 0

# Serializer 用于更新备注
class IssueFormNotesUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IssueForm
        fields = ['notes']

# Serializer 用于上传确认单文件
class IssueFormReceiptUploadSerializer(serializers.ModelSerializer):
    class Meta:
        model = IssueForm
        fields = ['confirmation_file']

```

### 2.3. ~~创建 FilterSet (`report_issuing/filters.py`)~~ (已跳过)

**此步骤已根据用户要求跳过。** 原计划使用 `django-filter` 创建一个专门的 `IssueFormFilter` 类来提供细粒度的后端过滤功能。但为了优先实现客户端模式下的快速搜索和过滤，此 `FilterSet` 未被创建。

作为替代，特定的过滤逻辑（目前主要是日期范围过滤）将在 `IssueFormViewSet` 的 `get_queryset` 方法中直接通过解析URL查询参数来实现。其他过滤（如状态过滤、文本搜索）将主要由前端 AgGrid 在客户端处理。

```python
# report_issuing/filters.py (文件未创建或内容被移除)

# import django_filters
# from django.contrib.auth import get_user_model
# from .models import IssueForm

# User = get_user_model()

# class IssueFormFilter(django_filters.FilterSet):
#     # ... (原计划的过滤器定义) ...
#     pass
```

### 2.4. 创建 ViewSet (`report_issuing/views.py`)

ViewSet `IssueFormViewSet` 将被修改以支持混合数据模式。它将不再处理服务端的的分页、排序或全文搜索；这些功能将由客户端的 AgGrid 处理。然而，ViewSet 仍将处理日期范围的筛选。

```python
# report_issuing/views.py
from datetime import datetime # 确保导入datetime
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Sum, Q
# 移除: from django_filters.rest_framework import DjangoFilterBackend
# 移除: from rest_framework.filters import SearchFilter, OrderingFilter
# 移除: from .filters import IssueFormFilter 

from .models import IssueForm
from .serializers import (
    IssueFormListSerializer, 
    IssueFormNotesUpdateSerializer,
    IssueFormReceiptUploadSerializer
)

class IssueFormViewSet(viewsets.ModelViewSet):
    serializer_class = IssueFormListSerializer
    # filter_backends, filterset_class, search_fields, ordering_fields, ordering 已移除/清空
    filter_backends = [] 
    search_fields = [] 
    ordering_fields = []
    ordering = [] 

    def get_queryset(self):
        queryset = IssueForm.objects.filter(is_deleted=False) 

        # 如果serializer使用它们且性能可接受，可以保留注解
        queryset = queryset.select_related('issuer').prefetch_related('items').annotate(
            item_count_annotated=Count('items'),
            issued_copies_count_annotated=Sum('items__copies')
        )
        
        # 从查询参数中获取日期范围过滤条件
        created_at_after = self.request.query_params.get('created_at_after', None)
        created_at_before = self.request.query_params.get('created_at_before', None)

        if created_at_after:
            queryset = queryset.filter(created_at__gte=created_at_after)
        if created_at_before:
            try: # 为包含结束日期，过滤到该天的结束时间
                end_date = datetime.strptime(created_at_before, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__lte=datetime.combine(end_date, datetime.max.time()))
            except ValueError: # 如果是完整的datetime字符串，直接使用
                queryset = queryset.filter(created_at__lte=created_at_before)
        
        # 状态过滤现在由客户端处理
        # 之前的status_filter逻辑已从此处移除
        
        return queryset

    def get_permissions(self):
        # 根据用户要求简化
        return [permissions.IsAuthenticated()]

    # update_notes, upload_receipt, retrieve_confirmation_file的@action定义保持不变
    # ... (现有@action代码保留在这里) ...
    @action(detail=True, methods=['patch'], serializer_class=IssueFormNotesUpdateSerializer, permission_classes=[permissions.IsAuthenticated])
    def update_notes(self, request, pk=None):
        issue_form = self.get_object()
        serializer = IssueFormNotesUpdateSerializer(issue_form, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            full_serializer = IssueFormListSerializer(issue_form, context={'request': request})
            return Response(full_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'], serializer_class=IssueFormReceiptUploadSerializer, permission_classes=[permissions.IsAuthenticated])
    def upload_receipt(self, request, pk=None):
        issue_form = self.get_object()
        serializer = IssueFormReceiptUploadSerializer(issue_form, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            if issue_form.status == 'confirmed':
                issue_form.status = 'archived'
                issue_form.save(update_fields=['status'])
            full_serializer = IssueFormListSerializer(issue_form, context={'request': request})
            return Response(full_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def retrieve_confirmation_file(self, request, pk=None):
        issue_form = self.get_object()
        if not issue_form.confirmation_file:
            return Response({'detail': '确认单文件不存在。'}, status=status.HTTP_404_NOT_FOUND)
        from django.http import FileResponse
        try:
            return FileResponse(issue_form.confirmation_file.open('rb'), as_attachment=False, filename=issue_form.confirmation_file.name)
        except FileNotFoundError:
            return Response({'detail': '确认单文件未找到。'}, status=status.HTTP_404_NOT_FOUND)
```

### 2.5. 配置 URL

`report_issuing.urls` 的 URL 配置及其在项目根 URL 中的包含方式保持不变，因为 `IssueFormViewSet` 仍然是发放单 API 端点的核心。

```python
# report_issuing/urls.py

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import IssueFormViewSet

router = DefaultRouter()
router.register(r'issue-forms', IssueFormViewSet, basename='issueform')

urlpatterns = [
    path('', include(router.urls)),
]
```

```python
# project_root/urls.py

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    # ... 其他应用的 URL ...
    path('api/report-issuing/', include('report_issuing.urls')), 
    # 或者你希望的任何API前缀
]
```

## 3. 前端实现步骤

### 3.1. 创建/更新 API 服务函数

API 服务 (`reportIssuingService.ts`) 将被修改，以反映后端不再处理主列表端点的分页、排序或全文搜索。参数将被简化为仅包含日期范围筛选器。

```typescript
// frontend/services/reportIssuingService.ts
import apiClientInstance from '@/services/api-client'; // 确保路径正确

export interface IssueFormData { // IssueFormData的定义保持不变
  id: string; 
  createdAt: string; 
  issuedAt: string; 
  departmentOrReceiverUnit: string; 
  itemCount: number; 
  issuedCopiesCount: number; 
  status: 'draft' | 'locked' | 'confirmed' | 'archived' | 'deleted';
  hasReceipt: boolean; 
  issuerName?: string; 
  notes?: string; 
  receiverName?: string; 
  receiverPhone?: string; 
}

// ListParams被简化：只保留日期过滤相关参数
export interface ListParams { 
  created_at_after?: string;
  created_at_before?: string;
  // status_filter可选，如果仍想在某些特殊情况下在服务器端过滤状态，
  // 如果不需要，可以删除它，完全由客户端处理状态过滤。
  status_filter?: 'active' | 'archived' | 'all'; 
}

// getIssueForms现在直接返回数组，而非分页对象
export const getIssueForms = async (params?: ListParams): Promise<IssueFormData[]> => {
  // @ts-ignore 假设apiClientInstance具有get方法
  const response = await apiClientInstance.get<IssueFormData[]>('/report-issuing/issue-forms/', { params });
  return response.data; // 期望后端返回IssueFormData[]
};

// updateIssueFormNotes, uploadIssueFormReceipt, getReceiptFileUrl在功能上保持不变
export const updateIssueFormNotes = async (id: string, notes: string): Promise<IssueFormData> => {
  // @ts-ignore 
  const response = await apiClientInstance.patch<IssueFormData>(`/report-issuing/issue-forms/${id}/update_notes/`, { notes });
  return response.data;
};

export const uploadIssueFormReceipt = async (id: string, file: File): Promise<IssueFormData> => {
  const formData = new FormData();
  formData.append('confirmation_file', file);
  // @ts-ignore 
  const response = await apiClientInstance.patch<IssueFormData>(`/report-issuing/issue-forms/${id}/upload_receipt/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const getReceiptFileUrl = (issueFormId: string): string => {
  // @ts-ignore 
  const baseUrl = apiClientInstance.defaults.baseURL || ''; 
  return `${baseUrl}/report-issuing/issue-forms/${issueFormId}/retrieve_confirmation_file/`;
};
```

**注意**: `apiClientInstance` 的导入路径已更新为 `'@/services/api-client';`。 `@ts-ignore` 的使用是暂时的，表明 `apiClientInstance` 的确切结构（无论是直接的 axios 实例还是包装器）及其可用方法（如 `get`, `patch`, `defaults.baseURL`）需要在实际项目上下文中进行验证。

### 3.2. 修改 `frontend/app/reports/management/page.tsx`

此页面组件将进行重大更改，以反映混合数据模式：

* **数据加载 (`loadIssueForms`)**: 此函数现在将仅根据应用的日期筛选器获取数据。用于分页、排序或全文搜索的参数将不会发送到后端。
* **状态管理**: 诸如 `totalRows` 和 `currentPage` (用于API分页) 之类的状态将不再需要或将被调整。`pageSize` 将控制AgGrid客户端分页。`sortModel` 将由AgGrid用于客户端排序。
* **客户端筛选**: 状态筛选将在客户端实现 (例如，在获取的 `rowData` 上使用 `useMemo`)。
* **AgGridReact 配置**:
  * `rowModelType` 将显式设置为 `"clientSide"`。
  * `quickFilterText` 属性将用于启用AgGrid的客户端快速搜索功能。
  * `paginationTotalRows` 不再需要，因为AgGrid将为客户端分页计算行数。
  * 诸如 `onSortChanged` 和 `onPaginationChanged` 之类的回调将不再触发API调用以获取新数据，而是处理内部的AgGrid客户端操作。
* **数据更新**: 诸如用于备注的 `valueSetter` 和 `handleUploadReceipt` 之类的函数现在将乐观地修改客户端 `rowData` 状态，然后将更新发送到服务器。然后，客户端数据集将使用服务器确认的数据进行更新。
* **Linter错误处理**: 需要注意，可能存在与AgGrid相关的linter错误，这需要用户根据项目的特定AgGrid版本和配置进行特别处理。

```tsx
// frontend/app/reports/management/page.tsx

// ... (导入和接口定义，如从reportIssuingService导入的IssueFormData, ListParams)

export default function ReportsManagementPage() {
  // ... (useAuth钩子，rowData、isLoading、error状态)
  const [rowData, setRowData] = useState<ReportIssuingIssueFormData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const [pageSize, setPageSize] = useState<number>(agGridConfig.clientSideDefaults.paginationPageSize ?? 20);
  const gridApiRef = useRef<GridApi<ReportIssuingIssueFormData> | null>(null);
  const [quickFilterInputText, setQuickFilterInputText] = useState("");
  const [currentReportStatusFilter, setCurrentReportStatusFilter] = useState<'active' | 'archived' | 'all'>("active");
  const [appliedStartDate, setAppliedStartDate] = useState<string>("");
  const [appliedEndDate, setAppliedEndDate] = useState<string>("");

  const loadIssueForms = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const apiParams: ListParams = {
        created_at_after: appliedStartDate || undefined,
        created_at_before: appliedEndDate || undefined,
        // status_filter: currentReportStatusFilter, // 已移除，因为状态过滤现在由客户端处理
      };
      // 从apiParams中移除undefined属性
      Object.keys(apiParams).forEach(key => apiParams[key as keyof ListParams] === undefined && delete apiParams[key as keyof ListParams]);

      const responseData = await getIssueForms(apiParams);
      setRowData(responseData); // 设置完整数据集供AgGrid在客户端处理
    } catch (err) {
      setError("数据加载失败，请稍后重试。");
      console.error("Failed to load issue forms:", err);
      setRowData([]); // 如有错误，清空数据
    } finally {
      setIsLoading(false);
    }
  }, [appliedStartDate, appliedEndDate]);

  useEffect(() => {
    loadIssueForms(); // 当日期过滤器变化（或初始化）时加载数据
  }, [loadIssueForms]);

  // 状态的客户端过滤逻辑
  const filteredRowData = useMemo(() => {
    if (currentReportStatusFilter === 'all') {
      return rowData;
    }
    return rowData.filter(form => {
      if (currentReportStatusFilter === 'active') {
        return ['draft', 'locked', 'confirmed'].includes(form.status);
      }
      if (currentReportStatusFilter === 'archived') {
        return form.status === 'archived';
      }
      return true;
    });
  }, [rowData, currentReportStatusFilter]);

  // handleUploadReceipt和'notes'的valueSetter将修改`rowData`
  const handleUploadReceipt = useCallback(async (data: ReportIssuingIssueFormData, file: File) => {
    // ... (实现保持相同，但更新rowData)
    try {
      const updatedForm = await uploadIssueFormReceipt(data.id, file);
      setRowData(prevData => 
        prevData.map(row => 
          row.id === updatedForm.id ? updatedForm : row
        )
      );
      alert(`确认单已为 ${data.id} 上传成功。`);
    } catch (error: any) {
      console.error("Failed to upload receipt:", error);
      alert(`确认单上传失败: ${error.message || '未知错误'}`);
    }
  }, []);
  
  const columnDefs = useMemo<ColDef<ReportIssuingIssueFormData>[]>(() => [
    // ... (大部分列定义保持不变)
    {
      field: 'notes',
      // ... (其他属性如headerName, editable, cellEditor)
      valueSetter: (params: ValueSetterParams<ReportIssuingIssueFormData, string | null>) => {
        const originalNotes = params.data?.notes;
        let newNotesValue = params.newValue;
        const notesForUI = newNotesValue === null ? (originalNotes === undefined ? undefined : '') : newNotesValue;
        const notesForServer = (newNotesValue === null || newNotesValue === undefined) ? '' : newNotesValue;

        if (notesForUI === originalNotes) return false;
        if (!params.data || !params.data.id || !params.node) return false;

        const currentData = params.data;
        const reportId = String(currentData.id);

        currentData.notes = notesForUI; // 乐观UI更新
        params.api.refreshCells({ rowNodes: [params.node], columns: [params.colDef.colId!], force: true });

        updateIssueFormNotes(reportId, notesForServer)
          .then(updatedFormServer => {
            setRowData(prevData => prevData.map(row => (row.id === updatedFormServer.id ? updatedFormServer : row)));
          })
          .catch(error => {
            console.error("Failed to update notes:", error);
            alert("备注更新失败！正在恢复原值。");
            currentData.notes = originalNotes;
            if (params.node) {
                params.api.refreshCells({ rowNodes: [params.node], columns: [params.colDef.colId!], force: true });
            }
          });
        return true;
      },
      // ...
    },
    // ... (其他列)
  ], [hasPermission, handleUploadReceipt]);

  // onSortChanged和onPaginationChanged现在用于AgGrid客户端操作
  const onSortChanged = useCallback((event: SortChangedEvent<ReportIssuingIssueFormData>) => {
    // 无需从API重新加载数据
  }, []);

  const onPaginationChanged = useCallback(() => {
    // 无需从API重新加载数据
  }, []);
  
  // ... (ActionsCellRenderer定义等仍然存在，但onUploadReceiptClick现在调用修改后的版本)

  return (
    // ... (TablePageLayout的JSX)
      <AgGridReact<ReportIssuingIssueFormData>
        rowData={filteredRowData} // 使用已在客户端按状态过滤的数据
        columnDefs={columnDefs}
        defaultColDef={defaultColDef} // 确保这也是稳定的（例如，从useMemo获得）
        rowModelType="clientSide" // 明确指定客户端模式
        pagination={true}
        paginationPageSize={pageSize} // 用于AgGrid客户端分页
        // clientSide模式不再需要paginationTotalRows
        quickFilterText={quickFilterInputText} // 启用AgGrid客户端快速搜索
        onGridReady={onGridReady}
        onFirstDataRendered={onFirstDataRendered}
        onSortChanged={onSortChanged} // AgGrid处理客户端排序
        onPaginationChanged={onPaginationChanged} // AgGrid处理客户端分页
        // ... (其他相关属性，如theme, performanceConfig)
      />
    // ... (其他JSX)
  );
}
```

#### ~~a. 替换模拟数据~~ (已包含在上述说明中)

#### ~~b. 更新 `columnDefs` 中 `notes` 的 `valueSetter`~~ (已包含在上述说明中)

#### ~~c. 更新 `handleOpenUploadReceiptModal` (现在是 `handleUploadReceipt`)~~ (已包含在上述说明中)

#### ~~d. 更新 `ActionsCellRenderer` 中 `view_receipt` 的链接~~ (此逻辑没有重大更改，只需确保它与客户端数据一起使用)

#### ~~e. AgGrid 服务器端分页/排序/过滤~~ (已替换为上述客户端模式)

## 4. 字段映射表

由于 `IssueFormListSerializer` 的核心字段映射没有发生重大更改，下面的字段映射表在很大程度上仍然适用。主要的变化是，这些数据现在由前端以不同的方式加载和处理（在可能从后端按日期筛选的初始数据获取之后，进行客户端筛选、分页等）。

| 前端 `IssueFormData` 字段    | 后端 `IssueForm` 字段/派生方式                                  | 后端Serializer源/方法       |
|----------------------------|-----------------------------------------------------------------|----------------------------|
| `id`                       | `number`                                                        | `source='number'`          |
| `createdAt`                | `created_at`                                                    | `source='created_at'`      |
| `issuedAt`                 | `issue_date`                                                    | `source='issue_date'`      |
| `departmentOrReceiverUnit` | `receiver_unit`                                                 | `source='receiver_unit'`   |
| `itemCount`                | `Count('items')` 或 `obj.items.count()`                          | `get_itemCount` / Annotated |
| `issuedCopiesCount`        | `Sum('items__copies')` 或 `

## 5. 后续步骤与测试建议

1. **后端验证**：
    * 确认 `IssueFormViewSet` 的 `get_queryset` 方法在接收到 `created_at_after` 和 `created_at_before` 参数时能正确过滤数据，并在没有这些参数时返回所有未删除的记录。
    * 确认API不再响应分页、排序或全局搜索参数。
    * 自定义操作 (`update_notes`, `upload_receipt`, `retrieve_confirmation_file`) 的功能和权限 (`IsAuthenticated`) 保持不变并正常工作。

2. **前端 API 服务验证 (`reportIssuingService.ts`)**：
    * 确认 `getIssueForms` 函数的 `ListParams` 只包含日期过滤相关参数，并且正确调用API。
    * 确认该函数能正确处理从后端返回的 `IssueFormData[]` 数组。
    * **关键**: 验证 `apiClientInstance` (从 `'@/services/api-client'` 导入) 是否能按预期工作，并提供所需的 `.get()`, `.patch()` 方法以及 `.defaults.baseURL` 属性。

3. **前端页面组件验证 (`ReportsManagementPage.tsx`)**：
    * **数据加载**：
        * 初始加载（无日期筛选）时，应从后端获取所有（或默认范围的）数据，并由AgGrid进行客户端分页和排序。
        * 应用日期范围筛选后，`loadIssueForms` 应仅使用日期参数调用API，并用返回的数据子集更新AgGrid。
    * **客户端过滤与搜索**：
        * 状态过滤器 (`currentReportStatusFilter`) 应在客户端正确过滤 `rowData`。
        * AgGrid的快速搜索 (`quickFilterText`) 应在当前显示的 `filteredRowData` (可能已按日期和状态筛选) 上正确执行客户端搜索。
    * **客户端分页与排序**：
        * AgGrid的分页控件应在 `filteredRowData` 上正确工作。
        * 点击列头排序应在 `filteredRowData` 上正确执行客户端排序。
    * **数据操作**：
        * 备注编辑 (`valueSetter`) 和确认单上传 (`handleUploadReceipt`) 应能正确更新客户端的 `rowData` 状态，并将更改成功保存到后端。UI应反映这些更改。
        * 查看确认单链接应正常工作。
    * **Linter错误**: 强烈建议解决 `page.tsx` 中与AgGrid相关的剩余Linter错误，这可能需要根据项目具体的AgGrid版本和配置进行调整。
    * **用户体验**: 确保加载状态、错误提示、操作反馈（如保存成功/失败）清晰友好。

4. **整体测试场景**：
    * 测试无日期筛选时的完整客户端功能（搜索、排序、分页、状态筛选）。
    * 测试应用日期筛选后，返回的数据是否正确，以及在该数据子集上的客户端功能是否依然正常。
    * 测试各种操作（编辑备注、上传文件）在不同数据视图下的表现。

此文档已更新以反映新的混合数据处理模式。请根据项目具体情况进行最终调整和实现。
