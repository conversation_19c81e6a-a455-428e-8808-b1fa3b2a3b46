"use client"

import { useState, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, FileText, Package } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getDay,
  isSameMonth,
  isSameDay,
  getMonth,
  parseISO,
} from "date-fns"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

// 扩展归档数据结构，包含归档数和档案盒数
type ArchiveData = {
  count: number
  boxCount: number
}

// 生成模拟数据
function generateMockData() {
  const dailyData: Record<string, ArchiveData> = {}
  const monthlyData: Record<string, ArchiveData> = {}

  // 初始种子数据 - 确保有一些固定的数据点以便于验证
  const seedData = [
    { date: "2023-04-15", count: 42, boxCount: 3 },
    { date: "2023-04-16", count: 27, boxCount: 2 },
    { date: "2023-04-17", count: 35, boxCount: 2 },
    { date: "2023-04-18", count: 18, boxCount: 1 },
    { date: "2023-04-19", count: 23, boxCount: 2 },
    { date: "2023-04-20", count: 53, boxCount: 3 },
    { date: "2023-04-21", count: 47, boxCount: 3 },
    { date: "2023-04-22", count: 18, boxCount: 1 },
    { date: "2023-04-23", count: 0, boxCount: 0 }, // 周日无数据
    { date: "2023-04-24", count: 29, boxCount: 2 },
    { date: "2023-04-25", count: 64, boxCount: 4 },
    { date: "2023-04-26", count: 38, boxCount: 2 },
    { date: "2023-04-27", count: 31, boxCount: 2 },
    { date: "2023-04-28", count: 25, boxCount: 2 },
  ]

  // 添加种子数据
  for (const item of seedData) {
    dailyData[item.date] = { count: item.count, boxCount: item.boxCount }
  }

  // 生成2023年4月的月度汇总数据
  monthlyData["2023-04"] = {
    count: seedData.reduce((sum, item) => sum + item.count, 0),
    boxCount: Math.max(...seedData.map((item) => item.boxCount)),
  }

  // 生成2023年剩余月份的数据
  for (let month = 0; month < 12; month++) {
    // 跳过4月，因为我们已经有了种子数据
    if (month === 3) continue

    let monthlyCount = 0
    let monthlyBoxCount = 0

    // 每个月中大部分天都有数据，但周日可能没有
    const daysInMonth = new Date(2023, month + 1, 0).getDate()

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(2023, month, day)
      // 如果是周日，80%的概率没有数据
      const isSunday = date.getDay() === 0
      const hasData = !isSunday || Math.random() > 0.8

      if (hasData) {
        // 生成随机归档数 (5-100)
        const count = Math.floor(Math.random() * 95) + 5
        // 生成随机档案盒数 (1-5)
        const boxCount = Math.floor(Math.random() * 5) + 1

        const dateStr = `2023-${String(month + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`
        dailyData[dateStr] = { count, boxCount }

        monthlyCount += count
        monthlyBoxCount += boxCount
      }
    }

    const monthStr = `2023-${String(month + 1).padStart(2, "0")}`
    monthlyData[monthStr] = {
      count: monthlyCount,
      boxCount: monthlyBoxCount,
    }
  }

  return { dailyData, monthlyData }
}

// 生成模拟数据
const { dailyData: dailyArchiveData, monthlyData: monthlyArchiveData } = generateMockData()

// 安全地格式化日期为字符串
function formatDateToString(date: Date | string | number | null | undefined, formatStr = "yyyy-MM-dd"): string {
  if (!date) return ""

  try {
    // 如果是Date对象
    if (date instanceof Date) {
      return format(date, formatStr)
    }

    // 如果是字符串或数字，尝试转换为Date对象
    const dateObj = new Date(date)
    if (!isNaN(dateObj.getTime())) {
      return format(dateObj, formatStr)
    }

    return ""
  } catch (error) {
    console.error("Date formatting error:", error)
    return ""
  }
}

// 星期几的标签
const weekDays = ["日", "一", "二", "三", "四", "五", "六"]

// 月份名称
const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

// 为了演示效果，将2023-04-20设为"今天"
const TODAY_FOR_DEMO = parseISO("2023-04-20")

export function ArchiveCalendar() {
  // 将初始日期设置为2023年4月，确保有数据显示
  const [currentDate, setCurrentDate] = useState<Date>(new Date(2023, 3, 1))
  const [view, setView] = useState<"month" | "year">("month")
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  const yearSelectRef = useRef<HTMLButtonElement>(null)
  const monthSelectRef = useRef<HTMLButtonElement>(null)

  // 获取当前月的所有日期
  const daysInMonth = eachDayOfInterval({
    start: startOfMonth(currentDate),
    end: endOfMonth(currentDate),
  })

  // 获取月份第一天是星期几 (0-6)
  const firstDayOfMonth = getDay(startOfMonth(currentDate))

  // 获取当前年的所有月份
  const monthsInYear = Array.from({ length: 12 }, (_, i) => new Date(currentDate.getFullYear(), i, 1))

  // 年份列表 (2020-2030)
  const years = Array.from({ length: 11 }, (_, i) => 2020 + i)

  // 图标颜色 - 使用更柔和的色调
  const fileIconColor = "text-blue-400" // 柔和的蓝色
  const boxIconColor = "text-teal-400" // 柔和的青绿色

  return (
    <div className="space-y-4">
      <Tabs value={view} onValueChange={(v) => setView(v as any)}>
        <TabsList>
          <TabsTrigger value="month">月视图</TabsTrigger>
          <TabsTrigger value="year">年视图</TabsTrigger>
        </TabsList>
      </Tabs>

      {view === "month" ? (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(subMonths(currentDate, 1))}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              上个月
            </Button>

            <div className="flex items-center gap-2">
              {/* 年份选择器 */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button ref={yearSelectRef} variant="ghost" className="font-medium hover:bg-gray-100">
                    {currentDate.getFullYear()}年
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-0" align="center">
                  <div className="grid grid-cols-3 gap-1 p-2">
                    {years.map((year) => (
                      <Button
                        key={year}
                        variant="ghost"
                        size="sm"
                        className={cn("h-9 rounded-md", year === currentDate.getFullYear() ? "bg-blue-100" : "")}
                        onClick={() => {
                          setCurrentDate(new Date(year, getMonth(currentDate), 1))
                          yearSelectRef.current?.click()
                        }}
                      >
                        {year}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* 月份选择器 */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button ref={monthSelectRef} variant="ghost" className="font-medium hover:bg-gray-100">
                    {monthNames[currentDate.getMonth()]}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-0" align="center">
                  <div className="grid grid-cols-3 gap-1 p-2">
                    {monthNames.map((month, index) => (
                      <Button
                        key={month}
                        variant="ghost"
                        size="sm"
                        className={cn("h-9 rounded-md", index === currentDate.getMonth() ? "bg-blue-100" : "")}
                        onClick={() => {
                          setCurrentDate(new Date(currentDate.getFullYear(), index, 1))
                          monthSelectRef.current?.click()
                        }}
                      >
                        {month}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(addMonths(currentDate, 1))}
              className="flex items-center gap-1"
            >
              下个月
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-7 gap-2">
            {/* 星期标题 */}
            {weekDays.map((day, index) => (
              <div key={index} className="text-center font-medium py-2 text-sm">
                {day}
              </div>
            ))}

            {/* 填充月初空白 */}
            {Array.from({ length: firstDayOfMonth }).map((_, index) => (
              <div key={`empty-${index}`} className="h-24 bg-gray-50 rounded-md"></div>
            ))}

            {/* 日期格子 */}
            {daysInMonth.map((day) => {
              const dateStr = formatDateToString(day)
              const data = dailyArchiveData[dateStr] || { count: 0, boxCount: 0 }
              // 使用模拟的今天日期进行演示
              const isToday = isSameDay(day, TODAY_FOR_DEMO)
              const isSelected = selectedDate && isSameDay(day, selectedDate)
              const isCurrentMonth = isSameMonth(day, currentDate)
              const isWeekend = day.getDay() === 0 || day.getDay() === 6

              return (
                <Card
                  key={day.toString()}
                  className={cn(
                    "h-24 cursor-pointer hover:shadow-md transition-shadow overflow-hidden border",
                    isSelected ? "ring-2 ring-blue-500" : "",
                    !isCurrentMonth ? "opacity-50" : "",
                    isWeekend ? "bg-gray-50" : "bg-white",
                  )}
                  onClick={() => setSelectedDate(day)}
                >
                  <CardContent className="p-2 h-full flex flex-col">
                    <div className="flex justify-between items-center mb-2">
                      <span className={cn("text-sm font-medium", isWeekend ? "text-gray-500" : "")}>
                        {format(day, "d")}
                      </span>
                      {isToday && <span className="h-2 w-2 rounded-full bg-blue-500"></span>}
                    </div>

                    {data.count > 0 ? (
                      <div className="flex-1 flex flex-col justify-center">
                        <div className="grid grid-cols-2 gap-x-1 gap-y-2">
                          <div className="flex items-center">
                            <FileText className={cn("h-3.5 w-3.5 mr-1 flex-shrink-0", fileIconColor)} />
                            <span className="font-medium text-sm">{data.count}</span>
                          </div>
                          <div className="flex items-center">
                            <Package className={cn("h-3.5 w-3.5 mr-1 flex-shrink-0", boxIconColor)} />
                            <span className="font-medium text-sm">{data.boxCount}</span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex-1 flex items-center justify-center text-gray-400 text-xs">无归档</div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date(currentDate.getFullYear() - 1, 0, 1))}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              上一年
            </Button>

            {/* 年份选择器 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" className="font-medium hover:bg-gray-100">
                  {currentDate.getFullYear()}年
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-0" align="center">
                <div className="grid grid-cols-3 gap-1 p-2">
                  {years.map((year) => (
                    <Button
                      key={year}
                      variant="ghost"
                      size="sm"
                      className={cn("h-9 rounded-md", year === currentDate.getFullYear() ? "bg-blue-100" : "")}
                      onClick={() => {
                        setCurrentDate(new Date(year, 0, 1))
                      }}
                    >
                      {year}
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date(currentDate.getFullYear() + 1, 0, 1))}
              className="flex items-center gap-1"
            >
              下一年
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {monthsInYear.map((month, index) => {
              const monthStr = formatDateToString(month, "yyyy-MM")
              const data = monthlyArchiveData[monthStr] || { count: 0, boxCount: 0 }
              // 使用模拟的今天日期进行演示
              const isCurrentMonth =
                month.getMonth() === TODAY_FOR_DEMO.getMonth() && month.getFullYear() === TODAY_FOR_DEMO.getFullYear()
              const isSelected =
                selectedDate &&
                selectedDate.getMonth() === month.getMonth() &&
                selectedDate.getFullYear() === month.getFullYear()

              return (
                <Card
                  key={index}
                  className={cn(
                    "cursor-pointer hover:shadow-md transition-shadow border",
                    isSelected ? "ring-2 ring-blue-500" : "",
                  )}
                  onClick={() => {
                    setSelectedDate(month)
                    setCurrentDate(month)
                    setView("month")
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="font-medium">{monthNames[month.getMonth()]}</h3>
                      {isCurrentMonth && <span className="h-2 w-2 rounded-full bg-blue-500"></span>}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <FileText className={cn("h-4 w-4 mr-2 flex-shrink-0", fileIconColor)} />
                        <span className="text-lg font-bold">{data.count}</span>
                      </div>
                      <div className="flex items-center">
                        <Package className={cn("h-4 w-4 mr-2 flex-shrink-0", boxIconColor)} />
                        <span className="text-lg font-bold">{data.boxCount}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
