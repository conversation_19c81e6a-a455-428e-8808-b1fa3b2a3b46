# 环境配置指南

## 目录

- [环境配置指南](#环境配置指南)
  - [目录](#目录)
  - [概述](#概述)
  - [Docker Compose 环境](#docker-compose-环境)
    - [配置文件概览](#配置文件概览)
    - [服务组件说明](#服务组件说明)
    - [启动与管理命令](#启动与管理命令)
  - [API 通信架构](#api-通信架构)
    - [请求路径规范](#请求路径规范)
    - [Nginx 反向代理配置](#nginx-反向代理配置)
  - [环境变量配置](#环境变量配置)
  - [本地开发环境](#本地开发环境)
    - [独立 Nginx 配置](#独立-nginx-配置)
      - [Linux 环境](#linux-环境)
      - [Windows 环境](#windows-环境)
  - [故障诊断与调试](#故障诊断与调试)

## 概述

本项目采用前后端分离架构，使用 Nginx 作为反向代理服务器统一处理请求转发。Docker Compose 作为推荐的部署方式，提供了一站式环境配置解决方案。

## Docker Compose 环境

### 配置文件概览

项目根目录的 `docker-compose.yml` 定义了完整的应用栈结构：

```yaml
version: '3'

services:
  nginx:
    image: nginx:alpine
    container_name: archive-flow-nginx
    ports:
      - "80:80"
    volumes:
      - ./frontend/config/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./frontend/public:/var/www/html/public
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - web
    networks:
      - archive-flow-network
    restart: unless-stopped
      
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: archive-flow-frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - archive-flow-network
    restart: unless-stopped
    
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: archive-flow-backend
    volumes:
      - .:/app
      - ./media:/app/media
      - ./static:/app/static
    environment:
      - DEBUG=True
    networks:
      - archive-flow-network
    restart: unless-stopped

networks:
  archive-flow-network:
    driver: bridge
```

### 服务组件说明

- **nginx**: 反向代理服务器，处理前端静态资源并转发 API 请求至后端
- **frontend**: Next.js 前端应用程序
- **web**: Django 后端 API 服务

所有服务通过共享的 Docker 网络 `archive-flow-network` 实现相互通信。

### 启动与管理命令

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务日志
docker-compose logs -f [服务名]

# 重建并重启特定服务
docker-compose up -d --build [服务名]

# 停止并移除所有容器
docker-compose down
```

## API 通信架构

### 请求路径规范

所有前端 API 请求统一使用 `/api/` 前缀路径，例如：

```javascript
// 前端 API 调用示例
fetch('/api/archive-records/import-excel/', {
  method: 'POST',
  body: formData
});
```

### Nginx 反向代理配置

Nginx 配置（位于 `frontend/config/nginx.conf`）负责：

1. 将 `/api/` 路径的请求转发到后端 Django 服务
2. 提供前端静态资源服务
3. 处理 CORS（跨域资源共享）问题

转发时会自动移除 `/api/` 前缀，以匹配 Django 路由配置。

## 环境变量配置

前端项目使用 `.env.local` 文件管理环境变量：

```env
# 开发环境配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# 生产环境配置（容器内部通信）
# NEXT_PUBLIC_API_URL=http://web:8000
```

**注意**：在 Docker 环境中，服务间通信使用服务名而非 localhost。

## 本地开发环境

推荐使用 Docker Compose 进行开发，但如需单独配置，可参考以下方法：

### 独立 Nginx 配置

#### Linux 环境

```bash
# 安装 Nginx
sudo apt update && sudo apt install -y nginx
# 或
sudo yum install -y epel-release nginx

# 部署配置文件
sudo cp frontend/config/nginx.conf /etc/nginx/conf.d/archive-flow.conf

# 检查配置并重新加载
sudo nginx -t
sudo systemctl reload nginx
```

#### Windows 环境

1. 从 [官方网站](http://nginx.org/en/download.html) 下载 Nginx
2. 解压至本地目录（如 `C:\nginx`）
3. 复制配置文件：

   ```powershell
   copy frontend\config\nginx.conf C:\nginx\conf\conf.d\archive-flow.conf
   ```

4. 启动 Nginx：

   ```powershell
   C:\nginx\nginx.exe
   ```

5. 重新加载配置：

   ```powershell
   C:\nginx\nginx.exe -s reload
   ```

## 故障诊断与调试

如遇 API 请求问题，可按以下步骤排查：

1. **检查 Nginx 配置**：

   ```bash
   docker-compose exec nginx nginx -t
   ```

2. **测试后端服务可达性**：

   ```bash
   docker-compose exec nginx curl http://web:8000/health/
   ```

3. **检查 Nginx 日志**：

   ```bash
   docker-compose exec nginx cat /var/log/nginx/error.log
   ```

4. **容器网络连通性测试**：

   ```bash
   docker-compose exec nginx ping web
   ```

5. **浏览器网络调试**：
   - 打开浏览器开发者工具的网络面板
   - 检查请求是否使用 `/api/` 前缀
   - 验证 HTTP 状态码和响应内容

完整请求流程：客户端 → Nginx (`/api/` 路径) → 后端 Django (无 `/api/` 前缀)
