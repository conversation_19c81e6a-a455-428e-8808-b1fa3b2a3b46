# CHANGE: [2024-03-29] 在 services 包的 __init__ 中导入核心服务类 #AFM-28
from .upload_service import UploadService
from .task_service import TaskService
from .record_update_service import update_archive_record, generate_file_url
# CHANGE: [2024-05-19] 导入 PdfProcessingService
# CHANGE: [DATE] 修正导入路径
from .pdf_processing_service import PdfProcessingService

# CHANGE: [2025-01-17] 导入 ReportSplittingService #PDF-Processing-V-MAX
from .report_splitting_service import ReportSplittingService

# CHANGE: [2024-04-19] 添加对文件存储和安全服务的引用 #AFM-29
# CHANGE: [DATE] 仅导入 FileStorageService 类
from .file_storage_service import FileStorageService
# 移除对旧的独立文件操作函数的导入
# from .file_storage_service import (
#     generate_archive_filename,
#     generate_archive_storage_path,
#     get_archive_storage_path,
#     generate_file_hash,
#     ensure_directory_exists,
#     get_temp_directory,
#     backup_archive_file,
#     verify_file_integrity,
#     get_backup_locations
# )

from .security_service import (
    create_secure_access_token,
    verify_access_token,
    check_file_access_permission,
    generate_download_url
)

# CHANGE: [2024-05-05] 添加对进度跟踪服务的引用 #AFM-30
from .progress_tracking_service import (
    ProgressTracker,
    NotificationService,
    create_progress_tracker,
    update_progress,
    get_task_progress,
    send_completion_notification,
    send_task_notification
)

# 定义 __all__ 来明确导出的公开接口 (可选但推荐)
__all__ = [
    'UploadService',
    'TaskService',
    'PdfProcessingService', # 添加 PDF 处理服务
    'ReportSplittingService', # 添加报告分割服务
    'update_archive_record',
    'generate_file_url',
    'FileStorageService', # 导出 FileStorageService 类
    # 移除旧的文件存储函数
    # 'generate_archive_filename',
    # 'generate_archive_storage_path',
    # 'get_archive_storage_path',
    # 'generate_file_hash',
    # 'ensure_directory_exists',
    # 'get_temp_directory',
    # 'backup_archive_file',
    # 'verify_file_integrity',
    # 'get_backup_locations',
    # 添加安全服务
    'create_secure_access_token',
    'verify_access_token',
    'check_file_access_permission',
    'generate_download_url',
    # 添加进度跟踪服务
    'ProgressTracker',
    'NotificationService',
    'create_progress_tracker',
    'update_progress',
    'get_task_progress',
    'send_completion_notification',
    'send_task_notification'
]
