# 操作文档: 更新 Celery 任务以传递预检查错误报告

## 📋 变更摘要
**目的**: 修改 Celery 任务 `process_pdf_task`，使其在预检查失败时，能够正确调用 `create_result_summary` 并传递详细的预检查错误信息。
**范围**:
*   `archive_processing/tasks.py::process_pdf_task`
**关联**: #AFM-Req1-Strict, #AFM-29, (接续对 `create_result_summary` 的修改)

## 🔧 操作步骤

### 📊 OP-001: 分析 `process_pdf_task` 预检查逻辑
**前置条件**: `process_pdf_task` 包含预检查逻辑，`create_result_summary` 已更新以接收 `pre_check_errors` 参数。
**操作**: 读取 `process_pdf_task` 代码，定位预检查失败的逻辑分支和当前调用报告生成的位置。发现当前调用错误且在成功路径缺少调用。
**后置条件**: 确认需要移除错误调用，并在预检查失败路径添加正确调用。

### ✏️ OP-002: 修改 `process_pdf_task` 调用报告逻辑
**前置条件**: OP-001 完成，用户确认修改计划。
**操作**: 编辑 `archive_processing/tasks.py` 文件：
    1. 在预检查失败的逻辑分支 (`if not pre_check_passed:`) 中，移除原有的、参数错误的 `create_result_summary` 调用。
    2. 在同一分支中，构造一个 `pre_check_errors_dict` 字典，包含 `parts_missing_number` 和 `numbers_missing_record` 列表。
    3. 将 `task.result_data` 更新为包含结构化错误信息的字典。
    4. 在更新 `task` 状态后，添加对 `processing_report_utils.create_result_summary` 的正确调用，传递必要的参数，包括 `pre_check_errors=pre_check_errors_dict`。
    5. 添加日志记录报告生成结果。
    6. 添加 TODO 注释，标记需要在任务成功路径和其它错误处理路径中补充报告生成逻辑。
    7. 添加代码以初始化 `archived_files_list` 和 `status_update_results`，为后续补充报告逻辑做准备。
**后置条件**: `process_pdf_task` 在预检查失败时能正确调用 `create_result_summary` 传递错误详情。

## 📝 变更详情

### CH-001: 更新 `process_pdf_task` 的报告调用
**文件**: `archive_processing/tasks.py`
**变更**: (见上一个 `edit_file` 工具调用的 diff 输出)
    * 移除了预检查失败路径下的错误 `create_result_summary` 调用。
    * 添加了构造 `pre_check_errors_dict` 的代码。
    * 更新了 `task.result_data` 的赋值，使用包含结构化错误信息的字典。
    * 添加了对 `create_result_summary` 的正确调用，并传递了 `pre_check_errors` 字典。
    * 添加了日志记录。
    * 添加了多个 TODO 注释，提醒补充缺失的报告生成调用。
    * 初始化了 `archived_files_list` 和 `status_update_results`。
**理由**: 修正错误的报告调用逻辑，确保在预检查失败时能利用更新后的 `create_result_summary` 生成详细的错误报告，满足 #AFM-Req1-Strict 的要求。
**潜在影响**: 轻微增加了预检查失败路径的处理逻辑。主要影响是现在预检查失败时会尝试生成一个文本摘要文件。成功路径和其它错误路径的报告生成仍需补充。

## ✅ 验证结果
**方法**: 代码审查（已完成），后续需要通过单元测试和集成测试进行验证，特别是模拟预检查失败的场景。
**结果**: 代码修改已应用。
**问题**: 报告生成逻辑仍不完整（缺少成功和其它失败路径）。`splitting_info_results` 作为 DTO 的假设待验证。
**解决方案**: 下一步将完成剩余的报告生成逻辑。后续需验证 DTO 假设。 