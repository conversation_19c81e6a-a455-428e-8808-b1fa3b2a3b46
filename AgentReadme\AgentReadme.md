# AI Agent Operator's manual  -  (AI 代理操作手册)

本项目旨在利用 AI 代理操作探索和实践项目管理、需求分析、代码生成、重构和测试等开发流程。通过结构化的文档组织和协作模式，为 AI 代理提供清晰的上下文和指导，提高开发效率和代码质量。

## 项目核心功能

*(请参考 `project_vision_and_roadmap.md` 和 `project_requirements.md` 获取本项目的具体功能详情)*

## 文档结构与协作指南 (`AgentReadme/` 目录)

为了清晰地管理项目信息并指导 AI 代理，我们采用了分层的文档结构，如目录结构图所示。

```ASCII 目录结构图
AgentReadme/
├── planning_and_requirements/         # 规划与需求 (核心活动文档)
│   ├── project_vision_and_roadmap.md  # 项目愿景与高层路线图 (低频更新)
│   ├── project_requirements.md        # 核心业务需求 (需求变更时更新)
│   ├── ai_dev_checkpoint.md           # AI开发检查点 (高频更新, Commit相关)
│   └── detailed_work_plan_and_log.md  # 详细工作计划和日志 (开发期间持续更新)
│
├── active_feature_plans/              # 当前正在开发的特性/功能块的详细计划 (可选)
│   └── feature_plan_api_implementation.md # (特性开发期间更新)
│   └── ...                            # (可能同时有多个活跃计划)
│
├── completed_feature_plans/           # 已完成归档的特性计划 (历史参考)
│   └── feature_plan_pdf_refactoring.md # (历史参考)
│   └── ...
│
├── guides/                            # 静态指南文档 (低频更新)
│   └── testing_guide.md               # 如何测试
│   └── ...                            # (未来可能包含代码风格、部署等)
│
├── testing_logs/                      # 测试与Debug过程记录 (按需添加)
│   └── debug_log_YYYYMMDD.md          # 或 debug_log_feature_X.md
│
├── framework_docs/                    # 框架/流程定义文档 (低频更新)
│   └── ai_rules_workflow.md           # AI规则交互工作流
│
├── ai_operation_logs/                 # AI 操作日志 (AI操作时自动生成)
│   └── YYYYMMDD_HHMMSS_description.md # AI执行重要操作的记录
│
└── function_map/                      # 代码函数映射 (脚本生成)
    └── function_summary.md            # 代码库函数概览
    └── generate_function_summary.py   # 生成脚本
```

**总结来说，这个体系是这样的：**

* **高层战略**: `project_vision_and_roadmap.md` (Why & Long-term What)
* **详细计划与历史**: `detailed_work_plan_and_log.md` (Detailed What & How - Current, Near-term, Done, Archived)
  * **当前执行**: 区域二
  * **待办/未来**: 区域三
  * **历史记录**: 区域四 & 五
* **迭代执行**: `ai_dev_checkpoint.md` (Times How & Status)
* **专题支持**: `testing_guide.md` (Specific How-to for Testing)

通过这种方式，`detailed_work_plan_and_log.md` 成为了连接战略和迭代执行的关键枢纽，既提供了足够的细节来指导当前工作，又通过历史记录和归档保持了项目的上下文和可追溯性，同时避免了信息过于分散或过于臃肿。

### `detailed_work_plan_and_log.md`与其他文档的具体协作说明

各主要规划文档（主要位于 `planning_and_requirements/` 目录）职责与协作方式如下：

1. **`planning_and_requirements/project_vision_and_roadmap.md` (项目愿景与路线图)**:
    * **角色**: 这是最高层级的文档，定义项目的**"为什么"**（业务目标、核心价值）和**长期的"什么"**（主要功能模块、大致阶段划分、关键架构决策）。它应该保持简洁，聚焦战略方向，避免陷入具体任务细节。
    * **协同**:
        * `detailed_work_plan_and_log.md` 的 **区域一 (文档指南与项目概览)** 会引用或链接到 `project_vision_and_roadmap.md`，从中提取核心目标和流程作为本详细计划的上下文。
        * `detailed_work_plan_and_log.md` 的 **区域六 (项目级信息与风险)** 可能会包含一些从路线图文档中提炼出的、贯穿整个项目的风险或关键信息。

2. **`planning_and_requirements/detailed_work_plan_and_log.md` (详细工作计划与日志)**:
    * **角色**: 这是**承上启下**的核心文档，负责将高层路线图分解为**可执行的、详细的"什么"和"如何"**。它跟踪当前阶段的具体任务、记录已完成的工作、管理待办事项，并存档历史计划。它是项目执行层面的主要参考。
    * **协同**:
        * **区域二 (当前阶段核心任务与状态)** 是当前开发活动的焦点，详细说明**现在正在做什么**。
        * **区域三 (近期规划、遗留与暂缓任务)** 管理**接下来要做什么**以及需要稍后处理的任务。
        * **区域四 (详细完成历史记录)** 提供了比路线图更详细的**"做完了什么"**的记录。
        * **区域五 (特定历史计划归档)** 存档了**过去某个重要部分是如何规划的**。

3. **`planning_and_requirements/ai_dev_checkpoint.md` (AI 开发检查点)**:
    * **角色**: 这是最底层的、**高频更新**的日志，记录**每次迭代的**'如何做'——具体的进展、遇到的问题、下一步的小计划。它非常注重时效性和细节。
    * **协同**:
        * `ai_dev_checkpoint.md` 中的进展和状态更新，应该**在用户做提交代码（commit）准备时**汇总到 `detailed_work_plan_and_log.md` 的 **区域二** 中，更新对应任务的状态（比如从 `[ ]` 到 `[>]` 或 `[x]`）。
        * 如果在检查点中发现了需要长期跟踪的遗留问题或新近期规划，可以添加到 `detailed_work_plan_and_log.md` 的 **区域三**。

4. **`guides/testing_guide.md` (测试指南)**: (位于 `guides/` 目录)
    * **角色**: 这是一个**专题文档**，专门说明如何进行测试（环境设置、命令、策略）。
    * **协同**: 当 `detailed_work_plan_and_log.md` 中的任务涉及到测试时（尤其是在区域二或区域四的完成记录中），可以简单地**引用或链接**到 `testing_guide.md` 中的相关部分，避免在计划文档中重复测试细节。

**AI 规则交互**: 为了更直观地理解上述文档角色以及 AI 核心规则（如 `@document-overview`, `@commit-and-update-docs` 等）之间是如何协同工作的，请参考 **[AI 规则交互工作流指南](./framework_docs/ai_rules_workflow.md)**。

## 协作流程建议

1. 从 `project_vision_and_roadmap.md` 理解整体目标。
2. 在 `detailed_work_plan_and_log.md` 查看和管理当前阶段的主要任务 (区域二)。
3. 在 `ai_dev_checkpoint.md` 记录迭代开发细节和进展。
4. **在做提交代码（commit）准备时，将 `ai_dev_checkpoint.md` 的关键状态更新同步到 `detailed_work_plan_and_log.md` 中的任务状态。**
5. 需要测试操作细节时查阅 `guides/testing_guide.md`。

## 迭代开发流程

项目采用清晰的迭代循环来推进开发：

1. **查看** - 查看之前确定的"下一步计划"（在`ai_dev_checkpoint.md`中），明确本迭代聚焦的任务。
2. **执行** - 根据计划编写代码。
3. **分析** - 对比本次迭代**实际完成的工作**与聚焦的任务：
   * 识别偏差、新增内容或部分完成情况。
   * **检查聚焦任务是否完成**: 如果聚焦任务已完成，但其所属的区域二主任务尚未结束，则**识别主任务的下一个逻辑步骤**，并准备在当前迭代继续执行。
4. **计划** - 基于**分析结果**和项目整体目标，确定并确认下一个迭代周期的计划：
   * **通常情况**: 聚焦于完成当前区域二任务的下一个步骤。
   * **区域二清空时**: 如果分析发现区域二所有任务已完成，则回顾区域三，**选择下一批任务并提议迁移至区域二**作为新的当前阶段核心任务。
5. **更新文档** - **在做提交准备时**更新以下文档：
   * 检查点文档 (`AgentReadme/planning_and_requirements/ai_dev_checkpoint.md`) - 在"最近提交"记录**实际完成内容**，在"下一步计划"记录已确认的下一步。
   * 工作计划文档 (`AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md`) - 根据分析结果灵活更新：
     * 更新区域二父任务的整体状态 (`[>]` 或 `[x]`)，并将迭代中新发现的、为完成该父任务所必需的**子任务或步骤**，根据其完成情况，作为子项添加到区域二的父任务下（标记为 `[ ]` 或 `[x]`）。
     * 将迭代中完成的**独立小任务或步骤**（非区域二任务子项）直接添加到区域四历史记录。
     * 将分析或新计划产生的长期条目添加到区域三。
   * 如有测试相关内容，更新测试日志 (`AgentReadme/testing_logs/`目录下的相关文件)  
   * 如涉及特定功能开发，更新功能计划文档 (`AgentReadme/active_feature_plans/`目录下的相关文件)
6. **提交** - 生成 commit message 并提交代码

**注意**：在做提交准备时，必须先更新相关文档，以便生成准确的 commit message。这确保了文档驱动的开发流程持续有效。
