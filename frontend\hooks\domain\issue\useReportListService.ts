"use client"

import { useState, useMemo, useCallback, useRef, useEffect } from "react"
import { useQueryClient } from "@tanstack/react-query"
import {
  GridApi,
  IServerSideDatasource,
  FilterModel,
  GridReadyEvent,
  GetRowIdParams,
  RowSelectedEvent,
} from 'ag-grid-enterprise'
import { getIssuableArchives, type IssuableArchive, type IssuableArchivesParams, updateArchiveTotalCopies } from "@/services/domain/issue/reportIssuingService"
import { getTodayStr, getDaysAgoStr } from '@/utils/date-utils'

// 严格的TypeScript类型定义
export interface SelectedRecord {
  id: string
  sampleNumber: string
  clientCompany: string
  clientContact: string
  clientDate: string
  projectNumber: string
  projectName: string
  projectLocation: string
  totalCopies: number
  remainingCopies: number
  issueCopies: number
  // status: 'pending' | 'processing' | 'completed'  目前没使用，发放状态目前应该是前端计算，这个值应该是档案归档状态
  firstDistribution: {
    copies: number
    time?: string           // 第一次发放时间
    distributor?: string    // 发放人
    recipient?: string      // 领取人
    recipientPhone?: string // 领取人电话
  } | null
  secondDistribution: {
    copies: number
    time?: string           // 第二次发放时间
    distributor?: string    // 发放人(2)
    recipient?: string      // 领取人(2)
    recipientPhone?: string // 领取人电话(2)
  } | null
  distributionCount?: number
}

// 导出组件Props类型，供外部使用
export interface ReportListProps {
  selectedRecords: SelectedRecord[]
  mockReports: any[]
  isEditable: boolean
  setSelectedRecords: React.Dispatch<React.SetStateAction<SelectedRecord[]>>
}

// 从此文件导出 IssuableArchive 类型，以便 report-list.tsx 可以使用
export type { IssuableArchive }
// 也导出 RowSelectedEvent 类型
export type { RowSelectedEvent }

interface UseReportListServiceOptions {
  onError?: (error: Error) => void
  onDataLoad?: (count: number) => void
}

// 查询参数构建函数
function buildIssuableArchivesQueryParams(
  pagination: { offset: number; limit: number },
  filterModel?: FilterModel,
  searchQuery?: string,
  startDate?: string,
  endDate?: string
): IssuableArchivesParams {
  const params: IssuableArchivesParams = {
    offset: pagination.offset,
    limit: pagination.limit,
  }

  if (searchQuery?.trim()) {
    params.search = searchQuery.trim()
  }

  if (startDate) {
    params.commissionDatetimeStart = startDate
  }

  if (endDate) {
    params.commissionDatetimeEnd = endDate
  }

  if (filterModel) {
    Object.entries(filterModel).forEach(([field, filter]: [string, any]) => {
      if (filter?.filter) {
        const fieldMap: Record<string, keyof IssuableArchivesParams> = {
          'unifiedNumber': 'unifiedNumber',
          'sampleNumber': 'sampleNumber',
          'clientUnit': 'clientUnit',
          'clientName': 'clientName',
          'projectNumber': 'projectNumber',
          'projectName': 'projectName',
          'projectLocation': 'projectLocation',
        }
        
        const paramKey = fieldMap[field]
        if (paramKey) {
          params[paramKey] = filter.filter
        }
      }
    })
  }

  return params
}

// 导出映射函数，使其可以在组件层被复用
export function mapLedgerRecordToSelectedRecord(ledgerRecord: IssuableArchive): SelectedRecord {
  // 处理null值，确保计算正确
  const firstCopies = ledgerRecord.firstIssueCopies || 0;
  const secondCopies = ledgerRecord.secondIssueCopies || 0;
  
  const remainingCopies = Math.max(0, 
    ledgerRecord.totalIssueCopies - (firstCopies + secondCopies)
  )

  return {
    id: ledgerRecord.unifiedNumber,
    sampleNumber: ledgerRecord.sampleNumber,
    clientCompany: ledgerRecord.clientUnit,
    clientContact: ledgerRecord.clientName,
    clientDate: ledgerRecord.commissionDatetime 
      ? new Date(ledgerRecord.commissionDatetime).toLocaleDateString() 
      : '-',
    projectNumber: ledgerRecord.projectNumber,
    projectName: ledgerRecord.projectName,
    projectLocation: ledgerRecord.projectLocation,
    totalCopies: ledgerRecord.totalIssueCopies,
    remainingCopies,
    issueCopies: 1,
    // status: 'pending',
    firstDistribution: firstCopies > 0 
      ? { 
          copies: firstCopies,
          time: ledgerRecord.firstIssueDatetime,
          distributor: ledgerRecord.firstIssuePerson,
          recipient: ledgerRecord.firstReceiverName,
          recipientPhone: ledgerRecord.firstReceiverPhone,
        } 
      : null,
    secondDistribution: secondCopies > 0 
      ? { 
          copies: secondCopies,
          time: ledgerRecord.secondIssueDatetime,
          distributor: ledgerRecord.secondIssuePerson,
          recipient: ledgerRecord.secondReceiverName,
          recipientPhone: ledgerRecord.secondReceiverPhone,
        } 
      : null,
    distributionCount: (firstCopies > 0 ? 1 : 0) + (secondCopies > 0 ? 1 : 0)
  }
}

// 重构后的Hook，不再管理选择状态
export const useReportListService = (
  options: UseReportListServiceOptions = {}
) => {
  console.log('🚀 [DEBUG] useReportListService 初始化 - 现代化版本已加载!', new Date().toISOString())
  const { onError, onDataLoad } = options
  const queryClient = useQueryClient()
  
  const [searchQuery, setSearchQuery] = useState("")
  const [startDate, setStartDate] = useState(getDaysAgoStr(90))
  const [endDate, setEndDate] = useState(getTodayStr())
  
  const gridApiRef = useRef<GridApi | null>(null)
  
  // CHANGE: [2025-06-17] 现代化重构 - 使用统一的筛选状态管理
  // 使用ref存储最新的筛选状态值，以确保datasource的稳定性
  const searchQueryRef = useRef(searchQuery)
  const startDateRef = useRef(startDate)
  const endDateRef = useRef(endDate)
  
  // 防抖计时器ref
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  // 每次渲染时更新ref的值
  searchQueryRef.current = searchQuery
  startDateRef.current = startDate
  endDateRef.current = endDate

  const handleError = useCallback((error: Error) => {
    onError?.(error)
    console.error("ReportListService Error:", error)
  }, [onError])

  const handleDataLoad = useCallback((count: number) => {
    onDataLoad?.(count)
  }, [onDataLoad])

  // CHANGE: [2025-06-17] 现代化重构 - 统一的数据刷新方法
  const refreshGridData = useCallback((reason: string, immediate = false) => {
    // 清除之前的刷新计时器
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current)
    }
    
    const executeRefresh = () => {
      if (gridApiRef.current) {
        console.log(`🔄 [DATA_REFRESH] ${reason}`)
        queryClient.invalidateQueries({ queryKey: ['issuableArchives'] })
        gridApiRef.current.refreshServerSide({ purge: true })
      }
    }
    
    if (immediate) {
      executeRefresh()
    } else {
      // 使用微任务确保状态已更新
      refreshTimeoutRef.current = setTimeout(executeRefresh, 0)
    }
  }, [queryClient])

  // CHANGE: [2025-06-17] 现代化重构 - 使用useEffect统一管理筛选状态变化
  // 监听日期筛选变化（立即刷新）
  useEffect(() => {
    if (gridApiRef.current) {
      refreshGridData(`日期筛选变更: ${startDate} ~ ${endDate}`, true)
    }
  }, [startDate, endDate, refreshGridData])

  // 监听搜索查询变化（防抖刷新）
  useEffect(() => {
    // 清除之前的搜索防抖计时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }
    
    // 如果有grid API，设置防抖刷新
    if (gridApiRef.current) {
      searchTimeoutRef.current = setTimeout(() => {
        refreshGridData(`搜索查询变更: "${searchQuery}"`, true)
      }, 500) // 500ms防抖延迟
    }
    
    // 清理函数
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchQuery, refreshGridData])

  // 清理所有计时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [])

  // 创建一个稳定的datasource
  const datasource: IServerSideDatasource = useMemo(() => {
    return {
      getRows: async (params) => {
        const { startRow, endRow, filterModel } = params.request
        if (startRow === undefined || endRow === undefined) {
          params.fail()
          return
        }

        const queryParams = buildIssuableArchivesQueryParams(
          { offset: startRow, limit: endRow - startRow },
          filterModel || undefined,
          searchQueryRef.current,
          startDateRef.current,
          endDateRef.current
        )

        // 2. 构建唯一的Query Key
        const queryKey = ['issuableArchives', queryParams]

        try {
          // 3. 使用 queryClient.fetchQuery
          const response = await queryClient.fetchQuery({
            queryKey,
            queryFn: () => getIssuableArchives(queryParams),
            staleTime: 5 * 60 * 1000, // 缓存5分钟
          })

          handleDataLoad(response.count)
          params.success({ rowData: response.results, rowCount: response.count })
        } catch (error) {
          const errorObj = error instanceof Error ? error : new Error('Unknown error occurred')
          handleError(errorObj)
          params.fail()
        }
      }
    }
  }, [queryClient, handleError, handleDataLoad]) // 依赖项中加入 queryClient

  const onGridReady = useCallback((params: GridReadyEvent) => {
    gridApiRef.current = params.api
  }, [])

  const getRowId = useCallback((params: GetRowIdParams<IssuableArchive>) => {
    // 确保返回一个有效的字符串ID，避免undefined导致的AG Grid错误
    return params.data.unifiedNumber || `row-${params.data.id}` || `row-${Math.random().toString(36).slice(2, 11)}`
  }, [])

  const refreshData = useCallback(() => {
    refreshGridData('手动刷新', true)
  }, [refreshGridData])

  // 选择当前页所有可选行
  const selectCurrentPage = useCallback((isRowSelectableFn?: (params: any) => boolean) => {
    console.log('🎯 [DEBUG] selectCurrentPage 被调用 - 新功能正在执行!', { hasGridApi: !!gridApiRef.current })
    if (!gridApiRef.current) {
      console.warn('Grid API 未准备就绪')
      return { success: false, message: 'Grid API 未准备就绪' }
    }

    try {
      const nodes: any[] = []
      let totalNodes = 0
      let selectableNodes = 0

      // 获取当前页的分页信息
      const currentPage = gridApiRef.current.paginationGetCurrentPage()
      const pageSize = gridApiRef.current.paginationGetPageSize()
      const startIndex = currentPage * pageSize
      const endIndex = startIndex + pageSize

      console.log('📊 [DEBUG] 分页信息:', { currentPage, pageSize, startIndex, endIndex })

      // 遍历当前页面的节点 (使用分页索引)
      for (let i = startIndex; i < endIndex; i++) {
        const rowNode = gridApiRef.current.getDisplayedRowAtIndex(i)
        if (!rowNode) break // 如果没有更多行，退出循环
        
        totalNodes++
        
        // 检查节点是否可选
        const isSelectable = isRowSelectableFn ? isRowSelectableFn(rowNode) : true
        
        if (rowNode.data && isSelectable) {
          selectableNodes++
          nodes.push(rowNode)
        }
      }

      if (nodes.length === 0) {
        return { 
          success: false, 
          message: '当前页面没有可选择的记录',
          stats: { total: totalNodes, selectable: selectableNodes, selected: 0 }
        }
      }

      // 批量设置选中状态
      gridApiRef.current.setNodesSelected({ nodes, newValue: true })
      
      return { 
        success: true, 
        message: `已选择当前页 ${nodes.length} 条记录`,
        stats: { total: totalNodes, selectable: selectableNodes, selected: nodes.length }
      }
    } catch (error) {
      console.error('选择当前页失败:', error)
      return { 
        success: false, 
        message: `选择操作失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }, [])

  // 取消选择当前页
  const deselectCurrentPage = useCallback(() => {
    if (!gridApiRef.current) {
      console.warn('Grid API 未准备就绪')
      return { success: false, message: 'Grid API 未准备就绪' }
    }

    try {
      const nodes: any[] = []
      let totalNodes = 0

      // 获取当前页的分页信息
      const currentPage = gridApiRef.current.paginationGetCurrentPage()
      const pageSize = gridApiRef.current.paginationGetPageSize()
      const startIndex = currentPage * pageSize
      const endIndex = startIndex + pageSize

      console.log('📊 [DEBUG] 取消选择 - 分页信息:', { currentPage, pageSize, startIndex, endIndex })

      // 查找当前页面所有已选中的节点 (使用分页索引)
      for (let i = startIndex; i < endIndex; i++) {
        const rowNode = gridApiRef.current.getDisplayedRowAtIndex(i)
        if (!rowNode) break // 如果没有更多行，退出循环
        
        totalNodes++
        if (rowNode.data && rowNode.isSelected()) {
          nodes.push(rowNode)
        }
      }

      if (nodes.length === 0) {
        return { 
          success: false, 
          message: '当前页面没有已选择的记录',
          stats: { total: totalNodes, deselected: 0 }
        }
      }

      // 批量取消选中状态
      gridApiRef.current.setNodesSelected({ nodes, newValue: false })
      
      return { 
        success: true, 
        message: `已取消选择当前页 ${nodes.length} 条记录`,
        stats: { total: totalNodes, deselected: nodes.length }
      }
    } catch (error) {
      console.error('取消选择当前页失败:', error)
      return { 
        success: false, 
        message: `取消选择操作失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }, [])

  // 获取当前页选择状态统计
  const getCurrentPageSelectionStats = useCallback(() => {
    if (!gridApiRef.current) {
      return { total: 0, selected: 0, selectable: 0 }
    }

    let total = 0
    let selected = 0
    let selectable = 0

    try {
      // 获取当前页的分页信息
      const currentPage = gridApiRef.current.paginationGetCurrentPage()
      const pageSize = gridApiRef.current.paginationGetPageSize()
      const startIndex = currentPage * pageSize
      const endIndex = startIndex + pageSize

      // 遍历当前页面的节点获取统计信息
      for (let i = startIndex; i < endIndex; i++) {
        const rowNode = gridApiRef.current.getDisplayedRowAtIndex(i)
        if (!rowNode) break // 如果没有更多行，退出循环
        
        if (rowNode.data) {
          total++
          if (rowNode.isSelected()) {
            selected++
          }
          // 这里可以根据实际的 isRowSelectable 逻辑来判断
          // 暂时假设所有有数据的节点都是可选的
          selectable++
        }
      }
    } catch (error) {
      console.error('获取选择状态统计失败:', error)
    }

    return { total, selected, selectable }
  }, [])

  // 更新档案总份数的事务协调函数（统一处理单个和批量）
  const updateTotalCopies = useCallback(async (
    archiveIdOrIds: number | number[], 
    newTotalCopies: number
  ): Promise<{ success: boolean; message: string; updatedCount: number; totalRequested: number; errors: any[] }> => {
    try {
      const isMultiple = Array.isArray(archiveIdOrIds)
      const result = await updateArchiveTotalCopies(archiveIdOrIds, newTotalCopies)
      
      // 构建批量操作的详细消息（全有或全无语义）
      let message = ''
      const totalRequested = result.totalRequested || (isMultiple ? archiveIdOrIds.length : 1)
      
      if (result.updatedCount === totalRequested) {
        // 全部成功
        message = `成功更新 ${result.updatedCount} 条记录的总份数为 ${newTotalCopies}`
        return {
          success: true, 
          message,
          updatedCount: result.updatedCount,
          totalRequested,
          errors: result.errors
        }
      } else {
        // 部分失败或全部失败
        const failedCount = totalRequested - result.updatedCount
        message = `更新失败：${result.updatedCount}/${totalRequested} 条记录成功，${failedCount} 条失败`
        
        if (result.errors && result.errors.length > 0) {
          const errorDetails = result.errors.map((err: any) => err.detail || err.message || JSON.stringify(err)).join('; ')
          message += `。错误详情：${errorDetails}`
        }
        
        return {
          success: false, 
          message,
          updatedCount: result.updatedCount,
          totalRequested,
          errors: result.errors
        }
      }
    } catch (error: any) {
      const isMultiple = Array.isArray(archiveIdOrIds)
      const errorMessage = `更新总份数失败：${error.message || '未知错误'}`
      
      return {
        success: false, 
        message: errorMessage,
        updatedCount: 0,
        totalRequested: isMultiple ? archiveIdOrIds.length : 1,
        errors: [{ detail: error.message }]
      }
    }
  }, [])

  console.log('📦 [DEBUG] useReportListService 返回对象包含新方法:', {
    hasSelectCurrentPage: typeof selectCurrentPage === 'function',
    hasDeselectCurrentPage: typeof deselectCurrentPage === 'function',
    hasGetCurrentPageSelectionStats: typeof getCurrentPageSelectionStats === 'function',
    hasUpdateTotalCopies: typeof updateTotalCopies === 'function'
  })

  return {
    // 状态
    searchQuery,
    startDate,
    endDate,
    
    // 操作方法 - CHANGE: 现在使用原生的state setters，由useEffect处理刷新
    setSearchQuery,
    setStartDate,
    setEndDate,
    refreshData,
    updateTotalCopies,
    
    // 选择相关方法
    selectCurrentPage,
    deselectCurrentPage,
    getCurrentPageSelectionStats,
    
    // ag-grid相关
    datasource,
    onGridReady,
    getRowId,
    gridApi: gridApiRef.current,
  }
}
