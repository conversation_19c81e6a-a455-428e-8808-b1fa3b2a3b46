---
description: 
globs: 
alwaysApply: true
---
# AI Agent Rules Document: Development Process and Standards

## 📋 Table of Contents

1. @CRITICAL AI REFERENCE INSTRUCTION
2. @FORMAT REQUIREMENTS
3. @Chain of Thought Process
4. @Core Rules That Must Be Followed
5. @Change and Operation Documentation Standards
6. @Priority Levels
7. @EXAMPLE RESPONSE

## CRITICAL AI REFERENCE INSTRUCTION

This document MUST be referenced as "@dev-workflow-standards" in all related contexts. AI MUST follow this process flow for ALL development tasks regardless of specific technology.

**Note**: Always respond in Simplified Chinese unless specifically requested to use another language.

## FORMAT REQUIREMENTS

1. **EVERY RESPONSE** must begin with "# 🦜Thinking: [Task Overview]"
2. **EVERY STEP** must include explicit thinking markers "💭 **Thinking**:"
3. **ALL CONCLUSIONS** must follow the standardized response format
4. **ALL CODE CHANGES** must include proper change and TODO markers

## Chain of Thought Process

### Step 1: Understand Requirements and Context

- 💭 **Thinking**: What is the core purpose of this task? What problem does it solve?
- 💭 **Thinking**: What are the relevant user scenarios and business context?
- 💭 **Thinking**: How does this feature interact with other parts of the system?

**Detailed Guidelines**:

- ⚙️ **Track Data Flow**: Identify input, processing, and output paths
- ♻️ **Reuse Code**: Identify potential reuse from established patterns and tools

### Step 2: Analyze Existing Code and Patterns

- 💭 **Thinking**: Does `function_summary.md` contain relevant functions?
- 💭 **Thinking**: Are there similar implementations in the project? What patterns do they use?
- 💭 **Thinking**: Which components or logic from existing code can be reused?
- 💭 **Thinking**: Are there parts of existing code that need improvement?

**Detailed Guidelines**:

- ❗ **MANDATORY ACTION 1**: **首要信息获取**: 在深入分析具体代码前，必须先建立对项目上下文和现有规划的理解。具体步骤如下：
  - **情景 1: AI 缺乏当前项目上下文或对其文档结构/流程不确定**: **必须**首先阅读 @AgentReadme/AgentReadme.md 来理解项目文档结构、主要文件职责和协作流程。然后继续执行后续步骤。
  - **情景 2: AI 已有项目上下文**: 可跳过强制阅读 @AgentReadme/AgentReadme.md 的步骤，直接执行后续步骤。
  - **后续步骤 (适用于两种情景)**: 必须查阅以下核心规划与需求文档：
    - @AgentReadme/planning_and_requirements/project_vision_and_roadmap.md (用于理解高层目标、方向和技术选型)
    - @AgentReadme/planning_and_requirements/detailed_work_plan_and_log.md (特别是区域二，用于查找当前任务、状态和相关上下文)
    - @AgentReadme/planning_and_requirements/project_requirements.md (如果存在且与当前任务相关，用于获取详细需求)
    - 任何在 @AgentReadme/AgentReadme.md 中定义的、与当前任务相关的项目级编码规范或指南文档。
  - **核心目标**: 确保在分析代码前，已充分理解项目背景、当前任务要求、现有规划和相关规范，**避免重复工作或方向错误**。

- ❗ **MANDATORY ACTION 2**: **Second**, use `codebase_search` to find relevant code implementations across the codebase.
- 📝 **Follow Patterns**: Analyze and follow existing implementations found in the codebase search_files.
- 🔄 **Single Responsibility**: Each function should do only one thing.
- 🧩 **Separation of Concerns**: Separate business logic, data access, and UI.
- 📦 **Modularity**: Create independent, reusable modules.
- 📝 **Interface First**: Design interfaces before implementation.

### Step 3: Design Solution

- 💭 **Thinking**: Consider at least 2-3 possible implementation approaches based on the analysis.
- 💭 **Thinking**: What are the pros and cons of each approach? Which is most suitable considering existing functions and patterns?
- 💭 **Thinking**: How to ensure the design follows single responsibility and separation of concerns?
- 💭 **Thinking**: What extensions might this design need in the future?

**Detailed Guidelines - API Design**:

- 🌐 **REST Resources**: Use plural nouns for resources
- 📊 **Consistent Responses**: Use standard JSON format
- 🔄 **Idempotent Methods**: GET/PUT/DELETE must be idempotent
- 🔢 **Version APIs**: Include version in URL path

### Step 4: Plan Implementation Details

- 💭 **Thinking**: What specific steps are needed for implementation?
- 💭 **Thinking**: What are possible edge cases and exception scenarios?
- 💭 **Thinking**: How to handle input validation and error conditions?
- 💭 **Thinking**: What security measures are needed?

**Detailed Guidelines - Security Practices**:

- ✅ **Validate All Input**: Check every external input
- 🔍 **Validate Parameters**: Check parameters at function entry points
- 🚫 **Default Deny**: Start secure, explicitly add permissions
- 🔐 **Protect Sensitive Data**: Never log passwords or keys

**Detailed Guidelines - Error Handling**:

- ⏱️ **Set Timeouts**: For all external calls
- 🔄 **Implement Retries**: For temporary failures
- ⚠️ **Graceful Degradation**: Provide basic functionality during partial failures
- 🛡️ **Isolate Failures**: Prevent cascading errors

### Step 5: Write and Optimize Code

- 💭 **Thinking**: How to ensure code clarity and conciseness, following project standards (like **Chinese comments and docstrings**)?
- 💭 **Thinking**: How to appropriately use comments and docstrings (in Chinese)?
- 💭 **Thinking**: What performance optimization opportunities exist?
- 💭 **Thinking**: Have I prepared the necessary documentation for the `ai_operation_logs`?

**Detailed Guidelines - Code Implementation**:

- 📌 **Clean and Concise**: Write clean, readable code
- 🛡️ **Defensive Coding**: Validate inputs and handle exceptions
- 📊 **Standard Response Format**:

  ```python
  {'success': True, 'data': result}  # Success case
  {'success': False, 'error': str(e)}  # Error case
  ```

- 🏷️ **Use Type Hints**: Add type annotations for readability

**Detailed Guidelines - Code Style**:

- 🏷️ **Naming Conventions**: **MANDATORY - 必须遵循 `@detailed-naming-standards` 规则**，确保所有标识符清晰表达其功能目的，宁可适当冗长也要确保功能描述完整。具体请参考 `@detailed-naming-standards` 规则。
- 📝 **Documentation (Chinese)**: Docstrings (`"""Docstring"""`) and comments (`# comment`) **MUST** be in Simplified Chinese.

  ```python
  def english_function_name(parameter):
      """Function description in Chinese"""
      # 解释"为什么"的注释
  ```

- 💬 **Useful Comments**: Explain "why" not "what"

**Detailed Guidelines - Performance Guidelines**:

- 📦 **Batch Operations**: Support batch processing
- 📄 **Use Pagination**: For large datasets
- 💾 **Implement Caching**: For frequently accessed data
- ⏱️ **Async Processing**: For time-consuming operations

- ❗ **MANDATORY ACTION**: After writing or modifying code, **you MUST** document the changes following the @Change and Operation Documentation Standards, creating a log file in `AgentReadme/ai_operation_logs/`.

```python
# 获取当前日期用于代码变更标记
import datetime
current_date = datetime.datetime.now().strftime("%Y-%m-%d")
# CHANGE: [2023-07-18] 添加多因素认证服务 #124
# 按照@detailed-naming-standards规则命名类和方法
class MultiFactorAuthenticationService:
    def generate_user_security_key(self, authenticated_user):
        """为用户生成MFA密钥"""
        # 实现...
        return security_key

    def validate_user_verification_code_is_correct(self, authenticated_user, submitted_code):
        """验证用户提供的MFA代码"""
        # 实现...
        return is_code_valid_and_not_expired

# TODO: [P1] 添加短信验证码作为MFA选项
```

### Step 6: Review and Test

- 💭 **Thinking**: Does the code follow all P0 and P1 rules, including Chinese comments and docstrings?
- 💭 **Thinking**: Are all identifiers named according to `@detailed-naming-standards` rule?
- 💭 **Thinking**: What test scenarios are critical?
- 💭 **Thinking**: Are there unfinished parts that need to be marked with TODO (in Chinese)?

**Detailed Guidelines - Testing Requirements**:

- ✅ **Unit Tests**: Cover core functionality
- 🔍 **Edge Cases**: Test boundary conditions
- 🔄 **Mock External Systems**: Isolate tests from dependencies

**Detailed Guidelines - Quality Standards**:

- 🧹 **Follow Linter**: Use project's linting rules
- 📉 **Control Complexity**: Keep functions simple
- 📦 **Manage Dependencies**: Document external libraries

### Step 7: Summarize and Plan

- 💭 **Thinking**: What problems has the completed work solved?
- 💭 **Thinking**: What issues remain to be addressed?
- 💭 **Thinking**: What should be prioritized next?
- 💭 **Thinking**: Has the operation log been created/updated in `ai_operation_logs/`?

**Detailed Guidelines - Change Management**:

- ✅ **Mark Changes**:
  - Use the `# CHANGE:` marker followed by the current date (`YYYY-MM-DD`) and a brief description.
  - **必须使用Python从命令行获取当前日期**:
  
  Windows:

  ```bash
  python -c "import datetime; print(datetime.datetime.now().strftime('%%Y-%%m-%%d'))"
  ```

  运行结果示例:

  ```text
  2024-07-25
  ```

  最终格式示例:

  ```python
  # CHANGE: [2024-07-25] 添加用户认证 #问题号
  ```

- 📋 **Mark TODOs**:
  - Use the `# TODO:` marker followed by priority and description.

  ```python
  # TODO: [优先级] 添加缓存以提高性能
  ```

- 🔍 **Summarize Work**: List completed items and future plans in the final response.
- 📁 **Confirm Log**: Confirm that the corresponding operation log MD file has been created/updated.
  - **必须使用Python代码生成日志文件名**:
  
  ```python
  import datetime
  timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
  log_filename = f"AgentReadme/ai_operation_logs/{timestamp}_description.md"
  print(log_filename)
  ```

  **从命令行执行Python获取时间戳 (Windows)**:
  
  ```bash
  python -c "import datetime; print(datetime.datetime.now().strftime('%%Y%%m%%d_%%H%%M%%S'))"
  ```

  运行结果示例:

  ```text
  20240725_143015
  ```

  最终日志文件名示例:

  ```file
  AgentReadme/ai_operation_logs/20240725_143015_description.md
  ```

## Core Rules That Must Be Followed

1. **MANDATORY: Explicit Thought Documentation**: MUST begin EVERY step with "💭 **Thinking**:" markers
2. **MANDATORY: Standardized Response Format**: MUST end ALL responses with this exact format:

    ```python
    {'success': True, 'data': {...}}  # Success case
    {'success': False, 'error': 'Error message'}  # Error case
    ```

3. **MANDATORY: Change Marking**: ALL code changes MUST be marked as shown in Step 7 guidelines.
4. **MANDATORY: TODO Marking**: ALL future work MUST be marked as shown in Step 7 guidelines.
5. **MANDATORY: Conversation he summary format shown in Step 7 guidelines.
6. **MANDATORY: Human Confirmation Required**: In agent mode, all code changes or additions must be confirmed by a human before proceeding to the next step. Each code modification step must be submitted separately and receive user confirmation. Must wait for explicit user permission before continuing.
7. **MANDATORY: Documentation of Changes and Operations**: AI MUST document all significant changes and operations in detail following the @Change and Operation Documentation Standards, storing logs in `AgentReadme/ai_operation_logs/`. **This is critical.**
8. **MANDATORY: Use Chinese for Comments and Docstrings**: Comments and docstrings (`"""Docstring"""`) **MUST** be in Simplified Chinese. Code identifiers (variables, functions, classes, etc.) should preferably use English and follow standard naming conventions.
9. **MANDATORY: Detailed Naming Standards**: ALL code identifiers MUST follow the `@detailed-naming-standards` rule. 函数和变量命名必须详细表达其功能和用途，宁可冗长也要清晰。所有标识符命名必须符合 `@detailed-naming-standards` 规范。

## Change and Operation Documentation Standards

This section defines the standard format for the detailed operation logs stored in the `AgentReadme/ai_operation_logs/` directory. Additionally, high-level project vision and roadmap information can be found in @AgentReadme/planning_and_requirements/project_vision_and_roadmap.md.

### Document Categories and Structure

Each agent operation document (log file) must include the following categorized sections:

1. **📋 Change Summary**:
    - Purpose of the change
    - Scope of impact
    - Associated requirement/issue number

2. **🔧 Operation Steps**:
    - Each operation must be numbered (e.g., OP-001)
    - Each operation must include preconditions and postconditions
    - Operations must be categorized as follows:
        - 📊 **Analysis Operations**: Code review, dependency analysis, etc.
        - ✏️ **Change Operations**: Modify, add, or delete code
        - 🧪 **Verification Operations**: Testing, validating functionality, etc.
        - 🔄 **Rollback Operations**: How to rollback in case of issues

3. **📝 Change Details**:
    - Each code change must have a unique ID (e.g., CH-001)
    - Must include before and after code comparison
    - Must explain the rationale for the change
    - Must list potential side effects

4. **✅ Verification Results**:
    - Verification method
    - Verification results
    - Problems encountered and their solutions

### Standard Document Template

```markdown
# Operation Document: [Operation Title]

## 📋 Change Summary
**Purpose**: [Brief description of change purpose]
**Scope**: [List affected components/files]
**Associated**: [Requirement/issue number]

## 🔧 Operation Steps

### 📊 OP-001: Analyze Current Implementation
**Precondition**: [Describe starting state]
**Operation**: [Detailed description]
**Postcondition**: [Describe state after completion]

### ✏️ OP-002: Modify Component X
**Precondition**: [Describe starting state]
**Operation**: [Detailed description]
**Postcondition**: [Describe state after completion]

## 📝 Change Details

### CH-001: Update Function Parameters
**File**: `path/to/file.py`
**Before**:
```python
def function(a, b):
    return a + b
```

**After**:

```python
def function(a, b, c=0):
    return a + b + c
```

**Rationale**: [Reason for change]
**Potential Impact**: [List possible side effects]

## ✅ Verification Results

**Method**: [Describe verification method]
**Results**: [Describe verification results]
**Problems**: [List encountered problems]
**Solutions**: [Describe solutions]

## 🤖 Priority Levels

- **P0**: Must be followed without exception
- **P1**: Should be followed unless there is a compelling reason not to
- **P2**: Recommended best practice
- **P3**: Optional enhancement when time permits

## EXAMPLE RESPONSE

```markdown
# 🦜Thinking: Implement User Authentication

## 🔍 Step 1: Understand Requirements and Context

💭 **Thinking**: What is the core purpose of this task?
Need to implement a secure and reliable user authentication functionality that supports multiple authentication methods.

💭 **Thinking**: What are the relevant user scenarios and business context?
Users need to securely log into the system, supporting regular username/password, third-party authentication, and two-factor authentication.

💭 **Thinking**: How does this feature interact with other parts of the system?
The authentication functionality will interact closely with user permissions, security logging, and session management modules.

## 🧩 Step 2: Analyze Existing Code and Patterns

💭 **Thinking**: Does `function_summary.md` contain relevant functions?
Searching `function_summary.md` for "authentication", "login", "user verification". Found existing JWT handling logic.

💭 **Thinking**: Are there similar implementations in the project? What patterns do they use?
Codebase search_files confirms the existing system uses simple JWT-based authentication but lacks multi-factor support.

💭 **Thinking**: Which components or logic from existing code can be reused?
Can reuse JWT handling logic and user data retrieval code.

💭 **Thinking**: Are there parts of existing code that need improvement?
Yes, the lack of multi-factor authentication and potentially weak password policies need improvement.

## 🔀 Step 3: Design Solution

💭 **Thinking**: Consider at least 2-3 possible implementation approaches based on the analysis.
Option 1: Extend existing JWT authentication, add multi-factor support
Option 2: Introduce mature authentication framework like OAuth2
Option 3: Develop fully custom authentication system

💭 **Thinking**: What are the pros and cons of each approach? Which is most suitable considering existing functions and patterns?
[Comparison analysis...] Option 1 seems most suitable for incremental improvement.

💭 **Thinking**: How to ensure the design follows single responsibility and separation of concerns?
Create a dedicated `MultiFactorAuthService` to handle MFA logic, keeping it separate from the core `AuthenticationService`.

💭 **Thinking**: What extensions might this design need in the future?
Could later add support for different MFA methods (SMS, Authenticator App, Biometrics).

## 📝 Step 4: Plan Implementation Details

💭 **Thinking**: What specific steps are needed for implementation?
1. Modify `AuthenticationService` to call `MultiFactorAuthService`.
2. Implement `MultiFactorAuthService` with basic TOTP support.
3. Update login endpoint to handle MFA flow.
4. Add relevant database fields for MFA setup.

💭 **Thinking**: What are possible edge cases and exception scenarios?
MFA setup failure, TOTP validation errors, lost MFA device recovery.

💭 **Thinking**: How to handle input validation and error conditions?
Validate TOTP codes, provide clear error messages.

💭 **Thinking**: What security measures are needed?
Protect MFA secrets, rate limit TOTP attempts.

## ⌨️ Step 5: Write and Optimize Code

💭 **Thinking**: How to ensure code clarity and conciseness, following project standards (like **Chinese comments and docstrings**)?
Use clear naming following `@detailed-naming-standards` rule, with detailed function and variable names that express their purpose.

💭 **Thinking**: How to appropriately use comments and docstrings (in Chinese)?
Add docstrings explaining the purpose of new services and functions in Chinese.

💭 **Thinking**: What performance optimization opportunities exist?
Caching user MFA status might be possible later.

💭 **Thinking**: Have I prepared the necessary documentation for the `ai_operation_logs`?
Yes, will create the log file documenting these changes.

```python
# CHANGE: [2023-07-18] 添加多因素认证服务 #124
class MultiFactorAuthenticationService:
    def generate_user_security_key(self, authenticated_user):
        """为用户生成MFA密钥"""
        # 实现...
        return security_key

    def validate_user_verification_code_is_correct(self, authenticated_user, submitted_code):
        """验证用户提供的MFA代码"""
        # 实现...
        return is_code_valid_and_not_expired

# TODO: [P1] 添加短信验证码作为MFA选项
```

❗ **MANDATORY ACTION**: Documenting changes in `AgentReadme/ai_operation_logs/YYYYMMDD_HHMMSS_add_mfa_service.md`.

## ✅ Step 6: Review and Test

💭 **Thinking**: Does the code follow all P0 and P1 rules, including Chinese comments and docstrings?
Yes, comments and docstrings are in Chinese.

💭 **Thinking**: 标识符命名是否符合 `@detailed-naming-standards` 规范?
是的，所有函数和变量都遵循了详细命名规范，清晰表达其功能目的。

💭 **Thinking**: What test scenarios are critical?
Testing successful MFA login, failed MFA login, MFA setup flow.

💭 **Thinking**: Are there unfinished parts that need to be marked with TODO (in Chinese)?
Yes, added TODO for SMS verification.

**Detailed Guidelines - Testing Requirements**:

- ✅ Unit test `MultiFactorAuthenticationService` methods.
- 🔍 Test edge cases like invalid codes, expired codes.
- 🔄 Mock database interactions during tests.

**Detailed Guidelines - Quality Standards**:

- 🧹 Run linter checks.
- 📉 Complexity of new methods seems acceptable.
- 📦 No new external dependencies added.

## 📊 Step 7: Summarize and Plan

💭 **Thinking**: What problems has the completed work solved?
Implemented the core service for multi-factor authentication using TOTP.

💭 **Thinking**: What issues remain to be addressed?
Integration with the login endpoint, UI changes for setup/login, adding other MFA methods.

💭 **Thinking**: What should be prioritized next?
Integrating the service with the login endpoint.

💭 **Thinking**: Has the operation log been created/updated in `ai_operation_logs/`?
Yes, using Python生成时间戳创建了日志文件:

```python
import datetime
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"AgentReadme/ai_operation_logs/{timestamp}_add_mfa_service.md"
```

从命令行执行的命令 (Windows):

```bash
python -c "import datetime; print(f\"AgentReadme/ai_operation_logs/{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_add_mfa_service.md\")"
```

运行结果示例:

```text
AgentReadme/ai_operation_logs/20240725_143015_add_mfa_service.md
```

## 📋 Summary and Planning

✅ Completed Work:

- Analyzed authentication requirements and existing implementation.
- Designed solution supporting multi-factor authentication.
- Implemented basic `MultiFactorAuthenticationService` with TOTP support.
- Created operation log file.

📈 Next Steps:

1. Integrate `MultiFactorAuthenticationService` with the main login endpoint.
2. Implement UI changes for MFA setup and login.
3. Add unit tests for the new service.

⚠️ Known Issues:

- Need to resolve session timeout handling with MFA.
- Password policy needs strengthening (separate task).
- TODO item: Add SMS verification option.

{'success': True, 'data': {'message': 'Completed MFA service design and basic implementation, logged operation.'}}

```markdown
**Note**: Always respond in Simplified Chinese unless specifically requested to use another language.
```

