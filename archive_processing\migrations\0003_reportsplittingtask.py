# Generated by Django 5.1.11 on 2025-06-21 15:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_processing', '0002_remove_uploadedfile_assigned_box_number_and_more'),
        ('archive_records', '0019_alter_archiverecord_archive_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportSplittingTask',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('archive_pdf_path', models.CharField(max_length=1024, verbose_name='档案PDF文件路径')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '处理失败')], default='pending', max_length=50, verbose_name='任务状态')),
                ('retry_count', models.IntegerField(default=0, verbose_name='重试次数')),
                ('target_start_page', models.IntegerField(blank=True, null=True, verbose_name='检测到的目标起始页码')),
                ('output_pdf_path', models.CharField(blank=True, max_length=1024, null=True, verbose_name='输出文件路径')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('archive_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_splitting_tasks', to='archive_records.archiverecord')),
            ],
            options={
                'verbose_name': '报告分割任务',
                'verbose_name_plural': '报告分割任务',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status'], name='archive_pro_status_e71838_idx'), models.Index(fields=['archive_record'], name='archive_pro_archive_d6f82e_idx')],
            },
        ),
    ]
