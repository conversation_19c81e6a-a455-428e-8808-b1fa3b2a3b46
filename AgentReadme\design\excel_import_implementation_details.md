# Excel导入功能技术实现文档

## 1. 功能概述

Excel导入功能是系统的核心功能之一，允许用户通过上传Excel文件批量导入档案记录。该功能采用两阶段导入流程：首先分析Excel文件并检测潜在冲突，然后由用户决策如何处理这些冲突并确认导入。

主要特点包括：

- 两阶段导入流程：分析阶段和确认阶段
- 细粒度的冲突检测与处理
- 实时进度反馈
- 会话持久化支持页面刷新恢复
- 全系统导入锁定机制
- 交互式冲突解决界面

## 2. 架构设计

### 2.1 前后端职责分离

**前端职责**：

- 文件上传与验证
- 分析进度轮询与显示
- 冲突可视化与用户交互
- 会话状态管理
- 响应式UI适配不同设备

**后端职责**：

- Excel数据解析
- 数据验证与冲突检测
- 会话管理与锁定机制
- 执行实际导入操作
- 性能优化与大文件处理

### 2.2 关键技术组件

1. **前端**：
   - React函数式组件
   - ShadcnUI组件库
   - AG Grid用于冲突表格展示
   - 自定义状态管理
   - SessionStorage用于会话持久化

2. **后端**：
   - Django REST框架API
   - pandas用于Excel处理
   - 缓存系统用于会话管理
   - 自定义冲突分析与解决策略
   - 事务管理确保数据一致性

### 2.3 数据流与通信

```
前端                              后端
┌────────────┐    1.上传Excel    ┌────────────┐
│            │─────────────────> │            │
│            │    2.返回会话ID   │            │
│            │ <────────────────-│            │
│   React    │    3.轮询进度     │   Django   │
│  Components│─────────────────> │   REST     │
│            │    4.返回进度     │    API     │
│            │ <────────────────-│            │
│            │    5.获取分析结果 │            │
│            │─────────────────> │            │
│            │    6.返回冲突记录 │            │
│            │ <────────────────-│            │
│            │    7.确认导入     │            │
│            │─────────────────> │            │
│            │    8.返回导入结果 │            │
│            │ <────────────────-│            │
└────────────┘                   └────────────┘
```

## 3. 导入流程详解

### 3.1 分析阶段

1. **文件上传**：
   - 用户选择Excel文件并点击"开始上传并分析"按钮
   - 前端验证文件格式，确保是`.xlsx`或`.xls`文件
   - 通过`FormData`将文件上传到`/api/archive-records/analyze-excel/`端点

2. **创建导入会话**：
   - 后端创建全局唯一会话ID
   - 检查系统中是否已有活跃会话，如有则拒绝新会话
   - 将Excel文件保存到临时目录
   - 在缓存中初始化会话状态为`pending`
   - 设置导入锁定，确保系统中同一时间只有一个活跃导入会话

3. **后台分析**：
   - 后端使用独立线程进行分析，不阻塞响应
   - 前端立即返回会话ID，进入分析进度显示界面
   - 使用pandas读取Excel，识别表头，映射字段
   - 与数据库比对，识别新记录、冲突记录和完全相同的记录
   - 更新会话状态和分析进度，包括已分析记录数、总记录数和百分比

4. **进度轮询**：
   - 前端定期调用`/api/archive-records/excel-import-progress/`端点
   - 使用指数退避策略动态调整轮询间隔，减少服务器负载
   - 将进度显示在UI上，包括进度条和分析统计
   - 分析完成后自动获取完整分析结果

### 3.2 确认阶段

1. **获取分析结果**：
   - 前端调用`/api/archive-records/excel-analysis-result/`获取完整分析结果
   - 后端返回冲突记录列表和统计信息，包括新记录数、冲突记录数、完全相同记录数等
   - 前端显示统计信息并准备冲突处理界面

2. **冲突处理**：
   - 用户查看冲突记录并选择处理策略（更新、跳过）
   - 使用AG Grid组件显示冲突记录并允许用户展开查看详情
   - 用户可以批量设置相同类型记录的处理策略
   - 支持筛选不同类型的冲突记录

3. **确认导入**：
   - 用户确认处理策略后，前端调用`/api/archive-records/confirm-import/`端点
   - 后端根据用户决策执行实际导入
   - 更新会话状态为`confirmed`并释放导入锁
   - 返回导入结果统计

4. **结果显示**：
   - 前端显示导入结果，包括成功记录数、失败记录数、创建数、更新数、跳过数等
   - 提供导入历史链接，允许用户查看详细导入日志
   - 允许用户重置界面开始新的导入

## 4. 会话管理与持久化

### 4.1 会话数据结构

```python
@dataclass
class ImportSessionInfo:
    session_id: str           # 会话ID
    file_path: str            # 临时文件路径
    user_id: int              # 用户ID
    created_at: datetime      # 创建时间
    expires_at: datetime      # 过期时间
    conflict_stats: Dict      # 冲突统计
    total_records: int        # 总记录数
    sheet_name: Union[str, int] # 工作表名称或索引
    original_filename: str    # 原始文件名
    session_status: str       # 会话状态
    analyzed_records: int     # 已分析的记录数
    analyzed_percentage: float # 分析进度百分比
```

### 4.2 会话状态流转

```
┌─────────┐     上传Excel      ┌─────────────┐    开始分析    ┌─────────────┐
│ pending │───────────────────>│ analyzing   │───────────────>│ analyzed    │
└─────────┘                    └─────────────┘                └─────────────┘
                                                                    │
┌─────────┐                    ┌─────────────┐                      │
│ expired │<───────────────────│ cancelled   │<─────────────────────┘
└─────────┘      过期清理      └─────────────┘        取消         
     ^                                ^
     │                                │
     └────────────────────────────────┘
                用户取消或超时
```

### 4.3 会话持久化

前端使用`sessionStorage`保存会话关键信息：

```typescript
// 保存会话信息
sessionStorage.setItem('importSessionId', sessionId);
sessionStorage.setItem('importStep', step);
sessionStorage.setItem('importTimestamp', new Date().getTime().toString());
```

页面刷新时，组件会检查`sessionStorage`中的会话信息：

1. 验证会话有效性（30分钟内创建的会话）
2. 调用API检查会话在服务端是否仍有效
3. 恢复会话状态和进度显示
4. 根据会话阶段（分析中/已分析）显示对应界面

### 4.4 会话锁定机制

为避免同时执行多个导入操作导致数据一致性问题，系统实现了全局导入锁：

- 使用缓存键`excel_import_active_session`存储活跃会话信息
- 每次创建新会话前检查锁是否存在
- 锁包含会话ID、用户ID和时间戳信息
- 确认导入或取消导入时释放锁
- 会话过期时自动释放锁

## 5. 特殊场景处理

### 5.1 页面刷新处理

**实现逻辑**：

1. 页面初始化时检查`sessionStorage`中的会话信息
2. 验证会话是否在有效期内（30分钟内创建）
3. 调用`getAnalysisProgress` API检查会话状态
4. 根据会话状态恢复对应界面：
   - `analyzing`: 显示进度界面，继续轮询进度
   - `analyzed`: 获取完整分析结果，显示确认界面
   - 其他状态：清理会话信息，显示选择文件界面
5. 注意：**刷新页面不会重新执行分析任务**，只是恢复UI状态并获取最新进度

**关键代码**：

```typescript
// 尝试从会话存储恢复导入会话
const savedSessionId = sessionStorage.getItem('importSessionId');
const savedStep = sessionStorage.getItem('importStep');

// 验证会话有效性
if (savedSessionId && savedStep) {
  // 首先检查会话是否仍然有效（不会重新执行分析，只检查状态）
  const progress = await excelImportService.getAnalysisProgress(savedSessionId);
  
  if (progress.status === 'analyzing' || progress.status === 'analyzed') {
    // 恢复会话状态
    setImportSessionId(savedSessionId);
    setCurrentStep(savedStep);
    
    // 获取完整结果或继续轮询
    if (progress.status === 'analyzed') {
      // 注意：这里只是获取已经分析完的结果，不会重新执行分析
      const result = await excelImportService.getAnalysisResult(savedSessionId);
      // ...更新UI状态
    }
  }
}
```

### 5.2 取消导入处理

**实现逻辑**：

1. 用户点击"取消"按钮调用`resetForm`函数
2. 立即停止前端轮询并显示加载状态
3. 调用`excelImportService.cancelImport`发送取消请求
4. 后端处理：
   - 验证会话是否存在
   - 更新会话状态为`cancelled`
   - 释放会话锁
   - 清理临时文件
   - 删除会话相关缓存
5. 前端清理本地状态并显示导入已取消通知
6. 无论API调用成功与否，都会重置前端状态

**关键代码**：

```typescript
// 重置表单前先终止轮询
const resetForm = async () => {
  // 立即停止进度轮询
  setIsPollingProgress(false);
  if (progressPollingRef.current) {
    clearTimeout(progressPollingRef.current);
    progressPollingRef.current = null;
  }
  
  // 显示加载状态
  setIsUploading(true);
  
  try {
    // 如果有活跃的会话ID，通知后端取消会话
    if (importSessionId) {
      // 显示取消进行中的状态
      toast({
        title: "正在取消导入",
        description: "正在取消并清理导入会话...",
        duration: 2000,
      });
      
      // 调用取消导入API
      await excelImportService.cancelImport(importSessionId);
      
      // 取消成功通知
      toast({
        title: "导入已取消",
        description: "导入会话已成功取消",
        variant: "default",
      });
    }
  } catch (error) {
    // 错误处理...
  } finally {
    // 无论API调用成功与否，都重置前端状态
    setIsUploading(false);
    setSelectedFile(null);
    setUploadProgress(0);
    setImportSessionId("");
    setAnalysisResult(null);
    setConflictRecords([]);
    setShowConflictModal(false);
    setCurrentStep('select');
    setFilterType(null);
    
    // 清理文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    
    // 移除可能存在的会话存储
    sessionStorage.removeItem('importSessionId');
    sessionStorage.removeItem('importStep');
    sessionStorage.removeItem('importTimestamp');
  }
};
```

### 5.3 会话过期处理

**实现逻辑**：

1. 会话在服务端有固定过期时间（默认30分钟）
2. 前端轮询时检测会话过期错误
3. 当检测到会话过期，立即：
   - 停止轮询
   - 清理会话存储
   - 显示会话过期通知
   - 重置到选择文件状态
4. 服务端会话过期时自动释放会话锁

**关键代码**：

```typescript
// 特殊处理会话过期错误
const isSessionExpired = error instanceof Error && 
  (error.name === 'SessionExpiredError' || 
   error.message.includes('导入会话不存在') || 
   error.message.includes('会话已过期'));

if (isSessionExpired) {
  console.log('导入会话已过期或不存在，停止轮询');
  setIsPollingProgress(false);
  
  // 清理会话存储
  sessionStorage.removeItem('importSessionId');
  sessionStorage.removeItem('importStep');
  sessionStorage.removeItem('importTimestamp');
  
  // 通知用户会话已过期
  toast({
    title: "导入会话已过期",
    description: "您的导入会话已过期或不存在，请重新上传文件",
    variant: "destructive",
  });
  
  // 重置到选择文件状态
  setCurrentStep('select');
  setSelectedFile(null);
  setImportSessionId('');
}
```

## 6. 冲突检测与解决

### 6.1 冲突类型定义

系统定义了三种冲突类型：

- **new**: 新记录，数据库中不存在该委托编号
- **update**: 可更新记录，数据库中存在该委托编号但有字段差异
- **identical**: 完全相同记录，数据库中存在且所有字段值相同

### 6.2 冲突分析流程

1. **收集现有记录**：查询数据库中所有可能冲突的委托编号
2. **批量检查**：将Excel记录与数据库记录比对
3. **对比字段**：逐一比对关键字段，生成差异列表
4. **分类记录**：根据比对结果将记录分入不同冲突类型
5. **生成差异**：针对冲突记录生成详细字段差异信息

### 6.3 冲突解决策略

系统支持三种冲突解决策略：

- **UPDATE**: 更新数据库中现有记录
- **SKIP**: 跳过该记录，保留数据库中现有值
- **CREATE**: （已弃用，实际使用SMART_UPDATE策略）理论上创建新记录

实际导入策略：

- 对于所有记录（包括标记为CREATE的）都使用`SMART_UPDATE`策略
- `SMART_UPDATE`策略会自动检查记录是否存在
  - 如果存在且用户选择更新，则更新记录
  - 如果存在且用户选择跳过，则跳过记录
  - 如果不存在，则创建新记录

### 6.4 冲突UI实现

**冲突网格**：

- 使用AG Grid组件展示冲突记录
- 支持Master/Detail模式，允许展开/折叠查看差异
- 自定义渲染器展示差异详情
- 类型徽章标识不同冲突类型
- 操作列允许选择处理策略

**策略选择**：

- 通过下拉菜单选择处理策略
- 支持批量设置特定类型记录的处理策略
- 冲突类型过滤，方便用户集中处理特定类型

## 7. 性能优化

### 7.1 批量处理与分页

1. **批量数据库查询**：
   - 一次性查询所有可能冲突的记录，减少数据库交互
   - 使用in-memory缓存存储查询结果，避免重复查询

2. **进度更新优化**：
   - 采用批量更新机制，减少更新频率
   - 进度报告间隔设置为5-10条记录，平衡实时性和性能
   - 使用单独线程处理分析，不阻塞主请求

### 7.2 前端优化

1. **动态轮询间隔**：
   - 使用指数退避策略，初始间隔500ms
   - 根据进度动态调整间隔，进度>50%时增加间隔
   - 设置最大间隔2000ms，避免过频或过慢更新

2. **组件优化**：
   - AG Grid虚拟滚动支持大量记录
   - 使用React.memo减少不必要的渲染
   - 延迟加载详情面板内容

3. **错误恢复机制**：
   - 轮询失败后采用指数退避重试
   - 自动检测会话过期，优雅降级

### 7.3 后端优化

1. **并发控制**：
   - 全局导入锁确保系统中只有一个活跃导入会话
   - 使用独立线程处理分析，不阻塞其他请求

2. **缓存优化**：
   - 会话信息使用缓存存储，避免数据库压力
   - 冲突记录单独缓存，优化内存使用

3. **数据验证优化**：
   - 前置数据类型检验，早期过滤无效数据
   - 委托编号规范化，确保一致比对

## 8. 可能的改进方向

1. **多会话支持**：
   - 允许不同用户同时进行导入操作
   - 增加会话权限控制，允许管理员查看和取消其他用户会话

2. **断点续传**：
   - 支持大文件分块上传
   - 允许中断后从断点继续分析

3. **更精细的冲突处理**：
   - 允许字段级别的选择性更新
   - 提供数据合并选项，取现有和导入数据的最佳值

4. **AI辅助决策**：
   - 基于历史操作和数据质量自动推荐处理策略
   - 批量智能分类提高处理效率

5. **导入模板管理**：
   - 支持保存和应用不同的导入模板
   - 自定义字段映射和验证规则

6. **WebSocket实时进度**：
   - 替代轮询机制，使用WebSocket推送实时进度
   - 减少服务器和网络负载

## 9. 附录：核心类与方法

### 前端

1. **ExcelImportWithConflictResolution**：
   - 主组件，管理整个导入流程的UI和状态
   - 实现文件上传、分析轮询、会话管理等核心逻辑

2. **ExcelImportService**：
   - 前端服务类，封装与后端API交互
   - 提供文件上传、进度查询、确认导入等方法

3. **ConflictResolutionGrid**：
   - 用于展示冲突记录的AG Grid包装组件
   - 实现Master/Detail模式显示冲突详情

### 后端

1. **ImportSessionManager**：
   - 管理导入会话的创建、获取、更新和删除
   - 实现会话锁定机制和过期清理

2. **ExcelConflictAnalyzer**：
   - 分析Excel数据与数据库的冲突
   - 生成冲突记录和差异信息

3. **ExcelImportService**：
   - 执行实际的导入操作
   - 根据用户决策应用不同的导入策略
