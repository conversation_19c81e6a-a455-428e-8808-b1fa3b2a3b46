"""
生成项目函数汇总文档

此脚本用于生成项目中所有 Python 函数的汇总文档。
它会扫描指定目录下的所有 Python 文件，提取函数信息并生成 Markdown 格式的汇总文档。

# 命令行参数:
python generate_function_summary.py -d . -o function_summary.md

# 实际运行命令:
python AgentReadme/function_map/generate_function_summary.py

"""

import ast
import os
import argparse
from pathlib import Path
import fnmatch # CHANGE: [2024-03-29] 导入 fnmatch 用于 .gitignore 模式匹配 #AFM-XX

# --- 配置 ---
# 要扫描的根目录 (通常是项目根目录)
# 可以通过命令行参数覆盖
DEFAULT_ROOT_DIR = '.'

# CHANGE: [2024-03-29] 让输出文件默认保存在脚本所在目录 #AFM-XX
# DEFAULT_OUTPUT_FILE = 'AgentReadme/project_documentation/function_summary.md'
# 获取脚本所在的目录
SCRIPT_DIR = Path(__file__).resolve().parent
DEFAULT_OUTPUT_FILE = SCRIPT_DIR / 'function_summary.md'

# 要排除的目录名称列表 (硬编码, 精确匹配)
# 这些通常是即使 .gitignore 允许也需要排除的
HARDCODED_EXCLUDE_DIRS = {
    '.conda',
    # 'archive_flow_manager.conda',
    '.git',
    '.idea',
    '.venv',
    '.vscode',
    '__pycache__',
    'migrations',
    'static',
    'media',
    'node_modules',
    'venv',
    'env',
    'build',
    'dist',
    'AgentReadme',
    #'test_suite', # 保持注释，让 .gitignore 控制是否排除
}

# 要排除的文件名列表 (硬编码, 精确匹配)
HARDCODED_EXCLUDE_FILES = {
    'manage.py',
    'setup.py',
    'conftest.py',
    '__init__.py', # CHANGE: [2024-03-29] 排除 __init__.py 文件 #AFM-XX
    #'settings.py', # 让 .gitignore 控制
    #'urls.py',     # 让 .gitignore 控制
    'wsgi.py',
    'asgi.py',
    #'admin.py',    # 让 .gitignore 控制
    #'apps.py',     # 让 .gitignore 控制
}
# --- 配置结束 ---

# CHANGE: [2024-03-29] 添加函数读取 .gitignore #AFM-XX
def load_gitignore_patterns(root_dir: Path) -> list:
    """读取项目根目录下的 .gitignore 文件并返回模式列表"""
    gitignore_path = root_dir / '.gitignore'
    patterns = []
    if gitignore_path.is_file():
        print(f"  正在读取: {gitignore_path.relative_to(root_dir)}")
        try:
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        patterns.append(line)
        except Exception as e:
            print(f"警告：无法读取或解析 .gitignore 文件: {e}")
    else:
        print("警告：未找到 .gitignore 文件。")
    return patterns

def get_function_signature(node: ast.FunctionDef | ast.AsyncFunctionDef) -> str:
    """尝试从 AST 节点获取函数签名字符串"""
    args = []
    # 位置或关键字参数
    for arg in node.args.posonlyargs + node.args.args:
        arg_str = arg.arg
        if arg.annotation:
            # 简单地获取注解名称，不处理复杂的类型
            try:
                # 尝试处理 Name 和 Attribute 类型
                if isinstance(arg.annotation, ast.Name):
                    arg_str += f": {arg.annotation.id}"
                elif isinstance(arg.annotation, ast.Attribute):
                     # 尝试构建如 'module.Class' 的形式
                    parts = []
                    curr = arg.annotation
                    while isinstance(curr, ast.Attribute):
                        parts.append(curr.attr)
                        curr = curr.value
                    if isinstance(curr, ast.Name):
                        parts.append(curr.id)
                        arg_str += f": {'.'.join(reversed(parts))}"
                    else: # 其他复杂情况，仅用 '...' 表示
                         arg_str += ": ..."
                elif isinstance(arg.annotation, ast.Subscript):
                    # 尝试处理类似 List[str] 的情况
                     value_id = getattr(arg.annotation.value, 'id', '...')
                     slice_id = getattr(arg.annotation.slice, 'id', '...') if isinstance(arg.annotation.slice, ast.Name) else '...'
                     arg_str += f": {value_id}[{slice_id}]"

                elif isinstance(arg.annotation, ast.Constant): # 处理 None, True, False, Ellipsis 等
                     arg_str += f": {arg.annotation.value}"

                else: # 其他注解类型，简化处理
                    arg_str += ": ..."
            except Exception:
                 arg_str += ": ..." # 出错时也用 ...
        args.append(arg_str)

    # *args
    if node.args.vararg:
        arg_str = f"*{node.args.vararg.arg}"
        if node.args.vararg.annotation:
             arg_str += ": ..." # 简化处理
        args.append(arg_str)

    # 关键字参数
    for arg in node.args.kwonlyargs:
        arg_str = arg.arg
        if arg.annotation:
             arg_str += ": ..." # 简化处理
        args.append(f"{arg_str}=...") # 假设有默认值

    # **kwargs
    if node.args.kwarg:
        arg_str = f"**{node.args.kwarg.arg}"
        if node.args.kwarg.annotation:
             arg_str += ": ..." # 简化处理
        args.append(arg_str)

    signature = f"{node.name}({', '.join(args)})"
    if node.returns:
         try:
            if isinstance(node.returns, ast.Name):
                signature += f" -> {node.returns.id}"
            elif isinstance(node.returns, ast.Attribute):
                 parts = []
                 curr = node.returns
                 while isinstance(curr, ast.Attribute):
                     parts.append(curr.attr)
                     curr = curr.value
                 if isinstance(curr, ast.Name):
                     parts.append(curr.id)
                     signature += f" -> {'.'.join(reversed(parts))}"
                 else:
                      signature += " -> ..."
            elif isinstance(node.returns, ast.Subscript):
                 value_id = getattr(node.returns.value, 'id', '...')
                 slice_id = getattr(node.returns.slice, 'id', '...') if isinstance(node.returns.slice, ast.Name) else '...'
                 signature += f" -> {value_id}[{slice_id}]"

            elif isinstance(node.returns, ast.Constant):
                 signature += f" -> {node.returns.value}"

            else:
                signature += " -> ..." # 其他返回类型，简化
         except Exception:
            signature += " -> ..."

    return signature


def analyze_file(filepath: Path, root_dir: Path) -> list:
    """解析单个 Python 文件并提取函数信息"""
    functions = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            tree = ast.parse(content, filename=str(filepath))

            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    docstring = ast.get_docstring(node) or "无文档字符串"
                    signature = get_function_signature(node)
                    relative_path = filepath.relative_to(root_dir)

                    # 检查是否是类方法
                    parent_is_class = False
                    # 简单地向上查找一层父节点看是否是 ClassDef
                    # 注意：这不会处理嵌套函数等复杂情况
                    try:
                        # ast.walk 不直接提供父节点，需要手动查找或使用 NodeVisitor
                        # 为了简单起见，我们这里不做严格的父节点检查
                        # 可以通过函数名约定（如包含 self）或更复杂的 AST 遍历来判断
                        if node.args.args and node.args.args[0].arg in ('self', 'cls'):
                             parent_is_class = True
                    except IndexError:
                        pass # 没有参数的函数

                    func_type = "方法" if parent_is_class else "函数"
                    if isinstance(node, ast.AsyncFunctionDef):
                        func_type = "异步" + func_type

                    functions.append({
                        'name': node.name,
                        'signature': signature,
                        'docstring': docstring.strip(),
                        'lineno': node.lineno,
                        'path': str(relative_path).replace('\\', '/'), # 统一路径分隔符
                        'type': func_type
                    })
    except SyntaxError as e:
        print(f"警告：无法解析文件 {filepath} (语法错误): {e}")
    except Exception as e:
        print(f"警告：处理文件 {filepath} 时出错: {e}")
    return functions

def main():
    parser = argparse.ArgumentParser(description="生成项目 Python 函数汇总文档。")
    parser.add_argument(
        "-d", "--directory",
        default=DEFAULT_ROOT_DIR,
        help=f"要扫描的项目根目录 (默认: '{DEFAULT_ROOT_DIR}')"
    )
    parser.add_argument(
        "-o", "--output",
        default=DEFAULT_OUTPUT_FILE,
        help=f"输出 Markdown 文件路径 (默认: '{DEFAULT_OUTPUT_FILE}')"
    )
    args = parser.parse_args()

    root_dir = Path(args.directory).resolve()
    output_file = Path(args.output).resolve()

    print(f"开始扫描目录: {root_dir}")
    print(f"将输出到: {output_file}")

    # CHANGE: [2024-03-29] 加载 .gitignore 模式 #AFM-XX
    gitignore_patterns = load_gitignore_patterns(root_dir)
    print(f"硬编码排除目录: {HARDCODED_EXCLUDE_DIRS}")
    print(f"硬编码排除文件: {HARDCODED_EXCLUDE_FILES}")
    if gitignore_patterns:
        print(f".gitignore 模式: {gitignore_patterns}")

    all_functions = []
    for current_path, dirnames, filenames in os.walk(root_dir, topdown=True):
        current_path_obj = Path(current_path)
        relative_current_path = current_path_obj.relative_to(root_dir)

        # --- 排除目录 --- CHANGE: [2024-03-29] #AFM-XX
        original_dirnames = list(dirnames) # 复制一份用于迭代，因为我们要修改 dirnames[:]
        dirnames[:] = [] # 清空原列表，重新填充需要保留的
        for d in original_dirnames:
            # 1. 检查硬编码排除列表
            if d in HARDCODED_EXCLUDE_DIRS:
                continue
            # 2. 检查 .gitignore 模式 (检查目录本身和带斜杠的路径)
            d_path_str = (relative_current_path / d).as_posix() # 使用 posix 路径分隔符
            is_gitignored = False
            for pattern in gitignore_patterns:
                # 简单的检查：匹配目录名，或匹配以 '/' 结尾的模式
                if fnmatch.fnmatch(d, pattern) or fnmatch.fnmatch(d_path_str + '/', pattern) or fnmatch.fnmatch(d_path_str, pattern): # 添加不带斜杠的路径匹配
                    is_gitignored = True
                    break
            if not is_gitignored:
                dirnames.append(d)
            # else:
            #     print(f"    跳过目录 (gitignore): {d_path_str}") # Debugging
        # --- --- --- --- ---

        for filename in filenames:
            filepath = current_path_obj / filename
            relative_filepath = filepath.relative_to(root_dir)
            relative_filepath_str = relative_filepath.as_posix()

            # --- 排除文件 --- CHANGE: [2024-03-29] #AFM-XX
            # 1. 检查硬编码排除列表
            if filename in HARDCODED_EXCLUDE_FILES:
                continue
            # 2. 检查 .gitignore 模式
            is_gitignored = False
            for pattern in gitignore_patterns:
                if fnmatch.fnmatch(filename, pattern) or fnmatch.fnmatch(relative_filepath_str, pattern):
                    is_gitignored = True
                    break
            if is_gitignored:
                # print(f"    跳过文件 (gitignore): {relative_filepath_str}") # Debugging
                continue
            # --- --- --- --- ---

            if filename.endswith('.py'):
                print(f"  正在分析: {relative_filepath}")
                file_functions = analyze_file(filepath, root_dir)
                if file_functions:
                    all_functions.extend(file_functions)

    # 按文件路径和行号排序
    all_functions.sort(key=lambda x: (x['path'], x['lineno']))

    # 生成 Markdown 内容
    markdown_content = ["# 项目函数汇总\n"]
    current_file = None
    for func_info in all_functions:
        if func_info['path'] != current_file:
            current_file = func_info['path']
            markdown_content.append(f"\n## `{current_file}`\n")

        markdown_content.append(f"*   **`{func_info['signature']}`** ({func_info['type']}, 行: {func_info['lineno']})")
        # 将文档字符串放入代码块以便保留格式
        markdown_content.append(f"    ```\n    {func_info['docstring']}\n    ```")

    # 确保输出目录存在
    output_file.parent.mkdir(parents=True, exist_ok=True)

    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        print(f"\n成功生成汇总文件: {output_file}")
    except Exception as e:
        print(f"\n错误：无法写入文件 {output_file}: {e}")

if __name__ == "__main__":
    main()