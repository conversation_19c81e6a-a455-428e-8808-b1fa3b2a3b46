// 获取更改单数据
export async function fetchChangeOrder(id: string) {
  try {
    // 实际应用中，这里会从API获取更改单数据
    // const response = await fetch(`/api/change-orders/${id}`);
    // if (!response.ok) {
    //   throw { status: response.status, message: 'Failed to fetch change order' };
    // }
    // return await response.json();

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 模拟数据 - 实际应用中应从API获取
    // 如果ID不符合预期格式，模拟404错误
    if (!id.match(/^CO-\d{4}-\d{4}$/) && !id.startsWith("temp-")) {
      throw { status: 404, message: "Change order not found" }
    }

    return {
      id: id,
      type: "field_correction",
      reason: '项目名称拼写错误，原名称"XX项目环评"更正为"XX工程环境影响评价报告"',
      details: '根据项目批复文件，正确的项目名称应为"XX工程环境影响评价报告"，原档案记录中的名称有误。',
      documentNumber: "XYZ-2023-001-DOC",
      status: "draft", // draft, locked, confirmed, archived
      createdAt: "2023-12-20",
      createdBy: "李四",
      lockedAt: null,
      lockedBy: null,
      confirmedAt: null,
      confirmedBy: null,
      archivedAt: null,
      archivedBy: null,
      records: [
        {
          id: "AR-2023-0001",
          unifiedNumber: "XYZ-2023-001",
          title: "项目环评报告",
          projectName: "XYZ工业园区扩建项目",
          status: "archived",
          fields: [
            { name: "title", label: "标题", value: "项目环评报告" },
            { name: "projectName", label: "项目名称", value: "XYZ工业园区扩建项目" },
            { name: "projectType", label: "项目类型", value: "环境影响评价" },
            { name: "department", label: "部门", value: "环评部" },
            { name: "author", label: "编制人", value: "李四" },
            { name: "reviewedBy", label: "审核人", value: "王五" },
            { name: "approvedBy", label: "批准人", value: "张三" },
            { name: "approvalDate", label: "批准日期", value: "2023-05-12" },
            { name: "expiryDate", label: "有效期至", value: "2028-05-12" },
            { name: "confidentialLevel", label: "保密级别", value: "普通" },
            { name: "remarks", label: "备注", value: "按时完成，无特殊情况" },
          ],
        },
        // 其他记录...
      ],
      changes: [
        {
          recordId: "AR-2023-0001",
          field: "title",
          fieldLabel: "标题",
          oldValue: "项目环评报告",
          newValue: "XX工程环境影响评价报告",
        },
        // 其他更改...
      ],
    }
  } catch (error) {
    console.error("Error fetching change order:", error)
    throw error // 重新抛出错误，让调用者处理
  }
}

// 用于存储临时ID到永久ID的映射
const tempToPermanentIdMap = new Map<string, string>()

// 添加一个方法来获取永久ID
export const getPermanentId = (tempId: string): string | null => {
  return tempToPermanentIdMap.get(tempId) || null
}

// 保存更改单为草稿
export const saveChangeOrderAsDraft = async (data: any): Promise<any> => {
  console.log("服务: 保存更改单为草稿", data)

  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // 生成一个永久ID
  const permanentId = `CO-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`

  console.log("服务: 生成永久ID", permanentId)

  // 如果有临时ID，存储映射关系
  if (data.originalId) {
    console.log("服务: 存储ID映射", data.originalId, "->", permanentId)
    tempToPermanentIdMap.set(data.originalId, permanentId)
  }

  // 返回带有永久ID的结果
  return {
    ...data,
    id: permanentId,
    status: "draft",
  }
}

// 更新现有更改单
export const updateChangeOrder = async (id: string, changeOrderData: any) => {
  try {
    // 实际应用中，这里会调用API更新更改单
    // const response = await fetch(`/api/change-orders/${id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(changeOrderData)
    // });
    // if (!response.ok) throw new Error('Failed to update change order');
    // return await response.json();

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 800))

    return {
      id: id,
      status: changeOrderData.status,
      message: "更改单已更新",
    }
  } catch (error) {
    console.error("Failed to update change order:", error)
    throw error
  }
}
