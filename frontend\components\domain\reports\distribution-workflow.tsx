"use client"

import { Card, CardContent } from "@/components/ui/card"
import { FileText, FileLock, CheckCircle, Archive, Upload } from "lucide-react"

export function DistributionWorkflow() {
  return (
    <Card>
      <CardContent className="pt-6">
        <h2 className="text-2xl font-bold mb-8">发放单工作流程</h2>

        <div className="flex items-center justify-between mb-8">
          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-full p-4 mb-2">
              <FileText className="h-6 w-6 text-gray-600" />
            </div>
            <span className="text-sm font-medium">创建/草稿</span>
          </div>

          <div className="h-px bg-gray-200 flex-1 mx-2"></div>

          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-full p-4 mb-2">
              <FileLock className="h-6 w-6 text-gray-600" />
            </div>
            <span className="text-sm font-medium">锁定</span>
          </div>

          <div className="h-px bg-gray-200 flex-1 mx-2"></div>

          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-full p-4 mb-2">
              <CheckCircle className="h-6 w-6 text-gray-600" />
            </div>
            <span className="text-sm font-medium">确认</span>
          </div>

          <div className="h-px bg-gray-200 flex-1 mx-2"></div>

          <div className="flex flex-col items-center">
            <div className="bg-gray-100 rounded-full p-4 mb-2">
              <Archive className="h-6 w-6 text-gray-600" />
            </div>
            <span className="text-sm font-medium">归档</span>
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-md">
          <div className="flex items-center text-blue-600 font-medium mb-2">
            <Upload className="h-5 w-5 mr-2" />
            确认单上传
          </div>
          <p className="text-sm text-blue-700">
            确认单扫描件可以在确认阶段或归档阶段上传。上传确认单不是必须的，但建议上传以便于后续查阅和管理。
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
