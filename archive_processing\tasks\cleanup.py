import logging
import os
from celery import shared_task
from django.utils import timezone
from datetime import timedelta

from ..models import ProcessingTask, UploadedFile
from .core_tasks import dispatch_pdf_processing

logger = logging.getLogger(__name__)


@shared_task(name="archive_processing.periodic_cleanup_deleted_files")
def periodic_cleanup_deleted_files():
    """
    一个周期性任务，用于扫描并物理删除所有标记为'deleted'的UploadedFile对应的文件。
    这是一个健壮的"垃圾回收"机制，确保因任何原因未能即时清理的文件最终都会被处理。
    """
    # 1. 查找所有逻辑删除的文件记录
    # 为了避免在循环中操作同一个查询集，一次性将ID加载到内存中
    files_to_cleanup_qs = UploadedFile.objects.filter(status='deleted')
    file_ids_to_process = list(files_to_cleanup_qs.values_list('file_id', flat=True))

    if not file_ids_to_process:
        logger.info("没有找到标记为'deleted'的文件，本次清理任务结束。")
        return

    logger.info(f"发现 {len(file_ids_to_process)} 个待清理的物理文件。")
    
    # 2. 遍历并清理
    for file_id in file_ids_to_process:
        try:
            # 在循环内部重新获取对象，保证操作的是最新的数据
            file_record = UploadedFile.objects.get(file_id=file_id)

            # 双重保险：再次确认状态
            if file_record.status != 'deleted':
                logger.warning(f"文件 {file_record.file_id} 的状态已不再是 'deleted'，跳过清理。")
                continue

            # 检查并删除物理文件
            if file_record.file and os.path.exists(file_record.file.path):
                logger.info(f"开始物理删除文件: {file_record.file.path} (ID: {file_record.file_id})")
                os.remove(file_record.file.path)
                logger.info(f"成功物理删除文件: {file_record.file.path}")

            # 3. 更新数据库记录状态为'purged'，避免重复扫描并保留审计日志
            file_record.status = 'purged'
            file_record.save(update_fields=['status'])
            logger.info(f"文件记录 {file_record.file_id} 的状态已更新为 'purged'。")

        except UploadedFile.DoesNotExist:
            # 可能在任务执行期间，记录已被其他进程删除
            logger.warning(f"尝试清理时，文件记录 {file_id} 已不存在。")
        except Exception as e:
            # 即使单个文件处理失败，也不应中断整个循环
            logger.exception(f"清理文件 {file_id} 时发生意外错误，将跳过此文件。")

    logger.info("周期性文件清理任务执行完毕。")


@shared_task(name="archive_processing.cleanup_stuck_tasks")
def cleanup_stuck_tasks():
    """
    一个周期性任务，用于清理和恢复因意外中断而卡住的任务。
    """
    logger.info("开始执行周期性任务：清理卡住的任务...")

    # 1. 处理卡在 'pending' 状态的任务 (重新入队)
    # 这些任务可能在入队前服务就重启了
    PENDING_TIMEOUT = timedelta(minutes=5)  # 由原10分钟改为5分钟
    stuck_pending_tasks = ProcessingTask.objects.filter(
        status='pending',
        created_at__lt=timezone.now() - PENDING_TIMEOUT
    )

    if stuck_pending_tasks.exists():
        logger.warning(f"发现 {stuck_pending_tasks.count()} 个卡在 'pending' 状态的任务，正在尝试重新入队...")
        for task in stuck_pending_tasks:
            logger.info(f"重新入队任务: {task.task_id}")
            try:
                # 遵循"先更新状态，后入队"的原则
                task.status = 'queued'
                task.save(update_fields=['status', 'updated_at'])
                dispatch_pdf_processing(task_id=task.task_id)  # 重新调用分发函数
            except Exception as e:
                logger.error(f"任务 {task.task_id} 自动重入队失败: {e}", exc_info=True)
                task.status = 'failed'
                task.error_message = f"系统自动重入队失败: {e}"
                task.save(update_fields=['status', 'error_message', 'updated_at'])

    # 2. 处理卡在 'processing' 状态的任务 (标记为失败)
    # 这些任务的Worker可能已经崩溃
    PROCESSING_TIMEOUT = timedelta(minutes=5)  # 由原1小时改为5分钟
    stale_processing_tasks = ProcessingTask.objects.filter(
        status='processing',
        updated_at__lt=timezone.now() - PROCESSING_TIMEOUT
    )

    if stale_processing_tasks.exists():
        logger.warning(f"发现 {stale_processing_tasks.count()} 个过期的 'processing' 状态任务，正在标记为失败...")
        for task in stale_processing_tasks:
            logger.warning(f"任务 {task.task_id} 因处理超时而被标记为失败。")
            task.status = 'failed'
            task.error_message = '任务因处理超时或服务重启而被系统自动标记为失败。'
            task.save(update_fields=['status', 'error_message', 'updated_at'])

    logger.info("清理卡住的任务执行完毕。") 