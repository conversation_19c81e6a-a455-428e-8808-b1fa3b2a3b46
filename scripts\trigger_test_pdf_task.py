#!/usr/bin/env python3
"""
触发测试PDF任务

这个脚本用于手动触发一个简单的PDF任务，验证PDF worker是否能正确接收和处理任务。
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_flow_manager.celery import app

def create_test_task():
    """创建一个测试任务来验证PDF worker"""
    print("=" * 60)
    print("PDF Worker 测试任务")
    print("=" * 60)
    
    try:
        # 导入PDF处理任务
        from archive_processing.tasks.core_tasks import process_pdf_serial_task
        print("✅ 成功导入 process_pdf_serial_task")
        
        # 检查任务路由
        task_name = process_pdf_serial_task.name
        route = app.conf.task_routes.get(task_name)
        if route:
            queue = route.get('queue', 'default')
            print(f"✅ 任务将路由到队列: {queue}")
        else:
            print("⚠️ 未找到路由配置")
            return
        
        # 创建一个测试任务ID（模拟）
        test_task_id = "test-task-123"
        print(f"🚀 准备触发测试任务，ID: {test_task_id}")
        
        # 异步调用任务
        print("📤 发送任务到队列...")
        result = process_pdf_serial_task.delay(test_task_id)
        
        print(f"✅ 任务已发送到队列")
        print(f"   Celery任务ID: {result.id}")
        print(f"   初始状态: {result.status}")
        
        # 等待一段时间查看任务状态变化
        print("\n⏳ 监控任务状态变化...")
        for i in range(10):  # 监控10秒
            time.sleep(1)
            status = result.status
            print(f"   {i+1}秒后状态: {status}")
            
            if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                break
        
        # 获取最终结果
        print(f"\n📊 最终状态: {result.status}")
        
        if result.status == 'SUCCESS':
            print("✅ 任务执行成功！PDF worker正常工作")
            try:
                result_data = result.get(timeout=1)
                print(f"📋 任务结果: {result_data}")
            except Exception as e:
                print(f"⚠️ 获取结果失败: {e}")
        elif result.status == 'FAILURE':
            print("❌ 任务执行失败")
            try:
                error_info = result.get(propagate=False)
                print(f"❌ 错误信息: {error_info}")
            except Exception as e:
                print(f"⚠️ 获取错误信息失败: {e}")
        elif result.status == 'PENDING':
            print("⏳ 任务仍在等待处理")
            print("💡 这可能意味着:")
            print("   1. PDF worker正在处理任务")
            print("   2. 任务在队列中等待")
            print("   3. 数据库连接问题导致任务无法完成")
        else:
            print(f"ℹ️ 任务状态: {result.status}")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 任务触发失败: {e}")
        import traceback
        traceback.print_exc()

def check_worker_activity():
    """检查worker活动"""
    print("\n🔍 检查Worker活动:")
    print("-" * 40)
    
    try:
        inspect = app.control.inspect()
        
        # 检查活跃任务
        active_tasks = inspect.active()
        if active_tasks:
            print("🔄 当前活跃任务:")
            for worker, tasks in active_tasks.items():
                print(f"  {worker}: {len(tasks)} 个任务")
                for task in tasks:
                    print(f"    - {task.get('name', 'Unknown')} ({task.get('id', 'No ID')[:8]}...)")
        else:
            print("ℹ️ 当前没有活跃任务")
        
        # 检查预留任务
        reserved_tasks = inspect.reserved()
        if reserved_tasks:
            print("\n📋 预留任务:")
            for worker, tasks in reserved_tasks.items():
                print(f"  {worker}: {len(tasks)} 个任务")
        else:
            print("\nℹ️ 当前没有预留任务")
        
        # 检查worker统计
        stats = inspect.stats()
        if stats:
            print("\n📊 Worker统计:")
            for worker, stat in stats.items():
                if stat:
                    total_tasks = stat.get('total', {})
                    if total_tasks:
                        print(f"  {worker}:")
                        for task_name, count in total_tasks.items():
                            if count > 0:
                                print(f"    - {task_name}: {count} 次")
                    else:
                        print(f"  {worker}: 尚未处理任何任务")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始PDF Worker测试...\n")
    
    # 检查当前worker活动
    check_worker_activity()
    
    # 创建测试任务
    create_test_task()
    
    # 再次检查worker活动
    print("\n" + "=" * 60)
    print("测试后Worker状态")
    print("=" * 60)
    check_worker_activity()
    
    print("\n💡 测试完成！")
    print("如果看到任务被成功发送到队列，说明路由配置正确。")
    print("如果任务状态变为SUCCESS，说明PDF worker正常工作。")
    print("如果任务一直是PENDING，可能需要检查:")
    print("1. PDF worker是否正在运行")
    print("2. 数据库连接是否正常")
    print("3. 任务参数是否有效")

if __name__ == '__main__':
    main()
