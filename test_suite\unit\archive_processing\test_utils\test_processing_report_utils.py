# test_suite/unit/archive_processing/test_utils/test_processing_report_utils.py
import os
import pytest
from unittest.mock import MagicMock, patch

# 模拟依赖的 DTO 和函数
# (根据实际需要调整模拟对象的属性和返回值)
# 假设 DTO 定义在 archive_processing.dto 中
try:
    from archive_processing.dto.processing_dtos import ProcessingResultDto, ProcessingStatsDto
    # 如果 DTO 很复杂或难以实例化，可以继续使用下面的 Mock 类
    class MockProcessingStatsDto: # 保留 Mock 作为备选或简化测试
        def __init__(self, time_val=10.5, pages=20):
            self.processing_time = time_val
            self.total_pages = pages
            self.pages_per_second = pages / time_val if time_val else 0

    class MockProcessingResultDto: # 保留 Mock 作为备选或简化测试
        def __init__(self, success=True, error=None, stats=None, numbers=None, rec_stats=None):
            self.success = success
            self.error_message = error
            self.stats = stats if stats else MockProcessingStatsDto()
            # 假设统一编号信息存储在字典中
            self.unified_numbers = numbers if numbers else {0: 'NUM-001', 10: 'NUM-002'}
            # 假设识别统计信息存储在字典中
            self.recognition_stats = rec_stats if rec_stats else {'MethodA': 2, 'MethodB': 1}

except ImportError:
    # 如果无法导入真实 DTO，定义 Mock 类
    class MockProcessingStatsDto:
        def __init__(self, time_val=10.5, pages=20):
            self.processing_time = time_val
            self.total_pages = pages
            self.pages_per_second = pages / time_val if time_val else 0

    class MockProcessingResultDto:
        def __init__(self, success=True, error=None, stats=None, numbers=None, rec_stats=None):
            self.success = success
            self.error_message = error
            self.stats = stats if stats else MockProcessingStatsDto()
            self.unified_numbers = numbers if numbers else {0: 'NUM-001', 10: 'NUM-002'}
            self.recognition_stats = rec_stats if rec_stats else {'MethodA': 2, 'MethodB': 1}

# 被测函数路径
from archive_processing.utils.processing_report_utils import create_result_summary
# 假设 system_utils 可以导入
try:
    from archive_processing.utils.system_utils import format_time
except ImportError:
    # Mock format_time if needed
    def format_time(seconds):
        return f"{seconds:.2f} 秒"

# 确保 PyMuPDF (fitz) 被 Mock (如果它不是测试依赖项)
try:
    import fitz  # PyMuPDF
except ImportError:
    # 如果 PyMuPDF 不是测试依赖，创建一个 Mock 类
    fitz = MagicMock()
    # 配置 mock 文档实例的行为
    mock_doc_instance = MagicMock()
    mock_doc_instance.page_count = 5  # 默认模拟页数
    # 默认的 close 方法
    mock_doc_instance.close = MagicMock()
    # fitz.open() 返回已配置的 mock 文档实例
    fitz.open.return_value = mock_doc_instance
    
@pytest.fixture
def mock_result_dto():
    """提供一个模拟的成功 ProcessingResultDto 对象"""
    # 可以选择使用导入的真实 DTO 或 Mock DTO
    # return ProcessingResultDto(...)
    return MockProcessingResultDto() # 当前使用 Mock

@pytest.fixture
def mock_archived_files():
    """提供一个模拟的成功归档文件列表"""
    return [
        ('/fake/archive/path/NUM-001.pdf', 'NUM-001'),
        ('/fake/archive/path/NUM-002.pdf', 'NUM-002')
    ]

@pytest.fixture
def mock_status_update():
    """提供一个模拟的成功状态更新字典"""
    return {
        'total': 2,
        'updated': 2,
        'not_found': 0,
        'errors': 0,
        'not_found_numbers': [],
        'error_details': []
    }


class TestCreateResultSummary:

    # 使用 mocker fixture 代替 @patch 装饰器
    def test_summary_success_scenario(self, mocker, tmp_path, mock_result_dto, mock_archived_files, mock_status_update):
        """
        测试成功场景下生成摘要文件
        """
        # 使用 mocker 在函数内部进行 patching
        mock_exists = mocker.patch('archive_processing.utils.processing_report_utils.os.path.exists')
        mock_getsize = mocker.patch('archive_processing.utils.processing_report_utils.os.path.getsize')
        # 模拟 fitz 库的 open 方法
        mock_fitz_open = mocker.patch('archive_processing.utils.processing_report_utils.fitz.open')
        mock_format_time = mocker.patch('archive_processing.utils.processing_report_utils.format_time', side_effect=lambda s: f"{s:.1f}s")

        # 配置 Mock 返回值
        mock_exists.return_value = True # 模拟归档文件都存在
        mock_getsize.return_value = 1024 * 50 # 模拟文件大小 50KB

        # 配置 fitz.open() mock 返回的文档实例行为
        # 需要从 unittest.mock 导入 MagicMock
        from unittest.mock import MagicMock 
        mock_doc_instance = MagicMock()
        mock_doc_instance.page_count = 5  # 每个文件5页
        mock_doc_instance.close = MagicMock()  # 模拟 close 方法
        mock_fitz_open.return_value = mock_doc_instance  # 当 fitz.open() 被调用时返回 mock 文档实例

        # --- 后续测试逻辑保持不变 ---
        input_pdf = "/fake/input/mydoc.pdf"
        output_dir = tmp_path / "summary_output"

        # 设置自定义统一编号，确保与断言一致
        custom_numbers = {5: "SIMPLE-TEST-NUM"}
        mock_result_dto.unified_numbers = custom_numbers

        # 调用被测函数
        summary_path = create_result_summary(
            input_pdf_path=input_pdf,
            result_dto=mock_result_dto,
            archived_files=mock_archived_files,
            output_dir=str(output_dir), # tmp_path 需要转为字符串
            status_update=mock_status_update,
            pre_check_errors=None
        )

        # 断言
        assert summary_path is not None
        assert summary_path.startswith(str(output_dir))
        assert summary_path.endswith(".txt")

        try:
            with open(summary_path, "r", encoding="utf-8") as f:
                content = f.read()
                print(f"文件内容: {content}")  # 调试输出，查看实际内容
        except FileNotFoundError:
            pytest.fail(f"Summary file not found at {summary_path}.")

        # 基本信息断言
        assert "=== PDF分割处理摘要 ===" in content
        assert f"原始文件: mydoc.pdf" in content
        assert "处理结果: 成功" in content
        assert f"总页数: {mock_result_dto.stats.total_pages}" in content
        assert "处理时间: 10.5s" in content 
        assert mock_format_time.call_count > 0 
        
        # 分割和归档结果断言
        assert "--- 分割与归档结果 ---" in content
        assert mock_fitz_open.call_count == len(mock_archived_files)
        assert mock_getsize.call_count == len(mock_archived_files)
        assert "文件 1: NUM-001.pdf (5 页, 50.0 KB) -> 编号: NUM-001" in content
        assert "文件 2: NUM-002.pdf (5 页, 50.0 KB) -> 编号: NUM-002" in content
        
        # 检查 close() 方法是否被调用
        assert mock_doc_instance.close.call_count == len(mock_archived_files)
        
        # 统一编号部分断言 - 修复断言格式，匹配实际输出中带换行符的格式
        assert "\n--- 识别到的统一编号 (页码 -> 编号) ---" in content
        # 页码显示是从1开始的，而索引是从0开始的，所以索引5对应第6页
        assert f"第6页: SIMPLE-TEST-NUM" in content
        
        # 识别方法统计断言
        assert "--- 识别方法效果统计 ---" in content
        assert "MethodA: 2" in content
        
        # 档案状态更新断言
        assert "--- 档案状态更新结果 ---" in content
        assert f"总计尝试更新/检查的统一编号: {mock_status_update['total']}" in content
        assert f"更新成功: {mock_status_update['updated']}" in content
        assert f"数据库中未找到记录: {mock_status_update['not_found']}" in content
        assert "未找到的统一编号列表:" not in content
        # 确保预检查失败的内容不存在于成功测试中
        assert "*** ⚠️ 预检查失败" not in content

    def test_summary_precheck_failure(self, mocker, tmp_path, mock_result_dto):
        """测试预检查失败场景下生成摘要文件"""
        # Mock 依赖
        mock_exists = mocker.patch('archive_processing.utils.processing_report_utils.os.path.exists')
        mock_exists.return_value = True
        mock_format_time = mocker.patch('archive_processing.utils.processing_report_utils.format_time', 
                                       side_effect=lambda s: f"{s:.1f}s")
        
        input_pdf = "/fake/input/mydoc.pdf"
        output_dir = str(tmp_path / "summary_output")
        
        # 准备预检查错误数据
        pre_check_errors = {
            'parts_missing_number': ['Part 1 (pages 1-5)', 'Part 3 (pages 10-15)'],
            'numbers_missing_record': ['TEST-NUM-123', 'TEST-NUM-456']
        }
        
        # 调用被测函数
        summary_path = create_result_summary(
            input_pdf_path=input_pdf,
            result_dto=mock_result_dto,
            archived_files=[],  # 预检查失败时不会有归档文件
            output_dir=output_dir,
            pre_check_errors=pre_check_errors
        )
        
        # 断言结果
        assert summary_path is not None
        
        try:
            with open(summary_path, "r", encoding="utf-8") as f:
                content = f.read()
                print(f"预检查失败内容: {content}")  # 调试输出
        except FileNotFoundError:
            pytest.fail(f"Summary file not found at {summary_path}.")
        
        # 预检查失败断言
        assert "*** ⚠️ 预检查失败，处理已中止 ***" in content
        assert "以下部分未能识别统一编号:" in content
        assert "Part 1 (pages 1-5)" in content
        assert "以下统一编号在数据库中未找到对应记录:" in content
        assert "TEST-NUM-123" in content
        assert "(由于预检查失败，未执行分割与归档操作)" in content
        assert "(由于预检查失败，未执行状态更新操作)" in content

