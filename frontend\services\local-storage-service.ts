// Base localStorage service - 保留这部分作为通用的本地存储服务
export const localStorageService = {
  getItem: (key: string): string | null => {
    if (typeof window === "undefined") return null
    return localStorage.getItem(key)
  },

  setItem: (key: string, value: string): void => {
    if (typeof window === "undefined") return
    localStorage.setItem(key, value)
  },

  removeItem: (key: string): void => {
    if (typeof window === "undefined") return
    localStorage.removeItem(key)
  },
}

// 移除了特定于更改单的函数:
// - saveTemporaryChangeOrder
// - getTemporaryChangeOrder
// - removeTemporaryChangeOrder
