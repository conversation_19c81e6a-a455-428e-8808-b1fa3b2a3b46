# Generated by Django 5.1.8 on 2025-05-16 12:33

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ArchiveRecord',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('sample_number', models.CharField(max_length=2000, verbose_name='样品编号')),
                ('account_from_excel', models.CharField(blank=True, max_length=2000, null=True, verbose_name='账号')),
                ('commission_number', models.CharField(max_length=2000, unique=True, verbose_name='委托编号')),
                ('unified_number', models.Char<PERSON>ield(blank=True, help_text='自动从委托编号复制', max_length=2000, null=True, verbose_name='统一编号')),
                ('report_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='报告编号')),
                ('province_unified_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='省统一报告编号')),
                ('station_code', models.CharField(blank=True, max_length=2000, null=True, verbose_name='站点编号')),
                ('organization_code', models.CharField(blank=True, max_length=2000, null=True, verbose_name='机构代号')),
                ('project_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='工程编号')),
                ('project_name', models.CharField(max_length=2000, verbose_name='工程名称')),
                ('sub_project', models.CharField(blank=True, max_length=2000, null=True, verbose_name='分项工程')),
                ('project_location', models.CharField(blank=True, max_length=2000, null=True, verbose_name='工程部位')),
                ('project_address', models.CharField(blank=True, max_length=2000, null=True, verbose_name='工程地址')),
                ('client_unit', models.CharField(max_length=2000, verbose_name='委托单位')),
                ('client_name', models.CharField(blank=True, max_length=2000, null=True, verbose_name='委托人')),
                ('commission_datetime', models.DateTimeField(null=True, verbose_name='委托日期')),
                ('test_start_datetime', models.DateTimeField(blank=True, null=True, verbose_name='试验开始日期')),
                ('test_end_datetime', models.DateTimeField(blank=True, null=True, verbose_name='试验结束日期')),
                ('test_person1', models.CharField(blank=True, max_length=2000, null=True, verbose_name='试验人1')),
                ('test_person2', models.CharField(blank=True, max_length=2000, null=True, verbose_name='试验人2')),
                ('data_entry_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='数据录入人')),
                ('test_result', models.TextField(blank=True, null=True, verbose_name='检测结果')),
                ('conclusion', models.TextField(blank=True, null=True, verbose_name='结论')),
                ('test_parameters', models.TextField(blank=True, null=True, verbose_name='检测参数')),
                ('unqualified_parameters', models.TextField(blank=True, null=True, verbose_name='不合格参数')),
                ('archive_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='报告归档状态')),
                ('change_count', models.IntegerField(default=0, verbose_name='更改次数')),
                ('current_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='当前数据状态')),
                ('processing_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='待处理状态')),
                ('archive_box_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='档案盒编号')),
                ('archive_url', models.URLField(blank=True, null=True, verbose_name='档案URL链接')),
                ('attachments_from_excel', models.TextField(blank=True, null=True, verbose_name='附件')),
                ('storage_datetime', models.DateTimeField(blank=True, help_text='系统记录的入库日期时间，包含Excel导入的日期', null=True, verbose_name='入库日期')),
                ('storage_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='入库人')),
                ('outbound_datetime', models.DateTimeField(blank=True, help_text='系统记录的出库日期时间', null=True, verbose_name='出库日期')),
                ('outbound_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='出库人')),
                ('archive_datetime', models.DateTimeField(blank=True, help_text='系统记录的归档日期时间', null=True, verbose_name='归档日期')),
                ('archive_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='归档人')),
                ('report_issue_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='报告发放状态')),
                ('first_issue_copies', models.PositiveIntegerField(default=0, verbose_name='第一次发放份数')),
                ('first_issue_datetime', models.DateTimeField(blank=True, null=True, verbose_name='第一次发放日期')),
                ('first_issue_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第一次发放人')),
                ('first_receiver_name', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第一次领取人')),
                ('first_receiver_unit', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第一次领取单位')),
                ('first_receiver_phone', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第一次领取人电话')),
                ('second_issue_copies', models.PositiveIntegerField(default=0, verbose_name='第二次发放份数')),
                ('second_issue_datetime', models.DateTimeField(blank=True, null=True, verbose_name='第二次发放日期')),
                ('second_issue_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第二次发放人')),
                ('second_receiver_name', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第二次领取人')),
                ('second_receiver_unit', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第二次领取单位')),
                ('second_receiver_phone', models.CharField(blank=True, max_length=2000, null=True, verbose_name='第二次领取人电话')),
                ('total_issue_copies', models.PositiveIntegerField(default=0, verbose_name='总发放份数')),
                ('group_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='组号')),
                ('sample_name', models.CharField(blank=True, max_length=2000, null=True, verbose_name='样品/项目名称')),
                ('assigned_person', models.CharField(blank=True, max_length=2000, null=True, verbose_name='分配人')),
                ('component_count', models.IntegerField(blank=True, null=True, verbose_name='构件(桩)数')),
                ('test_point_count', models.IntegerField(blank=True, null=True, verbose_name='测点数')),
                ('unqualified_point_count', models.IntegerField(blank=True, null=True, verbose_name='不合格点数')),
                ('sample_retention_datetime', models.DateTimeField(blank=True, null=True, verbose_name='样品留样时间')),
                ('sample_remaining_time', models.IntegerField(blank=True, null=True, verbose_name='样品剩余时间(天)')),
                ('payment_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='收费状态')),
                ('price_adjustment_status', models.CharField(blank=True, max_length=2000, null=True, verbose_name='价格调整状态')),
                ('standard_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='标准价格费用')),
                ('discount_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='折扣价格费用')),
                ('actual_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='实际价格费用')),
                ('import_date', models.DateTimeField(auto_now_add=True, verbose_name='导入时间')),
                ('batch_number', models.CharField(blank=True, max_length=2000, null=True, verbose_name='导入批次号')),
                ('source_system', models.CharField(default='excel_import', max_length=2000, verbose_name='数据来源系统')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('import_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='imported_records', to=settings.AUTH_USER_MODEL, verbose_name='导入人')),
            ],
            options={
                'verbose_name': '档案记录',
                'verbose_name_plural': '档案记录',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='ChangeLogBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('change_source', models.CharField(choices=[('excel_import', 'Excel导入'), ('manual_edit', '手动编辑'), ('api_update', 'API更新'), ('system', '系统操作'), ('batch_operation', '批量操作'), ('manual_rollback', '手动回滚')], default='excel_import', max_length=50, verbose_name='变更来源')),
                ('change_reason', models.TextField(blank=True, verbose_name='变更原因')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='变更时间')),
                ('affected_records_count', models.IntegerField(default=0, verbose_name='受影响记录数')),
                ('summary', models.JSONField(default=dict, help_text='变更摘要统计', verbose_name='变更摘要')),
                ('client_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='客户端IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='变更用户')),
            ],
            options={
                'verbose_name': '变更批次',
                'verbose_name_plural': '变更批次',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='ChangeOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='更改单编号')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('cancelled', '已取消'), ('deleted', '已删除')], default='draft', max_length=20, verbose_name='状态')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('change_reason', models.TextField(verbose_name='变更原因')),
                ('execution_time', models.DateTimeField(blank=True, null=True, verbose_name='实际执行时间')),
                ('records_count', models.PositiveIntegerField(default=0, verbose_name='关联记录数')),
                ('changes_count', models.PositiveIntegerField(default=0, verbose_name='变更字段总数')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='archives/change_orders/%Y/%m/', verbose_name='附件')),
                ('attachment_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='附件名称')),
                ('attachment_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='附件类型')),
                ('is_rollback_source', models.BooleanField(default=False, verbose_name='是否已被回滚')),
                ('change_batch', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='change_order', to='archive_records.changelogbatch', verbose_name='关联变更批次')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_change_orders', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('executed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='executed_change_orders', to=settings.AUTH_USER_MODEL, verbose_name='执行人')),
                ('rollback_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='source_orders', to='archive_records.changeorder', verbose_name='回滚更改单')),
            ],
            options={
                'verbose_name': '档案更改单',
                'verbose_name_plural': '档案更改单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChangeOrderAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='archives/change_orders/attachments/%Y/%m/', verbose_name='附件文件')),
                ('filename', models.CharField(max_length=255, verbose_name='文件名')),
                ('file_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='文件类型')),
                ('file_size', models.PositiveIntegerField(default=0, verbose_name='文件大小(字节)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='文件描述')),
                ('upload_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('change_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='archive_records.changeorder', verbose_name='更改单')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_attachments', to=settings.AUTH_USER_MODEL, verbose_name='上传人')),
            ],
            options={
                'verbose_name': '更改单附件',
                'verbose_name_plural': '更改单附件',
                'ordering': ['-upload_time'],
            },
        ),
        migrations.CreateModel(
            name='ImportLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=100, unique=True, verbose_name='导入批次号')),
                ('file_name', models.CharField(max_length=255, verbose_name='导入文件名')),
                ('file_size', models.IntegerField(verbose_name='文件大小(字节)')),
                ('file_hash', models.CharField(max_length=64, verbose_name='文件哈希值')),
                ('import_date', models.DateTimeField(auto_now_add=True, verbose_name='导入时间')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '完成'), ('failed', '失败'), ('partial', '部分成功')], default='pending', max_length=20, verbose_name='导入状态')),
                ('total_records', models.IntegerField(default=0, verbose_name='总记录数')),
                ('processed_records', models.IntegerField(default=0, verbose_name='已处理记录数')),
                ('success_records', models.IntegerField(default=0, verbose_name='成功记录数')),
                ('failed_records', models.IntegerField(default=0, verbose_name='失败记录数')),
                ('error_log', models.TextField(blank=True, verbose_name='错误日志')),
                ('processing_time', models.FloatField(default=0, verbose_name='处理时间(秒)')),
                ('detailed_report', models.JSONField(default=dict, help_text='详细的导入报告，包含创建、更新、跳过和错误记录的信息', verbose_name='详细报告')),
                ('created_count', models.IntegerField(default=0, verbose_name='新建记录数')),
                ('updated_count', models.IntegerField(default=0, verbose_name='更新记录数')),
                ('system_skipped_records', models.IntegerField(default=0, help_text='系统因重复等原因自动跳过的记录数', verbose_name='系统自动跳过记录数')),
                ('unchanged_count', models.IntegerField(default=0, help_text='与数据库中已有记录完全一致，不需要更新的记录数', verbose_name='完全相同记录数')),
                ('total_skipped_records', models.IntegerField(default=0, help_text='所有被跳过的记录总数(包括用户手动跳过和系统自动跳过)', verbose_name='总跳过记录数')),
                ('user_manual_skipped_records', models.IntegerField(default=0, help_text='用户手动选择跳过的记录数', verbose_name='用户手动跳过记录数')),
                ('import_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='导入用户')),
            ],
            options={
                'verbose_name': '导入日志',
                'verbose_name_plural': '导入日志',
                'ordering': ['-import_date'],
            },
        ),
        migrations.AddField(
            model_name='changelogbatch',
            name='import_log',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='change_batches', to='archive_records.importlog'),
        ),
        migrations.CreateModel(
            name='ImportSession',
            fields=[
                ('session_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('select', '选择文件'), ('upload', '文件上传'), ('analysis_start', '分析开始'), ('analyzing', '分析中'), ('analyzed', '分析完成'), ('processing', '冲突处理'), ('import_start', '导入开始'), ('importing', '导入中'), ('imported', '导入完成'), ('cancelled', '已取消'), ('error', '出错')], default='select', max_length=20)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('file_path', models.CharField(blank=True, max_length=512, null=True)),
                ('sheet_count', models.IntegerField(default=0)),
                ('record_count', models.IntegerField(default=0)),
                ('conflict_count', models.IntegerField(default=0)),
                ('progress', models.FloatField(default=0)),
                ('current_sheet', models.IntegerField(default=0)),
                ('current_record', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('last_heartbeat_at', models.DateTimeField(blank=True, db_index=True, null=True, verbose_name='最后心跳时间')),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True, verbose_name='取消时间')),
                ('cleaned_up_at', models.DateTimeField(blank=True, null=True, verbose_name='异步清理完成时间')),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_sessions', to=settings.AUTH_USER_MODEL)),
                ('import_log', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='import_session', to='archive_records.importlog')),
                ('processing_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processing_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '导入会话',
                'verbose_name_plural': '导入会话',
            },
        ),
        migrations.CreateModel(
            name='ImportConflictDetail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='冲突详情ID')),
                ('commission_number', models.CharField(db_index=True, max_length=2000, verbose_name='委托编号')),
                ('excel_row_number', models.IntegerField(verbose_name='Excel行号')),
                ('existing_record_pk', models.IntegerField(blank=True, null=True, verbose_name='现有记录主键')),
                ('conflict_type', models.CharField(choices=[('new', '新记录'), ('update', '需更新'), ('identical', '完全相同')], max_length=20, verbose_name='冲突类型')),
                ('differences_json', models.JSONField(default=list, verbose_name='字段差异详情 (JSON)')),
                ('user_resolution', models.CharField(blank=True, max_length=20, null=True, verbose_name='用户解决方案')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conflict_details', to='archive_records.importsession', verbose_name='关联导入会话')),
            ],
            options={
                'verbose_name': '导入冲突详情',
                'verbose_name_plural': '导入冲突详情',
                'ordering': ['session', 'excel_row_number'],
            },
        ),
        migrations.CreateModel(
            name='RecordChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_number', models.PositiveIntegerField(verbose_name='版本号')),
                ('change_type', models.CharField(choices=[('create', '新建'), ('update', '更新'), ('delete', '删除'), ('rollback', '回滚')], max_length=20, verbose_name='变更类型')),
                ('is_rollback', models.BooleanField(default=False, verbose_name='是否为回滚操作')),
                ('rollback_source_version', models.PositiveIntegerField(blank=True, null=True, verbose_name='回滚源版本号')),
                ('record_before', models.JSONField(blank=True, null=True, verbose_name='变更前状态')),
                ('record_after', models.JSONField(blank=True, null=True, verbose_name='变更后状态')),
                ('changed_fields_count', models.IntegerField(default=0, verbose_name='变更字段数')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='变更时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='变更备注')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='record_changes', to='archive_records.changelogbatch')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='变更人')),
                ('record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='change_logs', to='archive_records.archiverecord')),
            ],
            options={
                'verbose_name': '记录变更日志',
                'verbose_name_plural': '记录变更日志',
                'ordering': ['-version_number'],
            },
        ),
        migrations.CreateModel(
            name='FieldChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='字段名')),
                ('field_label', models.CharField(max_length=100, verbose_name='字段中文名称')),
                ('old_value', models.TextField(blank=True, null=True, verbose_name='旧值')),
                ('new_value', models.TextField(blank=True, null=True, verbose_name='新值')),
                ('change_action', models.CharField(choices=[('added', '新增'), ('modified', '修改'), ('removed', '删除'), ('unchanged', '未变更')], default='modified', max_length=10, verbose_name='变更动作')),
                ('field_importance', models.CharField(choices=[('critical', '关键字段'), ('important', '重要字段'), ('normal', '普通字段')], default='normal', max_length=20, verbose_name='字段重要性')),
                ('record_change', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_changes', to='archive_records.recordchangelog')),
            ],
            options={
                'verbose_name': '字段变更日志',
                'verbose_name_plural': '字段变更日志',
            },
        ),
        migrations.CreateModel(
            name='ChangeOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_before', models.JSONField(verbose_name='变更前状态')),
                ('planned_changes', models.JSONField(verbose_name='计划变更')),
                ('changes_count', models.PositiveIntegerField(default=0, verbose_name='变更字段数')),
                ('is_executed', models.BooleanField(default=False, verbose_name='是否已执行')),
                ('execution_time', models.DateTimeField(blank=True, null=True, verbose_name='执行时间')),
                ('execution_result', models.CharField(blank=True, max_length=20, null=True, verbose_name='执行结果')),
                ('execution_message', models.TextField(blank=True, null=True, verbose_name='执行消息')),
                ('archive_record', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='change_order_items', to='archive_records.archiverecord', verbose_name='档案记录')),
                ('change_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='archive_records.changeorder', verbose_name='更改单')),
                ('record_change_log', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='change_order_item', to='archive_records.recordchangelog', verbose_name='记录变更日志')),
            ],
            options={
                'verbose_name': '更改单条目',
                'verbose_name_plural': '更改单条目',
            },
        ),
        migrations.CreateModel(
            name='SessionOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('create_session', '创建会话'), ('analyze_start', '分析开始'), ('analyze_complete', '分析完成'), ('error_in_analysis', '分析出错'), ('error_in_analysis_manager', '分析管理器出错'), ('error_in_background_analysis', '后台分析出错'), ('import_start', '导入开始'), ('import_processing', '导入处理中'), ('import_complete', '导入完成'), ('import_error', '导入出错'), ('cancel_cleanup', '取消并清理'), ('takeover', '接管会话'), ('status_change', '状态变更'), ('system_cleanup_expired', '系统清理过期'), ('error', '通用错误')], max_length=50)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('old_status', models.CharField(blank=True, max_length=20, null=True)),
                ('new_status', models.CharField(blank=True, max_length=20, null=True)),
                ('details', models.JSONField(blank=True, null=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='archive_records.importsession')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '会话操作日志',
                'verbose_name_plural': '会话操作日志',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='archiverecord',
            index=models.Index(fields=['sample_number'], name='archive_rec_sample__592ea0_idx'),
        ),
        migrations.AddIndex(
            model_name='archiverecord',
            index=models.Index(fields=['report_number'], name='archive_rec_report__854015_idx'),
        ),
        migrations.AddIndex(
            model_name='archiverecord',
            index=models.Index(fields=['archive_box_number'], name='archive_rec_archive_455fa1_idx'),
        ),
        migrations.AddIndex(
            model_name='archiverecord',
            index=models.Index(fields=['batch_number'], name='archive_rec_batch_n_8b61db_idx'),
        ),
        migrations.AddIndex(
            model_name='archiverecord',
            index=models.Index(fields=['commission_number'], name='archive_rec_commiss_76b33c_idx'),
        ),
        migrations.AddIndex(
            model_name='changeorder',
            index=models.Index(fields=['order_number'], name='archive_rec_order_n_185923_idx'),
        ),
        migrations.AddIndex(
            model_name='changeorder',
            index=models.Index(fields=['status'], name='archive_rec_status_1bb084_idx'),
        ),
        migrations.AddIndex(
            model_name='changeorder',
            index=models.Index(fields=['created_at'], name='archive_rec_created_1d1b31_idx'),
        ),
        migrations.AddIndex(
            model_name='changelogbatch',
            index=models.Index(fields=['changed_at'], name='archive_rec_changed_7fe5db_idx'),
        ),
        migrations.AddIndex(
            model_name='changelogbatch',
            index=models.Index(fields=['change_source'], name='archive_rec_change__eee9be_idx'),
        ),
        migrations.AddIndex(
            model_name='changelogbatch',
            index=models.Index(fields=['changed_by'], name='archive_rec_changed_63bf3e_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['status'], name='archive_rec_status_8f18a1_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['created_by'], name='archive_rec_created_93c87c_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['processing_user'], name='archive_rec_process_0f7a78_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['created_at'], name='archive_rec_created_9b1d34_idx'),
        ),
        migrations.AddIndex(
            model_name='importsession',
            index=models.Index(fields=['last_heartbeat_at'], name='archive_rec_last_he_3abbca_idx'),
        ),
        migrations.AddIndex(
            model_name='importconflictdetail',
            index=models.Index(fields=['session', 'commission_number'], name='archive_rec_session_65f881_idx'),
        ),
        migrations.AddIndex(
            model_name='importconflictdetail',
            index=models.Index(fields=['session', 'conflict_type'], name='archive_rec_session_95e8d1_idx'),
        ),
        migrations.AddIndex(
            model_name='recordchangelog',
            index=models.Index(fields=['record', 'version_number'], name='archive_rec_record__0939fc_idx'),
        ),
        migrations.AddIndex(
            model_name='recordchangelog',
            index=models.Index(fields=['changed_at'], name='archive_rec_changed_e3c6ab_idx'),
        ),
        migrations.AddIndex(
            model_name='recordchangelog',
            index=models.Index(fields=['change_type'], name='archive_rec_change__a59c7a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='recordchangelog',
            unique_together={('record', 'version_number')},
        ),
        migrations.AddIndex(
            model_name='fieldchangelog',
            index=models.Index(fields=['field_name'], name='archive_rec_field_n_2fe43e_idx'),
        ),
        migrations.AddIndex(
            model_name='fieldchangelog',
            index=models.Index(fields=['change_action'], name='archive_rec_change__2c88fd_idx'),
        ),
        migrations.AddIndex(
            model_name='fieldchangelog',
            index=models.Index(fields=['field_importance'], name='archive_rec_field_i_2073f5_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='changeorderitem',
            unique_together={('change_order', 'archive_record')},
        ),
        migrations.AddIndex(
            model_name='sessionoperation',
            index=models.Index(fields=['session'], name='archive_rec_session_2a8aeb_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionoperation',
            index=models.Index(fields=['operation_type'], name='archive_rec_operati_7e1817_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionoperation',
            index=models.Index(fields=['user'], name='archive_rec_user_id_2ba7ac_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionoperation',
            index=models.Index(fields=['timestamp'], name='archive_rec_timesta_dcf4a8_idx'),
        ),
    ]
