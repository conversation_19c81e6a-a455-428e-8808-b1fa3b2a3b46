"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowLeft, AlertTriangle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import ExcelImportReportSummary from "@/components/records/import/excel-import-report-summary"
import Link from "next/link"
import React from "react"
import { PageLayout } from "@/components/common/page-layout"
import { useImportLogDetail } from "@/hooks/domain/records/import/useImportLogDetail"

interface ImportHistoryDetailPageProps {
  params: Promise<{ id: string }>
}

export default function ImportHistoryDetailPage({ params }: ImportHistoryDetailPageProps) {
  const resolvedParams = React.use(params)
  const id = resolvedParams.id
  const { toast } = useToast()

  // 使用专用的hook获取数据
  const { reportData, rawData, isLoading, error } = useImportLogDetail(id);

  // 显示错误提示
  React.useEffect(() => {
    if (error) {
      toast({
        title: "加载失败", 
        description: error || "无法获取Excel导入详情，请稍后重试",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const renderLoadingState = () => (
    <div className="space-y-4">
      <Skeleton className="h-8 w-1/3" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-2/3" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
      </div>
      <Skeleton className="h-64 w-full mt-8" />
    </div>
  )

  const renderErrorState = () => (
    <div className="text-center py-12 flex flex-col items-center justify-center text-muted-foreground">
      <AlertTriangle className="h-12 w-12 mb-4 text-red-500" />
      <h3 className="font-semibold text-xl mb-2">无法获取详情</h3>
      <p className="mb-6">{error || "获取导入批次详情失败，请返回列表重试"}</p>
      <Button asChild>
        <Link href="/records/import-history">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回导入历史列表
        </Link>
      </Button>
    </div>
  )

  const actions = [
    {
      label: "返回列表",
      icon: <ArrowLeft className="h-4 w-4" />,
      href: "/records/import-history",
      variant: "outline" as const
    },
    {
      label: "导入新文件",
      href: "/records/import",
      variant: "outline" as const
    }
  ]

  if (isLoading) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        {renderLoadingState()}
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        {renderErrorState()}
      </PageLayout>
    )
  }

  if (!rawData || !reportData) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        <div className="text-center py-12 flex flex-col items-center justify-center text-muted-foreground">
          <AlertTriangle className="h-12 w-12 mb-4 text-amber-500" />
          <h3 className="font-semibold text-xl mb-2">无法找到导入批次</h3>
          <p className="mb-6">找不到ID为 {id} 的导入批次记录，请返回列表查看可用的导入记录</p>
          <Button asChild>
            <Link href="/records/import-history">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回导入历史列表
            </Link>
          </Button>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout
      title="Excel导入详情"
      subtitle="查看Excel文件导入的详细处理结果"
      actions={actions}
      contentGap="space-y-6"
    >
      <ExcelImportReportSummary reportData={reportData} />
      
      {/* 批次操作暂时注释掉
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>批次操作</CardTitle>
          <Button variant="outline" size="sm" disabled={rawData.processing_status === "processing"}>
            <Download className="mr-2 h-4 w-4" />
            下载详情
          </Button>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">导入记录操作功能开发中...</p>
          </div>
        </CardContent>
      </Card>
      */}
    </PageLayout>
  )
} 