import unittest
from datetime import datetime, timedelta, timezone as dt_timezone
import pytz
import pandas as pd
from django.test import TestCase
from django.utils import timezone

from archive_records.utils.datetime_utils import TimezoneHandler


class TimezoneHandlerTestCase(TestCase):
    """
    TimezoneHandler的单元测试
    
    测试现代化时区处理方案的各种场景
    """
    
    def setUp(self):
        """测试设置"""
        # 使用与TimezoneHandler相同的时区定义
        self.local_tz = dt_timezone(timedelta(hours=8))  # 固定UTC+8
        self.utc_tz = dt_timezone.utc
        
        # 测试时间: 2025-03-17 14:30:00 (本地时间)
        self.test_local_naive = datetime(2025, 3, 17, 14, 30, 0)
        self.test_local_aware = self.test_local_naive.replace(tzinfo=self.local_tz)
        self.test_utc_aware = self.test_local_aware.astimezone(self.utc_tz)
    
    def test_parse_excel_datetime_naive_string(self):
        """测试解析Excel字符串时间（naive）"""
        excel_value = "2025-03-17 14:30:00"
        result = TimezoneHandler.parse_excel_datetime(excel_value)
        
        self.assertIsNotNone(result)
        # 现在应该能直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        self.assertEqual(result.replace(tzinfo=None), self.test_local_naive)
    
    def test_parse_excel_datetime_with_wrong_timezone(self):
        """测试解析带有错误时区标记的Excel时间"""
        # 模拟pandas错误地标记为UTC的情况
        excel_value = self.test_local_naive.replace(tzinfo=self.utc_tz)  # 错误：本地时间被标记为UTC
        result = TimezoneHandler.parse_excel_datetime(excel_value)
        
        self.assertIsNotNone(result)
        # 现在应该能直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        # 应该移除错误的UTC标记，按本地时间重新解释
        self.assertEqual(result.replace(tzinfo=None), self.test_local_naive)
    
    def test_parse_excel_datetime_none_value(self):
        """测试解析空值"""
        result = TimezoneHandler.parse_excel_datetime(None)
        self.assertIsNone(result)
        
        result = TimezoneHandler.parse_excel_datetime(pd.NaT)
        self.assertIsNone(result)
    
    def test_convert_to_utc(self):
        """测试本地时间转UTC"""
        result = TimezoneHandler.convert_to_utc(self.test_local_aware)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.tzinfo, self.utc_tz)
        self.assertEqual(result, self.test_utc_aware)
    
    def test_convert_to_utc_naive(self):
        """测试naive时间转UTC（假设为本地时间）"""
        result = TimezoneHandler.convert_to_utc(self.test_local_naive)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.tzinfo, self.utc_tz)
        self.assertEqual(result, self.test_utc_aware)
    
    def test_convert_to_local(self):
        """测试UTC时间转本地时间"""
        result = TimezoneHandler.convert_to_local(self.test_utc_aware)
        
        self.assertIsNotNone(result)
        # 直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        # 检查时间值是否正确
        self.assertEqual(result.replace(tzinfo=None), self.test_local_naive)
    
    def test_convert_to_local_naive(self):
        """测试naive时间转本地时间（假设为UTC）"""
        utc_naive = self.test_utc_aware.replace(tzinfo=None)
        result = TimezoneHandler.convert_to_local(utc_naive)
        
        self.assertIsNotNone(result)
        # 直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        # 检查时间值是否正确
        self.assertEqual(result.replace(tzinfo=None), self.test_local_naive)
    
    def test_compare_datetime_values_equal(self):
        """测试相等时间的比较"""
        excel_value = "2025-03-17 14:30:00"  # 本地时间
        db_value = self.test_utc_aware  # UTC时间
        
        result = TimezoneHandler.compare_datetime_values(excel_value, db_value, "test_field")
        self.assertTrue(result)
    
    def test_compare_datetime_values_within_tolerance(self):
        """测试在容忍度内的时间比较"""
        excel_value = "2025-03-17 14:30:00"  # 本地时间
        # 数据库时间比Excel时间晚0.5秒
        db_utc_with_microseconds = self.test_utc_aware + timedelta(microseconds=500000)
        
        result = TimezoneHandler.compare_datetime_values(
            excel_value, db_utc_with_microseconds, "test_field", tolerance_seconds=1.0
        )
        self.assertTrue(result)
    
    def test_compare_datetime_values_beyond_tolerance(self):
        """测试超出容忍度的时间比较"""
        excel_value = "2025-03-17 14:30:00"  # 本地时间
        # 数据库时间比Excel时间晚2秒
        db_utc_late = self.test_utc_aware + timedelta(seconds=2)
        
        result = TimezoneHandler.compare_datetime_values(
            excel_value, db_utc_late, "test_field", tolerance_seconds=1.0
        )
        self.assertFalse(result)
    
    def test_compare_datetime_values_both_none(self):
        """测试两个都为空值的比较"""
        result = TimezoneHandler.compare_datetime_values(None, None, "test_field")
        self.assertTrue(result)
    
    def test_compare_datetime_values_one_none(self):
        """测试一个为空值的比较"""
        excel_value = "2025-03-17 14:30:00"
        result = TimezoneHandler.compare_datetime_values(excel_value, None, "test_field")
        self.assertFalse(result)
        
        result = TimezoneHandler.compare_datetime_values(None, self.test_utc_aware, "test_field")
        self.assertFalse(result)
    
    def test_full_workflow_excel_to_db_and_compare(self):
        """测试完整的工作流程：Excel -> 数据库存储 -> 比较"""
        # 1. 模拟Excel导入：解析Excel时间
        excel_value = "2025-03-17 14:30:00"
        local_dt = TimezoneHandler.parse_excel_datetime(excel_value)
        
        # 2. 模拟数据库存储：转换为UTC
        utc_dt = TimezoneHandler.convert_to_utc(local_dt)
        
        # 3. 模拟重新导入时的比较
        is_same = TimezoneHandler.compare_datetime_values(excel_value, utc_dt, "commission_datetime")
        
        # 应该判断为相同
        self.assertTrue(is_same)
    
    def test_pandas_timezone_edge_cases(self):
        """测试pandas时区处理的边界情况"""
        # 测试pandas Timestamp对象
        pandas_ts = pd.Timestamp("2025-03-17 14:30:00")
        result = TimezoneHandler.parse_excel_datetime(pandas_ts)
        
        self.assertIsNotNone(result)
        # 直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        
        # 测试带有错误时区的pandas Timestamp
        pandas_ts_utc = pd.Timestamp("2025-03-17 14:30:00", tz='UTC')
        result = TimezoneHandler.parse_excel_datetime(pandas_ts_utc)
        
        self.assertIsNotNone(result)
        # 直接比较时区对象
        self.assertEqual(result.tzinfo, self.local_tz)
        # 应该移除UTC标记，按本地时间解释
        self.assertEqual(result.replace(tzinfo=None), self.test_local_naive)


if __name__ == '__main__':
    unittest.main() 