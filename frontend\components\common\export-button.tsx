"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Download, FileSpreadsheet, FileIcon as FilePdf, Loader2 } from "lucide-react"
import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

interface ExportButtonProps {
  resourceName: string
  resourceId?: string
  onExport?: (format: string) => Promise<void>
  className?: string
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive"
}

export function ExportButton({
  resourceName,
  resourceId,
  onExport,
  className,
  variant = "outline",
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false)
  const [exportFormat, setExportFormat] = useState<string | null>(null)
  const { toast } = useToast()

  const handleExport = async (format: string) => {
    setIsExporting(true)
    setExportFormat(format)

    try {
      if (onExport) {
        await onExport(format)
      } else {
        // 默认导出逻辑
        // 实际应用中，这里会调用API导出数据
        // const response = await fetch(`/api/${resourceName}${resourceId ? `/${resourceId}` : ''}/export?format=${format}`, {
        //   method: 'GET',
        // });
        //
        // if (response.ok) {
        //   const blob = await response.blob();
        //   const url = window.URL.createObjectURL(blob);
        //   const a = document.createElement('a');
        //   a.href = url;
        //   a.download = `${resourceName}${resourceId ? `-${resourceId}` : ''}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
        //   document.body.appendChild(a);
        //   a.click();
        //   window.URL.revokeObjectURL(url);
        //   a.remove();
        // } else {
        //   throw new Error('导出失败');
        // }

        // 模拟导出延迟
        await new Promise((resolve) => setTimeout(resolve, 1500))

        toast({
          title: "导出成功",
          description: `${resourceName}已成功导出为${format === "excel" ? "Excel" : "PDF"}文件`,
        })
      }
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出数据时发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
      setExportFormat(null)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} className={className} disabled={isExporting}>
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              导出中...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              导出
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleExport("excel")} disabled={isExporting} className="flex items-center">
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          <span>导出为Excel</span>
          {isExporting && exportFormat === "excel" && <Loader2 className="ml-2 h-3 w-3 animate-spin" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport("pdf")} disabled={isExporting} className="flex items-center">
          <FilePdf className="mr-2 h-4 w-4" />
          <span>导出为PDF</span>
          {isExporting && exportFormat === "pdf" && <Loader2 className="ml-2 h-3 w-3 animate-spin" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
