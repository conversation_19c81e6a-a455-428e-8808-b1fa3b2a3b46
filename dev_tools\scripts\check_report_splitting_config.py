#!/usr/bin/env python
"""
报告分割功能配置检查脚本

此脚本检查报告分割功能所需的配置是否完整，包括：
1. 数据库字段
2. CMA章模板配置
3. 相关依赖库
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')
django.setup()

from django.conf import settings
from django.db import connection
import logging

logger = logging.getLogger(__name__)

class ReportSplittingConfigChecker:
    """报告分割功能配置检查器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.success = []
    
    def check_database_fields(self):
        """检查数据库字段"""
        print("🔍 检查数据库字段...")
        
        try:
            from archive_records.models import ArchiveRecord
            
            # 检查关键字段
            required_fields = {
                'archive_url': 'URLField',
                'report_url': 'URLField', 
                'source_file': 'ForeignKey'
            }
            
            model_fields = {field.name: field.__class__.__name__ for field in ArchiveRecord._meta.fields}
            
            for field_name, field_type in required_fields.items():
                if field_name in model_fields:
                    self.success.append(f"✅ 字段 {field_name} 存在 ({model_fields[field_name]})")
                else:
                    self.issues.append(f"❌ 缺少字段 {field_name} ({field_type})")
            
            # 检查数据库连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                self.success.append("✅ 数据库连接正常")
                
        except Exception as e:
            self.issues.append(f"❌ 数据库检查失败: {e}")
    
    def check_template_configuration(self):
        """检查CMA章模板配置"""
        print("🔍 检查CMA章模板配置...")
        
        # 检查设置中的模板路径配置
        ma_template_paths = getattr(settings, 'MA_SEAL_TEMPLATE_PATHS', [])
        if ma_template_paths:
            self.success.append(f"✅ Settings中配置了模板路径: {len(ma_template_paths)} 个")
            for path in ma_template_paths:
                if os.path.exists(path):
                    self.success.append(f"✅ 模板文件存在: {path}")
                else:
                    self.issues.append(f"❌ 模板文件不存在: {path}")
        else:
            self.warnings.append("⚠️ Settings中未配置 MA_SEAL_TEMPLATE_PATHS")
        
        # 检查默认模板目录
        default_template_path = os.path.join(settings.BASE_DIR, "templates", "ma_seal")
        if os.path.exists(default_template_path):
            template_files = [f for f in os.listdir(default_template_path) 
                            if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            if template_files:
                self.success.append(f"✅ 默认模板目录存在，包含 {len(template_files)} 个模板文件")
                for file in template_files[:3]:  # 只显示前3个
                    self.success.append(f"   📁 {file}")
                if len(template_files) > 3:
                    self.success.append(f"   📁 ... 还有 {len(template_files) - 3} 个文件")
            else:
                self.warnings.append(f"⚠️ 默认模板目录存在但无模板文件: {default_template_path}")
        else:
            self.warnings.append(f"⚠️ 默认模板目录不存在: {default_template_path}")
    
    def check_recognition_parameters(self):
        """检查识别参数配置"""
        print("🔍 检查识别参数配置...")
        
        parameters = {
            'REPORT_RECOGNITION_DPI': (200, int),
            'MA_SEAL_COLOR_THRESHOLD': (30, int),
            'MA_SEAL_MIN_CONTOUR_AREA': (500, int),
            'MA_SEAL_MATCH_THRESHOLD': (10, int),
            'MA_SEAL_USE_SIFT': (True, bool),
            'MA_SEAL_MAX_FEATURES': (500, int)
        }
        
        for param_name, (default_value, expected_type) in parameters.items():
            value = getattr(settings, param_name, None)
            if value is not None:
                if isinstance(value, expected_type):
                    self.success.append(f"✅ 参数 {param_name} = {value}")
                else:
                    self.warnings.append(f"⚠️ 参数 {param_name} 类型错误: {type(value).__name__}, 期望: {expected_type.__name__}")
            else:
                self.warnings.append(f"⚠️ 未配置参数 {param_name}, 将使用默认值: {default_value}")
    
    def check_dependencies(self):
        """检查依赖库"""
        print("🔍 检查依赖库...")
        
        required_packages = {
            'PIL': 'Pillow',
            'cv2': 'opencv-python',
            'numpy': 'numpy',
            'fitz': 'PyMuPDF'
        }
        
        for module_name, package_name in required_packages.items():
            try:
                __import__(module_name)
                self.success.append(f"✅ 依赖库 {package_name} 已安装")
            except ImportError:
                self.issues.append(f"❌ 缺少依赖库 {package_name}")
    
    def check_services(self):
        """检查关键服务类"""
        print("🔍 检查关键服务类...")
        
        try:
            from archive_processing.services.report_recognition_service import ReportRecognitionService
            self.success.append("✅ ReportRecognitionService 导入成功")
            
            # 尝试初始化服务
            service = ReportRecognitionService()
            template_count = len(service.ma_templates)
            if template_count > 0:
                self.success.append(f"✅ 成功加载 {template_count} 个CMA章模板")
            else:
                self.warnings.append("⚠️ 未加载到任何CMA章模板")
                
        except Exception as e:
            self.issues.append(f"❌ ReportRecognitionService 初始化失败: {e}")
        
        try:
            from archive_processing.services.report_splitting_service import ReportSplittingService
            self.success.append("✅ ReportSplittingService 导入成功")
        except Exception as e:
            self.issues.append(f"❌ ReportSplittingService 导入失败: {e}")
    
    def generate_recommendations(self):
        """生成配置建议"""
        recommendations = []
        
        if not self.success or self.issues:
            recommendations.append("## 📋 配置建议")
            recommendations.append("")
        
        if any("模板" in issue for issue in self.issues + self.warnings):
            recommendations.extend([
                "### 1. CMA章模板配置",
                "- 将CMA章样本图片放入 `templates/ma_seal/` 目录",
                "- 支持PNG、JPG格式，建议200-300 DPI",
                "- 或在settings.py中设置 `MA_SEAL_TEMPLATE_PATHS`",
                ""
            ])
        
        if any("依赖库" in issue for issue in self.issues):
            recommendations.extend([
                "### 2. 安装缺失依赖",
                "```bash",
                "pip install opencv-python Pillow PyMuPDF numpy",
                "```",
                ""
            ])
        
        if any("参数" in warning for warning in self.warnings):
            recommendations.extend([
                "### 3. 识别参数调优",
                "在settings.py中添加：",
                "```python",
                "# CMA章识别参数",
                "REPORT_RECOGNITION_DPI = 200",
                "MA_SEAL_COLOR_THRESHOLD = 30",
                "MA_SEAL_MIN_CONTOUR_AREA = 500", 
                "MA_SEAL_MATCH_THRESHOLD = 10",
                "MA_SEAL_USE_SIFT = True",
                "MA_SEAL_MAX_FEATURES = 500",
                "```",
                ""
            ])
        
        return "\n".join(recommendations)
    
    def run_check(self):
        """运行完整检查"""
        print("🔧 报告分割功能配置检查")
        print("=" * 50)
        
        self.check_database_fields()
        self.check_template_configuration()
        self.check_recognition_parameters()
        self.check_dependencies()
        self.check_services()
        
        print("\n" + "=" * 50)
        print("📊 检查结果汇总")
        print("=" * 50)
        
        if self.success:
            print(f"\n✅ 成功项目 ({len(self.success)}):")
            for item in self.success:
                print(f"  {item}")
        
        if self.warnings:
            print(f"\n⚠️ 警告项目 ({len(self.warnings)}):")
            for item in self.warnings:
                print(f"  {item}")
        
        if self.issues:
            print(f"\n❌ 问题项目 ({len(self.issues)}):")
            for item in self.issues:
                print(f"  {item}")
        
        recommendations = self.generate_recommendations()
        if recommendations:
            print(f"\n{recommendations}")
        
        # 总结
        print("=" * 50)
        if not self.issues:
            if not self.warnings:
                print("🎉 配置完美！报告分割功能已就绪。")
            else:
                print("✅ 基本配置完整，报告分割功能可用。建议处理警告项目以获得更好性能。")
        else:
            print("❌ 存在配置问题，需要修复后才能正常使用报告分割功能。")
        
        return len(self.issues) == 0

if __name__ == "__main__":
    checker = ReportSplittingConfigChecker()
    success = checker.run_check()
    sys.exit(0 if success else 1) 