# Excel导入会话状态管理架构设计

## 1. 概述

本文档详细描述Excel导入会话状态管理架构，包括状态流转、持久化策略和多用户协作模型。设计遵循"服务器为唯一真相来源"的原则，确保在多用户协作环境下的数据一致性和操作安全性。

## 2. 会话状态模型

### 2.1 状态枚举定义

```python
# 后端状态定义（Python/Django）
class ImportSessionStatus(models.TextChoices):
    SELECT = 'select', _('选择文件')
    UPLOAD = 'upload', _('文件上传')
    ANALYSIS_START = 'analysis_start', _('分析开始')
    ANALYSIS_IN_PROGRESS = 'analyzing', _('分析中')
    ANALYSIS_COMPLETE = 'analyzed', _('分析完成')
    CONFLICT_RESOLUTION = 'processing', _('冲突处理')
    IMPORT_START = 'import_start', _('导入开始')
    IMPORT_IN_PROGRESS = 'importing', _('导入中')
    IMPORT_COMPLETE = 'imported', _('导入完成')
    CANCELLED = 'cancelled', _('已取消')
    ERROR = 'error', _('出错')
```

```typescript
// 前端状态定义（TypeScript）
export enum ImportSessionStep {
  SELECT = 'select',
  UPLOAD = 'upload',
  ANALYSIS_START = 'analysis_start',
  ANALYSIS_IN_PROGRESS = 'analyzing',
  ANALYSIS_COMPLETE = 'analyzed',
  CONFLICT_RESOLUTION = 'processing',
  IMPORT_START = 'import_start',
  IMPORT_IN_PROGRESS = 'importing',
  IMPORT_COMPLETE = 'imported',
  CANCELLED = 'cancelled',
  ERROR = 'error'
}
```

### 2.2 状态流转图

```
+----------+     +--------+     +---------------+     +--------------------+     +------------------+
|          |     |        |     |               |     |                    |     |                  |
|  SELECT  +---->+ UPLOAD +---->+ ANALYSIS_START+---->+ ANALYSIS_IN_PROGRESS---->+ ANALYSIS_COMPLETE+
|          |     |        |     |               |     |                    |     |                  |
+----------+     +--------+     +---------------+     +--------------------+     +-------+----------+
                                                                                         |
                                                                                         v
                                                                                 +-----------------+
                     +----------------+     +---------------+     +-------------+ |                 |
                     |                |     |               |     |               |CONFLICT_RESOLUTION|
                     | IMPORT_COMPLETE+<----+ IMPORT_IN_PROGRESS<--+ IMPORT_START +<-----------------+
                     |                |     |               |     |               |                 |
                     +----------------+     +---------------+     +---------------+                 |
                                                                                                   |
                                                                                                   |
                     +----------+                                                                  |
                     |          |                                                                  |
                     | CANCELLED+<-----------------------------------------------------------------+
                     |          |                                                                  |
                     +----------+                                                                  |
                                                                                                   |
                     +-------+                                                                     |
                     |       |                                                                     |
                     | ERROR +<--------------------------------------------------------------------+
                     |       |
                     +-------+
```

### 2.3 状态转换规则

| 当前状态 | 可转换状态 | 转换条件 | 权限要求 |
|---------|-----------|---------|---------|
| SELECT | UPLOAD | 用户选择文件 | 导入权限 |
| UPLOAD | ANALYSIS_START | 文件上传完成 | 导入权限 |
| ANALYSIS_START | ANALYSIS_IN_PROGRESS | 后端开始分析 | 系统自动 |
| ANALYSIS_IN_PROGRESS | ANALYSIS_COMPLETE | 分析完成 | 系统自动 |
| ANALYSIS_IN_PROGRESS | ERROR | 分析过程出错 | 系统自动 |
| ANALYSIS_IN_PROGRESS | CANCELLED | 用户取消分析 | 导入权限 |
| ANALYSIS_COMPLETE | CONFLICT_RESOLUTION | 用户开始处理冲突 | 导入权限 |
| ANALYSIS_COMPLETE | CANCELLED | 用户取消导入 | 导入权限 |
| CONFLICT_RESOLUTION | IMPORT_START | 用户确认冲突处理 | 导入权限 |
| CONFLICT_RESOLUTION | CANCELLED | 用户取消导入 | 导入权限 |
| IMPORT_START | IMPORT_IN_PROGRESS | 后端开始导入 | 系统自动 |
| IMPORT_IN_PROGRESS | IMPORT_COMPLETE | 导入完成 | 系统自动 |
| IMPORT_IN_PROGRESS | ERROR | 导入过程出错 | 系统自动 |
| 任何状态 | ERROR | 系统错误 | 系统自动 |

## 3. 数据模型设计

### 3.1 ImportSession 模型

```python
class ImportSession(models.Model):
    """Excel导入会话模型"""
    
    session_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    status = models.CharField(
        max_length=20,
        choices=ImportSessionStatus.choices,
        default=ImportSessionStatus.SELECT
    )
    
    # 文件和分析信息
    file_name = models.CharField(max_length=255, blank=True, null=True)
    file_path = models.CharField(max_length=512, blank=True, null=True)
    sheet_count = models.IntegerField(default=0)
    record_count = models.IntegerField(default=0)
    conflict_count = models.IntegerField(default=0)
    
    # 进度信息
    progress = models.FloatField(default=0)  # 0-100
    current_sheet = models.IntegerField(default=0)
    current_record = models.IntegerField(default=0)
    
    # 用户和会话管理
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_sessions'
    )
    processing_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processing_sessions'
    )
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # 错误信息
    error_message = models.TextField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_by']),
            models.Index(fields=['processing_user']),
        ]
    
    def is_active(self):
        """检查会话是否活跃（未完成、未取消且未过期）"""
        if self.status in [
            ImportSessionStatus.IMPORT_COMPLETE,
            ImportSessionStatus.CANCELLED,
            ImportSessionStatus.ERROR
        ]:
            return False
            
        if self.expires_at and timezone.now() > self.expires_at:
            return False
            
        return True
    
    def can_be_taken_over(self):
        """检查会话是否可被接管"""
        if not self.is_active():
            return False
            
        # 会话超时但状态仍为活跃，可以被接管
        inactive_threshold = timezone.now() - timedelta(minutes=settings.SESSION_INACTIVITY_TIMEOUT)
        return self.last_activity < inactive_threshold
```

### 3.2 SessionOperation 模型（审计日志）

```python
class SessionOperation(models.Model):
    """会话操作日志模型"""
    
    OPERATION_TYPES = [
        ('create', '创建会话'),
        ('analyze', '开始分析'),
        ('cancel', '取消会话'),
        ('resolve', '处理冲突'),
        ('import', '执行导入'),
        ('takeover', '接管会话'),
        ('status_change', '状态变更'),
    ]
    
    session = models.ForeignKey(
        ImportSession,
        on_delete=models.CASCADE,
        related_name='operations'
    )
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    old_status = models.CharField(max_length=20, blank=True, null=True)
    new_status = models.CharField(max_length=20, blank=True, null=True)
    details = models.JSONField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['session']),
            models.Index(fields=['operation_type']),
            models.Index(fields=['user']),
            models.Index(fields=['timestamp']),
        ]
```

## 4. 会话锁定机制

### 4.1 全局唯一会话实现

使用Django缓存框架实现全局会话锁定机制：

```python
# 使用缓存实现全局会话锁
LOCK_KEY = "excel_import_active_session"
SESSION_EXPIRATION_MINUTES = 60  # 会话默认过期时间

def _acquire_session_lock(user_id):
    """尝试获取全局会话锁，返回是否成功"""
    current_lock = cache.get(LOCK_KEY)
    
    # 如果已有锁，检查是否有效
    if current_lock:
        # 检查锁是否过期
        lock_timestamp = datetime.fromisoformat(current_lock.get('timestamp'))
        expires_at = lock_timestamp + timedelta(minutes=SESSION_EXPIRATION_MINUTES)
        
        if timezone.now() < expires_at:
            # 锁仍然有效
            return False
    
    # 设置新锁
    lock_data = {
        'user_id': user_id,
        'timestamp': timezone.now().isoformat()
    }
    cache.set(LOCK_KEY, lock_data, timeout=SESSION_EXPIRATION_MINUTES * 60)
    return True

def _release_session_lock(user_id):
    """释放全局会话锁，只有锁的拥有者可以释放"""
    current_lock = cache.get(LOCK_KEY)
    
    if not current_lock:
        return True
        
    if str(current_lock.get('user_id')) == str(user_id):
        cache.delete(LOCK_KEY)
        return True
        
    return False
```

### 4.2 会话处理锁定

```python
def acquire_processing_lock(session_id, user_id):
    """获取会话处理锁，只有一个用户可以处理会话"""
    try:
        with transaction.atomic():
            session = ImportSession.objects.select_for_update().get(session_id=session_id)
            
            # 检查会话状态是否允许处理
            if session.status not in [ImportSessionStatus.ANALYSIS_COMPLETE]:
                return False, "会话状态不允许处理"
                
            # 检查当前是否已有处理用户
            if session.processing_user and session.processing_user.id != user_id:
                # 检查处理锁是否超时
                inactive_threshold = timezone.now() - timedelta(minutes=settings.PROCESSING_LOCK_TIMEOUT)
                if session.last_activity > inactive_threshold:
                    return False, "会话正在被其他用户处理"
            
            # 设置或更新处理用户和活动时间
            session.processing_user_id = user_id
            session.last_activity = timezone.now()
            session.save()
            
            # 记录操作日志
            if not session.processing_user or session.processing_user.id != user_id:
                SessionOperation.objects.create(
                    session=session,
                    operation_type='takeover',
                    user_id=user_id,
                    details={"previous_user": session.processing_user_id if session.processing_user else None}
                )
                
            return True, "成功获取处理锁"
            
    except ImportSession.DoesNotExist:
        return False, "会话不存在"
    except Exception as e:
        return False, f"获取处理锁出错: {str(e)}"
```

## 5. API接口设计

### 5.1 核心API端点

| API端点 | 方法 | 描述 | 参数 | 返回值 |
|---------|------|------|------|--------|
| `/api/archive-records/analyze-excel/` | POST | 上传并分析Excel文件 | Excel文件 | 会话ID，会话信息 |
| `/api/archive-records/excel-import-progress/` | GET | 获取分析进度 | 会话ID | 进度百分比，状态 |
| `/api/archive-records/excel-analysis-result/` | GET | 获取分析结果 | 会话ID | 冲突记录，统计信息 |
| `/api/archive-records/confirm-import/` | POST | 确认导入 | 会话ID，冲突解决方案 | 导入结果 |
| `/api/archive-records/cancel-import/` | POST | 取消导入 | 会话ID | 取消结果 |
| `/api/archive-records/active-import-session/` | GET | 获取活跃会话 | 无 | 会话信息 |
| `/api/archive-records/takeover-session/` | POST | 接管会话 | 会话ID | 接管结果 |

### 5.2 API响应格式统一标准

所有API响应均使用统一的JSON格式：

**成功响应**:
```json
{
  "success": true,
  "data": {
    // 响应数据，根据接口不同而变化
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "人类可读的错误消息",
    "details": {} // 可选的详细错误信息
  }
}
```

### 5.3 API示例

#### 5.3.1 获取活跃会话

```python
@api_view(['GET'])
@permission_required('archive_records.view_importsession')
def get_active_import_session(request):
    """获取当前活跃的导入会话"""
    try:
        # 查找未完成且未过期的会话
        active_session = ImportSession.objects.filter(
            Q(status__in=[
                ImportSessionStatus.SELECT,
                ImportSessionStatus.UPLOAD,
                ImportSessionStatus.ANALYSIS_START,
                ImportSessionStatus.ANALYSIS_IN_PROGRESS,
                ImportSessionStatus.ANALYSIS_COMPLETE,
                ImportSessionStatus.CONFLICT_RESOLUTION,
                ImportSessionStatus.IMPORT_START,
                ImportSessionStatus.IMPORT_IN_PROGRESS,
            ]) & 
            (Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now()))
        ).order_by('-created_at').first()
        
        if active_session:
            # 检查会话是否已过期但未更新状态
            if not active_session.is_active():
                return Response({
                    'success': True,
                    'data': {
                        'has_active_session': False
                    }
                })
                
            # 格式化会话信息
            session_info = {
                'session_id': active_session.session_id,
                'status': active_session.status,
                'file_name': active_session.file_name,
                'created_by': {
                    'id': active_session.created_by.id,
                    'username': active_session.created_by.username,
                },
                'created_at': active_session.created_at.isoformat(),
                'updated_at': active_session.updated_at.isoformat(),
                'progress': active_session.progress,
                'expires_at': active_session.expires_at.isoformat() if active_session.expires_at else None,
            }
            
            # 添加处理用户信息（如果有）
            if active_session.processing_user:
                session_info['processing_user'] = {
                    'id': active_session.processing_user.id,
                    'username': active_session.processing_user.username,
                }
                session_info['last_activity'] = active_session.last_activity.isoformat()
            
            # 检查当前用户是否有权处理此会话
            has_permission = request.user.id == active_session.created_by.id or \
                            request.user.has_perm('archive_records.process_any_importsession')
            
            return Response({
                'success': True,
                'data': {
                    'has_active_session': True,
                    'session_info': session_info,
                    'has_permission': has_permission
                }
            })
        else:
            return Response({
                'success': True,
                'data': {
                    'has_active_session': False
                }
            })
    except Exception as e:
        return Response({
            'success': False,
            'error': {
                'code': 'SESSION_QUERY_ERROR',
                'message': f'获取活跃会话失败: {str(e)}',
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

## 6. 前端状态管理策略

### 6.1 状态存储结构

使用Zustand构建状态管理服务:

```typescript
// services/import-session-state.ts
import { create } from 'zustand'
import { excelImportService } from './excel-import-service'
import { toast } from '@/components/ui/toast'

// 会话状态接口
interface ImportSessionState {
  // 状态数据
  sessionId: string | null;
  currentStep: ImportSessionStep;
  fileName: string | null;
  fileSize: number | null;
  progress: number;
  isLoading: boolean;
  isProcessing: boolean;
  error: string | null;
  analysisResults: any | null;
  processingUser: {
    id: number;
    username: string;
  } | null;
  lastServerSync: Date | null;
  progressPollingId: number | null;
  
  // 操作方法
  fetchCurrentState: () => Promise<boolean>;
  uploadFile: (file: File) => Promise<boolean>;
  startAnalysis: () => Promise<boolean>;
  cancelAnalysis: () => Promise<boolean>;
  getAnalysisResults: () => Promise<boolean>;
  startProcessing: () => Promise<boolean>;
  confirmImport: (resolutions: any[]) => Promise<boolean>;
  cancelImport: () => Promise<boolean>;
  takeoverSession: (sessionId: string) => Promise<boolean>;
  clearSession: () => void;
  
  // 内部方法
  _startProgressPolling: (sessionId: string) => void;
  _stopProgressPolling: () => void;
  _storeSessionReference: (sessionId: string | null) => void;
}

// 创建状态管理服务
export const useImportSessionStore = create<ImportSessionState>((set, get) => ({
  // 初始状态
  sessionId: null,
  currentStep: ImportSessionStep.SELECT,
  fileName: null,
  fileSize: null,
  progress: 0,
  isLoading: false,
  isProcessing: false,
  error: null,
  analysisResults: null,
  processingUser: null,
  lastServerSync: null,
  progressPollingId: null,
  
  // 从服务器获取当前状态
  fetchCurrentState: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await excelImportService.getActiveImportSession();
      const { success, data, error } = response;
      
      if (!success) throw new Error(error?.message || '获取会话状态失败');
      
      set({ lastServerSync: new Date() });
      
      if (data.has_active_session) {
        const sessionInfo = data.session_info;
        const sessionId = sessionInfo.session_id;
        
        // 存储会话引用ID
        get()._storeSessionReference(sessionId);
        
        // 更新状态
        set({
          sessionId,
          currentStep: sessionInfo.status as ImportSessionStep,
          fileName: sessionInfo.file_name,
          progress: sessionInfo.progress || 0,
          processingUser: sessionInfo.processing_user || null
        });
        
        // 根据状态启动对应的轮询
        if (sessionInfo.status === ImportSessionStep.ANALYSIS_IN_PROGRESS) {
          get()._startProgressPolling(sessionId);
        } else if (sessionInfo.status === ImportSessionStep.ANALYSIS_COMPLETE) {
          // 获取分析结果
          await get().getAnalysisResults();
        }
        
        return true;
      } else {
        // 无活跃会话
        get().clearSession();
        return false;
      }
    } catch (error) {
      console.error('获取会话状态失败:', error);
      set({ error: error.message });
      return false;
    } finally {
      set({ isLoading: false });
    }
  },
  
  // 分析文件（新的两阶段导入流程）
  analyzeFile: async (file: File) => {
    set({ isLoading: true, error: null, fileName: file.name, fileSize: file.size });
    
    try {
      // 乐观更新
      set({ currentStep: ImportSessionStep.UPLOAD });
      
      const response = await excelImportService.analyzeExcelFile(file);
      const { importSessionId, fileName, totalRecords, status, message } = response;
      
      // 更新状态
      set({
        sessionId: importSessionId,
        currentStep: ImportSessionStep.ANALYSIS_START,
        fileName: fileName,
        totalRecords: totalRecords
      });
      
      // 存储会话引用ID
      get()._storeSessionReference(importSessionId);
      
      return true;
    } catch (error) {
      console.error('分析文件失败:', error);
      set({ 
        error: error.message,
        currentStep: ImportSessionStep.SELECT 
      });
      return false;
    } finally {
      set({ isLoading: false });
    }
  },
  
  // 更多操作方法...
  
  // 内部方法: 存储会话引用
  _storeSessionReference: (sessionId: string | null) => {
    if (sessionId) {
      sessionStorage.setItem('importSessionId', sessionId);
    } else {
      sessionStorage.removeItem('importSessionId');
    }
  },
  
  // 内部方法: 开始进度轮询
  _startProgressPolling: (sessionId: string) => {
    // 先清除可能存在的旧轮询
    get()._stopProgressPolling();
    
    const pollingId = window.setInterval(async () => {
      try {
        const response = await excelImportService.getExcelImportProgress(sessionId);
        
        if (!response.success) {
          console.error('获取进度失败:', response.error);
          return;
        }
        
        // 更新进度
        set({ 
          progress: response.data.progress,
          currentStep: response.data.status as ImportSessionStep,
          lastServerSync: new Date()
        });
        
        // 状态转换处理
        if (response.data.status === ImportSessionStep.ANALYSIS_COMPLETE) {
          // 分析完成，停止轮询并获取结果
          get()._stopProgressPolling();
          await get().getAnalysisResults();
        } else if (
          response.data.status === ImportSessionStep.CANCELLED ||
          response.data.status === ImportSessionStep.ERROR
        ) {
          // 会话取消或出错，停止轮询
          get()._stopProgressPolling();
          
          if (response.data.status === ImportSessionStep.ERROR) {
            set({ error: response.data.error_message || '分析过程中出现错误' });
          }
        }
      } catch (error) {
        console.error('轮询进度失败:', error);
      }
    }, 2000);
    
    set({ progressPollingId: pollingId });
  },
  
  // 内部方法: 停止进度轮询
  _stopProgressPolling: () => {
    const { progressPollingId } = get();
    if (progressPollingId !== null) {
      window.clearInterval(progressPollingId);
      set({ progressPollingId: null });
    }
  },
  
  // 清理会话
  clearSession: () => {
    // 停止轮询
    get()._stopProgressPolling();
    
    // 清理会话引用
    get()._storeSessionReference(null);
    
    // 重置状态
    set({
      sessionId: null,
      currentStep: ImportSessionStep.SELECT,
      fileName: null,
      fileSize: null,
      progress: 0,
      error: null,
      analysisResults: null,
      processingUser: null
    });
  }
}));
```

### 6.2 组件集成策略

```tsx
// frontend/app/records/import/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useImportSessionStore } from '@/services/import-session-state';
import ExcelImportWithConflictResolution from '@/components/records/import/excel-import-with-conflict-resolution';
import { Loader2, AlertTriangle, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/toast';
import { ImportSessionStep } from '@/services/import-session-state';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export default function ImportPage() {
  const { 
    sessionId, 
    currentStep, 
    fetchCurrentState, 
    processingUser,
    isLoading
  } = useImportSessionStore();
  const [pageLoaded, setPageLoaded] = useState(false);
  const { toast } = useToast();
  
  // 页面加载时获取当前状态
  useEffect(() => {
    const initPage = async () => {
      await fetchCurrentState();
      setPageLoaded(true);
    };
    
    initPage();
  }, [fetchCurrentState]);
  
  // 页面卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理轮询
      useImportSessionStore.getState()._stopProgressPolling();
    };
  }, []);
  
  // 构建当前状态显示
  const renderStatusInfo = () => {
    if (processingUser) {
      return (
        <div className="flex items-center gap-2 text-sm bg-blue-50 p-2 rounded-md text-blue-700 my-2">
          <UserCheck size={16} />
          <span>当前由 <b>{processingUser.username}</b> 处理中</span>
        </div>
      );
    }
    return null;
  };
  
  // 根据状态渲染不同内容
  if (!pageLoaded || isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="mt-2 text-muted-foreground">加载导入状态...</p>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Excel数据导入</h1>
      
      {renderStatusInfo()}
      
      <ExcelImportWithConflictResolution />
    </div>
  );
}
```

## 7. 异常处理与恢复策略

### 7.1 异常类型和错误代码

定义标准化的错误代码，便于前端处理：

```python
# 错误代码枚举
class ImportErrorCode(Enum):
    # 通用错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    SESSION_NOT_FOUND = "SESSION_NOT_FOUND"
    
    # 会话状态错误
    INVALID_STATE_TRANSITION = "INVALID_STATE_TRANSITION"
    SESSION_EXPIRED = "SESSION_EXPIRED"
    ACTIVE_SESSION_EXISTS = "ACTIVE_SESSION_EXISTS"
    
    # 文件处理错误
    FILE_UPLOAD_ERROR = "FILE_UPLOAD_ERROR"
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    
    # 分析和导入错误
    ANALYSIS_ERROR = "ANALYSIS_ERROR"
    IMPORT_ERROR = "IMPORT_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    
    # 并发和锁定错误
    SESSION_LOCKED = "SESSION_LOCKED"
    CONCURRENT_MODIFICATION = "CONCURRENT_MODIFICATION"
```

### 7.2 会话恢复机制

在会话意外中断后，实现自动恢复：

```python
def recover_stuck_sessions():
    """
    恢复卡在中间状态的会话
    定期由调度任务执行
    """
    now = timezone.now()
    timeout_threshold = now - timedelta(minutes=30)
    
    # 查找可能卡住的分析中会话
    stuck_analyzing_sessions = ImportSession.objects.filter(
        status=ImportSessionStatus.ANALYSIS_IN_PROGRESS,
        updated_at__lt=timeout_threshold
    )
    
    # 查找可能卡住的导入中会话
    stuck_importing_sessions = ImportSession.objects.filter(
        status=ImportSessionStatus.IMPORT_IN_PROGRESS,
        updated_at__lt=timeout_threshold
    )
    
    # 处理卡住的分析会话
    for session in stuck_analyzing_sessions:
        # 检查后台任务状态
        task_active = check_analysis_task_active(session.session_id)
        
        if not task_active:
            # 分析任务已不存在，标记为错误
            session.status = ImportSessionStatus.ERROR
            session.error_message = "分析任务异常中断，请重试。"
            session.save()
            
            # 记录操作日志
            SessionOperation.objects.create(
                session=session,
                operation_type='status_change',
                user=None,  # 系统操作
                old_status=ImportSessionStatus.ANALYSIS_IN_PROGRESS,
                new_status=ImportSessionStatus.ERROR,
                details={"reason": "analysis_task_interrupted"}
            )
    
    # 处理卡住的导入会话
    for session in stuck_importing_sessions:
        # 检查后台任务状态
        task_active = check_import_task_active(session.session_id)
        
        if not task_active:
            # 导入任务已不存在，标记为错误
            session.status = ImportSessionStatus.ERROR
            session.error_message = "导入任务异常中断，请重试。"
            session.save()
            
            # 记录操作日志
            SessionOperation.objects.create(
                session=session,
                operation_type='status_change',
                user=None,  # 系统操作
                old_status=ImportSessionStatus.IMPORT_IN_PROGRESS,
                new_status=ImportSessionStatus.ERROR,
                details={"reason": "import_task_interrupted"}
            )
```

### 7.3 前端错误处理策略

```typescript
// 全局状态更新处理
const updateStateFromServerResponse = (response: any) => {
  if (!response.success) {
    // 处理错误
    const errorCode = response.error?.code || 'UNKNOWN_ERROR';
    
    // 处理特定错误
    switch (errorCode) {
      case 'SESSION_EXPIRED':
        toast({
          title: "会话已过期",
          description: "导入会话已过期，请重新开始。",
          variant: "destructive"
        });
        get().clearSession();
        break;
        
      case 'SESSION_LOCKED':
        toast({
          title: "会话被锁定",
          description: `此会话正在被${response.error?.details?.username || '其他用户'}处理。`,
          variant: "warning"
        });
        break;
        
      case 'PERMISSION_DENIED':
        toast({
          title: "权限不足",
          description: "您没有执行此操作的权限。",
          variant: "destructive"
        });
        break;
        
      default:
        // 一般错误
        set({ error: response.error?.message || '操作失败' });
        toast({
          title: "操作失败",
          description: response.error?.message || '未知错误',
          variant: "destructive"
        });
    }
  }
};
```

## 8. 多用户协作模型

### 8.1 基于角色的访问控制

```python
# 权限定义
class ImportSessionPermissions:
    # 基础权限
    VIEW = 'archive_records.view_importsession'
    CREATE = 'archive_records.add_importsession'
    CHANGE = 'archive_records.change_importsession'
    DELETE = 'archive_records.delete_importsession'
    
    # 扩展权限
    PROCESS_ANY = 'archive_records.process_any_importsession'
    TAKEOVER = 'archive_records.takeover_importsession'
    CANCEL_ANY = 'archive_records.cancel_any_importsession'
    FORCE_CLEANUP = 'archive_records.force_cleanup_importsession'

# 权限检查
def can_process_session(user, session):
    """检查用户是否可以处理会话"""
    if user.has_perm(ImportSessionPermissions.PROCESS_ANY):
        return True
    
    # 会话创建者可以处理自己的会话
    return session.created_by == user

def can_takeover_session(user, session):
    """检查用户是否可以接管会话"""
    # 拥有接管权限的用户可以接管任何会话
    if user.has_perm(ImportSessionPermissions.TAKEOVER):
        return True
    
    # 拥有处理权限的用户可以接管超时会话
    if user.has_perm(ImportSessionPermissions.PROCESS_ANY) and session.can_be_taken_over():
        return True
    
    return False
```

### 8.2 会话接管机制

```python
@api_view(['POST'])
@permission_required('archive_records.view_importsession')
def takeover_session(request):
    """接管会话处理权"""
    session_id = request.data.get('session_id')
    
    if not session_id:
        return Response({
            'success': False,
            'error': {
                'code': 'INVALID_REQUEST',
                'message': '缺少会话ID'
            }
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        session = ImportSession.objects.get(session_id=session_id)
        
        # 检查会话是否可接管
        if not session.is_active():
            return Response({
                'success': False,
                'error': {
                    'code': 'SESSION_NOT_ACTIVE',
                    'message': '会话不活跃，无法接管'
                }
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 检查权限
        if not can_takeover_session(request.user, session):
            if session.processing_user:
                return Response({
                    'success': False,
                    'error': {
                        'code': 'SESSION_LOCKED',
                        'message': '会话正在被其他用户处理',
                        'details': {
                            'username': session.processing_user.username,
                            'since': session.last_activity.isoformat()
                        }
                    }
                }, status=status.HTTP_403_FORBIDDEN)
            else:
                return Response({
                    'success': False,
                    'error': {
                        'code': 'PERMISSION_DENIED',
                        'message': '您没有接管此会话的权限'
                    }
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 接管会话
        previous_user = session.processing_user
        session.processing_user = request.user
        session.last_activity = timezone.now()
        session.save()
        
        # 记录操作日志
        SessionOperation.objects.create(
            session=session,
            operation_type='takeover',
            user=request.user,
            details={
                'previous_user_id': previous_user.id if previous_user else None,
                'previous_user_name': previous_user.username if previous_user else None
            }
        )
        
        return Response({
            'success': True,
            'data': {
                'message': '成功接管会话',
                'session_id': session_id
            }
        })
    except ImportSession.DoesNotExist:
        return Response({
            'success': False,
            'error': {
                'code': 'SESSION_NOT_FOUND',
                'message': '会话不存在'
            }
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': {
                'code': 'TAKEOVER_ERROR',
                'message': f'接管会话失败: {str(e)}'
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

## 9. 总结

本架构设计提供了一个健壮的Excel导入会话状态管理解决方案，关键特性包括：

1. **完整的状态模型**：清晰定义了11种状态和转换规则，覆盖整个导入流程。

2. **全局唯一会话**：使用缓存实现全局锁定机制，确保任意时刻只有一个活跃导入会话。

3. **多用户协作支持**：实现会话接管、处理锁定和操作审计，支持团队协作。

4. **服务器为唯一真相来源**：前端状态完全由服务器驱动，避免状态不一致问题。

5. **完善的异常处理**：包含错误代码体系、会话恢复机制和前端错误处理策略。

6. **详细的API设计**：统一的响应格式和完整的端点定义，易于集成和扩展。

7. **完整的前端状态管理**：使用Zustand实现高效状态管理，并提供组件集成策略。

上述设计确保了Excel导入过程的稳定性、可靠性和良好的用户体验。后续实现时，应特别关注状态转换的原子性、并发控制和性能优化。 