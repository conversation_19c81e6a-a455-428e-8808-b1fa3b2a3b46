# 开发环境 Docker Compose 配置
version: '3.8'

services:
  ocr-service-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: archive-flow-ocr-dev
    ports:
      - "8001:8001"
    environment:
      # 开发环境配置
      - OCR_DEBUG=true
      - OCR_LOG_LEVEL=DEBUG
      - OCR_LOG_FORMAT=text
      - OCR_ENABLE_METRICS=true
      - OCR_MAX_CONCURRENT_REQUESTS=5
      # 开发模式下启用所有优化功能
      - OCR_ENABLE_ADAPTIVE=true
      - OCR_ENABLE_UNSHARP_MASK=true
      - OCR_ENABLE_COLOR_ADJUSTMENT=true
      - OCR_ENABLE_QUALITY_CHECK=true
    volumes:
      # 开发时挂载代码目录以支持热重载
      - ./app:/app/app:ro
      - ./logs:/app/logs
    networks:
      - ocr-dev-network
    restart: unless-stopped
    command: [
      "python", "-m", "uvicorn", "app.main:app",
      "--host", "0.0.0.0",
      "--port", "8001",
      "--reload",  # 开发模式热重载
      "--log-level", "debug"
    ]

networks:
  ocr-dev-network:
    driver: bridge
