"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, RefreshCw } from "lucide-react"
import { useState } from "react"
import Link from "next/link"
import { useToast } from "@/components/ui/use-toast"

export function ProcessingTasks() {
  const { toast } = useToast()
  const [isRefreshing, setIsRefreshing] = useState(false)

  // 模拟数据
  const tasks = [
    {
      id: "TASK-2023-001",
      fileName: "环评报告-XYZ项目.pdf",
      uploadTime: "2023-12-20 09:30",
      status: "completed",
      boxNumber: "BOX-001",
      processedItems: 3,
    },
    {
      id: "TASK-2023-002",
      fileName: "安全评估-ABC项目.pdf",
      uploadTime: "2023-12-20 10:15",
      status: "processing",
      boxNumber: "BOX-001",
      processedItems: 0,
    },
    {
      id: "TASK-2023-003",
      fileName: "技术报告-DEF项目.pdf",
      uploadTime: "2023-12-19 16:45",
      status: "failed",
      boxNumber: "BOX-002",
      processedItems: 0,
    },
    {
      id: "TASK-2023-004",
      fileName: "监测报告-GHI项目.pdf",
      uploadTime: "2023-12-19 14:20",
      status: "completed",
      boxNumber: "BOX-002",
      processedItems: 1,
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            已完成
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            处理中
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            失败
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const handleRefresh = () => {
    setIsRefreshing(true)

    // 模拟刷新
    setTimeout(() => {
      setIsRefreshing(false)
      toast({
        title: "刷新成功",
        description: "任务列表已更新",
      })
    }, 1000)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>处理任务</CardTitle>
          <CardDescription>最近的PDF处理任务状态</CardDescription>
        </div>
        <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isRefreshing}>
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
          <span className="sr-only">刷新</span>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>任务ID</TableHead>
                <TableHead>文件名</TableHead>
                <TableHead>盒号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>处理项</TableHead>
                <TableHead className="w-[80px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tasks.map((task) => (
                <TableRow key={task.id}>
                  <TableCell className="font-medium">{task.id}</TableCell>
                  <TableCell className="max-w-[200px] truncate" title={task.fileName}>
                    {task.fileName}
                  </TableCell>
                  <TableCell>{task.boxNumber}</TableCell>
                  <TableCell>{getStatusBadge(task.status)}</TableCell>
                  <TableCell>{task.processedItems}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon" asChild>
                      <Link href={`/archive/tasks/${task.id}`}>
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">查看详情</span>
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 text-center">
          <Button variant="link" asChild>
            <Link href="/archive/tasks">查看所有任务</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
