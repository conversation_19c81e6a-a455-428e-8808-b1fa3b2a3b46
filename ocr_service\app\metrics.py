"""
指标收集模块
"""
import time
import threading
from typing import Dict, Any
from collections import defaultdict, deque
import structlog

logger = structlog.get_logger(__name__)


class MetricsManager:
    """指标管理器"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._processing_times = deque(maxlen=1000)  # 保留最近1000次请求的处理时间
        self._start_time = time.time()
        self._running = False
    
    def start(self):
        """启动指标收集"""
        with self._lock:
            self._running = True
            self._start_time = time.time()
        logger.info("指标收集已启动")
    
    def stop(self):
        """停止指标收集"""
        with self._lock:
            self._running = False
        logger.info("指标收集已停止")
    
    def record_request(self, success: bool, processing_time: float):
        """记录请求指标"""
        if not self._running:
            return
        
        with self._lock:
            self._total_requests += 1
            if success:
                self._successful_requests += 1
            else:
                self._failed_requests += 1
            
            self._processing_times.append(processing_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            if not self._processing_times:
                avg_time = 0.0
            else:
                avg_time = sum(self._processing_times) / len(self._processing_times)
            
            return {
                "total_requests": self._total_requests,
                "successful_requests": self._successful_requests,
                "failed_requests": self._failed_requests,
                "success_rate": (
                    self._successful_requests / self._total_requests 
                    if self._total_requests > 0 else 0.0
                ),
                "average_processing_time": avg_time,
                "uptime": time.time() - self._start_time
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self._total_requests = 0
            self._successful_requests = 0
            self._failed_requests = 0
            self._processing_times.clear()
            self._start_time = time.time()
        logger.info("指标统计已重置")


# 全局指标管理器实例
metrics_manager = MetricsManager()
