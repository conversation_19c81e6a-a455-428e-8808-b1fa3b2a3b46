import NextAuth, { NextAuthOptions, User as NextAuthUserAccount, Account, Profile, Session, User } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import type { BackendUser } from '@/types/next-auth';

// Interfaces are now defined in frontend/types/next-auth.d.ts and augmented globally
// So, we don't need to redefine BackendUser, BackendLoginResponse here.
// CustomNextAuthUser is also effectively defined by the augmented NextAuth.User type

async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    const internalApiUrl = process.env.INTERNAL_API_URL; // For server-to-server calls
    const publicApiUrl = process.env.NEXT_PUBLIC_API_URL; // Fallback or for other contexts
    const apiUrl = internalApiUrl || publicApiUrl; // Prioritize internal URL

    if (!apiUrl) {
      console.error("[NextAuth] REFRESH_TOKEN_ERROR: API_URL (INTERNAL or PUBLIC) is not defined.");
      throw new Error("API_URL is not defined for refresh token request.");
    }
    if (!token.refreshToken) {
        console.error("[NextAuth] REFRESH_TOKEN_ERROR: No refresh token available.");
        return { ...token, error: "NoRefreshToken" };
    }
    const response = await fetch(`${apiUrl}/api/django-auth/token/refresh/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh: token.refreshToken }),
    });
    const refreshedTokens = await response.json();
    if (!response.ok) {
      console.error("[NextAuth] REFRESH_TOKEN_API_ERROR: Status:", response.status, "Body:", refreshedTokens);
      if (response.status === 401 || response.status === 403) {
        return { ...token, error: "InvalidRefreshToken" }; 
      }
      throw refreshedTokens;
    }
    const accessTokenLifetimeSeconds = parseInt(process.env.DJANGO_ACCESS_TOKEN_LIFETIME_SECONDS || '3600');
    return { ...token, accessToken: refreshedTokens.access, refreshToken: refreshedTokens.refresh ?? token.refreshToken, accessTokenExpires: Date.now() + accessTokenLifetimeSeconds * 1000, error: undefined };
  } catch (error) {
    console.error("[NextAuth] REFRESH_ACCESS_TOKEN_FUNCTION_ERROR:", error);
    return { ...token, error: "RefreshAccessTokenError" };
  }
}

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: { 
        username: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req): Promise<User | null> {
        console.log("[NextAuth Authorize] Received credentials:", credentials);
        if (!credentials) {
          console.log("[NextAuth Authorize] No credentials received.");
          return null;
        }
        try {
          const internalApiUrl = process.env.INTERNAL_API_URL;
          const publicApiUrl = process.env.NEXT_PUBLIC_API_URL;
          const apiUrl = internalApiUrl || publicApiUrl; // Prioritize internal for server-side fetch

          console.log("[NextAuth Authorize] Effective API_URL for Django call:", apiUrl);
          if (!apiUrl) {
            console.error("[NextAuth Authorize] ERROR: API_URL (INTERNAL or PUBLIC) is not defined for login request.");
            throw new Error("API URL not configured for login.");
          }

          const loginApiUrl = `${apiUrl}/api/django-auth/login/`;
          console.log("[NextAuth Authorize] Attempting to login to:", loginApiUrl);

          const res = await fetch(loginApiUrl, {
            method: 'POST',
            body: JSON.stringify({ username: credentials.username, password: credentials.password }),
            headers: { "Content-Type": "application/json" }
          });

          console.log("[NextAuth Authorize] Django login API response status:", res.status);

          if (!res.ok) {
            let errorData;
            try {
                // 先获取响应文本，然后尝试解析为JSON
                const responseText = await res.text();
                try {
                    errorData = JSON.parse(responseText);
                } catch (parseError) {
                    console.error("[NextAuth Authorize] Django login API !res.ok, response not JSON:", responseText);
                    errorData = { detail: responseText || "Login failed, server response not valid JSON." };
                }
            } catch (e) {
                console.error("[NextAuth Authorize] Failed to read response body:", e);
                errorData = { detail: `Authentication failed with status: ${res.status}` };
            }
            console.error("[NextAuth Authorize] Django login API error. Status:", res.status, "Data:", errorData);
            throw new Error(errorData.detail || `Authentication failed with status: ${res.status}`);
          }
          
          const data = await res.json() as { access: string; refresh: string; user: BackendUser }; 
          console.log("[NextAuth Authorize] Django login API success data:", data);

          if (data && data.user && data.access && data.refresh) {
            const accessTokenLifetimeSeconds = parseInt(process.env.DJANGO_ACCESS_TOKEN_LIFETIME_SECONDS || '3600');
            const userToReturn: User = {
              id: String(data.user.pk),
              name: data.user.username,
              email: data.user.email,
              accessToken: data.access,
              refreshToken: data.refresh,
              accessTokenExpires: Date.now() + accessTokenLifetimeSeconds * 1000,
              backendUser: data.user,
            };
            console.log("[NextAuth Authorize] Successfully authorized, returning user object for JWT:", userToReturn);
            return userToReturn;
          }
          console.error("[NextAuth Authorize] Unexpected response structure from Django login API:", data);
          return null;
        } catch (e: any) {
          console.error("[NextAuth Authorize] Authorize function exception:", e.message);
          throw new Error(e.message || "An unknown error occurred during authorization.");
        }
      }
    })
  ],
  session: { strategy: 'jwt' },
  jwt: { secret: process.env.NEXTAUTH_SECRET },
  callbacks: {
    async jwt({ token, user, account }): Promise<JWT> {
      // console.log("[NextAuth JWT Callback] Triggered. User:", user, "Account:", account, "Existing Token:", token);
      // Initial sign in
      if (account && user) {
        const authUser = user as User; 
        token.accessToken = authUser.accessToken;
        token.refreshToken = authUser.refreshToken;
        token.accessTokenExpires = authUser.accessTokenExpires;
        token.userId = authUser.id;
        token.name = authUser.name;
        token.email = authUser.email;
        token.backendUser = authUser.backendUser; // Persist backendUser to token
        
        token.error = undefined; 
        console.log("[NextAuth JWT Callback] Initial sign-in, token populated with backendUser (core info only now):", token);
        return token;
      }

      const expires = typeof token.accessTokenExpires === 'number' ? token.accessTokenExpires : 0;
      if (Date.now() < expires) {
        return token;
      }

      if (token.refreshToken) {
        console.log("[NextAuth JWT Callback] Access token expired, attempting refresh...");
        return refreshAccessToken(token);
      }
      
      console.warn("[NextAuth JWT Callback] Access token expired, but no refresh token available or refresh failed.");
      return token; 
    },
    async session({ session, token }): Promise<Session> { 
      if (!session.user) session.user = {}; 
      
      session.user.id = token.userId as string;
      if (token.backendUser) {
        session.user.name = token.backendUser.username;
        session.user.email = token.backendUser.email;
        // Any other core user fields from backendUser you want on session.user can be mapped here
      } else {
        session.user.name = token.name as string | null; 
        session.user.email = token.email as string | null; 
      }
      
      session.accessToken = token.accessToken as string;
      session.error = token.error as string; 
      
      // console.log("[NextAuth Session Callback] Session created/updated (core info only now):", session);
      return session;
    }
  },
  pages: { signIn: '/login' },
  debug: process.env.NODE_ENV === 'development',
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST }; 