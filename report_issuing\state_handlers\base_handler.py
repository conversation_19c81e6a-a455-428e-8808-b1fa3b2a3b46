# This is the base handler for the state machine. 
from abc import ABC, abstractmethod

class InvalidTransitionError(Exception):
    """当在不允许的状态下执行操作时抛出此异常。"""
    pass

class BaseStateHandler(ABC):
    """状态处理器抽象基类"""
    
    def __init__(self, form, user_id):
        self.form = form
        self.user_id = user_id
        # 此处将通过依赖注入传入所需的服务
        # self.form_data_service = ...
        # self.business_service = ...
        # self.audit_service = ...

    def update_draft(self, form_data, items_to_add, items_to_remove):
        raise InvalidTransitionError("当前状态不允许更新发放单")

    def lock(self):
        raise InvalidTransitionError("当前状态不允许锁定")

    def unlock(self):
        raise InvalidTransitionError("当前状态不允许解锁")
    
    def issue(self):
        raise InvalidTransitionError("当前状态不允许归档(发放)")

    def soft_delete(self, reason):
        raise InvalidTransitionError("当前状态不允许软删除")
        
    def hard_delete(self):
        raise InvalidTransitionError("当前状态不允许硬删除") 