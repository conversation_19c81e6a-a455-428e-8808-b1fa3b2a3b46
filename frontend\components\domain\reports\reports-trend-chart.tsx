"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

export function ReportsTrendChart() {
  const [activeTab, setActiveTab] = useState("week")

  // 模拟数据 - 实际应用中应从API获取
  const weekData = [
    { name: "周一", value: 12 },
    { name: "周二", value: 18 },
    { name: "周三", value: 15 },
    { name: "周四", value: 22 },
    { name: "周五", value: 28 },
    { name: "周六", value: 10 },
    { name: "周日", value: 5 },
  ]

  const monthData = [
    { name: "第1周", value: 65 },
    { name: "第2周", value: 78 },
    { name: "第3周", value: 92 },
    { name: "第4周", value: 86 },
  ]

  const yearData = [
    { name: "1月", value: 220 },
    { name: "2月", value: 180 },
    { name: "3月", value: 240 },
    { name: "4月", value: 280 },
    { name: "5月", value: 250 },
    { name: "6月", value: 310 },
    { name: "7月", value: 290 },
    { name: "8月", value: 320 },
    { name: "9月", value: 340 },
    { name: "10月", value: 280 },
    { name: "11月", value: 300 },
    { name: "12月", value: 340 },
  ]

  return (
    <Card className="col-span-2">
      <CardHeader className="flex flex-row items-center">
        <div className="grid gap-2">
          <CardTitle>报告发放趋势</CardTitle>
        </div>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="ml-auto">
          <TabsList>
            <TabsTrigger value="week">本周</TabsTrigger>
            <TabsTrigger value="month">本月</TabsTrigger>
            <TabsTrigger value="year">全年</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={activeTab === "week" ? weekData : activeTab === "month" ? monthData : yearData}>
              <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
              <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
              <Tooltip />
              <Line type="monotone" dataKey="value" stroke="#4f46e5" strokeWidth={2} activeDot={{ r: 6 }} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
