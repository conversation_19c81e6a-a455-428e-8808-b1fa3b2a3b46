"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle2, Clock, Loader2, AlertCircle, Eye, Download } from "lucide-react"

interface ArchiveLedgerTableProps {
  period: "today" | "yesterday" | "week"
}

export function ArchiveLedgerTable({ period }: ArchiveLedgerTableProps) {
  // 模拟台账数据
  const getTodayData = () => [
    {
      id: "AR-2023-11-16-001",
      archiveBoxNumber: "ARC-2023-001",
      fileName: "2023年第三季度财务报表.pdf",
      uploadTime: "2023-11-16 09:15:22",
      processingTime: "2023-11-16 09:18:45",
      status: "completed",
      fileSize: "15.2 MB",
      pageCount: 45,
      splitCount: 3,
      uploadUser: "张三",
    },
    {
      id: "AR-2023-11-16-002",
      archiveBoxNumber: "ARC-2023-001",
      fileName: "产品设计规范V2.0.pdf",
      uploadTime: "2023-11-16 10:30:15",
      processingTime: "2023-11-16 10:35:22",
      status: "completed",
      fileSize: "8.7 MB",
      pageCount: 32,
      splitCount: 1,
      uploadUser: "李四",
    },
    {
      id: "AR-2023-11-16-003",
      archiveBoxNumber: "ARC-2023-002",
      fileName: "市场调研报告.pdf",
      uploadTime: "2023-11-16 11:45:30",
      processingTime: "",
      status: "processing",
      fileSize: "12.1 MB",
      pageCount: 0,
      splitCount: 0,
      uploadUser: "王五",
    },
    {
      id: "AR-2023-11-16-004",
      archiveBoxNumber: "ARC-2023-003",
      fileName: "人力资源管理制度.pdf",
      uploadTime: "2023-11-16 14:20:10",
      processingTime: "",
      status: "pending",
      fileSize: "4.3 MB",
      pageCount: 0,
      splitCount: 0,
      uploadUser: "赵六",
    },
    {
      id: "AR-2023-11-16-005",
      archiveBoxNumber: "ARC-2023-003",
      fileName: "技术研发计划书.pdf",
      uploadTime: "2023-11-16 15:10:45",
      processingTime: "2023-11-16 15:12:30",
      status: "failed",
      fileSize: "7.8 MB",
      pageCount: 28,
      splitCount: 0,
      uploadUser: "钱七",
    },
  ]

  const getYesterdayData = () => [
    {
      id: "AR-2023-11-15-001",
      archiveBoxNumber: "ARC-2023-001",
      fileName: "2023年第二季度财务报表.pdf",
      uploadTime: "2023-11-15 08:30:15",
      processingTime: "2023-11-15 08:35:40",
      status: "completed",
      fileSize: "14.5 MB",
      pageCount: 42,
      splitCount: 2,
      uploadUser: "张三",
    },
    {
      id: "AR-2023-11-15-002",
      archiveBoxNumber: "ARC-2023-002",
      fileName: "产品规划文档.pdf",
      uploadTime: "2023-11-15 10:15:30",
      processingTime: "2023-11-15 10:20:15",
      status: "completed",
      fileSize: "9.2 MB",
      pageCount: 35,
      splitCount: 1,
      uploadUser: "李四",
    },
    {
      id: "AR-2023-11-15-003",
      archiveBoxNumber: "ARC-2023-002",
      fileName: "客户满意度调查.pdf",
      uploadTime: "2023-11-15 13:45:20",
      processingTime: "2023-11-15 13:50:10",
      status: "completed",
      fileSize: "5.8 MB",
      pageCount: 22,
      splitCount: 1,
      uploadUser: "王五",
    },
  ]

  const getWeekData = () => [
    ...getTodayData(),
    ...getYesterdayData(),
    {
      id: "AR-2023-11-14-001",
      archiveBoxNumber: "ARC-2023-001",
      fileName: "项目进度报告.pdf",
      uploadTime: "2023-11-14 09:10:25",
      processingTime: "2023-11-14 09:15:40",
      status: "completed",
      fileSize: "10.3 MB",
      pageCount: 38,
      splitCount: 2,
      uploadUser: "张三",
    },
    {
      id: "AR-2023-11-13-001",
      archiveBoxNumber: "ARC-2023-002",
      fileName: "销售数据分析.pdf",
      uploadTime: "2023-11-13 14:20:35",
      processingTime: "2023-11-13 14:25:50",
      status: "completed",
      fileSize: "8.7 MB",
      pageCount: 30,
      splitCount: 1,
      uploadUser: "李四",
    },
  ]

  // 根据时间段获取数据
  const getData = () => {
    switch (period) {
      case "today":
        return getTodayData()
      case "yesterday":
        return getYesterdayData()
      case "week":
        return getWeekData()
      default:
        return getTodayData()
    }
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" /> 已完成
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 flex items-center gap-1">
            <Loader2 className="h-3 w-3 animate-spin" /> 处理中
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 flex items-center gap-1">
            <Clock className="h-3 w-3" /> 待处理
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" /> 失败
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const data = getData()

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>档案盒号</TableHead>
            <TableHead>文件名</TableHead>
            <TableHead>上传时间</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>文件大小</TableHead>
            <TableHead>页数</TableHead>
            <TableHead>拆分数量</TableHead>
            <TableHead>上传用户</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((record) => (
            <TableRow key={record.id}>
              <TableCell className="font-medium">{record.archiveBoxNumber}</TableCell>
              <TableCell>{record.fileName}</TableCell>
              <TableCell>{record.uploadTime}</TableCell>
              <TableCell>{getStatusBadge(record.status)}</TableCell>
              <TableCell>{record.fileSize}</TableCell>
              <TableCell>{record.pageCount || "-"}</TableCell>
              <TableCell>{record.splitCount}</TableCell>
              <TableCell>{record.uploadUser}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Eye className="h-4 w-4" />
                  </Button>
                  {record.status === "completed" && (
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
