FROM node:20-alpine

# 添加一个非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 增加网络超时时间和重试次数
RUN pnpm config set network-timeout 60000
RUN pnpm config set fetch-retries 3
RUN pnpm config set fetch-retry-mintimeout 10000
RUN pnpm config set fetch-retry-maxtimeout 30000

# 1. 只复制 package.json 和 pnpm-lock.yaml
COPY --chown=nextjs:nodejs package.json pnpm-lock.yaml ./

# 2. 安装依赖
# 这一步生成的 node_modules 将保留在镜像的一个层中
# 使用 --frozen-lockfile 确保精确安装
RUN pnpm install --frozen-lockfile

# 3. 复制剩余的应用代码
# 这一步不会再影响到镜像中已经安装好的 node_modules
COPY --chown=nextjs:nodejs . .

# 4. 在切换用户前，创建 .next 目录并赋予正确所有权
RUN mkdir -p /app/.next && chown -R nextjs:nodejs /app/.next

# 设置正确的权限 (针对 /app 下的其他文件)
RUN chown -R nextjs:nodejs /app

USER nextjs

# 设置环境变量
ENV NODE_ENV=development
ENV NEXT_PUBLIC_API_URL=http://web:8000/api

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["pnpm", "dev"] 