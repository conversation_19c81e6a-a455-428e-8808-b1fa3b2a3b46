import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Edit, Eye } from "lucide-react"
import Link from "next/link"

const recentUsers = [
  {
    id: "u1",
    name: "张三",
    username: "z<PERSON><PERSON>",
    email: "zhang<PERSON>@example.com",
    role: "档案管理员",
    status: "active",
    createdAt: "2023-05-01T08:30:00Z",
  },
  {
    id: "u2",
    name: "李四",
    username: "lisi",
    email: "<EMAIL>",
    role: "报告发放员",
    status: "active",
    createdAt: "2023-05-01T09:15:00Z",
  },
  {
    id: "u3",
    name: "王五",
    username: "wangwu",
    email: "<EMAIL>",
    role: "系统管理员",
    status: "inactive",
    createdAt: "2023-05-02T10:20:00Z",
  },
  {
    id: "u4",
    name: "赵六",
    username: "z<PERSON><PERSON><PERSON>",
    email: "z<PERSON><PERSON><PERSON>@example.com",
    role: "档案管理员",
    status: "active",
    createdAt: "2023-05-02T14:45:00Z",
  },
  {
    id: "u5",
    name: "钱七",
    username: "qianqi",
    email: "<EMAIL>",
    role: "报告发放员",
    status: "active",
    createdAt: "2023-05-03T11:30:00Z",
  },
]

export function RecentUsers() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>用户</TableHead>
          <TableHead>角色</TableHead>
          <TableHead>状态</TableHead>
          <TableHead>添加时间</TableHead>
          <TableHead className="text-right">操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {recentUsers.map((user) => (
          <TableRow key={user.id}>
            <TableCell className="font-medium">
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={`/placeholder.svg?height=32&width=32&text=${user.name.charAt(0)}`}
                    alt={user.name}
                  />
                  <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{user.name}</div>
                  <div className="text-xs text-muted-foreground">{user.email}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>{user.role}</TableCell>
            <TableCell>
              <Badge variant={user.status === "active" ? "default" : "secondary"}>
                {user.status === "active" ? "已激活" : "未激活"}
              </Badge>
            </TableCell>
            <TableCell>{new Date(user.createdAt).toLocaleDateString("zh-CN")}</TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" asChild>
                  <Link href={`/users/detail/${user.id}`}>
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">查看</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="icon" asChild>
                  <Link href={`/users/edit/${user.id}`}>
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">编辑</span>
                  </Link>
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
