# Generated by Django 5.1.11 on 2025-06-22 16:06

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_processing', '0003_reportsplittingtask'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='reportsplittingtask',
            name='archive_record',
        ),
        migrations.AddField(
            model_name='uploadedfile',
            name='status',
            field=models.CharField(choices=[('active', '活跃'), ('deleted', '已删除')], db_index=True, default='active', max_length=20, verbose_name='文件状态'),
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['status'], name='archive_pro_status_adc2c3_idx'),
        ),
        migrations.DeleteModel(
            name='ReportSplittingTask',
        ),
    ]
