# 档案台账发放管理功能 - 详细需求规格说明

## 1. 功能概述

在档案台账界面直接管理发放操作，允许用户选择档案记录并创建发放单，同时支持单个和批量操作。

## 2. 用户界面详细设计

### 2.1 档案台账界面新增功能

1. **选择操作区域**：
   - 添加复选框到每条档案记录前，支持多选功能
   - 增加"全选"和"取消全选"按钮
   - 显示已选记录数量计数器（如："已选择 15 条记录"）

2. **批量操作工具栏**：
   - 当有记录被选中时显示
   - 包含"批量创建发放单"按钮
   - 显示当前选中记录数量

3. **状态标识**：
   - 在每条记录旁显示发放状态图标
   - 已有第一次发放记录：显示"1"标记
   - 已有第二次发放记录：显示"2"标记
   - 已发满所有份数：显示特殊标记（如发放完毕图标）

### 2.2 创建发放单弹窗

1. **基本信息区域**：
   - 发放日期选择器（默认当天）
   - 领取人姓名、单位、联系电话输入框
   - 备注文本框

2. **发放类型选择区域**：
   - 单选按钮：
     - "发放1份"选项
     - "发放全部"选项
   - 每个选项旁显示详细说明：
     - 发放1份：固定发放1份报告，无论之前是否已发放
     - 发放全部：计算剩余可发份数（默认总份数3份 - 已发放份数），至少发放1份

3. **选中记录预览区域**：
   - 表格显示所有选中的档案记录
   - 列：档号、委托单位、工程名称、归档日期、发放状态
   - 对不符合当前发放类型条件的记录显示警告标识
   - 支持在此区域取消选择部分记录

4. **发放信息预览**：
   - 根据选择的发放类型，显示每条记录实际将发放的份数
   - 汇总信息：总发放份数、发放单包含的记录数等

5. **操作按钮**：
   - "创建发放单"按钮
   - "取消"按钮

## 3. 业务逻辑详细说明

### 3.1 档案记录筛选逻辑

1. **可发放记录条件**：
   - ~~档案状态必须为"已归档"~~ (**已移除此限制** - 所有档案都可以发放)
   - 记录不能被标记为删除

2. **发放1份（单份发放）的筛选逻辑**：
   - 不能已有活跃的第一次发放记录（`issue_type='first'，is_active=True，is_deleted=False`）
   - 系统将自动识别并过滤掉已有第一次发放记录的档案

3. **发放全部（剩余发放）的筛选逻辑**：
   - 不能已有活跃的第二次发放记录（`issue_type='second'，is_active=True，is_deleted=False`）
   - 可以没有第一次发放记录（这时将作为一次性全部发放）
   - 也可以已有第一次发放记录（这时将作为第二次发放剩余份数）

### 3.2 发放份数计算逻辑

1. **发放1份（`issue_type='single'`）**：
   - 固定发放1份
   - 实际发放类型(`issue_type`)将被设置为"first"
   - 不考虑已发放情况，始终为1

2. **发放全部（`issue_type='all_remaining'`）**：
   - 计算逻辑：

     ```说明
     总份数 = archive_record.total_issue_copies 或默认值 3
     已发放份数 = 该档案的所有活跃发放记录的 copies 字段之和
     剩余可发放份数 = max(1, 总份数 - 已发放份数)
     ```

   - 即使计算结果为0或负数，也至少发放1份
   - 实际发放类型(`issue_type`)确定方式：
     - 如果该档案没有活跃的第一次发放记录：设为"first"
     - 如果该档案已有活跃的第一次发放记录：设为"second"

3. **发放记录状态的相互影响**：
   - 当创建新的发放记录后，会根据发放份数，更新档案记录的发放状态
   - 系统自动跟踪每个档案的总发放份数和剩余可发放份数

### 3.3 批量添加到发放单的流程

1. **用户选择多条档案记录**：
   - 选择符合条件的档案记录
   - 点击"批量创建发放单"

2. **填写发放单基本信息**：
   - 填写发放日期、领取人信息等
   - 选择发放类型（1份或全部）

3. **系统验证选中记录**：
   - 检查每条记录是否符合当前发放类型的条件
   - 显示不符合条件的记录，并允许用户排除或继续

4. **预览发放信息**：
   - 显示每条记录实际将发放的份数
   - 显示发放单总信息

5. **创建发放单**：
   - 系统创建发放单主记录
   - 为每条选中的档案记录创建发放单条目

### 3.4 错误处理与边界情况

1. **无符合条件的记录**：
   - 当选中的记录都不符合发放条件时，显示明确错误信息
   - 建议用户更改发放类型或选择其他记录

2. **并发操作冲突**：
   - 在用户查看列表到提交发放单期间，如果其他用户已创建了冲突的发放记录
   - 系统在提交时再次验证，并显示具体哪些记录发生了冲突

3. **份数超限处理**：
   - 当发放后总份数超过预设限制（例如3份）时，系统仍允许发放
   - 但会显示警告提示，并记录超发情况

## 4. 数据模型交互

### 4.1 相关模型

1. **ArchiveRecord（档案记录）**：
   - 记录基本档案信息
   - 包含`archive_status`字段表示归档状态
   - 包含`total_issue_copies`字段表示总份数（默认值为3）

2. **IssueForm（发放单）**：
   - 包含发放日期、领取人信息
   - 状态流转：draft（草稿）→ confirmed（已确认）→ archived（已归档）
   - 关联多个IssueFormItem条目

3. **IssueFormItem（发放单条目）**：
   - 关联到特定ArchiveRecord
   - 包含`issue_type`（发放类型）和`copies`（发放份数）
   - 包含备注等附加信息

4. **IssueRecord（发放记录）**：
   - 发放单归档后生成
   - 包含实际发放类型（first或second）
   - 记录发放历史和份数信息

### 4.2 API交互流程

1. **查询可发放档案记录**：
   - 根据筛选条件和发放类型获取符合条件的档案记录
   - URL: `/api/issue-forms/available_records/`
   - 参数：
     - `issue_type`: 发放类型（single或all_remaining）
     - 其他筛选参数：如`unified_number`、`project_name`等

2. **创建发放单**：
   - URL: `/api/issue-forms/`
   - 方法：POST
   - 数据：包含基本发放单信息（不含条目）

3. **添加条目到发放单**：
   - 单个添加：
     - URL: `/api/issue-forms/{form_id}/add_item/`
     - 方法：POST
     - 数据：`archive_record_id`、`issue_type`、备注等

   - 批量添加：
     - URL: `/api/issue-forms/{form_id}/add_items/`
     - 方法：POST
     - 数据：条目列表，每项包含`archive_record_id`、`issue_type`、备注等

4. **确认发放单**：
   - URL: `/api/issue-forms/{form_id}/confirm/`
   - 方法：POST

5. **归档发放单**：
   - URL: `/api/issue-forms/{form_id}/archive/`
   - 方法：POST
   - 可选参数：confirmation_file（确认单文件）

## 5. 界面交互流程

### 5.1 从台账创建发放单流程

1. **档案台账界面操作**：
   - 用户浏览档案台账记录
   - 勾选需要发放的档案记录（单个或多个）
   - 点击"批量创建发放单"按钮

2. **创建发放单弹窗**：
   - 系统弹出创建发放单界面
   - 用户填写基本信息
   - 选择发放类型（1份或全部）
   - 预览发放信息并确认

3. **确认创建**：
   - 用户点击"创建发放单"按钮
   - 系统验证所有数据
   - 创建发放单及相关条目

4. **结果反馈**：
   - 显示成功创建的消息
   - 提供查看发放单详情的链接
   - 返回台账界面，刷新选中记录的发放状态显示

### 5.2 发放状态查询与展示

1. **档案发放状态显示**：
   - 在台账列表中通过图标或标记显示每条记录的发放状态
   - 鼠标悬停时显示详细发放信息（已发份数/总份数等）

2. **发放历史查询**：
   - 点击记录的发放状态图标
   - 弹出该档案的发放历史详情面板
   - 显示每次发放的日期、领取人、份数等信息

## 6. 权限控制

1. **创建发放单权限**：
   - 需要"创建发放单"权限
   - 用户组：档案管理员、报告发放员等

2. **查看发放记录权限**：
   - 需要"查看发放记录"权限
   - 用户组：所有档案系统用户

3. **确认与归档发放单权限**：
   - 需要"确认发放单"和"归档发放单"权限
   - 用户组：档案管理员、部门主管等

## 7. 错误信息与用户提示

1. **筛选条件无匹配**：
   - 提示："没有找到符合条件的档案记录"
   - 建议："请尝试修改筛选条件或选择其他发放类型"

2. **无法添加到发放单**：
   - 提示："以下档案记录无法添加到发放单"
   - 详情：列出每条记录无法添加的具体原因

3. **发放单创建成功**：
   - 提示："发放单已成功创建"
   - 详情："包含X条档案记录，共Y份报告"

4. **超出常规份数**：
   - 警告："注意：某些档案记录的发放份数已超过常规限制(3份)"
   - 确认："是否确认继续发放？"

## 8. 技术实现考虑

### 8.1 前端实现细节

1. **组件设计**：
   - 可重用的档案记录选择组件
   - 发放状态标签组件
   - 发放单预览组件
   - 批量操作工具栏组件

2. **状态管理**：
   - 使用状态管理存储选中的记录ID
   - 缓存发放单创建过程数据，支持分步骤操作
   - 处理页面刷新后的状态恢复

3. **响应式设计**：
   - 桌面端优化界面（宽屏布局）
   - 平板端适配（中等屏幕布局）
   - 确保在不同设备上的可用性

### 8.2 后端优化

1. **查询优化**：
   - 为档案记录发放状态相关字段添加索引
   - 使用JOIN查询优化发放记录获取
   - 实现结果缓存，减少重复计算

2. **事务处理**：
   - 确保批量添加操作的原子性
   - 发放单创建与条目添加应在同一事务中完成
   - 防止并发操作冲突的锁机制

3. **批量处理**：
   - 批量创建发放单条目的优化
   - 批量更新档案记录状态
   - 通过批处理减少数据库操作次数

## 9. 数据报表与统计功能

### 9.1 发放统计报表

1. **时间维度统计**：
   - 按日/周/月/年统计发放单数量
   - 按日/周/月/年统计发放报告份数
   - 趋势图显示发放量变化

2. **接收方维度统计**：
   - 按接收单位统计发放数量
   - 按接收人统计发放数量
   - 生成接收频率最高的单位/个人排行榜

3. **档案类型统计**：
   - 按工程类型统计发放情况
   - 按委托单位统计发放数量
   - 特定档案类型的发放趋势分析

### 9.2 导出功能

1. **发放记录导出**：
   - 支持Excel格式导出发放记录清单
   - PDF格式的发放统计报告
   - 自定义导出字段与时间范围

2. **确认单生成**：
   - 自动生成发放确认单PDF文档
   - 包含发放单详情和领取人确认区域
   - 支持打印功能

## 10. 扩展功能与未来规划

### 10.1 近期扩展功能

1. **高级筛选功能**：
   - 组合条件筛选界面
   - 保存常用筛选条件
   - 智能筛选推荐

2. **批量操作扩展**：
   - 批量更新发放状态
   - 批量标记特殊处理情况
   - 批量导出选中记录

3. **通知机制**：
   - 发放单状态变更通知
   - 领取提醒功能
   - 超期未领取提醒

### 10.2 中长期规划

1. **与外部系统集成**：
   - 与OA系统集成，支持流程审批
   - 与短信平台集成，支持短信通知
   - 与电子档案系统集成

2. **电子签名功能**：
   - 支持领取人电子签名
   - 数字签名验证
   - 电子发放记录的法律效力支持

3. **智能辅助功能**：
   - 基于历史数据的发放预测
   - 智能填充发放信息
   - 异常发放行为检测

## 11. 测试与验证方案

### 11.1 功能测试用例

1. **档案台账发放操作测试**：
   - 单条记录发放操作
   - 多条记录批量发放
   - 边界情况（如所有记录都不符合条件）

2. **发放份数计算测试**：
   - 首次发放1份测试
   - 首次发放全部测试
   - 已有第一次发放记录后发放剩余测试
   - 已发满份数后继续发放测试

3. **发放单流程测试**：
   - 创建->确认->归档完整流程
   - 中途取消或修改测试
   - 发放单删除与恢复测试

### 11.2 性能与并发测试

1. **批量操作性能测试**：
   - 50/100/500条记录批量添加测试
   - 响应时间与资源占用监控
   - 优化阈值确定

2. **并发操作测试**：
   - 多用户同时操作同一发放单
   - 多用户同时发放同一档案记录
   - 锁机制与冲突解决验证

## 12. 安全与审计

### 12.1 安全控制

1. **数据访问控制**：
   - 基于角色的访问控制(RBAC)
   - 字段级的权限控制
   - 敏感操作二次确认

2. **操作审核**：
   - 记录所有关键操作日志
   - 包含操作人、时间、IP地址等信息
   - 支持审计追踪

### 12.2 数据历史与追溯

1. **发放记录历史**：
   - 完整记录每次发放操作
   - 保留修改历史与原因
   - 支持历史记录对比

2. **状态变更跟踪**：
   - 跟踪每个发放单的状态变更
   - 记录每次变更的操作人与时间
   - 支持还原到历史状态（管理员功能）
