# 统一认证授权与权限管理指南

本文档旨在提供项目中现代化、健壮的统一认证授权与权限管理系统的设计理念、核心组件、配置方法及最佳实践。

## 当前实现状况 (截至最新更新)

### ✅ 已完成功能

1. **基础认证体系**
   - ✅ JWT认证流程完整实现 (前端NextAuth.js + 后端Django REST framework)
   - ✅ 用户登录/登出功能正常运行
   - ✅ 会话管理和Token刷新机制已建立
   - ✅ API请求自动携带认证Token
   - ✅ 路由级别的登录保护 (通过Next.js中间件实现)
   - ✅ 前后端认证联调完成并通过全面测试

2. **安全基础设施**
   - ✅ HTTPS强制启用
   - ✅ CSRF保护机制
   - ✅ 安全的Cookie配置
   - ✅ JWT签名验证和过期控制

3. **开发环境支持**
   - ✅ `PERMISSION_MODE` 环境变量支持 (debug/prod模式切换)
   - ✅ Docker环境下的权限调试机制
   - ✅ API认证测试工具和流程

### 🔄 当前阶段：细粒度权限控制设计

**现状**: 目前系统具备完整的"登录保护"能力，即区分"已登录用户"和"未登录用户"，但尚未实现基于角色、权限的细粒度访问控制。

**下一步计划**:

1. **选择并集成成熟的第三方权限管理库**
   - **`django-guardian`**: 提供对象级权限，适合需要精细化数据访问控制的场景
     - 优势: 成熟稳定、与现有django-allauth兼容性好、支持复杂的对象权限逻辑
     - 考虑: 学习成本中等、对于简单权限需求可能过于复杂
   - **`django-role-permissions`**: 简化的角色权限管理
     - 优势: 简单易用、快速上手、适合中小型项目
     - 考虑: 功能相对简单、扩展性有限
   - **Django内置Groups和Permissions系统**: 官方标准方案
     - 优势: 零额外依赖、与DRF完美集成、长期维护保证
     - 考虑: 需要更多自定义代码、对象级权限需额外实现

   **倾向选择**: 基于项目当前技术栈的成熟度和团队维护能力，倾向于使用**Django内置系统 + 部分自定义权限类**的组合方案，确保可控性和可维护性。

2. **权限模型设计**
   - 定义系统角色 (管理员、审计员、普通用户等)
   - 设计权限粒度 (页面级、功能级、数据级)
   - 建立权限与业务模块的映射关系

3. **实施计划**
   - 后端API权限类定义和应用
   - 前端组件级权限控制
   - 权限数据在前后端的同步机制

### 📋 待实现的权限功能

- [ ] 基于角色的访问控制 (RBAC)
- [ ] 对象级权限控制 (针对具体记录、档案等)
- [ ] 功能级权限控制 (针对创建、编辑、删除、导出等操作)
- [ ] 前端UI元素的权限显示/隐藏
- [ ] 权限继承和权限组合逻辑
- [ ] 权限审计和日志记录
- [ ] 多因素认证 (MFA) 集成

## 1. 设计目标与核心理念

本系统的核心目标是为企业内部管理系统提供一个安全、可靠、易于维护且可扩展的认证授权解决方案，特别关注公网访问场景下的安全性。

- **安全性优先**: 采用业界推荐的安全实践和成熟库，防范常见Web攻击。
- **标准化与现代化**: 使用OAuth 2.0/JWT等标准，并结合现代前端框架特性。
- **职责分离**: 前后端在认证授权流程中各司其职，清晰界定。
- **可维护性**: 通过成熟库减少自定义代码，降低维护成本。
- **用户体验**: 提供流畅的登录、登出和会话管理体验，并为需要权限控制的场景提供清晰的机制。

## 2. 核心技术组件

- **前端 (Next.js)**:
  - `NextAuth.js (Auth.js)`: 负责处理客户端的用户会话管理、与后端认证API的交互、安全的Cookie管理、CSRF保护以及提供便捷的React Hooks (`useSession`, `signIn`, `signOut`)。
- **后端 (Django & Django REST framework)**:
  - `django-allauth`: 提供全面的本地账户管理功能（安全密码、邮件验证、登录尝试限制、MFA基础等）。
  - `dj-rest-auth`: 在 `django-allauth` 基础上提供一套完整的RESTful API认证端点（登录、登出、用户信息、密码管理、JWT签发与刷新等）。
  - `djangorestframework-simplejwt`: 作为JWT的实际提供者，负责JWT的生成与验证，被 `dj-rest-auth` 集成使用。

## 3. 后端权限管理 (Django)

Django后端负责定义和强制执行API级别的访问权限。

### 3.1. API认证基础

- 所有受保护的API端点都将通过JWT进行认证。
- 在 `settings.py` 的 `REST_FRAMEWORK` 配置中，`DEFAULT_AUTHENTICATION_CLASSES` 应主要设置为 `rest_framework_simplejwt.authentication.JWTAuthentication`。

### 3.2. API授权与权限控制

- **Django REST framework (DRF) 权限类**: 这是后端API访问控制的核心。
  - 在每个DRF视图（View或ViewSet）中，通过设置 `permission_classes` 属性来定义访问该视图所需的权限。
  - 常用的内置权限类：
    - `rest_framework.permissions.IsAuthenticated`: 要求用户必须已登录（即请求必须带有有效的JWT）。
    - `rest_framework.permissions.IsAdminUser`: 要求用户必须是管理员 (`user.is_staff=True`)。
    - `rest_framework.permissions.AllowAny`: 允许任何访问（用于公共API）。
- **自定义权限类**: 对于更复杂的业务逻辑（如基于角色的权限、对象级权限），可以创建自定义权限类，继承自 `rest_framework.permissions.BasePermission` 并实现 `has_permission(self, request, view)` 和/或 `has_object_permission(self, request, view, obj)` 方法。

  ```python
  # example_app/permissions.py
  from rest_framework.permissions import BasePermission

  class IsProjectOwner(BasePermission):
      def has_object_permission(self, request, view, obj):
          # 假设 obj 是一个项目实例，并且有 owner 字段
          return obj.owner == request.user
  ```

- **`PERMISSION_MODE` 环境变量与 `@permission_mode_aware` 装饰器 (可选的开发便利性)**:
  - 项目中现有的基于 `PERMISSION_MODE` (`debug` 或 `prod`) 动态调整权限的机制可以保留和调整，以服务于开发和生产环境的不同需求。
  - 在 `debug` 模式下，`@permission_mode_aware` 装饰器可以为视图设置一个较宽松的权限集，例如仅要求 `IsAuthenticated`，允许开发人员用任意有效账户访问所有API，便于调试。
  - 在 `prod` 模式下，则会应用预定义的、更严格的权限类（如自定义的角色权限或对象权限）。
  - `archive_flow_manager/permissions.py` 中的 `get_permission_classes()` 函数可以根据 `PERMISSION_MODE` 返回不同的权限列表。

      ```python
      # archive_flow_manager/permissions.py
      from rest_framework.permissions import IsAuthenticated
      # from .custom_permissions import IsAuditorRole, CanEditSpecificResource 
      # (假设的自定义权限)
      PERMISSION_MODE = getattr(settings, 'PERMISSION_MODE', 'prod')

      def get_permission_classes_for_view(view_name: str):
          if PERMISSION_MODE == 'debug':
              return [IsAuthenticated]
          else:
              if view_name == 'SensitiveDataView':
                  # return [IsAuthenticated, IsAuditorRole, CanEditSpecificResource]
                  return [IsAuthenticated] # 生产环境也先只要求登录
              return [IsAuthenticated]

      # @permission_mode_aware 装饰器内部会调用类似上面的逻辑
      ```

  - **注意**: 即使在 `debug` 模式下，所有API也应至少要求 `IsAuthenticated`，不应允许匿名访问受保护资源。

### 3.3. 账户安全特性 (由 `django-allauth` 提供)

- **密码策略**: Django本身及 `django-allauth` 提供了密码验证器，可在 `settings.py` 中配置 `AUTH_PASSWORD_VALIDATORS` 来强制密码复杂度。
- **登录尝试限制**: `django-allauth` 通过 `ACCOUNT_RATE_LIMITS` (例如 `ACCOUNT_RATE_LIMITS = {'login_failed': '5/m'}`) 提供登录失败尝试次数限制，防止暴力破解。
- **(未来) MFA基础**: `django-allauth` 支持TOTP等MFA方法，为将来启用MFA打下基础。

## 4. 前端权限管理 (Next.js + NextAuth.js)

前端负责用户界面的展示和用户交互，权限管理主要体现在控制用户能看到什么和能做什么。

### 4.1. 会话管理

- 由 `NextAuth.js` 自动处理。采用JWT策略时，NextAuth.js会为用户创建一个会话JWT，并通常将其存储在安全的HttpOnly Cookie中。
- 客户端通过 `useSession()` Hook访问会话状态 (`data`, `status`)。

### 4.2. 路由保护 (页面级访问控制)

- **Next.js中间件 (`frontend/middleware.ts`)**: 这是保护页面路由的首选方式。

  ```typescript
  // frontend/middleware.ts
  export { default } from "next-auth/middleware"

  export const config = {
    matcher: [
      // 列出所有需要登录才能访问的路径模式
      // 例如: "/dashboard/:path*", "/profile", "/reports/:path*", "/records/:path*", "/archive/:path*", ...
      // 确保排除公共路径如 /login, /api/auth/* 等
      '/((?!api/|_next/static|_next/image|assets|public|favicon.ico|login|register).*)', // 示例
    ]
  }
  ```

- 未认证用户访问受保护路径时，中间件会自动将其重定向到登录页（在 `NextAuthOptions` 的 `pages.signIn` 中配置，例如 `'/login'`）。

### 4.3. 组件级内容/UI保护 (功能级访问控制)

- 在客户端组件中，使用 `useSession()` Hook：

  ```typescript
  // frontend/components/MyProtectedComponent.tsx
  "use client";
  import { useSession } from "next-auth/react";
  import Link from "next/link";

  export default function MyProtectedComponent() {
    const { data: session, status } = useSession();

    if (status === "loading") {
      return <p>Loading session...</p>;
    }

    if (status === "unauthenticated") {
      return <p>Access Denied. Please <Link href="/login">login</Link>.</p>;
    }

    // status === "authenticated"
    // 可以进一步基于 session.user 中的角色或权限控制UI元素
    // const userPermissions = (session.user as any)?.permissions || []; 
    // if (!userPermissions.includes('can_view_this_button')) return null;

    return <div>Sensitive content for authenticated users. Welcome {session.user?.name}</div>;
  }
  ```

- **`hasPermission` 辅助函数 (客户端)**: 可以根据需要封装一个客户端的 `hasPermission` 函数，它接收 `session.user` 对象和所需权限作为参数，返回布尔值。这需要确保用户角色和权限信息在 `session.user` 中可用。

### 4.4. API请求认证

- 前端API客户端 (`useApiClient` 返回的 `fetchApi`) 会从 `useSession()` 获取 `session.accessToken`。
- 该 `accessToken` 会自动附加到所有对后端受保护API的请求的 `Authorization: Bearer <token>` 头中。
- `NextAuth.js` 的 `jwt` 和 `session` 回调负责将从后端获取的 `access` token 存储到NextAuth.js自己的JWT中，并使其在客户端的 `session` 对象中可用 (例如 `session.accessToken`)。

## 5. 角色和权限数据流

1. **后端定义**: 用户的角色和具体权限应在Django后端定义（例如，通过Django的`Group`和`Permission`模型，或自定义的角色/权限系统）。
2. **通过API暴露**: 当用户登录成功后，Django后端的 `/api/auth/login/` 端点返回的 `user` 对象（或后续通过 `/api/auth/user/` 获取的用户详情）中应包含用户的角色和/或权限列表。

    ```json
    // 示例 /api/auth/login/ 或 /api/auth/user/ 的响应
    {
      "pk": 1,
      "username": "root",
      "email": "<EMAIL>",
      "roles": ["admin", "editor"],
      "permissions": ["view_records", "edit_reports"]
      // ... access 和 refresh tokens (仅登录时)
    }
    ```

3. **NextAuth.js JWT存储**: 在 `frontend/app/api/auth/[...nextauth]/route.ts` 的 `authorize` 函数获取到这些信息后，通过返回对象传递给 `jwt` 回调。`jwt` 回调将这些角色/权限存入NextAuth.js的JWT中。

    ```typescript
    // [...nextauth]/route.ts - jwt callback
    async jwt({ token, user }) {
      if (user) { // Initial sign in
        token.userId = user.id;
        token.accessToken = (user as any).accessToken;
        token.roles = (user as any).backendUser?.roles; // 假设从authorize返回的user对象上有backendUser.roles
        token.permissions = (user as any).backendUser?.permissions;
      }
      return token;
    }
    ```

4. **NextAuth.js Session暴露**: `session` 回调从 `token` 中读取角色/权限，并将其放入客户端可访问的 `session.user` 对象中。

    ```typescript
    // [...nextauth]/route.ts - session callback
    async session({ session, token }) {
      if (session.user) {
        (session.user as any).id = token.userId;
        (session.user as any).roles = token.roles;
        (session.user as any).permissions = token.permissions;
      }
      session.accessToken = token.accessToken; 
      return session;
    }
    ```

5. **前端类型定义**: 在 `frontend/types/next-auth.d.ts` 中扩展 `User` 和 `Session` 类型以包含 `roles` 和 `permissions` 属性。
6. **前端使用**: 组件中通过 `useSession().data?.user.roles` 或 `useSession().data?.user.permissions` 访问，并据此实现 `hasPermission` 逻辑。

## 6. 配置与使用

### 6.1. 后端 (`settings.py`)

- `INSTALLED_APPS`: 包含 `rest_framework`, `rest_framework.authtoken`, `rest_framework_simplejwt`, `allauth`, `allauth.account`, `dj_rest_auth`。
- `AUTHENTICATION_BACKENDS`: 包含 `allauth.account.auth_backends.AuthenticationBackend` 和 `django.contrib.auth.backends.ModelBackend`。
- `SITE_ID = 1`.
- `ACCOUNT_*` 配置 (for `django-allauth`): 如 `ACCOUNT_LOGIN_METHODS`, `ACCOUNT_EMAIL_VERIFICATION`, `ACCOUNT_RATE_LIMITS` 等。
- `REST_FRAMEWORK`: `DEFAULT_AUTHENTICATION_CLASSES` 设置为 `['rest_framework_simplejwt.authentication.JWTAuthentication', ...]`。
- `SIMPLE_JWT`: 配置JWT生命周期、签名密钥等。
- `REST_AUTH`: 配置 `USE_JWT = True`, `SESSION_LOGIN = False`, `JWT_AUTH_COOKIE = None` 等，确保JWT通过响应体返回。

### 6.2. 后端 (`urls.py` - Root)

- 包含 `path('api/auth/', include('dj_rest_auth.urls'))`。
- (可选) `path('api/auth/registration/', include('dj_rest_auth.registration.urls'))`。
- (可选) `path('accounts/', include('allauth.urls'))`。

### 6.3. 前端 (`frontend/app/api/auth/[...nextauth]/route.ts`)

- 配置 `authOptions`：
  - `CredentialsProvider` 调用Django的 `/api/auth/login/`，处理 `access`, `refresh`, `user` 响应。
  - `session`策略为 `jwt`。
  - `callbacks.jwt` 存储后端token和用户信息到NextAuth JWT。
  - `callbacks.session` 将必要信息从NextAuth JWT暴露给客户端会话。
  - `pages.signIn` 指向自定义登录页 `/login`。

### 6.4. 前端 (`frontend/app/layout.tsx` & `frontend/app/client-layout.tsx`)

- 在客户端布局 (`client-layout.tsx` 或直接在 `layout.tsx` 如果它是客户端组件) 的根部使用 `<SessionProvider>` 包裹应用。

### 6.5. 环境变量

- **后端 (Docker)**: `PERMISSION_MODE` (可选，用于动态权限切换)。
- **前端 (Docker/`.env.local`)**: `NEXT_PUBLIC_API_URL` (Nginx入口, e.g., `http://localhost`), `INTERNAL_API_URL` (Django服务名, e.g., `http://web:8000`), `NEXTAUTH_URL` (前端应用URL, e.g., `http://localhost`), `NEXTAUTH_SECRET` (强随机密钥), `DJANGO_ACCESS_TOKEN_LIFETIME_SECONDS`。

## 7. 最佳实践

- **后端**:
  - 尽可能使用DRF内置的或成熟库提供的权限类。
  - 自定义权限类时，逻辑应清晰、高效。
  - 对敏感操作使用严格的权限检查。
  - (如果使用 `PERMISSION_MODE`) 生产环境务必确保 `PERMISSION_MODE=prod`。
- **前端**:
  - 使用NextAuth.js中间件进行集中的路由保护。
  - 在组件中使用 `useSession()` 进行细粒度的UI控制和条件渲染。
  - 将权限检查逻辑封装在可复用的辅助函数或自定义Hook中。
  - 不要在客户端硬编码敏感的权限逻辑；权限判断应基于从后端安全获取的用户角色/权限信息。
- **通用**:
  - 强制HTTPS。
  - 定期审查和更新所有认证授权相关的库和配置。
  - 启用并监控认证相关的日志。
  - 考虑为关键操作和高权限用户启用MFA。

本文档提供了新认证授权体系的概览和关键配置点。具体的实现细节请参考各库的官方文档和项目代码。

## 8. 实施路线图

基于当前已完成的登录保护基础，以下是细粒度权限控制的具体实施计划：

### 阶段一：权限库选型与集成 (预计1-2周)

**任务清单**:

1. **技术调研**
   - [ ] 评估 `django-guardian` 的对象级权限能力
   - [ ] 评估 `django-role-permissions` 的角色管理便利性
   - [ ] 对比Django原生Groups/Permissions系统的适用性
   - [ ] 考虑项目复杂度和维护成本

2. **权限模型设计**
   - [ ] 定义业务角色 (超级管理员、部门管理员、审计员、操作员、只读用户)
   - [ ] 梳理权限粒度矩阵:
     - **模块级**: 记录管理、档案管理、报告管理、变更单管理、用户管理等
     - **操作级**: 查看、创建、编辑、删除、导出、审批等
     - **数据级**: 自己的数据 vs 部门数据 vs 全局数据
   - [ ] 设计权限继承规则

3. **初步集成**
   - [ ] 安装选定的权限库
   - [ ] 配置基础权限模型
   - [ ] 创建数据库迁移文件

### 阶段二：后端API权限控制实现 (预计2-3周)

**任务清单**:

1. **权限类开发**
   - [ ] 实现自定义权限类 (继承 `BasePermission`)
   - [ ] 为每个业务模块定义对应的权限检查逻辑
   - [ ] 实现对象级权限检查 (`has_object_permission`)

2. **API端点权限应用**
   - [ ] 更新所有API视图的 `permission_classes`
   - [ ] 实现基于用户角色的访问控制
   - [ ] 添加数据过滤逻辑 (用户只能看到有权限的数据)

3. **权限管理API**
   - [ ] 实现用户权限查询端点 (`/api/auth/permissions/`)
   - [ ] 实现角色管理端点 (仅管理员可用)
   - [ ] 确保权限变更能实时反映到用户会话

### 阶段三：前端权限控制实现 (预计2周)

**任务清单**:

1. **权限数据同步**
   - [ ] 更新 NextAuth.js 配置，在用户登录时获取完整权限信息
   - [ ] 扩展前端类型定义，包含角色和权限字段
   - [ ] 实现权限数据的本地缓存和刷新机制

2. **组件级权限控制**
   - [ ] 开发 `hasPermission` 和 `hasRole` 辅助函数
   - [ ] 创建权限保护组件 (`<PermissionGuard>`)
   - [ ] 为所有敏感UI元素添加权限检查

3. **页面级权限细化**
   - [ ] 基于角色和权限细化路由保护
   - [ ] 实现功能级访问控制 (如编辑按钮、删除按钮的显示/隐藏)
   - [ ] 添加权限不足时的友好提示页面

### 阶段四：测试与优化 (预计1周)

**任务清单**:

1. **权限测试**
   - [ ] 编写自动化测试，覆盖各种权限场景
   - [ ] 进行跨角色的手工测试
   - [ ] 验证权限边界和异常情况处理

2. **性能优化**
   - [ ] 优化权限查询的数据库性能
   - [ ] 实现权限结果缓存机制
   - [ ] 监控权限检查的响应时间

3. **文档更新**
   - [ ] 更新API文档，说明每个端点的权限要求
   - [ ] 编写权限配置和管理指南
   - [ ] 更新部署文档，包含权限相关的环境配置

### 阶段五：高级功能 (后续迭代)

**可选增强功能**:

- [ ] 权限审计日志系统
- [ ] 权限申请和审批工作流
- [ ] 多因素认证 (MFA) 集成
- [ ] 基于时间的权限控制
- [ ] API频率限制和权限结合
- [ ] 权限可视化管理界面

### 技术风险与缓解策略

**风险点**:

1. **性能影响**: 权限检查可能影响API响应速度
   - *缓解*: 实施权限缓存、优化数据库查询、使用索引

2. **复杂度增加**: 权限逻辑可能使系统过于复杂
   - *缓解*: 从简单的RBAC开始，逐步增加复杂度；充分的文档和测试

3. **数据迁移**: 现有数据需要分配默认权限
   - *缓解*: 编写数据迁移脚本，为现有用户分配合适的默认角色

**成功指标**:

- 所有API端点都有明确的权限控制
- 前端UI能根据用户权限动态调整
- 权限变更能在不重启服务的情况下生效
- 系统性能不因权限检查明显下降
- 开发团队能快速理解和修改权限逻辑

这个路线图将帮助团队有序地从当前的"登录保护"阶段过渡到完整的"细粒度权限控制"系统。
