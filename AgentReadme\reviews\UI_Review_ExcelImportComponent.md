# UI/UX Review Checklist: ExcelImportWithConflictResolution Component (Post-Refactor)

## 1. Document Purpose

This document outlines key review points to ensure that the user interface (UI) and user experience (UX) of the `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` component are correctly preserved or appropriately adapted after its refactoring to use the `useExcelImportSession` Hook. The review should compare the refactored component against the original JSX structure (as documented in `AgentReadme/active_feature_plans/REFAC_ExcelImport_UI_Integration.md`, Section 7) and the intended behavior driven by the new Hook states.

## 2. General Review Points (Applicable to all steps)

* `[ ]` **Overall Layout**: Is the main card structure (`Card`, `CardHeader`, `CardTitle`, `CardDescription`, `CardContent`) preserved?
* `[ ]` **Responsiveness**: Are layouts and elements responsive as per the original design (e.g., `sm:`, `md:` prefixes in Tailwind classes)?
* `[ ]` **Accessibility**: Are `aria-disabled`, `role`, `tabIndex`, `htmlFor` (for Labels) attributes correctly used, especially after logic changes?
* `[ ]` **Icons**: Are all `lucide-react` icons correctly imported and used in their intended places with appropriate sizes and classes?
* `[ ]` **Toast Notifications**: Are `toast` messages for success, error, and info triggered корректно (correctly) by the refactored handlers and effects, with appropriate content and variants?
* `[ ]` **Loading States**:
  * `[ ]` Is `isLoadingSession` (from Hook) used for a general loading indicator when the component or session data is initially loading?
  * `[ ]` Is `isSubmitting` (from Hook) used to disable buttons and show inline loading indicators (e.g., spinning icon, "处理中..." text) during specific user-triggered actions (upload, confirm, cancel, takeover)?
* `[ ]` **Error Display**:
  * `[ ]` Is `errorLoadingSession` (from Hook) displayed prominently and clearly when an error occurs (e.g., within an `<Alert variant="destructive">`)?
  * `[ ]` Is a "Reset" or "Retry" option provided alongside the error display?

## 3. Step-Specific Review Points (for `renderStep()`)

### 3.1. `derivedCurrentStep === 'loading'` (New state based on Hook `isLoadingSession`)

* `[ ]` **UI**: Does it show a clear, centered loading message (e.g., "正在加载最新的会话信息，请稍候...") with a spinner icon (`Clock`)?
* `[ ]` **Condition**: Is this state shown appropriately when `isLoadingSession` is true and no `activeSessionInfo` or `errorLoadingSession` is present yet?

### 3.2. `derivedCurrentStep === 'error'` (New state based on Hook `errorLoadingSession`)

* `[ ]` **UI**: Does it display the `errorLoadingSession` message within a destructive Alert?
* `[ ]` **Actions**: Is a "返回并重置" button (calling `handleReset`) provided?
* `[ ]` **Condition**: Is this shown when `errorLoadingSession` has a value?

### 3.3. `derivedCurrentStep === 'select'` (File Selection Step)

* `[ ]` **Instructional Alert**: Is the initial instructional `<Alert>` for file selection correctly displayed with its icon (`Info`) and text?
* `[ ]` **File Input Area**:
  * `[ ]` Is the dashed border, text ("点击此处或拖拽文件上传"), and icon (`DownloadCloud`) for the empty state preserved?
  * `[ ]` When a file is selected (`selectedFile` is not null):
    * `[ ]` Is the `FileSpreadsheet` icon, file name, file size, and "清除选择" button correctly displayed and functional?
    * `[ ]` Does clearing selection correctly update `selectedFile` and the file input value?
  * `[ ]` When `isSubmitting` is true (after clicking upload, before `activeSessionInfo` from hook updates to a processing state):
    * `[ ]` Is the "文件处理启动中..." message and `Clock` spinner shown?
  * `[ ]` **Busy/Override State** (when `systemBusyWithOtherSessionSelect` is true, based on `useActiveSessionWarning` context and `activeSessionInfo` comparison):
    * `[ ]` Is the file input area correctly styled as disabled/inactive (grayed out, cursor-not-allowed)?
    * `[ ]` Is the `AlertTriangle` icon, "系统当前有活跃导入" message, and details of `activeSessionToProcess` (file name, status, creator, processor) displayed?
    * `[ ]` **Takeover Button**:
      * `[ ]` Is it shown ONLY IF `isCurrentHookSessionBusyAndTakeableSelect` is true (which means `currentUserPermissions.canTakeover` is true for the *specific* session being displayed by `activeSessionToProcess`)?
      * `[ ]` Does it correctly call `handleTakeoverSession` with the right `session_id`?
      * `[ ]` Is it `disabled` when `isSubmitting`?
      * `[ ]` Does its text change to "处理中..." when `isSubmitting`?
* `[ ]` **"上传文件并启动分析" Button**:
  * `[ ]` Text: Correctly shows "处理启动中..." (with spinner) when `isSubmitting`, "系统正忙" when `systemBusyWithOtherSessionSelect`, and "上传并分析" otherwise?
  * `[ ]` `disabled` state: Correctly set based on `!selectedFile`, `isSubmitting`, or `systemBusyWithOtherSessionSelect`?
  * `[ ]` `onClick`: Calls `handleFileUpload`?
* `[ ]` **System Busy Alert** (bottom alert when `systemBusyWithOtherSessionSelect` is true):
  * `[ ]` Variant, icon (`AlertTriangle`), title, and description correctly styled and displayed?
  * `[ ]` Does the description text dynamically include the takeover option based on `isCurrentHookSessionBusyAndTakeableSelect`?
  * `[ ]` Does the link to `/records/import` work?

### 3.4. `derivedCurrentStep === 'analyze'` (Covers old 'upload' XHR and server 'analyze' states)

* `[ ]` **Main Alert**:
  * `[ ]` Icon: `FileUp` for XHR upload phase (if `activeSessionInfo.status === 'UPLOAD'`), `Info` (pulsing if processing) for analysis phase?
  * `[ ]` Title: Dynamically changes ("文件上传中", "服务器正在分析数据", "数据分析已完成") based on `activeSessionInfo.status` and `currentProgressPercentAnalyze`?
  * `[ ]` Description: Shows correct file name (`displayFileNameAnalyze`), record/file size. Shows appropriate contextual message for upload vs. analysis vs. analysis complete but fetching results.
* `[ ]` **Progress Display**:
  * `[ ]` Label: Correctly changes ("上传进度", "分析进度", "分析结果")?
  * `[ ]` Percentage: Shows `currentProgressPercentAnalyze` correctly?
  * `[ ]` Progress Bar: Width and color correctly reflect `currentProgressPercentAnalyze` and stage (blue for upload, amber for analyze)? Pulsing animation when appropriate?
  * `[ ]` Scanned records text: `已扫描 {hookAnalysisProgress?.analyzed_records || 0} / {hookAnalysisProgress?.total_records || 0} 条记录` shown correctly only during analysis (not upload)?
* `[ ]` **"处理进行中..." Message**: Shown with spinner when `isSubmitting` OR analysis is ongoing (`isStillProcessingBackend` or `activeSessionInfo.status === 'UPLOAD'`) AND analysis is not yet complete (`!isAnalysisActuallyComplete`)?
* `[ ]` **"强制取消并重置" Button**:
  * `[ ]` `onClick`: Calls `handleReset`?
  * `[ ]` `disabled`: Correctly set based on `isSubmitting` or `!activeSessionInfo?.session_id`?
  * `[ ]` Visibility: Only shown when analysis is genuinely in progress (not yet `ANALYSIS_COMPLETE`) or during upload?

### 3.5. `derivedCurrentStep === 'confirm'` (Conflict Resolution Stage)

* `[ ]` **Analysis Complete Alert**: Is the `<Alert>` with `CheckCircle2` icon, title, and description (mentioning file name `activeSessionInfo?.file_name`) correctly displayed?
* `[ ]` **Analysis Summary Cards**:
  * `[ ]` Are all 5 cards (总记录, 新记录, 可更新, 无差异, 错误/待定) displayed?
  * `[ ]` Do they correctly show counts from `analysisStats` (populated by `getAnalysisResult`)?
  * `[ ]` Are the styles (colors, backgrounds) for different types of stats preserved?
* `[ ]` **Conflict Handling Section** (if `currentConflictCountConfirm > 0`):
  * `[ ]` Is the warning Alert (`AlertTriangle` icon, title, description about found conflicts) correctly displayed with the count from `currentConflictCountConfirm`?
  * `[ ]` **"处理冲突记录" Button**:
    * `[ ]` Text: "处理 {count} 条冲突记录"?
    * `[ ]` `onClick`: Sets `setShowConflictModal(true)`?
    * `[ ]` `disabled`: By `isSubmitting`?
* `[ ]` **No Conflicts Display** (if `currentConflictCountConfirm === 0 && analysisTotalRecordsConfirm > 0`):
  * `[ ]` Is the success Alert (`CheckCircle2` icon, title, description about no conflicts) displayed?
* `[ ]` **No Data Display** (if `analysisTotalRecordsConfirm === 0`):
  * `[ ]` Is the info Alert (`Info` icon, title, description about no data) displayed?
* `[ ]` **Separator**: Is it present?
* `[ ]` **Action Buttons Row**:
  * `[ ]` **"取消并重新选择文件" Button**: `onClick` calls `handleReset`. `disabled` by `isSubmitting`?
  * `[ ]` **Main Confirmation Button** (`handleConfirmImport`):
    * `[ ]` Text: Dynamically changes based on `currentConflictCountConfirm` and `analysisTotalRecordsConfirm` ("应用解决方案并导入", "全部确认并开始导入", "无数据可导入")?
    * `[ ]` Icon: `ArrowRight` (or `Clock` when `isSubmitting`)?
    * `[ ]` `disabled`: By `isSubmitting`, `!activeSessionInfo?.session_id`, or `analysisTotalRecordsConfirm === 0`?
  * `[ ]` Tip paragraph (if conflicts exist and modal not shown): Is it displayed correctly?

### 3.6. `derivedCurrentStep === 'completed'` (Import Completion Summary)

* `[ ]` **Success Icon & Title**: `CheckCircle2` icon and "导入成功完成!" title displayed?
* `[ ]` **Import Log Details Card**:
  * `[ ]` Displayed if `importLogDetails` is present?
  * `[ ]` Header and title ("导入结果摘要") correct?
  * `[ ]` All statistics (总处理, 成功导入, 新建, 更新, 手动跳过, 系统跳过, 失败) correctly displayed from `importLogDetails` with appropriate styling/colors?
  * `[ ]` Separators shown correctly?
* `[ ]` **Fallback Alert** (if `!importLogDetails` but step is `completed`):
  * `[ ]` Is the destructive Alert ("无法加载导入结果的详细摘要信息") shown?
* `[ ]` **Action Buttons Row**:
  * `[ ]` **"导入新的Excel文件" Button**: `onClick` calls `handleReset({showToast: false})`. `disabled` by `isSubmitting`?
  * `[ ]` **"查看本次导入日志" Button/Link**: Visible if `importLogDetails?.import_log_id` exists. Links to correct `/records/import-history/:id` path?

### 3.7. `default` case (Fallback UI)

* `[ ]` **UI**: Is a generic "未知或正在加载..." message with an `AlertCircle` icon shown if `derivedCurrentStep` is an unexpected value?

## 4. Child Component Interactions

### 4.1. `ConflictResolutionModal` (`renderConflictDialog()`)

* `[ ]` **`open`**: Correctly bound to `showConflictModal` state?
* `[ ]` **`conflicts`**: Receives `filteredConflictRecords`?
* `[ ]` **`analysisResult`**: Receives `analysisStats`?
* `[ ]` **`onClose`**: Calls `setShowConflictModal(false)`?
* `[ ]` **`onResolve`**: Calls `handleConfirmImport`?
* `[ ]` **`isProcessing`**: Bound to Hook's `isSubmitting` state?
* `[ ]` Other props (`updateAction`, `updateAllActions`, `currentFilter`, `onFilterChange`) correctly passed for local conflict management within the modal?

## 5. `useActiveSessionWarning` Context Reconciliation (Step 7 review)

* `[X]` Sync Hook (`activeSessionInfo`) -> Context (`activeSessionToProcess`, `isSessionOverrideActive`): Implemented in main `useEffect`.
* `[X]` Sync Context (`activeSessionToProcess`) -> Hook (`fetchSystemActiveSession`): Implemented in its own `useEffect`.
* `[X]` `saveSession` (Context) usage: `activeSessionInfo` is saved via `saveImportSessionToStorage` after successful `getAnalysisResult`.
* `[X]` `clearSession` (Context) usage: Called in `handleReset` and when `activeSessionInfo` becomes null.
* `[X]` Redundant direct `sessionStorage` recovery `useEffect`: Removed.
* `[ ]` **Final Check**: Does the interaction between the main Hook and this context feel seamless and robust during testing, especially around page reloads or multiple tabs?

This checklist will be used to guide the review of the refactored component.
