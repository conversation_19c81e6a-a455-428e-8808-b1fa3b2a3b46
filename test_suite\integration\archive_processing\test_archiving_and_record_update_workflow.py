# coding: utf-8
import os
import logging
import time
import tempfile # Import tempfile for dummy PDF path
from django.test import TransactionTestCase
from django.contrib.auth.models import User
from django.conf import settings
from django.db import close_old_connections
from django.utils import timezone # Import timezone for datetime fields

# --- Attempt to import reportlab --- 
# This might fail if reportlab is not installed. Add it to requirements if needed.
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("reportlab not found. Cannot create dummy PDF. Test might rely on existing large files or fail.")

# --- App Imports ---
# archive_records models are needed as the target for updates
from archive_records.models import ArchiveRecord 
# archive_processing services and utils are the core focus of this test
from archive_processing.services.file_storage_service import FileStorageService
from archive_processing.services.record_update_service import update_archive_record, generate_file_url
from archive_processing.utils import pdf_utils
from archive_processing.models import UploadedFile

# --- Test Suite Helpers ---
# Using helpers for consistent test environment setup and cleanup
from test_suite.utils.test_settings import apply_test_settings, cleanup_test_files
from test_suite.utils.test_helpers import get_test_file_path # Keep this if needed elsewhere, or remove if not

# --- Test Location Justification ---
# This test file is placed in test_suite/integration/archive_processing/ because:
# 1. It's an INTEGRATION test, verifying the interaction between different components.
# 2. Its primary focus originates from the ARCHIVE_PROCESSING app's responsibilities:
#    - Using FileStorageService to archive files.
#    - Using record_update_service (designed as part of archive_processing's workflow) 
#      to update the status and details of ArchiveRecord models based on the archiving result.
# Although it updates models from the 'archive_records' app, the trigger and core logic reside
# within the intended workflow of 'archive_processing'.

# --- Logging Setup ---
LOG_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'output', 'integration_logs'))
os.makedirs(LOG_DIR, exist_ok=True)
log_filename = os.path.join(LOG_DIR, f"archiving_record_update_workflow_test_{time.strftime('%Y%m%d_%H%M%S')}.log")

logger = logging.getLogger(__name__) # Use __name__ for logger uniqueness per module
logger.setLevel(logging.INFO)

fh = logging.FileHandler(log_filename, encoding='utf-8')
fh.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s')
fh.setFormatter(formatter)

# Avoid adding handler multiple times if module is reloaded or test runner initializes it differently
if not logger.handlers:
    logger.addHandler(fh)
# --- End Logging Setup ---


class ArchivingAndRecordUpdateIntegrationTest(TransactionTestCase):
    """
    集成测试：验证文件归档流程及其对档案记录状态的更新。
    
    测试步骤：
    1. 设置：创建测试用户和待归档的档案记录。
    2. 循环处理：
        a. 为每个待归档记录创建临时模拟PDF。
        b. 使用 FileStorageService 归档临时PDF。
        c. 使用 record_update_service 更新对应档案记录的状态和信息。
    3. 验证：检查归档文件是否存在，数据库记录状态是否正确更新（状态、URL等）。
    4. 清理：删除测试过程中生成的临时文件和归档文件。
    """
    reset_sequences = True

    # Unified numbers to be used in the test
    # These should correspond to records created in setUp
    TEST_UNIFIED_NUMBERS = ['ARCH_TEST_001', 'ARCH_TEST_002', 'ARCH_TEST_003']

    def setUp(self):
        """Prepare the test environment before each test method."""
        close_old_connections()
        apply_test_settings()
        logger.info("已应用测试配置设置")

        # --- Create Dummy Source PDF --- 
        self.source_pdf_path = None
        if REPORTLAB_AVAILABLE:
            # Create a path in a temporary directory managed by test settings if possible,
            # otherwise use tempfile. mkstemp gives us a secure file handle and path.
            fd, self.source_pdf_path = tempfile.mkstemp(suffix=".pdf", prefix="dummy_source_")
            os.close(fd) # Close the file descriptor immediately
            logger.info(f"创建临时的源PDF文件: {self.source_pdf_path}")
            try:
                c = canvas.Canvas(self.source_pdf_path, pagesize=letter)
                num_dummy_pages = 5 # Create a few pages
                for i in range(num_dummy_pages):
                    c.drawString(72, 72, f"Dummy Page {i+1}")
                    c.showPage()
                c.save()
                logger.info(f"已成功创建包含 {num_dummy_pages} 页的源PDF。")
                # Register this file for cleanup if test_settings supports it,
                # otherwise, we'll handle it in tearDown.
                # Example: register_temp_file_for_cleanup(self.source_pdf_path)
            except Exception as pdf_err:
                logger.error(f"创建临时源PDF失败: {pdf_err}", exc_info=True)
                self.source_pdf_path = None # Ensure path is None if creation failed
        else:
            # Fallback: Use an existing (potentially large) file if reportlab is unavailable
            # This is less ideal. Consider adding reportlab to requirements.
            logger.warning("Reportlab不可用，尝试使用现有的测试PDF作为源文件 (可能较慢)。")
            # Choose one of the existing files, e.g., the smaller one if possible
            potential_source = get_test_file_path('pdf', 'test_excel_import_and_pdf_processing(OCR).pdf')
            if os.path.exists(potential_source):
                 self.source_pdf_path = potential_source
                 logger.info(f"使用现有文件作为源PDF: {self.source_pdf_path}")
            else:
                 logger.error("无法创建或找到源PDF文件，测试可能失败。")
                 # Optionally fail setup: self.fail("Missing source PDF for test")

        # Ensure we have a source PDF path to continue
        # self.assertIsNotNone(self.source_pdf_path, "未能准备源PDF文件以供测试")

        # Get or create the test user using Django's standard method
        self.user, created = User.objects.get_or_create(
            username='test_archiving_user', # Use a specific username for this test suite
            defaults={'password': 'testpassword'} # Provide defaults if creating
        )
        if created:
            logger.info(f"测试用户 '{self.user.username}' 已创建。")
        else:
            logger.info(f"测试用户 '{self.user.username}' 已存在，直接使用。")

        # Initialize the service to be tested
        self.storage_service = FileStorageService()

        # --- Create prerequisite ArchiveRecord instances ---
        logger.info(f"创建测试用档案记录: {self.TEST_UNIFIED_NUMBERS}")
        records_to_create = []
        for number in self.TEST_UNIFIED_NUMBERS:
            # Ensure records don't exist before creating
            ArchiveRecord.objects.filter(unified_number=number).delete() 
            records_to_create.append(
                ArchiveRecord(
                    unified_number=number,
                    sample_number=f"SAMPLE_{number}",
                    commission_number=f"COMM_{number}",
                    project_name=f"测试项目 {number}",
                    client_unit="测试委托单位",
                    commission_datetime=timezone.now(),
                    archive_status="待归档",
                    import_user=self.user,
                )
            )
        ArchiveRecord.objects.bulk_create(records_to_create)
        logger.info(f"已创建 {len(records_to_create)} 条测试档案记录。")
        
        # Verify records are created with the correct initial status
        created_count = ArchiveRecord.objects.filter(
            unified_number__in=self.TEST_UNIFIED_NUMBERS,
            archive_status="待归档"
        ).count()
        self.assertEqual(created_count, len(self.TEST_UNIFIED_NUMBERS), 
                         "未能正确创建所有待归档的测试记录")

    def tearDown(self):
        """Clean up after each test method."""
        # --- Clean up dummy source PDF if created --- 
        if hasattr(self, 'source_pdf_path') and self.source_pdf_path and os.path.exists(self.source_pdf_path) and self.source_pdf_path.startswith(tempfile.gettempdir()):
             # Only delete if it's in the temp directory (meaning we created it)
             try:
                 os.remove(self.source_pdf_path)
                 logger.info(f"已清理临时的源PDF文件: {self.source_pdf_path}")
             except OSError as e:
                 logger.error(f"无法清理临时的源PDF文件 {self.source_pdf_path}: {e}")

        cleanup_test_files()
        logger.info("已清理测试文件")
        close_old_connections()

    def test_file_archiving_and_record_update_workflow(self):
        """Tests the workflow of archiving a file and updating the record."""
        if not self.source_pdf_path or not os.path.exists(self.source_pdf_path):
             self.fail("测试设置失败：源PDF文件不存在，无法继续测试。")
             
        close_old_connections()
        logger.info("===== 开始测试文件归档与记录更新流程 =====")

        # 创建一个模拟的UploadedFile记录
        mock_uploaded_file = UploadedFile.objects.create(
            original_name="integration_test.pdf",
            saved_path=self.source_pdf_path,
            file_size=12345,
            archive_box_number="INTEGRATION-BOX-001"
        )
        
        # Tracking results
        successful_updates = 0
        failed_updates = 0
        archived_files_map = {} # Stores {unified_number: archive_path}

        # --- Loop through test numbers, extract page, archive, update record ---
        logger.info(f"[第一步] 循环处理统一编号: {self.TEST_UNIFIED_NUMBERS}")
        for idx, unified_number in enumerate(self.TEST_UNIFIED_NUMBERS):
            temp_extracted_pdf_path = None # Path for the single extracted page PDF
            
            # Define page range to extract (e.g., one page per record)
            # Ensure idx does not go out of bounds of the source PDF pages
            # We need to know the number of pages in source_pdf_path or handle errors in create_temp_pdf
            start_page = idx
            end_page = idx
            
            try:
                # 1. Define path for the temporary extracted PDF
                # Use tempfile directory for these intermediate files
                temp_dir = tempfile.gettempdir()
                temp_extracted_pdf_path = os.path.join(temp_dir, f"temp_extracted_{unified_number}.pdf")
                logger.info(f"准备从源文件提取页 {start_page}-{end_page} 到临时文件: {temp_extracted_pdf_path}")

                # 2. Extract page(s) from the source PDF using pdf_utils
                success = pdf_utils.create_temp_pdf_for_single_archive(
                    original_pdf_path=self.source_pdf_path,
                    start_page=start_page,
                    end_page=end_page,
                    output_path=temp_extracted_pdf_path
                    # Removed incorrect 'content' argument
                )
                self.assertTrue(success, f"未能从源文件提取页面到 {temp_extracted_pdf_path}")
                self.assertTrue(os.path.exists(temp_extracted_pdf_path), f"提取的临时PDF文件未创建: {temp_extracted_pdf_path}")
                logger.info(f"已成功提取页面到临时PDF: {temp_extracted_pdf_path}")

                # 3. Archive the *extracted* temporary PDF using FileStorageService
                logger.info(f"归档提取出的临时PDF: {os.path.basename(temp_extracted_pdf_path)} (编号: {unified_number})")
                # Store the result dictionary from the service
                archive_result = self.storage_service.archive_single_archive_pdf(temp_extracted_pdf_path, unified_number)
                logger.info(f"归档服务返回结果: {archive_result}")
                
                # Extract the final path IF successful
                archive_path = None
                if archive_result and archive_result.get('success'):
                    archive_path = archive_result.get('final_path')
                    logger.info(f"文件已归档到: {archive_path}")
                else:
                    # Fail the test if archiving failed
                    self.fail(f"文件归档失败: {unified_number}, 返回: {archive_result}")
                
                # Assert archive file exists using the extracted path string
                self.assertTrue(os.path.exists(archive_path), f"归档文件未找到: {archive_path}")
                self.assertTrue(archive_path.startswith(settings.ARCHIVE_ROOT), 
                                f"归档路径 {archive_path} 不在预期的根目录 {settings.ARCHIVE_ROOT} 下")
                archived_files_map[unified_number] = archive_path # Store for verification

                # 4. Update the corresponding ArchiveRecord using the service
                logger.info(f"更新档案记录: {unified_number}，归档路径: {archive_path}")
                # Ensure archive_path is a string before passing to update_archive_record
                if not isinstance(archive_path, str):
                     self.fail(f"无效的归档路径类型传递给 update_archive_record: {type(archive_path)}")
                     
                update_result = update_archive_record(
                    unified_number=unified_number,
                    file_path=archive_path, # Use the extracted final archive path string
                    user_id=self.user.id,
                    source_file_id=mock_uploaded_file.file_id
                )
                
                # Check update result
                if update_result.get('success'):
                    successful_updates += 1
                    logger.info(f"成功更新记录: {unified_number}")
                else:
                    # Record should exist as we created it in setUp
                    failed_updates += 1
                    logger.error(f"更新记录失败: {unified_number}, 状态: {update_result.get('status')}, 错误: {update_result.get('error')}")
                    # Fail fast if an expected update fails unexpectedly
                    self.fail(f"更新记录 {unified_number} 失败: {update_result.get('error')}") 

            except Exception as e:
                logger.error(f"处理统一编号 {unified_number} 时发生意外错误: {e}", exc_info=True)
                failed_updates += 1 
                # Include the original exception type and message in the failure
                self.fail(f"处理 {unified_number} 时发生异常: {type(e).__name__}: {e}") 
            finally:
                # 5. Clean up the *extracted* temporary PDF file
                if temp_extracted_pdf_path and os.path.exists(temp_extracted_pdf_path):
                    try:
                        os.remove(temp_extracted_pdf_path)
                        logger.info(f"已清理提取的临时PDF: {temp_extracted_pdf_path}")
                    except OSError as rm_err:
                        logger.error(f"无法删除提取的临时PDF {temp_extracted_pdf_path}: {rm_err}")

        # --- Verify Overall Results ---
        logger.info(f"[第二步] 验证总体结果...")
        logger.info(f"处理完成: 成功更新={successful_updates}, 失败={failed_updates}")
        
        # Assert all expected records were successfully processed
        self.assertEqual(successful_updates, len(self.TEST_UNIFIED_NUMBERS), 
                         f"预期成功更新 {len(self.TEST_UNIFIED_NUMBERS)} 条记录，实际成功 {successful_updates} 条")
        self.assertEqual(failed_updates, 0, f"有 {failed_updates} 条记录更新失败")

        # --- Verify Database State ---
        logger.info(f"[第三步] 验证数据库状态...")
        
        # Query records that should now be archived
        archived_records = ArchiveRecord.objects.filter(
            unified_number__in=self.TEST_UNIFIED_NUMBERS,
            archive_status='已归档'
        )
        archived_records_count = archived_records.count()
        logger.info(f"数据库中查询到 {archived_records_count} 条状态为 '已归档' 的相关记录")
        
        # Assert count matches the number of successful updates
        self.assertEqual(archived_records_count, successful_updates,
                         "数据库中已归档记录数与成功更新的记录数不匹配")

        # Verify details of each archived record
        matched_count = 0
        for record in archived_records:
            unified_number = record.unified_number
            logger.debug(f"验证记录: {unified_number}")
            
            # Check if we have an expected archive path for this number
            self.assertIn(unified_number, archived_files_map, 
                          f"记录 {unified_number} 已归档，但在 archived_files_map 中未找到预期路径")
            
            expected_archive_path = archived_files_map[unified_number]
            
            # Generate the expected URL from the expected file path
            expected_url = generate_file_url(expected_archive_path)
            logger.debug(f"  预期文件路径: {expected_archive_path}")
            logger.debug(f"  生成预期 URL: {expected_url}")
            logger.debug(f"  数据库存储URL: {record.archive_url}")
            
            # Verify archive URL matches the URL generated from the expected path
            self.assertEqual(record.archive_url, expected_url,
                             f"记录 {unified_number} 的 archive_url ('{record.archive_url}') "
                             f"与根据预期路径 ('{expected_archive_path}') 生成的预期URL ('{expected_url}') 不符")
                             
            # Verify other archive-related fields were set
            self.assertIsNotNone(record.archive_datetime, f"记录 {unified_number} 缺少 archive_datetime")
            self.assertEqual(record.archive_person, self.user.username, 
                             f"记录 {unified_number} 的归档人 ('{record.archive_person}') 不是预期的 '{self.user.username}'")
            self.assertIsNotNone(record.source_file, f"记录 {unified_number} 未关联源文件")
            self.assertEqual(record.source_file.archive_box_number, "INTEGRATION-BOX-001",
                             f"记录 {unified_number} 的盒号不正确")
            
            matched_count += 1

        logger.info(f"已详细验证 {matched_count} 条记录的归档信息。")
        # Final check that we verified all successfully updated records
        self.assertEqual(matched_count, successful_updates, "验证的记录数与成功更新的记录数不匹配")

        logger.info("===== 文件归档与记录更新流程测试成功结束 =====")
        close_old_connections()

# Example of how to run this specific test file from project root:
# python manage.py test test_suite.integration.archive_processing.test_archiving_and_record_update_workflow 