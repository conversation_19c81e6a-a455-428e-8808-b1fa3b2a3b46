import logging
import os
from typing import Optional, List, Dict, Any
from urllib.parse import urljoin
from django.conf import settings
from django.db import transaction
from django.utils import timezone
# CHANGE: [2024-04-17] Import ArchiveRecord locally
from archive_records.models import ArchiveRecord
from archive_processing.models import UploadedFile, ProcessingTask
import uuid

logger = logging.getLogger(__name__)

# CHANGE: [2024-04-17] Moved from pdf_processor_usefull.py and tasks.py
def generate_file_url(file_path: Optional[str]) -> Optional[str]:
    """根据文件路径生成可访问的URL (示例)

    Args:
        file_path (Optional[str]): 文件的绝对或相对路径。

    Returns:
        Optional[str]: 生成的URL或None。
    """
    if not file_path:
        return None
    try:
        # 首先处理相对路径的情况
        if not os.path.isabs(file_path):
            # 如果是相对路径，直接组合MEDIA_URL和相对路径
            # 确保使用前斜杠
            formatted_path = file_path.replace('\\', '/')
            return urljoin(settings.MEDIA_URL, formatted_path)
            
        # 处理绝对路径的情况
        abs_path = os.path.abspath(file_path)
        media_root = os.path.abspath(settings.MEDIA_ROOT)
        
        # 检查路径是否有相同的驱动器
        # 在Windows上，这可能会导致os.path.commonpath抛出ValueError
        if os.path.splitdrive(abs_path)[0] != os.path.splitdrive(media_root)[0]:
            # 驱动器不同，无法使用commonpath，返回file URI
            logger.warning(f"文件路径与MEDIA_ROOT驱动器不同，生成file URI: {file_path}")
            # 预先替换反斜杠
            formatted_path = abs_path.replace('\\', '/')
            return f"file://{formatted_path}"
            
        # 现在可以安全地使用commonpath
        if os.path.commonpath([abs_path, media_root]) == media_root:
            relative_path = os.path.relpath(abs_path, media_root)
            # Ensure forward slashes for URL
            relative_path = relative_path.replace('\\', '/')
            url = urljoin(settings.MEDIA_URL, relative_path)
            return url
        else:
            # Path is not under MEDIA_ROOT, return file URI or handle differently
            logger.warning(f"文件路径不在 MEDIA_ROOT 下，生成 file URI: {file_path}")
            # Convert drive letter if needed for file URI (e.g., C:\ -> /C:/)
            if ":" in abs_path:
                parts = abs_path.split(':', 1)
                drive = parts[0].upper()
                path_part = parts[1].replace('\\', '/')
                return f"file:///{drive}:{path_part}"
            else:
                # 修复：预先替换反斜杠，而不是在 f-string 内做替换
                formatted_path = abs_path.replace('\\', '/')
                return f"file://{formatted_path}"

    except Exception as e:
        logger.error(f"生成文件URL失败 for {file_path}: {e}", exc_info=True)
        return None

# CHANGE: [2024-04-17] Moved from pdf_processor_usefull.py and enhanced
# CHANGE: [2025-06-23] Extended to support report path and task association
def update_archive_record(
    unified_number: str,
    archive_file_path: str,
    report_file_path: Optional[str] = None,  # 修改为可选参数
    task_id: str = None
) -> Dict[str, Any]:
    """
    更新档案记录，支持档案和报告路径
    
    Args:
        unified_number: 统一编号
        archive_file_path: 档案PDF最终路径（必须）
        report_file_path: 报告PDF最终路径（可选，为None时跳过报告处理）
        task_id: 任务ID，用于关联原PDF文件（必须，便于追溯）
        
    Returns:
        Dict[str, Any]: 更新结果
        {
            'success': bool,
            'record_id': int,               # ArchiveRecord.id
            'archive_datetime': str,        # 归档时间
            'unified_number': str,          # 统一编号
            'archive_url': str,             # 档案PDF访问URL
            'report_url': str,              # 报告PDF访问URL（可能为None）
            'archive_file_path': str,       # 档案PDF文件路径
            'report_file_path': str,        # 报告PDF文件路径（可能为None）
            'archive_person': str,          # 归档人（来自上传人）
            'source_pdf_file': str,         # 关联的源PDF文件ID
            'has_report': bool,             # 是否包含报告
            'error': str                    # 如果失败
        }
        
    内部实现逻辑：
    1. 通过task_id获取ProcessingTask，进而获取UploadedFile外键
    2. 获取Archive记录（通过unified_number）
    3. 更新Archive记录的archive_url、report_url、archive_datetime、source_file等字段
    4. 直接用UploadedFile.uploader覆盖archive_person字段（归档人）
    5. 支持报告缺失的情况，此时不处理报告相关字段
    """
    if not unified_number:
        return {'success': False, 'error': '缺少统一编号'}
    if not archive_file_path:
        return {'success': False, 'error': '缺少档案文件路径'}
    # 不再强制要求report_file_path
    
    # 记录是否有报告
    has_report = bool(report_file_path)

    try:
        logger.info(f"[服务] 开始更新档案记录 {unified_number}，报告状态：{'有报告' if has_report else '无报告'}...")

        with transaction.atomic():
            # === 第1步：获取Archive记录 ===
            record = ArchiveRecord.objects.filter(unified_number=unified_number).first()

            if not record:
                logger.warning(f"未找到统一编号为 {unified_number} 的档案记录。")
                return {
                    'success': False,
                    'status': 'not_found',
                    'error': f'未找到统一编号为 {unified_number} 的档案记录'
                }

            # === 第2步：通过task_id获取UploadedFile外键 ===
            uploaded_file = None
            if task_id:
                try:
                    from archive_processing.models import ProcessingTask
                    processing_task = ProcessingTask.objects.get(task_id=task_id)
                    uploaded_file = processing_task.file  # 获取关联的UploadedFile
                except ProcessingTask.DoesNotExist:
                    logger.warning(f"任务 {task_id} 不存在，跳过UploadedFile关联")

            # === 第3步：生成访问URL ===
            archive_url = generate_file_url(archive_file_path)
            report_url = None
            
            if not archive_url:
                logger.warning(f"未能为档案文件 {archive_file_path} 生成URL")
                
            if has_report:
                report_url = generate_file_url(report_file_path)
                if not report_url:
                    logger.warning(f"未能为报告文件 {report_file_path} 生成URL")
            else:
                logger.info(f"跳过报告URL生成（报告文件路径为空）")

            # === 第4步：更新Archive记录 ===
            current_time = timezone.now()
            
            # 更新档案相关字段
            record.archive_url = archive_url
            record.archive_datetime = current_time
            record.archive_status = '已归档'
            
            # 更新报告URL（仅当有报告时）
            if hasattr(record, 'report_url'):
                record.report_url = report_url  # 可能为None
            else:
                if has_report:
                    logger.warning(f"Archive模型缺少report_url字段，无法保存报告URL")
            
            # 保存文件路径到数据库
            if hasattr(record, 'archive_file_path'):
                record.archive_file_path = archive_file_path
            if hasattr(record, 'report_file_path'):
                record.report_file_path = report_file_path  # 可能为None
            
            # 关联原始PDF文件（如果task_id有效）
            archive_person_name = None
            if uploaded_file and hasattr(record, 'source_file'):
                record.source_file = uploaded_file
                
                # 直接用上传人覆盖归档人
                if hasattr(record, 'archive_person') and hasattr(uploaded_file, 'uploader'):
                    # 获取上传人的用户名或姓名
                    archive_person_name = getattr(uploaded_file.uploader, 'get_full_name', lambda: None)() or \
                                         getattr(uploaded_file.uploader, 'username', None)
                    if archive_person_name:
                        record.archive_person = archive_person_name
                        logger.info(f"[服务] 设置archive_person为上传人: {archive_person_name}")
            
            # 保存更新
            record.save()

            logger.info(f"[服务] 档案记录 {unified_number} 更新成功。")

            return {
                'success': True,
                'record_id': record.id,
                'archive_datetime': current_time.isoformat(),
                'unified_number': unified_number,
                'archive_url': archive_url,
                'report_url': report_url,
                'archive_file_path': archive_file_path,
                'report_file_path': report_file_path,
                'archive_person': archive_person_name or getattr(record, 'archive_person', None),
                'source_pdf_file': str(uploaded_file.file_id) if uploaded_file else None,
                'has_report': has_report,
            }

    except Exception as e:
        logger.error(f"[服务] 更新档案记录 {unified_number} 失败: {e}", exc_info=True)
        return {'success': False, 'error': f'数据库更新异常: {str(e)}'}

# CHANGE: [2024-05-18] 添加批量检查记录存在性的函数 #AFM-Req1
def check_records_exist(unified_numbers: List[str]) -> List[str]:
    """
    检查提供的统一编号列表中，哪些在 ArchiveRecord 表中不存在。

    Args:
        unified_numbers (List[str]): 需要检查的统一编号列表。

    Returns:
        List[str]: 不存在于数据库中的统一编号列表。
                  如果所有编号都存在，则返回空列表。
    """
    if not unified_numbers:
        logger.info("[服务] check_records_exist 收到空列表，无需检查。")
        return []

    logger.info(f"[服务] 开始检查 {len(unified_numbers)} 个统一编号的存在性...")
    try:
        input_numbers_set = set(unified_numbers) # 使用集合以提高效率和去重

        # 查询数据库中实际存在的编号
        # 使用 values_list 获取字段值列表，flat=True 使其成为 ['num1', 'num2'] 而不是 [('num1',), ('num2',)]
        found_numbers_set = set(
            ArchiveRecord.objects.filter(unified_number__in=input_numbers_set)
                                 .values_list('unified_number', flat=True)
        )

        # 计算输入集合与找到集合的差集，即为缺失的编号
        missing_numbers_set = input_numbers_set - found_numbers_set

        missing_numbers_list = list(missing_numbers_set)

        if missing_numbers_list:
            logger.warning(f"[服务] 检查发现 {len(missing_numbers_list)} 个编号在数据库中不存在: {missing_numbers_list}")
        else:
            logger.info(f"[服务] 所有 {len(input_numbers_set)} 个检查的编号都在数据库中存在。")

        return missing_numbers_list

    except Exception as e:
        logger.error(f"[服务] 检查记录存在性时发生数据库错误: {e}", exc_info=True)
        # 在发生异常时，保守地认为所有记录都可能缺失，或者抛出异常让调用者处理
        # 这里选择返回原始列表，表示无法确认存在性，调用者需要处理此情况
        # 或者可以抛出异常： raise e
        # 目前策略：记录错误，返回输入列表，让调用方知道检查失败
        logger.error("由于数据库错误，无法确认记录存在性，将返回所有输入编号作为潜在缺失项。")
        return unified_numbers # 返回原始列表表示检查失败 