"use client"

import { FileEdit, MoreH<PERSON>zontal } from "lucide-react"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Pagination } from "@/components/ui/pagination"
import { useState } from "react"
import { Button as DropdownButton } from "@/components/ui/button"

interface ChangeOrder {
  id: string
  recordId: string
  recordTitle: string
  type: string
  reason: string
  createdBy: string
  createdAt: string
  status: string
}

interface ChangeOrdersTableProps {
  filterParams: {
    status: string
    dateRange: { from: string; to: string }
    keyword: string
  }
}

export function ChangeOrdersTable({ filterParams }: ChangeOrdersTableProps) {
  // 模拟更改单数据
  const changeOrders = [
    {
      id: "CO-2023-0001",
      recordId: "A-2023-0001",
      recordTitle: "某项目环评报告",
      type: "字段更正",
      reason: "项目名称拼写错误",
      createdBy: "张三",
      createdAt: "2023-10-15",
      status: "completed",
    },
    {
      id: "CO-2023-0002",
      recordId: "A-2023-0005",
      recordTitle: "XX工程可行性研究报告",
      type: "元数据更新",
      reason: "更新项目分类信息",
      createdBy: "李四",
      createdAt: "2023-10-16",
      status: "in_progress",
    },
    {
      id: "CO-2023-0003",
      recordId: "A-2023-0008",
      recordTitle: "某水利工程设计方案",
      type: "文件替换",
      reason: "替换错误上传的文件",
      createdBy: "王五",
      createdAt: "2023-10-17",
      status: "in_progress",
    },
    {
      id: "CO-2023-0004",
      recordId: "A-2023-0012",
      recordTitle: "城市规划环境影响评估",
      type: "分类变更",
      reason: "调整档案分类",
      createdBy: "赵六",
      createdAt: "2023-10-18",
      status: "completed",
    },
    {
      id: "CO-2023-0005",
      recordId: "A-2023-0015",
      recordTitle: "某化工项目安全评价报告",
      type: "字段更正",
      reason: "更正项目负责人信息",
      createdBy: "张三",
      createdAt: "2023-10-19",
      status: "completed",
    },
  ]

  // Filter based on filterParams
  const filteredOrders = changeOrders.filter((order) => {
    // Filter by status if specified
    if (filterParams.status && filterParams.status !== "all" && order.status !== filterParams.status) {
      return false
    }

    // Filter by keyword if specified
    if (
      filterParams.keyword &&
      !order.id.toLowerCase().includes(filterParams.keyword.toLowerCase()) &&
      !order.recordId.toLowerCase().includes(filterParams.keyword.toLowerCase()) &&
      !order.recordTitle.toLowerCase().includes(filterParams.keyword.toLowerCase())
    ) {
      return false
    }

    // Filter by date range if specified
    if (filterParams.dateRange.from || filterParams.dateRange.to) {
      const orderDate = new Date(order.createdAt)
      if (filterParams.dateRange.from && new Date(filterParams.dateRange.from) > orderDate) {
        return false
      }
      if (filterParams.dateRange.to && new Date(filterParams.dateRange.to) < orderDate) {
        return false
      }
    }

    return true
  })

  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10
  const totalPages = Math.ceil(changeOrders.length / itemsPerPage)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            待审批
          </Badge>
        )
      case "approved":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            已审批
          </Badge>
        )
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            已驳回
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  if (filteredOrders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <FileEdit className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">没有找到更改单</h3>
        <p className="text-muted-foreground mt-2">当前没有符合条件的更改单记录</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>更改单号</TableHead>
            <TableHead>档案信息</TableHead>
            <TableHead>更改类型</TableHead>
            <TableHead>更改原因</TableHead>
            <TableHead>创建信息</TableHead>
            <TableHead>状态</TableHead>
            <TableHead className="w-[80px]">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredOrders.map((order) => (
            <TableRow key={order.id}>
              <TableCell className="font-medium">{order.id}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div>{order.recordTitle}</div>
                  <div className="text-xs text-muted-foreground">{order.recordId}</div>
                </div>
              </TableCell>
              <TableCell>{order.type}</TableCell>
              <TableCell>{order.reason}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div>{order.createdBy}</div>
                  <div className="text-xs text-muted-foreground">{order.createdAt}</div>
                </div>
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={`${
                    order.status === "in_progress"
                      ? "border-blue-200 bg-blue-100 text-blue-800"
                      : "border-green-200 bg-green-100 text-green-800"
                  }`}
                >
                  {order.status === "in_progress" ? "进行中" : "已完成"}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <DropdownButton variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">打开菜单</span>
                    </DropdownButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/change-orders/detail/${order.id}`}>查看详情</Link>
                    </DropdownMenuItem>
                    {order.status === "in_progress" && <DropdownMenuItem>标记完成</DropdownMenuItem>}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex items-center justify-end p-4">
        <Pagination 
          currentPage={1}
          totalPages={1}
          onPageChange={() => {}}
        />
      </div>
    </div>
  )
}
