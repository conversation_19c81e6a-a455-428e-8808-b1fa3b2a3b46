"use client"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { CheckCircle2, Clock, Loader2, AlertCircle, Eye } from "lucide-react"

export function RecentArchiveActivities() {
  // 模拟最近活动数据
  const activities = [
    {
      id: "1",
      fileName: "2023年第三季度财务报表.pdf",
      time: "10分钟前",
      user: "张三",
      status: "completed",
      message: "归档处理完成，生成3个子文档",
    },
    {
      id: "2",
      fileName: "产品设计规范V2.0.pdf",
      time: "25分钟前",
      user: "李四",
      status: "processing",
      message: "正在进行OCR识别，已完成45%",
    },
    {
      id: "3",
      fileName: "市场调研报告.pdf",
      time: "1小时前",
      user: "王五",
      status: "failed",
      message: "处理失败：文件格式不兼容",
    },
    {
      id: "4",
      fileName: "人力资源管理制度.pdf",
      time: "2小时前",
      user: "赵六",
      status: "pending",
      message: "等待处理，队列位置：3",
    },
    {
      id: "5",
      fileName: "技术研发计划书.pdf",
      time: "3小时前",
      user: "钱七",
      status: "completed",
      message: "归档处理完成，生成1个子文档",
    },
  ]

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "processing":
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return null
    }
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            已完成
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            处理中
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            失败
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            待处理
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-4 border-b pb-4 last:border-0 last:pb-0">
          <div className="mt-0.5">{getStatusIcon(activity.status)}</div>
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <p className="font-medium">{activity.fileName}</p>
              {getStatusBadge(activity.status)}
            </div>
            <p className="text-sm text-muted-foreground">{activity.message}</p>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>{activity.user}</span>
              <span className="mx-1">•</span>
              <span>{activity.time}</span>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      ))}
      <div className="pt-2 text-center">
        <Button variant="outline" size="sm">
          查看更多活动
        </Button>
      </div>
    </div>
  )
}
