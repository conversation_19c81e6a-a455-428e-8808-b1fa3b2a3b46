/**
 * AG Grid 全局配置和初始化
 * 
 * 集中管理 AG Grid Enterprise 的配置、许可证和模块注册
 * 应当在应用程序入口处导入该文件一次，确保全局初始化
 */

import { LicenseManager, ModuleRegistry, AllEnterpriseModule, SparklinesModule } from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';
import { themeQuartz } from 'ag-grid-enterprise';

// 内部标志，防止重复初始化
let _isInitialized = false;
let _isErrorInterceptorActive = false;

/**
 * 配置 AG Grid Enterprise 许可证
 * 
 * 如果没有有效的许可证密钥，表格将带有水印但在本地环境中仍能正常工作
 * 在生产环境中应该提供有效的许可证密钥
 */
export const setupAgGridLicense = () => {
    // 优先使用环境变量中的许可证密钥
    const licenseKey = process.env.NEXT_PUBLIC_AG_GRID_LICENSE_KEY || 'YOUR_AG_GRID_ENTERPRISE_LICENSE_KEY';

    // 为 AG Grid Enterprise 设置许可证密钥
    LicenseManager.setLicenseKey(licenseKey);

    // 如果在开发环境中，打印许可证状态 (受 NEXT_PUBLIC_AG_GRID_DEBUG 控制)
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_AG_GRID_DEBUG === 'true') {
        console.log('[AG Grid] 许可证已配置');
    }
};

/**
 * 注册所有必需的 AG Grid 模块
 * 
 * 使用 AllEnterpriseModule 包含所有企业版功能模块，包括:
 * - 服务器端行模型 (ServerSideRowModelModule)
 * - 高级筛选 (FiltersToolPanelModule)
 * - Excel导出 (ExcelExportModule)
 * - 等其他所有企业版功能
 */
export const registerAgGridModules = () => {
    // 注册所有 AG Grid Enterprise 模块，并额外注册 Sparklines 模块
    ModuleRegistry.registerModules([
        AllEnterpriseModule,
        SparklinesModule.with(AgChartsEnterpriseModule)
    ]);

    // 如果在开发环境中，打印模块注册状态 (受 NEXT_PUBLIC_AG_GRID_DEBUG 控制)
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_AG_GRID_DEBUG === 'true') {
        console.log('[AG Grid] 所有企业版模块已注册');
    }
};

/**
 * 设置错误拦截
 * 
 * 拦截并屏蔽 AG Grid 许可证相关的错误消息
 * 注意: 此功能直接在代码中实现，不在React组件中，
 * 这样可以更早地拦截错误（在组件挂载前）
 */
export const setupErrorInterception = () => {
    // 检查是否已经激活，避免重复设置导致控制台输出混乱
    if (_isErrorInterceptorActive) {
        return;
    }
    
    // 检查环境变量是否禁用了拦截器
    const isEnabledByEnv = process.env.NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT !== 'false';
    
    // 如果明确禁用，则不激活拦截器
    if (!isEnabledByEnv) return;
    
    // 立即执行函数，避免污染全局作用域
    (function() {
        // 保存原始的 console.error 函数
        const originalConsoleError = console.error;

        // 定义AG Grid许可证错误相关的关键词
        const agGridErrorKeywords = [
            "AG Grid Enterprise License",
            "Invalid License Key",
            "ag-grid.com/licensing",
            "@ag-grid.com",
            "All AG Charts Enterprise",
            "Your license key is not valid",
            "*****************************", // 添加星号行，AG Grid常用于错误格式化
            "License Key",
            "validateLicense",
            "centerPadAndOutput", // 拦截格式化函数
            "padAndOutput" // 拦截格式化函数
        ];

        // 重写 console.error 函数
        console.error = function(...args) {
            // 将所有参数转换为字符串，以便进行检查
            const message = args.map(arg => {
                if (typeof arg === 'object' && arg !== null) {
                    try {
                        return JSON.stringify(arg);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            }).join(' ');

            // 检查消息中是否包含任何AG Grid许可证错误关键词
            const isAgGridLicenseError = agGridErrorKeywords.some(keyword => message.includes(keyword));

            // 如果不是AG Grid许可证错误，则调用原始的 console.error
            if (!isAgGridLicenseError) {
                originalConsoleError.apply(console, args);
            } 
            // 如果是AG Grid错误且debug模式开启，则输出提示
            else if (process.env.NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG === 'true') {
                originalConsoleError("拦截到AG Grid许可证错误，已被屏蔽");
            }
        };
        
        // 如果在开发环境中且调试模式开启，则输出激活消息
        if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_AG_GRID_DEBUG === 'true') {
            originalConsoleError("[AG Grid] 许可证错误拦截器已激活");
        }
        
        // 标记拦截器已激活
        _isErrorInterceptorActive = true;
    })();
};

/**
 * 初始化 AG Grid (许可证、模块和错误拦截)
 * 
 * 应在应用程序入口处调用一次该函数
 * 包含防止重复初始化的逻辑
 */
export const initializeAgGrid = () => {
    // 检查是否已经初始化，避免重复初始化
    if (_isInitialized) {
        if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_AG_GRID_DEBUG === 'true') {
            console.log('[AG Grid] 已经初始化，跳过重复初始化');
        }
        return;
    }

    // 设置错误拦截 - 最先执行，以便拦截后续步骤可能产生的错误
    setupErrorInterception();
    
    // 设置许可证
    setupAgGridLicense();
    
    // 注册模块
    registerAgGridModules();
    
    // 标记已完成初始化
    _isInitialized = true;
    
    // 如果在开发环境中，打印初始化完成状态 (受 NEXT_PUBLIC_AG_GRID_DEBUG 控制)
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_AG_GRID_DEBUG === 'true') {
        console.log('[AG Grid] 初始化完成');
    }
};

// [新增] 定义自定义主题对象，设置字体和字号
export const myQuartzTheme = themeQuartz.withParams({
  browserColorScheme: "light",
  fontFamily: { googleFont: "IBM Plex Sans" }, // 全局字体
  fontSize: 14, // 正文字号
  headerFontSize: 14 // 表头字号
});

// 导出默认的 AG Grid 配置对象
export default {
    // 服务端行模型默认配置
    serverSideDefaults: {
        cacheBlockSize: 200,    // 每次请求加载的行数，从500减小到200
        maxBlocksInCache: 50,   // 最大缓存块数 (200 * 50 = 10000条记录)，增加到50
        debug: process.env.NODE_ENV === 'development',
        rowBuffer: 10           // 缓存行数，提高滚动性能
    },
    
    // 客户端行模型默认配置
    clientSideDefaults: {
        pagination: true,       // 默认启用分页
        paginationPageSize: 50, // 默认每页显示50条记录
        debug: process.env.NODE_ENV === 'development',
        rowBuffer: 10,          // 缓存行数，提高滚动性能
        suppressRowHoverHighlight: true // 禁用行高亮，提高滚动性能
    },

    // 可复用的默认列定义
    defaultColDef: {
        resizable: true,         // 允许调整列宽
        sortable: true,          // 允许排序
        filter: true,            // 允许筛选
        floatingFilter: true,    // 显示浮动筛选控件
        minWidth: 80,            // 最小列宽
        enableRowGroup: true,    // 允许行分组
        enablePivot: false,      // 默认不启用数据透视
        enableValue: false       // 默认不启用值聚合功能
    },
    
    // 主题配置
    themes: {
        quartz: 'ag-theme-quartz',       // 默认主题 (v33+)
        quartzDark: 'ag-theme-quartz-dark',
        alpine: 'ag-theme-alpine',
        alpineDark: 'ag-theme-alpine-dark',
        balham: 'ag-theme-balham',
        balhamDark: 'ag-theme-balham-dark',
        material: 'ag-theme-material',
        // [新增] 自定义主题对象，推荐所有表格统一用此项
        quartzCustom: myQuartzTheme
    },
    
    // 侧边栏配置
    sideBarConfig: {
        toolPanels: [
            {
                id: 'columns',
                labelDefault: '列管理',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
                toolPanelParams: {
                    suppressRowGroups: false,
                    suppressValues: true,
                    suppressPivots: true,
                    suppressPivotMode: true,
                    suppressColumnFilter: false,
                    suppressColumnSelectAll: false,
                    suppressColumnExpandAll: false
                }
            }
        ],
        defaultToolPanel: 'columns'
    },
    
    // 性能优化配置
    performanceConfig: {
        // 行和列虚拟化默认开启 - 这是核心性能优化，保持不变
        suppressColumnVirtualisation: false,
        suppressRowVirtualisation: false,
        
        // 固定行高，提高渲染性能 - 保持这项优化
        rowHeight: 40,
        headerHeight: 42,
        
        // 缓存最近滚动的行，提高滚动性能 - 保持不变
        rowBuffer: 10,
        
        // 禁用行高亮 - 恢复行高亮，提升用户体验
        suppressRowHoverHighlight: false,
        
        // 禁用垂直滚动防抖 - 它可能导致滚动不流畅
        debounceVerticalScrollbar: false,
        
        // 保留表头自动调整大小计算
        skipHeaderOnAutoSize: false,
        
        // 确保动画流畅
        suppressColumnMoveAnimation: false,
        suppressAnimationFrame: false,
        
        // 恢复行变换动画以提高用户体验
        suppressRowTransform: false,
        
        // 保持单元格焦点功能
        suppressCellFocus: false,
        
        // 保持文本选择功能
        enableCellTextSelection: true,
        
        // 使用正常布局
        domLayout: 'normal'
    }
}; 