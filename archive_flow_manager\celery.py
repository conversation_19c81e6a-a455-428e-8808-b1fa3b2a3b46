import os
from celery import Celery

# 设置 Django settings 模块的环境变量，让 Celery 能找到它
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

# 创建 Celery 应用实例
# 第一个参数是当前模块的名称，第二个参数 broker 和 backend 会从 settings 中读取
app = Celery('archive_flow_manager')

# 使用 Django settings 文件来配置 Celery
# namespace='CELERY' 表示所有 Celery 配置项在 settings.py 中都以 'CELERY_' 开头
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动从所有已注册的 Django app 中加载 tasks.py 文件
app.autodiscover_tasks()

# （可选）添加一个简单的测试任务
@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}') 