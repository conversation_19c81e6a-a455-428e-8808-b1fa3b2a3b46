import apiClient from '@/lib/apiClient';
import { ProcessingTask, ArchiveRecord } from './types';

/**
 * 定义处理任务统计数据的类型。
 */
export interface ProcessingStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  total: number;
}

/**
 * 获取处理任务的统计数据。
 * @returns 包含各状态计数的对象。
 */
export async function getProcessingStats(): Promise<ProcessingStats> {
  const response = await apiClient.get('/api/archive-processing/processing-tasks/statistics/');
  if (response.success && response.data) {
    // 后端直接返回了所需格式的数据，无需转换
    return response.data;
  } else {
    // 提供一个默认的、全为0的返回，防止UI崩溃
    console.error("无法加载处理任务统计:", response.error);
    return { pending: 0, processing: 0, completed: 0, failed: 0, total: 0 };
  }
}

/**
 * 获取处理任务列表。
 * @param status - 要筛选的状态 ('all', 'processing', 'completed', 'failed', 'queued')。
 * @returns 任务列表。
 */
export async function getTasks(status: string): Promise<ProcessingTask[]> {
  let apiUrl = '/api/archive-processing/processing-tasks/';

  if (status === 'all') {
    // 对于'all'状态，我们不添加任何状态过滤器以获取所有数据
  } else if (status === 'pending') {
    apiUrl += '?status=pending,queued';
  } else if (status === 'processing') {
    apiUrl += '?status=processing,chunking,processing_parallel,aggregating';
  } else if (status === 'failed') {
    apiUrl += '?status=failed,failed_validation';
  } else if (status === 'completed') {
    apiUrl += '?status=completed,completed_without_report';
  } else {
    // This handles any other status directly, e.g., a custom status.
    apiUrl += `?status=${status}`;
  }
  
  const response = await apiClient.get(apiUrl);
  
  if (response.success && response.data) {
    const tasks = response.data.results || response.data;
    // 在服务层处理数据转换，确保返回的类型正确
    return tasks.map((task: any) => ({
      ...task,
      fileId: task.file?.fileId || '',
      uploadedFile: {
         originalName: task.file?.originalName || '文件名加载失败',
         username: task.file?.username || '未知用户',
         archiveBoxNumber: task.file?.archiveBoxNumber,
      },
      resultData: task.resultData
    }));
  } else {
    throw new Error(response.error || "无法加载任务列表");
  }
}

/**
 * 获取指定已完成任务关联的档案记录详情。
 * @param taskId - 主任务的ID。
 * @returns 档案记录列表。
 */
export async function getTaskDetailRecords(taskId: string): Promise<ArchiveRecord[]> {
  const response = await apiClient.get(`/api/archive-processing/processing-tasks/${taskId}/records/`);
  if (response.success && response.data) {
    return response.data.results || response.data;
  } else {
    throw new Error(response.error || "无法加载任务详情");
  }
}

/**
 * 重试一个失败的任务。
 * @param taskId - 要重试的任务ID。
 */
export async function retryTask(taskId: string): Promise<void> {
  const response = await apiClient.post(`/api/archive-processing/processing-tasks/${taskId}/retry/`);
  if (!response.success) {
    throw new Error(response.error || "重试失败");
  }
}

/**
 * 重新上传一个文件以替换旧文件。
 * @param fileId - 要被替换的旧文件的ID。
 * @param file - 新的File对象。
 */
export async function reuploadFile(fileId: string, file: File): Promise<void> {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await apiClient.post(`/api/archive-processing/uploaded-files/${fileId}/re-upload/`, formData);
  if (!response.success) {
    throw new Error(response.error || "重新上传失败");
  }
}

/**
 * 更新指定文件的档案盒号。
 * @param fileId - 要更新的文件的ID。
 * @param newBoxNumber - 新的档案盒号。
 */
export async function updateArchiveBoxNumber(fileId: string, newBoxNumber: string): Promise<void> {
  const response = await apiClient.patch(`/api/archive-processing/uploaded-files/${fileId}/`, {
    archive_box_number: newBoxNumber,
  });

  if (!response.success) {
    // 健壮的错误信息提取
    let errorMessage = "更新档案盒号失败";
    if (response.error) {
      if (typeof response.error === 'object' && response.error !== null && 'detail' in response.error) {
        errorMessage = (response.error as any).detail;
      } else if (typeof response.error === 'string') {
        errorMessage = response.error;
      }
    }
    throw new Error(errorMessage);
  }
} 