"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Clock, CheckCircle, AlertTriangle } from "lucide-react"
import Link from "next/link"

interface PendingReport {
  id: string
  title: string
  status: string
  priority: string
  dueDate: string
  client: string
}

export function PendingDistributions() {
  const [pendingReports, setPendingReports] = useState<PendingReport[]>([])

  useEffect(() => {
    // 模拟数据
    const reports = [
      {
        id: "REP-2023-0046",
        title: "某工程结构检测报告",
        status: "pending",
        priority: "high",
        dueDate: "2023-05-15",
        client: "某建筑公司",
      },
      {
        id: "REP-2023-0045",
        title: "道路工程质量检测报告",
        status: "processing",
        priority: "medium",
        dueDate: "2023-05-16",
        client: "市政工程局",
      },
      {
        id: "REP-2023-0044",
        title: "桥梁承重测试报告",
        status: "pending",
        priority: "medium",
        dueDate: "2023-05-17",
        client: "交通建设集团",
      },
      {
        id: "REP-2023-0043",
        title: "建筑材料强度检测报告",
        status: "warning",
        priority: "high",
        dueDate: "2023-05-14",
        client: "某建材有限公司",
      },
      {
        id: "REP-2023-0042",
        title: "地基稳定性评估报告",
        status: "processing",
        priority: "low",
        dueDate: "2023-05-18",
        client: "房地产开发公司",
      },
    ]

    setPendingReports(reports)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "processing":
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            高
          </Badge>
        )
      case "medium":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            中
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            低
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card className="col-span-2">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>待处理发放单</CardTitle>
        <Button variant="outline" size="sm" asChild>
          <Link href="/reports/management?status=pending">查看全部</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pendingReports.map((report) => (
            <div key={report.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
              <div className="flex items-start gap-3">
                {getStatusIcon(report.status)}
                <div>
                  <div className="font-medium">{report.id}</div>
                  <div className="text-sm text-muted-foreground">{report.title}</div>
                  <div className="text-xs text-muted-foreground mt-1">客户: {report.client}</div>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                {getPriorityBadge(report.priority)}
                <div className="text-xs text-muted-foreground">截止日期: {report.dueDate}</div>
                <Button variant="link" size="sm" className="h-auto p-0" asChild>
                  <Link href={`/reports/detail/${report.id}`}>处理</Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
