# 发放单模块业务服务层设计文档

## 📋 文档信息

- **文档名称**: 发放单模块业务服务层设计
- **版本**: v1.0
- **创建日期**: 2025-06-08
- **最后更新**: 2025-06-08
- **相关文档**:
  - [发放单模块服务层架构设计](./service_layer_architecture_design.md)
  - [发放单模块事务层设计](./transaction_service_layer_design.md)
  - [IssueRecordService 数据服务层设计](./issue_record_service_design.md)

## 🎯 设计目标

### 主要目标

1. **统一业务编排**: 将发放单、发放记录、档案状态的业务逻辑统一管理
2. **业务逻辑纯粹**: 专注业务规则和验证，不处理数据同步和事务管理
3. **接口简化**: 为上层API提供简洁的业务操作接口
4. **规则集中**: 发放类型计算、数量控制等核心业务逻辑集中管理

### 解决的问题

- **业务逻辑分散**: 原有三个业务服务导致发放业务逻辑分散
- **服务间协调复杂**: 多个业务服务之间的调用关系复杂
- **计算逻辑归属不明**: 发放类型和数量计算没有明确的归属
- **业务完整性缺失**: 发放作为完整业务流程被人为拆分

## 🏗️ 架构定位

### 在整体架构中的位置

```mermaid
graph TB
    subgraph "API/视图层"
        V1[IssueFormViewSet<br/>发放单API]
        V2[ArchiveRecordViewSet<br/>档案记录API]
        V3[IssueRecordViewSet<br/>发放记录API]
    end
    
    subgraph "业务服务层"
        BS[IssueBusinessService<br/>统一发放业务编排]
    end
    
    subgraph "事务服务层"
        TS[IssueTransactionService<br/>跨模型事务协调]
    end
    
    subgraph "数据服务层"
        DS1[IssueFormService<br/>发放单数据操作]
        DS2[ArchiveRecordService<br/>档案记录数据操作]
        DS3[IssueRecordService<br/>发放记录数据操作]
    end
    
    subgraph "审计服务层"
        AS[AuditService<br/>统一审计管理]
    end
    
    subgraph "数据模型层"
        M1[(IssueForm)]
        M2[(ArchiveRecord)]
        M3[(IssueRecord)]
    end
    
    V1 --> BS
    V2 --> BS
    V3 --> BS
    
    BS -.->|只读操作| DS1
    BS -.->|只读操作| DS2
    BS -.->|只读操作| DS3
    BS -->|写入/事务| TS
    
    TS --> DS1
    TS --> DS2
    TS --> DS3
    TS --> AS
    
    DS1 --> M1
    DS2 --> M2
    DS3 --> M3
    
    style BS fill:#e3f2fd
    style TS fill:#fff8e1
```

### IssueFormItem vs IssueRecord 业务层视角

| 特性 | IssueFormItem | IssueRecord |
|------|---------------|-------------|
| **业务阶段** | 发放计划阶段 | 发放执行阶段 |
| **创建时机** | 发放单创建时 | 发放单归档时 |
| **业务含义** | 表达发放意图和计划 | 记录实际发放历史 |
| **编辑能力** | 草稿状态可编辑 | 创建后不可更改 |
| **业务逻辑** | 支持计划调整 | 确定执行结果 |
| **数据验证** | 计划可行性验证 | 执行一致性验证 |

### 职责边界

- ✅ **负责**: 完整的发放业务流程编排（IssueFormItem到IssueRecord的转换）
- ✅ **负责**: 发放类型和数量的计算逻辑
- ✅ **负责**: 业务规则验证和业务异常处理
- ✅ **负责**: 为API层提供简洁的业务接口
- ✅ **负责**: 业务查询的数据聚合和格式化
- ✅ **负责**: IssueFormItem 生命周期管理（创建、编辑、锁定）
- ❌ **不负责**: 直接的数据库操作
- ❌ **不负责**: 跨模型的数据同步
- ❌ **不负责**: 事务管理和数据一致性保证
- ❌ **不负责**: 审计日志的创建和管理

## 📚 IssueBusinessService 详细设计

### 核心接口定义

```python
class IssueBusinessService:
    """
    发放业务编排服务
    
    统一管理发放单、发放记录、档案状态的完整业务流程。
    专注业务逻辑和规则验证，通过事务层执行跨模型操作。
    """
    
    def __init__(self):
        from .transaction_service import IssueTransactionService
        from .data_services import IssueFormService, ArchiveRecordService, IssueRecordService
        
        self.transaction_service = IssueTransactionService()
        self.issue_form_service = IssueFormService()
        self.archive_record_service = ArchiveRecordService()
        self.issue_record_service = IssueRecordService()
```

## 📝 方法分类详细设计

### 1. 发放单及条目管理类

```python
def create_or_update_issue_form_with_items(
    self,
    form_id: Optional[int] = None,
    form_data: Optional[Dict] = None,
    selected_archives: List[Dict] = None
) -> Dict:
    """
    统一的发放单及条目管理业务接口
    
    - form_id=None: 创建新发放单及条目
    - form_id存在: 更新现有发放单的条目选择
    
    业务逻辑：验证 + 组合调用事务层原子操作
    """
    # 实现细节省略
```

### 2. 状态转换管理类

```python
def lock_issue_form(self, form_id: int) -> Dict:
    """锁定发放单（草稿 → 锁定）"""
    # 实现细节省略

def unlock_issue_form(self, form_id: int) -> Dict:
    """解锁发放单（锁定 → 草稿）"""
    # 实现细节省略

def issue_form_final(self, form_id: int) -> Dict:
    """最终发放归档（锁定 → 已发放）"""
    # 实现细节省略
```

### 3. 删除管理类

```python
def delete_draft_form(self, form_id: int) -> Dict:
    """删除草稿状态发放单"""
    # 实现细节省略

def delete_issued_form(self, form_id: int, reason: str) -> Dict:
    """删除已归档发放单"""
    # 实现细节省略
```

### 4. 查询类

```python
def get_issue_form_detail(self, form_id: int) -> Dict:
    """获取发放单详细信息"""
    # 实现细节省略

def list_issue_forms(self, filters: Dict = None, page: int = 1, page_size: int = 20) -> Dict:
    """分页查询发放单列表"""
    # 实现细节省略
```

### 5. 核心业务计算类

```python
def get_issueable_archives(self, filters: Dict = None) -> List[Dict]:
    """
    获取可发放的档案表
    
    Args:
        filters: 过滤条件 {

        }
        
    Returns:
        List[dict]: 可发放档案列表 [
            {

            },
            ...
        ]
        
    Note:
        返回所有满足发放条件的档案，用于前端选择发放对象
    """
    # 实现细节省略

def _determine_issue_type(self, archive_item_id: int) -> dict:
    """
    确定档案项的发放类型和基础状态
    
    Args:
        archive_item_id: 档案项ID
        
    Returns:
        {
            'issue_type': str,              # 'first', 'second', 'completed'
            'can_issue': bool,              # 是否可以发放
            'has_first_issue': bool,        # 是否有第一次发放记录
            'has_second_issue': bool,       # 是否有第二次发放记录
            'first_copies': int or None,    # 第一次发放份数
            'second_copies': int or None,   # 第二次发放份数
            'total_copies': int,           # 总可发放份数
            'scenario': str                # 场景描述
        }
    """
    
    # 获取基础数据
    archive_item = self.archive_record_service.get_archive_item(archive_item_id)
    total_copies = max(3, archive_item.total_copies)  # 最少3份
    
    first_issue = self.issue_record_service.get_first_issue_record(archive_item_id)
    second_issue = self.issue_record_service.get_second_issue_record(archive_item_id)
    
    has_first = first_issue is not None
    has_second = second_issue is not None
    first_copies = first_issue.copies if has_first else None
    second_copies = second_issue.copies if has_second else None
    
    # 判断发放类型和状态
    if has_first and has_second:
        # 场景1：两次都有 - 发放完成
        return {
            'issue_type': 'completed',
            'can_issue': False,
            'has_first_issue': True,
            'has_second_issue': True,
            'first_copies': first_copies,
            'second_copies': second_copies,
            'total_copies': total_copies,
            'scenario': 'both_completed'
        }
    
    elif has_first and not has_second:
        if first_copies == total_copies:
            # 场景2：第一次发放全部 - 发放完成
            return {
                'issue_type': 'completed',
                'can_issue': False,
                'has_first_issue': True,
                'has_second_issue': False,
                'first_copies': first_copies,
                'second_copies': None,
                'total_copies': total_copies,
                'scenario': 'first_all_completed'
            }
        else:
            # 场景3：第一次发放部分 - 可进行第二次
            return {
                'issue_type': 'second',
                'can_issue': True,
                'has_first_issue': True,
                'has_second_issue': False,
                'first_copies': first_copies,
                'second_copies': None,
                'total_copies': total_copies,
                'scenario': 'first_partial_awaiting_second'
            }
    
    elif not has_first and has_second:
        # 场景4：只有第二次（异常情况）- 需补第一次
        return {
            'issue_type': 'first',
            'can_issue': True,
            'has_first_issue': False,
            'has_second_issue': True,
            'first_copies': None,
            'second_copies': second_copies,
            'total_copies': total_copies,
            'scenario': 'missing_first_补发'
        }
    
    else:
        # 场景5：都没有 - 可进行第一次
        return {
            'issue_type': 'first',
            'can_issue': True,
            'has_first_issue': False,
            'has_second_issue': False,
            'first_copies': None,
            'second_copies': None,
            'total_copies': total_copies,
            'scenario': 'initial_state'
        }

def calculate_issue_quantity(self, archive_item_id: int) -> dict:
    """
    计算档案项的发放数量和选项
    
    依赖 _determine_issue_type 获取发放类型，然后计算具体数量
    
    Args:
        archive_item_id: 档案项ID
        
    Returns:
        {
            # 继承 _determine_issue_type 的所有字段
            'available_options': list,      # 可选发放份数
            'suggested_copies': int,        # 建议发放份数  
            'remaining_copies': int,        # 剩余可发放份数
            'quantity_rule': str,          # 数量规则说明
            'validation': dict             # 验证信息
        }
    """
    
    # 1. 获取发放类型信息
    type_info = self._determine_issue_type(archive_item_id)
    
    # 2. 根据发放类型计算数量选项
    if not type_info['can_issue']:
        # 无法发放的情况
        quantity_info = {
            'available_options': [],
            'suggested_copies': 0,
            'remaining_copies': 0,
            'quantity_rule': '发放已完成，无可发放数量'
        }
    
    elif type_info['issue_type'] == 'first':
        if type_info['scenario'] == 'missing_first_补发':
            # 补发第一次，只能1份
            quantity_info = {
                'available_options': [1],
                'suggested_copies': 1,
                'remaining_copies': 1,
                'quantity_rule': '补发第一次，固定1份'
            }
        else:
            # 正常第一次发放，1份或全部
            total = type_info['total_copies']
            quantity_info = {
                'available_options': [1, total],
                'suggested_copies': 1,
                'remaining_copies': total,
                'quantity_rule': '第一次发放：1份或全部'
            }
    
    elif type_info['issue_type'] == 'second':
        # 第二次发放，剩余全部
        remaining = type_info['total_copies'] - type_info['first_copies']
        quantity_info = {
            'available_options': [remaining],
            'suggested_copies': remaining,
            'remaining_copies': remaining,
            'quantity_rule': '第二次发放：剩余全部'
        }
    
    # 3. 添加验证信息
    quantity_info['validation'] = {
        'business_rules': {
            'first_issue_constraint': '第一次发放只能是1份或全部',
            'second_issue_constraint': '第二次发放必须是剩余全部',
            'min_total_copies': 3,
            '补发_constraint': '补发第一次时只能是1份'
        },
        'scenario_description': self._get_scenario_description(type_info['scenario'])
    }
    
    # 4. 合并返回结果
    return {**type_info, **quantity_info}

def _get_scenario_description(self, scenario: str) -> str:
    """获取场景描述"""
    descriptions = {
        'both_completed': '第一次和第二次发放都已完成',
        'first_all_completed': '第一次发放了全部，无需第二次',
        'first_partial_awaiting_second': '第一次发放了部分，等待第二次发放',
        'missing_first_补发': '缺失第一次发放记录，需要补发',
        'initial_state': '初始状态，准备进行第一次发放'
    }
    return descriptions.get(scenario, '未知场景')

### 6. 验证类

```python
def validate_issue_eligibility(self, form_id_or_archives) -> Dict:
    """统一的发放可行性验证"""
    # 实现细节省略
```

### 7. 内部辅助方法

```python
def _validate_form_editable(self, form_id: int) -> 'IssueForm':
    """内部方法：验证发放单可编辑性"""
    # 实现细节省略

def _get_form_with_validation(self, form_id: int) -> 'IssueForm':
    """内部方法：获取发放单并验证存在性"""
    # 实现细节省略
```

## 🧪 测试策略

### 1. 单元测试

```python
class TestIssueBusinessService:
    """IssueBusinessService 单元测试"""
    
    def test_calculate_issue_plan_first_issue(self):
        """测试正本发放计划计算"""
        
    def test_calculate_issue_plan_second_issue(self):
        """测试副本发放计划计算"""
        
    def test_calculate_issue_plan_exceeds_limit(self):
        """测试超出发放限额的异常处理"""
        
    def test_validate_archive_requests_success(self):
        """测试档案申请验证成功"""
        
    def test_validate_archive_requests_duplicate(self):
        """测试重复档案申请验证"""
        
    def test_form_status_validation(self):
        """测试发放单状态验证"""
```

### 2. 集成测试

- **完整业务流程测试**: 从创建发放单到执行发放的完整流程
- **业务规则验证测试**: 各种业务规则的边界情况测试
- **错误处理测试**: 异常情况的处理和错误信息测试
- **数据一致性测试**: 验证业务操作后数据状态的正确性

### 3. 性能测试

- **计算逻辑性能**: 大量档案申请的计算性能
- **查询聚合性能**: 复杂查询和数据聚合的性能
- **并发处理**: 同时处理多个发放单的并发性能

## ✅ 设计优势

### 1. **业务完整性**

- 统一管理完整的发放业务流程，避免业务逻辑分散
- 发放类型计算有明确归属，便于维护和扩展

### 2. **职责清晰**

- 专注业务逻辑，不处理技术性同步和事务管理
- 与事务层分工明确，各自专注核心职责

### 3. **接口简洁**

- 为API层提供高级业务接口，隐藏复杂的内部协调
- 减少上层调用的复杂度

### 4. **易于测试**

- 业务逻辑相对独立，便于单元测试
- 通过mock事务层可以专注测试业务规则

### 5. **可扩展性**

- 业务规则变更只需修改业务层
- 新增业务功能可以复用现有的事务协调机制

## 🚀 未来扩展方向

### 1. 业务规则引擎

- 考虑引入规则引擎支持更复杂的发放策略
- 支持可配置的业务规则和审批流程

### 2. 智能推荐

- 基于历史数据提供发放建议
- 智能检测潜在的业务规则冲突

### 3. 批量操作优化

- 支持大批量档案的发放申请处理
- 优化计算性能和内存使用

---

**文档维护**: 本文档应随着业务需求变化和代码实现及时更新，确保设计与实现的一致性。
