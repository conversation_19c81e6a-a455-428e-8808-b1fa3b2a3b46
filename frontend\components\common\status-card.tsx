"use client"

import React from "react"
import { cn } from "@/lib/utils"

// ==================== 单个状态卡片 ====================

export interface StatusCardData {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  color?: "blue" | "orange" | "green" | "red" | "default";
}

export const StatusCard: React.FC<StatusCardData> = ({ 
  icon, 
  title, 
  value, 
  color = 'default' 
}) => {
  const colorClasses = {
    blue: "text-blue-600",
    orange: "text-orange-600",
    green: "text-green-600",
    red: "text-red-600",
    default: "text-gray-900 dark:text-gray-50",
  }

  return (
    <div className="flex items-center p-4 bg-white rounded-lg shadow dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
      <div className="p-3 mr-4 text-white bg-gray-100 rounded-full dark:text-gray-100 dark:bg-gray-700">
        {icon}
      </div>
      <div>
        <p className="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">
          {title}
        </p>
        <p className={cn("text-lg font-semibold", colorClasses[color])}>
          {value}
        </p>
      </div>
    </div>
  );
};


// ==================== 状态卡片组 ====================

interface StatusCardGroupProps {
  cards: StatusCardData[];
}

export const StatusCardGroup: React.FC<StatusCardGroupProps> = ({ cards }) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
      {cards.map((card, index) => (
        <StatusCard key={index} {...card} />
      ))}
    </div>
  );
}; 