# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
/env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
/lib/
!frontend/lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
/media/

# 保留关键媒体目录
!media/templates/
!media/archives/*/
!media/backups/
# 但排除临时文件
media/temp_split/

# 测试文件和输出
tests/output/

# 虚拟环境
venv/
ENV/
archive_flow_manager.conda/

# IDE和编辑器
.idea/
.vscode/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# Node.js
node_modules/
*/node_modules/
# 仅使用pnpm
.pnpm-store/
.pnpm-debug.log
# pnpm-lock.yaml

# Next.js
.next/
out/

# 构建输出
frontend/build/
frontend/dist/

# 缓存
.cache/
.temp/
.eslintcache
tsconfig.tsbuildinfo
frontend/tsconfig.tsbuildinfo

# 环境变量
# .env
# .env.local
# .env.development.local
# .env.test.local
# .env.production.local

# Docker
.docker/
docker-compose.override.yml
docker/data/
# .dockerignore
docker-data/
docker/volumes/
docker/postgres/
docker/redis/
*.tar
*.tar.gz
*.tar.bz2

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/
!*/tasks/