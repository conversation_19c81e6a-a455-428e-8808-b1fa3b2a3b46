#!/bin/bash
# OCR 服务紧急修复脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚨 OCR 服务紧急修复"
echo "=========================="

# 1. 立即停止服务
log_info "停止所有 OCR 相关服务..."
docker-compose stop ocr-service || true
docker-compose rm -f ocr-service || true

# 2. 清理旧镜像
log_info "清理旧镜像..."
docker rmi archive-flow-ocr:latest || true

# 3. 重新构建
log_info "重新构建 OCR 服务..."
cd ocr_service

# 确保目录结构正确
if [ ! -d "app" ]; then
    log_error "app 目录不存在，请检查文件结构"
    exit 1
fi

# 构建新镜像
if docker build -t archive-flow-ocr:latest .; then
    log_success "镜像构建成功"
else
    log_error "镜像构建失败"
    exit 1
fi

cd ..

# 4. 启动服务
log_info "启动 OCR 服务..."
docker-compose up -d ocr-service

# 5. 等待启动
log_info "等待服务启动..."
sleep 15

# 6. 检查状态
log_info "检查服务状态..."
if docker-compose ps ocr-service | grep -q "Up"; then
    log_success "服务启动成功"
else
    log_error "服务启动失败，查看日志:"
    docker-compose logs --tail=30 ocr-service
    exit 1
fi

# 7. 健康检查
log_info "执行健康检查..."
max_attempts=20
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:8001/health &> /dev/null; then
        log_success "健康检查通过！"
        break
    else
        echo -n "."
        sleep 3
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    log_error "健康检查失败，查看日志:"
    docker-compose logs --tail=50 ocr-service
    exit 1
fi

log_success "OCR 服务修复完成！"
echo ""
echo "📋 服务信息:"
echo "  🔍 OCR 服务: http://localhost:8001"
echo "  📊 健康检查: http://localhost:8001/health"
echo "  📚 API 文档: http://localhost:8001/docs"
