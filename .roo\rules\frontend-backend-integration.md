---
description: 
globs: 
alwaysApply: true
---
# 前后端对接计划

**作用**: 指导前后端对接过程，确保团队遵循一致的对接顺序和规范。

**触发指令示例**: "前后端对接", "对接顺序", "API规范", "如何对接前端和后端"

## 对接顺序

本项目采用核心业务优先的对接策略，顺序如下：

### 第一阶段：核心业务页面

1. **记录管理** (records)
   - 后端：实现记录相关API，包括查询、创建、更新和删除
   - 前端：对接records页面，保持现有UI布局
   - 关键文件: [frontend/app/records/page.tsx](mdc:frontend/app/records/page.tsx)

2. **档案管理** (archive)
   - 后端：实现档案CRUD接口，支持复杂查询和筛选
   - 前端：对接archive页面组件，包括档案列表和详情页
   - 关键文件: [frontend/app/archive/page.tsx](mdc:frontend/app/archive/page.tsx)

3. **报告管理** (reports)
   - 后端：实现报告生成、查询和分发API
   - 前端：对接reports页面，配合报告查看组件
   - 关键文件: [frontend/app/reports/page.tsx](mdc:frontend/app/reports/page.tsx)

4. **变更单管理** (change-orders)
   - 后端：实现变更单创建、审批、执行API
   - 前端：对接变更单页面和流程组件
   - 关键文件: [frontend/app/change-orders/page.tsx](mdc:frontend/app/change-orders/page.tsx)

5. **PDF导入台账** (archive/ledger)
   - 后端：实现PDF上传、处理和切分API
   - 前端：对接pdf-import-ledger组件
   - 关键文件: [frontend/components/archive/pdf-import-ledger.tsx](mdc:frontend/components/archive/pdf-import-ledger.tsx)

### 第二阶段：认证与基础设施

6. **登录认证**
   - 后端：实现JWT认证接口，用户验证
   - 前端：对接login页面，集成token管理
   - 关键文件: [frontend/app/login/page.tsx](mdc:frontend/app/login/page.tsx)

7. **用户管理基础**
   - 后端：实现用户信息和权限API
   - 前端：对接profile页面和header组件
   - 关键文件: [frontend/app/profile/page.tsx](mdc:frontend/app/profile/page.tsx)

### 第三阶段：辅助功能

8. **统计分析** (statistics)
   - 后端：实现统计数据API，聚合查询
   - 前端：对接统计图表组件
   - 关键文件: [frontend/app/statistics/page.tsx](mdc:frontend/app/statistics/page.tsx)

9. **通知系统** (notifications)
   - 后端：实现通知API，支持不同通知类型
   - 前端：对接通知组件和消息中心
   - 关键文件: [frontend/app/notifications/page.tsx](mdc:frontend/app/notifications/page.tsx)

10. **系统设置与管理功能**
    - 后端：实现系统配置、用户管理和帮助内容API
    - 前端：对接相关页面组件
    - 关键文件: 
      - [frontend/app/settings/page.tsx](mdc:frontend/app/settings/page.tsx)
      - [frontend/app/users/page.tsx](mdc:frontend/app/users/page.tsx)
      - [frontend/app/help/page.tsx](mdc:frontend/app/help/page.tsx)

## 对接方法与规范

### 对接流程（每个页面）

1. **API定义阶段**
   - 后端团队定义接口规范（参数、返回值）
   - 前端团队评审接口是否满足UI需求
   - 双方确认接口规范并形成文档

2. **后端实现阶段**
   - 后端优先实现API，提供测试环境
   - 提供API测试集合供前端调试
   - 编写API文档，包括示例

3. **前端对接阶段**
   - 前端基于mock数据开发（保持UI不变）
   - 逐步替换mock数据为真实API调用
   - 处理错误情况和边界条件

4. **联调优化阶段**
   - 处理数据格式不一致问题
   - 优化性能和用户体验
   - 完善错误处理和数据验证

### 技术规范

1. **API规范**
   - 使用RESTful风格接口
   - 返回格式：`{ success: true/false, data: {...} }` 或 `{ success: false, error: "错误信息" }`
   - 分页参数统一：`page`、`page_size`
   - 过滤参数命名统一：`filter_{字段名}`

2. **状态码使用**
   - 200: 成功
   - 400: 客户端错误
   - 401: 未认证/token失效
   - 403: 无权限
   - 500: 服务器错误

3. **无认证阶段的处理**
   - 初期API不强制认证，通过配置开启/关闭
   - 前端设置临时绕过认证的机制
   - 最终对接认证系统时平滑过渡

4. **版本控制**
   - API URL包含版本：`/api/v1/...`
   - 重大变更必须升级版本号

5. **安全性考虑**
   - 所有API输入必须验证
   - 敏感数据传输加密
   - 实施适当的CORS策略
   - 实现请求频率限制

