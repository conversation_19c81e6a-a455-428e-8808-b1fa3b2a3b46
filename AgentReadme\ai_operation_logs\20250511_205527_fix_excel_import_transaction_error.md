# Operation Document: Fix Excel Import Transaction Error

## 📋 Change Summary

**Purpose**: To resolve a "You can't execute queries until the end of the 'atomic' block." error occurring during Excel file imports.
**Scope**: Modified the `_process_dataframe` method in `archive_records/services/excel_import.py`.
**Associated**: User-reported error: "Excel上传或处理失败: ... An error occurred in the current transaction. You can't execute queries until the end of the 'atomic' block."

## 🔧 Operation Steps

### 📊 OP-001: Analyze Error and Code

**Precondition**: User reports a 500 error during Excel import with a specific transaction-related message.
**Operation**:
    1. Reviewed the error message, indicating a problem within a Django `transaction.atomic()` block.
    2. Used `grep_search` to locate uses of `transaction.atomic` and identified `archive_records/services/excel_import.py` as the relevant file.
    3. Read the `excel_import.py` file, focusing on the `_process_dataframe` method and its batch processing logic within an `atomic()` block.
    4. Identified that generic `except Exception` blocks were catching `django.db.Error` exceptions without re-raising them, leading to attempts to run queries on a broken transaction.
**Postcondition**: Understanding of the root cause and a plan to fix the exception handling.

### ✏️ OP-002: Modify Exception Handling in `excel_import.py`

**Precondition**: The `excel_import.py` service has overly broad exception handlers within its batch transaction.
**Operation**:
    1. Modified the `try-except` blocks around `existing_record.save()` calls within the row processing loop.
        - Added a specific `except django.db.Error as db_err_update:` block.
        - Inside this block, logged the error, added details to `error_records`, and then re-raised the `db_err_update` exception.
        - Retained `except Exception as other_err_update:` for other non-DB errors.
    2. Modified the `try-except` blocks around `self._safe_bulk_create()` calls.
        - Added a specific `except django.db.Error as db_err_bulk:` block.
        - Inside this block, logged the error, added details to `error_records` for all records in the failed bulk operation, and then re-raised the `db_err_bulk` exception.
        - Retained `except Exception as other_err_bulk:` for other non-DB errors.
    3. Added initialization for `current_sample_number_for_error_reporting` and `current_commission_number_for_error_reporting` at the start of each row's processing loop for better error logging.
**Postcondition**: The code now correctly re-raises `django.db.Error` exceptions from within the batch `transaction.atomic()` block, allowing for proper transaction rollback and preventing the "can't execute queries" error. The root database error will propagate and be logged appropriately.

## 📝 Change Details

### CH-001: Refactor Exception Handling for `existing_record.save()`

**File**: `archive_records/services/excel_import.py`
**Before**:

```python
# ...
                                        try:
                                            # 更新记录
                                            for field, value in changes.items():
                                                setattr(existing_record, field, value)
                                            existing_record.save()
                                            # ...
                                        except Exception as e: # Catches all, including DB errors
                                            # 记录错误
                                            error_records.append({ ... })
                                            # Does not re-raise DB error
# ...
```

**After**:

```python
# ...
                                        try:
                                            # 更新记录
                                            for field, value in changes.items():
                                                setattr(existing_record, field, value)
                                            existing_record.save()
                                            # ...
                                        except django.db.Error as db_err_update: # Specific catch for DB errors
                                            logger.error(f"数据库错误 (更新记录 {current_commission_number_for_error_reporting or 'N/A'} 时，Excel行 {original_row_idx + 2}): {db_err_update}", exc_info=True)
                                            error_records.append({ ... }) # Log details
                                            raise # RE-RAISE DB error
                                        except Exception as e: # Catch other non-DB errors
                                            logger.error(f"更新记录 {current_commission_number_for_error_reporting or 'N/A'} 时发生非数据库错误 (Excel行 {original_row_idx + 2}): {e}", exc_info=True)
                                            error_records.append({ ... }) # Log details
                                            # Non-DB errors here might not need to fail the batch.
# ...
```

**Rationale**: Specifically catching and re-raising `django.db.Error` ensures that the atomic transaction for the batch rolls back correctly if a database operation fails, preventing further queries on a broken transaction.

### CH-002: Refactor Exception Handling for `_safe_bulk_create()`

**File**: `archive_records/services/excel_import.py`
**Before**:

```python
# ...
                    try:
                        # 第一次尝试批量创建
                        created_batch = self._safe_bulk_create(records_to_create)
                        # ...
                    except Exception as e: # Catches all, including DB errors from _safe_bulk_create
                        logger.error(f"批次 {batch_index + 1}/{total_batches} 创建失败: {str(e)}")
                        error_records.extend([...])
                        # Does not re-raise DB error
# ...
```

**After**:

```python
# ...
                    try:
                        # 第一次尝试批量创建
                        created_batch = self._safe_bulk_create(records_to_create)
                        # ...
                    except django.db.Error as db_err_bulk: # Specific catch for DB errors
                        logger.error(f"批量创建数据库错误 (批次 {batch_index + 1}/{total_batches}): {db_err_bulk}", exc_info=True)
                        error_records.extend([...]) # Log details for all records in batch
                        raise # RE-RAISE DB error
                    except Exception as e: # Catch other non-DB errors
                        logger.error(f"批次 {batch_index + 1}/{total_batches} 创建时发生非数据库错误: {str(e)}", exc_info=True)
                        error_records.extend([...]) # Log details
                        # Decide if non-DB errors here should also fail the batch.
# ...
```

**Rationale**: Similar to CH-001, this ensures that if `_safe_bulk_create` results in a `django.db.Error`, the batch transaction is rolled back, and the error propagates correctly.

### CH-003: Initialize Variables for Error Reporting

**File**: `archive_records/services/excel_import.py`
**Before**: Variables like `sample_number`, `commission_number` might not be defined in `except` blocks if an error occurred before their assignment within the `try`.
**After**:

```python
# ...
                for idx, row in batch_df.iterrows():
                    original_row_idx = idx
                    # 初始化行特定信息以用于错误报告
                    current_sample_number_for_error_reporting = None
                    current_commission_number_for_error_reporting = None
                    try:
                        # 处理行数据
                        record_data = self._process_row(row)
                        current_sample_number_for_error_reporting = record_data.get('sample_number')
                        current_commission_number_for_error_reporting = record_data.get('commission_number')
                        # ...
```

**Rationale**: Ensures that variables used in error logging within `except` blocks are always defined, preventing potential `NameError` issues and improving log quality.

## ✅ Verification Results

**Method**: Code review and reasoning about Django's transaction management. The fix ensures that database errors within an atomic block are handled in a way that allows the transaction to roll back as intended.
**Results**: The "You can't execute queries until the end of the 'atomic' block." error should no longer occur due to the fixed exception handling. Instead, the original database error will propagate, leading to a 500 error with a more accurate root cause logged.
**Problems**: None anticipated with this specific fix.
**Solutions**: N/A.
