import os
import logging
import uuid
import hashlib
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import UploadedFile as DjangoUploadedFile
from werkzeug.utils import secure_filename
from ..models import UploadedFile, ProcessingTask
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import transaction
from .task_service import TaskService

logger = logging.getLogger(__name__)

# CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 用于验证 PDF 文件有效性 #AFM-Refactor-PDFLib
try:
    import fitz  # PyMuPDF

    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logger.warning(
        "PyMuPDF 库未安装，将无法验证 PDF 文件的有效性，建议安装: pip install PyMuPDF"
    )

# CHANGE: [2024-03-28] 创建上传服务 UploadService #AFM-2
# TODO: [P1] 添加更严格的文件类型验证（例如，检查文件签名而不仅仅是扩展名）
# TODO: [P2] 实现更灵活的上传路径配置（例如，基于用户、日期等）
# TODO: [P2] 集成病毒扫描功能

logger = logging.getLogger(__name__)


class UploadService:
    """
    上传管理服务，处理文件上传、验证和临时存储
    """

    # 允许上传的文件扩展名 (可以从配置中读取)
    ALLOWED_EXTENSIONS = {".pdf"}
    # 最大允许的文件大小 (字节, 例如 100MB)
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 示例值

    @staticmethod
    def validate_file(file_obj: DjangoUploadedFile):
        """
        验证上传的文件类型和大小。

        Args:
            file_obj: Django的UploadedFile对象。

        Raises:
            ValidationError: 如果文件验证失败。
        """
        # Thinking: 首先检查文件对象是否存在。
        if not file_obj:
            logger.warning("上传验证失败：未提供文件对象")
            raise ValidationError("未提供文件对象。")

        # Thinking: 验证文件大小是否在限制范围内。
        if file_obj.size > UploadService.MAX_FILE_SIZE:
            # TODO: [P3] 允许为不同用户或组配置不同的大小限制
            logger.warning(
                f"上传验证失败：文件过大 ({file_obj.size} > {UploadService.MAX_FILE_SIZE})，文件名：{file_obj.name}"
            )
            raise ValidationError(
                f"文件过大，最大允许 {UploadService.MAX_FILE_SIZE // (1024*1024)}MB。"
            )

        # Thinking: 验证文件扩展名是否在允许列表中。
        ext = os.path.splitext(file_obj.name)[1].lower()
        if ext not in UploadService.ALLOWED_EXTENSIONS:
            logger.warning(
                f"上传验证失败：不允许的文件类型 '{ext}'，文件名：{file_obj.name}"
            )
            raise ValidationError(
                f"不允许的文件类型 '{ext}'。只允许上传 {', '.join(UploadService.ALLOWED_EXTENSIONS)} 文件。"
            )

        logger.debug(f"文件验证通过: {file_obj.name} ({file_obj.size} 字节)")

        # CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 用于 PDF 验证 #AFM-Refactor-PDFLib
        # 如果文件对象已经有内容并且可以使用 PyMuPDF，则尝试验证 PDF 格式有效性
        if (
            PYMUPDF_AVAILABLE
            and hasattr(file_obj, "temporary_file_path")
            and file_obj.temporary_file_path()
        ):
            # 某些文件上传处理程序（如 FileUploadHandler）创建了临时文件
            doc = None
            try:
                logger.debug(
                    f"使用 PyMuPDF 验证 PDF 文件有效性: {file_obj.temporary_file_path()}"
                )
                doc = fitz.open(file_obj.temporary_file_path())
                # 获取页数并尝试访问第一页，验证基本结构
                page_count = doc.page_count
                if page_count > 0:
                    # 轻量级地访问第一页确认其存在
                    _ = doc.load_page(0)
                    logger.debug(f"PDF 验证成功，文件包含 {page_count} 页")
                else:
                    logger.warning(f"PDF 验证警告：文件不包含任何页面")
            except Exception as e:
                logger.error(f"PDF 验证失败: {e}", exc_info=True)
                raise ValidationError(f"文件似乎不是有效的 PDF 文档，或者已损坏: {e}")
            finally:
                # 确保关闭文档释放资源
                if doc:
                    doc.close()
        else:
            # 流式上传或不支持 PyMuPDF，将在保存后验证
            logger.debug("跳过初始 PDF 验证，将在保存后验证")

    @staticmethod
    def validate_pdf_content(file_path: str):
        """
        验证已保存的文件是否为有效的 PDF 文档。

        Args:
            file_path: 已保存的文件路径。

        Raises:
            ValidationError: 如果文件不是有效的 PDF 文档。
        """
        if not PYMUPDF_AVAILABLE:
            logger.warning("PyMuPDF 库未安装，无法验证 PDF 内容")
            return

        doc = None
        try:
            logger.debug(f"使用 PyMuPDF 验证 PDF 文件有效性: {file_path}")
            doc = fitz.open(file_path)
            page_count = doc.page_count
            if page_count > 0:
                # 轻量级地访问第一页确认其存在
                _ = doc.load_page(0)
                logger.debug(f"PDF 验证成功，文件包含 {page_count} 页")
            else:
                logger.warning(f"PDF 验证警告：文件不包含任何页面")
        except Exception as e:
            logger.error(f"PDF 内容验证失败: {e}", exc_info=True)
            raise ValidationError(f"文件似乎不是有效的 PDF 文档，或者已损坏: {e}")
        finally:
            # 确保关闭文档释放资源
            if doc:
                doc.close()

    @staticmethod
    def calculate_file_hash(file_path: str) -> str:
        """
        计算文件的SHA256哈希值。

        Args:
            file_path: 文件路径。

        Returns:
            str: 文件的SHA256哈希值（16进制字符串）。

        Raises:
            IOError: 如果无法读取文件。
        """
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                # 分块读取文件以处理大文件
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except IOError as e:
            logger.error(f"计算文件哈希失败: {e}", exc_info=True)
            raise IOError(f"无法读取文件进行哈希计算: {e}")

    @staticmethod
    def save_uploaded_file(
        file_obj: DjangoUploadedFile, archive_box_number: str, user_id: int = None
    ) -> UploadedFile:
        """
        保存上传的文件到临时存储，并在数据库中创建记录。

        Args:
            file_obj: Django的UploadedFile对象。
            archive_box_number: 用户提供的档案盒号。
            user_id: 上传用户的ID (可选)。

        Returns:
            UploadedFile: 创建的数据库记录实例。

        Raises:
            ValidationError: 如果文件验证失败。
            IOError: 如果文件保存过程中发生错误。
        """
        # CHANGE: [2024-04-16] 添加日志记录提高可追踪性 #AFM-49
        logger.info(
            f"开始处理文件上传: {file_obj.name} (大小: {file_obj.size} 字节, 盒号: {archive_box_number}, 用户ID: {user_id})"
        )

        # Thinking: 首先调用验证方法确保文件有效。
        UploadService.validate_file(file_obj)

        # Thinking: 生成一个安全的文件名，避免路径遍历等问题。
        original_filename = file_obj.name
        # 使用UUID确保文件名唯一，保留原始扩展名
        file_uuid = uuid.uuid4()
        filename = f"{file_uuid}{os.path.splitext(original_filename)[1]}"

        # Thinking: 确定文件保存路径。这里使用配置中的 MEDIA_ROOT/uploads/ 作为临时存储。
        # TODO: [P2] 使上传路径更具可配置性，例如 settings.UPLOAD_TEMP_DIR
        upload_dir = os.path.join(settings.MEDIA_ROOT, "uploads")
        if not os.path.exists(upload_dir):
            logger.debug(f"创建上传目录: {upload_dir}")
            os.makedirs(upload_dir, exist_ok=True)

        saved_path = os.path.join(upload_dir, filename)
        logger.debug(f"即将保存文件到临时路径: {saved_path}")
        file_hash = None

        # Thinking: 将上传的文件内容写入到目标路径。
        try:
            with open(saved_path, "wb+") as destination:
                for chunk in file_obj.chunks():
                    destination.write(chunk)
            logger.info(f"文件已成功保存到临时位置: {saved_path}")

            # 文件保存后的额外验证
            if PYMUPDF_AVAILABLE:
                # 验证文件内容是否为有效的 PDF
                try:
                    UploadService.validate_pdf_content(saved_path)
                except ValidationError as pdf_error:
                    # 验证失败，删除文件并重新抛出异常
                    os.remove(saved_path)
                    logger.error(f"PDF 内容验证失败，已删除临时文件: {saved_path}")
                    raise  # 重新抛出 ValidationError

            # CHANGE: [2024-04-16] 添加文件哈希计算功能 #AFM-50
            # 计算文件哈希值
            try:
                file_hash = UploadService.calculate_file_hash(saved_path)
                logger.debug(f"计算文件哈希成功: {file_hash}")
            except IOError as hash_error:
                logger.warning(f"计算文件哈希失败，将继续处理: {hash_error}")
                # 在这种情况下我们不阻止上传过程继续，只是没有哈希值

        except IOError as e:
            logger.error(f"保存上传文件失败: {e}", exc_info=True)
            raise IOError(f"无法保存文件 '{original_filename}' 到 '{saved_path}': {e}")

        # Thinking: 文件成功保存后，在数据库中创建 UploadedFile 记录。
        try:
            # 尝试获取 User 对象，如果 user_id 提供了的话
            uploader_instance = None
            if user_id:
                from django.contrib.auth.models import (
                    User,
                )  # 延迟导入以避免循环依赖风险

                try:
                    uploader_instance = User.objects.get(pk=user_id)
                    logger.debug(
                        f"关联上传用户: {uploader_instance.username} (ID: {user_id})"
                    )
                except User.DoesNotExist:
                    # TODO: [P1] 决定如何处理用户不存在的情况（警告日志？引发错误？）
                    logger.warning(f"尝试关联上传文件到不存在的用户ID: {user_id}")
                    pass  # 或者根据策略决定是否抛出错误

            uploaded_file_record = UploadedFile.objects.create(
                original_name=original_filename,
                saved_path=saved_path,
                file_size=file_obj.size,
                archive_box_number=archive_box_number,
                uploader=uploader_instance,  # 关联用户对象
                file_hash=file_hash,  # 添加文件哈希值
            )
            logger.info(
                f"已创建上传文件记录 ID: {uploaded_file_record.file_id}, 原始名称: {original_filename}, 分配盒号: {archive_box_number}, 哈希值: {file_hash}"
            )

            return uploaded_file_record
        except Exception as e:
            # 如果数据库记录创建失败，尝试删除已保存的文件以避免孤立文件
            # TODO: [P1] 增加重试逻辑或标记为待清理
            logger.error(f"创建数据库记录失败: {e}", exc_info=True)
            if os.path.exists(saved_path):
                try:
                    os.remove(saved_path)
                    logger.info(f"数据库记录创建失败，已删除临时文件: {saved_path}")
                except OSError as remove_error:
                    logger.error(
                        f"数据库记录创建失败后，删除物理文件也失败: {remove_error}",
                        exc_info=True,
                    )
                    pass  # 记录错误，但继续抛出原始的数据库错误
            raise e  # 重新抛出数据库相关的错误

    @staticmethod
    def handle_re_upload(old_file, new_file_obj, user):
        """
        处理文件的重新上传，封装了完整的业务逻辑。

        - 标记旧文件为'deleted'。
        - 为新上传的文件创建一个全新的UploadedFile和ProcessingTask记录。
        - 异步触发旧物理文件的清理。
        """
        # 前置条件检查：确保只对活跃的文件进行操作
        if old_file.status != 'active':
            logger.warning(
                f"尝试对非活跃状态的文件进行re-upload操作。文件ID: {old_file.file_id}, "
                f"当前状态: '{old_file.status}'。"
            )
            return False, f"此文件当前状态为'{old_file.status}'，无法重新上传。"

        try:
            with transaction.atomic():
                # 1. 标记旧文件记录为 'deleted'
                old_file_id = old_file.file_id
                logger.info(f"开始处理重新上传。旧文件ID: {old_file_id}。")
                old_file.status = 'deleted'
                old_file.save(update_fields=['status'])
                logger.info(f"旧文件记录 {old_file_id} 状态已更新为 'deleted'。")

                # 2. 保存新文件，继承旧文件的元数据
                logger.info(f"正在为用户 {user.id if user else '匿名'} 保存新上传的文件...")
                new_uploaded_file = UploadService.save_uploaded_file(
                    file_obj=new_file_obj,
                    archive_box_number=old_file.archive_box_number, # 继承盒号
                    user_id=user.id if user and user.is_authenticated else None,
                )
                logger.info(f"新文件保存成功，新文件ID: {new_uploaded_file.file_id}。")

                # 3. 创建新的处理任务，尝试继承旧任务的参数
                params = {}
                # 通过反向查询找到与旧文件关联的最后一个任务
                last_task = ProcessingTask.objects.filter(file=old_file).order_by('-created_at').first()
                if last_task:
                    params = last_task.processing_params or {}
                    logger.info(f"从旧任务 {last_task.task_id} 继承了处理参数: {params}")
                else:
                    logger.warning(f"旧文件 {old_file_id} 没有关联的处理任务，将使用默认参数。")
                
                logger.info(f"正在为新文件 {new_uploaded_file.file_id} 创建处理任务...")
                new_task = TaskService.create_task(
                    uploaded_file=new_uploaded_file,
                    params=params
                )
                logger.info(f"新处理任务 {new_task.task_id} 创建并分发成功。")

            # 4. 异步触发物理文件清理 (此步骤已移除)
            # 物理清理工作现由周期性任务 periodic_cleanup_deleted_files 统一处理
            # 确保了即使在服务重启或消息队列故障时，文件最终也能被清理，增强了系统的健壮性。
            # logger.info(f"准备异步清理旧的物理文件: {old_file_id}。")
            # cleanup_deleted_file.delay(str(old_file_id))
            # logger.info(f"清理任务已成功入队。")

            # 5. 返回成功结果
            result_data = {
                "message": "文件重新上传成功，新的处理任务已启动。",
                "new_file_id": str(new_uploaded_file.file_id),
                "new_task_id": str(new_task.task_id),
                "new_task_status": new_task.status,
            }
            return True, result_data

        except Exception as e:
            logger.exception(f"处理重新上传 (旧文件ID: {old_file.file_id}) 时发生严重错误。")
            return False, "服务器内部错误，重新上传失败。"
