"use client"

import React, { ReactNode } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { PageHeader, PageAction } from "@/components/common/page-header"

// 滚动区域配置接口
interface ScrollAreaConfig {
  id: string;
  scrollable: boolean;
  height?: string;
  overflowBehavior?: 'auto' | 'hidden' | 'visible' | 'scroll';
}

// 滚动配置接口
interface ScrollConfig {
  enabled?: boolean;
  exclusions?: string[];
  areas?: ScrollAreaConfig[];
}

interface PageLayoutProps {
  /** 页面标题 */
  title: string
  /** 页面副标题/描述 */
  subtitle?: string
  /** 操作按钮数组 */
  actions?: PageAction[]
  /** 自定义头部内容 */
  header?: ReactNode
  /** 子内容 */
  children: ReactNode
  /** 内容间距，默认为space-y-8 */
  contentGap?: "space-y-6" | "space-y-8" | string
  /** 禁用页面滚动（使用原生滚动） */
  disableScrollArea?: boolean
  /** 是否显示头部底部边框 */
  showHeaderBorder?: boolean
  /** 自定义类名 */
  className?: string
  /** 固定在顶部的标签栏内容 */
  fixedTabs?: ReactNode
  /** 状态卡片内容 - 在标题和标签之间，固定显示 */
  statusCards?: ReactNode
  /** 滚动内容的内边距 - 默认为 pr-3 */
  contentPadding?: string
  /** 滚动配置 - 允许更细粒度地控制页面中不同区域的滚动行为 */
  scrollConfig?: ScrollConfig
}

/**
 * 通用页面布局组件
 *
 * 提供一致的页面结构，包括标题区域和滚动内容区域
 * 支持固定的标签栏，在滚动时保持在顶部
 * 支持精细化控制页面内特定区域的滚动行为
 */
export function PageLayout({
  title,
  subtitle,
  actions,
  header,
  children,
  contentGap = "space-y-8",
  disableScrollArea = false,
  showHeaderBorder = true,
  className,
  fixedTabs,
  statusCards,
  contentPadding = "pr-3",
  scrollConfig,
}: PageLayoutProps) {
  // 新增：生成滚动控制样式
  const generateScrollStyles = () => {
    if (!scrollConfig) return '';
    
    const { exclusions = [], areas = [] } = scrollConfig;
    
    // 为排除的区域生成样式
    const exclusionStyles = exclusions.map(id => `
      #${id}, [data-scroll-area="${id}"] {
        overflow: visible !important;
        max-height: none !important;
        height: auto !important;
      }
    `).join('\n');
    
    // 为特定区域生成样式
    const areaStyles = areas.map(area => `
      #${area.id}, [data-scroll-area="${area.id}"] {
        overflow: ${area.overflowBehavior || (area.scrollable ? 'auto' : 'visible')} !important;
        height: ${area.height || 'auto'} !important;
        max-height: ${area.scrollable ? 'none' : 'none'} !important;
      }
    `).join('\n');
    
    return `${exclusionStyles}\n${areaStyles}`;
  };

  // 渲染内容区域
  const renderContent = () => {
    const contentClass = cn(contentGap, contentPadding, className)
    
    // 使用自定义滚动配置（如果有）或使用disableScrollArea兼容旧代码
    const shouldDisableScroll = 
      (scrollConfig && scrollConfig.enabled === false) || 
      (!scrollConfig && disableScrollArea);
    
    if (shouldDisableScroll) {
      return <div className={cn("flex-1", contentClass)}>{children}</div>
    }
    
    return (
      <ScrollArea className="flex-1">
        <div className={contentClass}>{children}</div>
      </ScrollArea>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* 动态生成的滚动控制样式 */}
      {scrollConfig && (
        <style jsx global>{`
          ${generateScrollStyles()}
        `}</style>
      )}
      
      {/* 使用共享的PageHeader组件替代原有的header渲染逻辑 */}
      {header ? (
        <div className="flex-none">{header}</div>
      ) : (
        <PageHeader
          title={title}
          subtitle={subtitle}
          actions={actions}
          showHeaderBorder={showHeaderBorder}
          statusCards={statusCards}
          fixedTabs={fixedTabs}
        />
      )}
      
      {/* 内容区域 - 滚动部分 */}
      {renderContent()}
    </div>
  )
} 