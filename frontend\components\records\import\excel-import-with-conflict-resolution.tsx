"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import {
  Upload, FileSpreadsheet, AlertCircle, CheckCircle2, FileUp, DownloadCloud,
  Clock, ArrowRight, AlertTriangle, Info, RotateCcw, Filter, FileText, RefreshCw
} from 'lucide-react';
import type { GridApi, GridReadyEvent } from 'ag-grid-enterprise'; // CHANGE: Import from ag-grid-enterprise
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import {
  ConflictRecord,
  ConflictResolutionAction,
  type ConflictResolution,
  AnalysisResult,
  ImportReportData, // CHANGE: [2025-06-04] 使用ImportReportData替代ImportConfirmResultData
  type SessionInfoData,
  ExcelAnalysisResultPayload // Ensure this is imported if it's a distinct type
} from '@/services/domain/records/import/excel-import-service';
import { ConflictResolutionModal } from './conflict-resolution-modal';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
// 活跃会话提示逻辑已移至Hook中统一处理，不再需要直接导入
import {
  useExcelImportSession,
  ImportSessionStatusEnum
} from '@/hooks/domain/records/import/useExcelImportSession';
import type {
  ExcelAnalysisStats,
  UserDecisionStats
} from '@/services/domain/records/import/excel-import-service';
import { useSession } from 'next-auth/react'; // CHANGE: [2025-01-16] 保留 NextAuth session hook 用于用户身份验证和显示用户信息

import ExcelImportReportSummary from './excel-import-report-summary';
// CHANGE: [2025-06-03] 添加上传保护Hook
import { useUploadProtection } from '@/hooks/shared/use-upload-protection';
// CHANGE: [2025-06-05] 使用专用hook获取详细数据
import { useImportLogDetail } from '@/hooks/domain/records/import/useImportLogDetail';

const logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  debug: console.debug,
};

export type ConflictFilterType = 'all' | 'new' | 'update';

interface ConflictRecordWithAction extends ConflictRecord {
  action: ConflictResolutionAction;
}

export interface ExcelImportWithConflictResolutionProps {
  showTitle?: boolean;
}

type ImportStep = 'select' | 'upload' | 'analyze' | 'confirm' | 'importing' | 'completed' | 'loading' | 'operation_error' | 'session_error';

const COMPLETION_CONFIRM_DELAY = 100;
const DATA_LOADING_PHASES = [
  '准备获取数据', 
  '正在获取分析结果', 
  '处理结果数据',
  '准备显示结果'
];
const MAX_GET_RESULT_RETRIES = 15;
const NORMAL_POLL_INTERVAL = 1000;
const FAST_POLL_INTERVAL = 200;

const ExcelImportWithConflictResolution: React.FC<ExcelImportWithConflictResolutionProps> = ({
  showTitle = true,
}) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isResettingRef = useRef<boolean>(false);
  const previousStatusRef = useRef<string | null>(null);
  const isConfirmingCompletionRef = useRef<boolean>(false);
  const gridApiRef = useRef<GridApi | null>(null); // 新增：存储AG Grid API实例

  const {
    activeSessionInfo,
    isLoadingSession,
    errorLoadingSession,
    isSubmitting,
    fetchSystemActiveSession,
    startNewImport,
    cancelCurrentImport,
    getAnalysisResult,
    confirmImport,
    beginActiveConflictProcessing,
    pendActiveConflictProcessing,
    resetImportState,
    finalImportResults, 
    acknowledgeResults,
    canCurrentUserTakeoverSession,
  } = useExcelImportSession();

  const { data: session, status: sessionStatus } = useSession(); // CHANGE: [2025-01-16] 保留 NextAuth session 用于用户身份验证和显示

  const [derivedCurrentStep, setDerivedCurrentStep] = useState<ImportStep>('select');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [analysisStats, setAnalysisStats] = useState<AnalysisResult | null>(null);
  const [conflictRecordsInternal, setConflictRecordsInternal] = useState<ConflictRecordWithAction[]>([]);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [filterType, setFilterType] = useState<ConflictFilterType>('update'); // 默认 'update'
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [dataLoadingPhase, setDataLoadingPhase] = useState<number>(-1);
  const [dataLoadingProgress, setDataLoadingProgress] = useState<number>(0);
  const [isConfirmingCompletion, setIsConfirmingCompletion] = useState<boolean>(false);
  const [lastAnalysisStatusRef, setLastAnalysisStatusRef] = useState<string | null>(null);
  const [isHardResetting, setIsHardResetting] = useState<boolean>(false);

  // 活跃会话提示逻辑已移至Hook中统一处理

  // CHANGE: [2025-06-03] 文件上传时的页面离开保护
  const isFileUploading = derivedCurrentStep === 'upload' && uploadProgress > 0 && uploadProgress < 100;
  const { setUploadProtection } = useUploadProtection({
    enabled: true,
    isUploading: isFileUploading,
    message: `文件正在上传中 (${uploadProgress}%)，离开页面将中断上传。确定要离开吗？`,
    onBeforeUnload: () => {
      console.log(`[ExcelImport] 用户尝试在文件上传过程中离开页面，当前进度: ${uploadProgress}%`);
      // 可以在这里记录上传中断的日志
    }
  });

  // CHANGE: [2025-06-05] 将useImportLogDetail hook移到组件顶层，修复Hooks顺序错误
  const { reportData: detailedReportData } = useImportLogDetail(
    finalImportResults?.importLogId || null
  );

  // REFACTORED: [2024-07-31] applyConflictFiltersAndSetStates - Atomic function to set filter states.
  const applyConflictFiltersAndSetStates = useCallback((recordsToProcess: ConflictRecordWithAction[], targetFilter: ConflictFilterType) => {
    logger.info(`[applyConflictFiltersAndSetStates] Setting filter type to: '${targetFilter}'. recordsToProcess count: ${recordsToProcess.length}.`);
    
    setFilterType(targetFilter); // This will trigger the AG Grid filtering useEffect

    logger.debug(
      `[applyConflictFiltersAndSetStates] States set: filterType='${targetFilter}'. AG Grid will handle filtering.`
    );
    // Reset data loading indicators as this function is usually called after data processing is complete for the modal.
    setDataLoadingPhase(-1);
    setDataLoadingProgress(0);
    setIsConfirmingCompletion(false); // Assuming modal opening implies data loading for modal is done.
    setLastAnalysisStatusRef(null); // Reset as analysis/conflict data is now processed for display.

  }, [
    setFilterType, 
    setDataLoadingPhase, 
    setDataLoadingProgress, 
    setIsConfirmingCompletion, 
    setLastAnalysisStatusRef
  ]);

  // REFACTORED: [2024-07-31] handleOpenConflictModal - Manages opening the conflict modal with pre-checks.
  const handleOpenConflictModal = useCallback(async () => {
    logger.info("[handleOpenConflictModal] User requested to open conflict modal.");

    // Validate prerequisites
    if (!activeSessionInfo) {
      toast({ title: "错误", description: "无法打开冲突处理：会话信息缺失。", variant: "destructive" });
      logger.warn("[handleOpenConflictModal] Aborted: No activeSessionInfo.");
      return;
    }
    const sessionId = activeSessionInfo.sessionId;
    if (!sessionId) {
      toast({ title: "错误", description: "无法打开冲突处理：会话ID缺失。", variant: "destructive" });
      logger.warn("[handleOpenConflictModal] Aborted: No sessionId.");
      return;
    }
    // CHANGE: [2025-01-16] 业务逻辑：获取用户信息用于会话管理（非权限检查）
    const currentUserId = session?.user?.id || null;
    if (!analysisStats) {
      toast({ title: "错误", description: "无法打开冲突处理：分析结果尚未就绪。", variant: "destructive" });
      logger.warn("[handleOpenConflictModal] Aborted: No analysisStats.");
      return;
    }

    // CHANGE: [2025-06-02] 简化状态处理逻辑 - 直接依赖后端的BeginActiveConflictProcessingView处理4种占用场景
    // 后端已完整实现：无人占用、本人占用、有人占用未超时、有人占用超时的所有处理逻辑
    // 前端只需要调用beginActiveConflictProcessing，让后端统一处理和返回相应的结果
    
    let canProceed = true;
    const importSessionStatus = activeSessionInfo.status; // CHANGE: [2025-01-16] 重命名以避免与NextAuth sessionStatus冲突
    const currentProcessingUserId = activeSessionInfo.processingUser?.id;
    // currentUserId 已在上面定义，用于业务逻辑

    try {
      logger.info(`[handleOpenConflictModal] 开始处理冲突解决模态框，会话状态: ${importSessionStatus}, processing_user: ${currentProcessingUserId}`);
      
      // 直接调用beginActiveConflictProcessing，让后端处理所有场景
      const success = await beginActiveConflictProcessing(sessionId);
      
      if (success) {
        logger.info(`[handleOpenConflictModal] 后端成功处理场景，允许打开冲突处理模态框`);
        // 刷新会话信息以获取最新状态
        await fetchSystemActiveSession(true);
      } else {
        logger.warn(`[handleOpenConflictModal] 后端处理失败，无法打开冲突处理模态框`);
        // beginActiveConflictProcessing内部已经显示了错误提示
        canProceed = false;
      }
    } catch (error) {
      logger.error(`[handleOpenConflictModal] 调用beginActiveConflictProcessing时出错:`, error);
      toast({ 
        title: "网络错误", 
        description: "无法连接到服务器，请检查网络连接", 
        variant: "destructive" 
      });
      canProceed = false;
    }

    // 添加场景处理总结日志
    logger.info(`[handleOpenConflictModal] 场景处理完成 - 执行结果: ${canProceed ? '成功' : '失败'}, 最终状态: ${canProceed ? '允许打开冲突处理' : '阻止操作'}`);

    if (!canProceed) {
      logger.warn("[handleOpenConflictModal] Aborted due to failed pre-checks or takeover.");
      return;
    }

    // Apply the default 'update' filter. AG Grid will handle the actual filtering.
    applyConflictFiltersAndSetStates(conflictRecordsInternal, 'update');

    // Determine if there are any actionable conflicts to show.
    const actionableConflicts = conflictRecordsInternal.filter(r => r.conflictType === 'new' || r.conflictType === 'update');
    if (actionableConflicts.length > 0) {
      setShowConflictModal(true); // Modal becomes visible
      logger.info(`[handleOpenConflictModal] Showing conflict modal. Actionable conflicts: ${actionableConflicts.length}. Default filter 'update' requested.`);
    } else {
      // No 'new' or 'update' records. Modal will not show.
      // User will see 'update' filter selected with an empty grid.
      // If analysisStats.total > 0 (meaning some rows were read, e.g. identical/error), 
      // it implies that all processable records were handled or none existed.
      // If analysisStats.total is 0, it means an empty Excel or no data rows.
      logger.info("[handleOpenConflictModal] No actionable (new/update) conflicts found. Modal will not be shown. 'update' filter requested.");
      toast({ 
        title: "无待处理冲突", 
        description: analysisStats.total > 0 ? "所有记录均为无差异或导入时已自动处理。" : "文件中未找到可处理的数据行。", 
        variant: "default" 
      });
    }
  }, [
    activeSessionInfo, 
    session,       // CHANGE: [2025-01-16] 保留 NextAuth session 用于获取用户信息
    analysisStats, 
    conflictRecordsInternal, 
    beginActiveConflictProcessing, 
    fetchSystemActiveSession,
    applyConflictFiltersAndSetStates, 
    setShowConflictModal, 
    toast
  ]);
  
  // 主要状态效果 - 简化版本，只处理活跃会话提示和基本状态管理
  useEffect(() => {
    if (isLoadingSession && !activeSessionInfo && !errorLoadingSession) {
      setDerivedCurrentStep('loading');
      return;
    }
    
    if (errorLoadingSession && !isSubmitting) {
      logger.warn(`[ExcelImport.StatusEffect] errorLoadingSession detected: ${errorLoadingSession}. Setting step to 'operation_error'.`);
      setDerivedCurrentStep('operation_error');
      return;
    }
    
    if (activeSessionInfo) {
      const statusString = activeSessionInfo.status;
      logger.debug("[ExcelImport.StatusEffect.Entry] Full activeSessionInfo:", JSON.stringify(activeSessionInfo, null, 2));

      // 模态框触发逻辑已移至Hook中统一处理

      // 基本状态管理逻辑保持不变
      switch (statusString) {
        case "upload":
          setDerivedCurrentStep('upload');
          break;
        case "analysis_start":
        case "analyzing":
          setDerivedCurrentStep('analyze');
          break;
        case "analyzed":
          setDerivedCurrentStep('confirm');
          break;
        case "conflict_resolution_started":
        case "conflict_resolution_in_progress":
        case "conflict_resolution_pending":
          setDerivedCurrentStep('confirm');
          break;
        case "conflict_resolution_completed":
          setDerivedCurrentStep('importing');
          break;
        case ImportSessionStatusEnum.IMPORT_QUEUED:
        case ImportSessionStatusEnum.IMPORT_START:
        case ImportSessionStatusEnum.IMPORT_IN_PROGRESS:
          setDerivedCurrentStep('importing');
          break;
        case ImportSessionStatusEnum.IMPORT_COMPLETED_SUCCESSFULLY:
        case ImportSessionStatusEnum.IMPORT_COMPLETED_WITH_ERRORS:
          setDerivedCurrentStep('completed');
          break;
        case "error":
          setDerivedCurrentStep('session_error');
          break;
        case "cancelled":
          toast({ 
            title: "会话已取消", 
            description: activeSessionInfo.errorMessage || '操作已取消。', 
            variant: "default" 
          });
          break;
        case "finalized":
          setDerivedCurrentStep('select');
          break;
        default:
          const knownStatusValues = Object.values(ImportSessionStatusEnum).map(e => e.toString());
          if (!isSubmitting && !knownStatusValues.includes(statusString)) {
            setDerivedCurrentStep('select');
          }
      }
    } else {
      // activeSessionInfo is null
      if (errorLoadingSession && derivedCurrentStep !== 'completed' && derivedCurrentStep !== 'select' && derivedCurrentStep !== 'operation_error' && derivedCurrentStep !== 'session_error') {
        setDerivedCurrentStep('operation_error');
      } else if (finalImportResults && finalImportResults.status) {
        if (derivedCurrentStep !== 'completed') {
          setDerivedCurrentStep('completed');
        }
      } else if (!isSubmitting && derivedCurrentStep !== 'select') {
        setDerivedCurrentStep('select');
      }
    }
  }, [
    activeSessionInfo,
    isLoadingSession,
    errorLoadingSession,
    isSubmitting,
    toast,
    derivedCurrentStep,
    finalImportResults,
    session?.user?.id,
  ]);

  useEffect(() => {
    if (errorLoadingSession) {
      toast({
        title: "操作出错",
        description: errorLoadingSession,
        variant: "destructive",
        duration: 7000,
      });
    }
  }, [errorLoadingSession, toast]);

  // 移除会话存储相关的方法，不再需要

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast({ title: "文件类型错误", description: "请上传Excel格式的文件（.xlsx或.xls）", variant: "destructive"});
        if (fileInputRef.current) fileInputRef.current.value = "";
        setSelectedFile(null);
        return;
      }
      setSelectedFile(file);
      setAnalysisStats(null);
      setConflictRecordsInternal([]);
      // CHANGE: 重置时也清理 finalImportResults (如果之前由旧会话设置)
      // setImportLogDetails(null); // 已移除，由Hook的resetImportState处理
      // END CHANGE
      logger.info('[ExcelImport] File selected:', file.name);
    } else {
      setSelectedFile(null);
    }
  };

  // 移除会话同步逻辑，不再需要 

  useEffect(() => {
    logger.info(`[ExcelImport.derivedCurrentStepEffect] Derived current step changed to: ${derivedCurrentStep}`);
    if (derivedCurrentStep === 'select') {
      // 当我们进入或被强制回到 'select' 步骤时，确保所有相关的UI状态被清理
      logger.info('[ExcelImport.derivedCurrentStepEffect] Now in \'select\' step, ensuring UI components are reset.');
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setUploadProgress(0);
      setAnalysisStats(null); 
      setConflictRecordsInternal([]); 
      setSelectedRowIds([]); 
      setShowConflictModal(false); 
      // setImportLogDetails(null); // 这些UI元素重置由 derivedCurrentStep === 'select' 的 effect 处理
      setDataLoadingPhase(-1); 
      setDataLoadingProgress(0); 
      setIsConfirmingCompletion(false); 
    }
  }, [derivedCurrentStep]);

  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "未选择文件", 
        description: "请先选择一个Excel文件." 
      });
      return;
    }
      
    const lastResetTime = (window as any).lastResetTime || 0;
    const timeSinceLastReset = Date.now() - lastResetTime;
    if (timeSinceLastReset < 2000) { 
      toast({
        title: "请稍候",
        description: "正在清理前一个会话状态，请等待几秒后再试。",
        duration: 3000
      });
      return;
    }
    
    setUploadProgress(0);
    setAnalysisStats(null);
    setConflictRecordsInternal([]);
    // The line below was intended to be removed as setImportLogDetails is no longer defined.
    // setImportLogDetails(null); 

    toast({ title: "处理中...", description: "正在上传并分析文件...", duration: 15000});
    
    const newSessionId = await startNewImport(
      selectedFile,
      undefined, // sheetName, 如果需要可以从UI获取
      (progress) => {
        setUploadProgress(progress);
      }
    );

    if (newSessionId) {
      // 上传和分析开始成功
      // 注意：hook中会根据实际状态决定是否需要启动轮询, 例如 setIsPollingProgress(true) 已在 hook 的 startNewImport 中处理
      // setIsPollingProgress(true); // <-- 已移除
      
      toast({
        title: "文件上传处理中",
        description: `"${selectedFile.name}" 已发送到服务器进行分析。会话ID: ${newSessionId}`,
      });
      
      logger.info(`[ExcelImport] 文件上传成功，开始轮询分析进度, Session ID: ${newSessionId}`);
    } else {
      // 上传或分析启动失败
      if (!errorLoadingSession) { 
        toast({ 
          title: "上传启动失败", 
          description: "未能启动导入过程，请稍后重试。", 
          variant: "destructive" 
        });
      }
      
      // 重置UI到初始状态
      setSelectedFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  const handleConfirmImport = useCallback(async () => {
    const sessionId = activeSessionInfo?.sessionId;
    if (!sessionId) {
      toast({ title: "错误", description: "会话信息丢失，无法提交确认。", variant: "destructive" });
      return;
    }

    // 1. 准备 resolutions (与原来一致)
    let resolutions: ConflictResolution[] = [];
    if (conflictRecordsInternal && conflictRecordsInternal.length > 0) {
      resolutions = conflictRecordsInternal.map(c => ({
        commissionNumber: c.commissionNumber,
        action: c.action, 
      }));
    }

    // 2. 准备 analysisStatsForApi (从组件state的analysisStats进行键名映射)
    let analysisStatsForApi: ExcelAnalysisStats | undefined = undefined;
    if (analysisStats) { // analysisStats 是组件的 state: AnalysisResult {total, new, update, identical, error}
      analysisStatsForApi = {
        totalRowsRead: analysisStats.total,         // 映射到新键名
        failedRows: analysisStats.error,           // 映射到新键名
        skippedIdentical: analysisStats.identical, // 映射到新键名
        foundNewCount: analysisStats.new,         // 映射到新键名
        foundUpdateCount: analysisStats.update,     // 映射到新键名
      };
    } else {
      logger.warn("[ExcelImport] handleConfirmImport: analysisStats is null. Backend might receive default/zero values for analysis phase.");
      analysisStatsForApi = {
        totalRowsRead: 0,
        failedRows: 0,
        skippedIdentical: 0,
        foundNewCount: 0,
        foundUpdateCount: 0,
      };
    }

    // 3. 准备 userDecisionStatsForApi (从 conflictRecordsInternal 计算)
    let skippedUpdateCount = 0;
    let confirmedUpdateCount = 0;
    if (conflictRecordsInternal && conflictRecordsInternal.length > 0) {
      conflictRecordsInternal.forEach(record => {
        if (record.conflictType === 'update') { 
          if (record.action === ConflictResolutionAction.SKIP) {
            skippedUpdateCount++;
          }
          else if (record.action === ConflictResolutionAction.UPDATE) {
            confirmedUpdateCount++;
          }
        }
      });
    }
    const userDecisionStatsForApi: UserDecisionStats = {
      skippedUpdateCount,
      confirmedUpdateCount,
    };

    logger.info(`[ExcelImport] Submitting for session ${sessionId}:`,
        `Resolutions count: ${resolutions.length}`,
        `Analysis Stats for API: ${JSON.stringify(analysisStatsForApi)}`,
        `User Decision Stats for API: ${JSON.stringify(userDecisionStatsForApi)}`
    );

    // 4. 调用 confirmImport hook 函数
    // confirmImport now returns a promise that resolves to an object like { success: boolean, message?: string } or null
    const result = await confirmImport(
      sessionId, 
      resolutions,
      analysisStatsForApi, 
      userDecisionStatsForApi  
    ); 

    // Check if the submission was successfully initiated
    if (result && result.success) {
      setShowConflictModal(false); 
      
      toast({
        title: "导入任务已提交",
        description: result.message || "您的数据导入请求已成功提交到后台处理。系统状态将自动更新。",
        variant: "default",
        duration: 5000,
      });
      // UI will automatically transition to 'importing' via activeSessionInfo updates from the hook polling.
    } else {
      // This case handles both hook-internal errors (via errorLoadingSession) and explicit API failure responses.
      const errorMessage = result?.message || errorLoadingSession || "提交导入请求时发生未知错误，请重试。";
      toast({ title: "确认导入失败", description: errorMessage, variant: "destructive" });
      logger.error("[ExcelImport.handleConfirmImport] Submission failed. Result:", result, "Hook error:", errorLoadingSession);
    }
  }, [
    activeSessionInfo, 
    analysisStats, 
    conflictRecordsInternal, 
    confirmImport, 
    toast, 
    errorLoadingSession
  ]);

  useEffect(() => {
    const sessionId = activeSessionInfo?.sessionId;
    const sessionStatus = activeSessionInfo?.status as ImportSessionStatusEnum;
    const statusesNeedingAnalysisResults = [
      ImportSessionStatusEnum.ANALYSIS_COMPLETE,
      ImportSessionStatusEnum.CONFLICT_RESOLUTION_STARTED,
      ImportSessionStatusEnum.CONFLICT_RESOLUTION_IN_PROGRESS,
      ImportSessionStatusEnum.CONFLICT_RESOLUTION_PENDING,
      // ImportSessionStatusEnum.CONFLICT_RESOLUTION_COMPLETED, // Data for this might be too transient
    ];
    
    if (
      sessionId && 
      statusesNeedingAnalysisResults.includes(sessionStatus) &&
      !isSubmitting &&
      !analysisStats && // Ensure analysisStats is not already populated
      !isConfirmingCompletionRef.current && // Ensure not already fetching/confirming
      (derivedCurrentStep === 'analyze' || derivedCurrentStep === 'confirm')
    ) {
      logger.info(`[ExcelImport.FetchResultsEffect] Conditions met. SessionId: ${sessionId}, Status: ${sessionStatus}, isSubmitting: ${isSubmitting}, analysisStats: ${!!analysisStats}, derivedCurrentStep: ${derivedCurrentStep}`);
      
      if (isConfirmingCompletionRef.current) return;
      
      const fetchResultsAndProcess = async () => {
        if (isResettingRef.current) { 
            logger.info('[ExcelImport.FetchResults] Reset in progress, skipping fetch.');
            return;
        }
        
        logger.info(`[ExcelImport.FetchResults] Session ${sessionId} status ${sessionStatus}. Fetching analysis results.`);
        isConfirmingCompletionRef.current = true;
        setIsConfirmingCompletion(true);
        setDataLoadingPhase(0);
        setDataLoadingProgress(0);
        toast({ title: "分析完成", description: "正在获取详细分析结果...", duration: 2500 });
        let resultRetryCount = 0;
        
        const attemptGetResult = async (): Promise<void> => {
          if (isResettingRef.current) return;
          try {
            await new Promise(resolve => setTimeout(resolve, 1200)); // Simulating network delay
            if (isResettingRef.current) return;
            
            setDataLoadingPhase(1); // "正在获取分析结果"
            setDataLoadingProgress(25);
            logger.info(`[ExcelImport.FetchResults] Attempt ${resultRetryCount + 1}/${MAX_GET_RESULT_RETRIES}`);
            const resultsPayload = await getAnalysisResult(sessionId) as ExcelAnalysisResultPayload | null;
            if (isResettingRef.current) return; 
            if (!resultsPayload) throw new Error("获取分析结果为空，请稍后重试");
            
            setDataLoadingPhase(2); // "处理结果数据"
            setDataLoadingProgress(65);
            
            await new Promise(resolve => setTimeout(resolve, 500)); // Simulating processing delay
              if (isResettingRef.current) return;
              setDataLoadingPhase(3); // "准备显示结果"
              setDataLoadingProgress(85);
              
            await new Promise(resolve => setTimeout(resolve, 300)); // Simulating final prep delay
                if (isResettingRef.current) return;
            
            logger.info(`[ExcelImport.FetchResults] Success`, resultsPayload);
                isConfirmingCompletionRef.current = false;
                setIsConfirmingCompletion(false);
                setDataLoadingProgress(100);
                setAnalysisStats(resultsPayload.analysisStats || null);
                const conflicts = resultsPayload.conflictDetails || [];
                const conflictsWithAction = conflicts.map(c => ({
                  ...c,
              action: c.conflictType === 'new' ? ConflictResolutionAction.CREATE :
                      ConflictResolutionAction.UPDATE // Default all others to UPDATE initially
                }));
                setConflictRecordsInternal(conflictsWithAction);

            // Apply default filter state without opening modal
            applyConflictFiltersAndSetStates(conflictsWithAction, 'update');
            
            // Show notification about analysis results without auto-opening modal
            const actionableConflicts = conflictsWithAction.filter(r => r.conflictType === 'new' || r.conflictType === 'update');
            if (actionableConflicts.length > 0) {
              logger.info(`[ExcelImport.FetchResults] Analysis complete. Found ${actionableConflicts.length} actionable conflicts. Modal can be opened manually.`);
              toast({ 
                title: "分析完成", 
                description: `发现 ${actionableConflicts.length} 条需要处理的冲突记录。请点击"处理冲突记录"按钮查看。`, 
                variant: "default",
                duration: 4000
              });
            } else {
              logger.info("[ExcelImport.FetchResults] Analysis complete. No actionable conflicts found.");
              toast({ 
                title: "分析完成", 
                description: resultsPayload.analysisStats && resultsPayload.analysisStats.total > 0 ? "所有记录均为无差异或新记录，可以直接导入。" : "文件中未找到可处理的数据行。", 
                variant: "default" 
              });
            }
            
          } catch (error: any) {
            if (isResettingRef.current) return;
            logger.error("[ExcelImport.FetchResults] Error calling getAnalysisResult:", error);
            if (error.name === 'AnalysisNotCompleteError' || error.message?.includes('分析尚未完成')) {
              resultRetryCount++;
              logger.warn(`[ExcelImport.FetchResults] Backend not ready, retry (${resultRetryCount}/${MAX_GET_RESULT_RETRIES})`);
              setDataLoadingProgress(Math.min(20 + resultRetryCount * 5, 60));
              if (resultRetryCount < MAX_GET_RESULT_RETRIES) {
                // ... (retry logic unchanged) ...
              } else {
                // ... (max retries unchanged) ...
              }
            } else {
              // ... (other error handling unchanged) ...
            }
          }
        };
        attemptGetResult();
      };
      fetchResultsAndProcess();
    }
  }, [
    activeSessionInfo, 
    derivedCurrentStep, 
    getAnalysisResult, 
    toast, 
    isSubmitting, 
    errorLoadingSession, 
    analysisStats, // Added analysisStats to dependency array
    handleOpenConflictModal, // Dependency
    applyConflictFiltersAndSetStates, // Added applyConflictFiltersAndSetStates as it's used in the flow after getAnalysisResult
  ]);

  const updateConflictAction = useCallback((commissionNumber: string, excelRowNumber: number, newAction: ConflictResolutionAction) => {
    setConflictRecordsInternal(prev => 
              prev.map(c => (c.commissionNumber === commissionNumber && c.excelRowNumber === excelRowNumber) ? { ...c, action: newAction } : c)
    );
  }, []);

  const updateAllConflictsAction = useCallback((action: ConflictResolutionAction, conflictTypeFilter?: 'new' | 'update' | 'identical') => {
    setConflictRecordsInternal(prev =>
      prev.map(c => {
        if (conflictTypeFilter && c.conflictType !== conflictTypeFilter) return c;
        if (c.conflictType === 'new' || c.conflictType === 'identical') return c; 
        return { ...c, action };
      })
    );
    toast({ title: "批量操作完成", description: `符合条件的记录已设为 "${action === ConflictResolutionAction.SKIP ? '跳过' : '更新'}"。`});
  }, [toast]);
  
  const updateSelectedRowsAction = useCallback((action: ConflictResolutionAction, rowIds: string[]) => {
    if (!rowIds.length) return;
    
    setConflictRecordsInternal(prev => 
      prev.map(c => {
        // 构造行ID并检查是否在选中行中
        const rowId = `${c.commissionNumber}-${c.excelRowNumber}`;
        if (!rowIds.includes(rowId)) return c;
        
        // 不允许更改新记录和无差异记录的操作
        if (c.conflictType === 'new' || c.conflictType === 'identical') return c;
        
        return { ...c, action };
      })
    );
    
    toast({ 
      title: "批量操作完成", 
      description: `已将${rowIds.length}条选中记录设为"${action === ConflictResolutionAction.SKIP ? '跳过' : '更新'}"。`
    });
  }, [toast]);
  
  const handleSelectionChanged = useCallback((selectedIds: string[]) => {
    setSelectedRowIds(selectedIds);
  }, []);

  const renderStep = () => { 
    // CHANGE: 优先渲染 errorLoadingSession 的错误（来自Hook的API/网络级错误）
    // 这个错误通常更全局，应优先于会话内部的错误状态
    if (derivedCurrentStep === 'operation_error' && errorLoadingSession) { // 'error' changed to 'operation_error'
        return (
            <div className="p-4">
                <Alert variant="destructive" className="max-w-lg mx-auto shadow-md">
                    <AlertCircle className="h-6 w-6" />
                    <AlertTitle className="font-semibold text-lg">系统操作错误</AlertTitle>
                    <AlertDescription>
                        <p className="mb-2">加载或操作会话时遇到问题: {errorLoadingSession || "发生未知错误。"}</p>
                        <p className="text-xs text-gray-500 mt-3">您可以尝试刷新或稍后重试。</p>
                    </AlertDescription>
                                    <div className="mt-6 flex justify-end space-x-3">
                    <Button onClick={async () => {
                        logger.info('[UI.ErrorStep] User clicked fetch latest state.');
                        toast({ title: "正在重试", description: "正在重新获取服务器状态...", duration: 3000});
                        
                        // 1. 重置本地状态
                        await resetImportState();
                        
                        // 2. 重新获取后端真实状态
                        try {
                            await fetchSystemActiveSession(false); // 非静默模式
                            toast({ title: "状态已更新", description: "已获取最新的服务器状态", duration: 2000 });
                        } catch (error) {
                            toast({ title: "获取状态失败", description: "仍无法连接服务器，请检查网络连接", variant: "destructive" });
                        }
                     }} variant="outline">
                        <RefreshCw className="mr-2 h-4"/> 重新获取状态
                    </Button>
                    {activeSessionInfo?.sessionId && (
                        <Button onClick={async () => {
                            logger.info(`[UI.ErrorStep] User clicked force reset session: ${activeSessionInfo.sessionId}`);
                            toast({ title: "正在强制重置", description: "正在终止当前会话...", duration: 3000});
                            await acknowledgeResults(activeSessionInfo.sessionId);
                            // Hook will auto-update UI after acknowledgeResults -> fetchSystemActiveSession -> FINALIZED
                        }} variant="destructive">
                            <RotateCcw className="mr-2 h-4"/> 强制重置会话
                        </Button>
                    )}
                </div>
                </Alert>
            </div>
        );
    }
    
    if (isLoadingSession && derivedCurrentStep === 'loading') { 
        return (
            <div className="text-center p-8 min-h-[200px] flex flex-col justify-center items-center">
                <Clock className="h-12 w-12 mx-auto animate-spin text-primary mb-4" />
                <p className="text-muted-foreground">正在加载最新的会话信息，请稍候...</p>
            </div>
        );
    }
    switch (derivedCurrentStep) {
      case 'select':
        // 移除isSystemBusy逻辑，因为如果用户被正确引导到select步骤，
        // 说明没有活跃会话或会话已终结，不需要额外检查
        return (
          <div className="space-y-6 py-1">
            <Alert variant="default" className="bg-sky-50 border-sky-200 text-sky-700">
              <Info className="h-5 w-5 text-sky-600" />
              <AlertTitle className="font-semibold text-sky-800">操作指引：开始导入</AlertTitle>
              <AlertDescription className="text-sky-700/90">
                请选择一个有效的Excel文件 (.xlsx 或 .xls) 。系统将首先对文件进行预分析以检测与现有数据的潜在冲突，之后您可以对这些冲突进行处理并最终确认导入操作。
              </AlertDescription>
            </Alert>
            <div className="space-y-2">
              <Label htmlFor="excel-file-input" className="text-md font-medium text-gray-800">1. 选择您的Excel文件</Label>
              <div 
                className={`relative group border-2 border-dashed rounded-xl p-6 text-center transition-all duration-150 ease-in-out min-h-[180px] flex flex-col justify-center items-center 
                            ${isSubmitting ? 'border-gray-300 bg-gray-100 cursor-not-allowed opacity-70' : 'border-gray-300 hover:border-primary hover:shadow-md'}`}
                onClick={() => !isSubmitting && fileInputRef.current?.click()}
                role="button" aria-disabled={!!isSubmitting} tabIndex={isSubmitting ? -1 : 0}
              >
                <Input 
                  id="excel-file-input" 
                  ref={fileInputRef} 
                  type="file" 
                  accept=".xlsx,.xls,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" 
                  onChange={handleFileChange} 
                  className="sr-only" 
                  disabled={!!isSubmitting} 
                />
                {isSubmitting && selectedFile ? ( 
                    <div className="flex flex-col items-center">
                        <Clock className="h-12 w-12 text-primary mb-3 animate-spin" />
                        <p className="font-semibold text-md text-primary">文件处理启动中...</p>
                    </div>
                ) : selectedFile ? (
                  <div className="flex flex-col items-center">
                    <FileSpreadsheet className="h-12 w-12 text-green-600 mb-3" />
                    <p className="font-semibold text-md text-gray-700 truncate max-w-xs sm:max-w-md md:max-w-lg" title={selectedFile.name}>{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">({(selectedFile.size / 1024).toFixed(1)} KB)</p>
                    <Button variant="ghost" size="sm" className="text-xs mt-2 text-red-600 hover:text-red-700 hover:bg-red-50 h-auto py-1 px-2" onClick={(e) => { e.stopPropagation(); setSelectedFile(null); if(fileInputRef.current) fileInputRef.current.value=""; }}> <RotateCcw className="mr-1 h-3 w-3"/> 清除选择</Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <DownloadCloud className="h-12 w-12 text-gray-400 group-hover:text-primary mb-3 transition-colors duration-150" />
                    <p className="font-semibold text-md text-gray-700 group-hover:text-primary transition-colors">点击此处或拖拽文件上传</p>
                    <p className="text-sm text-muted-foreground mt-1">支持 .xlsx, .xls 格式的文件</p>
                  </div>
                )}
              </div>
            </div>
            <Button type="button" onClick={handleFileUpload} disabled={!selectedFile || isSubmitting} className="w-full py-3 text-md font-semibold" size="lg">
              {isSubmitting ? <><Clock className="mr-2 h-5 w-5 animate-spin" />处理启动中...</> : <><Upload className="mr-2 h-5 w-5" />上传并分析</>}
            </Button>
            {/* 移除了混乱的Alert逻辑，因为用户访问时会被自动引导到正确的会话状态 */}
          </div>
        );
      case 'upload':
      case 'analyze':
        const displayFileNameAnalyze = activeSessionInfo?.fileName || selectedFile?.name || '未知文件';
        // CHANGE: Use activeSessionInfo.recordCount for total records display in analysis, fallback if needed.
        const displayTotalRecordsAnalyze = activeSessionInfo?.recordCount || 0; 
        const displaySelectedFileSizeAnalyze = selectedFile ? `${(selectedFile.size/1024).toFixed(1)}KB` : '';
        
        let currentProgressPercentAnalyze = 0;
        let progressStageTitle = '服务器正在分析数据';
        let progressTypeLabel = '分析进度';

        if (activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD) {
            currentProgressPercentAnalyze = uploadProgress;
            progressStageTitle = '文件上传中';
            progressTypeLabel = '上传进度';
        } else if (activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_IN_PROGRESS || activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_START) {
            // CHANGE: Use activeSessionInfo.progress directly for analysis progress percentage.
            currentProgressPercentAnalyze = activeSessionInfo?.progress ?? 0;
        } else if (activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_COMPLETE) {
            // CHANGE: [2025-01-16] 移除旧逻辑：ANALYSIS_COMPLETE状态下不再在analyze步骤中处理数据获取
            // 数据获取现在在confirm步骤中进行
            currentProgressPercentAnalyze = 100;
            progressStageTitle = '数据分析已完成';
            progressTypeLabel = '分析结果';
        } else if (activeSessionInfo?.status === ImportSessionStatusEnum.CONFLICT_RESOLUTION_STARTED || 
                   activeSessionInfo?.status === ImportSessionStatusEnum.CONFLICT_RESOLUTION_IN_PROGRESS ||
                   activeSessionInfo?.status === ImportSessionStatusEnum.CONFLICT_RESOLUTION_PENDING ||
                   activeSessionInfo?.status === ImportSessionStatusEnum.CONFLICT_RESOLUTION_COMPLETED) {
            // CHANGE: [2025-01-16] 移除旧逻辑：这些状态下不再在analyze步骤中处理
            // 这些状态应该直接在confirm步骤中处理
            currentProgressPercentAnalyze = 100;
            progressStageTitle = '数据分析已完成'; 
            progressTypeLabel = '分析结果';
        }
        
        const isAnalysisActuallyComplete = activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_COMPLETE;
        const isStillProcessingBackend = activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_IN_PROGRESS || activeSessionInfo?.status === ImportSessionStatusEnum.ANALYSIS_START;
        
        return (
          <div className="space-y-6 py-4">
            <Alert variant="default" className={`p-4 rounded-lg border shadow-sm ${activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD ? 'bg-blue-50 border-blue-300 text-blue-800' : (dataLoadingPhase >= 0 ? 'bg-purple-50 border-purple-300 text-purple-800' : 'bg-amber-50 border-amber-300 text-amber-800')}`}>
              <div className="flex items-center">
                <div className={`flex-shrink-0 p-3 rounded-full mr-4 ${activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD ? 'bg-blue-100' : (dataLoadingPhase >= 0 ? 'bg-purple-100' : 'bg-amber-100')}`}>
                  {activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD ?
                       <FileUp className="h-7 w-7 text-blue-700" /> :
                       (dataLoadingPhase >= 0 ?
                           <Info className={`h-7 w-7 text-purple-700 animate-pulse`} /> :
                          <Info className={`h-7 w-7 text-amber-700 ${isSubmitting || isLoadingSession || isStillProcessingBackend ? 'animate-pulse' : ''}`} />
                      )
                  }
                </div>
                <div className="flex-1">
                  <AlertTitle className="font-bold text-lg">{progressStageTitle}</AlertTitle>
                  <AlertDescription className="mt-1 text-sm">
                    <span className="block font-medium" title={displayFileNameAnalyze}>文件: {displayFileNameAnalyze} ({displayTotalRecordsAnalyze > 0 ? `${displayTotalRecordsAnalyze} 条记录` : displaySelectedFileSizeAnalyze})</span>
                    {activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD && '您的文件正在安全上传到服务器。上传完成后将自动开始数据分析过程。'}
                    {derivedCurrentStep === 'analyze' && (
                      dataLoadingPhase >= 0 ?
                         `正在从服务器加载分析后的数据，请稍候...` :
                         (isAnalysisActuallyComplete ? '分析结果很快准备就绪，请稍候...' : '系统正在处理您的数据并识别潜在冲突，请耐心等候。此过程可能需要数分钟。' )
                    )}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
            <div className="space-y-2 px-1">
              <div className="flex items-baseline justify-between text-sm mb-1">
                <span className="font-medium text-gray-700">{progressTypeLabel}</span>
                <span className={`font-semibold text-xl ${activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD ? 'text-blue-600' : 'text-amber-600'}`}>
                  {currentProgressPercentAnalyze.toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700 overflow-hidden shadow-inner">
                <div
                  className={`h-4 rounded-full transition-all duration-300 ease-out flex items-center justify-center text-white text-xs font-medium
                     ${activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD ? 'bg-blue-500' : 'bg-amber-500'}
                     ${(isSubmitting || isStillProcessingBackend) && !isAnalysisActuallyComplete ? 'animate-pulse' : ''}`}
                  style={{ width: `${currentProgressPercentAnalyze}%` }}
                >
                  {currentProgressPercentAnalyze > 10 ? `${currentProgressPercentAnalyze.toFixed(0)}%` : ''}
                </div>
              </div>
              {/* CHANGE: Remove dependency on hookAnalysisProgress for scanned records display. 
                   This information might not be available directly in activeSessionInfo for this specific display format.
                   Consider if activeSessionInfo.record_count (total) and progress (percentage) are sufficient, or if a new field is needed from backend.
                   For now, this specific line about scanned records is removed as its direct source is gone.
              */}
              {/* {(derivedCurrentStep === 'analyze' && (hookAnalysisProgress?.total_records ?? 0) > 0 && activeSessionInfo?.status !== ImportSessionStatusEnum.UPLOAD && dataLoadingPhase < 0) && (
                 <p className="text-xs text-muted-foreground text-right mt-1.5">
                   已扫描 {hookAnalysisProgress?.analyzed_records || 0} / {hookAnalysisProgress?.total_records || 0} 条记录
                 </p>
               )} */}
               {/* CHANGE: [2025-01-16] 移除analyze步骤中的dataLoadingPhase阶段显示，现在在confirm步骤中处理 */}
               {/* {dataLoadingPhase >= 0 && (
                 <p className="text-xs text-purple-600 text-right mt-1.5">
                   正在加载阶段 {dataLoadingPhase + 1}/4: {DATA_LOADING_PHASES[dataLoadingPhase]}
                 </p>
               )} */}
            </div>
            {(isSubmitting || isStillProcessingBackend || activeSessionInfo?.status === ImportSessionStatusEnum.UPLOAD) && !isAnalysisActuallyComplete && (
                 <div className="text-center text-sm text-muted-foreground mt-10 flex items-center justify-center">
                    <Clock className="inline-block mr-2 h-4 w-4 animate-spin" />
                     <span>处理进行中，请勿关闭或刷新此页面...</span>
                 </div>
            )}
            {/* 数据加载阶段提示 */}
            {/* CHANGE: [2025-01-16] 移除analyze步骤中的数据加载阶段提示，数据获取现在在confirm步骤中进行 */}
            {/* {dataLoadingPhase >= 0 && (
                <div className="text-center text-sm text-purple-600 mt-10 flex items-center justify-center">
                    <Clock className="inline-block mr-2 h-4 w-4 animate-spin" />
                    <span>正在加载分析结果数据，请稍候...</span>
                </div>
            )} */}
            {(isStillProcessingBackend && !isAnalysisActuallyComplete) && (
                <div className="flex justify-center mt-10">
                    <Button
                         variant="destructive"
                        onClick={async () => {
                          if (activeSessionInfo?.sessionId) {
                            logger.info(`[UI.AnalyzeStep] 用户请求取消分析中的会话 ${activeSessionInfo.sessionId}`);
                            toast({ title: "操作请求中", description: "正在尝试取消当前分析操作...", duration: 3000});
                            await cancelCurrentImport(activeSessionInfo.sessionId);
                            // UI will update based on activeSessionInfo changing to CANCELLED/FINALIZED
                          } else {
                            logger.warn('[UI.AnalyzeStep] 取消按钮点击，但无活动会话ID。');
                            toast({ title: "操作提示", description: "当前无活动会话可取消。", variant: "destructive"});
                          }
                        }}
                         disabled={isSubmitting || !activeSessionInfo?.sessionId}
                        className="shadow-md hover:shadow-lg transition-shadow px-6 py-3"
                    >
                        <RotateCcw className="mr-2 h-4 w-4"/> 强制取消并重置
                    </Button>
                </div>
            )}
          </div>
        );
      case 'confirm':
        logger.info(`[ExcelImport.RenderConfirm] Rendering CONFLICT RESOLUTION / CONFIRM SUMMARY UI.`);
        
        // CHANGE: [2025-06-02] 按用户设计：在confirm步骤中处理数据获取显示
        if (!analysisStats && dataLoadingPhase >= 0) {
          // 显示数据获取进度条
          const displayFileNameDataLoading = activeSessionInfo?.fileName || selectedFile?.name || '未知文件';
          const currentDataLoadingProgress = dataLoadingProgress;
          
          return (
            <div className="space-y-6 py-4">
              <Alert variant="default" className="p-4 rounded-lg bg-purple-50 border-purple-300 text-purple-800 shadow-sm">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 rounded-full mr-4 bg-purple-100">
                    <Info className="h-7 w-7 text-purple-700 animate-pulse" />
                  </div>
                  <div className="flex-1">
                    <AlertTitle className="font-bold text-lg">分析结果数据加载中 ({DATA_LOADING_PHASES[dataLoadingPhase]})</AlertTitle>
                    <AlertDescription className="mt-1 text-sm">
                      <span className="block font-medium" title={displayFileNameDataLoading}>文件: {displayFileNameDataLoading}</span>
                      正在从服务器加载分析后的数据，请稍候...
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
              <div className="space-y-2 px-1">
                <div className="flex items-baseline justify-between text-sm mb-1">
                  <span className="font-medium text-gray-700">数据加载进度</span>
                  <span className="font-semibold text-xl text-purple-600">
                    {currentDataLoadingProgress.toFixed(0)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700 overflow-hidden shadow-inner">
                  <div
                    className="h-4 rounded-full transition-all duration-300 ease-out flex items-center justify-center text-white text-xs font-medium bg-purple-500 animate-pulse"
                    style={{ width: `${currentDataLoadingProgress}%` }}
                  >
                    {currentDataLoadingProgress > 10 ? `${currentDataLoadingProgress.toFixed(0)}%` : ''}
                  </div>
                </div>
                <p className="text-xs text-purple-600 text-right mt-1.5">
                  正在加载阶段 {dataLoadingPhase + 1}/4: {DATA_LOADING_PHASES[dataLoadingPhase]}
                </p>
              </div>
              <div className="text-center text-sm text-purple-600 mt-10 flex items-center justify-center">
                <Clock className="inline-block mr-2 h-4 w-4 animate-spin" />
                <span>正在加载分析结果数据，请稍候...</span>
              </div>
            </div>
          );
        }
        
        // 原有的confirm界面逻辑（当analysisStats存在时）
        const analysisTotalRecordsConfirm = analysisStats?.total || 0;
        // CHANGE: [2025-01-17] 修复冲突记录统计错误 - 只计算真正需要用户处理的冲突记录（new和update类型）
        const currentConflictCountConfirm = conflictRecordsInternal.filter(r => r.conflictType === 'new' || r.conflictType === 'update').length;
          
        return (
          <div className="space-y-6 py-4">
              <Alert variant="default" className="p-4 rounded-lg bg-green-50 border border-green-300 text-green-800 shadow-sm">
                <CheckCircle2 className="h-6 w-6 mr-3 flex-shrink-0 text-green-600" />
                <div>
                  <AlertTitle className="font-bold text-lg">Excel文件分析完成</AlertTitle>
                  <AlertDescription className="text-sm">
                    系统已完成对 <span className="font-semibold">{activeSessionInfo?.fileName || selectedFile?.name || '您上传的Excel文件'}</span> 的分析。
                    请查看下面的摘要并处理潜在的数据冲突（如有）。
                  </AlertDescription>
                </div>
              </Alert>
              {analysisStats && (
                <div className="pt-2">
                  <h4 className="text-md font-semibold mb-3 text-gray-800">分析结果摘要:</h4>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3 text-center">
                    <Card className="p-3 shadow"><p className="text-sm text-muted-foreground">总记录</p><p className="text-2xl font-bold text-gray-700">{analysisStats.total || 0}</p></Card>
                    <Card className="p-3 bg-green-50 shadow"><p className="text-sm text-green-700">新记录</p><p className="text-2xl font-bold text-green-700">{analysisStats.new || 0}</p></Card>
                    <Card className="p-3 bg-yellow-50 shadow"><p className="text-sm text-yellow-700">可更新</p><p className="text-2xl font-bold text-yellow-700">{analysisStats.update || 0}</p></Card>
                    <Card className="p-3 bg-blue-50 shadow"><p className="text-sm text-blue-700">无差异</p><p className="text-2xl font-bold text-blue-700">{analysisStats.identical || 0}</p></Card>
                    <Card className="p-3 bg-red-50 shadow"><p className="text-sm text-red-700">错误/待定</p><p className="text-2xl font-bold text-red-700">{analysisStats.error || 0}</p></Card>
                  </div>
                </div>
              )}
              {currentConflictCountConfirm > 0 ? (
                <div className="mt-6 space-y-4">
                  <Alert variant="default" className="border-amber-400 bg-amber-50 text-amber-800 shadow-sm">
                      <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0"/>
                      <div className="ml-3 flex-1">
                          <AlertTitle className="font-semibold">发现需要处理的记录</AlertTitle>
                          <AlertDescription className="mt-1">
                              检测到 <span className="font-bold">{currentConflictCountConfirm}</span> 条记录需要您的处理决策（新记录或数据更新）。请点击下方按钮查看并选择处理方式。
                              对于无差异的记录，系统会自动跳过，无需用户干预。
                          </AlertDescription>
                      </div>
                  </Alert>
                  <Button 
                    onClick={handleOpenConflictModal} // CHANGE: Corrected function name
                    className="w-full py-3 text-md font-semibold" 
                    size="lg" 
                    disabled={isSubmitting}
                  >
                    <Filter className="mr-2 h-5 w-5" /> 处理 {currentConflictCountConfirm} 条待决策记录
                  </Button>
                </div>
              ) : (analysisStats?.identical || 0) > 0 || (analysisStats?.error || 0) > 0 ? (
                <Alert variant="default" className="border-green-400 bg-green-50 text-green-800 shadow-sm">
                  <CheckCircle2 className="h-5 w-5 text-green-600 flex-shrink-0"/>
                   <div className="ml-3 flex-1">
                      <AlertTitle className="font-semibold">无需用户处理</AlertTitle>
                      <AlertDescription className="mt-1">
                          分析完成，所有记录均为无差异记录或存在错误。无差异记录将自动跳过，错误记录需要修正后重新导入。
                      </AlertDescription>
                   </div>
                </Alert>
              ) : (
                   <Alert variant='default' className="border-gray-300 bg-gray-50 text-gray-700">
                      <Info className="h-5 w-5 text-gray-500" />
                      <AlertTitle>无数据记录</AlertTitle>
                      <AlertDescription>分析完成，但Excel文件中未找到可操作的数据行。</AlertDescription>
                  </Alert>
              )}
              <Separator className="my-8" />
              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="outline" 
                  onClick={async () => {
                    if (activeSessionInfo?.sessionId) {
                      logger.info(`[UI.ConfirmStep] 用户在确认步骤选择取消并重选，会话 ${activeSessionInfo.sessionId}`);
                      toast({ title: "操作请求中", description: "正在取消当前分析结果并准备重新选择文件...", duration: 3000});
                      await cancelCurrentImport(activeSessionInfo.sessionId);
                      // UI will update based on activeSessionInfo changing to CANCELLED/FINALIZED
                    } else {
                      logger.info('[UI.ConfirmStep] 用户在确认步骤选择取消，但无活动会话，直接重置前端。');
                      toast({ title: "操作完成", description: "已重置，请重新选择文件。", duration: 3000});
                      await resetImportState();
                    }
                  }}
                  className="flex-1 py-3 text-md" 
                  disabled={isSubmitting}
                >
                  <RotateCcw className="mr-2 h-4 w-4"/> 取消并重新选择文件
                </Button>
                {/* CHANGE: [2025-06-03] 根据用户要求移除"应用解决方案并导入"按钮 */}
                {/*
                <Button 
                  onClick={handleConfirmImport} // This leads to the actual import
                  className="flex-1 py-6 text-lg font-semibold bg-green-600 hover:bg-green-700 shadow-md hover:shadow-lg transition-all"
                  disabled={isSubmitting || !activeSessionInfo?.session_id || analysisTotalRecordsConfirm === 0 }
                  size="lg"
                >
                  {isSubmitting ? <><Clock className="mr-2 h-5 w-5 animate-spin"/>请求处理中...</> : 
                   (currentConflictCountConfirm > 0 ? '应用解决方案并导入' : (analysisTotalRecordsConfirm > 0 ? '全部确认并开始导入' : '无数据可导入'))}
                </Button>
                */}
              </div>
              {currentConflictCountConfirm > 0 && !showConflictModal && (
                  <p className="text-xs text-center text-muted-foreground mt-3">提示：请先点击"处理冲突记录"按钮解决所有冲突，或确认默认操作，然后再进行导入。</p>
              )}
            </div>
          );
      case 'importing': // New case for when import is actually in progress
        const importProgressPercent = activeSessionInfo?.progress ?? 0;
        logger.info(`[ExcelImport.RenderImporting] Rendering IMPORTING UI with progress: ${importProgressPercent}`);
        return (
          <div className="space-y-6 py-4">
            <Alert variant="default" className="p-4 rounded-lg bg-sky-50 border border-sky-300 text-sky-800 shadow-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-3 rounded-full mr-4 bg-sky-100">
                  <Clock className="h-7 w-7 text-sky-700 animate-spin" />
                </div>
                <div className="flex-1">
                  <AlertTitle className="font-bold text-lg">正在导入数据...</AlertTitle>
                  <AlertDescription className="mt-1 text-sm">
                    系统正在将记录写入数据库，请耐心等待。({activeSessionInfo?.fileName || '您的文件'})
                  </AlertDescription>
                </div>
              </div>
            </Alert>
            <div className="space-y-2 px-1">
              <div className="flex items-baseline justify-between text-sm mb-1">
                <span className="font-medium text-gray-700">导入进度</span>
                <span className="font-semibold text-xl text-sky-600">
                  {importProgressPercent.toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700 overflow-hidden shadow-inner">
                <div
                  className="h-4 rounded-full transition-all duration-300 ease-out flex items-center justify-center text-white text-xs font-medium bg-sky-500 animate-pulse"
                  style={{ width: `${importProgressPercent}%` }}
                >
                  {importProgressPercent > 10 ? `${importProgressPercent.toFixed(0)}%` : ''}
                </div>
              </div>
               {(activeSessionInfo && (activeSessionInfo as any).processed_records_in_import !== undefined && (activeSessionInfo as any).total_records_for_import !== undefined) && (
                  <p className="text-xs text-muted-foreground text-right mt-1.5">
                      已处理 { (activeSessionInfo as any).processed_records_in_import } / { (activeSessionInfo as any).total_records_for_import } 条记录
                  </p>
               )}
            </div>
            <div className="text-center text-sm text-muted-foreground mt-10 flex items-center justify-center">
              <Info className="inline-block mr-2 h-4 w-4" />
              <span>此过程可能需要一些时间，请勿关闭或刷新页面。</span>
            </div>
          </div>
        );

      // CHANGE: [2024-07-30] Consolidate completed_successfully and completed_with_errors into 'completed'
      case 'completed':
        if (!finalImportResults) { 
          // CHANGE: [2025-06-03] 修复状态转换时序问题 - 避免在finalized或null session状态转换时显示错误UI
          
          // 检查是否为正常的状态转换过程
          const isNormalTransition = activeSessionInfo?.status === 'finalized' || 
                                    (activeSessionInfo === null || activeSessionInfo === undefined);
          
          if (isNormalTransition) {
            logger.info("[ExcelImport.RenderStep.Completed] Normal state transition in progress, showing loading state.");
            return (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600 mx-auto mb-4"></div>
                <p className="font-semibold text-lg">处理完成，正在返回...</p>
                <p className="text-sm text-muted-foreground">请稍候，系统正在更新界面状态。</p>
              </div>
            );
          }
          
          // CHANGE: [2025-06-03] 只有在非正常转换状态下才记录详细调试信息和显示错误UI
          logger.warn("[ExcelImport.RenderStep.Completed] finalImportResults is null for session in non-transition state.", {
            activeSessionStatus: activeSessionInfo?.status,
            activeSessionId: activeSessionInfo?.sessionId,
            derivedCurrentStep,
            finalImportResultsType: typeof finalImportResults,
            finalImportResultsValue: finalImportResults
          });
          return (
            <div className="p-8 text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
              <p className="font-semibold text-lg">导入已完成，但无法加载详细日志。</p>
              <p className="text-sm text-muted-foreground">请尝试刷新页面，或检查历史导入记录以获取详情。</p>
              {activeSessionInfo && activeSessionInfo.sessionId ? (
                <Button 
                  onClick={async () => {
                    logger.info(`[UI.CompletedNoDetails] User clicked acknowledge for session ${activeSessionInfo.sessionId}`);
                    await acknowledgeResults(activeSessionInfo.sessionId!); 
                  }} 
                  variant="outline" size="sm" className="mt-6" disabled={isSubmitting}>
                  <CheckCircle2 className="mr-2 h-4 w-4"/> 确认并结束
              </Button>
              ) : (
                <Button onClick={async () => {
                    logger.info('[UI.CompletedNoDetails] No active session, resetting UI.');
                    toast({ title: "操作完成", description: "界面已重置。", duration: 3000});
                    await resetImportState();
                }} variant="outline" size="sm" className="mt-6">
                  <RotateCcw className="mr-2 h-4 w-4"/> 重置界面
                </Button>
              )}
            </div>
          );
        }
        
        // Type guard for finalImportResults, though the if above should handle null
        const currentFinalResults: ImportReportData = finalImportResults!;

        const excelColumnMapping: Record<string, string> = {
          sample_number: "样品编号",
          commission_number: "委托编号",
          report_number: "报告编号",
          commission_datetime: "委托日期",
          project_name: "工程名称",
          client_unit: "委托单位",
          test_parameters: "检测参数",
        };

        // 如果有详细数据就用详细数据，否则用基本数据
        const reportDataToUse = detailedReportData || finalImportResults;

        const reportData = {
          ...reportDataToUse,
          status: reportDataToUse.status as 'completed' | 'partial' | 'failed',
          importLogId: reportDataToUse.importLogId === null ? undefined : reportDataToUse.importLogId,
          fileName: activeSessionInfo?.fileName || undefined,
        };

        return (
          <div className="space-y-6 py-4">
            <ExcelImportReportSummary 
              reportData={reportData}
              excelColumnNameMapping={excelColumnMapping}
            />

            {/* CHANGE: [2025-05-30] 根据用户反馈调整按钮 */}
            {/* 主要操作是确认结果并结束会话，这将通过状态流转引导到新的导入流程 */}
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4 mt-8">
              {/* 按钮1: 确认结果并结束当前会话 (调用 acknowledgeResults) */}
              {activeSessionInfo && (activeSessionInfo.status === ImportSessionStatusEnum.IMPORT_COMPLETED_SUCCESSFULLY ||
                                   activeSessionInfo.status === ImportSessionStatusEnum.IMPORT_COMPLETED_WITH_ERRORS) && (
                <Button 
                  onClick={async () => {
                    if (activeSessionInfo?.sessionId) {
                      const ackSuccess = await acknowledgeResults(activeSessionInfo.sessionId);
                      if (ackSuccess) {
                        logger.info("[UI.CompletedStep] Acknowledge results succeeded.");
                        toast({ 
                          title: "操作成功", 
                          description: "导入结果已确认。系统将返回文件选择界面。", 
                          duration: 3000 
                        });
                        // ackSuccess -> hook updates activeSessionInfo (to FINALIZED) -> useEffect updates derivedCurrentStep to 'select'
                      } else {
                        logger.warn("[UI.CompletedStep] Acknowledge results failed.");
                        // 错误应由 acknowledgeResults hook 内的 errorLoadingSession 处理并显示toast
                      }
                    }
                  }}
                  variant="default" 
                  className="w-full sm:w-auto bg-sky-600 hover:bg-sky-700 text-white"
                  disabled={isSubmitting} 
                >
                  {isSubmitting && 
                   (activeSessionInfo?.status === ImportSessionStatusEnum.IMPORT_COMPLETED_SUCCESSFULLY ||
                    activeSessionInfo?.status === ImportSessionStatusEnum.IMPORT_COMPLETED_WITH_ERRORS) ? 
                    <><Clock className="mr-2 h-4 w-4 animate-spin"/> 正在确认...</> : 
                    <><CheckCircle2 className="mr-2 h-4 w-4"/> 确认结果并结束会话</> 
                  }
                </Button>
              )}

              {/* 按钮2: 查看详细导入日志 (如果可用) */}
              {finalImportResults?.importLogId && (
                <Button asChild className="w-full sm:w-auto" variant="outline">
                  <Link href={`/records/import-history/${finalImportResults.importLogId}`}> <FileText className="mr-2 h-4 w-4"/> 查看详细导入日志</Link>
                </Button>
              )}
              
              {/* 根据用户反馈，已移除此处直接调用handleReset的"开始新的导入"按钮 */}
              {/* 用户如果想彻底重新开始，可以在其他步骤（如select，或错误处理后）进行，或者在确认当前结果后自然回到select步骤 */}
            </div>
          </div>
        ); 
      case 'session_error': // 'error_display' changed to 'session_error'
        const isTimeoutError = activeSessionInfo?.errorMessage?.includes('超时') || 
                               activeSessionInfo?.errorMessage?.includes('自动终止');
        const isNetworkError = activeSessionInfo?.errorMessage?.includes('网络') || 
                               activeSessionInfo?.errorMessage?.includes('连接');
        const isConcurrencyError = activeSessionInfo?.errorMessage?.includes('系统当前已有一个正在进行的导入会话');
        
        let displayErrorTitle = "导入过程中发生错误"; 
        let errorGuidance = "您可以确认此错误后开始新的导入。如果问题持续出现，请联系技术支持。";
        // CHANGE: [2025-06-04] recoverySuggestions 现在可以是字符串数组或 React.JSX.Element 数组
        let recoverySuggestions: (string | React.JSX.Element)[] = [];
        
        if (isTimeoutError && activeSessionInfo) {
          displayErrorTitle = "导入会话已过期";
          // CHANGE: [2025-06-04] 更新 errorGuidance 和 recoverySuggestions 以显示详细会话信息
          errorGuidance = "本次导入会话因超时已自动终止。"; // 或者为空，如果所有信息都在下面
          recoverySuggestions = [
            <p key="sid" className="mb-0.5"><span className="font-medium">会话ID:</span> {activeSessionInfo.sessionId}</p>,
            <p key="fname" className="mb-0.5"><span className="font-medium">文件名:</span> {activeSessionInfo.fileName || '未知文件'}</p>,
            <p key="creator" className="mb-0.5">
              <span className="font-medium">创建者:</span> {activeSessionInfo.createdBy?.username || "未知用户"}
              {session?.user?.id && activeSessionInfo.createdBy?.id && 
               String(activeSessionInfo.createdBy.id) === String(session.user.id) && 
                <span className="text-green-600 font-bold">（本人）</span>
              }
            </p>,
            <p key="ctime" className="mb-0.5"><span className="font-medium">创建时间:</span> {activeSessionInfo.createdAt ? new Date(activeSessionInfo.createdAt).toLocaleString() : '未知'}</p>,
            <p key="etime"><span className="font-medium">过期时间:</span> {activeSessionInfo.expiresAt ? new Date(activeSessionInfo.expiresAt).toLocaleString() : '未设置'}</p>
          ];
        } else if (isNetworkError) {
          displayErrorTitle = "网络连接问题";
          errorGuidance = "导入过程中遇到网络连接问题。";
          recoverySuggestions = [
            "检查网络连接是否稳定",
            "刷新页面后重新尝试",
            "如果问题持续，请联系网络管理员"
          ];
        } else if (isConcurrencyError && activeSessionInfo) {
          displayErrorTitle = "并发会话冲突";
          errorGuidance = activeSessionInfo.errorMessage + " 请等待该会话完成后再尝试，或联系管理员处理。";
          recoverySuggestions = [
            "等待当前活跃会话完成",
            "在导入页面查看会话状态",
            "如有权限，可尝试接管会话"
          ];
        }
        // END CHANGE

        // CHANGE: [2025-05-31] 按钮文本更新，以反映调用 acknowledgeResults 的行为
        const buttonText = "确认错误并结束会话"; 

        return (
          <div className="p-4">
            <Alert variant="destructive" className="max-w-lg mx-auto shadow-md">
              <AlertTriangle className="h-6 w-6" />
              <AlertTitle className="font-semibold text-lg">{displayErrorTitle}</AlertTitle>
              {/* CHANGE: [2025-06-04] 还原 AlertDescription 结构，并根据 isTimeoutError 调整 recoverySuggestions 的渲染方式 */}
              <AlertDescription>
                <p className="my-2">
                  处理文件 "<strong>{activeSessionInfo?.fileName || '您上传的文件'}</strong>" 时遇到以下问题：
                </p>
                <div className="font-mono bg-red-50 p-3 rounded text-xs text-red-700 border border-red-200 whitespace-pre-wrap max-h-48 overflow-y-auto">
                  {activeSessionInfo?.errorMessage || '服务器未提供具体的错误信息。'}
                </div>
                <p className="text-sm text-gray-600 mt-3">
                  {errorGuidance}
                </p>
                {recoverySuggestions.length > 0 && (
                  <div className="mt-3">
                    {isTimeoutError && activeSessionInfo ? (
                                              <div className="bg-red-50 p-3 rounded border border-red-200 text-sm space-y-1">
                        <p className="font-medium text-red-800 mb-2">会话信息</p>
                        {recoverySuggestions.map((item) => item)}
                      </div>
                    ) : (
                      <>
                        <p className="text-sm font-medium text-gray-700 mb-2">建议操作：</p>
                        <ul className="list-disc pl-6 space-y-1">
                          {recoverySuggestions.map((suggestion, index) => (
                            typeof suggestion === 'string' && <li key={index} className="text-sm text-gray-600">{suggestion}</li>
                          ))}
                        </ul>
                      </>
                    )}
                  </div>
                )}
              </AlertDescription>
              {/* END CHANGE */}
              <div className="mt-6 flex justify-end space-x-3">
                <Button 
                  onClick={async () => {
                    if (activeSessionInfo?.sessionId) {
                      logger.info(`[UI.ErrorDisplay] 用户确认错误 (会话: ${activeSessionInfo.sessionId})。调用 acknowledgeResults。`);
                      await acknowledgeResults(activeSessionInfo.sessionId);
                      // UI will update after acknowledgeResults -> fetchSystemActiveSession -> FINALIZED -> useEffect
                    } else {
                      logger.warn('[UI.ErrorDisplay] 尝试确认错误但会话ID丢失。重置前端。');
                      toast({ title: "操作提示", description: "前端状态已重置。", duration: 3000 });
                      await resetImportState();
                    }
                  }}
                  variant="outline"
                  disabled={isSubmitting}
                >
                  <CheckCircle2 className="mr-2 h-4 w-4"/> {buttonText} {/* buttonText should be "确认错误并结束会话" */}
                </Button>
              </div>
            </Alert>
          </div>
        );
      default:
        return (
            <div className="text-center p-8 min-h-[200px] flex flex-col justify-center items-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-muted-foreground">步骤 ({derivedCurrentStep}) 未知或正在加载中...</p>
            </div>
        );
    }
  };
  
  const renderConflictDialog = () => { 
      if (!showConflictModal || !activeSessionInfo) return null; 
      logger.debug(`[renderConflictDialog] Rendering. conflictRecordsInternal length: ${conflictRecordsInternal?.length}, isArray: ${Array.isArray(conflictRecordsInternal)}`); // Log here
      return (
        <ConflictResolutionModal
          open={showConflictModal}
          allConflictRecords={conflictRecordsInternal} // 修改：传递全量数据
          analysisResult={analysisStats}
          onClose={async () => {
            // CHANGE: [2025-06-01] 关闭冲突处理模态框时调用pendActiveConflictProcessing
            if (activeSessionInfo?.sessionId) {
              logger.info(`[ExcelImport] Closing conflict modal, calling pendActiveConflictProcessing for session: ${activeSessionInfo.sessionId}`);
              await pendActiveConflictProcessing(activeSessionInfo.sessionId);
            }
            setShowConflictModal(false);
          }}
          onResolve={handleConfirmImport}
          isProcessing={isSubmitting}
          updateAction={updateConflictAction}
          updateAllActions={updateAllConflictsAction}
          updateSelectedActions={updateSelectedRowsAction}
          currentFilter={filterType}
          onFilterChange={setFilterType} // 这个setFilterType会触发上面的useEffect来应用Grid筛选
          selectedRowIds={selectedRowIds}
          onSelectionChanged={handleSelectionChanged}
          onGridReady={(api: GridApi) => { // 修改：onGridReady只设置gridApiRef
            gridApiRef.current = api;
            logger.info(`[onGridReady ExcelImport] Grid API instance has been set.`);
            // 初始筛选逻辑已移除，将由useEffect处理
          }}
        />
      );
  };
  
  // CHANGE: [2025-06-02] Refactored filtering and modal display logic into a helper function
  // This function definition is removed as it's now part of the main component body, defined earlier with useCallback.
  
  // CHANGE: [2025-06-02] 修复AG Grid v32+ overlay一直显示的根本问题
  // 问题根源：
  // 1. AG Grid v32+ 在使用applyTransaction时loading overlay不会自动移除 (GitHub issue #8358)
  // 2. 双重筛选机制导致冲突：父组件setFilterModel + 子组件filteredData
  // 3. 筛选状态不同步导致显示异常
  //
  // 解决方案：
  // 1. 移除父组件中的AG Grid API筛选操作
  // 2. 让ConflictResolutionGrid完全控制自己的数据和overlay状态
  // 3. 使用官方推荐的loading=false和适当的overlay管理
  // 4. 避免手动hideOverlay()调用，遵循v32+最佳实践
  
  // useEffect(() => {
  //   if (gridApiRef.current && !gridApiRef.current.isDestroyed() && showConflictModal) {
  //     logger.info(`[AG_Grid_Filter_Effect] Applying filter. Type: ${filterType}. Modal visible: ${showConflictModal}.`);
      
  //     const filterModel = filterType === 'all' ? null : {
  //       conflict_type: {
  //         type: 'equals',
  //         filter: filterType,
  //       },
  //     };
      
  //     gridApiRef.current.setFilterModel(filterModel);
  //     // CHANGE: [2025-06-02] 移除hideOverlay()调用，遵循AG Grid v32+最佳实践
  //     logger.info(`[AG_Grid_Filter_Effect] Filter applied.`);
  //   } else if (gridApiRef.current && !gridApiRef.current.isDestroyed() && !showConflictModal) {
  //     gridApiRef.current.setFilterModel(null);
  //     logger.info(`[AG_Grid_Filter_Effect] Modal hidden. Clearing AG Grid filter.`);
  //   }
  // }, [filterType, showConflictModal]);
  
  return (
    <div className="overflow-hidden">
      <div className="p-4 sm:p-6">
        <div className="mb-6">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5, 6].map((stepNum, index, arr) => {
              let stepName = "";
              let isActive = false;
              let isCompleted = false;
              
              switch (stepNum) {
                case 1: 
                  stepName = "选择文件"; 
                  isActive = derivedCurrentStep === 'select'; 
                  isCompleted = ['upload', 'analyze', 'confirm', 'importing', 'completed'].includes(derivedCurrentStep); 
                  break;
                case 2: 
                  stepName = "上传文件"; 
                  isActive = derivedCurrentStep === 'upload'; 
                  isCompleted = ['analyze', 'confirm', 'importing', 'completed'].includes(derivedCurrentStep); 
                  break;
                case 3: 
                  stepName = "数据分析"; 
                  isActive = derivedCurrentStep === 'analyze' || derivedCurrentStep === 'loading'; 
                  isCompleted = ['confirm', 'importing', 'completed'].includes(derivedCurrentStep); 
                  break;
                case 4: 
                  stepName = "处理冲突"; 
                  isActive = derivedCurrentStep === 'confirm'; 
                  isCompleted = ['importing', 'completed'].includes(derivedCurrentStep); 
                  break;
                case 5: 
                  stepName = "正在导入";
                  isActive = derivedCurrentStep === 'importing'; 
                  isCompleted = ['completed'].includes(derivedCurrentStep); 
                  break;
                case 6: 
                  stepName = "导入报告";
                  isActive = derivedCurrentStep === 'completed'; 
                  isCompleted = derivedCurrentStep === 'completed'; 
                  break;
              }
              
              const displayStepNum = isCompleted && stepNum !== 6 ? <CheckCircle2 size={16}/> : stepNum;
              
              return (
                <React.Fragment key={stepNum}>
                  <div className="flex flex-col items-center">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 
                      ${isActive ? 'bg-primary border-primary text-white shadow-lg' : 
                        isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                        'bg-gray-100 border-gray-300 text-gray-500'} 
                      transition-all duration-300`}
                    >
                      {displayStepNum}
                    </div>
                    <p className={`mt-1.5 text-xs ${(isActive || isCompleted) ? 
                      'text-primary font-semibold' : 'text-muted-foreground'}`}>
                      {stepName}
                    </p>
                  </div>
                  {index < arr.length - 1 && (
                    <div className={`flex-1 h-1 mx-2 rounded 
                      ${isCompleted ? 'bg-primary' : 'bg-gray-200'} 
                      transition-colors`}>
                    </div>
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>
        
        <div className="min-h-[300px]">
          {renderStep()}
        </div>
      </div>
      {renderConflictDialog()}
    </div>
  );
};

export default ExcelImportWithConflictResolution;
