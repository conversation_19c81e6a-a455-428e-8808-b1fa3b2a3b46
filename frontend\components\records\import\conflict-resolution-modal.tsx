import React, { useCallback, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Filter } from "lucide-react";
import { Clock } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ConflictResolutionGrid } from './conflict-resolution-grid';
import { 
  type ConflictRecord,
  ConflictResolutionAction,
  type AnalysisResult
} from '@/services/domain/records/import/excel-import-service';
import { Label } from "@/components/ui/label";
import type { GridApi } from 'ag-grid-enterprise';

// CHANGE: [2024-07-30] 移除 'identical' 筛选类型。如果父组件也定义了，确保同步。
export type ConflictFilterType = 'all' | 'new' | 'update';

// 扩展冲突记录类型，添加用户选择的操作
interface ConflictRecordWithAction extends ConflictRecord {
  action: ConflictResolutionAction;
}

// 冲突解决方案
interface ConflictResolution {
  commissionNumber: string;
  action: ConflictResolutionAction;
}

export interface ConflictResolutionModalProps {
  /** 控制模态框开关 */
  open: boolean;
  /** 全量的冲突记录 (用于表格rowData和计算筛选计数) */
  allConflictRecords: ConflictRecordWithAction[];
  /** 分析结果 */
  analysisResult: AnalysisResult | null;
  /** 关闭模态框的回调 */
  onClose: () => void;
  /** 处理冲突解决方案的回调 */
  onResolve: () => void;
  /** 处理中状态 */
  isProcessing?: boolean;
  /** 更新单个冲突记录的处理动作 */
  updateAction: (commissionNumber: string, excelRowNumber: number, newAction: ConflictResolutionAction) => void;
  /** 批量更新冲突记录的处理动作 */
  updateAllActions: (action: ConflictResolutionAction, conflictTypeFilter?: 'new' | 'update') => void;
  /** 批量更新选中的记录的处理动作 */
  updateSelectedActions?: (action: ConflictResolutionAction, selectedRowIds: string[]) => void;
  /** 当前筛选类型 */
  currentFilter: ConflictFilterType;
  /** 筛选记录类型的回调 */
  onFilterChange: React.Dispatch<React.SetStateAction<ConflictFilterType>>;
  /** 目前选中的行 */
  selectedRowIds?: string[];
  /** 选中行变更回调 */
  onSelectionChanged?: (selectedRowIds: string[]) => void;
  /** Callback when grid is ready, passing the GridApi instance */
  onGridReady?: (api: GridApi) => void;
}

/**
 * @component ConflictResolutionModal
 * A modal dialog component for displaying and resolving Excel import conflicts.
 * It shows a summary of analysis results, allows filtering of conflicts, provides batch actions,
 * and embeds the `ConflictResolutionGrid` to display and interact with individual conflict records.
 *
 * @param {ConflictResolutionModalProps} props - Props for the ConflictResolutionModal component.
 * @see ConflictResolutionGrid
 * @see ExcelImportWithConflictResolution (Parent component that typically uses this modal)
 */
export function ConflictResolutionModal({
  open,
  allConflictRecords,
  analysisResult,
  onClose,
  onResolve,
  isProcessing = false,
  updateAction,
  updateAllActions,
  updateSelectedActions,
  currentFilter,
  onFilterChange,
  selectedRowIds = [],
  onSelectionChanged,
  onGridReady,
}: ConflictResolutionModalProps) {

  const handleApplyAll = useCallback(() => {
    onResolve();
  }, [onResolve]);

  // 确定是否有选中的行
  const hasSelectedRows = selectedRowIds.length > 0;

  // 批量更新选中行的处理方法
  const onBatchUpdateSelected = useCallback((action: ConflictResolutionAction) => {
    if (updateSelectedActions && selectedRowIds.length > 0) {
      updateSelectedActions(action, selectedRowIds);
    }
  }, [updateSelectedActions, selectedRowIds]);

  // Determine the actual conflict type to filter for batch actions
  // If currentFilter is 'all', we still only want to batch-apply to 'update' types.
  // If currentFilter is 'update', we apply to 'update' types.
  const batchActionTargetType = (currentFilter === 'all' || currentFilter === 'update') ? 'update' : undefined;
  
  return (
    <Dialog open={open} onOpenChange={onClose} modal={true}>
      <DialogContent 
        onOpenAutoFocus={(e) => e.preventDefault()}
        className="max-w-6xl max-h-[90vh] flex flex-col bg-white shadow-lg rounded-lg overflow-hidden p-2"
      >
        <DialogHeader className="px-3 py-1">
          <DialogTitle className="text-base">确认Excel导入冲突处理方式</DialogTitle>
          <DialogDescription className="text-xs">
            导入时发现以下冲突，请确认如何处理这些记录
          </DialogDescription>
        </DialogHeader>
        
        {/* 冲突统计信息 - 极简布局 */}
        {analysisResult && (
          <div className="grid grid-cols-5 gap-0.5 mb-1 text-center px-3">
            <Card className="py-0 px-0.5"><p className="text-xs text-muted-foreground leading-tight">总记录</p><p className="text-sm font-bold leading-tight">{analysisResult.total || 0}</p></Card>
            <Card className="py-0 px-0.5 bg-green-50"><p className="text-xs text-green-700 leading-tight">新记录</p><p className="text-sm font-bold text-green-700 leading-tight">{analysisResult.new || 0}</p></Card>
            <Card className="py-0 px-0.5 bg-yellow-50"><p className="text-xs text-yellow-700 leading-tight">可更新</p><p className="text-sm font-bold text-yellow-700 leading-tight">{analysisResult.update || 0}</p></Card>
            <Card className="py-0 px-0.5 bg-blue-50"><p className="text-xs text-blue-700 leading-tight">无差异</p><p className="text-sm font-bold text-blue-700 leading-tight">{analysisResult.identical || 0}</p></Card>
            <Card className="py-0 px-0.5 bg-red-50"><p className="text-xs text-red-700 leading-tight">错误</p><p className="text-sm font-bold text-red-700 leading-tight">{analysisResult.error || 0}</p></Card>
          </div>
        )}
        
        {/* 批量操作和筛选 - 极简布局 */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-1 px-3" role="region" aria-label="批量操作和筛选控制区">
          <div className="flex gap-1 items-center" role="group" aria-label="批量处理操作">
            <span className="text-xs font-medium">批量处理:</span>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-5 py-0 px-1 text-xs"
              onClick={() => onBatchUpdateSelected(ConflictResolutionAction.UPDATE)}
              disabled={currentFilter !== 'update' || !hasSelectedRows || isProcessing}
              title="将所有选中行的操作设为'更新'"
              aria-label="将选中的记录设为更新操作"
            >
              选中行设为更新
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-5 py-0 px-1 text-xs"
              onClick={() => onBatchUpdateSelected(ConflictResolutionAction.SKIP)}
              disabled={currentFilter !== 'update' || !hasSelectedRows || isProcessing}
              title="将所有选中行的操作设为'跳过'"
              aria-label="将选中的记录设为跳过操作"
            >
              选中行设为跳过
            </Button>
          </div>
          <div className="flex gap-1 items-center" role="group" aria-label="筛选控制">
            <Label htmlFor="conflict-filter-select" className="text-xs font-medium">筛选:</Label>
            <Select 
              value={currentFilter} 
              onValueChange={(value) => onFilterChange(value as ConflictFilterType)}
              disabled={isProcessing}
            >
              <SelectTrigger 
                className="w-auto sm:w-[180px] h-5 text-xs" 
                id="conflict-filter-select"
                aria-label="选择要显示的冲突记录类型"
              >
                <SelectValue placeholder="筛选冲突类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有待处理记录 ({allConflictRecords.filter(c => c.conflictType === 'new' || c.conflictType === 'update').length})</SelectItem>
                <SelectItem value="update">可更新 ({allConflictRecords.filter(c => c.conflictType === 'update').length})</SelectItem>
                <SelectItem value="new">新记录 ({allConflictRecords.filter(c => c.conflictType === 'new').length})</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* 使用AG Grid冲突解决组件 - 进一步增加表格高度 */}
        <div style={{ height: "calc(90vh - 180px)", width: "100%", display: "flex", marginBottom: "2px" }}>
          <ConflictResolutionGrid 
            conflicts={allConflictRecords}
            onResolutionChange={updateAction}
            onSelectionChanged={onSelectionChanged}
            currentFilter={currentFilter}
            onGridReadyCallback={onGridReady}
          />
        </div>
        
        <DialogFooter className="mt-0 px-3 py-1">
          <Button variant="outline" size="sm" className="h-6 text-xs" onClick={onClose} disabled={isProcessing}>取消</Button>
          <Button size="sm" className="h-6 text-xs" onClick={handleApplyAll} disabled={isProcessing || allConflictRecords.filter(c => c.conflictType === 'new' || c.conflictType === 'update').length === 0}>
            {isProcessing ? <><Clock className="mr-1 h-3 w-3 animate-spin" /> 处理中...</> : '确认操作并导入'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default ConflictResolutionModal; 