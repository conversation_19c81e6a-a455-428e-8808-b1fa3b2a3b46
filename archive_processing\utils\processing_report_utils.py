"""用于生成处理报告和摘要的工具函数。"""

import logging
import os
import time # 导入 time 模块
from typing import List, Tuple, Optional, Dict, Any, Union, cast
# CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 获取 PDF 页数 #AFM-Refactor-PDFLib
import fitz  # PyMuPDF
from ..dto.processing_dtos import ProcessingResultDto
from .system_utils import format_time # 假设 system_utils 在同一目录下

logger = logging.getLogger(__name__)

def create_result_summary(
    input_pdf_path: str, 
    result_dto: Union[ProcessingResultDto, Dict[str, Any]],
    archived_files: List[Tuple[Optional[str], Optional[str]]], 
    output_dir: str, 
    status_update: Optional[Dict] = None,
    pre_check_errors: Optional[Dict] = None
) -> Optional[str]:
    """
    创建处理结果摘要文件。

    Args:
        input_pdf_path: 输入的原始PDF文件路径。
        result_dto: PdfProcessingService 返回的处理结果对象 或 简化的字典版本。
        archived_files: 包含最终归档文件信息 (路径, 统一编号) 的元组列表。
                        如果某部分未成功归档，路径可能为 None。
        output_dir: 摘要文件输出目录。
        status_update: 档案状态更新结果字典 (可选)。
        pre_check_errors (Dict, optional): 包含预检查错误信息的字典，
                                          结构如 {'parts_missing_number': [...], 'numbers_missing_record': [...] }。

    Returns:
        成功创建的摘要文件路径，如果发生错误则返回 None。
    """
    # 检查result_dto是否为None或无效
    if result_dto is None:
        logger.error("无法创建摘要：处理结果DTO为None")
        return None

    # 处理简化版DTO（字典格式）
    is_simplified_dto = isinstance(result_dto, dict)
    
    # 检查所需属性是否存在
    if is_simplified_dto:
        if "stats" not in result_dto:
            logger.error("无法创建摘要：简化版DTO缺少stats字段")
            return None
        stats = result_dto["stats"]
        unified_numbers = result_dto.get("unified_numbers", [])
        success = result_dto.get("success", True)  # 简化版默认为成功
        error_message = result_dto.get("error_message", None)
        recognition_stats = result_dto.get("recognition_stats", None)
    else:
        # 标准ProcessingResultDto对象
        if not hasattr(result_dto, "stats") or result_dto.stats is None:
            logger.error("无法创建摘要：处理结果DTO缺少有效的stats属性")
            return None
        stats = result_dto.stats
        unified_numbers = getattr(result_dto, "unified_numbers", {})
        success = getattr(result_dto, "success", True)
        error_message = getattr(result_dto, "error_message", None)
        recognition_stats = getattr(result_dto, "recognition_stats", None)

    timestamp = time.strftime("%Y%m%d_%H%M%S") # 获取当前时间戳
    summary_filename = f"处理结果摘要_{os.path.splitext(os.path.basename(input_pdf_path))[0]}_{timestamp}.txt"
    summary_path = os.path.join(output_dir, summary_filename)
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        with open(summary_path, "w", encoding="utf-8") as f:
            f.write("=== PDF分割处理摘要 ===\n\n")
            f.write(f"原始文件: {os.path.basename(input_pdf_path)}\n")
            processing_successful = success and not pre_check_errors
            f.write(f"处理结果: {'成功' if processing_successful else '失败'}\n")
            if not success and error_message:
                 f.write(f"处理错误信息: {error_message}\n")

            # 提取和显示处理统计信息
            if is_simplified_dto:
                # 简化版DTO使用不同的统计字段
                total_pages = stats.get("total_pages", 0) 
                processing_time = stats.get("processing_time", 0)
                pages_per_second = stats.get("pages_per_second", 0)
            else:
                # 标准DTO
                total_pages = stats.total_pages
                processing_time = stats.processing_time
                pages_per_second = stats.pages_per_second
                 
            f.write(f"处理时间: {format_time(processing_time)}\n")
            f.write(f"总页数: {total_pages}\n")
            f.write(f"处理速度: {pages_per_second:.2f} 页/秒\n\n")
            
            if pre_check_errors:
                f.write("*** ⚠️ 预检查失败，处理已中止 ***\n")
                parts_missing = pre_check_errors.get('parts_missing_number')
                records_missing = pre_check_errors.get('numbers_missing_record')
                if parts_missing:
                    f.write("以下部分未能识别统一编号:\n")
                    for part_info in parts_missing:
                        f.write(f"  - {str(part_info)}\n")
                if records_missing:
                    f.write("以下统一编号在数据库中未找到对应记录:\n")
                    for number in records_missing:
                        f.write(f"  - {number}\n")
                f.write("\n")
            
            f.write("--- 分割与归档结果 ---\n")
            if pre_check_errors:
                f.write("(由于预检查失败，未执行分割与归档操作)\n")
            elif not archived_files:
                 f.write("未生成或归档任何文件部分\n")
            else:
                 for i, archive_info in enumerate(archived_files):
                     file_path, unified_number = archive_info
                     if file_path and os.path.exists(file_path):
                         try:
                             output_filename = os.path.basename(file_path)
                             page_count_str = "?"
                             file_size_kb = 0
                             try:
                                 # CHANGE: [2024-07-25] 使用 PyMuPDF 替换 PyPDF2 获取 PDF 页数 #AFM-Refactor-PDFLib
                                 doc = fitz.open(file_path)
                                 page_count_str = str(doc.page_count)
                                 doc.close()  # 确保关闭文档释放资源
                             except Exception as e:
                                 logger.warning(f"无法读取归档文件 {output_filename} 的页数: {e}")
                             try:
                                 file_size_kb = os.path.getsize(file_path) / 1024
                             except Exception:
                                  logger.warning(f"无法获取归档文件 {output_filename} 的大小。")

                             f.write(f"  文件 {i+1}: {output_filename} ({page_count_str} 页, {file_size_kb:.1f} KB) -> 编号: {unified_number or '未关联'}\n")
                         except Exception as read_err:
                             f.write(f"  - 文件 {i+1} ({os.path.basename(file_path)}) - 读取信息时出错: {read_err}\n")
                     elif file_path:
                         f.write(f"  - 文件 {i+1} ({os.path.basename(file_path)}) - 未找到文件 -> 编号: {unified_number or '未关联'}\n")
                     else:
                         f.write(f"  - 文件 {i+1}: 未成功归档 -> 尝试关联编号: {unified_number or '无'}\n")

            
            # 预构建统一编号部分的内容字符串
            unified_numbers_content = "\n--- 识别到的统一编号 (页码 -> 编号) ---\n"
            if is_simplified_dto:
                # 简化版DTO中统一编号为列表
                if not unified_numbers:
                    unified_numbers_content += "未识别到任何统一编号\n"
                else:
                    unified_numbers_content += f"识别到 {len(unified_numbers)} 个统一编号:\n"
                    for number in unified_numbers:
                        unified_numbers_content += f"  - {number}\n"
            else:
                # 标准DTO中统一编号为映射
                if not unified_numbers:
                    unified_numbers_content += "未识别到任何统一编号\n"
                else:
                    # 按页码排序显示
                    for page_num in sorted(unified_numbers.keys()):
                        display_page = page_num + 1 # 页码从1开始显示
                        unified_numbers_content += f"第{display_page}页: {unified_numbers[page_num] or '识别失败'}\n"
            
            # 一次性写入整个统一编号部分
            f.write(unified_numbers_content)
            
            # 读取并显示识别方法统计
            f.write("\n--- 识别方法效果统计 ---\n")
            if recognition_stats:
                # 按计数值降序排序显示
                for method, count in sorted(recognition_stats.items(), key=lambda item: item[1], reverse=True):
                    f.write(f"  {method}: {count}\n")
            else:
                f.write("  无统计数据\n")
            
            # 添加状态更新结果
            f.write("\n--- 档案状态更新结果 ---\n")
            if pre_check_errors:
                f.write("(由于预检查失败，未执行状态更新操作)\n")
            elif status_update:
                f.write(f"总计尝试更新/检查的统一编号: {status_update.get('total', '未知')}\n")
                f.write(f"更新成功: {status_update.get('updated', '未知')}\n")
                f.write(f"数据库中未找到记录: {status_update.get('not_found', '未知')}\n")
                not_found_numbers = status_update.get('not_found_numbers', [])
                if not_found_numbers:
                    f.write("未找到的统一编号列表:\n")
                    for number in not_found_numbers:
                        f.write(f"  - {number}\n")
            else:
                f.write("无状态更新信息（可能未执行或无相关数据）\n")
        
        logger.info(f"处理结果摘要已保存至: {summary_path}")
        return summary_path
    except Exception as e:
        logger.error(f"创建处理结果摘要文件失败: {e}", exc_info=True)
        return None

# Example usage (conceptual):
# result: ProcessingResultDto = pdf_service.process_pdf_for_splitting_info(...)
# archived: List[Tuple[Optional[str], Optional[str]]] = orchestrator.split_and_archive(...)
# summary_file = create_result_summary(input_path, result, archived, output_dir) 