#!/usr/bin/env python3
"""
图像处理逻辑验证脚本
验证方案1的图像处理与原方案的一致性
"""
import os
import sys
import time
import hashlib
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archive_flow_manager.settings')

import django
django.setup()

from archive_processing.utils import image_utils


def create_test_image(text: str, size: tuple = (600, 150)) -> Image.Image:
    """创建测试图像"""
    img = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 28)
    except:
        font = ImageFont.load_default()
    
    # 计算文本位置 (居中)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    draw.text((x, y), text, fill='black', font=font)
    return img


def calculate_image_hash(image: Image.Image) -> str:
    """计算图像的哈希值"""
    img_bytes = image.tobytes()
    return hashlib.md5(img_bytes).hexdigest()[:16]


def analyze_image_properties(image: Image.Image) -> dict:
    """分析图像属性"""
    img_array = np.array(image)
    
    return {
        'size': image.size,
        'mode': image.mode,
        'brightness': float(np.mean(img_array)),
        'contrast': float(np.std(img_array)),
        'hash': calculate_image_hash(image)
    }


def compare_image_processing():
    """对比原方案和微服务的图像处理"""
    print("🔍 图像处理逻辑验证")
    print("=" * 60)
    
    # 创建测试图像
    test_cases = [
        ("标准文本", "Hello World 你好世界"),
        ("低对比度", "Light Gray Text"),
        ("复杂文本", "Contract No. 2025-001 合同编号"),
        ("数字编号", "统一编号: 12345678901234567890")
    ]
    
    for case_name, text in test_cases:
        print(f"\n📝 测试用例: {case_name}")
        print("-" * 40)
        
        # 创建测试图像
        original_image = create_test_image(text)
        print(f"原始图像: {original_image.size}, {original_image.mode}")
        
        # 使用原方案处理
        print("🔄 使用原方案处理...")
        start_time = time.time()
        original_enhanced = image_utils.get_enhanced_images_for_paddle(original_image)
        original_time = time.time() - start_time
        
        print(f"   生成图像数量: {len(original_enhanced)}")
        print(f"   处理时间: {original_time:.3f}s")
        
        # 分析每个变体
        for i, img in enumerate(original_enhanced):
            props = analyze_image_properties(img)
            print(f"   变体{i+1}: 亮度={props['brightness']:.1f}, 对比度={props['contrast']:.1f}, 哈希={props['hash']}")
        
        # 模拟微服务处理 (导入微服务模块)
        try:
            sys.path.insert(0, 'ocr_service')
            from image_processor import ImageProcessor
            
            print("🔄 使用微服务方案处理...")
            start_time = time.time()
            microservice_enhanced = ImageProcessor.get_enhanced_images(original_image, 4)
            microservice_time = time.time() - start_time
            
            print(f"   生成图像数量: {len(microservice_enhanced)}")
            print(f"   处理时间: {microservice_time:.3f}s")
            
            # 分析每个变体
            for i, img in enumerate(microservice_enhanced):
                props = analyze_image_properties(img)
                print(f"   变体{i+1}: 亮度={props['brightness']:.1f}, 对比度={props['contrast']:.1f}, 哈希={props['hash']}")
            
            # 对比分析
            print("📊 对比分析:")
            if len(original_enhanced) == len(microservice_enhanced):
                print(f"   ✅ 图像数量一致: {len(original_enhanced)}")
            else:
                print(f"   ❌ 图像数量不一致: 原方案{len(original_enhanced)} vs 微服务{len(microservice_enhanced)}")
            
            # 性能对比
            if microservice_time < original_time:
                improvement = (original_time - microservice_time) / original_time * 100
                print(f"   🚀 微服务处理更快: +{improvement:.1f}%")
            else:
                degradation = (microservice_time - original_time) / original_time * 100
                print(f"   ⚠️ 微服务处理较慢: -{degradation:.1f}%")
            
            # 质量对比 (基于亮度和对比度的相似性)
            quality_match = True
            for i in range(min(len(original_enhanced), len(microservice_enhanced))):
                orig_props = analyze_image_properties(original_enhanced[i])
                micro_props = analyze_image_properties(microservice_enhanced[i])
                
                brightness_diff = abs(orig_props['brightness'] - micro_props['brightness'])
                contrast_diff = abs(orig_props['contrast'] - micro_props['contrast'])
                
                if brightness_diff > 10 or contrast_diff > 5:
                    quality_match = False
                    print(f"   ⚠️ 变体{i+1}质量差异较大: 亮度差{brightness_diff:.1f}, 对比度差{contrast_diff:.1f}")
            
            if quality_match:
                print("   ✅ 图像质量基本一致")
            
        except ImportError as e:
            print(f"   ❌ 无法导入微服务模块: {e}")
        except Exception as e:
            print(f"   ❌ 微服务处理失败: {e}")


def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 边缘情况测试")
    print("=" * 60)
    
    edge_cases = [
        ("极小图像", (50, 20)),
        ("极大图像", (2000, 800)),
        ("正方形图像", (400, 400)),
        ("窄长图像", (1000, 100))
    ]
    
    for case_name, size in edge_cases:
        print(f"\n📝 {case_name}: {size}")
        
        try:
            # 创建特殊尺寸的测试图像
            test_image = create_test_image("Test", size)
            
            # 测试原方案
            original_enhanced = image_utils.get_enhanced_images_for_paddle(test_image)
            print(f"   原方案: 生成{len(original_enhanced)}张图像")
            
            # 测试微服务方案
            try:
                sys.path.insert(0, 'ocr_service')
                from image_processor import ImageProcessor
                
                microservice_enhanced = ImageProcessor.get_enhanced_images(test_image, 4)
                print(f"   微服务: 生成{len(microservice_enhanced)}张图像")
                
                if len(original_enhanced) == len(microservice_enhanced):
                    print("   ✅ 边缘情况处理一致")
                else:
                    print("   ⚠️ 边缘情况处理不一致")
                    
            except Exception as e:
                print(f"   ❌ 微服务边缘情况失败: {e}")
                
        except Exception as e:
            print(f"   ❌ 边缘情况测试失败: {e}")


def main():
    """主函数"""
    print("🔬 图像处理逻辑验证工具")
    print("验证方案1微服务与原方案的图像处理一致性")
    print("=" * 60)
    
    try:
        # 主要对比测试
        compare_image_processing()
        
        # 边缘情况测试
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("📋 验证总结:")
        print("1. 检查图像数量是否一致 (应该都是4张)")
        print("2. 检查处理时间性能")
        print("3. 检查图像质量相似性")
        print("4. 检查边缘情况处理")
        print("\n✅ 如果所有测试通过，说明方案1的图像处理逻辑与原方案一致")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 验证过程出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
