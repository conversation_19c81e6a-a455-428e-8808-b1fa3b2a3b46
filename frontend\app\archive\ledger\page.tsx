"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { TablePageLayout } from "@/components/common/table-page-layout"
import { PageAction } from "@/components/common/page-header"
import { StatusCardGroup, StatusCardData } from "@/components/common/status-card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { FileText, CheckCircle, AlertTriangle, Clock, LayoutDashboard } from "lucide-react"
import ProcessingTaskGrid from "@/components/domain/archive/ledger/ProcessingTaskGrid"
import { getProcessingStats, ProcessingStats } from "@/services/domain/archive/ledger/processing-task-service"
import { useToast } from "@/components/ui/use-toast"

export default function ArchiveLedgerPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [showCards, setShowCards] = useState(true)
  const [stats, setStats] = useState<ProcessingStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 5000; // 5 seconds

  const fetchStats = useCallback(async (isSilent: boolean = false) => {
    if (!isSilent) {
      setIsLoading(true);
    }
    try {
      const data = await getProcessingStats();
      setStats(data);
    } catch (error) {
      console.error("Failed to fetch processing stats:", error);
      toast({
        title: "错误",
        description: "无法加载统计数据，请稍后重试。",
        variant: "destructive",
      });
      setStats({ pending: 0, processing: 0, completed: 0, failed: 0, total: 0 });
    } finally {
      if (!isSilent) {
        setIsLoading(false);
      }
    }
  }, [toast]);

  useEffect(() => {
    // 立即执行一次
    fetchStats();

    const startPolling = () => {
      // 停止任何已存在的轮询
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
      // 开始新的轮询
      pollingRef.current = setInterval(() => {
        fetchStats(true); // 静默刷新
      }, POLLING_INTERVAL);
    };

    const stopPolling = () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
      }
    };
    
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else {
        fetchStats(true); // 切换回标签页时立即刷新一次
        startPolling();
      }
    };

    startPolling();
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // 组件卸载时清理
    return () => {
      stopPolling();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [fetchStats]);

  const statusCards: StatusCardData[] = stats ? [
    { title: "待处理", value: stats.pending.toLocaleString(), icon: <Clock className="w-4 h-4 text-muted-foreground" />, color: "blue" },
    { title: "处理中", value: stats.processing.toLocaleString(), icon: <FileText className="w-4 h-4 text-muted-foreground" />, color: "orange" },
    { title: "已完成", value: stats.completed.toLocaleString(), icon: <CheckCircle className="w-4 h-4 text-muted-foreground" />, color: "green" },
    { title: "失败", value: stats.failed.toLocaleString(), icon: <AlertTriangle className="w-4 h-4 text-muted-foreground" />, color: "red" },
  ] : [];

  const pageActions: PageAction[] = [
    {
      label: showCards ? "隐藏统计" : "显示统计",
      icon: <LayoutDashboard className="h-4 w-4" />,
      onClick: () => setShowCards(!showCards),
      variant: "outline",
    },
  ]
  
  const statusCardsContent = (
    <AnimatePresence>
      {showCards && (
        <motion.div
          initial={{ opacity: 0, height: 0, y: -20 }}
          animate={{ opacity: 1, height: "auto", y: 0 }}
          exit={{ opacity: 0, height: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="overflow-hidden"
        >
          <div className="pt-3 pb-1.5">
            <StatusCardGroup cards={statusCards} />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  const tabsComponent = (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="all">全部</TabsTrigger>
        <TabsTrigger value="pending">待处理</TabsTrigger>
        <TabsTrigger value="processing">处理中</TabsTrigger>
        <TabsTrigger value="completed">已完成</TabsTrigger>
        <TabsTrigger value="failed">失败</TabsTrigger>
      </TabsList>
    </Tabs>
  );

  return (
    <TablePageLayout
      title="PDF导入台账"
      subtitle="查看和管理通过PDF上传的档案处理任务。"
      actions={pageActions}
      statusCards={statusCardsContent}
      fixedTabs={tabsComponent}
    >
      <ProcessingTaskGrid status={activeTab as any} />
    </TablePageLayout>
  )
}
