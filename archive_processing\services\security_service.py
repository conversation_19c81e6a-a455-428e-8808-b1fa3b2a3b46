import logging
import time
import base64
import hmac
import hashlib
import json
from typing import Optional, Dict, Any, Tuple
from django.conf import settings

logger = logging.getLogger(__name__)

def create_secure_access_token(file_path: str, user_id: int, expires_in: int = 86400) -> str:
    """
    创建安全访问令牌，用于控制文件访问权限
    
    Args:
        file_path: 文件路径
        user_id: 用户ID
        expires_in: 有效期(秒)，默认24小时
        
    Returns:
        安全访问令牌
    """
    # 获取当前时间和过期时间
    current_time = int(time.time())
    expiry_time = current_time + expires_in
    
    # 创建令牌数据
    token_data = {
        'path': file_path,
        'user': user_id,
        'created': current_time,
        'expires': expiry_time
    }
    
    # 序列化令牌数据
    token_json = json.dumps(token_data)
    token_bytes = token_json.encode('utf-8')
    
    # 获取密钥
    secret_key = getattr(settings, 'SECRET_KEY', 'default_secret_key')
    
    # 创建签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        token_bytes,
        hashlib.sha256
    ).digest()
    
    # 组合令牌和签名
    token = base64.urlsafe_b64encode(token_bytes).decode('utf-8')
    signature_b64 = base64.urlsafe_b64encode(signature).decode('utf-8')
    
    # 返回完整令牌
    return f"{token}.{signature_b64}"


def verify_access_token(token: str, file_path: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
    """
    验证访问令牌的有效性
    
    Args:
        token: 访问令牌
        file_path: 文件路径（可选，用于额外验证）
        
    Returns:
        Tuple[bool, Dict]: (是否有效, 令牌数据)
    """
    try:
        # 分离令牌和签名
        token_part, signature_part = token.split('.')
        
        # 解码令牌数据
        token_bytes = base64.urlsafe_b64decode(token_part)
        token_data = json.loads(token_bytes.decode('utf-8'))
        
        # 获取密钥
        secret_key = getattr(settings, 'SECRET_KEY', 'default_secret_key')
        
        # 验证签名
        expected_signature = hmac.new(
            secret_key.encode('utf-8'),
            token_bytes,
            hashlib.sha256
        ).digest()
        
        actual_signature = base64.urlsafe_b64decode(signature_part)
        if not hmac.compare_digest(expected_signature, actual_signature):
            logger.warning("令牌签名验证失败")
            return False, {}
        
        # 检查令牌是否过期
        current_time = int(time.time())
        if token_data.get('expires', 0) < current_time:
            logger.warning("令牌已过期")
            return False, token_data
        
        # 如果提供了文件路径，验证是否匹配
        if file_path and token_data.get('path') != file_path:
            logger.warning(f"令牌文件路径不匹配: {token_data.get('path')} != {file_path}")
            return False, token_data
        
        return True, token_data
        
    except Exception as e:
        logger.error(f"验证访问令牌失败: {e}")
        return False, {}


def check_file_access_permission(file_path: str, user_id: int) -> bool:
    """
    检查用户是否有权限访问文件
    
    Args:
        file_path: 文件路径
        user_id: 用户ID
        
    Returns:
        bool: 是否有权限
    """
    try:
        # 此处可以实现更复杂的权限检查逻辑
        # 例如：检查用户角色、文件所有者、部门权限等
        
        # 简单示例：admin用户有所有权限
        from django.contrib.auth.models import User
        user = User.objects.get(id=user_id)
        
        if user.is_superuser or user.is_staff:
            return True
            
        # 检查文件所有者（如果有此类数据模型）
        # 检查文件部门权限
        # 检查文件分享权限
        
        # 默认实现：先返回True，应根据实际需求完善
        logger.warning("文件权限检查功能未完全实现，默认允许访问")
        return True
        
    except Exception as e:
        logger.error(f"检查文件访问权限失败: {e}")
        return False


def generate_download_url(file_path: str, user_id: int, expires_in: int = 3600) -> str:
    """
    生成带有访问令牌的下载URL
    
    Args:
        file_path: 文件路径
        user_id: 用户ID
        expires_in: 有效期(秒)，默认1小时
        
    Returns:
        下载URL
    """
    try:
        # 生成访问令牌
        token = create_secure_access_token(file_path, user_id, expires_in)
        
        # 构建下载URL（基于项目URL配置）
        base_url = getattr(settings, 'DOWNLOAD_URL', '/download/')
        
        # 编码文件路径和令牌
        encoded_path = base64.urlsafe_b64encode(file_path.encode('utf-8')).decode('utf-8')
        
        # 构建最终URL
        url = f"{base_url}?file={encoded_path}&token={token}"
        
        return url
        
    except Exception as e:
        logger.error(f"生成下载URL失败: {e}")
        return "" 