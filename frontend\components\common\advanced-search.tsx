"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Search, SlidersHorizontal, X } from "lucide-react"
import { useState } from "react"
import { DateRangePicker } from "../ui/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"

interface FilterField {
  id: string
  label: string
  type: "text" | "select" | "date" | "dateRange" | "number"
  options?: { value: string; label: string }[]
  placeholder?: string
}

interface AdvancedSearchProps {
  fields: FilterField[]
  onSearch: (filters: Record<string, any>) => void
  className?: string
}

export function AdvancedSearch({ fields, onSearch, className }: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [filters, setFilters] = useState<Record<string, any>>({})

  const handleFilterChange = (fieldId: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [fieldId]: value,
    }))
  }

  const handleClearFilters = () => {
    setFilters({})
  }

  const handleSearch = () => {
    onSearch(filters)
    setIsOpen(false)
  }

  const activeFilterCount = Object.keys(filters).filter((key) => {
    const value = filters[key]
    return value !== undefined && value !== "" && value !== null
  }).length

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className={className}>
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          高级搜索
          {activeFilterCount > 0 && (
            <span className="ml-2 rounded-full bg-primary text-primary-foreground w-5 h-5 text-xs flex items-center justify-center">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-4" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">高级搜索</h4>
            <Button variant="ghost" size="sm" onClick={handleClearFilters} className="h-8 px-2 text-muted-foreground">
              <X className="mr-2 h-4 w-4" />
              清除全部
            </Button>
          </div>
          <div className="space-y-4">
            {fields.map((field) => (
              <div key={field.id} className="space-y-2">
                <Label htmlFor={field.id}>{field.label}</Label>
                {field.type === "text" && (
                  <Input
                    id={field.id}
                    value={filters[field.id] || ""}
                    onChange={(e) => handleFilterChange(field.id, e.target.value)}
                    placeholder={field.placeholder}
                  />
                )}
                {field.type === "select" && (
                  <Select
                    value={filters[field.id] || ""}
                    onValueChange={(value) => handleFilterChange(field.id, value)}
                  >
                    <SelectTrigger id={field.id}>
                      <SelectValue placeholder={field.placeholder || "选择选项"} />
                    </SelectTrigger>
                    <SelectContent>
                      {field.options?.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                {field.type === "dateRange" && (
                  <DateRangePicker onChange={(range) => handleFilterChange(field.id, range)} />
                )}
                {field.type === "number" && (
                  <Input
                    id={field.id}
                    type="number"
                    value={filters[field.id] || ""}
                    onChange={(e) => handleFilterChange(field.id, e.target.value)}
                    placeholder={field.placeholder}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              搜索
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
