"use client"

import React, { ReactNode } from "react"
import { PageTitle } from "@/components/page-title"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// 定义共享的接口
export interface PageAction {
  label: string
  icon?: React.ReactNode
  onClick?: () => void
  href?: string
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive"
  disabled?: boolean
}

interface PageHeaderProps {
  /** 页面标题 */
  title: string
  /** 页面副标题/描述 */
  subtitle?: string
  /** 操作按钮数组 */
  actions?: PageAction[]
  /** 自定义类名 */
  className?: string
  /** 是否显示头部底部边框 */
  showHeaderBorder?: boolean
  /** 标题下方的额外内容 */
  statusCards?: ReactNode
  /** 固定标签栏内容 */
  fixedTabs?: ReactNode
}

/**
 * 共享的页面头部组件
 * 
 * 提供统一的页面标题、副标题和操作按钮渲染
 * 被PageLayout和TablePageLayout共同使用以确保UI一致性
 */
export function PageHeader({
  title,
  subtitle,
  actions,
  className,
  showHeaderBorder = true,
  statusCards,
  fixedTabs,
}: PageHeaderProps) {
  return (
    <div
      className={cn(
        "flex-none bg-background",
        !fixedTabs && "mb-3",
        className
      )}
    >
      {/* Section 1: Title, Subtitle, Actions. This section gets the border. */}
      <div className={cn("py-1.5", showHeaderBorder && "border-b")}>
        <div className="flex justify-between items-center">
          <PageTitle title={title} subtitle={subtitle} />
          {actions && actions.length > 0 && (
            <div className="flex gap-2">
              <TooltipProvider>
                {actions.map((action, index) => (
                  <Tooltip key={index}>
                    <TooltipTrigger asChild>
                      <Button
                        variant={action.variant || "default"}
                        size="sm"
                        onClick={action.onClick}
                        className="flex items-center gap-2"
                        disabled={action.disabled}
                      >
                        {action.icon}
                        <span className="hidden sm:inline">{action.label}</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{action.label}</p>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </TooltipProvider>
            </div>
          )}
        </div>
      </div>

      {/* Section 2: Status Cards. No border here. */}
      {statusCards}

      {/* Section 3: Fixed Tabs. No border here. */}
      {fixedTabs && (
        <div className="flex-none bg-background z-10 pt-1.5 mb-1">
          {fixedTabs}
        </div>
      )}
    </div>
  )
} 