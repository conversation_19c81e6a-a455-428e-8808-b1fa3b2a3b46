"""
档案记录序列化器模块

此模块包含与档案记录相关的序列化器类。
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    ArchiveRecord,
    ImportLog,
    ChangeLogBatch,
    RecordChangeLog,
    FieldChangeLog,
    ChangeOrder,
    ChangeOrderItem,
    ChangeOrderAttachment,
    ImportSession,
    ImportConflictDetail,
    ImportFieldDifference,
)
from .field_mappings import get_field_display_name

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "email"]


class ImportLogSerializer(serializers.ModelSerializer):
    """导入日志序列化器"""

    import_user = UserSerializer(read_only=True)
    created_by = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    import_datetime = serializers.DateTimeField(source='created_at', read_only=True)

    class Meta:
        model = ImportLog
        fields = [
            "id",
            "batch_number",
            "file_name",
            "file_size",
            "file_hash",
            "created_by",
            "created_at",
            "updated_at",
            "completed_at",
            "session_id",
            "import_user",
            "import_datetime",
            "status",
            "status_display",
            "error_log",
            "processing_time",
            "detailed_report",

            # 分析阶段统计
            "analysis_total_rows_read",
            "analysis_failed_rows",
            "analysis_skipped_identical",
            "analysis_found_new_count",
            "analysis_found_update_count",
            "analysis_successfully_parsed_rows",
            
            # 用户决策阶段统计
            "user_decision_skipped_update_count",
            "user_decision_confirmed_update_count",

            # 导入Task (数据库执行) 阶段统计
            "import_task_total_records_submitted",
            "import_task_created_count",
            "import_task_updated_count",
            "import_task_unchanged_count",
            "import_task_processed_successfully_count",
            "import_task_failed_count",

            # Overall - 直接镜像用户要求的核心统计
            "overall_total_initial_records",
            "overall_user_decision_skipped_updates",
            "overall_final_created_count",
            "overall_final_updated_count",
            
            # Overall - 计算型汇总统计
            "overall_skipped_by_system_total",
            "overall_skipped_total",
            "overall_processed_successfully_total",
            "overall_failed_total",
        ]

    def get_status_display(self, obj):
        return obj.get_status_display()


class ArchiveRecordSerializer(serializers.ModelSerializer):
    """
    档案记录序列化器 - 用于台账列表及详情展示

    序列化 ArchiveRecord 模型的所有字段，以满足前端AG Grid显示全部数据的需求。
    """

    # CHANGE: [2025-05-21] 添加 import_user_name 字段
    import_user_name = serializers.SerializerMethodField()

    # 示例：如果特定外键字段需要更详细的表示（例如用户名而不是ID），
    # 可以添加 SerializerMethodField 或嵌套序列化器。
    # 例如，对于 import_user (ForeignKey to User):
    # import_user_details = UserSerializer(source='import_user', read_only=True)
    # 然后需要在下面的 fields 列表中包含 'import_user_details' 而不是 'import_user' (如果只想显示详情)

    class Meta:
        model = ArchiveRecord
        fields = "__all__"  # 序列化模型所有字段

        # CHANGE: [2024-07-26] 清理read_only_fields，只保留ArchiveRecord模型中的字段
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "import_date",
            # "import_user", # 移除，因为我们将通过 import_user_name 提供用户名，ID仍可写（尽管通常不应直接写）
            "batch_number",  # 修正：原为 import_batch
            "source_system", # 新增：ArchiveRecord的字段，通常导入后不修改
            "unified_number",
            "commission_number", 
            "account_from_excel",
        ]

    # CHANGE: [2025-05-21] 添加 get_import_user_name 方法
    def get_import_user_name(self, obj):
        if obj.import_user:
            # 优先尝试获取用户的全名，其次是用户名
            full_name = obj.import_user.get_full_name()
            return full_name if full_name else obj.import_user.username
        return None


class ArchiveRecordListSerializer(serializers.ModelSerializer):
    """档案记录列表序列化器（简化版） - 审视是否仍需或可废弃/统一"""

    # TODO: [P2] 审视此序列化器是否可以被上面修改后的 ArchiveRecordSerializer 替代，
    # 或者是否需要保留并更新其字段以服务于特定列表场景（例如 ArchiveRecordViewSet 的 list action）。
    # 目前 ArchiveRecordListView 使用的是 ArchiveRecordSerializer。
    # 如果 ArchiveRecordViewSet 中的 list action 也需要不同的字段集，则应更新此处的 fields。
    # 原始字段: 'id', 'archive_number', 'title', 'status', 'created_at', 'updated_at'
    # 这些字段（archive_number, title, status）与当前模型不符。
    # 暂时保留结构，但字段可能需要更新或此类可被移除，取决于 ArchiveRecordViewSet 的需求。

    class Meta:
        model = ArchiveRecord
        fields = [  # 维持原样以便不破坏 ArchiveRecordViewSet (如果它用到的话)，但标记为待办
            "id",
            # 'archive_number', # 不在模型中
            # 'title', # 不在模型中
            # 'status', # 不在模型中, 应为 archive_status
            "unified_number",  # 示例替换
            "project_name",  # 示例替换
            "archive_status",
            "created_at",
            "updated_at",
        ]


class ExcelImportSerializer(serializers.Serializer):
    """Excel导入序列化器"""

    file = serializers.FileField(help_text="要导入的Excel文件(.xlsx或.xls格式)")
    sheet_name = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="要导入的工作表名称，留空则导入第一个工作表",
    )
    # CHANGE: [2024-07-26] 出于安全考虑，重复数据处理策略不再由前端控制，改为后端统一配置

    def validate_file(self, value):
        """验证文件是否为有效的Excel文件"""
        if not value.name.endswith((".xlsx", ".xls")):
            raise serializers.ValidationError(
                "上传的文件必须是.xlsx或.xls格式的Excel文件"
            )
        return value

    def validate_sheet_name(self, value):
        """验证工作表名称"""
        if not value:
            return 0  # 默认导入第一个工作表
        return value


class FieldChangeLogSerializer(serializers.ModelSerializer):
    """字段变更日志序列化器"""

    class Meta:
        model = FieldChangeLog
        fields = [
            "field_name",
            "field_label",
            "old_value",
            "new_value",
            "field_importance",
        ]


class RecordChangeLogSerializer(serializers.ModelSerializer):
    """记录变更日志序列化器"""

    field_changes = FieldChangeLogSerializer(many=True, read_only=True)
    change_type_display = serializers.SerializerMethodField()
    batch_info = serializers.SerializerMethodField()
    rollback_info = serializers.SerializerMethodField()

    class Meta:
        model = RecordChangeLog
        fields = [
            "id",
            "version_number",
            "change_type",
            "change_type_display",
            "is_rollback",
            "rollback_source_version",
            "rollback_info",
            "record_before",
            "record_after",
            "changed_fields_count",
            "field_changes",
            "batch_info",
        ]

    def get_change_type_display(self, obj):
        return obj.get_change_type_display()

    def get_batch_info(self, obj):
        batch = obj.batch
        return {
            "id": batch.id,
            "batch_id": str(batch.batch_id),
            "change_source": batch.change_source,
            "change_reason": batch.change_reason,
            "changed_at": batch.changed_at.strftime("%Y-%m-%d %H:%M:%S"),
            "changed_by": batch.changed_by.username if batch.changed_by else None,
        }

    def get_rollback_info(self, obj):
        """获取回滚相关信息"""
        if not obj.is_rollback:
            return None

        try:
            source_version = RecordChangeLog.objects.get(
                record=obj.record, version_number=obj.rollback_source_version
            )
            return {
                "source_version": obj.rollback_source_version,
                "source_version_date": source_version.batch.changed_at,
                "source_version_type": source_version.change_type,
            }
        except RecordChangeLog.DoesNotExist:
            return {
                "source_version": obj.rollback_source_version,
                "error": "源版本不存在",
            }


class ChangeLogBatchSerializer(serializers.ModelSerializer):
    """变更批次序列化器"""

    record_changes = RecordChangeLogSerializer(many=True, read_only=True)

    class Meta:
        model = ChangeLogBatch
        fields = [
            "id",
            "batch_id",
            "change_source",
            "change_reason",
            "changed_at",
            "changed_by",
            "affected_records_count",
            "summary",
            "record_changes",
        ]


class ArchiveRecordBriefSerializer(serializers.ModelSerializer):
    """档案记录简要信息序列化器"""

    class Meta:
        model = ArchiveRecord
        fields = [
            "id",
            "unified_number",
            "commission_number",
            "project_name",
            "client_unit",
            "archive_status",
        ]


class ChangeOrderItemCreateSerializer(serializers.ModelSerializer):
    """更改单条目创建序列化器"""

    class Meta:
        model = ChangeOrderItem
        fields = [
            "id",
            "change_order",
            "archive_record",
            "field_name",
            "old_value",
            "new_value",
            "note",
        ]


class ChangeOrderItemSerializer(serializers.ModelSerializer):
    """更改单条目序列化器"""

    archive_record_detail = serializers.SerializerMethodField()

    class Meta:
        model = ChangeOrderItem
        fields = [
            "id",
            "change_order",
            "archive_record",
            "archive_record_detail",
            "field_name",
            "old_value",
            "new_value",
            "note",
        ]

    def get_archive_record_detail(self, obj):
        """获取档案详细信息"""
        if obj.archive_record:
            return {
                "id": obj.archive_record.id,
                "archive_number": obj.archive_record.archive_number,
                "title": obj.archive_record.title,
            }
        return None


class ChangeOrderCreateSerializer(serializers.ModelSerializer):
    """更改单创建序列化器"""

    class Meta:
        model = ChangeOrder
        fields = ["title", "description", "priority"]


class ChangeOrderSerializer(serializers.ModelSerializer):
    """更改单序列化器"""

    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    approved_by = UserSerializer(read_only=True)
    executed_by = UserSerializer(read_only=True)
    item_count = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    priority_display = serializers.CharField(
        source="get_priority_display", read_only=True
    )

    class Meta:
        model = ChangeOrder
        fields = [
            "id",
            "order_number",
            "title",
            "description",
            "status",
            "status_display",
            "priority",
            "priority_display",
            "created_at",
            "updated_at",
            "approved_at",
            "executed_at",
            "created_by",
            "updated_by",
            "approved_by",
            "executed_by",
            "reject_reason",
            "cancel_reason",
            "item_count",
        ]
        read_only_fields = [
            "order_number",
            "status",
            "created_at",
            "updated_at",
            "approved_at",
            "executed_at",
            "created_by",
            "updated_by",
            "approved_by",
            "executed_by",
            "reject_reason",
            "cancel_reason",
        ]

    def get_item_count(self, obj):
        """获取更改单条目数量"""
        return obj.changeorderitem_set.count()


class ChangeOrderActionSerializer(serializers.Serializer):
    """更改单操作序列化器"""

    action = serializers.ChoiceField(
        choices=["submit", "approve", "reject", "execute", "cancel"], required=True
    )
    reason = serializers.CharField(required=False, allow_blank=True)


class ChangeOrderAttachmentSerializer(serializers.ModelSerializer):
    """更改单附件序列化器"""

    uploaded_by = UserSerializer(read_only=True)
    file_size = serializers.ReadOnlyField()

    class Meta:
        model = ChangeOrderAttachment
        fields = [
            "id",
            "change_order",
            "file",
            "filename",
            "file_type",
            "file_size",
            "upload_time",
            "uploaded_by",
        ]
        read_only_fields = ["uploaded_by", "upload_time", "file_size"]


# CHANGE: [2025-06-19] 新增用于冲突解决流程的序列化器
class ImportFieldDifferenceSerializer(serializers.ModelSerializer):
    """序列化器：用于单个字段的差异，符合DRF-camelcase规范"""
    field_display_name = serializers.SerializerMethodField()

    class Meta:
        model = ImportFieldDifference
        fields = [
            'id',
            'field_name',
            'field_display_name',
            'existing_value',
            'imported_value'
        ]
    
    def get_field_display_name(self, obj: ImportFieldDifference) -> str:
        """从后端映射获取字段的显示名称"""
        return get_field_display_name(obj.field_name)

class ImportConflictDetailSerializer(serializers.ModelSerializer):
    """序列化器：用于单条冲突记录，嵌套了字段差异"""
    field_differences = ImportFieldDifferenceSerializer(many=True, read_only=True)
    conflict_type_display = serializers.CharField(source='get_conflict_type_display', read_only=True)

    class Meta:
        model = ImportConflictDetail
        fields = [
            'id',
            'commission_number',
            'excel_row_number',
            'existing_record_pk',
            'conflict_type',
            'conflict_type_display',
            'user_resolution',
            'field_differences' # 嵌套序列化器
        ]

class ImportSessionAnalysisResultSerializer(serializers.ModelSerializer):
    """序列化器：用于返回完整的冲突分析结果"""
    conflict_details = ImportConflictDetailSerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = ImportSession
        fields = [
            'session_id',
            'status',
            'status_display',
            'file_name',
            'record_count',
            'conflict_count',
            'analysis_stats',
            'created_by',
            'created_at',
            'updated_at',
            'conflict_details',
        ]
        # 在ViewSet中应使用 prefetch_related 和 select_related 进行查询优化
        # prefetch_related: ['conflict_details__field_differences', 'created_by']
