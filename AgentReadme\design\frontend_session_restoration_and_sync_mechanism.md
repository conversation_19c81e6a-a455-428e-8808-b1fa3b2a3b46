# 前端会话状态恢复与同步机制解析

## 1. 引言

本文档旨在详细阐述在Excel导入功能中，前端如何处理会话状态的浏览器级别持久化、页面刷新后的状态恢复尝试，以及如何与从后端获取的权威会话状态进行同步，确保数据一致性并提升用户体验。重点围绕 `ActiveSessionWarningContext`、`sessionStorage` 以及 `useExcelImportSession` Hook 之间的交互。

## 2. 核心组件与职责

### 2.1. `ActiveSessionWarningContext` (`frontend/contexts/ActiveSessionWarningContext.tsx`)

* **主要职责**:
  * 利用浏览器的 `sessionStorage` 来持久化当前用户在单个浏览器标签页/会话中的活动导入会话信息。
  * 在组件加载时，尝试从 `sessionStorage` 恢复这些信息，为用户提供一定程度的状态保持（例如，在页面刷新后）。
  * 提供方法 (`saveSession`, `clearSession`) 供其他组件管理 `sessionStorage` 中的会话数据。
  * 存储并提供会话元数据（如保存时间、版本号、推断的过期时间）。
  * 其状态（如 `activeSessionToProcess`）用于与后端获取的全局活动会话进行比较，以辅助UI提示用户潜在的状态不一致或系统繁忙情况。

### 2.2. `useExcelImportSession` Hook (`frontend/hooks/useExcelImportSession.ts`)

* **主要职责**:
  * 作为与后端Excel导入相关API交互的主要接口。
  * 通过调用后端API（特别是 `getActiveImportSession`）获取系统当前全局唯一的、权威的活动导入会话信息 (`activeSessionInfo`) 和当前用户的权限 (`currentUserPermissions`)。**此从后端获取的数据被视为"唯一真实来源"。**
  * 管理核心业务逻辑状态（如 `activeSessionInfo`, `currentUserPermissions`, `isLoadingSession`, `errorLoadingSession`, `analysisProgress`, `isSubmitting`）。
  * 管理与后端交互的副作用，如心跳机制的启动/停止、分析进度的轮询等。
  * 驱动UI组件（如 `ExcelImportWithConflictResolution.tsx`）根据权威状态进行渲染和行为调整。

### 2.3. `sessionStorage` (浏览器API)

* **作用**:
  * 提供一个**浏览器标签页/窗口级别**的键值对存储。数据在页面刷新或恢复同一会话时保持存在，但在关闭标签页或浏览器后清除。
  * **非跨用户、非跨设备共享存储**。每个用户的每个浏览器会话都有其独立的 `sessionStorage`。
  * 在此机制中，被 `ActiveSessionWarningContext` 用来存储单个用户"上一次已知"的导入会话信息。

## 3. 交互流程详解

以下流程主要发生在 `ExcelImportWithConflictResolution.tsx` 组件中，它同时使用了 `useExcelImportSession` Hook 和 `useActiveSessionWarning` Context。

1. **组件初始化/挂载**:
    * `ActiveSessionWarningContext` 首先尝试从该浏览器标签页的 `sessionStorage` 中恢复 `activeSessionToProcess`（上次保存的会话信息）和相关元数据。
    * 几乎同时，`useExcelImportSession` Hook 会在其 `useEffect` (通常在首次挂载时执行) 中触发 `fetchSystemActiveSession()` 方法。

2. **权威状态获取 (`fetchSystemActiveSession`)**:
    * `fetchSystemActiveSession()` **总是会调用后端API** (`/api/archive-records/active-import-session/`) 来获取系统当前全局唯一的、权威的活动导入会话信息 (`apiSessionData`) 及用户权限。
    * **不存在"如果后端状态未变则使用sessionStorage数据"的逻辑**。API调用是强制性的，以确保获取的是最新、最准确的全局状态，这对于多用户协同场景至关重要。

3. **状态同步与UI更新**:
    * **Hook内部更新**: `useExcelImportSession` Hook在从API获取到 `apiSessionData` 后，会更新其内部状态 `activeSessionInfo` 和 `currentUserPermissions`。这些状态是驱动UI显示和业务逻辑的主要依据。
    * **Context (sessionStorage) 更新**: `ExcelImportWithConflictResolution.tsx` 组件中有一个 `useEffect` 会监听 `activeSessionInfo` (来自Hook，即后端权威数据) 的变化。
        * 当 `activeSessionInfo` 更新时，此 `useEffect` 会调用 `setActiveSessionToProcess(activeSessionInfo)` (这是 `ActiveSessionWarningContext` 提供的方法)。这会将从后端获取的最新会话信息写入到 `ActiveSessionWarningContext` 的状态中，进而通过Context的另一个 `useEffect` **持久化到 `sessionStorage`**。
        * 这意味着 **`sessionStorage` 中的数据总是被来自后端的权威数据所覆盖或更新**。
    * **UI驱动**: 组件的渲染逻辑（如 `derivedCurrentStep`）主要依赖于 `useExcelImportSession` Hook 提供的 `activeSessionInfo`。

4. **处理会话结束或无会话**:
    * 当 `useExcelImportSession` Hook确认会话已结束（例如，`activeSessionInfo.status` 变为 `IMPORT_COMPLETE`, `CANCELLED`, `ERROR`）或者系统当前没有活动会话 (`activeSessionInfo` 为 `null`) 时，`ExcelImportWithConflictResolution.tsx` 中的 `useEffect` 会调用 `clearSession()` (来自 `ActiveSessionWarningContext`) 来清除 `sessionStorage` 中的相关条目。

5. **利用 `sessionStorage` 进行辅助提示**:
    * 在文件选择步骤 (`derivedCurrentStep === 'select'`)，UI会比较 `activeSessionToProcess` (来自Context/`sessionStorage`) 和 `activeSessionInfo` (来自Hook/后端)。
    * 如果 `sessionStorage` 中记录了一个会话 (`activeSessionToProcess` 非空)，但 `activeSessionInfo` 为空（表示后端当前没有全局活动会话），或者两者ID不一致，UI可以向用户显示提示，如"系统当前有活跃导入...您在其他地方可能有一个未完成的导入..."，帮助用户理解上下文。
    * 同样，如果 `sessionStorage` 中有一个会话ID，而Hook初始加载时 `activeSessionInfo` 尚未获取，组件中的逻辑会促使 `fetchSystemActiveSession` 被调用，这可以理解为"用本地存储的会话ID去服务器核实一下最新情况"。

## 4. 机制优势与局限性

### 4.1. 优势

* **提升单个用户体验**:
  * **页面刷新状态保持**: 用户在导入过程中意外刷新页面，`sessionStorage` 中的信息可以帮助快速恢复到之前的会话上下文（通过后续API验证其有效性），避免从头开始。
  * **上下文提示**: 帮助用户感知其在当前浏览器会话中可能存在的、未完成或与其他地方不一致的导入状态。
* **辅助状态同步**: 在组件加载初期，如果 `sessionStorage` 中有会话信息，可以指导 `useExcelImportSession` 更快地去查询特定会话的状态。

### 4.2. 局限性

* **非多用户实时同步方案**: `sessionStorage` 是浏览器和标签页隔离的，**不能用于实现多个不同用户之间的实时状态同步**。多用户间的状态感知完全依赖于各自客户端的 `useExcelImportSession` Hook 定期或按需调用后端API获取最新全局状态。
* **数据非权威**: `sessionStorage` 中的数据仅为辅助和恢复尝试，**永远不能取代从后端API获取的权威状态**。UI的最终决策必须基于后者。

## 5. 设计原则总结

* **后端数据为唯一真实来源 (Single Source of Truth)**: 所有关于当前全局活动导入会话的权威信息均来自后端API。
* **`sessionStorage` 为辅助**: 其目的是提升单个用户的操作连贯性（防刷新丢失）和提供上下文提示。
* **后端数据覆盖前端存储**: 从后端获取的最新权威状态会用于更新或清除 `sessionStorage` 中的内容，确保其与全局状态尽可能保持一致（在当前用户会话范围内）。

## 6. 相关代码文件

* `frontend/contexts/ActiveSessionWarningContext.tsx`
* `frontend/hooks/useExcelImportSession.ts`
* `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` (主要消费者和协调者)
