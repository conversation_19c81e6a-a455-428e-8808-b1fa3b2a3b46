"""
档案记录视图包

此包包含所有档案记录相关的Django REST Framework ViewSet:
- views.py: 核心资源管理ViewSet (档案记录、导入日志、变更日志、更改单等)
- excel_import_viewset.py: Excel导入会话管理ViewSet

CHANGE: [2025-06-04] 重构为DRF ViewSet架构
"""

# 从 views.py 导入核心资源管理ViewSet
from .views import (
    ArchiveRecordViewSet,
    ImportLogViewSet,
    ChangeLogViewSet,
    ChangeOrderViewSet,
    ChangeOrderItemViewSet,
    ChangeOrderAttachmentViewSet,
)

# 从 excel_import_viewset.py 导入Excel导入管理ViewSet
from .excel_import_viewset import ExcelImportSessionViewSet

# 包级别导出的所有ViewSet
__all__ = [
    # 核心资源管理
    'ArchiveRecordViewSet',
    'ImportLogViewSet', 
    'ChangeLogViewSet',
    'ChangeOrderViewSet',
    'ChangeOrderItemViewSet',
    'ChangeOrderAttachmentViewSet',
    
    # Excel导入管理
    'ExcelImportSessionViewSet',
] 