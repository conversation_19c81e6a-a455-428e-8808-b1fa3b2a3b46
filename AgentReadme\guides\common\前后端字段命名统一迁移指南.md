# 🐪 前后端字段命名统一迁移指南

## 📋 概述

本文档指导如何将项目从 **snake_case（下划线命名）** 迁移到 **camelCase（驼峰命名）** 的统一命名规范。这是一个**破坏性重构**，旨在实现前后端数据格式的完全统一。

## 🎯 迁移目标

- **后端API**: 自动将数据库的 `snake_case` 字段转换为 `camelCase` 返回给前端
- **前端接口**: 所有TypeScript接口定义使用 `camelCase` 命名
- **前端组件**: 所有组件中的字段引用使用 `camelCase` 命名
- **数据映射**: 统一数据处理逻辑，消除手动转换

## 🔧 后端迁移步骤

### 1. 安装依赖包

```bash
pip install djangorestframework-camel-case>=1.4.2
```

更新 `requirements.txt`:

```txt
djangorestframework-camel-case>=1.4.2
```

### 2. 配置Django设置

在 `settings.py` 中添加配置：

```python
# settings.py

INSTALLED_APPS = [
    # ... 其他应用
    'djangorestframework_camel_case',
    # ... 其他应用
]

REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': [
        'djangorestframework_camel_case.render.CamelCaseJSONRenderer',
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'djangorestframework_camel_case.parser.CamelCaseJSONParser',
        'rest_framework.parsers.JSONParser',
    ],
    # ... 其他配置保持不变
}
```

### 3. 验证后端转换

创建测试脚本验证API返回格式：

```python
# test_api_format.py
import requests

def test_camel_case_conversion():
    # 获取认证token
    response = requests.post("http://localhost:8000/api/auth/login/", json={
        "username": "your_username", 
        "password": "your_password"
    })
    token = response.json().get("access")
    
    # 测试API响应格式
    headers = {"Authorization": f"Bearer {token}"}
    api_response = requests.get("http://localhost:8000/api/your-endpoint/", headers=headers)
    data = api_response.json()
    
    # 检查字段命名格式
    if "results" in data and len(data["results"]) > 0:
        first_item = data["results"][0]
        
        camel_fields = [k for k in first_item.keys() if any(c.isupper() for c in k[1:])]
        snake_fields = [k for k in first_item.keys() if "_" in k]
        
        print(f"camelCase字段: {camel_fields}")
        print(f"snake_case字段: {snake_fields}")
        
        # 期望: camel_fields有内容，snake_fields为空
```

## 🎨 前端迁移步骤

### 1. 更新TypeScript接口定义

**迁移前:**

```typescript
export interface UserData {
  user_id: number;
  user_name: string;
  created_at: string;
  updated_at: string;
  phone_number?: string;
}
```

**迁移后:**

```typescript
export interface UserData {
  userId: number;           // user_id -> userId
  userName: string;         // user_name -> userName  
  createdAt: string;        // created_at -> createdAt
  updatedAt: string;        // updated_at -> updatedAt
  phoneNumber?: string;     // phone_number -> phoneNumber
}
```

### 2. 更新API参数接口

**迁移前:**

```typescript
export interface QueryParams {
  page_size?: number;
  created_at_start?: string;
  created_at_end?: string;
  user_name?: string;
}
```

**迁移后:**

```typescript
export interface QueryParams {
  pageSize?: number;        // page_size -> pageSize
  createdAtStart?: string;  // created_at_start -> createdAtStart
  createdAtEnd?: string;    // created_at_end -> createdAtEnd
  userName?: string;        // user_name -> userName
}
```

### 3. 更新组件中的字段引用

**迁移前:**

```typescript
// 组件中的字段引用
<Input 
  name="user_name"
  value={userData.user_name || ""}
  onChange={handleInputChange}
/>

// 表格列定义
const columns = [
  { headerName: '用户名', field: 'user_name' },
  { headerName: '创建时间', field: 'created_at' },
  { headerName: '电话', field: 'phone_number' }
];
```

**迁移后:**

```typescript
// 组件中的字段引用  
<Input 
  name="userName"           // user_name -> userName
  value={userData.userName || ""}
  onChange={handleInputChange}
/>

// 表格列定义
const columns = [
  { headerName: '用户名', field: 'userName' },      // user_name -> userName
  { headerName: '创建时间', field: 'createdAt' },    // created_at -> createdAt  
  { headerName: '电话', field: 'phoneNumber' }       // phone_number -> phoneNumber
];
```

### 4. 更新数据映射函数

**迁移前:**

```typescript
function mapApiDataToDisplayData(apiData: ApiResponse): DisplayData {
  return {
    id: apiData.unified_number,
    name: apiData.user_name,
    company: apiData.client_unit,
    contact: apiData.client_name,
    date: apiData.created_at,
    totalCopies: apiData.total_copies
  };
}
```

**迁移后:**

```typescript
function mapApiDataToDisplayData(apiData: ApiResponse): DisplayData {
  return {
    id: apiData.unifiedNumber,      // unified_number -> unifiedNumber
    name: apiData.userName,         // user_name -> userName
    company: apiData.clientUnit,    // client_unit -> clientUnit
    contact: apiData.clientName,    // client_name -> clientName
    date: apiData.createdAt,        // created_at -> createdAt
    totalCopies: apiData.totalCopies // total_copies -> totalCopies
  };
}
```

### 5. 更新事件处理函数

**迁移前:**

```typescript
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const { name, value } = e.target;
  
  setUserData(prev => ({
    ...prev,
    [name]: value  // 直接使用snake_case字段名
  }));
};
```

**迁移后:**

```typescript
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const { name, value } = e.target;
  
  setUserData(prev => ({
    ...prev,
    [name]: value  // 现在使用camelCase字段名
  }));
};
```

## 📝 字段名转换规则

### 常见转换模式

| snake_case | camelCase | 说明 |
|------------|-----------|------|
| `user_id` | `userId` | 基础转换 |
| `created_at` | `createdAt` | 时间字段 |
| `phone_number` | `phoneNumber` | 联系信息 |
| `total_issue_copies` | `totalIssueCopies` | 复合业务字段 |
| `first_receiver_name` | `firstReceiverName` | 序号+业务字段 |
| `commission_datetime` | `commissionDatetime` | 业务时间字段 |

### 转换工具函数

```typescript
// 辅助函数：snake_case转camelCase
function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

// 辅助函数：camelCase转snake_case (如需向后兼容)
function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}
```

## ⚠️ 迁移注意事项

### 1. 一次性完整迁移

- **不要部分迁移**，必须一次性完成整个业务模块的迁移
- **不要保留兼容性代码**，避免维护双套命名系统

### 2. 迁移顺序建议

1. 先配置后端自动转换
2. 验证API返回格式正确
3. 更新前端TypeScript接口
4. 更新前端组件和逻辑
5. 测试完整业务流程

### 3. 测试验证

- 使用API测试工具验证字段格式
- 检查前端页面显示是否正常
- 验证表单提交和数据更新功能
- 确认搜索和筛选功能正常

### 4. 常见问题排查

**问题1: 页面显示空白或错误**  

- 检查组件中的字段引用是否已更新
- 确认TypeScript接口定义是否正确

**问题2: 表单提交失败**  

- 检查表单字段的name属性是否使用camelCase
- 确认事件处理函数中的字段映射

**问题3: 表格列显示异常**  

- 检查表格列定义中的field属性
- 确认数据映射函数的字段转换

## 🎯 迁移检查清单

### 后端检查

- [ ] 安装 `djangorestframework-camel-case` 依赖
- [ ] 配置 REST_FRAMEWORK 设置
- [ ] 验证API返回camelCase格式数据
- [ ] 确认0个snake_case字段残留

### 前端检查  

- [ ] 更新所有TypeScript接口定义
- [ ] 更新组件中的字段引用
- [ ] 更新表格列定义
- [ ] 更新表单字段名称
- [ ] 更新数据映射函数
- [ ] 更新搜索和筛选逻辑

### 功能验证

- [ ] 页面正常显示数据
- [ ] 表单提交功能正常
- [ ] 搜索筛选功能正常
- [ ] 数据更新功能正常
- [ ] 分页和排序功能正常

## 🚀 迁移收益

完成迁移后，您将获得：

- **统一的命名规范** - 前后端数据格式完全一致
- **更好的开发体验** - 符合前端JavaScript/TypeScript惯例
- **减少维护成本** - 无需维护双套命名系统
- **提高代码质量** - 类型安全，减少字段名错误
- **现代化架构** - 符合当前主流开发规范

---

**💡 提示**: 建议在开发环境中先完成一个完整业务模块的迁移，验证流程无误后再推广到其他模块。
