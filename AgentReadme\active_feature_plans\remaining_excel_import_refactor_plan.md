# Excel导入模块重构 - 剩余工作计划

## 引言

本文档整合了Excel导入模块重构项目中尚未完成的各项工作，包括前端实现、后端功能完善、权限增强以及全面的测试阶段。旨在提供一个清晰的、集中的剩余任务列表。

有关已经完成的工作，请参阅：[Excel导入模块重构 - 已完成工作概要](./completed_excel_import_refactor_plan.md)

## 一、 前端实现与完善 (核心Excel导入流程)

1. **冲突解决UI组件细化与功能完整性 (P0)** (CHANGE: [2025-05-16] 核心骨架和主要交互逻辑已存在，待详细测试和细化)
    * **`ConflictResolutionModal.tsx`**:
        * [~] 确保AG Grid (`ConflictResolutionGrid`) 的列定义、自定义单元格渲染器 (`DetailCellRenderer`, `actionCellRenderer`) 的视觉效果和交互逻辑（如下拉框选择、数值输入等）完全符合设计。 (核心渲染器已实现，视觉和特定交互待细化验证 -> 代码逻辑和核心交互已审查通过，视觉效果待用户确认)
        * [~] 确保模态框内的批量操作（例如："全部更新"、"全部跳过"）功能正确实现并能更新所有可见/符合条件的冲突项。 (基本批量操作已实现，按钮提示文本已修正以符合当前逻辑范围，但具体作用范围的精确性[可见vs所有]仍可按需调整 -> Modal组件内逻辑已审查，其行为是明确的：当视图为'所有冲突'或'可更新'时，批量操作意图应用于所有类型为'update'的冲突项。最终范围精确性依赖父组件回调实现。)
        * [x] 确保冲突类型筛选器 (`currentFilter`, `onFilterChange`) 按预期工作，并能正确更新AG Grid显示的数据。 (已实现)
    * **`ConflictResolutionGrid.tsx`**:
        * [x] 确保AG Grid对不同类型的冲突（新增、更新、一致）有清晰的视觉区分和交互提示。 (已通过自定义渲染器实现)
        * [x] 确保单个冲突项的操作（通过 `actionCellRenderer`）能够正确回调并更新状态。 (已实现)
    * **整体交互**:
        * [~] 保证在 `ExcelImportWithConflictResolution.tsx` 中，数据流（冲突列表、已解决的冲突、筛选状态）在父组件、模态框、表格之间正确传递和同步。 (回调机制存在，待全面测试验证 -> 已审查，数据流和回调机制设计合理，能够正确传递和同步状态)

2. **会话管理与API优化 (P2)**
    * **API缓存策略**: (CHANGE: [2025-05-16] 已移除 getActiveImportSession 的客户端内存缓存，以确保多用户协同的实时性)
        * [x] 为`getActiveImportSession()`<s>和`getSession()`</s>方法实现内存缓存，减少短时间内的重复请求 (因实时性要求，此缓存已移除)
        * [x] 设计合理的缓存失效策略，确保关键状态变更时及时刷新数据 (随缓存移除，此特定失效策略不再适用)
    * **统一会话管理**:
        * [ ] 考虑创建统一的会话管理钩子`useSessionManagement`，以简化组件中的会话获取、轮询和状态处理逻辑
        * [~] 实现智能集成层如`getOrFindSession()`，根据参数灵活调用不同API (excelImportService.getSession已有初步尝试)
    * **请求优化**:
        * [~] 完善错误处理与重试机制，区分网络错误与会话失效 (CHANGE: [2025-05-16] 已添加fetchWithRetry辅助函数并应用于getActiveImportSession，通用API重试机制初步建立，但未全面应用 -> CHANGE: [2025-05-17] fetchWithRetry 已推广到服务中所有合适的GET请求和sendHeartbeat POST请求。)
        * [x] 增加并发控制，防止同时发出多个相同请求 (已通过AbortController和isSubmitting实现部分控制)
        * [x] 优化会话健康检查逻辑，减少不必要的服务器请求 (已通过按需轮询和心跳实现)
    * **状态转换一致性**:
        * [x] 确保两个API返回结构可以无缝转换，避免组件中处理差异的复杂逻辑 (类型定义存在，基础良好)
        * [x] 维持API职责清晰的同时提高内部实现效率 (Service层和Hook层职责清晰)

3. **全局错误处理与用户提示 (P1/P2)** (CHANGE: [2025-05-16] 核心机制已存在，审查未发现明显重复提示，可考虑逻辑简化)
    * [~] 全面审阅前端所有API调用点 (`excel-import-service.ts` 及其消费者组件)。 (已对主要组件和Hook进行审查 -> 已进一步审查 useExcelImportSession.ts Hook，其错误处理和通过 errorLoadingSession 状态向UI传递错误的机制是健全的。)
    * [x] 确保对各种API错误（网络错误、服务器端错误、业务逻辑错误）都有友好且明确的用户提示（例如使用 `toast`）。 (Hook错误通过useEffect联动toast，组件内特定toast通过条件判断避免重复，整体机制存在)
    * [x] 优化加载状态的显示，确保在长时间操作（如文件上传、分析、确认导入）期间用户能明确感知系统正在工作。 (isLoadingSession 和 isSubmitting 已用于UI控制)

4. **代码清理与优化 (P2)**
    * [x] 移除前端代码中不再需要的 `console.log` 语句。 (CHANGE: [2025-05-17] 已清理 excel-import-service.ts 和 useExcelImportSession.ts 中的调试日志)
    * [~] 检查并优化组件的性能，特别是在处理大量冲突数据时。 (CHANGE: [2025-05-17] 初步优化 ConflictResolutionGrid.tsx: 添加 getRowId, 提取DetailCellRenderer辅助函数, 关闭enableCellExpressions。进一步优化待性能分析。)
    * [x] 确保代码风格一致性，并添加必要的注释。 (CHANGE: [2025-05-17] 已为 useExcelImportSession.ts 和 excel-import-service.ts 添加JSDoc注释)

## 二、 后端功能完善与维护

1. **实现过期导入会话的自动清理 (P2)**
    * **管理命令创建**: (CHANGE: [2025-05-16] 已创建 `cleanup_expired_imports.py` 管理命令)
        * [x] 在 `archive_records/management/commands/` 目录下创建 `cleanupimports.py`。 (实际创建名为 `cleanup_expired_imports.py`)
    * **命令实现**: (CHANGE: [2025-05-16] 已实现基本命令逻辑)
        * [x] 定义继承自 `BaseCommand` 的类，其 `handle` 方法调用 `ImportSessionManager.cleanup_expired_sessions()`。
    * **定时任务配置**: (CHANGE: [2025-05-17] 已添加 Celery Beat 服务及相关配置，*待用户在 Admin 中实际配置调度*)
        * [x] 根据项目技术栈选择并配置定时任务方案（如 Celery Beat, django-cron, 或系统级cron）以定期执行上述管理命令。
    * **日志与通知**: (CHANGE: [2025-05-17] 审查确认 `tasks.py` 和管理命令中的日志记录已较完善，通知机制待具体需求)
        * [~] 为清理任务添加适当的日志记录；对于清理失败或重要事件，考虑添加错误通知机制。 (基本日志已添加，通知机制待定 -> 日志记录已审查，基本满足要求。通知机制依赖具体需求，暂无进一步操作。)

## 三、 权限系统增强 (安全与管理)

1. **细化导入会话操作的权限控制 (P1/P2)**
    * **操作点梳理**: 全面梳理Excel导入流程中所有需要进行权限控制的操作点（如：创建会话、查看任意会话详情、取消他人会话、接管会话、处理冲突详情、最终确认导入等）。
    * **自定义权限定义**: 在 `archive_records/models.py` 或新的 `permissions.py` 文件中，为 `ImportSession` 模型（或相关模型）定义细粒度的Django权限 (e.g., `can_create_import_session`, `can_view_any_import_session`, `can_cancel_any_import_session`, `can_takeover_import_session`)。
    * **Admin配置**: 确保新定义的权限可以通过Django Admin界面方便地分配给用户或用户组。
    * **API视图集成**: 在 `archive_records/views.py` 中对应的API视图类中，更新 `permission_classes` 或在方法级别使用适当的权限检查机制（如DRF的权限类或装饰器），以强制执行这些新定义的权限。
    * **权限测试**: 编写单元测试或集成测试用例，验证新的权限控制逻辑是否按预期工作，覆盖允许和拒绝的场景。

## 四、 全面测试与质量保证 (P0/P1)

1. **后端单元测试和集成测试 (P1)**
    * **`ImportSessionManager` 测试**:
        * 对每个公共方法 (`create_session`, `get_session`, `analyze_session`, `confirm_import`, `extend_session`, `_cleanup_session_resources`, `cleanup_expired_sessions`) 编写全面的测试用例。
        * 覆盖正常业务流程、各种边缘情况和异常场景（例如文件不存在、会话过期、状态不正确、数据库交互错误等）。
        * 验证 `ImportSession`, `SessionOperation`, `ImportConflictDetail`, `ImportLog` 等数据库记录是否按预期被创建、更新或删除。
        * 适当时 Mock 外部依赖。
    * **`ExcelConflictAnalyzer` 测试**:
        * 针对 `analyze_dataframe` 方法，使用不同结构和内容的模拟Excel数据 (Pandas DataFrame) 作为输入进行测试。
        * 验证冲突检测逻辑（新增、更新、一致）的准确性。
        * 验证字段差异比较的正确性。
        * 验证 `ImportConflictDetail` 记录是否正确创建并与 `ImportSession` 关联。
        * 验证在分析过程中 `ImportSession` 实例的进度和状态更新是否符合预期。
    * **API视图测试 (`archive_records/views.py`)**:
        * 使用Django REST framework的 `APIClient` 或 `APITestCase` 对所有与导入相关的API端点进行测试。
        * 测试请求参数的验证逻辑。
        * 测试身份认证和在权限细化后新的权限控制逻辑。
        * 验证API响应的状态码、数据结构和内容是否符合接口约定。
        * 模拟不同的会话状态和用户操作场景进行测试。

2. **端到端 (E2E) 测试 (P0 - 用户协作)**
    * 在后端API和前端应用都完成上述开发和完善工作后进行。
    * 由用户实际操作前端界面，全面测试整个Excel导入流程，包括：
        * 成功路径：文件上传 -> 分析 -> 冲突解决 (如有) -> 确认导入。
        * 异常路径：上传无效文件、取消操作、会话过期、接管操作等。
        * 多用户协作场景（如果前端支持，例如一个用户上传，另一个用户接管处理）。
    * 重点观察系统行为的正确性、数据在前后端及数据库中的一致性。
    * 验证所有业务逻辑是否完全符合更新后的需求文档 (`excel_import_session_management.md`)。
    * 细致收集并记录测试过程中发现的任何问题、缺陷或用户体验可改进点。
