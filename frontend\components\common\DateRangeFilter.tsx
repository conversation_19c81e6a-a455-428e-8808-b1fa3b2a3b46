'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar as CalendarIcon, X } from "lucide-react";
import ClientOnly from "@/components/common/ClientOnly";
import { formatDate, getTodayStr, getDaysAgoStr } from '@/utils/date-utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DateRangeFilterProps {
  initialStartDate?: string;
  initialEndDate?: string;
  onApplyFilter: (startDate: string, endDate: string) => void;
  buttonSize?: 'sm' | 'default' | 'lg' | 'icon' | null | undefined;
  buttonVariant?: 'link' | 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | null | undefined;
  buttonClassName?: string;
  popoverAlign?: "start" | "center" | "end";
  popoverSide?: "top" | "right" | "bottom" | "left";
}

export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  initialStartDate = '',
  initialEndDate = '',
  onApplyFilter,
  buttonSize = 'sm',
  buttonVariant = 'outline',
  buttonClassName = '',
  popoverAlign = "center",
  popoverSide = "bottom"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [displayStartDate, setDisplayStartDate] = useState(initialStartDate);
  const [displayEndDate, setDisplayEndDate] = useState(initialEndDate);
  const [selectedStartDate, setSelectedStartDate] = useState(initialStartDate);
  const [selectedEndDate, setSelectedEndDate] = useState(initialEndDate);

  useEffect(() => {
    setDisplayStartDate(initialStartDate);
    setDisplayEndDate(initialEndDate);
    setSelectedStartDate(initialStartDate);
    setSelectedEndDate(initialEndDate);
  }, [initialStartDate, initialEndDate]);

  const handleApplyPreset = useCallback((preset: string) => {
    let start = '';
    let end = getTodayStr();
    switch (preset) {
      case 'today':
        start = getTodayStr();
        break;
      case '7days':
        start = getDaysAgoStr(7);
        break;
      case '30days':
        start = getDaysAgoStr(30);
        break;
      case '90days':
        start = getDaysAgoStr(90);
        break;
      case '1year':
        start = getDaysAgoStr(365);
        break;
    }
    setSelectedStartDate(start);
    setSelectedEndDate(end);
  }, []);

  const handleApplyFilter = useCallback(() => {
    onApplyFilter(selectedStartDate, selectedEndDate);
    setDisplayStartDate(selectedStartDate);
    setDisplayEndDate(selectedEndDate);
    setIsOpen(false);
  }, [selectedStartDate, selectedEndDate, onApplyFilter]);

  const handleClearFilter = useCallback(() => {
    setSelectedStartDate('');
    setSelectedEndDate('');
  }, []);

  const formattedDateRangeText = () => {
    if (displayStartDate && displayEndDate) {
      return `${formatDate(displayStartDate)} - ${formatDate(displayEndDate)}`;
    } else if (displayStartDate) {
      return `从 ${formatDate(displayStartDate)} 开始`;
    } else if (displayEndDate) {
      return `截至 ${formatDate(displayEndDate)}`;
    }
    return "日期筛选";
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={`flex items-center gap-1 text-xs ${buttonClassName}`.trim()}
        >
          <CalendarIcon className="h-3.5 w-3.5" />
          <ClientOnly>
            <div className="text-xs whitespace-nowrap">{formattedDateRangeText()}</div>
          </ClientOnly>
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-auto md:w-72 p-4 space-y-3" 
        align={popoverAlign} 
        side={popoverSide}
      >
        <div>
          <h4 className="font-medium mb-2">日期范围筛选</h4>
          <p className="text-sm text-muted-foreground mb-2">选择日期范围进行筛选</p>
          <div className="flex flex-wrap gap-2 mb-4">
            {[
              { preset: 'today', label: '今日' },
              { preset: '7days', label: '最近7天' },
              { preset: '30days', label: '最近30天' },
              { preset: '90days', label: '最近90天' },
              { preset: '1year', label: '最近一年' },
            ].map(({ preset, label }) => (
              <Button
                key={preset}
                variant="outline"
                size="sm"
                className="text-xs h-7 px-2"
                onClick={() => handleApplyPreset(preset)}
              >
                {label}
              </Button>
            ))}
          </div>
        </div>
        <div>
          <label htmlFor="startDateFilter" className="block text-sm font-medium mb-1">开始日期</label>
          <div className="relative">
            <Input
              id="startDateFilter"
              type="date"
              value={selectedStartDate}
              onChange={(e) => setSelectedStartDate(e.target.value)}
              className="w-full pr-8"
            />
            {selectedStartDate && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 -translate-y-1/2 h-full px-2"
                onClick={() => setSelectedStartDate('')}
                aria-label="清除开始日期"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <div>
          <label htmlFor="endDateFilter" className="block text-sm font-medium mb-1">结束日期</label>
          <div className="relative">
            <Input
              id="endDateFilter"
              type="date"
              value={selectedEndDate}
              onChange={(e) => setSelectedEndDate(e.target.value)}
              className="w-full pr-8"
            />
            {selectedEndDate && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 -translate-y-1/2 h-full px-2"
                onClick={() => setSelectedEndDate('')}
                aria-label="清除结束日期"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <div className="flex justify-between pt-2">
          <Button variant="outline" size="sm" onClick={handleClearFilter}>清除</Button>
          <Button variant="default" size="sm" onClick={handleApplyFilter}>应用</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}; 