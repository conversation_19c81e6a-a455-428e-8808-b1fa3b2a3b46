from django.db import models
import uuid
from django.contrib.auth.models import User
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

# 数据模型定义
class UploadedFile(models.Model):
    """
    上传文件记录
    """
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('deleted', '已删除'),
        ('archived', '已归档'),
        ('purged', '已清除'),
    ]
    
    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name="文件ID")
    original_name = models.CharField(max_length=255, verbose_name="原始文件名")
    saved_path = models.CharField(max_length=1024, verbose_name="保存路径")
    file_size = models.BigIntegerField(verbose_name="文件大小")
    archive_box_number = models.CharField(max_length=100, blank=True, null=True, db_index=True, verbose_name="档案盒号")
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")
    uploader = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name="上传者"
    )
    file_hash = models.CharField(max_length=64, blank=True, null=True, db_index=True, verbose_name="文件SHA256哈希")
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='active', 
        db_index=True, 
        verbose_name="文件状态"
    )
    
    class Meta:
        verbose_name = "上传的文件"
        verbose_name_plural = verbose_name
        ordering = ['-upload_time']
        indexes = [
            models.Index(fields=['upload_time']),
            models.Index(fields=['status']),
        ]
        
    def __str__(self):
        return f"{self.original_name} ({self.file_id})"

    def save(self, *args, **kwargs):
        if not self.archive_box_number:
            self.archive_box_number = f"BOX-{timezone.now().strftime('%Y%m%d')}-TEMP"
            logger.info(f"文件 {self.original_name} 未提供盒号，已自动生成临时盒号: {self.archive_box_number}")
        super().save(*args, **kwargs)

class ProcessingTask(models.Model):
    """
    处理任务记录
    """
    STATUS_CHOICES = (
        # 准备工作
        ('pending', '待处理'),
        ('queued', '已入队'),
        # 串行处理
        ('processing', '处理中'),
        # 并行处理
        ('chunking', '正在分块'),
        ('processing_parallel', '并行处理中'),
        ('aggregating', '汇总处理中'),
        # 完成
        ('completed', '已完成'),
        ('completed_without_report', '已完成(报告未完全分离)'),
        # 失败
        ('failed_validation', '验证失败'),
        ('failed', '处理失败'),
    )
    
    task_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name="任务ID")
    file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, verbose_name="关联文件")
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default='pending', db_index=True, verbose_name="任务状态")
    progress = models.IntegerField(default=0, verbose_name="处理进度")
    error_message = models.TextField(blank=True, null=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    result_data = models.JSONField(null=True, blank=True, verbose_name="结果数据(文件-编号映射)")
    summary_report_url = models.URLField(blank=True, null=True, verbose_name="处理报告URL")
    task_type = models.CharField(max_length=50, default='pdf_processing', db_index=True, verbose_name="任务类型")
    processing_params = models.JSONField(null=True, blank=True, verbose_name="处理参数")
    retry_count = models.PositiveIntegerField(default=0, verbose_name="重试次数")
    
    class Meta:
        verbose_name = "处理任务"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['file', 'task_type']),
        ]
        
    def __str__(self):
        return f"任务 {self.task_id} ({self.get_status_display()}) - 文件 {self.file.original_name}"

# CHANGE: [2024-07-30] 添加PDF分块处理子任务模型 #AFM-parallel-processing
class PDFChunkTask(models.Model):
    """用于跟踪PDF分块处理任务的状态和结果"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent_task = models.ForeignKey('ProcessingTask', on_delete=models.CASCADE, related_name='chunks')
    pdf_path = models.CharField(max_length=512, help_text='原始PDF文件路径')
    start_page = models.IntegerField(help_text='起始页码（包含）')
    end_page = models.IntegerField(help_text='结束页码（包含）')
    chunk_index = models.IntegerField(help_text='块索引号，用于排序')
    status = models.CharField(
        max_length=32,
        choices=[
            ('pending', '等待处理'),
            ('processing', '处理中'),
            ('completed', '已完成'),
            ('failed', '处理失败')
        ],
        default='pending'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    result_data = models.JSONField(null=True, blank=True, help_text='子任务处理结果数据')
    error = models.TextField(null=True, blank=True, help_text='处理错误信息')
    
    def __str__(self):
        return f"PDF子任务 {self.id} - 页码 {self.start_page}-{self.end_page} - 状态:{self.status}"
    
    class Meta:
        ordering = ['chunk_index']
        indexes = [
            models.Index(fields=['parent_task', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

