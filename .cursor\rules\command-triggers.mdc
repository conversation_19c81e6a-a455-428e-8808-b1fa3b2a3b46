---
description: 
globs: 
alwaysApply: false
---
# 命令触发词

本文档定义了一系列特殊命令的触发词，当用户输入这些触发词时，系统应自动执行相应的操作流程。

## 提交代码相关

**触发词**:
- "帮我编写commit"
- "我想进行commit"
- "准备提交"

**操作流程**:
1. 分析当前代码变更
2. 制定提交计划
3. 更新相关文档
4. 生成规范化的Commit Message

## 文档更新相关

**触发词**:
- "刚才讨论的内容更新一下文档吧"
- "同步一下项目状态"

**操作流程**:
1. 回顾项目关键文档及其核心职责
2. 分析需要更新的文档内容
3. 向用户提出具体的澄清问题
4. 执行文档更新

## 计划变更相关

**触发词**:
- "我们下一步不做A了，先做B"
- "把任务X的优先级提前"
- "调整任务优先级"

**操作流程**:
1. 更新检查点文档
2. 分析变更对核心计划文档的潜在影响
3. 向用户确认变更内容
4. 执行修改

## 项目状态查询

**触发词**:
- "请重新全面了解项目状态和下一步任务"
- "查看当前项目状态"
- "显示待办任务"

**操作流程**:
1. 系统性地解读AgentReadme文件夹中的文档
2. 准确理解项目上下文
3. 定位当前任务
4. 遵循既定协作流程

