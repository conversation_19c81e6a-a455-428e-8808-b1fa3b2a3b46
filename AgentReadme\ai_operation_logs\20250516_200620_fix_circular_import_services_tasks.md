# Operation Document: 解决 services 和 tasks 模块间的循环导入问题

## 📋 Change Summary

**Purpose**: 解决 `archive_records/services/import_session_manager.py` 和 `archive_records/tasks.py` 之间的循环导入问题，该问题导致 `ImportError`。
**Scope**: 主要影响上述两个文件及其导入方式。
**Associated**: 用户报告的 `ImportError` Traceback。

## 🔧 Operation Steps

### 📊 OP-001: 分析循环导入

**Precondition**: 项目存在 `ImportError`，Traceback 指向循环导入。
**Operation**: 分析Traceback，确定 `import_session_manager.py` 和 `tasks.py` 之间存在双向导入依赖。
**Postcondition**: 明确了循环导入的路径和涉及的模块及符号。

### ✏️ OP-002: 修改 `archive_records/services/import_session_manager.py`

**Precondition**: `import_session_manager.py` 在顶层导入了 `tasks.py` 中的 `cleanup_cancelled_session_task`。
**Operation**:

1. 移除顶层导入 `from ..tasks import cleanup_cancelled_session_task`。
2. 添加顶层导入 `from celery import current_app`。
3. 修改 `_cleanup_session_resources` 方法（以及附带的 `cleanup_expired_sessions` 方法）中对 `cleanup_cancelled_session_task` 的调用，使其通过 Celery app 实例和任务的字符串名称 (`current_app.send_task('archive_records.tasks.cleanup_cancelled_session_task', args=[...])`) 进行调用。
**Postcondition**: `import_session_manager.py` 不再在模块加载时直接依赖 `tasks.py`，任务调用解耦。

### ✏️ OP-003: 修改 `archive_records/tasks.py`

**Precondition**: `tasks.py` 在顶层导入了 `import_session_manager.py` 中的 `ImportSessionManager`。
**Operation**:

1. 移除顶层导入 `from .services.import_session_manager import ImportSessionManager`。
2. 在需要 `ImportSessionManager` 的任务函数 `run_cleanup_expired_sessions_task` 内部，添加局部导入 `from .services.import_session_manager import ImportSessionManager`。
**Postcondition**: `tasks.py` 在模块加载时不直接依赖 `import_session_manager.py`，服务使用解耦。

### ✅ OP-004: 验证

**Precondition**: 代码已修改。
**Operation**: 重新运行 Django 应用 (例如执行 `manage.py migrate` 或启动开发服务器) 以确认 `ImportError` 已解决。
**Postcondition**: 应用正常启动，无循环导入错误。

## 📝 Change Details

### CH-001: 修改 `archive_records/services/import_session_manager.py`

**File**: `archive_records/services/import_session_manager.py`
**Before**:

```python
# ...
from ..models import ImportSession, ImportSessionStatus, SessionOperation, ImportConflictDetail
from ..tasks import cleanup_cancelled_session_task # <--- offending import
# ...
class ImportSessionManager:
    # ...
    def _cleanup_session_resources(self, ...):
        # ...
        cleanup_cancelled_session_task.delay(str(db_session.session_id)) # <--- direct call
        # ...
    # ...
    def cleanup_expired_sessions(self, ...):
        # ...
        cleanup_cancelled_session_task.delay(str(session_to_process.session_id)) # <--- direct call
        # ...
```

**After**:

```python
# ...
from ..models import ImportSession, ImportSessionStatus, SessionOperation, ImportConflictDetail
# from ..tasks import cleanup_cancelled_session_task # <--- REMOVED
from celery import current_app # <--- ADDED
# ...
class ImportSessionManager:
    # ...
    def _cleanup_session_resources(self, ...):
        # ...
        task_name = 'archive_records.tasks.cleanup_cancelled_session_task'
        current_app.send_task(task_name, args=[str(db_session.session_id)]) # <--- MODIFIED
        # ...
    # ...
    def cleanup_expired_sessions(self, ...):
        # ...
        task_name = 'archive_records.tasks.cleanup_cancelled_session_task'
        current_app.send_task(task_name, args=[str(session_to_process.session_id)]) # <--- MODIFIED
        # ...
```

**Rationale**: 通过Celery任务名称字符串调用，解除了模块加载时的直接依赖。
**Potential Impact**: 如果任务名称字符串错误或任务未在Celery中正确注册，会导致任务调用失败。代码中的任务名称 `archive_records.tasks.cleanup_cancelled_session_task` 需要确保与Celery中注册的名称一致。

### CH-002: 修改 `archive_records/tasks.py`

**File**: `archive_records/tasks.py`
**Before**:

```python
# ...
from .models import ImportSession, ImportSessionStatus, SessionOperation
from .services.import_session_manager import ImportSessionManager # <--- offending import
# ...
@shared_task(name="archive_records.tasks.run_cleanup_expired_sessions_task")
def run_cleanup_expired_sessions_task():
    # ...
    manager = ImportSessionManager()
    # ...
```

**After**:

```python
# ...
from .models import ImportSession, ImportSessionStatus, SessionOperation
# from .services.import_session_manager import ImportSessionManager # <--- REMOVED
# ...
@shared_task(name="archive_records.tasks.run_cleanup_expired_sessions_task")
def run_cleanup_expired_sessions_task():
    # ...
    from .services.import_session_manager import ImportSessionManager # <--- ADDED LOCAL IMPORT
    manager = ImportSessionManager()
    # ...
```

**Rationale**: 将导入延迟到函数执行时，避免了模块加载时的循环依赖。
**Potential Impact**: 无显著负面影响，这是一种标准的循环依赖解决方案。

## ✅ Verification Results

**Method**: 用户需在修改后重新运行其Django应用（例如通过 `manage.py migrate` 或启动开发服务器）。
**Results**: 预期 `ImportError: cannot import name ... (most likely due to a circular import)` 错误不再出现。
**Problems**: N/A
**Solutions**: N/A
