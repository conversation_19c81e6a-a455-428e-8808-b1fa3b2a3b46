# CHANGE: [2025-06-08] 创建发放单数据服务层
from typing import Dict, List, Optional, Union
from django.db import models, transaction
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime
from report_issuing.models import IssueForm, IssueFormItem
from archive_records.models import ArchiveRecord
import logging

logger = logging.getLogger(__name__)


class IssueFormService:
    """
    发放单数据服务层
    
    提供发放单模型的基础CRUD操作，专注数据操作，不包含复杂业务逻辑。
    职责：
    - 发放单的创建、查询、更新、删除
    - 发放单状态的转换操作
    - 数据验证和约束检查
    """
    
    def create_issue_form(self, form_data: Dict, user_id: int) -> Dict:
        """
        创建发放单
        
        Args:
            form_data: 发放单数据 {
                'number': str,                   # 发放单编号（可选，自动生成）
                'issue_date': datetime,          # 发放日期
                'receiver_name': str,            # 领取人姓名
                'receiver_unit': str,            # 领取单位
                'receiver_phone': str,           # 领取人电话
                'notes': str,                    # 备注（可选）
            }
            user_id: 创建用户ID
            
        Returns:
            Dict: {
                'success': bool,
                'data': IssueForm instance or None,
                'error': str or None
            }
        """
        try:
            with transaction.atomic():
                # 获取用户对象
                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    return {
                        'success': False,
                        'data': None,
                        'error': f'用户不存在: user_id={user_id}'
                    }
                
                # 生成发放单编号（如果未提供）
                if not form_data.get('number'):
                    form_data['number'] = self._generate_issue_form_number(form_data.get('issue_date'))
                
                # 创建发放单实例
                issue_form = IssueForm.objects.create(
                    number=form_data['number'],
                    issue_date=form_data['issue_date'],
                    receiver_name=form_data['receiver_name'],
                    receiver_unit=form_data['receiver_unit'],
                    receiver_phone=form_data['receiver_phone'],
                    notes=form_data.get('notes', ''),
                    issuer=user,
                    status='draft'  # 默认创建为草稿状态
                )
                
                logger.info(f"成功创建发放单: {issue_form.number}, user_id={user_id}")
                
                return {
                    'success': True,
                    'data': issue_form,
                    'error': None
                }
                
        except Exception as e:
            logger.error(f"创建发放单失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'data': None,
                'error': f'创建失败: {str(e)}'
            }
    
    def get_issue_form_by_id(self, form_id: int, include_items: bool = False) -> Optional['IssueForm']:
        """
        根据ID获取发放单
        
        Args:
            form_id: 发放单ID
            include_items: 是否预加载发放条目
            
        Returns:
            IssueForm instance or None
        """
        try:
            queryset = IssueForm.objects.select_related('issuer', 'deleted_by')
            
            if include_items:
                queryset = queryset.prefetch_related(
                    'items__archive_record',
                    'issue_records'
                )
            
            return queryset.get(id=form_id, is_deleted=False)
            
        except IssueForm.DoesNotExist:
            logger.warning(f"发放单不存在: form_id={form_id}")
            return None
        except Exception as e:
            logger.error(f"获取发放单失败: form_id={form_id}, error={str(e)}")
            return None
    
    def get_issue_form_by_number(self, number: str) -> Optional['IssueForm']:
        """
        根据编号获取发放单
        
        Args:
            number: 发放单编号
            
        Returns:
            IssueForm instance or None
        """
        try:
            return IssueForm.objects.get(number=number, is_deleted=False)
        except IssueForm.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"根据编号获取发放单失败: number={number}, error={str(e)}")
            return None
    
    def update_issue_form(self, form_id: int, update_data: Dict) -> Dict:
        """
        更新发放单信息
        
        Args:
            form_id: 发放单ID
            update_data: 更新数据 {
                'receiver_name': str,
                'receiver_unit': str,
                'receiver_phone': str,
                'issue_date': datetime,
                'notes': str,
                'confirmation_file': File
            }
            
        Returns:
            Dict: {
                'success': bool,
                'data': IssueForm instance or None,
                'error': str or None
            }
        """
        try:
            issue_form = self.get_issue_form_by_id(form_id)
            if not issue_form:
                return {
                    'success': False,
                    'data': None,
                    'error': '发放单不存在'
                }
            
            # 检查是否可编辑
            if not issue_form.can_edit():
                return {
                    'success': False,
                    'data': None,
                    'error': f'发放单当前状态({issue_form.get_status_display()})不允许编辑'
                }
            
            # 更新允许的字段
            updatable_fields = [
                'receiver_name', 'receiver_unit', 'receiver_phone', 
                'issue_date', 'notes', 'confirmation_file'
            ]
            
            updated_fields = []
            for field in updatable_fields:
                if field in update_data:
                    setattr(issue_form, field, update_data[field])
                    updated_fields.append(field)
            
            if updated_fields:
                issue_form.save(update_fields=updated_fields + ['updated_at'])
                logger.info(f"更新发放单成功: {issue_form.number}, 更新字段: {updated_fields}")
            
            return {
                'success': True,
                'data': issue_form,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"更新发放单失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': None,
                'error': f'更新失败: {str(e)}'
            }
    
    def update_issue_form_status(self, form_id: int, new_status: str, user_id: int, reason: str = '') -> Dict:
        """
        更新发放单状态
        
        Args:
            form_id: 发放单ID
            new_status: 新状态 ('draft', 'locked', 'issued')
            user_id: 操作用户ID
            reason: 状态变更原因
            
        Returns:
            Dict: {
                'success': bool,
                'data': IssueForm instance or None,
                'error': str or None
            }
        """
        try:
            issue_form = self.get_issue_form_by_id(form_id)
            if not issue_form:
                return {
                    'success': False,
                    'data': None,
                    'error': '发放单不存在'
                }
            
            # 验证状态转换的合法性
            validation_result = self._validate_status_transition(issue_form, new_status)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'data': None,
                    'error': validation_result['error']
                }
            
            old_status = issue_form.status
            issue_form.status = new_status
            issue_form.save(update_fields=['status', 'updated_at'])
            
            logger.info(f"发放单状态变更成功: {issue_form.number}, {old_status} -> {new_status}, user_id={user_id}, reason={reason}")
            
            return {
                'success': True,
                'data': issue_form,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"更新发放单状态失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': None,
                'error': f'状态更新失败: {str(e)}'
            }
    
    def soft_delete_issue_form(self, form_id: int, user_id: int, reason: str) -> Dict:
        """
        软删除发放单
        
        Args:
            form_id: 发放单ID
            user_id: 删除用户ID
            reason: 删除原因
            
        Returns:
            Dict: {
                'success': bool,
                'data': IssueForm instance or None,
                'error': str or None
            }
        """
        try:
            issue_form = self.get_issue_form_by_id(form_id)
            if not issue_form:
                return {
                    'success': False,
                    'data': None,
                    'error': '发放单不存在'
                }
            
            # 检查是否可软删除
            if not issue_form.can_soft_delete():
                return {
                    'success': False,
                    'data': None,
                    'error': f'发放单当前状态({issue_form.get_status_display()})不允许软删除'
                }
            
            try:
                deleted_by = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return {
                    'success': False,
                    'data': None,
                    'error': f'操作用户不存在: user_id={user_id}'
                }
            
            # 执行软删除
            issue_form.is_deleted = True
            issue_form.deleted_by = deleted_by
            issue_form.deleted_at = timezone.now()
            issue_form.deletion_reason = reason
            issue_form.save(update_fields=['is_deleted', 'deleted_by', 'deleted_at', 'deletion_reason', 'updated_at'])
            
            logger.info(f"软删除发放单成功: {issue_form.number}, user_id={user_id}, reason={reason}")
            
            return {
                'success': True,
                'data': issue_form,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"软删除发放单失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': None,
                'error': f'删除失败: {str(e)}'
            }
    
    def hard_delete_issue_form(self, form_id: int) -> Dict:
        """
        硬删除发放单（仅草稿状态）
        
        Args:
            form_id: 发放单ID
            
        Returns:
            Dict: {
                'success': bool,
                'data': None,
                'error': str or None
            }
        """
        try:
            issue_form = self.get_issue_form_by_id(form_id)
            if not issue_form:
                return {
                    'success': False,
                    'data': None,
                    'error': '发放单不存在'
                }
            
            # 检查是否可硬删除
            if not issue_form.can_hard_delete():
                return {
                    'success': False,
                    'data': None,
                    'error': f'发放单当前状态({issue_form.get_status_display()})不允许硬删除'
                }
            
            form_number = issue_form.number
            issue_form.delete()  # 会触发模型的delete方法，清理相关数据
            
            logger.info(f"硬删除发放单成功: {form_number}")
            
            return {
                'success': True,
                'data': None,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"硬删除发放单失败: form_id={form_id}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': None,
                'error': f'删除失败: {str(e)}'
            }
    
    def list_issue_forms(
        self, 
        filters: Optional[Dict] = None,
        page: int = 1,
        page_size: int = 20,
        order_by: Optional[List[str]] = None
    ) -> Dict:
        """
        分页查询发放单列表
        
        Args:
            filters: 查询过滤条件 {
                'status': str,                   # 状态筛选
                'issuer_id': int,               # 发放人筛选
                'receiver_name': str,           # 领取人筛选
                'receiver_unit': str,           # 领取单位筛选
                'issue_date_start': datetime,   # 发放日期开始
                'issue_date_end': datetime,     # 发放日期结束
                'created_at_start': datetime,   # 创建时间开始
                'created_at_end': datetime,     # 创建时间结束
                'include_deleted': bool,        # 是否包含已删除的
            }
            page: 页码
            page_size: 每页记录数
            order_by: 排序字段
            
        Returns:
            Dict: {
                'success': bool,
                'data': {
                    'items': List[IssueForm],
                    'pagination': Dict
                },
                'error': str or None
            }
        """
        try:
            # 构建查询集
            queryset = IssueForm.objects.select_related('issuer', 'deleted_by')
            
            # 应用过滤条件
            if filters:
                queryset = self._apply_list_filters(queryset, filters)
            
            # 默认不包含已删除的记录
            if not filters or not filters.get('include_deleted', False):
                queryset = queryset.filter(is_deleted=False)
            
            # 应用排序
            if order_by:
                queryset = queryset.order_by(*order_by)
            else:
                queryset = queryset.order_by('-created_at')
            
            # 分页处理
            from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
            
            paginator = Paginator(queryset, page_size)
            total_count = paginator.count
            total_pages = paginator.num_pages
            
            try:
                current_page = paginator.page(page)
                items = list(current_page.object_list)
            except PageNotAnInteger:
                current_page = paginator.page(1)
                items = list(current_page.object_list)
                page = 1
            except EmptyPage:
                current_page = paginator.page(total_pages)
                items = list(current_page.object_list)
                page = total_pages
            
            return {
                'success': True,
                'data': {
                    'items': items,
                    'pagination': {
                        'total_count': total_count,
                        'total_pages': total_pages,
                        'current_page': page,
                        'page_size': page_size,
                        'has_previous': current_page.has_previous(),
                        'has_next': current_page.has_next(),
                    }
                },
                'error': None
            }
            
        except Exception as e:
            logger.error(f"查询发放单列表失败: error={str(e)}", exc_info=True)
            return {
                'success': False,
                'data': {
                    'items': [],
                    'pagination': {}
                },
                'error': f'查询失败: {str(e)}'
            }
    
    def _generate_issue_form_number(self, issue_date: Optional[datetime] = None) -> str:
        """
        生成发放单编号
        
        Args:
            issue_date: 发放日期，默认为当前日期
            
        Returns:
            str: 发放单编号，格式：IF-YYYYMMDD-NNNN
        """
        if not issue_date:
            issue_date = timezone.now()
        
        date_str = issue_date.strftime('%Y%m%d')
        
        # 查找当日最大编号
        prefix = f"IF-{date_str}-"
        latest_form = IssueForm.objects.filter(
            number__startswith=prefix
        ).order_by('-number').first()
        
        if latest_form:
            try:
                # 提取序号并+1
                last_number = latest_form.number.split('-')[-1]
                next_number = int(last_number) + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:04d}"
    
    def _validate_status_transition(self, issue_form: 'IssueForm', new_status: str) -> Dict:
        """
        验证状态转换的合法性
        
        Args:
            issue_form: 发放单实例
            new_status: 目标状态
            
        Returns:
            Dict: {'valid': bool, 'error': str or None}
        """
        current_status = issue_form.status
        
        # 定义允许的状态转换
        allowed_transitions = {
            'draft': ['locked'],
            'locked': ['draft', 'issued'],
            'issued': []  # 已发放状态不能转换到其他状态
        }
        
        if new_status not in allowed_transitions.get(current_status, []):
            return {
                'valid': False,
                'error': f'不允许从状态"{issue_form.get_status_display()}"转换到"{dict(IssueForm._meta.get_field("status").choices).get(new_status, new_status)}"'
            }
        
        # 额外的业务逻辑验证
        if new_status == 'locked':
            # 锁定前检查是否有发放条目
            if not issue_form.items.exists():
                return {
                    'valid': False,
                    'error': '发放单必须包含至少一个发放条目才能锁定'
                }
        
        elif new_status == 'issued':
            # 发放前检查是否已锁定
            if current_status != 'locked':
                return {
                    'valid': False,
                    'error': '只有锁定状态的发放单才能执行发放操作'
                }
        
        return {'valid': True, 'error': None}
    
    def _apply_list_filters(self, queryset, filters: Dict):
        """
        应用列表查询的过滤条件
        
        Args:
            queryset: 查询集
            filters: 过滤条件
            
        Returns:
            QuerySet: 过滤后的查询集
        """
        # 状态筛选
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        # 发放人筛选
        if filters.get('issuer_id'):
            queryset = queryset.filter(issuer_id=filters['issuer_id'])
        
        # 领取人筛选
        if filters.get('receiver_name'):
            queryset = queryset.filter(receiver_name__icontains=filters['receiver_name'])
        
        # 领取单位筛选
        if filters.get('receiver_unit'):
            queryset = queryset.filter(receiver_unit__icontains=filters['receiver_unit'])
        
        # 发放日期范围筛选
        if filters.get('issue_date_start'):
            queryset = queryset.filter(issue_date__gte=filters['issue_date_start'])
        if filters.get('issue_date_end'):
            queryset = queryset.filter(issue_date__lte=filters['issue_date_end'])
        
        # 创建时间范围筛选
        if filters.get('created_at_start'):
            queryset = queryset.filter(created_at__gte=filters['created_at_start'])
        if filters.get('created_at_end'):
            queryset = queryset.filter(created_at__lte=filters['created_at_end'])
        
        return queryset

    def delete_issue_form_items_by_ids(self, item_ids: List[int], form_id: int) -> Dict:
        """
        根据ID列表批量删除发放条目

        Args:
            item_ids: 要删除的发放条目ID列表
            form_id: 所属的发放单ID（用于验证归属权）

        Returns:
            Dict: {
                'removed_count': int,
                'warnings': List[str]
            }
        """
        removed_count = 0
        warnings = []
        
        # 确保只删除属于指定发放单的条目
        items_to_delete = IssueFormItem.objects.filter(
            id__in=item_ids,
            issue_form_id=form_id
        )
        
        found_item_ids = set(items_to_delete.values_list('id', flat=True))
        
        # 批量删除
        deleted_count, _ = items_to_delete.delete()
        removed_count += deleted_count
        
        # 检查是否有ID未找到或不匹配
        requested_ids = set(item_ids)
        not_found_ids = requested_ids - found_item_ids
        
        if not_found_ids:
            warning_msg = f"部分条目未找到或不属于该发放单: {list(not_found_ids)}"
            warnings.append(warning_msg)
            logger.warning(f"删除发放条目时: {warning_msg}, form_id={form_id}")
            
        return {
            'removed_count': removed_count,
            'warnings': warnings
        }

# TODO: [P1] 添加发放单统计信息查询功能
# TODO: [P2] 添加发放单编号冲突检测和自动修复功能 