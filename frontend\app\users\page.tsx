import { PageTitle } from "@/components/page-title"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ListChecks, Settings, Plus, Users, UserCheck, UserX, Activity } from "lucide-react"
import Link from "next/link"
import { UserRoleDistribution } from "@/components/users/user-role-distribution"
import { RecentUsers } from "@/components/users/recent-users"
import { UserActivityLog } from "@/components/users/user-activity-log"
import { UserSearchForm } from "@/components/users/user-search-form"

export default function UsersPage() {
  return (
    <div className="space-y-6">
      <PageTitle title="用户管理" subtitle="管理系统用户和权限" />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">较上月增长 +5.2%</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">98</div>
            <p className="text-xs text-muted-foreground">占总用户 77.2%</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未激活用户</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">29</div>
            <p className="text-xs text-muted-foreground">占总用户 22.8%</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本月新增</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">较上月增长 +20%</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-7">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>用户角色分布</CardTitle>
            <CardDescription>系统中各角色用户数量分布</CardDescription>
          </CardHeader>
          <CardContent>
            <UserRoleDistribution />
          </CardContent>
        </Card>

        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用用户管理功能</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Button asChild className="h-auto flex flex-col items-center justify-center gap-2 py-4">
                <Link href="/users/list">
                  <ListChecks className="h-8 w-8" />
                  <span>用户列表</span>
                </Link>
              </Button>

              <Button
                asChild
                variant="secondary"
                className="h-auto flex flex-col items-center justify-center gap-2 py-4"
              >
                <Link href="/users/new">
                  <Plus className="h-8 w-8" />
                  <span>新建用户</span>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto flex flex-col items-center justify-center gap-2 py-4">
                <Link href="/users/roles">
                  <Settings className="h-8 w-8" />
                  <span>角色权限</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent" className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-3">
          <TabsTrigger value="recent">最近添加的用户</TabsTrigger>
          <TabsTrigger value="activity">用户活动日志</TabsTrigger>
          <TabsTrigger value="search">用户搜索</TabsTrigger>
        </TabsList>

        <TabsContent value="recent" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>最近添加的用户</CardTitle>
              <CardDescription>系统中最近添加的10个用户</CardDescription>
            </CardHeader>
            <CardContent>
              <RecentUsers />
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="ml-auto">
                <Link href="/users/list">查看所有用户</Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>用户活动日志</CardTitle>
              <CardDescription>记录用户最近的登录和操作</CardDescription>
            </CardHeader>
            <CardContent>
              <UserActivityLog />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="search" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>用户搜索</CardTitle>
              <CardDescription>按条件搜索系统用户</CardDescription>
            </CardHeader>
            <CardContent>
              <UserSearchForm />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
