export interface ProcessingTask {
  taskId: string;
  status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed' | 'failed_validation' | 'chunking' | 'processing_parallel' | 'aggregating' | 'completed_without_report';
  progress: number;
  createdAt: string;
  uploadedFile: {
    originalName: string;
    username: string;
    archiveBoxNumber?: string;
  };
  summaryReportUrl?: string;
  fileId: string;
  errorMessage?: string;
}

export interface ArchiveRecord {
  id: number;
  unifiedNumber: string;
  archiveStatus: string;
  archiveUrl?: string;
  reportUrl?: string;
} 