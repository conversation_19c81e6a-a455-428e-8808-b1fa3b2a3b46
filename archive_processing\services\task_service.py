from ..models import UploadedFile, ProcessingTask
from typing import Tuple

# from ..tasks import process_pdf_task  # 导入Celery任务 - CHANGE: 延迟导入以解决循环依赖
import logging
import uuid
import datetime  # CHANGE: 导入 datetime 用于日期标记

logger = logging.getLogger(__name__)

# CHANGE: [2024-03-28] 创建任务服务 TaskService 框架 #AFM-3
# TODO: [P1] 实现 get_task_status 方法的完整逻辑
# TODO: [P1] 实现任务参数传递和处理逻辑


class TaskService:
    """
    任务管理服务，处理任务创建、状态更新和查询
    """

    @staticmethod
    def create_task(
        uploaded_file: UploadedFile,
        task_type: str = "pdf_processing",
        params: dict = None,
    ) -> ProcessingTask:
        """
        创建新的处理任务并将其放入队列。

        Args:
            uploaded_file: 关联的 UploadedFile 实例。
            task_type: 任务类型 (例如 'pdf_processing')。
            params: 任务需要的额外参数 (例如，分割依据文本)。

        Returns:
            ProcessingTask: 创建的任务记录实例。

        Raises:
            Exception: 如果任务创建或入队失败。
        """
        # Thinking: 首先在数据库中创建任务记录，初始状态为 'pending'。
        try:
            task_record = ProcessingTask.objects.create(
                file=uploaded_file,
                status="pending",
                task_type=task_type,
                processing_params=params,
            )
            logger.info(
                f"创建了新的处理任务: {task_record.task_id} 文件ID: {uploaded_file.file_id}"
            )
        except Exception as e:
            logger.error(f"数据库创建任务记录失败: {e}", exc_info=True)
            # TODO: [P1] 考虑是否需要通知或回滚其他操作
            raise Exception(f"无法创建处理任务记录: {e}")

        # Thinking: 将实际的处理任务发送到Celery队列。
        try:
            # 传递 task_id 以便 worker 更新状态
            # 注意：只传递必要的基本类型数据给Celery任务，避免传递复杂对象

            # CHANGE: [2025-04-18] 修复参数不匹配问题
            # 之前我们传递了多个参数，但process_pdf_task只接受两个参数(self, task_id)
            # 由于我们已经在数据库存储了所有必要信息，只需传递task_id即可
            # target_text = params.get('target_text') if params else None

            # CHANGE: [2024-07-26] 延迟导入 process_pdf_task 以解决循环依赖问题
            # CHANGE: [2025-04-22] 使用dispatch_pdf_processing替换过时的process_pdf_task
            from ..tasks.core_tasks import dispatch_pdf_processing

            # CHANGE: [2025-06-24] 修复参数传递 - 从params中提取处理参数
            # 提取处理参数，提供默认值
            use_parallel = params.get('use_parallel', True) if params else True
            chunk_size = params.get('chunk_size', 20) if params else 20
            
            # CHANGE: [YYYY-MM-DD] 遵循"先更新状态，后入队"原则，提升系统健壮性
            # 关键：在入队之前，立刻将状态更新为 'queued'
            task_record.status = "queued"
            task_record.save(update_fields=['status'])
            logger.info(f"任务 {task_record.task_id} 状态更新为 'queued'，准备入队...")

            dispatch_pdf_processing(
                task_id=task_record.task_id,
                use_parallel=use_parallel,
                chunk_size=chunk_size
            )

            # Thinking: 任务成功入队后，确认日志记录
            logger.info(f"任务 {task_record.task_id} 已成功发送到队列")
            return task_record
        except Exception as e:
            logger.error(
                f"发送任务 {task_record.task_id} 到队列失败: {e}", exc_info=True
            )
            # 任务入队失败，将数据库状态标记为 'failed'
            task_record.status = "failed"
            task_record.error_message = f"任务入队失败: {e}"
            task_record.save(update_fields=['status', 'error_message'])
            # TODO: [P1] 实现更健壮的错误处理和可能的自动重试入队逻辑
            raise Exception(f"无法将任务发送到处理队列: {e}")

    @staticmethod
    def get_task_status(task_id: uuid.UUID):
        """
        获取指定任务的状态和进度。

        Args:
            task_id: 要查询的任务ID。

        Returns:
            dict: 包含任务状态、进度等信息的字典，如果找不到任务则返回 None。
        """
        # Thinking: 查询数据库获取任务记录。
        try:
            task = ProcessingTask.objects.get(pk=task_id)
            # Thinking: 构造包含所需信息的返回字典。
            # TODO: [P2] 考虑添加更详细的信息，如预计完成时间等
            return {
                "task_id": task.task_id,
                "status": task.status,
                "progress": task.progress,
                "error_message": task.error_message,
                "created_at": task.created_at,
                "updated_at": task.updated_at,
                "result_data": task.result_data,  # 可能包含处理结果
                "file_id": task.file.file_id,
                "original_filename": task.file.original_name,
            }
        except ProcessingTask.DoesNotExist:
            logger.warning(f"尝试查询不存在的任务状态: {task_id}")
            return None
        except Exception as e:
            logger.error(
                f"查询任务状态时发生错误 (Task ID: {task_id}): {e}", exc_info=True
            )
            # TODO: [P1] 返回更具体的错误信息或状态
            return {"error": f"查询任务状态时出错: {e}"}  # 或者抛出异常

    @staticmethod
    def retry_task(task: ProcessingTask, user) -> Tuple[bool, str]:
        """
        重试一个失败的处理任务，封装了所有重试的业务逻辑。

        Args:
            task: 需要重试的 ProcessingTask 实例。
            user: 请求重试的用户对象。

        Returns:
            一个元组 (success, message)，表示操作是否成功和相应的消息。
        """
        # 1. 前置条件检查
        if task.status not in ['failed', 'failed_validation', 'completed_without_report']:
            return False, f"此任务的状态是 '{task.get_status_display()}'，不是可重试的失败状态。"
        
        # 增强的健壮性检查：确保关联文件处于可处理状态
        if task.file.status != 'active':
            return False, f"关联文件状态为'{task.file.status}'，不可重试。文件可能已被删除。"

        try:
            # 2. 准备并重新分发任务
            from ..tasks.core_tasks import dispatch_pdf_processing
            
            params = task.processing_params or {}
            use_parallel = params.get('use_parallel', True)
            chunk_size = params.get('chunk_size', 20)
            
            # 3. 更新数据库状态 (遵循"先更新状态，后入队")
            task.status = 'queued'
            task.error_message = None  # 清除之前的错误信息
            task.retry_count += 1      # 增加重试计数
            task.save(update_fields=['status', 'error_message', 'retry_count'])
            
            logger.info(f"任务 {task.task_id} 已被用户 {user} 请求重试，状态已重置为 'queued'。")
            
            # 4. 重新将任务发送到队列
            dispatch_pdf_processing(
                task_id=task.task_id,
                use_parallel=use_parallel,
                chunk_size=chunk_size
            )

            return True, f"任务 {task.task_id} 已成功加入重试队列。"
        except Exception as e:
            logger.exception(f"在服务层重试任务 {task.task_id} 时发生错误: {e}")
            # 注意：这里的任务状态可能已经是 'queued'，但入队失败。
            # 我们的周期性清理任务会处理这种情况，但也可以在这里直接标记为 failed。
            task.status = 'failed'
            task.error_message = f"重试入队时失败: {e}"
            task.save(update_fields=['status', 'error_message'])
            return False, f"服务器内部错误，重试任务失败: {e}"
