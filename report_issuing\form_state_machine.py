from report_issuing.states import IssueFormState
from report_issuing.state_handlers.base_handler import BaseStateHandler
from report_issuing.state_handlers.draft_handler import DraftHandler
from report_issuing.state_handlers.locked_handler import LockedHandler
from report_issuing.state_handlers.issued_handler import IssuedHandler

# from report_issuing.services.issue_form_service import IssueFormService # 稍后取消注释

import logging

logger = logging.getLogger(__name__)

class FormStateMachine:
    """
    发放单状态机引擎。
    
    这是与发放单状态相关操作的统一入口。
    它根据发放单的当前状态，自动选择并实例化对应的状态处理器，
    然后将操作请求委托给该处理器。
    """
    
    # 将状态枚举映射到对应的处理器类
    _handler_map = {
        IssueFormState.DRAFT: DraftHandler,
        IssueFormState.LOCKED: LockedHandler,
        IssueFormState.ISSUED: IssuedHandler,
        # DELETED 状态通常是最终状态，可能不需要专门的处理器，
        # 除非需要实现"恢复"等操作。
    }

    def __init__(self, form_id: int, user_id: int):
        """
        初始化状态机。
        
        Args:
            form_id (int): 要操作的发放单ID。
            user_id (int): 当前操作用户的ID。
        """
        # TODO: [P1] 依赖注入数据服务
        # form_service = IssueFormService()
        # self.form = form_service.get_issue_form_by_id(form_id)
        
        # 占位符：在服务注入实现前，我们先用一个模拟对象
        class MockForm:
            def __init__(self, id, status):
                self.id = id
                self.status = status
        # 假设我们获取了一个草稿状态的表单
        self.form = MockForm(id=form_id, status="draft") 

        if not self.form:
            raise ValueError(f"发放单不存在: id={form_id}")
            
        try:
            current_state = IssueFormState(self.form.status)
        except ValueError:
            raise TypeError(f"未知的发放单状态: '{self.form.status}'")
            
        handler_class = self._handler_map.get(current_state)
        
        if not handler_class:
            raise TypeError(f"没有为状态 '{current_state.value}' 配置处理器")
            
        self.handler: BaseStateHandler = handler_class(self.form, user_id)
        logger.debug(f"为发放单 {form_id} (状态: {current_state.value}) 初始化了处理器: {handler_class.__name__}")

    def update_draft(self, form_data: dict, items_data: list = None):
        """更新草稿状态的发放单，现在使用替换模式。"""
        return self.handler.update_draft(form_data, items_data)

    def lock(self):
        """锁定发放单。"""
        return self.handler.lock()

    def unlock(self):
        """解锁发放单。"""
        return self.handler.unlock()
    
    def issue(self):
        """归档(发放)发放单。"""
        return self.handler.issue()

    def soft_delete(self, reason: str):
        """软删除发放单。"""
        return self.handler.soft_delete(reason)
        
    def hard_delete(self):
        """硬删除发放单。"""
        return self.handler.hard_delete() 