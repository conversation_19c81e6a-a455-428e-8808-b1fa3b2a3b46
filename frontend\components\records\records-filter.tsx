"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Download } from "lucide-react"
import { useState } from "react"
import { DateRangePicker } from "../ui/date-range-picker"
// TODO: Replace with NextAuth imports after authentication implementation

export function RecordsFilter() {
  const [searchTerm, setSearchTerm] = useState("")
  const [status, setStatus] = useState("")
  // TODO: Replace with useSession() hook after NextAuth implementation

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索统一编号、标题或关键词..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
            <SelectItem value="not_archived">未归档</SelectItem>
            <SelectItem value="issued">已发放</SelectItem>
            <SelectItem value="not_issued">未发放</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
          <span className="sr-only">高级筛选</span>
        </Button>
        {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
        <Button variant="outline" size="icon">
          <Download className="h-4 w-4" />
          <span className="sr-only">导出数据</span>
        </Button>
      </div>
      <div className="flex flex-col sm:flex-row gap-4">
        <DateRangePicker />
        <Select>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="项目类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="type1">类型一</SelectItem>
            <SelectItem value="type2">类型二</SelectItem>
            <SelectItem value="type3">类型三</SelectItem>
          </SelectContent>
        </Select>
        <Select>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="归档盒号" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部盒号</SelectItem>
            <SelectItem value="box1">盒号 001</SelectItem>
            <SelectItem value="box2">盒号 002</SelectItem>
            <SelectItem value="box3">盒号 003</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
