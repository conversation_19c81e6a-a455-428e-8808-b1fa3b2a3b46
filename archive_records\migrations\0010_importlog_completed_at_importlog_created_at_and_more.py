# Generated by Django 5.1.9 on 2025-06-04 16:05

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0009_importsession_analysis_stats'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='importlog',
            name='completed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='完成时间'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='importlog',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='创建此导入任务的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='session_id',
            field=models.CharField(blank=True, db_index=True, help_text='关联的会话ID字符串，用于追溯', max_length=255, null=True, verbose_name='会话ID'),
        ),
        migrations.AddField(
            model_name='importlog',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='importlog',
            name='import_user',
            field=models.ForeignKey(help_text='实际执行导入操作的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='executed_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='导入操作人'),
        ),
    ]
