"""
报告识别服务

此服务负责识别CMA章位置，确定报告页面范围。
这是一个独立的服务，在validate_all_parts_processable之前调用。

职责：
1. 识别档案PDF中的CMA章位置
2. 确定报告的起始页和结束页
3. 为每个档案部分增强parts_info，添加report_page_range

技术方案：
- 基于现有ReportSplittingService的CMA章检测逻辑
- 独立于PDF分割操作，只负责页面范围识别
"""

import logging
import os
import time
from typing import Optional, List, Dict, Any
from PIL import Image
import cv2
import numpy as np
import fitz  # PyMuPDF

from django.conf import settings

logger = logging.getLogger(__name__)


class ReportRecognitionService:
    """
    报告识别服务：负责识别CMA章位置，确定报告页面范围
    这是一个独立的服务，在validate_all_parts_processable之前调用
    """
    
    def __init__(self, template_base_path: Optional[str] = None):
        """初始化报告识别服务"""
        self.template_base_path = template_base_path or self.get_default_template_path()
        
        # 从settings加载配置
        self.dpi = getattr(settings, 'REPORT_RECOGNITION_DPI', 200)
        self.color_threshold = getattr(settings, 'MA_SEAL_COLOR_THRESHOLD', 30)
        self.min_contour_area = getattr(settings, 'MA_SEAL_MIN_CONTOUR_AREA', 500)
        self.feature_match_threshold = getattr(settings, 'MA_SEAL_MATCH_THRESHOLD', 10)
        self.use_sift = getattr(settings, 'MA_SEAL_USE_SIFT', True)
        self.max_features = getattr(settings, 'MA_SEAL_MAX_FEATURES', 500)
        
        # 加载MA章模板
        self.ma_templates = []
        self._load_ma_templates()
        
        logger.info(f"ReportRecognitionService 初始化完成，模板路径: {self.template_base_path}")
    
    def identify_report_ranges(
        self, 
        pdf_path: str, 
        parts_info: List[Dict]
    ) -> List[Dict]:
        """
        识别各部分的报告页面范围
        
        Args:
            pdf_path: 原始PDF路径
            parts_info: 原始部分信息（只包含unified_number和archive_page_range）
            
        Returns:
            扩展后的parts_info（包含report_page_range）
        """
        logger.info(f"开始识别报告页面范围，PDF: {pdf_path}, 部分数: {len(parts_info)}")
        enhanced_parts_info = []
        
        for part in parts_info:
            unified_number = part["unified_number"]
            archive_page_range = part["archive_page_range"]
            
            logger.debug(f"处理部分 {unified_number}, 档案范围: {archive_page_range}")
            
            # 在档案页面范围内查找CMA章
            report_start_page = self.detect_cma_seal_position(
                pdf_path, archive_page_range
            )
            
            enhanced_part = part.copy()
            if report_start_page is not None:
                # 发现CMA章，设置报告页面范围
                # 报告从CMA章开始到档案结尾（相对于档案范围的页码）
                relative_start = report_start_page - archive_page_range[0]
                enhanced_part["report_page_range"] = [
                    relative_start,
                    archive_page_range[1] - archive_page_range[0]
                ]
                logger.info(f"部分 {unified_number} 检测到报告，相对页面范围: {enhanced_part['report_page_range']}")
            else:
                logger.warning(f"部分 {unified_number} 未检测到CMA章，无report_page_range")
            
            enhanced_parts_info.append(enhanced_part)
            
        return enhanced_parts_info
    
    def detect_cma_seal_position(
        self, 
        pdf_path: str, 
        archive_page_range: List[int]
    ) -> Optional[int]:
        """
        在指定档案页面范围内检测CMA章位置
        
        Args:
            pdf_path: PDF文件路径
            archive_page_range: 档案页面范围 [起始页, 结束页] (0-based)
            
        Returns:
            CMA章起始页位置（0-based），如果未找到返回None
        """
        doc = None
        
        try:
            doc = fitz.open(pdf_path)
            start_page, end_page = archive_page_range
            
            logger.debug(f"在页面范围 [{start_page}, {end_page}] 内搜索CMA章")
            
            # 在指定范围内按顺序检测
            for page_num in range(start_page, min(end_page + 1, len(doc))):
                logger.debug(f"检测第 {page_num} 页（0-based）是否包含CMA章...")
                
                # 获取页面图像
                page_image = self._get_page_image(doc, page_num)
                if page_image is None:
                    logger.warning(f"无法获取第 {page_num} 页图像，跳过")
                    continue
                
                # 执行CMA章检测
                if self._detect_ma_seal_in_image(page_image):
                    logger.info(f"在第 {page_num} 页（0-based）检测到CMA章")
                    return page_num
                    
            logger.debug(f"在页面范围内未找到CMA章")
            return None
            
        except Exception as e:
            logger.error(f"CMA章位置检测失败: {e}", exc_info=True)
            return None
        finally:
            if doc:
                doc.close()
    
    def _detect_ma_seal_in_image(self, image: Image.Image) -> bool:
        """在单个图像中检测MA章"""
        try:
            # 第一步：颜色筛选，提取候选区域
            candidate_regions = self._perform_color_filtering(image)
            
            if not candidate_regions:
                logger.debug("颜色筛选未找到候选区域")
                return False
            
            logger.debug(f"颜色筛选找到 {len(candidate_regions)} 个候选区域")
            
            # 第二步：对每个候选区域进行特征匹配
            for i, region in enumerate(candidate_regions):
                logger.debug(f"对候选区域 {i+1} 进行特征匹配...")
                
                if self._perform_feature_matching(region):
                    logger.debug(f"候选区域 {i+1} 特征匹配成功")
                    return True
                    
            logger.debug("所有候选区域特征匹配均失败")
            return False
            
        except Exception as e:
            logger.error(f"图像MA章检测失败: {e}", exc_info=True)
            return False
    
    def _perform_color_filtering(self, image: Image.Image) -> List[Image.Image]:
        """执行颜色筛选，提取红色区域作为候选区"""
        candidate_regions = []
        
        try:
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 转换为HSV色彩空间，便于红色筛选
            hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
            
            # 定义红色范围（HSV）
            # 红色在HSV中有两个范围：0-10和170-180
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            
            # 创建红色掩码
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            mask = mask1 + mask2
            
            # 形态学操作，去除噪声
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 筛选合适大小的轮廓
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= self.min_contour_area:
                    # 获取边界框
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 扩展边界框以包含更多上下文
                    padding = 20
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(cv_image.shape[1] - x, w + 2 * padding)
                    h = min(cv_image.shape[0] - y, h + 2 * padding)
                    
                    # 提取候选区域
                    candidate_region = cv_image[y:y+h, x:x+w]
                    
                    # 转换回PIL格式
                    candidate_pil = Image.fromarray(cv2.cvtColor(candidate_region, cv2.COLOR_BGR2RGB))
                    candidate_regions.append(candidate_pil)
                    
        except Exception as e:
            logger.error(f"颜色筛选失败: {e}", exc_info=True)
            
        return candidate_regions
    
    def _perform_feature_matching(self, candidate_image: Image.Image) -> bool:
        """对候选区域执行特征匹配"""
        try:
            # 转换为OpenCV灰度图像
            candidate_gray = cv2.cvtColor(np.array(candidate_image), cv2.COLOR_RGB2GRAY)
            
            # 初始化特征检测器
            detector = self._get_feature_detector()
            
            # 提取候选区域的特征点和描述符
            kp_candidate, desc_candidate = detector.detectAndCompute(candidate_gray, None)
            
            if desc_candidate is None or len(desc_candidate) < 5:
                logger.debug("候选区域特征点不足")
                return False
            
            # 与每个模板进行匹配
            for template_idx, template_data in enumerate(self.ma_templates):
                if self._match_with_template(desc_candidate, template_data, template_idx):
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"特征匹配失败: {e}", exc_info=True)
            return False
    
    def _match_with_template(self, desc_candidate: np.ndarray, template_data: Dict, template_idx: int) -> bool:
        """与单个模板进行匹配"""
        try:
            template_desc = template_data['descriptors']
            
            if template_desc is None or len(template_desc) < 5:
                logger.debug(f"模板 {template_idx} 特征描述符不足")
                return False
            
            # 创建匹配器
            if self.use_sift:
                # SIFT使用FLANN匹配器
                FLANN_INDEX_KDTREE = 1
                index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
                search_params = dict(checks=50)
                matcher = cv2.FlannBasedMatcher(index_params, search_params)
            else:
                # ORB使用BF匹配器
                matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            
            # 执行匹配
            if self.use_sift:
                matches = matcher.knnMatch(desc_candidate, template_desc, k=2)
                # Lowe's ratio test
                good_matches = []
                for match_pair in matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < 0.7 * n.distance:
                            good_matches.append(m)
            else:
                matches = matcher.match(desc_candidate, template_desc)
                # 按距离排序，取前N个最佳匹配
                matches = sorted(matches, key=lambda x: x.distance)
                good_matches = matches[:50]
            
            # 判断匹配质量
            match_count = len(good_matches)
            logger.debug(f"模板 {template_idx} 匹配数量: {match_count}")
            
            if match_count >= self.feature_match_threshold:
                logger.debug(f"模板 {template_idx} 匹配成功 (匹配点: {match_count})")
                return True
                
        except Exception as e:
            logger.error(f"与模板 {template_idx} 匹配失败: {e}", exc_info=True)
            
        return False
    
    def _get_feature_detector(self):
        """获取特征检测器"""
        if self.use_sift:
            return cv2.SIFT_create(nfeatures=self.max_features)
        else:
            return cv2.ORB_create(nfeatures=self.max_features)
    
    def _get_page_image(self, doc: fitz.Document, page_num: int) -> Optional[Image.Image]:
        """获取PDF页面图像"""
        try:
            page = doc.load_page(page_num)
            
            # 设置缩放比例
            zoom = self.dpi / 72.0
            mat = fitz.Matrix(zoom, zoom)
            
            # 渲染页面为图像
            pix = page.get_pixmap(matrix=mat, alpha=False)
            
            # 确保是RGB格式
            if pix.n != 3:
                logger.warning(f"第 {page_num} 页不是RGB格式 (components={pix.n})，尝试转换")
                try:
                    rgb_pix = fitz.Pixmap(fitz.csRGB, pix.irect, False)
                    rgb_pix.copy(pix, pix.irect)
                    pix = rgb_pix
                except Exception as conv_err:
                    logger.error(f"第 {page_num} 页RGB转换失败: {conv_err}")
                    return None
            
            # 转换为PIL图像
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            return img
            
        except Exception as e:
            logger.error(f"获取第 {page_num} 页图像失败: {e}", exc_info=True)
            return None
    
    def _load_ma_templates(self):
        """加载MA章模板"""
        self.ma_templates = []
        
        # 获取模板路径列表
        template_paths = self._get_template_paths()
        
        if not template_paths:
            logger.warning("未配置MA章模板路径")
            return
        
        detector = self._get_feature_detector()
        
        for template_path in template_paths:
            try:
                if not os.path.exists(template_path):
                    logger.warning(f"MA章模板文件不存在: {template_path}")
                    continue
                
                # 加载模板图像
                template_image = Image.open(template_path)
                template_gray = cv2.cvtColor(np.array(template_image), cv2.COLOR_RGB2GRAY)
                
                # 提取特征
                kp_template, desc_template = detector.detectAndCompute(template_gray, None)
                
                if desc_template is not None and len(desc_template) >= 5:
                    self.ma_templates.append({
                        'path': template_path,
                        'keypoints': kp_template,
                        'descriptors': desc_template
                    })
                    logger.info(f"成功加载MA章模板: {template_path} (特征点: {len(kp_template)})")
                else:
                    logger.warning(f"MA章模板特征点不足: {template_path}")
                    
            except Exception as e:
                logger.error(f"加载MA章模板失败 {template_path}: {e}", exc_info=True)
        
        logger.info(f"总共加载了 {len(self.ma_templates)} 个MA章模板")
    
    def _get_template_paths(self) -> List[str]:
        """获取模板路径列表"""
        template_paths = []
        
        # 首先尝试从settings获取
        ma_template_paths = getattr(settings, 'MA_SEAL_TEMPLATE_PATHS', [])
        if ma_template_paths:
            return ma_template_paths
        
        # 否则扫描模板目录
        if os.path.exists(self.template_base_path):
            for filename in os.listdir(self.template_base_path):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    template_paths.append(os.path.join(self.template_base_path, filename))
        
        return template_paths
    
    @staticmethod
    def get_default_template_path() -> str:
        """获取默认的CMA章模板路径"""
        return os.path.join(settings.BASE_DIR, "templates", "ma_seal") 