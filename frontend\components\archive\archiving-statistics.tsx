"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

export function ArchivingStatistics() {
  const [period, setPeriod] = useState("day")

  return (
    <div>
      <Tabs value={period} onValueChange={setPeriod}>
        <TabsList className="mb-4">
          <TabsTrigger value="day">日</TabsTrigger>
          <TabsTrigger value="week">周</TabsTrigger>
          <TabsTrigger value="month">月</TabsTrigger>
        </TabsList>
        <TabsContent value="day" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">每日归档统计图表</p>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="week" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">每周归档统计图表</p>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="month" className="h-[250px]">
          <div className="flex h-full items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">每月归档统计图表</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
