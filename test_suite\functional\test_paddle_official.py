#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
官方 PaddleOCR 3.0+ API 示例测试脚本
用于验证 paddleocr 库的基本功能是否在当前环境中正常工作。
"""
import logging
import os

try:
    # Initialize PaddleOCR instance
    from paddleocr import PaddleOCR

    # 抑制 paddleocr 的详细日志，只看关键信息
    logging.getLogger("ppocr").setLevel(logging.ERROR)

    print("=" * 60)
    print("🚀 开始运行 PaddleOCR 官方示例测试...")
    print("=" * 60)
    print("1. 正在初始化 PaddleOCR 引擎...")

    # 使用与示例一致的参数，并明确指定英文，因为图片是英文的
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
    )

    print("✅ PaddleOCR 引擎初始化成功。")
    print("\n2. 正在对网络图片进行 OCR 推理...")

    # Run OCR inference on a sample image
    # 注意：如果网络不通，这里可能会失败
    result = ocr.predict(
        input="https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
    )

    print("✅ OCR 推理完成。")
    print("\n3. 正在处理和保存结果...")

    # Visualize the results and save the JSON results
    if result and result[0]:
        for res in result:
            print("\n---识别结果---")
            res.print()
            # 指定输出目录，避免在根目录产生文件
            output_dir = "test_suite/output/functional_test/official_example"
            os.makedirs(output_dir, exist_ok=True)
            saved_img_path = res.save_to_img(output_dir)
            print(f"结果图片已保存至: {saved_img_path}")
            saved_json_path = res.save_to_json(output_dir)
            print(f"结果JSON已保存至: {saved_json_path}")
            print("---结果处理完毕---")

        print("\n🎉 测试成功完成！")
        print(f"请检查目录 '{output_dir}' 下的图片和json文件。")
        print("=" * 60)
    else:
        print("\n❌ OCR未能返回任何结果。")
        print("=" * 60)

except Exception as e:
    print(f"\n💥 测试过程中发生错误: {e}")
    import traceback

    traceback.print_exc()
    print("=" * 60)
