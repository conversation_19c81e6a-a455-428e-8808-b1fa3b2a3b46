"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ArchiveStatistics } from "./archive-statistics"
import { ArchiveCalendar } from "./archive-calendar"
import { RecentArchiveActivities } from "./recent-archive-activities"
import { SystemStatus } from "./system-status"
import {
  BarChart3,
  FileText,
  Users,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Database,
  ArrowRight,
  Upload,
  Package,
  TrendingUp,
  Clock,
  Calendar,
} from "lucide-react"
import Link from "next/link"

export function ArchiveDashboard() {
  const [statisticsTab, setStatisticsTab] = useState("by-archive")
  const [statisticsTimeTab, setStatisticsTimeTab] = useState<"day" | "week" | "month">("day")

  return (
    <div className="space-y-6">
      {/* 顶部统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">今日统计</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold">42</div>
                <p className="text-xs text-muted-foreground">今日归档量</p>
              </div>
              <div className="text-green-600 flex items-center text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                12%
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">今日归档盒数</p>
              </div>
              <div className="text-green-600 flex items-center text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                20%
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">本月统计</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold">876</div>
                <p className="text-xs text-muted-foreground">本月归档量</p>
              </div>
              <div className="text-green-600 flex items-center text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                8%
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold">32</div>
                <p className="text-xs text-muted-foreground">本月归档盒数</p>
              </div>
              <div className="text-red-600 flex items-center text-sm">
                <ArrowDownRight className="h-4 w-4 mr-1" />
                5%
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">处理异常</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold text-red-600">3</div>
                <p className="text-xs text-muted-foreground">今日异常数</p>
              </div>
              <div className="text-red-600 flex items-center text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />2
              </div>
            </div>

            {/* 添加空白间隔 */}
            <div className="h-8"></div>

            <div className="flex items-center justify-end gap-2">
              <Button variant="outline" size="sm" className="h-7 text-xs flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                全部重试
              </Button>
              <Button variant="ghost" size="sm" className="h-7 text-xs">
                查看详情
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">归档完成率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold">92%</div>
                <p className="text-xs text-muted-foreground">本月完成率</p>
              </div>
              <div className="text-green-600 flex items-center text-sm">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                3%
              </div>
            </div>

            {/* 添加空白间隔 - 增加高度 */}
            <div className="h-10"></div>

            <div className="w-full h-2 bg-gray-200 rounded-full">
              <div className="h-full bg-green-500 rounded-full" style={{ width: "92%" }}></div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速访问卡片 */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="bg-blue-50 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-1">
            <Upload className="h-5 w-5" />
            <h3 className="text-xl font-bold">上传PDF文件</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">上传新的PDF文件进行归档处理</p>

          <div className="flex justify-between items-end">
            <div className="text-sm">
              <p>今日上传: 45个文件</p>
              <p className="mt-1">总大小: 256.8 MB</p>
            </div>
            <Button asChild className="bg-black hover:bg-gray-800 text-white">
              <Link href="/archive/upload" className="flex items-center gap-1">
                立即上传
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-1">
            <Database className="h-5 w-5" />
            <h3 className="text-xl font-bold">PDF导入台账</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">查看和管理PDF导入记录及处理状态</p>

          <div className="flex justify-between items-end">
            <div className="text-sm">
              <p>待处理: 18个任务</p>
              <p className="mt-1">已完成: 1,284个任务</p>
            </div>
            <Button asChild className="bg-black hover:bg-gray-800 text-white">
              <Link href="/archive/import-ledger" className="flex items-center gap-1">
                查看台账
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* 最近活动和系统状态 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>最近归档活动</CardTitle>
            <CardDescription>显示最近的归档操作和状态变更</CardDescription>
          </CardHeader>
          <CardContent>
            <RecentArchiveActivities />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>系统状态</CardTitle>
            <CardDescription>归档系统运行状态</CardDescription>
          </CardHeader>
          <CardContent>
            <SystemStatus />
          </CardContent>
        </Card>
      </div>

      {/* 归档统计 */}
      <Card>
        <CardHeader>
          <CardTitle>归档统计</CardTitle>
          <CardDescription>多维度查看归档数据统计</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-6">
            <Tabs value={statisticsTab} onValueChange={setStatisticsTab}>
              <TabsList>
                <TabsTrigger value="by-archive" className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  按档案
                </TabsTrigger>
                <TabsTrigger value="by-box" className="flex items-center gap-1">
                  <Package className="h-4 w-4" />
                  按档案盒
                </TabsTrigger>
                <TabsTrigger value="by-user" className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  按用户
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <Tabs value={statisticsTimeTab} onValueChange={(value) => {
              if (value === "day" || value === "week" || value === "month") {
                setStatisticsTimeTab(value)
              }
            }}>
              <TabsList>
                <TabsTrigger value="day" className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />日
                </TabsTrigger>
                <TabsTrigger value="week" className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />周
                </TabsTrigger>
                <TabsTrigger value="month" className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />月
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* 统计卡片 - 按档案盒样式 */}
          {statisticsTab === "by-box" && (
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">今日档案盒数</p>
                      <p className="text-3xl font-bold mt-1">15</p>
                    </div>
                    <Package className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">平均文件数/盒</p>
                      <p className="text-3xl font-bold mt-1">28.4</p>
                    </div>
                    <BarChart3 className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">较昨日变化</p>
                      <p className="text-3xl font-bold text-green-500 mt-1 flex items-center">
                        +3
                        <ArrowUpRight className="h-5 w-5 ml-1" />
                      </p>
                    </div>
                    <TrendingUp className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">盒容量利用率</p>
                      <p className="text-3xl font-bold text-blue-500 mt-1">85.2%</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-500">85%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 统计卡片 - 按档案样式 */}
          {statisticsTab === "by-archive" && (
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">今日归档量</p>
                      <p className="text-3xl font-bold mt-1">42</p>
                    </div>
                    <FileText className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">平均大小</p>
                      <p className="text-3xl font-bold mt-1">2.4MB</p>
                    </div>
                    <BarChart3 className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">较昨日变化</p>
                      <p className="text-3xl font-bold text-green-500 mt-1 flex items-center">
                        +8
                        <ArrowUpRight className="h-5 w-5 ml-1" />
                      </p>
                    </div>
                    <TrendingUp className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">归档完成率</p>
                      <p className="text-3xl font-bold text-green-500 mt-1">94.6%</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-green-500">95%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 统计卡片 - 按用户样式 */}
          {statisticsTab === "by-user" && (
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">活跃用户数</p>
                      <p className="text-3xl font-bold mt-1">24</p>
                    </div>
                    <Users className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">人均归档量</p>
                      <p className="text-3xl font-bold mt-1">18.5</p>
                    </div>
                    <BarChart3 className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">较昨日变化</p>
                      <p className="text-3xl font-bold text-green-500 mt-1 flex items-center">
                        +2
                        <ArrowUpRight className="h-5 w-5 ml-1" />
                      </p>
                    </div>
                    <TrendingUp className="h-10 w-10 text-gray-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">完成任务率</p>
                      <p className="text-3xl font-bold text-blue-500 mt-1">86.3%</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-500">86%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <ArchiveStatistics type={statisticsTab as any} timeFrame={statisticsTimeTab} />
        </CardContent>
      </Card>

      {/* 归档日历 */}
      <Card>
        <CardHeader>
          <CardTitle>归档日历</CardTitle>
          <CardDescription>按日期查看归档情况</CardDescription>
        </CardHeader>
        <CardContent>
          <ArchiveCalendar />
        </CardContent>
      </Card>
    </div>
  )
}
