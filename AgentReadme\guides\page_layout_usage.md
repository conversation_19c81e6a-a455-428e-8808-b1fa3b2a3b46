# 通用页面布局组件使用指南

本指南介绍如何使用 `PageLayout`、`TablePageLayout` 和相关共享组件来确保整个应用的页面结构和样式统一。

## 📋 目录

- [通用页面布局组件使用指南](#通用页面布局组件使用指南)
  - [📋 目录](#-目录)
  - [组件概述](#组件概述)
  - [基本使用方法](#基本使用方法)
    - [PageLayout 基本使用](#pagelayout-基本使用)
    - [TablePageLayout 基本使用](#tablepagelayout-基本使用)
  - [属性详解](#属性详解)
    - [PageLayout 属性](#pagelayout-属性)
    - [TablePageLayout 属性](#tablepagelayout-属性)
    - [PageHeader 属性](#pageheader-属性)
    - [PageAction 属性](#pageaction-属性)
  - [高级特性](#高级特性)
    - [固定标签栏和状态卡片](#固定标签栏和状态卡片)
    - [布局模式](#布局模式)
      - [1. 基础布局（无标签）](#1-基础布局无标签)
      - [2. 带固定标签布局](#2-带固定标签布局)
      - [3. 带状态卡片的布局](#3-带状态卡片的布局)
      - [4. 完整布局（标题+状态卡片+标签）](#4-完整布局标题状态卡片标签)
      - [5. 自定义滚动布局](#5-自定义滚动布局)
      - [6. 表格布局](#6-表格布局)
    - [滚动行为规范](#滚动行为规范)
    - [间距规范](#间距规范)
  - [实际应用示例](#实际应用示例)
    - [仪表盘页面](#仪表盘页面)
    - [详情页面](#详情页面)
    - [表格页面](#表格页面)
    - [自定义头部](#自定义头部)
  - [页面迁移指南](#页面迁移指南)
    - [迁移步骤](#迁移步骤)
    - [迁移前后对比](#迁移前后对比)
    - [迁移示例：仪表盘页面](#迁移示例仪表盘页面)
      - [迁移前](#迁移前)
      - [迁移后](#迁移后)
    - [迁移示例：Excel导入详情页](#迁移示例excel导入详情页)
      - [迁移前-1](#迁移前-1)
      - [迁移后-1](#迁移后-1)
    - [关键迁移点](#关键迁移点)
    - [迁移后的好处](#迁移后的好处)
  - [最佳实践](#最佳实践)
  - [组件架构](#组件架构)
    - [共享组件和组合模式](#共享组件和组合模式)
    - [组件选择指南](#组件选择指南)

## 组件概述

本系统提供了三个关键的布局组件，用于构建统一风格的页面：

1. `PageLayout` - 通用页面布局组件，适用于大多数内容页面
2. `TablePageLayout` - 表格页面专用布局组件，优化了表格的滚动行为
3. `PageHeader` - 共享的页面头部组件，由前两者内部使用，实现UI一致性

这些组件共同负责处理的主要方面：

- 页面标题和副标题的展示
- 页面操作按钮的布局
- 内容区域的滚动行为
- 固定标签栏和状态卡片的展示（高级特性）

这些布局组件的设计旨在解决应用中复杂的页面布局和滚动挑战，并遵循一套统一的滚动解决方案架构。关于该滚动解决方案的背景、设计原理和更底层的实现细节，请参阅 [页面滚动解决方案指南](./layout_scrolling_solution.md)。

## 基本使用方法

### PageLayout 基本使用

```jsx
import { PageLayout } from "@/components/common/page-layout"
import { Card } from "@/components/ui/card"

export default function SomePage() {
  return (
    <PageLayout 
      title="页面标题" 
      subtitle="页面描述文本"
    >
      <Card>
        {/* 卡片内容 */}
      </Card>
      
      <Card>
        {/* 另一个卡片 */}
      </Card>
    </PageLayout>
  )
}
```

### TablePageLayout 基本使用

```jsx
import { TablePageLayout } from "@/components/common/table-page-layout"
import { RecordsFilter } from "@/components/records/records-filter"

export default function TablePage() {
  return (
    <TablePageLayout
      title="表格页面标题"
      subtitle="表格页面描述"
      filter={<RecordsFilter />}
    >
      <div className="h-full">
        {/* 表格组件 */}
        <Table />
      </div>
    </TablePageLayout>
  )
}
```

带操作按钮的基本使用：

```jsx
import { PageLayout } from "@/components/common/page-layout"
import { Plus, ArrowLeft } from "lucide-react"

export default function PageWithActions() {
  return (
    <PageLayout 
      title="页面标题" 
      subtitle="页面描述文本"
      actions={[
        {
          label: "返回",
          icon: <ArrowLeft className="h-4 w-4" />,
          href: "/back-link",
          variant: "outline"
        },
        {
          label: "新建",
          icon: <Plus className="h-4 w-4" />,
          onClick: () => console.log("新建按钮点击"),
          variant: "default"
        }
      ]}
    >
      {/* 页面内容 */}
    </PageLayout>
  )
}
```

## 属性详解

### PageLayout 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | string | 必填 | 页面标题 |
| `subtitle` | string | - | 页面副标题/描述 |
| `actions` | PageAction[] | - | 操作按钮数组 |
| `header` | ReactNode | - | 自定义头部内容，设置后将替代默认标题区域 |
| `children` | ReactNode | 必填 | 页面内容 |
| `contentGap` | string | "space-y-8" | 内容项之间的间距 |
| `disableScrollArea` | boolean | false | 是否禁用滚动区域组件，使用原生滚动 |
| `showHeaderBorder` | boolean | true | 是否显示头部底部边框 |
| `className` | string | - | 应用于内容区域的自定义类名 |
| `fixedTabs` | ReactNode | - | 在滚动时固定在顶部的标签栏 |
| `statusCards` | ReactNode | - | 在标题和标签栏之间添加固定显示的状态卡片 |
| `contentPadding` | string | - | 自定义内容区域的内边距 |
| `scrollConfig` | object | - | 高级滚动配置 |

### TablePageLayout 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | string | 必填 | 页面标题 |
| `subtitle` | string | - | 页面副标题/描述 |
| `actions` | PageAction[] | - | 操作按钮数组 |
| `filter` | ReactNode | - | 筛选器组件 |
| `children` | ReactNode | 必填 | 表格内容 |
| `contentClassName` | string | - | 应用于表格容器的自定义类名 |
| `topMargin` | string | - | 顶部间距 |
| `showHeaderBorder` | boolean | true | 是否显示头部底部边框 |
| `statusCards` | ReactNode | - | 状态卡片内容 |
| `fixedTabs` | ReactNode | - | 固定标签栏内容 |

### PageHeader 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | string | 必填 | 页面标题 |
| `subtitle` | string | - | 页面副标题/描述 |
| `actions` | PageAction[] | - | 操作按钮数组 |
| `className` | string | - | 自定义类名 |
| `showHeaderBorder` | boolean | true | 是否显示头部底部边框 |
| `statusCards` | ReactNode | - | 标题下方的额外内容 |
| `fixedTabs` | ReactNode | - | 固定标签栏内容 |

### PageAction 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `label` | string | 必填 | 按钮文本 |
| `icon` | ReactNode | - | 按钮图标 |
| `onClick` | function | - | 点击事件处理函数 |
| `href` | string | - | 链接地址，设置后按钮变为链接 |
| `variant` | string | "outline" | 按钮样式变体 |

## 高级特性

### 固定标签栏和状态卡片

PageLayout 和 TablePageLayout 都支持以下高级功能：

1. **固定标签栏**：通过 `fixedTabs` 属性在滚动时保持标签栏固定在顶部
2. **状态卡片区域**：通过 `statusCards` 属性在标题和标签栏之间显示状态信息
3. **自定义内容内边距**：通过 `contentPadding` 属性控制内容区域的内边距

```jsx
// 带固定标签和状态卡片的示例
import { PageLayout } from "@/components/common/page-layout"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function DetailPage() {
  // 定义固定标签栏
  const fixedTabs = (
    <Tabs defaultValue="tab1">
      <TabsList>
        <TabsTrigger value="tab1">标签1</TabsTrigger>
        <TabsTrigger value="tab2">标签2</TabsTrigger>
      </TabsList>
    </Tabs>
  )
  
  // 定义状态卡片
  const statusCards = (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-3">
      <StatusCard title="状态1" value="值1" />
      <StatusCard title="状态2" value="值2" />
      <StatusCard title="状态3" value="值3" />
    </div>
  )

  return (
    <PageLayout
      title="详情页面"
      subtitle="详细数据查看与操作"
      actions={[
        {
          label: "操作按钮",
          icon: <SomeIcon />,
          onClick: () => {}
        }
      ]}
      fixedTabs={fixedTabs}
      statusCards={statusCards}
    >
      <Tabs defaultValue="tab1">
        {/* 隐藏TabsList，因为已经在fixedTabs中定义 */}
        <div className="hidden">
          <TabsList>
            <TabsTrigger value="tab1">标签1</TabsTrigger>
            <TabsTrigger value="tab2">标签2</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="tab1" className="space-y-6 pt-2">
          {/* 标签1内容 */}
        </TabsContent>
        <TabsContent value="tab2" className="space-y-6 pt-2">
          {/* 标签2内容 */}
        </TabsContent>
      </Tabs>
    </PageLayout>
  )
}
```

### 布局模式

根据不同的页面需求，布局组件支持多种布局模式：

#### 1. 基础布局（无标签）

适用于简单内容页面，如仪表盘、列表页等。

```jsx
<PageLayout
  title="页面标题"
  subtitle="页面描述"
>
  <div className="space-y-6">
    {/* 内容 */}
  </div>
</PageLayout>
```

#### 2. 带固定标签布局

适用于需要多个视图切换的详情页面。

```jsx
<PageLayout
  title="页面标题"
  subtitle="页面描述"
  fixedTabs={<Tabs>...</Tabs>}
>
  <Tabs>
    {/* 隐藏TabsList */}
    <div className="hidden"><TabsList>...</TabsList></div>
    <TabsContent>...</TabsContent>
  </Tabs>
</PageLayout>
```

#### 3. 带状态卡片的布局

适用于需要显示重要状态信息的详情页面。

```jsx
<PageLayout
  title="页面标题"
  subtitle="页面描述"
  statusCards={<StatusCards />}
>
  <div className="space-y-6">
    {/* 内容 */}
  </div>
</PageLayout>
```

#### 4. 完整布局（标题+状态卡片+标签）

适用于复杂详情页面，如报告详情、档案详情等。

```jsx
<PageLayout
  title="页面标题"
  subtitle="页面描述"
  statusCards={<StatusCards />}
  fixedTabs={<Tabs>...</Tabs>}
>
  <Tabs>
    {/* 隐藏TabsList */}
    <div className="hidden"><TabsList>...</TabsList></div>
    <TabsContent>...</TabsContent>
  </Tabs>
</PageLayout>
```

#### 5. 自定义滚动布局

适用于需要完全自定义滚动行为的页面。

```jsx
<PageLayout
  title="页面标题"
  subtitle="页面描述"
  disableScrollArea={true}
>
  <div className="flex flex-col h-full">
    <div className="flex-none">
      {/* 固定内容 */}
    </div>
    <ScrollArea className="flex-1">
      <div className="space-y-6">
        {/* 可滚动内容 */}
      </div>
    </ScrollArea>
  </div>
</PageLayout>
```

#### 6. 表格布局

使用专门的TablePageLayout组件适用于数据表格页面，如数据列表、台账等。

```jsx
<TablePageLayout
  title="表格页面"
  subtitle="数据表格页面"
  filter={<FilterComponent />}
>
  <div className="h-full ag-theme-quartz">
    <AgGridReact {...gridProps} />
  </div>
</TablePageLayout>
```

### 滚动行为规范

1. **PageLayout默认滚动行为**：仅内容区域滚动，标题、状态卡片和标签栏保持固定
2. **TablePageLayout滚动行为**：固定标题和筛选器，表格内容由表格组件自行管理滚动
3. **自定义滚动行为**：通过 `disableScrollArea={true}` 禁用默认滚动，然后自行实现滚动逻辑

### 间距规范

1. **组件间距**：保持 `space-y-6` 或 `space-y-8` 的一致间距
2. **内容内边距**：默认右侧内边距为 `pr-3`，可通过 `contentPadding` 属性自定义
3. **固定区域间距**：
   - 标题下方间距：无状态卡片或标签时为 `mb-3`
   - 状态卡片内边距：`py-3`
   - 标签栏底部边距：`mb-3`
   - 标签内容顶部内边距：`pt-2`

## 实际应用示例

### 仪表盘页面

```jsx
import { PageLayout } from "@/components/common/page-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function Dashboard() {
  return (
    <PageLayout
      title="系统仪表盘"
      subtitle="档案管理系统概览和关键指标"
    >
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none shadow-md">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-blue-800">系统状态</CardTitle>
        </CardHeader>
        <CardContent>
          {/* 系统状态内容 */}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 统计卡片 */}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 活动和图表 */}
      </div>
    </PageLayout>
  )
}
```

### 详情页面

```jsx
import { PageLayout } from "@/components/common/page-layout"
import { Card } from "@/components/ui/card"
import { ArrowLeft, Download } from "lucide-react"

export default function DetailPage({ id }) {
  return (
    <PageLayout
      title="详情页面"
      subtitle="查看详细信息和相关数据"
      actions={[
        {
          label: "返回列表",
          icon: <ArrowLeft className="h-4 w-4" />,
          href: "/list-page",
          variant: "outline"
        },
        {
          label: "下载",
          icon: <Download className="h-4 w-4" />,
          onClick: () => console.log("下载数据"),
          variant: "outline"
        }
      ]}
    >
      <Card>
        {/* 详情内容 */}
      </Card>
      
      <Card>
        {/* 相关数据表格 */}
      </Card>
    </PageLayout>
  )
}
```

### 表格页面

```jsx
import { TablePageLayout } from "@/components/common/table-page-layout"
import { Upload } from "lucide-react"
import { RecordsFilter } from "@/components/records/records-filter"

export default function TablePage() {
  const actions = [
    {
      label: "导入数据",
      icon: <Upload className="h-4 w-4" />,
      href: "/import",
      variant: "default"
    }
  ];

  return (
    <TablePageLayout
      title="台账管理"
      subtitle="管理和查询档案记录"
      actions={actions}
      filter={<RecordsFilter />}
    >
      <div className="h-full ag-theme-quartz">
        <AgGridReact
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowModelType="serverSide"
          serverSideDatasource={datasource}
          pagination={true}
          paginationPageSize={20}
          cacheBlockSize={20}
          onGridReady={onGridReady}
        />
      </div>
    </TablePageLayout>
  )
}
```

### 自定义头部

```jsx
import { PageLayout } from "@/components/common/page-layout"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function PageWithCustomHeader() {
  return (
    <PageLayout
      title="不会显示这个标题"
      header={
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">自定义头部</h1>
          <Tabs defaultValue="tab1">
            <TabsList>
              <TabsTrigger value="tab1">标签一</TabsTrigger>
              <TabsTrigger value="tab2">标签二</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      }
    >
      {/* 页面内容 */}
    </PageLayout>
  )
}
```

## 页面迁移指南

### 迁移步骤

将现有页面迁移到布局组件时，需要注意以下几点:

1. 移除现有的 `flex flex-col h-full` 容器
2. 移除现有的标题区域和滚动区域
3. 根据需要设置 `contentGap` 属性，确保内容间距一致
4. 保持内部内容结构不变
5. 对于表格页面，考虑使用 `TablePageLayout` 代替 `PageLayout`

### 迁移前后对比

**迁移前**:

```jsx
export default function SomePage() {
  return (
    <div className="flex flex-col h-full">
      <div className="flex-none bg-background border-b mb-6">
        <PageTitle title="页面标题" subtitle="页面描述" />
      </div>

      <div className="flex-1 overflow-auto space-y-8">
        {/* 页面内容 */}
        <Card>...</Card>
        <div className="grid ...">...</div>
      </div>
    </div>
  )
}
```

**迁移后**:

```jsx
import { PageLayout } from "@/components/common/page-layout"

export default function SomePage() {
  return (
    <PageLayout
      title="页面标题"
      subtitle="页面描述"
    >
      {/* 页面内容 */}
      <Card>...</Card>
      <div className="grid ...">...</div>
    </PageLayout>
  )
}
```

### 迁移示例：仪表盘页面

#### 迁移前

```jsx
// dashboard/page.tsx
export default function Dashboard() {
  const { hasPermission } = useAuth()
  
  // ... 数据和状态处理代码 ...

  return (
    <div className="flex flex-col h-full">
      <div className="flex-none bg-background border-b mb-3">
        <PageTitle title="系统仪表盘" subtitle="档案管理系统概览和关键指标" />
      </div>

      <ScrollArea className="flex-1">
        <div className="space-y-8 pr-3">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none shadow-md">
            {/* 系统状态卡片内容 */}
          </Card>

          <Card>
            {/* 告警信息卡片内容 */}
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* 统计卡片 */}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 最近活动和趋势图表 */}
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
```

#### 迁移后

```jsx
// dashboard/page.tsx
import { PageLayout } from "@/components/common/page-layout"

export default function Dashboard() {
  const { hasPermission } = useAuth()
  
  // ... 数据和状态处理代码 ...

  return (
    <PageLayout
      title="系统仪表盘"
      subtitle="档案管理系统概览和关键指标"
    >
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none shadow-md">
        {/* 系统状态卡片内容 */}
      </Card>

      <Card>
        {/* 告警信息卡片内容 */}
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 统计卡片 */}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 最近活动和趋势图表 */}
      </div>
    </PageLayout>
  )
}
```

### 迁移示例：Excel导入详情页

#### 迁移前-1

```jsx
// records/import-history/[id]/page.tsx
export default function ImportHistoryDetailPage({ params }) {
  const resolvedParams = React.use(params)
  const id = resolvedParams.id
  
  // ... 数据和状态处理代码 ...

  return (
    <div className="flex flex-col h-full">
      <div className="flex-none bg-background z-10 pt-4 px-4 md:pt-6 md:px-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Excel导入详情</h1>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/records/import-history">
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回列表
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/records/import">
                导入新文件
              </Link>
            </Button>
          </div>
        </div>
        <p className="text-muted-foreground mb-4">
          查看Excel文件导入的详细处理结果
        </p>
      </div>
      
      <ScrollArea className="flex-1">
        <div className="px-4 md:px-6 pb-4 md:pb-6 pr-3 space-y-6">
          {isLoading ? (
            renderLoadingState()
          ) : error ? (
            renderErrorState()
          ) : !batchDetails ? (
            <div className="text-center py-12">无法找到导入批次</div>
          ) : (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>批次信息</CardTitle>
                </CardHeader>
                <CardContent>{/* 批次信息内容 */}</CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>处理结果统计</CardTitle>
                </CardHeader>
                <CardContent>{/* 统计内容 */}</CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>导入记录明细</CardTitle>
                </CardHeader>
                <CardContent>{/* 明细内容 */}</CardContent>
              </Card>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
```

#### 迁移后-1

```jsx
// records/import-history/[id]/page.tsx
import { PageLayout } from "@/components/common/page-layout"

export default function ImportHistoryDetailPage({ params }) {
  const resolvedParams = React.use(params)
  const id = resolvedParams.id
  
  // ... 数据和状态处理代码 ...

  // 定义操作按钮
  const actions = [
    {
      label: "返回列表",
      icon: <ArrowLeft className="h-4 w-4" />,
      href: "/records/import-history",
      variant: "outline"
    },
    {
      label: "导入新文件",
      href: "/records/import",
      variant: "outline"
    }
  ]

  // 处理加载、错误和空数据状态
  if (isLoading) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        {renderLoadingState()}
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        {renderErrorState()}
      </PageLayout>
    )
  }

  if (!batchDetails) {
    return (
      <PageLayout
        title="Excel导入详情"
        subtitle="查看Excel文件导入的详细处理结果"
        actions={actions}
      >
        <div className="text-center py-12">无法找到导入批次</div>
      </PageLayout>
    )
  }

  // 主内容渲染
  return (
    <PageLayout
      title="Excel导入详情"
      subtitle="查看Excel文件导入的详细处理结果"
      actions={actions}
      contentGap="space-y-6" // 注意这里使用space-y-6保持原有间距
    >
      <Card>
        <CardHeader>
          <CardTitle>批次信息</CardTitle>
        </CardHeader>
        <CardContent>{/* 批次信息内容 */}</CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>处理结果统计</CardTitle>
        </CardHeader>
        <CardContent>{/* 统计内容 */}</CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>导入记录明细</CardTitle>
        </CardHeader>
        <CardContent>{/* 明细内容 */}</CardContent>
      </Card>
    </PageLayout>
  )
}
```

### 关键迁移点

1. **标题区域统一**：使用布局组件的 `title` 和 `subtitle` 属性，确保所有页面的标题区域一致
2. **操作按钮统一**：使用 `actions` 属性，将页面右上角的操作按钮统一处理
3. **滚动行为统一**：布局组件内部已包含适当的滚动管理，无需再手动添加
4. **间距统一**：通过 `contentGap` 属性控制内容间距，确保各页面风格一致
5. **边距统一**：内容区域统一使用 `pr-3` 的右侧边距，确保滚动条不会紧贴内容

### 迁移后的好处

1. **代码简化**：页面布局代码更简洁，只需关注内容部分
2. **UI一致性**：所有页面的布局、滚动行为和间距保持一致
3. **维护性提升**：布局变更时只需修改相应的布局组件，而不是每个页面
4. **新页面开发加速**：使用统一组件快速搭建新页面，减少重复代码

## 最佳实践

1. **选择合适的布局组件**：
   - 一般内容页面使用 `PageLayout`
   - 表格数据页面使用 `TablePageLayout`
   - 特殊布局需求可使用自定义头部

2. **统一使用** - 所有页面都应使用布局组件
3. **内容间距** - 大多数页面应使用默认的 `space-y-8` 间距，详情或表单页面可考虑使用较紧凑的 `space-y-6`
4. **内容结构** - 保持卡片和网格布局的一致性
5. **按钮排序** - 返回或取消按钮通常放在左侧，主要操作按钮放在右侧
6. **边框样式** - 除特殊情况外，保持标题区域的底部边框
7. **滚动行为** - 除需要特殊滚动效果的页面外，应使用默认的滚动组件
8. **分离固定标签与内容标签** - 固定标签通过 `fixedTabs` 提供，内容中的 `TabsList` 需要隐藏
9. **使用语义化标签** - 为标签提供清晰的标识和描述
10. **适当使用状态卡片** - 只在必要时显示状态卡片，避免占用过多空间
11. **内容自适应** - 确保内容在不同屏幕尺寸下都能良好显示

## 组件架构

### 共享组件和组合模式

本系统采用组件组合模式实现UI一致性：

```c
PageHeader (共享组件)
   ↑
   |
   ├── PageLayout (通用页面布局)
   |
   └── TablePageLayout (表格专用布局)
```

- **PageHeader**：包含标题、操作按钮等共享UI元素，被其他布局组件使用
- **PageLayout**：通用内容页面布局，包含ScrollArea实现统一滚动
- **TablePageLayout**：表格页面专用布局，针对数据表格优化滚动行为

这种组合模式的优势：

- 减少代码重复，同一UI元素在单一地方实现
- 维护简化，UI变更只需修改一处
- 保持界面一致性

这些组件的设计和实现紧密关联于项目整体的滚动解决方案。若需深入理解其设计背景和架构决策，请参考 [页面滚动解决方案指南](./layout_scrolling_solution.md)。

### 组件选择指南

| 场景 | 推荐组件 | 原因 |
|------|-----------|------|
| 仪表盘 | PageLayout | 内容丰富，需要滚动区域 |
| 数据表格 | TablePageLayout | 优化表格滚动行为 |
| 详情页面 | PageLayout + statusCards | 方便展示状态信息 |
| 多标签页面 | PageLayout + fixedTabs | 标签栏固定，内容滚动 |
| 特殊布局 | PageLayout + header prop | 完全自定义头部内容 |
