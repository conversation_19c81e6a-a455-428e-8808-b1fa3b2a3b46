# -*- coding: utf-8 -*-
"""
总台账字段映射配置

将 snake_case 字段名映射到中文显示名称。
此模块为 frontend/config/master-ledger-field-mappings.ts 的后端等效实现。
"""

# Django模型字段为snake_case, DRF camelcase插件会自动转换输出为camelCase
MASTER_LEDGER_FIELD_MAPPINGS = {
    # 基础标识信息
    "sample_number": "样品编号",
    "commission_number": "委托编号",
    "unified_number": "统一编号",
    "report_number": "报告编号",
    "province_unified_number": "省统一编号",
    "station_code": "站点编号",
    "organization_code": "机构代号",
    "account_from_excel": "账号",

    # 项目与委托信息
    "project_name": "工程名称",
    "client_unit": "委托单位",
    "project_number": "工程编号",
    "sub_project": "分项工程",
    "project_location": "工程部位",
    "project_address": "工程地址",
    "client_name": "委托人",
    "commission_datetime": "委托日期",

    # 试验与结果信息
    "test_result": "检测结果",
    "conclusion": "结论",
    "test_parameters": "检测参数",
    "unqualified_parameters": "不合格参数",
    "test_start_datetime": "测试开始日期",
    "test_end_datetime": "测试结束日期",
    "test_person1": "试验人1",
    "test_person2": "试验人2",
    "data_entry_person": "数据录入人",

    # 档案生命周期信息
    "archive_status": "归档状态",
    "archive_box_number": "档案盒号",
    "current_status": "当前数据状态",
    "processing_status": "待处理状态",
    "change_count": "更改次数",
    "archive_url": "档案URL链接",
    "attachments_from_excel": "附件",
    "storage_datetime": "入库日期",
    "storage_person": "入库人",
    "outbound_datetime": "出库日期",
    "outbound_person": "出库人",
    "archive_datetime": "归档日期",
    "archive_person": "归档人",

    # 报告管理信息
    "report_issue_status": "报告发放状态",
    "first_issue_copies": "第一次发放份数",
    "first_issue_datetime": "第一次发放日期",
    "first_issue_person": "第一次发放人",
    "first_receiver_name": "第一次领取人",
    "first_receiver_unit": "第一次领取单位",
    "first_receiver_phone": "第一次领取人电话",
    "second_issue_copies": "第二次发放份数",
    "second_issue_datetime": "第二次发放日期",
    "second_issue_person": "第二次发放人",
    "second_receiver_name": "第二次领取人",
    "second_receiver_unit": "第二次领取单位",
    "second_receiver_phone": "第二次领取人电话",
    "total_issue_copies": "总发放份数",

    # 样品信息
    "group_number": "组号",
    "sample_name": "样品/项目名称",
    "assigned_person": "分配人",
    "component_count": "构件(桩)数",
    "test_point_count": "测点数",
    "unqualified_point_count": "不合格点数",
    "sample_retention_datetime": "样品留样时间",
    "sample_remaining_time": "样品剩余时间(天)",

    # 财务信息
    "payment_status": "收费状态",
    "price_adjustment_status": "价格调整状态",
    "standard_price": "标准价格费用",
    "discount_price": "折扣价格费用",
    "actual_price": "实际价格费用",

    # 系统元数据
    "import_user": "导入人",
    "import_date": "导入时间",
    "batch_number": "批次号",
    "source_system": "数据来源系统",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    
    # 伪字段或来自SerializerMethodField的字段
    "import_user_name": "导入人",
}

def get_field_display_name(field_name: str) -> str:
    """
    根据字段的 snake_case 名称获取其中文显示名称。
    如果未找到，则返回原始字段名。
    """
    return MASTER_LEDGER_FIELD_MAPPINGS.get(field_name, field_name) 