"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { ArchiveRecord, ReportIssueStatus } from "@/types/archive-record"
import apiClient from "@/lib/apiClient"

interface RecordDetailsProps {
  recordId: string
}

function getReportIssueStatusText(status?: ReportIssueStatus): string {
  if (!status) return "未知";
  switch (status) {
    case "not_issued": return "未发放";
    case "first_issued": return "已一次发放";
    case "second_issued": return "已二次发放";
    case "completed": return "发放完成";
    default: return status;
  }
}

export function RecordDetails({ recordId }: RecordDetailsProps) {
  const [record, setRecord] = useState<ArchiveRecord | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchRecord = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const response = await apiClient.get<ArchiveRecord>(`/api/archive-records/records/${recordId}/`)
        if (response.success && response.data) {
          setRecord(response.data)
        } else {
          setError(response.error || "未能加载记录详情。")
        }
      } catch (err) {
        setError("加载数据时发生网络错误。")
      } finally {
        setIsLoading(false)
      }
    };
    
    if (recordId) {
      fetchRecord();
    }
  }, [recordId])

  if (isLoading) {
    return <div className="flex justify-center items-center h-40">加载中...</div>
  }

  if (error) {
    return <div className="flex justify-center items-center h-40 text-red-500">{error}</div>
  }

  if (!record) {
    return <div className="flex justify-center items-center h-40">未找到记录</div>
  }

  const groups = [
    {
      title: "基本信息",
      fields: [
        { label: "统一编号", value: record.unifiedNumber },
        { label: "样品编号", value: record.sampleNumber },
        { label: "委托编号", value: record.commissionNumber },
        { label: "工程名称", value: record.projectName },
        { label: "委托单位", value: record.clientUnit },
        { label: "创建日期", value: record.createdAt },
      ],
    },
    {
      title: "归档信息",
      fields: [
        { label: "归档状态", value: record.archiveStatus },
        { label: "归档日期", value: record.archiveDatetime || "-" },
        { label: "档案盒号", value: record.archiveBoxNumber || "-" },
        { label: "变更次数", value: record.changeCount?.toString() },
      ],
    },
    {
      title: "发放信息",
      fields: [
        { label: "发放状态", value: getReportIssueStatusText(record.reportIssueStatus as ReportIssueStatus) },
        { label: "总发放份数", value: record.totalIssueCopies },
        { label: "一次发放日期", value: record.firstIssueDatetime || "-" },
        { label: "一次发放人", value: record.firstIssuePerson || "-" },
        { label: "二次发放日期", value: record.secondIssueDatetime || "-" },
        { label: "二次发放人", value: record.secondIssuePerson || "-" },
      ],
    },
    {
      title: "试验信息",
      fields: [
        { label: "试验员1", value: record.testPerson1 },
        { label: "试验员2", value: record.testPerson2 },
        { label: "试验开始日期", value: record.testStartDatetime },
        { label: "试验结束日期", value: record.testEndDatetime },
        { label: "试验结论", value: record.conclusion },
      ],
    },
  ]

  return (
    <div className="space-y-6">
      {groups.map((group) => (
        <Card key={group.title}>
          <CardHeader>
            <CardTitle>{group.title}</CardTitle>
            <CardDescription>档案记录的{group.title}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-2">
              {group.fields.map((field) => (
                <div key={field.label} className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">{field.label}</p>
                  <p className="text-sm">{field.value || "-"}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
