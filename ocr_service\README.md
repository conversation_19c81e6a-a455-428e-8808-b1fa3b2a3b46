# OCR 微服务

高性能的 OCR (光学字符识别) 微服务，基于 PaddleOCR 和 FastAPI 构建。

## 🎯 功能特性

### 核心功能
- **基础 OCR**: 单图像文本识别
- **增强 OCR**: 多变体图像处理，提高识别准确率
- **中英文支持**: 基于 PP-OCRv4 模型
- **高性能**: 异步处理，支持并发请求

### 现代化特性
- **健康检查**: `/health` 端点监控服务状态
- **性能指标**: `/metrics` 端点提供详细统计
- **结构化日志**: JSON 格式日志，便于分析
- **请求限流**: 防止服务过载
- **优雅关闭**: 安全的服务停止机制

### 可靠性保障
- **熔断器模式**: 自动故障恢复
- **重试机制**: 网络异常自动重试
- **超时控制**: 防止请求阻塞
- **错误处理**: 详细的错误分类和日志

## 🚀 快速开始

### 1. 使用 Docker Compose (推荐)

```bash
# 构建并启动服务
docker-compose up -d ocr-service

# 检查服务状态
curl http://localhost:8001/health
```

### 2. 本地开发

```bash
# 安装依赖
cd ocr_service
pip install -r requirements.txt

# 启动服务
python main.py
```

### 3. 自动化部署

```bash
# 使用部署脚本
chmod +x scripts/deploy_ocr_service.sh
./scripts/deploy_ocr_service.sh deploy
```

## 📖 API 文档

### 基础信息
- **服务地址**: `http://localhost:8001`
- **API 文档**: `http://localhost:8001/docs`
- **健康检查**: `http://localhost:8001/health`
- **性能指标**: `http://localhost:8001/metrics`

### 主要端点

#### 1. 基础 OCR
```bash
curl -X POST "http://localhost:8001/ocr" \
  -F "file=@image.png" \
  -F "mode=basic"
```

响应示例:
```json
{
  "success": true,
  "mode": "basic",
  "text": "识别出的文本内容",
  "processing_time": 1.23,
  "image_size": {"width": 800, "height": 600}
}
```

#### 2. 增强 OCR
```bash
curl -X POST "http://localhost:8001/ocr" \
  -F "file=@image.png" \
  -F "mode=enhanced" \
  -F "max_attempts=3"
```

响应示例:
```json
{
  "success": true,
  "mode": "enhanced",
  "text_results": [
    {
      "text": "第一次识别结果",
      "method": "PaddleOCR增强_1",
      "processing_time": 0.8
    },
    {
      "text": "第二次识别结果", 
      "method": "PaddleOCR增强_2",
      "processing_time": 0.9
    }
  ],
  "processing_time": 2.1
}
```

#### 3. 健康检查
```bash
curl http://localhost:8001/health
```

响应示例:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-03 10:30:00",
  "version": "1.0.0",
  "uptime": 3600.5,
  "ocr_engine_ready": true,
  "memory_usage": {
    "rss": **********,
    "percent": 12.5
  },
  "active_requests": 2
}
```

## ⚙️ 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `OCR_DEBUG` | `false` | 调试模式 |
| `OCR_LOG_LEVEL` | `INFO` | 日志级别 |
| `OCR_LOG_FORMAT` | `json` | 日志格式 |
| `OCR_MAX_CONCURRENT_REQUESTS` | `10` | 最大并发请求数 |
| `OCR_REQUEST_TIMEOUT` | `30.0` | 请求超时时间(秒) |
| `OCR_MAX_IMAGE_SIZE` | `10485760` | 最大图像大小(字节) |
| `OCR_USE_GPU` | `false` | 是否使用GPU |
| `OCR_ENABLE_METRICS` | `true` | 启用指标收集 |

### Docker Compose 配置示例

```yaml
ocr-service:
  build: ./ocr_service
  environment:
    - OCR_DEBUG=false
    - OCR_LOG_LEVEL=INFO
    - OCR_MAX_CONCURRENT_REQUESTS=10
    - OCR_USE_GPU=false
  ports:
    - "8001:8001"
  deploy:
    resources:
      limits:
        memory: 2G
      reservations:
        memory: 1G
```

## 🔧 开发指南

### 项目结构
```
ocr_service/
├── main.py              # FastAPI 应用入口
├── config.py            # 配置管理
├── models.py            # 数据模型
├── ocr_engine.py        # OCR 引擎管理
├── image_processor.py   # 图像处理
├── metrics.py           # 指标收集
├── middleware.py        # 中间件
├── logging_config.py    # 日志配置
├── requirements.txt     # 依赖列表
├── Dockerfile          # 容器构建
└── README.md           # 文档
```

### 添加新功能

1. **扩展 API 端点**:
   ```python
   @app.post("/new-endpoint")
   async def new_endpoint():
       # 实现逻辑
       pass
   ```

2. **添加中间件**:
   ```python
   app.add_middleware(CustomMiddleware)
   ```

3. **扩展配置**:
   ```python
   # config.py
   new_setting: str = Field(default="value", description="新配置")
   ```

## 🧪 测试

### 运行测试
```bash
# OCR 服务测试
python ocr_service/test_ocr_service.py

# 集成测试
python test_ocr_migration.py

# 使用部署脚本测试
./scripts/deploy_ocr_service.sh test
```

### 性能测试
```bash
# 并发测试
ab -n 100 -c 10 -T 'multipart/form-data' \
   -p test_image.png http://localhost:8001/ocr
```

## 📊 监控和运维

### 日志查看
```bash
# 查看服务日志
docker-compose logs -f ocr-service

# 查看错误日志
docker-compose logs ocr-service | grep ERROR
```

### 性能监控
```bash
# 获取指标
curl http://localhost:8001/metrics

# 监控资源使用
docker stats archive-flow-ocr
```

### 故障排查

1. **服务无法启动**:
   - 检查端口占用: `netstat -tlnp | grep 8001`
   - 查看容器日志: `docker logs archive-flow-ocr`

2. **OCR 识别失败**:
   - 检查图像格式和大小
   - 查看服务日志中的错误信息

3. **性能问题**:
   - 检查内存使用情况
   - 调整并发请求数限制

## 🔄 版本更新

### 更新服务
```bash
# 重新构建镜像
docker-compose build ocr-service

# 重启服务
docker-compose restart ocr-service
```

### 回滚版本
```bash
# 使用特定版本
docker-compose down
docker-compose up -d
```

## 📝 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
