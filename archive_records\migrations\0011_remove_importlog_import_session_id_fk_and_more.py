# Generated by Django 5.1.8 on 2025-06-04 17:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0010_importlog_completed_at_importlog_created_at_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='importlog',
            name='import_session_id_fk',
        ),
        migrations.AlterField(
            model_name='importlog',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='创建此导入任务的用户,从关联的ImportSession获取', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
    ]
