# Excel导入会话管理与多用户协作设计文档

## 1. 需求概述

### 1.1 背景

系统需要实现Excel文件导入功能，支持对导入数据进行冲突检测和处理。由于导入操作涉及大量数据处理和系统资源，需要实现会话管理机制，确保系统稳定性和数据一致性。同时，考虑到多用户协作场景，需要设计合理的状态管理和权限控制机制。

### 1.2 核心需求

- 实现全局唯一的导入会话机制，任意时刻系统中只允许一个活跃的导入会话
- 支持多用户协作处理同一导入会话
- 会话完整生命周期管理，包括创建、分析、处理、完成和清理
- 完善的异常处理和会话恢复机制
- 详细的操作日志和用户行为跟踪

## 2. 流程设计

### 2.1 导入会话状态流程

导入会话的完整状态流转如下：

1. **选择文件状态** (SELECT)
   - 用户选择Excel文件准备上传
   - 系统检查是否存在活跃会话

2. **上传状态** (UPLOAD)
   - 用户上传Excel文件
   - 后端分配唯一会话ID
   - 文件保存到临时存储位置

3. **分析开始状态** (ANALYSIS_START)
   - 后端开始分析Excel数据
   - 创建导入会话记录

4. **分析进行中状态** (ANALYSIS_IN_PROGRESS)
   - 后端分析Excel内容与现有数据的冲突
   - 前端可获取分析进度

5. **分析完成状态** (ANALYSIS_COMPLETE)
   - 分析结果包含冲突记录和统计信息
   - 用户可查看分析结果

6. **冲突处理状态** (CONFLICT_RESOLUTION)
   - 用户查看并处理数据冲突
   - 选择如何处理每条冲突记录

7. **导入开始状态** (IMPORT_START)
   - 用户确认处理方案
   - 系统准备执行导入操作

8. **导入进行中状态** (IMPORT_IN_PROGRESS)
   - 系统根据处理方案执行导入
   - 记录导入过程中的问题

9. **导入完成状态** (IMPORT_COMPLETE)
   - 导入完成，展示导入结果
   - 会话标记为已完成
   - 系统清理临时资源

### 2.2 异常处理流程

1. **强制终止分析**
   - 用户可在分析过程中随时取消
   - 系统停止分析，清理临时文件和会话信息
   - 会话状态标记为已取消

2. **分析后取消**
   - 用户可在分析完成后取消会话
   - 系统清理会话相关资源
   - 会话状态标记为已取消

3. **处理异常恢复**
   - 如果用户处理过程中发生异常（如浏览器崩溃）
   - 其他有权限用户可重新接管处理
   - 系统记录处理用户变更

## 3. 多用户协作机制

### 3.1 权限控制

- 具有相同权限级别的用户可以访问同一导入会话
- 所有有权限的用户都可以查看导入进度
- 任何有权限的用户都可以终止分析或取消会话
- 会话处理锁定机制确保同时只有一个用户可以处理数据冲突

### 3.2 会话接管机制

- 用户A开始处理会话后，系统记录当前处理用户
- 用户B访问导入页面时，系统检测到用户A正在处理
- 如果用户A处理过程中异常退出或会话超时
- 用户B可重新接管处理，系统更新处理用户为用户B

### 3.3 操作记录

- 每个关键操作都记录执行用户信息
- 导入会话的创建者、处理者和确认者分别记录
- 操作日志包含时间戳、用户ID、操作类型和结果

## 4. 状态管理设计

### 4.1 状态管理核心设计

建议将状态管理逻辑从UI组件中分离出来，采用专门的状态管理服务，并以服务器为状态唯一真相来源：

```typescript
// services/import-session-state.ts
import { create } from 'zustand' // 或其他状态管理库

export enum ImportSessionStep {
  SELECT = 'select',
  UPLOAD = 'upload',
  ANALYSIS_START = 'analysis_start',
  ANALYSIS_IN_PROGRESS = 'analysis_in_progress',
  ANALYSIS_COMPLETE = 'analysis_complete',
  CONFLICT_RESOLUTION = 'conflict_resolution',
  IMPORT_START = 'import_start',
  IMPORT_IN_PROGRESS = 'import_in_progress',
  IMPORT_COMPLETE = 'import_complete'
}

interface ImportSessionState {
  currentStep: ImportSessionStep;
  sessionId: string | null;
  userId: number | null; // 当前操作用户
  processingUserId: number | null; // 当前处理用户
  progress: {
    analyzed: number;
    total: number;
    percentage: number;
  };
  lastServerSync: Date; // 最后一次与服务器同步的时间
  
  // 操作方法
  fetchCurrentState: () => Promise<void>; // 从服务器获取当前状态
  startUpload: (file: File) => Promise<void>;
  beginAnalysis: () => Promise<void>;
  cancelAnalysis: () => Promise<void>;
  startProcessing: () => Promise<void>;
  confirmImport: (resolutions: any[]) => Promise<void>;
  // 其他方法...
}
```

### 4.2 完全状态驱动的UI设计

采用"服务器为唯一真相来源"的原则，实现完全状态驱动的UI：

- **页面加载即获取状态**：访问导入页面时立即从服务器获取当前全局会话状态
- **基于服务器状态渲染UI**：UI完全由服务器返回的最新状态决定，不依赖客户端存储的UI状态
- **避免状态假设**：前端不假设当前处于什么状态，而是始终查询后端获取真实状态
- **状态变更通知**：所有状态变更通过轮询获取，确保多用户场景下UI状态一致性
- **明确的会话控制权**：明确显示当前谁在处理会话，以及处理开始的时间

### 4.3 状态同步与持久化

```typescript
// 导入页面初始化 - 立即获取当前系统状态
useEffect(() => {
  const initializeImportPage = async () => {
    setIsLoading(true);
    try {
      // 获取当前系统活跃会话状态
      const activeSessionResponse = await excelImportService.getActiveImportSession();
      
      if (activeSessionResponse.has_active_session) {
        // 存在活跃会话，根据会话状态决定UI展示
        const sessionInfo = activeSessionResponse.session_info;
        
        // 根据会话状态跳转到对应UI阶段
        switch (sessionInfo.status) {
          case 'analyzing':
            setState({ ...state, currentStep: ImportSessionStep.ANALYSIS_IN_PROGRESS, sessionId: sessionInfo.id });
            startProgressPolling(sessionInfo.id);
            break;
          case 'analyzed':
            setState({ ...state, currentStep: ImportSessionStep.ANALYSIS_COMPLETE, sessionId: sessionInfo.id });
            loadAnalysisResults(sessionInfo.id);
            break;
          case 'processing':
            setState({ ...state, currentStep: ImportSessionStep.CONFLICT_RESOLUTION, sessionId: sessionInfo.id });
            loadConflictData(sessionInfo.id);
            break;
          case 'importing':
            setState({ ...state, currentStep: ImportSessionStep.IMPORT_IN_PROGRESS, sessionId: sessionInfo.id });
            startImportProgressPolling(sessionInfo.id);
            break;
          // 处理其他状态...
        }
        
        // 如果有其他用户正在处理，显示适当提示
        if (sessionInfo.processing_user_id && sessionInfo.processing_user_id !== currentUserId) {
          showProcessingUserNotification(sessionInfo.processing_user_name);
        }
      } else {
        // 无活跃会话，显示初始上传界面
        setState({ ...state, currentStep: ImportSessionStep.SELECT, sessionId: null });
      }
    } catch (error) {
      console.error('初始化导入页面失败:', error);
      showErrorNotification('获取导入状态失败，请刷新页面重试');
    } finally {
      setIsLoading(false);
    }
  };
  
  initializeImportPage();
}, []);

// 状态轮询 - 使用接口返回的完整状态更新UI
const startProgressPolling = (sessionId) => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await excelImportService.getAnalysisProgress(sessionId);
      
      // 根据返回的完整状态更新UI
      updateStateFromServerResponse(response);
      
      // 如果状态已变更（如分析完成），停止轮询并转换UI
      if (response.status !== 'analyzing') {
        clearInterval(pollInterval);
        handleStateTransition(response);
      }
    } catch (error) {
      console.error('获取进度失败:', error);
      // 轮询失败处理...
    }
  }, 2000); // 每2秒轮询一次
  
  // 保存interval id以便后续清理
  setState({ ...state, progressPollingId: pollInterval });
};

// 乐观更新 + 服务器确认模式
const handleUserAction = async (action, params) => {
  // 1. 乐观更新UI
  setOptimisticState(newExpectedState);
  
  try {
    // 2. 执行服务器操作
    const response = await excelImportService[action](params);
    
    // 3. 根据服务器响应更新真实状态
    updateStateFromServerResponse(response);
    
    // 4. 如需要，开始新的轮询
    if (needsPolling(response.status)) {
      startAppropriatePolling(response.session_id, response.status);
    }
  } catch (error) {
    // 5. 出错时回滚到服务器当前状态
    console.error(`执行${action}失败:`, error);
    const currentState = await excelImportService.getActiveImportSession();
    updateStateFromServerResponse(currentState);
    showErrorNotification(`操作失败: ${error.message}`);
  }
};
```

### 4.4 最小化会话引用存储

前端SessionStorage仅存储会话ID，目的是避免在多标签页或重新访问时创建多余会话：

```typescript
// 仅存储最小引用信息
const storeSessionReference = (sessionId) => {
  if (sessionId) {
    sessionStorage.setItem('importSessionId', sessionId);
  } else {
    sessionStorage.removeItem('importSessionId');
  }
};

// 页面加载时检查是否有会话引用
const checkExistingSession = () => {
  return sessionStorage.getItem('importSessionId');
};
```

## 5. 后端API设计

### 5.1 核心API

| API 端点 | 方法 | 描述 | 参数 | 返回值 |
|---------|------|------|------|--------|
| `/api/archive-records/analyze-excel/` | POST | 上传并分析Excel文件 | Excel文件 | 会话ID，会话信息 |
| `/api/archive-records/excel-import-progress/` | GET | 获取分析进度 | 会话ID | 进度百分比，状态 |
| `/api/archive-records/excel-analysis-result/` | GET | 获取分析结果 | 会话ID | 冲突记录，统计信息 |
| `/api/archive-records/confirm-import/` | POST | 确认导入 | 会话ID，冲突解决方案 | 导入结果 |
| `/api/archive-records/cancel-import/` | POST | 取消导入 | 会话ID | 取消结果 |
| `/api/archive-records/active-import-session/` | GET | 获取活跃会话 | 无 | 会话信息 |

### 5.2 会话锁定机制

后端使用缓存实现全局会话锁：

```python
# 通过缓存实现会话锁
LOCK_KEY = "excel_import_active_session"

# 设置活跃会话锁
def _set_active_session(session_id, user_id):
    lock_data = {
        'session_id': session_id,
        'user_id': user_id,
        'timestamp': timezone.now().isoformat()
    }
    cache.set(LOCK_KEY, lock_data, timeout=SESSION_EXPIRATION_MINUTES * 60)

# 释放活跃会话锁
def _release_active_session(session_id):
    lock_data = cache.get(LOCK_KEY)
    if lock_data and lock_data.get('session_id') == session_id:
        cache.delete(LOCK_KEY)
```

## 6. 实现建议

### 6.1 架构建议

1. **采用状态机模式**
   - 明确定义各状态及其转换条件
   - 每个状态对应特定的UI展现和可用操作

2. **前端状态管理**
   - 使用Zustand或Redux管理复杂状态
   - 将UI组件与状态逻辑解耦

3. **会话恢复机制**
   - 使用SessionStorage存储会话信息
   - 页面加载时自动检查并恢复会话

4. **错误处理**
   - 完善的错误捕获和恢复机制
   - 清晰的错误提示和建议操作

### 6.2 优化建议

1. **性能优化**
   - 分析大文件时采用分批处理
   - 进度轮询使用指数退避策略

2. **用户体验**
   - 提供清晰的进度指示
   - 会话过期自动提醒
   - 错误时提供详细说明和恢复建议

3. **安全考虑**
   - 会话操作权限严格控制
   - 敏感操作要求二次确认

## 7. 总结

本设计满足了用户提出的导入会话管理和多用户协作需求。通过实现全局唯一会话、多用户访问控制、完整状态流转和异常处理，确保了Excel导入功能的高可用性和数据一致性。

建议将状态管理逻辑从UI层分离，采用专门的状态管理服务，这将显著提高代码可维护性，并为未来功能扩展提供灵活性。对于多用户协作场景，明确的状态转换和权限控制确保了操作的安全性和可追踪性。
