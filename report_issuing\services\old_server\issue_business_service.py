# CHANGE: [2025-06-08] 创建发放业务服务层 - 可发放档案业务逻辑
from typing import Dict, List, Optional, Union
from django.utils.dateparse import parse_datetime
from datetime import datetime
from .archive_query_service import ArchiveQueryService
from report_issuing.models import IssueRecord
from archive_records.models import ArchiveRecord
import logging

logger = logging.getLogger(__name__)


class IssueBusinessService:
    """
    发放业务服务层
    
    统一管理发放相关的业务逻辑，专注业务规则验证和业务流程编排。
    通过数据服务层获取数据，不直接操作数据库。
    """
    
    def __init__(self):
        self.archive_query_service = ArchiveQueryService()
    
    def get_issueable_archives(
        self, 
        filters: Optional[Dict] = None,
        page: int = 1,
        page_size: int = 20,
        order_by: Optional[List[str]] = None
    ) -> Dict:
        """
        获取可发放的档案列表
        
        根据业务规则筛选出可以进行发放的档案条目：
        - 总发放份数 > 第一次发放份数 + 第二次发放份数
        - 应用前端查询条件
        - 支持分页和排序
        
        Args:
            filters: 查询条件 {
                'unified_number': str,           # 统一编号
                'sample_number': str,            # 样品编号  
                'client_unit': str,              # 委托单位
                'client_name': str,              # 委托人
                'commission_datetime_start': str,  # 委托时间开始 (ISO格式)
                'commission_datetime_end': str,    # 委托时间结束 (ISO格式)
                'project_number': str,           # 工程编号
                'project_name': str,             # 工程名称
                'project_location': str,         # 工程部位
            }
            page: 页码（从1开始）
            page_size: 每页记录数
            order_by: 排序字段列表
            
        Returns:
            Dict: {
                'success': bool,
                'data': {
                    'items': List[Dict],           # 档案条目列表
                    'pagination': Dict,           # 分页信息
                    'summary': Dict,             # 汇总信息
                },
                'message': str
            }
        """
        try:
            # 验证和处理查询参数
            processed_filters = self._process_query_filters(filters or {})
            
            # 验证分页参数
            page = max(1, page)
            page_size = min(max(1, page_size), 100)  # 限制最大每页100条
            
            # 获取可发放档案查询集
            queryset = self.archive_query_service.get_issuable_archives_queryset(processed_filters)
            
            # 分页查询
            archives, pagination_info = self.archive_query_service.get_paginated_archives(
                queryset=queryset,
                page=page,
                page_size=page_size,
                order_by=order_by
            )
            
            # 转换为业务数据格式
            items = [self._format_archive_item(archive) for archive in archives]
            
            # 生成汇总信息
            summary = self._generate_summary(queryset, pagination_info)
            
            return {
                'success': True,
                'data': {
                    'items': items,
                    'pagination': pagination_info,
                    'summary': summary,
                },
                'message': f'成功获取 {len(items)} 条可发放档案记录'
            }
            
        except Exception as e:
            logger.error(f"获取可发放档案失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'data': {
                    'items': [],
                    'pagination': {'total_count': 0, 'page': page, 'page_size': page_size, 'total_pages': 0},
                    'summary': {},
                },
                'error': f'查询失败: {str(e)}'
            }
    
    def get_archive_detail_for_issue(self, archive_id: int) -> Dict:
        """
        获取档案的详细发放信息
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            Dict: 档案详细发放信息
        """
        try:
            # 获取档案基础信息
            archive = self.archive_query_service.get_archive_by_id(archive_id)
            if not archive:
                return {
                    'success': False,
                    'error': '档案记录不存在'
                }
            
            # 获取发放状态详情
            issue_status = self.archive_query_service.get_archive_issue_status(archive_id)
            
            # 计算发放数量信息
            quantity_info = self.calculate_issue_quantity(archive_id)
            
            # 结合基础信息和发放状态
            detail = {
                'id': archive.id,
                'unified_number': archive.unified_number,
                'sample_number': archive.sample_number,
                'commission_number': archive.commission_number,
                'client_unit': archive.client_unit,
                'client_name': archive.client_name,
                'commission_datetime': archive.commission_datetime.isoformat() if archive.commission_datetime else None,
                'project_number': archive.project_number,
                'project_name': archive.project_name,
                'project_location': archive.project_location,
                'issue_status': issue_status,
                'quantity_info': quantity_info,  # 添加发放数量计算信息
            }
            
            return {
                'success': True,
                'data': detail,
                'message': '成功获取档案发放详情'
            }
            
        except Exception as e:
            logger.error(f"获取档案发放详情失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f'获取详情失败: {str(e)}'
            }

    def _determine_issue_type(self, archive_id: int) -> dict:
        """
        确定档案项的发放类型和基础状态
        
        根据设计文档实现完整的发放类型判断逻辑，通过调用ArchiveQueryService获取真实的发放历史
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            {
                'issue_type': str,              # 'first', 'second', 'completed'
                'can_issue': bool,              # 是否可以发放
                'has_first_issue': bool,        # 是否有第一次发放记录
                'has_second_issue': bool,       # 是否有第二次发放记录
                'first_copies': int or None,    # 第一次发放份数
                'second_copies': int or None,   # 第二次发放份数
                'total_copies': int,           # 总可发放份数
                'scenario': str                # 场景描述
            }
        """
        try:
            # 步骤 1: 从数据服务层获取数据
            history_data = self.archive_query_service.get_archive_issue_history(archive_id)

            if not history_data['success']:
                logger.error(f"无法获取档案发放历史: archive_id={archive_id}, error={history_data['error']}")
                return {
                    'issue_type': 'error',
                    'can_issue': False,
                    'has_first_issue': False,
                    'has_second_issue': False,
                    'first_copies': None,
                    'second_copies': None,
                    'total_copies': 0,
                    'scenario': 'archive_not_found' if not history_data['archive_exists'] else 'data_fetch_error',
                    'error': history_data['error']
                }

            total_copies = history_data['effective_total_copies']
            first_records = history_data['first_issue_records']
            second_records = history_data['second_issue_records']
            
            # 步骤 2: 执行业务逻辑（此处无数据库访问）
            first_copies = sum(record.copies for record in first_records) if first_records else None
            second_copies = sum(record.copies for record in second_records) if second_records else None
            
            has_first = first_copies is not None and first_copies > 0
            has_second = second_copies is not None and second_copies > 0
            
            # 根据设计文档的业务逻辑判断发放类型和状态
            if has_first and has_second:
                # 场景1：两次都有 - 发放完成
                return {
                    'issue_type': 'completed',
                    'can_issue': False,
                    'has_first_issue': True,
                    'has_second_issue': True,
                    'first_copies': first_copies,
                    'second_copies': second_copies,
                    'total_copies': total_copies,
                    'scenario': 'both_completed'
                }
            
            elif has_first and not has_second:
                if first_copies == total_copies:
                    # 场景2：第一次发放全部 - 发放完成
                    return {
                        'issue_type': 'completed',
                        'can_issue': False,
                        'has_first_issue': True,
                        'has_second_issue': False,
                        'first_copies': first_copies,
                        'second_copies': None,
                        'total_copies': total_copies,
                        'scenario': 'first_all_completed'
                    }
                else:
                    # 场景3：第一次发放部分 - 可进行第二次
                    return {
                        'issue_type': 'second',
                        'can_issue': True,
                        'has_first_issue': True,
                        'has_second_issue': False,
                        'first_copies': first_copies,
                        'second_copies': None,
                        'total_copies': total_copies,
                        'scenario': 'first_partial_awaiting_second'
                    }
            
            elif not has_first and has_second:
                # 场景4：只有第二次（异常情况）- 需补第一次
                return {
                    'issue_type': 'first',
                    'can_issue': True,
                    'has_first_issue': False,
                    'has_second_issue': True,
                    'first_copies': None,
                    'second_copies': second_copies,
                    'total_copies': total_copies,
                    'scenario': 'missing_first_补发'
                }
            
            else:
                # 场景5：都没有 - 可进行第一次
                return {
                    'issue_type': 'first',
                    'can_issue': True,
                    'has_first_issue': False,
                    'has_second_issue': False,
                    'first_copies': None,
                    'second_copies': None,
                    'total_copies': total_copies,
                    'scenario': 'initial_state'
                }
            
        except Exception as e:
            logger.error(f"判断发放类型时发生内部错误: archive_id={archive_id}, error={str(e)}", exc_info=True)
            return {
                'issue_type': 'error',
                'can_issue': False,
                'has_first_issue': False,
                'has_second_issue': False,
                'first_copies': None,
                'second_copies': None,
                'total_copies': 0,
                'scenario': 'calculation_error',
                'error': f'计算失败: {str(e)}'
            }

    def calculate_issue_quantity(self, archive_id: int) -> dict:
        """
        计算档案项的发放数量和选项
        
        依赖 _determine_issue_type 获取发放类型，然后计算具体数量
        
        Args:
            archive_id: 档案记录ID
            
        Returns:
            {
                # 继承 _determine_issue_type 的所有字段
                'available_options': list,      # 可选发放份数
                'suggested_copies': int,        # 建议发放份数  
                'remaining_copies': int,        # 剩余可发放份数
                'quantity_rule': str,          # 数量规则说明
                'validation': dict             # 验证信息
            }
        """
        try:
            # 1. 获取发放类型信息
            type_info = self._determine_issue_type(archive_id)
            
            # 如果发放类型判断失败，直接返回错误信息
            if type_info.get('issue_type') == 'error':
                return type_info
            
            # 2. 根据发放类型计算数量选项
            if not type_info['can_issue']:
                # 无法发放的情况
                quantity_info = {
                    'available_options': [],
                    'suggested_copies': 0,
                    'remaining_copies': 0,
                    'quantity_rule': '发放已完成，无可发放数量'
                }
            
            elif type_info['issue_type'] == 'first':
                if type_info['scenario'] == 'missing_first_补发':
                    # 补发第一次，只能1份
                    quantity_info = {
                        'available_options': [1],
                        'suggested_copies': 1,
                        'remaining_copies': 1,
                        'quantity_rule': '补发第一次，固定1份'
                    }
                else:
                    # 正常第一次发放，1份或全部
                    total = type_info['total_copies']
                    quantity_info = {
                        'available_options': [1, total],
                        'suggested_copies': 1,
                        'remaining_copies': total,
                        'quantity_rule': '第一次发放：1份或全部'
                    }
            
            elif type_info['issue_type'] == 'second':
                # 第二次发放，剩余全部
                remaining = type_info['total_copies'] - (type_info['first_copies'] or 0)
                quantity_info = {
                    'available_options': [remaining],
                    'suggested_copies': remaining,
                    'remaining_copies': remaining,
                    'quantity_rule': '第二次发放：剩余全部'
                }
            
            else:
                # 其他情况（如completed）
                quantity_info = {
                    'available_options': [],
                    'suggested_copies': 0,
                    'remaining_copies': 0,
                    'quantity_rule': '发放状态异常'
                }
            
            # 3. 添加验证信息
            quantity_info['validation'] = {
                'business_rules': {
                    'first_issue_constraint': '第一次发放只能是1份或全部',
                    'second_issue_constraint': '第二次发放必须是剩余全部',
                    'min_total_copies': 3,
                    '补发_constraint': '补发第一次时只能是1份'
                },
                'scenario_description': self._get_scenario_description(type_info['scenario'])
            }
            
            # 4. 合并返回结果
            return {**type_info, **quantity_info}
            
        except Exception as e:
            logger.error(f"计算发放数量失败: archive_id={archive_id}, error={str(e)}", exc_info=True)
            return {
                'issue_type': 'error',
                'can_issue': False,
                'available_options': [],
                'suggested_copies': 0,
                'remaining_copies': 0,
                'quantity_rule': '计算失败',
                'error': f'计算失败: {str(e)}'
            }

    def _get_scenario_description(self, scenario: str) -> str:
        """获取场景描述"""
        descriptions = {
            'both_completed': '第一次和第二次发放都已完成',
            'first_all_completed': '第一次发放了全部，无需第二次',
            'first_partial_awaiting_second': '第一次发放了部分，等待第二次发放',
            'missing_first_补发': '缺失第一次发放记录，需要补发',
            'initial_state': '初始状态，准备进行第一次发放',
            'archive_not_found': '档案记录不存在',
            'calculation_error': '计算过程出现错误'
        }
        return descriptions.get(scenario, '未知场景')

    def validate_issue_eligibility(self, archive_ids: Union[int, List[int]]) -> Dict:
        """
        统一的发放可行性验证
        
        Args:
            archive_ids: 档案ID或档案ID列表
            
        Returns:
            Dict: 验证结果 {
                'success': bool,
                'eligible_archives': List[int],     # 可发放的档案ID
                'ineligible_archives': List[Dict],  # 不可发放的档案及原因
                'summary': Dict                     # 验证汇总
            }
        """
        try:
            # 处理单个ID的情况
            if isinstance(archive_ids, int):
                archive_ids = [archive_ids]
            
            eligible_archives = []
            ineligible_archives = []
            
            for archive_id in archive_ids:
                # 计算发放信息
                quantity_info = self.calculate_issue_quantity(archive_id)
                
                if quantity_info.get('can_issue', False):
                    eligible_archives.append(archive_id)
                else:
                    ineligible_archives.append({
                        'archive_id': archive_id,
                        'reason': quantity_info.get('quantity_rule', '无法发放'),
                        'scenario': quantity_info.get('scenario', 'unknown'),
                        'error': quantity_info.get('error')
                    })
            
            return {
                'success': True,
                'eligible_archives': eligible_archives,
                'ineligible_archives': ineligible_archives,
                'summary': {
                    'total_archives': len(archive_ids),
                    'eligible_count': len(eligible_archives),
                    'ineligible_count': len(ineligible_archives),
                    'eligibility_rate': len(eligible_archives) / len(archive_ids) if archive_ids else 0
                }
            }
            
        except Exception as e:
            logger.error(f"验证发放可行性失败: archive_ids={archive_ids}, error={str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f'验证失败: {str(e)}',
                'eligible_archives': [],
                'ineligible_archives': [],
                'summary': {}
            }

    def _process_query_filters(self, filters: Dict) -> Dict:
        """
        处理和验证查询过滤条件
        
        Args:
            filters: 原始查询条件
            
        Returns:
            Dict: 处理后的查询条件
        """
        processed = {}
        
        # 处理字符串类型的过滤条件
        string_fields = [
            'unified_number', 'sample_number', 'client_unit', 'client_name',
            'project_number', 'project_name', 'project_location'
        ]
        
        for field in string_fields:
            value = filters.get(field)
            if value and isinstance(value, str) and value.strip():
                processed[field] = value.strip()
        
        # 处理日期时间过滤条件
        datetime_fields = ['commission_datetime_start', 'commission_datetime_end']
        
        for field in datetime_fields:
            value = filters.get(field)
            if value:
                if isinstance(value, str):
                    # 解析ISO格式的日期时间字符串
                    try:
                        parsed_datetime = parse_datetime(value)
                        if parsed_datetime:
                            processed[field] = parsed_datetime
                    except ValueError:
                        logger.warning(f"无法解析日期时间字段 {field}: {value}")
                elif isinstance(value, datetime):
                    processed[field] = value
        
        return processed
    
    def _format_archive_item(self, archive) -> Dict:
        """
        格式化档案条目为前端所需格式
        
        Args:
            archive: ArchiveRecord实例（包含注解字段）
            
        Returns:
            Dict: 格式化的档案条目数据
        """
        # 获取注解字段的值，处理可能为None的情况
        first_issued = getattr(archive, 'first_issue_copies_from_records', 0) or 0
        second_issued = getattr(archive, 'second_issue_copies_from_records', 0) or 0
        total_issued = getattr(archive, 'total_issued_copies', 0) or 0
        effective_total = getattr(archive, 'effective_total_copies', 3) or 3
        remaining = getattr(archive, 'remaining_copies', 0) or 0
        
        return {
            'id': archive.id,
            'unified_number': archive.unified_number,
            'sample_number': archive.sample_number,
            'client_unit': archive.client_unit,
            'client_name': archive.client_name,
            'commission_datetime': archive.commission_datetime.isoformat() if archive.commission_datetime else None,
            'project_number': archive.project_number,
            'project_name': archive.project_name,
            'project_location': archive.project_location,
            
            # 发放状态信息
            'issue_status': {
                'total_copies': effective_total,           # 总可发放份数
                'first_issued_copies': first_issued,       # 第一次已发放份数
                'second_issued_copies': second_issued,     # 第二次已发放份数
                'total_issued_copies': total_issued,       # 总已发放份数
                'remaining_copies': remaining,             # 剩余可发放份数
                'can_issue': remaining > 0,               # 是否可以发放
                'issue_type': self._determine_issue_type_simple(first_issued, second_issued, effective_total)
            },
            
            # 基础档案信息
            'commission_number': archive.commission_number,
            'created_at': archive.created_at.isoformat() if archive.created_at else None,
        }
    
    def _determine_issue_type_simple(self, first_issued: int, second_issued: int, total_copies: int) -> str:
        """
        简单判断当前档案的发放类型（用于列表显示）
        
        Args:
            first_issued: 第一次已发放份数
            second_issued: 第二次已发放份数
            total_copies: 总可发放份数
            
        Returns:
            str: 发放类型 ('not_issued', 'first_partial', 'first_complete', 'second_partial', 'completed')
        """
        if first_issued == 0 and second_issued == 0:
            return 'not_issued'  # 未发放
        elif first_issued > 0 and second_issued == 0:
            if first_issued == total_copies:
                return 'first_complete'  # 第一次发放完全
            else:
                return 'first_partial'   # 第一次发放部分
        elif first_issued > 0 and second_issued > 0:
            if first_issued + second_issued == total_copies:
                return 'completed'       # 发放完成
            else:
                return 'second_partial'  # 第二次发放部分
        else:
            return 'unknown'  # 异常状态
    
    def _generate_summary(self, queryset, pagination_info: Dict) -> Dict:
        """
        生成查询结果汇总信息
        
        Args:
            queryset: 档案查询集
            pagination_info: 分页信息
            
        Returns:
            Dict: 汇总信息
        """
        try:
            total_count = pagination_info.get('total_count', 0)
            
            if total_count == 0:
                return {
                    'total_count': 0,
                    'total_remaining_copies': 0,
                    'avg_remaining_copies': 0,
                }
            
            # 计算汇总统计（仅对当前查询结果）
            summary = {
                'total_count': total_count,
                'current_page_count': pagination_info.get('page_size', 0),
                'total_pages': pagination_info.get('total_pages', 0),
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成汇总信息失败: {str(e)}")
            return {
                'total_count': pagination_info.get('total_count', 0),
                'error': '汇总信息生成失败'
            }

# TODO: [P1] 添加发放单创建和管理功能
# TODO: [P1] 添加批量发放预检查功能
# TODO: [P1] 添加发放历史查询功能
# TODO: [P2] 添加发放统计报表功能
# TODO: [P2] 添加事务服务层的集成 