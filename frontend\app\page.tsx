"use client"

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * 首页组件
 * 认证用户自动重定向到仪表板
 */
export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // 如果用户已认证，重定向到仪表板
    if (status === 'authenticated') {
      router.replace('/dashboard');
    }
  }, [status, router]);

  // 加载状态
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-500">正在加载...</p>
        </div>
      </div>
    );
  }

  // 认证用户重定向中
  if (status === 'authenticated') {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-500">正在跳转到仪表板...</p>
        </div>
      </div>
    );
  }

  // 未认证用户看到的欢迎页面（理论上不会到达这里，因为中间件会重定向到登录）
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">档案管理系统</h1>
        <p className="text-gray-500 mb-4">欢迎使用档案管理系统</p>
      </div>
    </div>
  )
}
