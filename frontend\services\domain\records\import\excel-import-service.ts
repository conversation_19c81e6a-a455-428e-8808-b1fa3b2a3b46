/**
 * Excel导入服务
 * 
 * 提供与后端Excel导入API的交互功能，包括:
 * - 上传并分析Excel文件
 * - 获取分析结果和冲突信息
 * - 确认导入并处理冲突解决方案
 * - 会话管理和心跳维护
 * 
 * <AUTHOR> Team
 * @since 2024-07-27
 */

import apiClient from '@/lib/apiClient';

// ==================== 核心类型定义 ====================

/** 导入分析统计信息 */
export interface ExcelAnalysisStats {
  readonly totalRowsRead: number;
  readonly failedRows: number;
  readonly skippedIdentical: number;
  readonly foundNewCount: number;
  readonly foundUpdateCount: number;
}

/** 用户决策统计信息 */
export interface UserDecisionStats {
  readonly skippedUpdateCount: number;
  readonly confirmedUpdateCount: number;
}

/** 导入选项配置 */
export interface ExcelImportOptions {
  readonly sheetName?: string | number;
}

/** 冲突解决动作类型 */
export const enum ConflictResolutionAction {
  UPDATE = 'update',
  SKIP = 'skip',
  CREATE = 'create'
}

/** 冲突解决方案 */
export interface ConflictResolution {
  readonly commissionNumber: string;
  readonly action: ConflictResolutionAction;
}

/** 字段差异信息 */
export interface FieldDifference {
  readonly fieldName: string;
  readonly fieldDisplayName: string;
  readonly existingValue: string;
  readonly importedValue: string;
}

/** 冲突记录信息 */
export interface ConflictRecord {
  readonly commissionNumber: string;
  readonly excelRowNumber: number;
  readonly existingRecordPk?: number;
  readonly conflictType: 'new' | 'update' | 'identical';
  readonly fieldDifferences: readonly FieldDifference[];
}

/** 分析结果统计 */
export interface AnalysisResult {
  readonly total: number;
  readonly new: number;
  readonly update: number;
  readonly identical: number;
  readonly error: number;
}

/** 取消导入请求 */
export interface CancelImportRequest {
  expectedSessionId?: string;
  reason?: string;
}

// ==================== API响应类型 ====================

/** 分析启动响应数据 */
export interface ExcelAnalysisStartupInfoData {
  readonly importSessionId: string;
  readonly fileName: string | null;
  readonly totalRecords: number;
  readonly status: string;
  readonly message: string;
}

/** 分析结果完整数据 */
export interface ExcelAnalysisResultPayload {
  readonly sessionId: string;
  readonly status: string;
  readonly statusDisplay: string;
  readonly fileName: string | null;
  readonly recordCount: number;
  readonly conflictCount: number;
  readonly analysisStats: AnalysisResult;
  readonly conflictDetails: readonly ConflictRecord[];
  readonly createdBy: any;
  readonly createdAt: string;
  readonly updatedAt: string;
}

/** 分析进度数据 */
export interface AnalysisProgressData {
  readonly analyzedRecords: number;
  readonly totalRecords: number;
  readonly analyzedPercentage: number;
  readonly status: string;
}

/** 用户信息 */
export interface ActiveSessionUserData {
  readonly id: number;
  readonly username: string;
}

/** 会话详细信息 */
export interface SessionInfoData {
  readonly sessionId: string;
  readonly status: string;
  readonly fileName: string | null;
  readonly recordCount: number;
  readonly conflictCount: number;
  readonly progress: number;
  readonly createdAt: string | null;
  readonly updatedAt: string | null;
  readonly expiresAt: string | null;
  readonly resultsDisplayExpiresAt?: string | null;
  readonly importLogId?: string | null;
  readonly importResultsSummary?: ImportReportData;
  readonly createdBy: ActiveSessionUserData | null;
  readonly processingUser?: ActiveSessionUserData | null;
  readonly lastActivity?: string | null;
  readonly errorMessage?: string | null;
}

/** 活跃会话响应数据 */
export interface ActiveImportSessionResponseData {
  readonly hasActiveSession: boolean;
  readonly sessionInfo?: SessionInfoData;
  readonly canTakeover?: boolean;
  readonly message?: string;
}

/** 取消导入成功响应 */
export interface CancelImportSuccessData {
  readonly sessionId: string;
  readonly status: string;
  readonly message: string;
  readonly cancelledAt: string;
}

/** 详细错误项 */
export interface DetailedErrorItem {
  readonly row?: number;
  readonly field?: string;
  readonly message: string;
  readonly commissionNumber?: string;
  readonly sampleNumber?: string;
}

/** 导入详细报告 */
export interface ImportDetailedReport {
  readonly errors?: readonly DetailedErrorItem[];
  readonly importTaskProcessingErrors?: readonly DetailedErrorItem[];
}

/** 导入报告完整数据 */
export interface ImportReportData {
  // 元数据
  readonly fileName?: string;
  readonly fileSize?: number;
  readonly fileHash?: string;
  readonly batchNumber: string;
  readonly status: 'completed' | 'partial' | 'failed';
  readonly errorMessage?: string;
  readonly detailedReport?: ImportDetailedReport;
  readonly importDate?: string;
  readonly processingTime?: number;
  readonly createdBy?: {
    readonly id: number;
    readonly username: string;
  } | null;
  readonly importUser?: {
    readonly id: number;
    readonly username: string;
  } | null;

  // 统计数据
  readonly analysisTotalRowsRead?: number;
  readonly analysisSuccessfullyParsedRows?: number;
  readonly analysisFailedRows?: number;
  readonly analysisFoundNewCount?: number;
  readonly analysisFoundUpdateCount?: number;
  readonly analysisSkippedIdentical?: number;
  readonly userDecisionSkippedUpdateCount?: number;
  readonly userDecisionConfirmedUpdateCount?: number;
  readonly importTaskTotalRecordsSubmitted?: number;
  readonly importTaskCreatedCount?: number;
  readonly importTaskUpdatedCount?: number;
  readonly importTaskUnchangedCount?: number;
  readonly importTaskProcessedSuccessfullyCount?: number;
  readonly importTaskFailedCount?: number;
  readonly overallTotalInitialRecords?: number;
  readonly overallUserDecisionSkippedUpdates?: number;
  readonly overallFinalCreatedCount?: number;
  readonly overallFinalUpdatedCount?: number;
  readonly overallSkippedBySystemTotal?: number;
  readonly overallSkippedTotal?: number;
  readonly overallFailedTotal?: number;
  readonly overallProcessedSuccessfullyTotal?: number;
  readonly importLogId?: string | null;
}

/** 确认导入API响应 */
export interface ConfirmImportApiResponse {
  readonly success: boolean;
  readonly data?: ImportReportData;
  readonly error?: { readonly message?: string; readonly type?: string; readonly [key: string]: unknown } | string;
}

/** 导入详细报告类型别名 */
export type DetailedReport = ImportDetailedReport;

// ==================== 服务类 ====================

class ExcelImportService {
  /**
   * 分析Excel文件
   * 
   * @param excelFile - Excel文件
   * @param importOptions - 导入选项
   * @param onUploadProgress - 上传进度回调
   * @returns 分析启动信息
   */
  async analyzeExcelFile(
    excelFile: File,
    importOptions: ExcelImportOptions = {},
    onUploadProgress?: (progress: number) => void
  ): Promise<ExcelAnalysisStartupInfoData> {
    const formData = new FormData();
    formData.append('file', excelFile);
    
    if (importOptions.sheetName) {
      formData.append('sheet_name', String(importOptions.sheetName));
    }

    const response = await apiClient.post<ExcelAnalysisStartupInfoData>(
      '/api/archive-records/excel-import/analyze/',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onUploadProgress && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onUploadProgress(percentCompleted);
          }
        },
      }
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || '文件分析失败');
    }

    return response.data;
  }

  /**
   * 获取分析结果
   * 
   * @param sessionId - 会话ID
   * @returns 分析结果详细数据
   */
  async getAnalysisResult(sessionId: string): Promise<ExcelAnalysisResultPayload> {
    const response = await apiClient.get<ExcelAnalysisResultPayload>(
      `/api/archive-records/excel-import/${sessionId}/analysis-result/`
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || '获取分析结果失败');
    }

    return response.data;
  }

  /**
   * 确认导入并提交冲突解决方案
   * 
   * @param sessionId - 导入会话ID
   * @param resolutions - 冲突解决方案数组
   * @param analysisStats - 分析阶段统计信息
   * @param userDecisionStats - 用户决策统计信息
   * @returns 导入确认响应
   */
  async confirmImport(
    sessionId: string,
    resolutions: readonly ConflictResolution[] = [],
    analysisStats?: ExcelAnalysisStats,
    userDecisionStats?: UserDecisionStats
  ): Promise<ConfirmImportApiResponse> {
    const requestBody = {
      importSessionId: sessionId,
      resolutions,
      analysisStats,
      userDecisionStats,
    };

    return await apiClient.post<ImportReportData>(
      '/api/archive-records/excel-import/confirm/',
      requestBody
    );
  }

  /**
   * 获取当前用户的活跃导入会话
   * 
   * @returns 活跃会话信息
   * @throws {Error} 当获取失败时
   */
  async getActiveImportSession(): Promise<ActiveImportSessionResponseData> {
    const response = await apiClient.get<ActiveImportSessionResponseData>(
      '/api/archive-records/excel-import/active-session/'
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || '获取活跃会话失败');
    }

    return response.data;
  }

  /**
   * 取消导入会话
   * 
   * @param sessionId - 要取消的会话ID
   * @param expectedSessionId - 预期的会话ID（用于验证）
   * @param reason - 取消原因
   * @returns 取消操作结果
   */
  async cancelImport(
    sessionId: string, 
    expectedSessionId?: string,
    reason?: string
  ): Promise<CancelImportSuccessData> {
    const requestBody: CancelImportRequest = {};

    if (expectedSessionId) {
      requestBody.expectedSessionId = expectedSessionId;
    }

    if (reason) {
      requestBody.reason = reason;
    }

    const response = await apiClient.post<CancelImportSuccessData>(
      `/api/archive-records/excel-import/${sessionId}/cancel/`,
      requestBody
    );

    if (!response.success || !response.data) {
      throw new Error(response.error || '取消导入失败');
    }

    return response.data;
  }

  /**
   * 获取会话详细信息
   * 
   * @param sessionId - 会话ID
   * @returns 会话详细信息，如果会话不存在则返回null
   */
  async getSession(sessionId: string): Promise<SessionInfoData | null> {
    try {
      const response = await apiClient.get<SessionInfoData>(
        `/api/archive-records/excel-import/${sessionId}/session-info/`
      );

      if (!response.success) {
        return null;
      }

      return response.data || null;
    } catch (error) {
      console.warn('获取会话信息失败:', error);
      return null;
    }
  }

  /**
   * 发送心跳以保持会话活跃
   * 
   * @param sessionId - 会话ID
   * @returns 心跳响应
   */
  async sendHeartbeat(sessionId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await apiClient.post<{ message: string }>(
        `/api/archive-records/excel-import/${sessionId}/heartbeat/`,
        {}
      );

      return {
        success: response.success,
        message: response.data?.message || response.error
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '心跳发送失败'
      };
    }
  }

  /**
   * 确认导入结果
   * 
   * @param sessionId - 会话ID
   * @param expectedSessionId - 预期的会话ID
   * @returns 确认结果
   */
  async acknowledgeImportResults(
    sessionId: string, 
    expectedSessionId?: string
  ): Promise<{ success: boolean; data?: { message: string; sessionId: string; newStatus: string | null }; error?: string }> {
    return await apiClient.post<{ message: string; sessionId: string; newStatus: string | null }>(
      `/api/archive-records/excel-import/${sessionId}/acknowledge/`,
      expectedSessionId ? { expectedSessionId } : {}
    );
  }

  /**
   * 开始处理冲突（声明会话所有权）
   * 
   * @param sessionId - 会话ID
   * @returns 操作结果
   */
  async beginActiveConflictProcessing(
    sessionId: string
  ): Promise<{ success: boolean; data?: { message: string }; error?: string }> {
    return await apiClient.post<{ message: string }>(
      `/api/archive-records/excel-import/${sessionId}/begin-conflict-processing/`,
      {}
    );
  }

  /**
   * 暂停处理冲突（释放会话所有权）
   * 
   * @param sessionId - 会话ID
   * @returns 操作结果
   */
  async pendActiveConflictProcessing(
    sessionId: string
  ): Promise<{ success: boolean; data?: { message: string }; error?: string }> {
    return await apiClient.post<{ message: string }>(
      `/api/archive-records/excel-import/${sessionId}/pend-conflict-processing/`,
      {}
    );
  }
}

// ==================== 导出 ====================

// 创建单例实例
const excelImportService = new ExcelImportService();

export default excelImportService;
export { ExcelImportService }; 