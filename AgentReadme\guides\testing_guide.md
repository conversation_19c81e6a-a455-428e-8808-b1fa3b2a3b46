# 测试指南

本文档提供了运行和编写项目测试的指南。

## 测试结构

项目测试分为多个层次：

- **单元测试**：测试各组件的独立功能
- **集成测试**：测试组件间的交互
- **功能测试**：测试整个功能流程

测试文件位于以下目录：

```文件结构
test_suite/
├── unit/              # 单元测试
├── integration/       # 集成测试
│   ├── archive_processing/  # 档案处理集成测试
│   └── ...
└── functional/        # 功能测试
```

## 运行测试

### Django测试命令

可以使用Django的测试命令运行测试：

```bash
# 运行所有测试
python manage.py test

# 运行特定应用的测试
python manage.py test archive_processing

# 运行特定测试文件
python manage.py test test_suite.integration.archive_processing.test_tasks
```

### Pytest命令

项目也支持使用pytest运行测试，提供更丰富的功能和更详细的输出：

#### Windows环境

```powershell
# 设置环境变量解决OpenMP运行时问题
$env:KMP_DUPLICATE_LIB_OK="TRUE"; python -m pytest test_suite/integration/archive_processing/test_tasks.py -vv

# 运行特定测试
python -m pytest test_suite/integration/archive_processing/test_tasks.py::TestProcessPdfTaskIntegration::test_precheck_success_full_workflow -vv
```

#### Unix/Linux环境

```bash
# 设置环境变量解决OpenMP运行时问题
KMP_DUPLICATE_LIB_OK="TRUE" python -m pytest test_suite/integration/archive_processing/test_tasks.py -vv

# 运行特定测试
python -m pytest test_suite/integration/archive_processing/test_tasks.py::TestProcessPdfTaskIntegration::test_precheck_success_full_workflow -vv
```

### 特殊注意事项

1. **OpenMP运行时问题**：某些依赖(如scikit-learn/scikit-image)可能引起OpenMP运行时错误。通过设置环境变量 `KMP_DUPLICATE_LIB_OK="TRUE"` 可以解决。

2. **模拟对象设置**：测试中使用的模拟对象需要具有具体属性值，不要使用简单的 `MagicMock` 对象进行比较。

3. **使用命名元组**：当需要返回具有特定属性的元组时，使用命名元组(namedtuple)而不是普通元组，以确保属性访问正确。

## 编写测试

### 集成测试示例

以下是一个编写集成测试的简单示例：

```python
import pytest
from unittest.mock import patch, MagicMock
from django.contrib.auth.models import User
from collections import namedtuple

# 使用pytest-django标记
pytestmark = pytest.mark.django_db(transaction=True)

@pytest.fixture
def test_user(db):
    """创建测试用户"""
    user, _ = User.objects.get_or_create(
        username='testuser',
        defaults={'password': 'testpassword', 'email': '<EMAIL>'}
    )
    return user

@patch('myapp.services.SomeService')
def test_some_functionality(mock_service, test_user):
    # 1. 设置模拟对象
    mock_instance = MagicMock()
    mock_instance.method.return_value = {'success': True}
    mock_service.return_value = mock_instance
    
    # 2. 调用被测试的功能
    from myapp.tasks import some_task
    result = some_task(test_user.id)
    
    # 3. 验证结果
    assert result['success'] is True
    mock_instance.method.assert_called_once()
```

### 最佳实践

1. **使用fixture**：利用pytest的fixture机制创建测试数据和对象

2. **合理使用模拟**：模拟外部服务和耗时操作，但确保模拟对象行为与真实对象一致

3. **明确的断言**：使用清晰的断言验证预期行为，专注于测试业务逻辑而非实现细节

4. **清理资源**：使用try/finally确保测试结束后所有资源被正确清理
