"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface OperationHistoryProps {
  history: any[]
}

export function OperationHistory({ history }: OperationHistoryProps) {
  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "creating":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            正在创建
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            草稿
          </Badge>
        )
      case "locked":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            已锁定
          </Badge>
        )
      case "issued":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            已发放
          </Badge>
        )
      case "deleted":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            已删除
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardContent className="pt-6 flex-1 flex flex-col">
        {history && history.length > 0 ? (
          <div className="flex-1 overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">时间</th>
                  <th className="text-left py-3 px-4">操作</th>
                  <th className="text-left py-3 px-4">状态</th>
                  <th className="text-left py-3 px-4">操作人</th>
                  <th className="text-left py-3 px-4">原因</th>
                </tr>
              </thead>
              <tbody>
                {history.map((item: any, index: number) => (
                  <tr key={item.id || index} className="border-b">
                    <td className="py-3 px-4">{item.timestamp}</td>
                    <td className="py-3 px-4">
                      {(() => {
                        switch (item.action) {
                          case "create":
                            return "创建"
                          case "update":
                            return "更新"
                          case "lock":
                            return "锁定"
                          case "unlock":
                            return "解锁"
                          case "issue":
                            return "发放"
                          case "delete":
                            return "删除"
                          case "print":
                            return "打印"
                          default:
                            return item.action
                        }
                      })()}
                    </td>
                    <td className="py-3 px-4">{getStatusBadge(item.status)}</td>
                    <td className="py-3 px-4">{item.user}</td>
                    <td className="py-3 px-4">{item.reason || item.details}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <p>暂无操作历史</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
