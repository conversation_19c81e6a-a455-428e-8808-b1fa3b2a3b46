# report_issuing/services/state_specific/draft_state_service.py
import logging
from typing import Dict, Any, List
from django.db import transaction
from report_issuing.services.data_services.issue_form_data_service import IssueFormDataService

logger = logging.getLogger(__name__)

class DraftStateService:
    """
    提供发放单在"草稿"状态下所有允许操作的具体业务逻辑实现。
    这个服务被 DraftHandler 调用。
    """
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.data_service = IssueFormDataService()
        # 此处可以通过依赖注入传入所需的数据服务
        # self.issue_form_data_service = IssueFormDataService()
        # self.audit_service = AuditService(user_id)
        # 也可以注入其他公共服务
        # self.common_service = IssueCommonService(user_id)

    @transaction.atomic
    def update_draft(self, form_id: int, form_data: Dict[str, Any], items_data: List[Dict[str, Any]] = None):
        """
        以事务方式更新指定的草稿发放单。
        它会更新基础信息，并用 `items_data` 完全替换现有条目。
        """
        logger.info(f"用户 {self.user_id} 正在以事务方式为草稿单 {form_id} 更新内容（替换模式）。")
        
        # 1. 更新发放单基本信息
        if form_data:
            self.data_service.update_form_details(form_id, form_data)
            logger.debug(f"草稿单 {form_id}：更新基本信息完成。")

        # 2. 如果提供了 items_data，则替换所有条目
        if items_data is not None:
            # 注意：这里我们调用一个能处理替换逻辑的方法
            self.data_service.update_items_for_form(form_id, items_data)
            logger.debug(f"草稿单 {form_id}：替换所有条目完成。")
        
        logger.info(f"草稿单 {form_id} 的事务性更新完成。")
        # 注意：返回时预加载关联数据以优化性能
        return self.data_service.get_form_by_id_with_related(form_id)

    def perform_hard_delete(self, form_id: int):
        """
        硬删除指定的草稿发放单。
        """
        logger.info(f"用户 {self.user_id} 正在硬删除草稿单 {form_id}。")
        deleted = self.data_service.hard_delete_form(form_id)
        if not deleted:
            logger.warning(f"尝试硬删除一个不存在的发放单: ID={form_id}")
        return deleted 