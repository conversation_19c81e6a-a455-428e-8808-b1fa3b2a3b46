# Operation Document: Resolve AG Grid Filtering Errors and Warnings

## 📋 Change Summary

**Purpose**: To fix AG Grid error #63 (`setFilterModel() - unable to fully apply model`) for the `commission_datetime` column and resolve the warning about `quickFilterText` not being supported with the Server-Side Row Model (SSRM).
**Scope**: `frontend/app/records/ledger/page.tsx`
**Associated**: User-reported console errors/warnings after previous filtering fixes.

## 🔧 Operation Steps

### 📊 OP-001: Analyze AG Grid Error #63 for `commission_datetime`

**Precondition**: `applyDateFilter` function calls `gridApi.setFilterModel()` with a model for `commission_datetime`, but this column has `filter: false` in its definition.
**Operation**: Confirmed that AG Grid error #63 occurs when `setFilterModel` is called for a column that doesn't have an active filter instance (due to `filter: false`). The filter model specified `filterType: 'date'`, which the grid couldn't apply without a corresponding date filter component on the column.
**Postcondition**: Understood that `setFilterModel` for `commission_datetime` was problematic with `filter: false`.

### ✏️ OP-002: Modify `applyDateFilter` to Remove `setFilterModel` for `commission_datetime`

**Precondition**: `applyDateFilter` uses `setFilterModel` for `commission_datetime`.
**Operation**: Removed the lines that build `dateFilterModel` and call `gridApi.setFilterModel(dateFilterModel)` from the `applyDateFilter` function. The date filtering logic now relies solely on updating `currentFiltersRef.current.startDate` and `currentFiltersRef.current.endDate`, and then calling `gridApi.refreshServerSide({ purge: true })`. The `fetchArchiveLedgerRecords` function already reads these dates from `currentFiltersRef` to construct the API query parameters (`commission_datetime__gte`, `commission_datetime__lte`).
**Postcondition**: `applyDateFilter` no longer calls `setFilterModel` for `commission_datetime`, which should resolve error #63. Date filtering is now handled by passing specific date parameters to the backend.

### 📊 OP-003: Analyze `quickFilterText` SSRM Warning

**Precondition**: `onQuickFilterChanged` function calls `gridApi.setGridOption('quickFilterText', value)`.
**Operation**: AG Grid console warning explicitly states `quickFilterText` is not for SSRM. The backend already handles a `q` parameter for quick search, sourced from `currentFiltersRef.current.quickFilter`.
**Postcondition**: Confirmed `setGridOption('quickFilterText', ...)` is incorrect for SSRM.

### ✏️ OP-004: Modify `onQuickFilterChanged` to Remove `setGridOption('quickFilterText', ...)`

**Precondition**: `onQuickFilterChanged` uses `setGridOption('quickFilterText', ...)`.
**Operation**: Removed the line `gridApi.setGridOption('quickFilterText', value)` from `onQuickFilterChanged`. The function already updates `currentFiltersRef.current.quickFilter` and calls `gridApi.refreshServerSide({ purge: true })`, which correctly triggers the backend to filter using the `q` parameter.
**Postcondition**: The `quickFilterText` warning should be resolved. Quick filtering remains functional via backend processing.

## 📝 Change Details

### CH-001: Adjust `applyDateFilter` for `commission_datetime`

**File**: `frontend/app/records/ledger/page.tsx`
**Before**:

```typescript
// ... in applyDateFilter
    if (gridApi) {
      const dateFilterModel: any = {};
      if (startDate || endDate) {
        dateFilterModel['commission_datetime'] = {
          filterType: 'date',
          type: 'inRange',
          dateFrom: startDate || null, 
          dateTo: endDate || null
        };
      }
      gridApi.setFilterModel(dateFilterModel);
      gridApi.refreshServerSide({ purge: true });
    }
// ...
```

**After**:

```typescript
// ... in applyDateFilter
    if (gridApi) {
      console.log('[日期筛选] 触发服务端刷新，参数来源: currentFiltersRef');
      // 不再为 commission_datetime 调用 setFilterModel，因为它的 filter: false
      // 后端将直接使用 commission_datetime__gte 和 commission_datetime__lte 参数
      gridApi.refreshServerSide({ purge: true });
    }
// ...
```

**Rationale**: Resolves AG Grid error #63 by not attempting to `setFilterModel` on a column (`commission_datetime`) that has `filter: false`. Date filtering for this column is achieved by sending dedicated date parameters to the backend, which are picked up during the `refreshServerSide` process.
**Potential Impact**: Corrects an error. Ensures date filtering relies on the intended mechanism for this custom setup.

### CH-002: Remove `setGridOption('quickFilterText', ...)` for SSRM

**File**: `frontend/app/records/ledger/page.tsx`
**Before**:

```typescript
// ... in onQuickFilterChanged
      currentFiltersRef.current.quickFilter = value;
      gridApi.setGridOption('quickFilterText', value);
      console.log('[AG Grid] 应用快速筛选:', value);
      gridApi.refreshServerSide({ purge: true });
// ...
```

**After**:

```typescript
// ... in onQuickFilterChanged
      currentFiltersRef.current.quickFilter = value;
      console.log('[AG Grid] 快速筛选值已更新 (不通过setGridOption):', value);
      gridApi.refreshServerSide({ purge: true });
// ...
```

**Rationale**: Resolves the AG Grid warning that `quickFilterText` is not supported with SSRM. Quick filtering is correctly handled by the backend via the `q` parameter, which is already populated from `currentFiltersRef`.
**Potential Impact**: Removes console warning and aligns with correct SSRM quick filtering patterns.

## ✅ Verification Results

**Method**: Manual testing and console observation.

1. Apply a date filter using the custom date picker. Check console for AG Grid error #63.
2. Use the quick search input. Check console for the `quickFilterText` warning.
**Expected Results**:

- AG Grid error #63 should no longer appear when applying date filters for `commission_datetime`.
- The warning about `quickFilterText` should no longer appear when using the quick search.
- Both date filtering and quick search should remain fully functional, with filtering logic correctly handled by the backend.
**Problems**: None anticipated, as these changes align the implementation with AG Grid's documented behavior for SSRM and `filter: false` columns.
**Solutions**: Not applicable.
