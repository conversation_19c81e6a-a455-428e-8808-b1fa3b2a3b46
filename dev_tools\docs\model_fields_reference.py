"""
档案记录字段分类参考

此文件记录原系统的字段分组，用于参考和文档目的。
这些字段分组不影响实际代码，仅作为开发文档使用。
"""

# 原系统字段组（按业务类型分类）
ORIGINAL_SYSTEM_FIELDS = {
    # 基础信息字段
    'basic_info': [
        'sample_number',    # 样品编号
        'account',         # 账号
        'commission_number', # 委托编号
        'report_number',    # 报告编号
        'province_unified_number',  # 省统一报告编号
    ],
    
    # 状态字段
    'status_fields': [
        'change_count',     # 更改次数
        'current_status',   # 当前数据状态
        'processing_status', # 待处理状态
        'report_issue_status', # 报告发放状态
        'archive_status',   # 报告归档状态
        'payment_status',   # 收费状态
        'price_adjustment_status', # 价格调整状态
    ],
    
    # 工程信息字段
    'project_info': [
        'project_number',   # 工程编号
        'project_name',     # 工程名称
        'sub_project',      # 分项工程
        'project_location', # 工程部位
        'project_address',  # 工程地址
    ],
    
    # 委托信息字段
    'commission_info': [
        'client_unit',     # 委托单位
        'client_name',     # 委托人
        'commission_date',  # 委托日期
    ],
    
    # 试验信息字段
    'test_info': [
        'test_start_date', # 试验开始日期
        'test_end_date',   # 试验结束日期
        'test_person1',    # 试验人1
        'test_person2',    # 试验人2
    ],
    
    # 结果信息字段
    'result_info': [
        'test_result',     # 检测结果
        'conclusion',      # 结论
        'test_parameters', # 检测参数
        'unqualified_parameters', # 不合格参数
    ],
    
    # 人员和日期字段
    'personnel_dates': [
        'data_entry_person',  # 数据录入人
        'storage_date',     # 入库日期
        'storage_person',   # 入库人
        'outbound_date',   # 出库日期
        'outbound_person', # 出库人
        'verification_date', # 校核日期
        'verification_person', # 校核人
        'review_date',      # 审核日期
        'review_person',    # 审核人
        'approval_date',    # 批准日期
        'approval_person',  # 批准人
        'print_date',       # 打印日期
        'print_person',     # 打印人
        'print_count',      # 打印次数
        'issue_date',       # 发放日期
        'issue_person',     # 发放人
        'receive_date',     # 领取日期
        'receiver',         # 领取人
        'archive_date',     # 归档日期
        'archive_person',   # 归档人
    ],
    
    # 样品信息字段
    'sample_info': [
        'group_number',     # 组号
        'sample_name',      # 样品/项目名称
        'assigned_person',  # 分配人
        'component_count',  # 构件(桩)数
        'test_point_count', # 测点数
        'unqualified_point_count', # 不合格点数
    ],
    
    # 费用信息字段
    'cost_info': [
        'standard_price',   # 标准价格费用
        'discount_price',   # 折扣价格费用
        'actual_price',     # 实际价格费用
    ],
    
    # 其他信息字段
    'other_info': [
        'attachments',      # 附件
        'station_code',     # 站点编号
        'organization_code', # 机构代号
    ]
} 