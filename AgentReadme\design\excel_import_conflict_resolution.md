# Excel导入冲突确认功能设计方案（优化版）

## 1. 背景与目标

### 1.1 背景

目前系统的Excel导入功能能够自动处理重复数据，但缺乏用户干预机制。当发现同一编号但字段内容不同的记录时，系统会根据预设策略自动处理（更新或跳过），用户无法针对每条记录进行决策。

### 1.2 目标

- 增强Excel导入功能，实现针对冲突记录的用户确认机制
- 提供直观的冲突对比界面，展示数据差异
- 允许用户对每条冲突记录单独决策（更新或保留）
- 维持系统数据一致性，避免多用户同时导入造成的数据混乱
- 优化现有导入流程，提升用户体验
- **新增：实现实时进度反馈，提高大数据量处理时的用户体验**
- **新增：优化UI交互流程，减少操作步骤**

## 2. 需求分析

### 2.1 功能需求

1. **冲突预检测**：上传Excel文件后，系统应先分析潜在冲突而不立即导入
2. **冲突展示**：直观展示冲突记录及其字段差异
3. **批量操作**：支持全选/全不选等批量操作
4. **单条决策**：允许用户对每条冲突记录选择"更新"或"跳过"
5. **导入执行**：根据用户决策执行实际导入
6. **导入锁定**：同一时间只允许一个用户有未确认的导入任务
7. **新增：实时进度反馈**：在分析和导入过程中提供实时进度信息
8. **新增：会话过期管理**：自动识别并处理过期的导入会话
9. **新增：冲突记录筛选**：提供按冲突类型筛选记录的功能

### 2.2 非功能需求

1. **性能**：处理大量冲突记录时保持响应性能
2. **可靠性**：确保导入过程中断时数据不丢失
3. **扩展性**：设计应易于适应未来可能的策略变更
4. **用户体验**：提供清晰直观的操作界面
5. **新增：响应式设计**：确保在不同设备上都能良好工作
6. **新增：可访问性**：符合WCAG 2.1 AA级标准
7. **新增：类型安全**：严格的TypeScript类型定义，提高代码质量

### 2.3 风险分析

1. **重复导入策略风险**：
   - `skip`策略会跳过所有已存在的记录，而不考虑是否有字段变更
   - 可能导致有价值的更新被忽略
   - 系统中若已使用此策略，需重点检查导入结果

2. **并发操作风险**：
   - 多用户同时导入时可能造成数据不一致
   - 需实现导入锁定机制

3. **性能风险**：
   - 大量冲突记录时界面可能响应缓慢
   - 需适当设计分页机制和异步处理

4. **新增：异步状态管理风险**：
   - 轮询机制可能导致性能问题或不必要的服务器负载
   - 需优化异步状态管理策略，考虑使用更现代的技术方案

5. **新增：安全风险**：
   - 文件上传可能引入安全漏洞
   - 需实施严格的输入验证和文件类型检查

## 3. 系统设计

### 3.1 整体架构

设计采用两阶段导入模式，将冲突分析和实际导入分为独立的两个步骤：

1. **第一阶段**：上传并分析，生成冲突报告
2. **第二阶段**：用户确认后执行实际导入

```flow graph
┌────────────┐    ┌────────────┐    ┌────────────┐    ┌────────────┐
│            │    │            │    │            │    │            │
│  上传Excel  │───>│ 冲突预分析  │───>│ 用户确认   │───>│ 实际导入   │
│            │    │            │    │            │    │            │
└────────────┘    └────────────┘    └────────────┘    └────────────┘
```

### 3.2 数据流程

```flow
1. 上传Excel
   ↓
2. 临时存储文件
   ↓
3. 检查是否有其他用户的未完成导入
   ↓
4. 分析冲突记录
   ↓
5. 返回冲突报告
   ↓
6. 用户确认决策
   ↓
7. 提交导入请求(含用户决策)
   ↓
8. 执行实际导入
   ↓
9. 记录导入结果
   ↓
10. 释放导入锁
```

### 3.3 组件设计

#### 3.3.1 后端组件

1. **导入锁定管理器**：
   - 维护全局导入锁，确保同一时间只有一个活跃导入会话
   - 提供锁定状态查询、创建锁和释放锁功能
   - **新增：会话过期检测和自动清理机制**

2. **冲突分析器**：
   - 分析Excel与现有数据的冲突
   - 生成详细的字段差异报告
   - **新增：分析进度实时反馈机制**
   - **新增：异步分析任务队列管理**

3. **导入执行器**：
   - 根据用户决策执行实际导入
   - 跟踪导入进度和结果
   - **新增：容错机制和事务保障**

#### 3.3.2 前端组件

1. **冲突确认对话框**：
   - 展示冲突记录和字段差异
   - 提供更新/跳过选择界面
   - 支持批量选择操作
   - **新增：高级筛选功能**
   - **新增：响应式布局设计**

2. **导入状态追踪器**：
   - 显示导入进度
   - 处理锁定状态和等待状态
   - **新增：采用React状态管理最佳实践**
   - **新增：断线重连和错误恢复机制**

3. **新增：活跃会话处理组件**：
   - 显示当前系统活跃会话状态
   - 提供会话接管和处理选项
   - 过期会话提示和清理

### 3.4 数据模型扩展

在现有的`ImportLog`模型基础上扩展冲突确认相关字段：

```python
# 新增字段
status_extra = models.JSONField(
    verbose_name="额外状态信息",
    default=dict,
    blank=True,
    help_text="存储额外状态信息，如冲突确认状态等"
)

# 新增状态值
IMPORT_STATUS_CHOICES = [
    # 现有状态...
    ('pending_confirmation', '等待确认'),
    # 其他状态...
]
```

### 3.5 优化设计考虑

1. **新增：前端异步状态管理优化**:
   - 替换传统轮询机制，采用React Query或SWR等现代库
   - 减少不必要的服务器请求，优化网络资源利用
   - 实现智能的后台数据同步与更新

2. **新增：组件结构优化**:
   - 拆分大型组件为更小的职责单一组件
   - 提取共用逻辑为自定义Hook
   - 避免props drilling，优化状态传递

3. **新增：可访问性设计**:
   - 添加适当的ARIA标签和角色
   - 确保键盘导航支持
   - 支持屏幕阅读器和辅助技术

## 4. API 设计

### 4.1 冲突分析 API

```api
POST /api/archive-records/analyze-import-conflicts/
```

**请求体**:

```json
{
  "file": (binary),
  "sheet_name": "Sheet1",
  "duplicate_strategy": "smart_update"
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "import_session_id": "abc123",
    "total_records": 100,
    "new_records": 80,
    "conflict_records": [
      {
        "row": 5,
        "commission_number": "C2023001",
        "sample_number": "S2023001",
        "field_differences": [
          {
            "field": "client_name",
            "field_label": "客户名称",
            "existing": "公司A",
            "imported": "公司A(新)"
          }
        ]
      }
    ]
  }
}
```

### 4.2 导入确认 API

```api
POST /api/archive-records/confirm-import/
```

**请求体**:

```json
{
  "import_session_id": "abc123",
  "conflict_resolutions": [
    {
      "commission_number": "C2023001",
      "row": 5,
      "action": "update"  // 或 "skip"
    }
  ]
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "import_log": {
      "id": "log123",
      "batch_number": "IMPORT-20230801-123",
      "status": "completed",
      "total_records": 100,
      "created_count": 80,
      "updated_count": 15,
      "skipped_count": 5
    }
  }
}
```

### 4.3 导入状态查询 API

```api
GET /api/archive-records/import-sessions/status/
```

**响应**:

```json
{
  "success": true,
  "data": {
    "active_session": {
      "user": "admin",
      "started_at": "2023-08-01T10:00:00Z",
      "status": "pending_confirmation"
    },
    "can_import": false
  }
}
```

### 4.4 新增：分析进度查询 API

```api
GET /api/archive-records/import-sessions/{session_id}/progress/
```

**响应**:

```json
{
  "success": true,
  "data": {
    "analyzed_records": 150,
    "total_records": 300,
    "analyzed_percentage": 50,
    "status": "analyzing"
  }
}
```

## 5. 实施计划与项目结构组织

### 5.1 项目结构组织

#### 5.1.1 后端代码结构

```c
backend/
├── archive_records/                       # 应用目录
│   ├── models/                            # 模型目录
│   │   ├── __init__.py                    # 模型初始化文件
│   │   ├── import_log.py                  # 导入日志模型(含扩展)
│   │   └── ...
│   ├── migrations/                        # 迁移文件目录
│   │   └── xxxx_add_status_extra_to_importlog.py  # 添加status_extra字段的迁移
│   ├── services/                          # 服务层目录
│   │   ├── __init__.py                    # 服务初始化文件
│   │   ├── excel_import.py                # 现有Excel导入服务
│   │   ├── excel_conflict_analyzer.py     # 新增冲突分析服务
│   │   └── import_session_manager.py      # 导入会话管理服务
│   ├── api/                               # API视图目录
│   │   ├── __init__.py                    # API初始化文件
│   │   ├── excel_import.py                # 现有Excel导入视图
│   │   └── excel_import_conflicts.py      # 新增冲突分析视图
│   ├── serializers/                       # 序列化器目录
│   │   ├── __init__.py                    # 序列化器初始化文件
│   │   └── excel_import.py                # Excel导入相关序列化器
│   ├── urls.py                            # URL配置
│   ├── apps.py                            # 应用配置
│   └── ...
└── ...
```

#### 5.1.2 前端代码结构

```c
frontend/
├── app/                                   # 主应用目录(Next.js App Router)
│   ├── records/                           # 记录模块
│   │   ├── import/                        # 导入功能
│   │   │   ├── page.tsx                   # 导入页面
│   │   │   ├── conflict/                  # 冲突处理(可选)
│   │   │   │   └── page.tsx               # 冲突处理页面
│   │   │   └── ...
│   │   └── ...
│   └── ...
├── components/                            # 组件目录
│   ├── records/                           # 记录相关组件
│   │   ├── import/                        # 导入相关组件
│   │   │   ├── ImportDialog.tsx           # 导入对话框 
│   │   │   ├── ConflictResolutionModal.tsx # 冲突解决对话框
│   │   │   ├── ImportProgress.tsx         # 导入进度组件
│   │   │   ├── ActiveSessionBanner.tsx    # 新增：活跃会话提示横幅
│   │   │   ├── ConflictTableRow.tsx       # 新增：冲突表格行(提取为组件)
│   │   │   ├── ConflictFilterBar.tsx      # 新增：冲突筛选栏
│   │   │   └── ...
│   │   └── ...
│   └── ...
├── hooks/                                 # 自定义Hook目录
│   ├── useImportSession.ts                # 导入会话管理Hook
│   ├── useImportLock.ts                   # 导入锁检查Hook
│   ├── useAnalysisProgress.ts             # 新增：分析进度追踪Hook
│   ├── useConflictResolution.ts           # 新增：冲突解决Hook
│   └── ...
├── services/                              # 服务目录
│   ├── excel-import-service.ts            # Excel导入服务(扩展)
│   └── ...
└── ...
```

### 5.2 核心文件实现

#### 5.2.1 后端核心文件

1. **模型扩展 (`archive_records/models/import_log.py`)**

   ```python
   # 现有ImportLog模型扩展
   class ImportLog(models.Model):
       # 现有字段...
       
       # 新增字段
       status_extra = models.JSONField(
           verbose_name="额外状态信息",
           default=dict,
           blank=True,
           help_text="存储额外状态信息，如冲突确认状态等"
       )
       
       # 扩展状态选项
       STATUS_CHOICES = [
           ('pending', '等待处理'),
           ('processing', '处理中'),
           ('completed', '已完成'),
           ('failed', '失败'),
           ('partial', '部分成功'),
           ('pending_confirmation', '等待确认'),  # 新增状态
       ]
       
       # 其余代码...
   ```

2. **导入会话管理器 (`archive_records/services/import_session_manager.py`)**

   ```python
   """
   Excel导入会话管理器
   
   用于管理Excel导入过程中的会话状态和锁定机制，确保同一时间只有一个用户可以进行导入操作。
   """
   
   import uuid
   import logging
   from datetime import datetime, timedelta
   from django.core.cache import cache
   from django.contrib.auth import get_user_model
   
   User = get_user_model()
   logger = logging.getLogger(__name__)
   
   # 会话和锁相关常量
   SESSION_PREFIX = "excel_import_session:"
   LOCK_KEY = "excel_import_lock"
   SESSION_TIMEOUT = 30 * 60  # 30分钟
   LOCK_TIMEOUT = 60 * 60  # 1小时
   
   class ImportSession:
       """导入会话类，记录一次导入过程的状态和数据"""
       
       def __init__(self, session_id, user_id, status="created", temp_file_path=None, 
                   conflict_data=None, created_at=None, updated_at=None, import_log_id=None):
           # 会话属性初始化...
    
   class ImportSessionManager:
       """导入会话管理器，处理导入过程中的会话和锁定"""
       
       def __init__(self, cache_timeout=SESSION_TIMEOUT):
           self.cache_timeout = cache_timeout
       
       def create_session(self, user_id, temp_file_path=None):
           """创建新的导入会话，如果当前有活跃会话则拒绝"""
           # 实现代码...
       
       def update_session(self, session_id, **updates):
           """更新会话状态和数据"""
           # 实现代码...
       
       def get_session(self, session_id):
           """获取指定ID的会话"""
           # 实现代码...
       
       def get_import_status(self):
           """获取当前导入状态信息"""
           # 实现代码...
       
       # 其他会话管理方法...
   
   # 创建全局导入会话管理器实例
   import_session_manager = ImportSessionManager()
   ```

3. **冲突分析服务 (`archive_records/services/excel_conflict_analyzer.py`)**

   ```python
   """
   Excel导入冲突分析器
   
   用于分析Excel文件导入时可能的冲突情况，识别同一编号但字段内容不同的记录。
   """
   
   import pandas as pd
   import logging
   from typing import Dict, List, Optional, Any, Tuple
   
   from django.db.models import QuerySet
   from archive_records.models import ArchiveRecord
   
   logger = logging.getLogger(__name__)
   
   class FieldDifference:
       """字段差异类，记录单个字段的差异信息"""
       # 实现代码...
   
   class ConflictRecord:
       """冲突记录类，记录一条存在冲突的记录及其字段差异"""
       # 实现代码...
   
   class ExcelConflictAnalyzer:
       """Excel导入冲突分析器，用于分析Excel文件与现有数据的冲突"""
       
       # 配置属性...
       
       def analyze_excel_file(self, file_path, sheet_name=0):
           """分析Excel文件中的冲突记录"""
           # 实现代码...
       
       # 其他辅助方法...
   
   # 创建全局Excel冲突分析器实例
   excel_conflict_analyzer = ExcelConflictAnalyzer()
   ```

4. **冲突分析API视图 (`archive_records/api/excel_import_conflicts.py`)**

   ```python
   """
   Excel导入冲突分析API视图
   """
   
   import os
   import logging
   import tempfile
   from django.http import JsonResponse
   from rest_framework.views import APIView
   from rest_framework.parsers import MultiPartParser, FormParser
   from rest_framework.permissions import IsAuthenticated
   
   from ..services.excel_conflict_analyzer import excel_conflict_analyzer
   from ..services.import_session_manager import import_session_manager
   
   logger = logging.getLogger(__name__)
   
   class ExcelImportConflictAnalysisView(APIView):
       """Excel导入冲突分析视图"""
       # 实现代码...
   
   class ConfirmImportView(APIView):
       """Excel导入确认视图"""
       # 实现代码...
   
   class ImportSessionStatusView(APIView):
       """导入会话状态查询API"""
       # 实现代码...
   ```

5. **URL配置 (`archive_records/urls.py`)**

   ```python
   from django.urls import path
   from .api import excel_import, excel_import_conflicts
   
   urlpatterns = [
       # 现有Excel导入API
       path('import-excel/', excel_import.ExcelImportView.as_view(), name='excel-import'),
       path('import-logs/', excel_import.ExcelImportLogListView.as_view(), name='excel-import-logs'),
       path('import-logs/<uuid:pk>/', excel_import.ExcelImportLogDetailView.as_view(), name='excel-import-log-detail'),
       
       # 新增冲突分析API
       path('analyze-import-conflicts/', excel_import_conflicts.ExcelImportConflictAnalysisView.as_view(), name='analyze-import-conflicts'),
       path('confirm-import/', excel_import_conflicts.ConfirmImportView.as_view(), name='confirm-import'),
       path('import-sessions/status/', excel_import_conflicts.ImportSessionStatusView.as_view(), name='import-session-status'),
   ]
   ```

#### 5.2.2 前端核心文件

1. **冲突确认模态框 (`frontend/components/records/import/ConflictResolutionModal.tsx`)**

   ```tsx
   /**
    * 冲突确认对话框组件
    * 
    * 用于展示Excel导入时发现的冲突记录，并允许用户决定更新或跳过。
    */
   
   import React, { useState, useEffect, useCallback } from 'react';
   import {
     Dialog,
     DialogContent,
     // 其他UI组件导入...
   } from '@/components/ui/dialog';
   import { ConflictTableRow } from './ConflictTableRow';
   import { ConflictFilterBar } from './ConflictFilterBar';
   import { useConflictResolution } from '@/hooks/useConflictResolution';
   
   // 类型定义...
   
   export interface ConflictResolutionModalProps {
     // 属性定义...
   }
   
   export const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
     isOpen,
     onClose,
     conflicts,
     onResolve,
     isProcessing
   }) => {
     // 使用自定义Hook管理冲突解决逻辑
     const { 
       filteredConflicts, 
       filterType, 
       setFilterType,
       updateAction,
       updateAllActions,
       // ...其他状态和方法
     } = useConflictResolution(conflicts);
     
     // 组件实现...
     
     // 渲染筛选栏
     const renderFilterBar = () => (
       <ConflictFilterBar 
         filterType={filterType}
         onFilterChange={setFilterType}
         onBatchUpdate={updateAllActions}
         conflictTypes={/* 从冲突记录中提取的冲突类型 */}
       />
     );
     
     // 渲染冲突表格
     const renderConflictTable = () => (
       // 表格实现...
     );
     
     // 返回组件JSX
     return (
       <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
           {/* 对话框内容 */}
         </DialogContent>
       </Dialog>
     );
   };
   ```

2. **Excel导入服务扩展 (`frontend/services/excel-import-service.ts`)**

   ```typescript
   /**
    * Excel导入服务
    * 
    * 提供与后端Excel导入API的交互功能，包括:
    * - 上传Excel文件
    * - 分析潜在冲突
    * - 确认导入决策
    * - 获取导入历史和状态
    */
   
   import Cookies from 'js-cookie';
   
   // 现有类型定义...
   
   // 新增类型定义
   export interface ImportAnalysisResult {
     // 类型定义...
   }
   
   // 新增：分析进度类型
   export interface AnalysisProgress {
     analyzed_records: number;
     total_records: number;
     analyzed_percentage: number;
     status: string;
   }
   
   // 新增：自定义错误类型
   export class ActiveSessionExistsError extends Error {
     constructor(message: string) {
       super(message);
       this.name = 'ActiveSessionExistsError';
     }
   }
   
   class ExcelImportService {
     // 现有方法...
     
     /**
      * 分析Excel文件中的潜在冲突
      */
     async analyzeExcelFile(file, options = {}, onProgress) {
       // 改进的实现代码...
       // 添加进度回调支持
       // 添加错误类型判断与处理
     }
     
     /**
      * 确认导入决策
      */
     async confirmImport(sessionId, resolutions) {
       // 实现代码...
     }
     
     /**
      * 获取导入会话状态
      */
     async getImportSessionStatus() {
       // 实现代码...
     }
     
     /**
      * 新增：获取分析进度
      */
     async getAnalysisProgress(sessionId) {
       try {
         const response = await fetch(`/api/archive-records/import-sessions/${sessionId}/progress/`, {
           method: 'GET',
           headers: {
             'Content-Type': 'application/json',
             'X-CSRFToken': Cookies.get('csrftoken') || '',
           },
         });
         
         if (!response.ok) {
           throw new Error(`获取分析进度失败: ${response.status}`);
         }
         
         const data = await response.json();
         return data.data as AnalysisProgress;
       } catch (error) {
         console.error('获取分析进度出错:', error);
         throw error;
       }
     }
     
     /**
      * 新增：获取活跃导入会话
      */
     async getActiveImportSession() {
       // 实现代码...
     }
     
     /**
      * 新增：获取分析结果
      */
     async getAnalysisResult(sessionId) {
       // 实现代码...
     }
   }
   
   // 导出单例
   export const excelImportService = new ExcelImportService();
   ```

3. **分析进度追踪Hook (`frontend/hooks/useAnalysisProgress.ts`)**

   ```typescript
   /**
    * 导入分析进度追踪Hook
    * 使用React Query优化异步状态管理
    */
   
   import { useEffect, useState, useRef } from 'react';
   import { useQuery, useQueryClient } from '@tanstack/react-query';
   import { excelImportService, AnalysisProgress } from '@/services/excel-import-service';
   
   export function useAnalysisProgress(sessionId: string, enabled: boolean = false) {
     const queryClient = useQueryClient();
     
     // 使用React Query管理异步状态
     const { 
       data: progress, 
       isLoading, 
       error,
       refetch 
     } = useQuery({
       queryKey: ['analysisProgress', sessionId],
       queryFn: () => excelImportService.getAnalysisProgress(sessionId),
       enabled: !!sessionId && enabled,
       refetchInterval: (data) => {
         // 智能调整轮询间隔
         if (!data) return 1000; // 初始1秒
         if (data.status === 'analyzing') return 500; // 分析中时500ms
         if (data.analyzed_percentage >= 100 || data.status === 'analyzed') return false; // 完成时停止轮询
         return 1000; // 默认1秒
       },
       // 乐观UI更新策略
       onSuccess: (data) => {
         if (data.analyzed_percentage >= 95 && data.status === 'analyzing') {
           // 平滑UI体验，保持在95%直到完成
           return { ...data, analyzed_percentage: 95 };
         }
         return data;
       }
     });
     
     // 提供简化的API和派生状态
     return {
       progress: progress || { analyzed_records: 0, total_records: 0, analyzed_percentage: 0, status: '' },
       isLoading,
       error,
       isComplete: progress?.status === 'analyzed' || progress?.analyzed_percentage >= 100,
       refetch
     };
   }
   ```

4. **冲突解决Hook (`frontend/hooks/useConflictResolution.ts`)**

   ```typescript
   /**
    * 冲突解决逻辑Hook
    * 提取冲突处理相关逻辑为可复用Hook
    */
   
   import { useState, useEffect, useMemo, useCallback } from 'react';
   import { ConflictRecord, ConflictResolutionAction } from '@/services/excel-import-service';
   
   export function useConflictResolution(conflicts: ConflictRecord[]) {
     // 状态管理
     const [records, setRecords] = useState<ConflictRecord[]>([]);
     const [filterType, setFilterType] = useState<string | null>(null);
     
     // 初始化记录
     useEffect(() => {
       if (conflicts && conflicts.length > 0) {
         // 确保每条记录有默认动作
         const recordsWithActions = conflicts.map(record => ({
           ...record,
           action: record.conflict_type === 'update' 
             ? ConflictResolutionAction.UPDATE 
             : record.conflict_type === 'identical'
               ? ConflictResolutionAction.SKIP
               : ConflictResolutionAction.CREATE
         }));
         setRecords(recordsWithActions);
       }
     }, [conflicts]);
     
     // 筛选记录
     const filteredRecords = useMemo(() => {
       if (!filterType || filterType === 'all') return records;
       return records.filter(record => record.conflict_type === filterType);
     }, [records, filterType]);
     
     // 更新单条记录动作
     const updateAction = useCallback((index: number, action: ConflictResolutionAction) => {
       setRecords(prev => {
         const updated = [...prev];
         updated[index] = { ...updated[index], action };
         return updated;
       });
     }, []);
     
     // 批量更新动作
     const updateAllActions = useCallback((action: ConflictResolutionAction, type?: string) => {
       setRecords(prev => 
         prev.map(record => {
           if (!type || type === 'all' || record.conflict_type === type) {
             return { ...record, action };
           }
           return record;
         })
       );
     }, []);
     
     // 提取冲突类型列表
     const conflictTypes = useMemo(() => {
       const types = new Set(records.map(record => record.conflict_type));
       return Array.from(types);
     }, [records]);
     
     // 返回状态和方法
     return {
       records,
       filteredRecords,
       filterType,
       setFilterType,
       updateAction,
       updateAllActions,
       conflictTypes,
       // 转换为导入服务所需格式的工具方法
       getResolutionsForSubmit: () => {
         return records.map(({ commission_number, action }) => ({
           commission_number,
           action
         }));
       }
     };
   }
   ```

### 5.3 实施阶段规划

#### 5.3.1 阶段一：基础设施和后端实现 (2周)

1.**Week 1: 基础设施准备**

- 模型扩展和数据库迁移
- 导入会话管理器实现
- 基本单元测试

2.**Week 2: 后端API实现**

- 冲突分析服务实现
- API视图实现
- URL配置和集成测试

#### 5.3.2 阶段二：前端实现和集成 (2周)

3.**Week 3: 前端组件开发**

- 冲突确认模态框实现
- 导入服务扩展
- 导入会话Hook实现

4.**Week 4: 前后端集成与测试**

- 组件集成
- 端到端测试
- 用户体验优化

#### 5.3.3 阶段三：优化和部署 (1周)

5.**Week 5: 优化和部署准备**

- 性能优化
- 边界情况处理
- 部署文档和回滚计划
- 用户手册编写
- **新增：实现React Query/SWR异步状态管理优化**
- **新增：组件重构与代码质量优化**
- **新增：可访问性增强**

### 5.4 测试策略

1. **单元测试**
   - 后端服务和组件单元测试
   - 前端组件和Hook单元测试

2. **集成测试**
   - API端点测试
   - 导入流程测试
   - 并发操作测试

3. **端到端测试**
   - 完整导入流程测试
   - 用户界面交互测试
   - 边界条件测试

4. **性能测试**
   - 大数据量导入测试
   - 响应时间测试

## 6. 部署与回滚计划

### 6.1 部署步骤

1. **数据库迁移**
   - 应用迁移文件
   - 验证数据库结构

2. **后端部署**
   - 更新代码
   - 重启服务
   - 验证API可用性

3. **前端部署**
   - 构建前端资源
   - 部署静态文件
   - 验证前端功能

### 6.2 回滚计划

1. **回滚触发条件**
   - 导入功能完全不可用
   - 数据丢失或损坏
   - 严重性能问题

2. **回滚步骤**
   - 恢复前一版本代码
   - 回滚数据库迁移
   - 通知用户暂时使用旧版功能

### 6.3 监控与问题排查

1. **日志监控**
   - 关注导入相关错误日志
   - 监控导入会话状态

2. **性能监控**
   - 监控API响应时间
   - 监控内存使用情况

## 7. 总结

本设计方案通过引入两阶段导入流程和用户确认机制，解决了Excel导入过程中冲突处理的问题。系统将展示冲突记录及详细差异，允许用户针对每条冲突记录做出决策，从而提高数据质量和用户体验。同时，导入锁定机制确保了系统数据的一致性和可靠性。

通过优化设计，我们提高了系统的性能和用户体验，特别在以下几个方面：

1. **实时进度反馈**：提供分析和导入过程的实时进度
2. **现代化状态管理**：使用React Query替代传统轮询，减少不必要的API请求
3. **组件解耦与复用**：提取通用逻辑为自定义Hook，拆分大型组件
4. **增强错误处理与会话管理**：改进错误类型识别和处理，增强会话过期管理
5. **响应式与可访问性**：确保在各种设备上的良好体验，提高可访问性

实施计划提供了清晰的项目结构组织和分阶段实现策略，确保功能能够高质量地交付。建议按照阶段规划逐步实施，并通过全面的测试策略确保功能的稳定性和可靠性。

## 附录

### A. 风险提示

1. **关于`skip`策略的风险**：
   - 当使用`skip`策略时，系统会跳过所有已存在相同委托编号的记录，不管字段是否有变化
   - 这可能导致有价值的更新被忽略
   - 建议优先使用`smart_update`策略，结合用户确认功能
   - 特殊情况下谨慎使用`skip`策略

2. **关于两种重复情况的区分**：
   - "完全重复条目跳过"（skip策略）和"字段无变化忽略"（unchanged）是两种不同情况
   - 前者是一种策略选择，不检查字段变化直接跳过所有重复记录
   - 后者是在smart_update策略下，经详细比较发现确实没有变化而做的忽略处理

### B. 参考资料

1. 现有Excel导入服务文档
2. Django缓存机制文档
3. React状态管理最佳实践

### C. 代码优化建议（新增）

1. **状态管理最佳实践**：
   - 大型组件使用useReducer替代多个useState
   - 使用React Context进行深层状态传递
   - 采用React Query或SWR进行服务器状态管理

2. **性能优化技巧**：
   - 使用React.memo防止不必要的渲染
   - 合理使用useMemo和useCallback
   - 考虑虚拟滚动处理大量数据

3. **TypeScript类型安全**：
   - 使用严格的类型定义代替any
   - 利用类型收窄技术增强安全性
   - 对API响应使用精确的类型定义

4. **可访问性增强示例**：

   ```tsx
   // 扩展表格组件的可访问性
   <Table aria-label="冲突记录表格">
     <TableHeader>
       <TableRow>
         <TableHead aria-sort="none">委托编号</TableHead>
         {/* 其他表头 */}
       </TableRow>
     </TableHeader>
     {/* 表格内容 */}
   </Table>
   ```

5. **错误处理最佳实践**：

   ```typescript
   try {
     const result = await api.call();
     // 成功处理
   } catch (error) {
     // 类型化错误处理
     if (error instanceof ActiveSessionExistsError) {
       // 处理特定错误类型
     } else if (error instanceof NetworkError) {
       // 处理网络错误
     } else {
       // 处理未知错误
     }
   } finally {
     // 清理代码
   }
   ```

### D. 改进优先级排序

为确保项目实施有序推进，以下是按优先级排序的改进建议：

#### P0（必须实施）- 核心功能和稳定性

1. **异步状态管理优化**
   - 使用React Query/SWR替代手动轮询
   - 原因：显著降低服务器负载，优化网络请求，提高响应性
   - 实施难度：中等，但收益极高
   - 示例实现：

     ```typescript
     // 使用React Query优化分析进度查询
     const { data, isLoading } = useQuery(
       ['analysisProgress', sessionId],
       () => excelImportService.getAnalysisProgress(sessionId),
       {
         enabled: !!sessionId && currentStep === 'analyze',
         refetchInterval: (data) => 
           data?.status === 'analyzing' ? 500 : 
           data?.analyzed_percentage >= 100 ? false : 1000
       }
     );
     ```

2. **安全性增强**
   - 严格的文件类型验证和MIME检查
   - 上传大小限制实施
   - 原因：防止安全漏洞，保护系统安全
   - 示例实现：

     ```typescript
     // 前端文件验证
     const validateFile = (file: File): boolean => {
       const validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
       const maxSize = 10 * 1024 * 1024; // 10MB
       
       if (!validTypes.includes(file.type)) {
         toast({
           title: "文件类型错误",
           description: "请上传Excel格式的文件（.xlsx或.xls）",
           variant: "destructive",
         });
         return false;
       }
       
       if (file.size > maxSize) {
         toast({
           title: "文件过大",
           description: "文件大小不能超过10MB",
           variant: "destructive",
         });
         return false;
       }
       
       return true;
     };
     ```

#### P1（应当实施）- 用户体验和代码质量

3. **代码重构**
   - 提取`handleProcessActiveSession`与`handleFileUpload`的共用逻辑
   - 使用useReducer替代多个useState
   - 原因：提高代码可维护性，减少潜在bug
   - 示例实现：

     ```typescript
     // 提取共享的处理逻辑到独立函数
     const processExcelAnalysis = async (sessionId: string) => {
       setIsProcessing(true);
       try {
         // 共享的分析处理逻辑
         return await excelImportService.getAnalysisResult(sessionId);
       } catch (error) {
         // 统一错误处理
       } finally {
         setIsProcessing(false);
       }
     };
     
     // useReducer统一状态管理
     const initialState = {
       step: 'select',
       isUploading: false,
       analysisResult: null,
       // ...其他状态
     };
     
     function reducer(state, action) {
       switch (action.type) {
         case 'START_UPLOAD':
           return { ...state, step: 'upload', isUploading: true };
         // ...其他action处理
       }
     }
     ```

4. **组件拆分**
   - 将`renderConflictDialog`拆分为独立组件
   - 拆分大型组件为功能单一的小组件
   - 原因：改善代码组织，提高复用性和测试便捷性

5. **类型安全增强**
   - 严格类型定义和类型收窄
   - 精确的API响应类型定义
   - 原因：减少运行时错误，提高代码自文档化能力
   - 示例实现：

     ```typescript
     // 精确的API响应类型
     interface AnalysisResponse {
       success: boolean;
       data: {
         import_session_id: string;
         total_records: number;
         new_records: number;
         conflict_records: ConflictRecord[];
         // 其他字段...
       }
     }
     
     // 类型收窄函数
     function isActiveSessionError(error: unknown): error is ActiveSessionExistsError {
       return error instanceof Error && error.name === 'ActiveSessionExistsError';
     }
     ```

#### P2（建议实施）- 增强功能和可访问性

6. **响应式设计优化**
   - 移动设备上的布局优化
   - 表格组件的响应式调整
   - 原因：提升各种设备上的用户体验

7. **可访问性改进**
   - ARIA属性补充
   - 键盘导航支持
   - 屏幕阅读器兼容性
   - 原因：扩大应用可用群体，符合Web标准

#### P3（可选实施）- 高级优化

8. **高级UI功能**
   - 虚拟滚动处理大量记录
   - 按字段筛选功能扩展
   - 原因：提升处理大数据集时的性能和用户体验
   - 示例实现：

     ```typescript
     // 使用react-window实现虚拟滚动
     import { FixedSizeList } from 'react-window';
     
     const ConflictList = ({ items }) => (
       <FixedSizeList
         height={500}
         width="100%"
         itemCount={items.length}
         itemSize={50}
       >
         {({ index, style }) => (
           <ConflictItem 
             style={style}
             item={items[index]} 
             index={index}
           />
         )}
       </FixedSizeList>
     );
     ```

9. **离线支持和状态恢复**
   - 断线重连机制
   - 会话状态本地存储
   - 原因：提高网络不稳定环境下的用户体验

此优先级排序旨在指导实施计划，确保资源分配合理，最重要和收益最高的改进优先实施。优先级P0和P1的改进应在首次迭代中完成，P2可在次要迭代中实施，而P3可根据实际需求和资源状况灵活决定。
