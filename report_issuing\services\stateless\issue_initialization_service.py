# report_issuing/services/stateless/issue_initialization_service.py
import logging
from typing import Dict, Any, List

from report_issuing.services.utility_services.issue_rule_service import IssueRuleService
from report_issuing.services.data_services.issue_form_data_service import IssueFormDataService
from django.contrib.auth.models import User

# 在项目实际的异常模块中定义
class InvalidOperationError(Exception):
    pass

logger = logging.getLogger(__name__)

class IssueInitializationService:
    """
    负责初始化一个新的发放流程，核心职责是创建包含初始条目的草稿发放单。
    这是上层（如API视图）应调用的主要服务。
    """

    def __init__(self, user_id: int):
        self.user_id = user_id
        try:
            self.user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"初始化服务失败：用户ID {user_id} 不存在。")
            raise InvalidOperationError(f"用户ID {user_id} 不存在。")

        self.rule_service = IssueRuleService(user_id)
        self.data_service = IssueFormDataService()

    def create_draft_issue_form_with_items(
        self, 
        form_data: Dict[str, Any], 
        items_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        一步到位：在一个事务中，验证并创建一个带初始条目的新草稿发放单。

        Args:
            form_data: 发放单的基础数据 (e.g., title, remarks)。
            items_data: 初始条目列表 (e.g., [{'archive_record_id': 1, 'copies': 1}])

        Returns:
            一个表示新创建的发放单的字典。
            
        Raises:
            InvalidOperationError: 如果任何一个条目不满足发放条件。
        """
        logger.info(f"用户 {self.user_id} 正在初始化一个包含 {len(items_data)} 个条目的新发放流程。")

        # 1. 验证所有待添加条目的业务规则
        validated_items = []
        for item in items_data:
            archive_commission_number = item.get('archive_record_id')  # 前端传递的是委托编号，不是数据库ID
            requested_copies = item.get('copies')
            
            if not archive_commission_number or not requested_copies:
                raise InvalidOperationError(f"请求条目数据不完整: {item}")

            # 调用"模型函数"进行计算和验证，传递委托编号
            issue_rules = self.rule_service.calculate_issue_quantity(archive_commission_number)
            
            if not issue_rules['can_issue']:
                error_msg = f"验证失败：档案条目 {archive_commission_number} 已无法发放。原因: {issue_rules.get('reason', '未知')}"
                logger.error(error_msg)
                raise InvalidOperationError(error_msg)

            if requested_copies not in issue_rules['available_options']:
                error_msg = f"验证失败：为档案条目 {archive_commission_number} 请求的发放份数 {requested_copies} 不合法。允许的选项为: {issue_rules['available_options']}"
                logger.error(error_msg)
                raise InvalidOperationError(error_msg)
                
            validated_items.append({
                'archive_record_id': archive_commission_number,  # 保持原字段名，供DataService使用
                'copies': requested_copies,
                'issue_type': issue_rules['issue_type']
            })
        
        logger.info("所有待添加条目均已通过业务规则验证。")

        # 2. 调用数据服务，在一个事务中完成数据库操作
        try:
            new_issue_form = self.data_service.create_form_with_items(
                form_data=form_data,
                items_data=validated_items,
                user=self.user
            )
            logger.info(f"成功创建发放单，ID: {new_issue_form.id}")

        except Exception as e:
            logger.error(f"在数据层创建发放单时发生错误: {e}")
            # 此处可根据需要重新抛出异常或进行其他处理
            raise InvalidOperationError(f"创建发放单时数据库出错: {e}")
        
        # 3. 返回结果
        return new_issue_form 