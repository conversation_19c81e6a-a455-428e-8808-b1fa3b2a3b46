import { ReportDistribution, ReportDistributionFormData } from "@/hooks/domain/issue/use-report-distribution"
import apiClient, { ApiResponse } from "@/lib/apiClient";

// 导入SelectedRecord类型
import type { SelectedRecord } from "@/hooks/domain/issue/useReportListService";

// const API_BASE_URL = "/api/report-issuing" // 后端API基础路径 - 不再需要，由 fetchApi 处理

// --- 数据转换工具函数 ---

/**
 * 将前端records格式转换为后端items_data格式
 * @param records 前端选择的记录数组
 * @returns 后端期望的items_data格式 (camelCase)
 */
export function convertRecordsToItemsData(records: SelectedRecord[]): { archiveRecordId: string; copies: number }[] {
  return records.map(record => ({
    archiveRecordId: record.id,
    copies: record.issueCopies || 1
  }));
}

/**
 * 计算总份数
 * @param records 选择的记录数组
 * @returns 总份数
 */
export function calculateTotalCopies(records: SelectedRecord[]): number {
  return records.reduce((sum, record) => {
    const copies = record.totalCopies || 1;
    return sum + copies;
  }, 0);
}

/**
 * 检查记录是否已被选择
 * @param recordId 记录ID
 * @param selectedReports 已选择的记录数组
 * @returns 是否已选择
 */
export function isRecordSelected(recordId: string, selectedReports: SelectedRecord[]): boolean {
  return selectedReports.some((record) => record.id === recordId);
}

/**
 * 🎯 简化后的服务层架构说明：
 * 
 * 1. 工具函数：convertRecordsToItemsData, calculateTotalCopies, isRecordSelected
 *    - 提供数据转换和计算功能
 *    - 可在多处复用
 * 
 * 2. 核心API函数：createIssueForm, updateIssueForm, getIssueFormById
 *    - 直接对应后端API
 *    - Hook层直接调用，无需中间层
 * 
 * 3. reportDistributionService 对象：
 *    - 保留向后兼容的接口
 *    - 包含模拟实现的其他功能
 */

// --- 核心API函数 ---

/**
 * 创建一个新的草稿发放单
 * @param formData 发放单基础数据
 * @param itemsData 发放单条目数据 (camelCase格式)
 * @returns 后端返回的发放单对象
 */
export async function createIssueForm(
  formData: ReportDistributionFormData,
  itemsData: { archiveRecordId: string; copies: number }[]
): Promise<ReportDistribution> {
  const response = await apiClient.post<ReportDistribution>('/api/report-issuing/issue-forms/', {
    formData: formData,
    itemsData: itemsData,
  });

  if (!response.success || !response.data) {
    throw new Error(response.error || '未能创建发放单');
  }

  return response.data;
}

/**
 * 根据业务标识符获取发放单详情
 * @param issueNumber 发放单业务标识符
 * @returns 发放单对象
 */
export async function getIssueFormById(issueNumber: string): Promise<ReportDistribution> {
  const response = await apiClient.get<ReportDistribution>(`/api/report-issuing/issue-forms/${issueNumber}/`);
  if (!response.success || !response.data) {
    throw new Error(response.error || `无法获取业务标识符为 ${issueNumber} 的发放单`);
  }
  return response.data;
}

/**
 * 更新报告发放单（草稿）
 * @param id 发放单ID
 * @param formData 发放单基础数据
 * @param itemsData 发放单条目数据 (camelCase格式)
 * @returns 后端返回的发放单对象
 */
export async function updateIssueForm(
  id: string,
  formData: ReportDistributionFormData,
  itemsData: { archiveRecordId: string; copies: number }[]
): Promise<ReportDistribution> {
  const response = await apiClient.put<ReportDistribution>(`/api/report-issuing/issue-forms/${id}/`, {
    formData: formData,
    itemsData: itemsData,
  });

  if (!response.success || !response.data) {
    throw new Error(response.error || '未能更新发放单');
  }

  return response.data;
}

// --- 服务对象 ---

export const reportDistributionService = {
  // 核心API函数 - 现在直接导出，不需要在这里重复
  getReportById: getIssueFormById,

  // --- 以下是其他操作的模拟实现，待后续替换 ---
  saveAsDraft: async (report: any) => {
    console.log("模拟保存草稿:", report)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true, id: report.issue_number || "ISSUE-" + new Date().toISOString().slice(0,10).replace(/-/g,'') + "-" + Math.random().toString(16).substr(2,4).toUpperCase() }
  },

  lockReport: async (id: string) => {
    console.log(`模拟锁定: ${id}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  unlockReport: async (id: string) => {
    console.log(`模拟解锁: ${id}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  issueReport: async (id: string) => {
    console.log(`模拟发放: ${id}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  printReport: async (id: string) => {
    console.log(`模拟打印: ${id}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  deleteReport: async (id: string) => {
    console.log(`模拟删除: ${id}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  updateReportIssuedTime: async (id: string, time: string) => {
    console.log(`模拟更新发放时间: ${id}, ${time}`)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { success: true }
  },

  // 获取报告发放单列表
  getReports: (page: number) => {
    // Implementation of getReports function
  },

  // 通用的状态更新方法
  async updateStatus(id: string, action: string, reason?: string): Promise<{ success: boolean, data?: ReportDistribution, error?: string }> {
    try {
      const response = await apiClient.patch(`/api/report-issuing/issue-forms/${id}/status/`, { action, reason });
      if (response.success && response.data) {
        console.log(`[Service] updateStatus ${action} 成功`, response.data);
        return { success: true, data: response.data };
      } else {
        const errorMessage = response.error || `未能${action}该报告。`;
        console.error(`[Service] updateStatus ${action} 失败:`, errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error: any) {
      console.error(`[Service] updateStatus ${action} 异常:`, error);
      const errorMsg = error.response?.data?.error || error.message || "服务器发生未知错误";
      return { success: false, error: errorMsg };
    }
  },
}

// Local storage utilities for report distributions
export const localStorageService = {
  getItem: (key: string): string | null => {
    if (typeof window === "undefined") return null
    return localStorage.getItem(key)
  },

  setItem: (key: string, value: string): void => {
    if (typeof window === "undefined") return
    localStorage.setItem(key, value)
  },

  removeItem: (key: string): void => {
    if (typeof window === "undefined") return
    localStorage.removeItem(key)
  },
}
