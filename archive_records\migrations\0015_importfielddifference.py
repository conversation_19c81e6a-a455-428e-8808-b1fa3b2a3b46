# Generated by Django 5.1.11 on 2025-06-19 16:33

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('archive_records', '0014_alter_archiverecord_first_issue_copies_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportFieldDifference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='差异ID')),
                ('field_name', models.CharField(db_index=True, max_length=255, verbose_name='字段名')),
                ('existing_value', models.JSONField(blank=True, null=True, verbose_name='数据库现有值')),
                ('imported_value', models.JSONField(blank=True, null=True, verbose_name='Excel导入值')),
                ('conflict_detail', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_differences', to='archive_records.importconflictdetail', verbose_name='关联冲突详情')),
            ],
            options={
                'verbose_name': '导入字段差异',
                'verbose_name_plural': '导入字段差异',
                'ordering': ['conflict_detail', 'field_name'],
                'indexes': [models.Index(fields=['conflict_detail', 'field_name'], name='archive_rec_conflic_33b0a5_idx')],
            },
        ),
    ]
