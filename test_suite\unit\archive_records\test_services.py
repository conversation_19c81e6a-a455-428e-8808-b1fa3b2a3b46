"""
Excel导入服务测试模块
"""
import os
import pandas as pd
from django.test import TestCase
from django.contrib.auth.models import User
from archive_records.models import ArchiveRecord, ImportLog
from archive_records.services.excel_import import ExcelImportService
from archive_records.services.archive_status_service import ArchiveStatusService
from test_suite.utils.test_helpers import get_test_file_path
from django.utils import timezone
from django.conf import settings
import random
import tempfile
from datetime import datetime
import logging

class ExcelImportServiceTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='12345')
        self.service = ExcelImportService()
        
        # 测试文件路径
        self.test_file_path = get_test_file_path('excel', 'valid_data.xlsx')
        self._temp_files = []  # 用于跟踪测试创建的临时文件

    def tearDown(self):
        """清理测试环境，删除临时文件"""
        for file_path in self._temp_files:
            if os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                except Exception as e:
                    print(f"清理临时文件失败: {file_path}, 错误: {str(e)}")

    def _create_test_file(self, data, file_name):
        """创建用于测试的Excel文件（仅用于测试）"""
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            file_path = temp_file.name
        
        # 保存DataFrame到Excel文件
        df.to_excel(file_path, index=False)
        
        # 记录临时文件路径以便测试结束后清理
        self._temp_files.append(file_path)
        
        return file_path

    def test_real_file_import(self):
        """测试实际Excel文件导入"""
        # 修改：确保测试前清理数据或使用不同的标识符
        # 如果测试数据中有固定的ID，需要移除或修改
        file_path = os.path.join(settings.BASE_DIR, 'test_suite', 'test_files', 'excel', 'valid_data.xlsx')
        
        # 使用随机用户名，避免冲突
        random_suffix = random.randint(1000, 9999)
        user = User.objects.create_user(f'testuser{random_suffix}', '<EMAIL>', 'password')
        
        # 确保文件存在
        self.assertTrue(
            os.path.exists(file_path),
            f"测试文件不存在: {file_path}"
        )
        
        # 添加额外的诊断信息
        print("\n----- 开始文件内容诊断 -----")
        df = pd.read_excel(file_path)
        print(f"Excel文件路径: {file_path}")
        print(f"Excel文件行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查列名中是否有Unicode无法显示的字符
        for col in df.columns:
            print(f"列名: '{col}' | 长度: {len(col)} | 字节: {col.encode('utf-8').hex()}")
        
        # 打印第一行数据作为样本
        if len(df) > 0:
            print("\n第一行数据:")
            row = df.iloc[0]
            for col in df.columns:
                value = row[col]
                value_type = type(value).__name__
                value_str = str(value)
                print(f"  {col}: ({value_type}) {value_str}")
        print("----- 诊断结束 -----\n")
        
        # 执行导入
        import_log = self.service.import_from_file(
            file_path,
            user=user,
            duplicate_strategy='skip'  # 或使用其他策略
        )
        
        # 打印导入结果
        print(f"\n导入状态: {import_log.status}")
        print(f"总记录数: {import_log.total_records}")
        print(f"成功记录数: {import_log.success_records}")
        print(f"失败记录数: {import_log.failed_records}")
        
        # 验证导入结果
        self.assertIn(import_log.status, ['completed', 'partial', 'failed'])
        
        # 如果有成功导入的记录，验证记录是否正确创建
        if import_log.success_records > 0:
            records = ArchiveRecord.objects.all()
            self.assertTrue(records.exists())
            
            # 验证第一条记录的关键字段
            first_record = records.first()
            self.assertIsNotNone(first_record.sample_number)
            self.assertIsNotNone(first_record.project_name)

    def test_error_handling(self):
        """测试错误处理"""
        # 这里可以使用代码创建的无效数据进行测试
        ... 

    def test_unified_number_generation(self):
        """测试导入时自动生成统一编号"""
        import_log = self.service.import_from_file(
            self.test_file_path,
            user=self.user
        )
        
        # 验证导入成功
        self.assertEqual(import_log.status, 'completed')
        
        # 验证统一编号生成
        records = ArchiveRecord.objects.all()
        for record in records:
            self.assertEqual(
                record.unified_number,
                record.commission_number,
                f"统一编号应该与委托编号相同: {record.commission_number}"
            )

    def test_smart_update_strategy(self):
        """测试智能更新策略"""
        # 先创建记录
        initial_data = {
            '样品编号': ['S001'],
            '委托编号': ['C001'],
            '委托日期': ['2023-01-01'],
            '工程名称': ['初始项目'],
            '委托单位': ['初始单位'],
            '结论': ['初始结论']
        }
        
        # 创建初始数据文件
        initial_file_path = self._create_test_file(initial_data, 'initial_data.xlsx')
        
        # 导入初始数据
        self.service.import_from_file(
            initial_file_path,
            user=self.user
        )
        
        # 准备更新数据
        update_data = {
            '样品编号': ['S001'],
            '委托编号': ['C001'],  # 相同的委托编号，应该更新而不是创建
            '委托日期': ['2023-01-01'],
            '工程名称': ['更新后的项目'],  # 更新的字段
            '委托单位': ['更新后的单位'],  # 更新的字段
            '结论': ['更新后的结论']      # 更新的字段
        }
        
        # 创建更新数据文件
        update_file_path = self._create_test_file(update_data, 'update_data.xlsx')
        
        # 然后测试智能更新
        import_log = self.service.import_from_file(
            update_file_path,  # 现在这个变量已定义
            user=self.user,
            duplicate_strategy='smart_update'
        )
        
        # 验证记录被更新而不是重复创建
        records = ArchiveRecord.objects.filter(sample_number='S001')
        self.assertEqual(records.count(), 1, "应该只有一条记录，而不是创建了新记录")
        
        # 验证字段已更新
        record = records.first()
        self.assertEqual(record.project_name, '更新后的项目')
        self.assertEqual(record.client_unit, '更新后的单位')
        self.assertEqual(record.conclusion, '更新后的结论')

    def test_skip_strategy(self):
        """测试跳过策略"""
        # ... 类似代码 ...

    def test_debug_full_import_process(self):
        """调试完整导入过程"""
        print("\n===== 调试完整导入过程 =====")
        print(f"测试文件路径: {self.test_file_path}")
        
        # 捕获并记录导入服务的详细日志
        logger = logging.getLogger('archive_records.services.excel_import')
        original_level = logger.level
        logger.setLevel(logging.DEBUG)
        
        # 添加控制台处理器以直接显示日志
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        logger.addHandler(console_handler)
        
        try:
            # 运行导入
            import_log = self.service.import_from_file(
                self.test_file_path,
                user=self.user
            )
            
            # 打印导入结果
            print(f"\n导入状态: {import_log.status}")
            print(f"总记录数: {import_log.total_records}")
            print(f"成功记录数: {import_log.success_records}")
            print(f"失败记录数: {import_log.failed_records}")
            
            # 如果有错误记录，打印详细信息
            if hasattr(import_log, 'error_details') and import_log.error_details:
                print("\n错误详情:")
                print(import_log.error_details)
            
            # 尝试检查第一行的处理
            try:
                df = pd.read_excel(self.test_file_path)
                if not df.empty:
                    first_row = df.iloc[0]
                    print("\n第一行数据:")
                    for col in df.columns:
                        print(f"  {col}: {first_row[col]} (类型: {type(first_row[col]).__name__})")
                    
                    # 手动尝试处理第一行
                    print("\n尝试手动处理第一行:")
                    try:
                        # 将Series转换为字典
                        row_dict = first_row.to_dict()
                        # 获取字段映射
                        field_mapping = {v: k for k, v in self.service.field_mapping.items()}
                        
                        print("检查必填字段:")
                        required_fields = ['委托编号', '样品编号', '工程名称', '委托单位', '委托日期']
                        for field in required_fields:
                            if field in row_dict:
                                print(f"  {field}: {row_dict[field]}")
                            else:
                                print(f"  {field}: 缺失!")
                    except Exception as e:
                        print(f"手动处理第一行时出错: {str(e)}")
            except Exception as e:
                print(f"读取Excel文件时出错: {str(e)}")
        
        finally:
            # 恢复原始日志级别
            logger.setLevel(original_level)
            logger.removeHandler(console_handler)
        
        print("===== 调试完整导入过程完成 =====\n")

class ArchiveStatusServiceTest(TestCase):
    def setUp(self):
        self.service = ArchiveStatusService()
        
        # 创建测试数据
        self.test_records = [
            ArchiveRecord.objects.create(
                sample_number=f'Sample{i}',
                commission_number=f'Commission{i}',
                unified_number=f'Commission{i}'  # 与委托编号相同
            ) for i in range(1, 4)
        ]

    def test_update_archive_status(self):
        """测试更新档案状态"""
        # 模拟PDF处理识别到的统一编号
        unified_codes = {
            1: 'Commission1',
            2: 'Commission2',
            3: 'NonExistent'  # 测试不存在的编号
        }
        
        # 执行状态更新
        result = self.service.update_archive_status(unified_codes)
        
        # 验证结果
        self.assertEqual(result['total'], 3)
        self.assertEqual(result['updated'], 2)
        self.assertEqual(result['not_found'], 1)
        self.assertIn('not_found_numbers', result)
        self.assertEqual(len(result['not_found_numbers']), result['not_found'])
        
        # 验证数据库记录
        record1 = ArchiveRecord.objects.get(unified_number='Commission1')
        self.assertEqual(record1.archive_status, '已归档')
        
        record2 = ArchiveRecord.objects.get(unified_number='Commission2')
        self.assertEqual(record2.archive_status, '已归档')

    def test_empty_unified_codes(self):
        """测试空的统一编号列表"""
        result = self.service.update_archive_status({})
        self.assertEqual(result['total'], 0)
        self.assertEqual(result['updated'], 0)

