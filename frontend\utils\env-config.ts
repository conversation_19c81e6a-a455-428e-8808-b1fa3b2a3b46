/**
 * 环境变量配置工具
 * 
 * 提供统一的环境变量访问接口，简化环境配置管理
 * 集中处理环境变量默认值和验证逻辑
 */

/**
 * 获取API基础URL
 * @returns API基础URL，包含结尾斜杠
 */
export function getApiBaseUrl(): string {
  // 使用统一的API前缀，由Nginx管理真实路径
  return '/api/';
}

/**
 * 获取API请求完整URL
 * @param endpoint API端点路径(不含前导斜杠)
 * @returns 完整的API请求URL
 */
export function getApiUrl(endpoint: string): string {
  // 确保endpoint不以斜杠开头
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  
  // 确保endpoint以斜杠结尾（Django需要）
  const endpointWithSlash = cleanEndpoint.endsWith('/') ? cleanEndpoint : `${cleanEndpoint}/`;
  
  // 使用统一API前缀
  return `/api/${endpointWithSlash}`;
}

/**
 * 打印当前环境配置（用于调试）
 */
export function logEnvironmentConfig(): void {
  console.log('=== 环境配置 ===');
  console.log(`环境: ${process.env.NODE_ENV}`);
  console.log(`API基础URL: ${getApiBaseUrl()}`);
  console.log('================');
}

// 导出环境变量配置对象(方便一次性导入多个值)
export const envConfig = {
  apiBaseUrl: getApiBaseUrl(),
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
};

// 在开发环境下启动时自动打印配置
if (process.env.NODE_ENV === 'development') {
  // 使用setTimeout确保在客户端渲染时执行
  if (typeof window !== 'undefined') {
    setTimeout(() => logEnvironmentConfig(), 0);
  }
} 