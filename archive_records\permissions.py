from rest_framework import permissions

class IsChangeOrderOwnerOrAdmin(permissions.BasePermission):
    """
    只允许更改单创建者或管理员访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问任何更改单
        if request.user.is_staff or request.user.is_superuser:
            return True
        
        # 创建者可以访问自己创建的更改单
        if obj.created_by == request.user:
            return True
        
        return False


class CanManageChangeOrder(permissions.BasePermission):
    """
    检查用户是否有权管理（提交、执行、取消等）更改单
    根据更改单状态和用户角色确定权限
    """
    
    def has_permission(self, request, view):
        # 所有认证用户都可以查看列表
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 检查是否有创建更改单的权限
        if view.action == 'create':
            return request.user.has_perm('archive_records.add_changeorder')
        
        return True
    
    def has_object_permission(self, request, view, obj):
        # 管理员有全部权限
        if request.user.is_staff or request.user.is_superuser:
            return True
        
        # 只有创建者才能管理草稿状态的更改单
        if obj.status == 'draft' and obj.created_by == request.user:
            return True
        
        # 创建者不能执行自己的更改单（四眼原则）
        if view.action == 'perform_action':
            action = request.data.get('action')
            if action == 'execute' and obj.created_by == request.user:
                return False
        
        # 其他情况按原始逻辑处理
        return obj.created_by == request.user


class CanManageChangeOrder(permissions.BasePermission):
    """
    权限检查：检查用户是否有执行更改单特定操作的权限
    """
    
    def has_permission(self, request, view):
        # 所有认证用户都可以查看列表
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 检查是否有创建更改单的权限
        if view.action == 'create':
            return request.user.has_perm('archive_records.add_changeorder')
        
        return True
    
    def has_object_permission(self, request, view, obj):
        # 允许安全方法（GET, HEAD, OPTIONS）
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 创建者或管理员可以操作自己的更改单
        is_owner = obj.created_by == request.user
        is_admin = request.user.is_staff or request.user.is_superuser
        
        # 针对不同操作的权限检查
        if view.action in ['update', 'partial_update', 'destroy']:
            return is_owner or is_admin
        
        elif view.action in ['add_item', 'remove_item', 'update_item', 'upload_attachment']:
            # 只有草稿状态的更改单可以修改条目
            if obj.status != 'draft':
                return False
            return is_owner or is_admin
        
        elif view.action == 'perform_action':
            action = request.data.get('action')
            
            # 提交和取消操作只能由创建者或管理员执行，且只能是草稿或待处理状态
            if action in ['submit', 'cancel']:
                if obj.status not in ['draft', 'pending']:
                    return False
                return is_owner or is_admin
            
            # 执行操作需要特殊权限，且只能是待处理状态
            elif action == 'execute':
                if obj.status != 'pending':
                    return False
                return request.user.has_perm('archive_records.execute_changeorder') or is_admin
            
            # 删除操作只能由创建者或管理员执行，且只能是草稿或已取消状态
            elif action == 'delete':
                if obj.status not in ['draft', 'cancelled']:
                    return False
                return is_owner or is_admin
            
            # 回滚操作需要特殊权限，且只能是已完成状态
            elif action == 'rollback':
                if obj.status != 'completed':
                    return False
                return request.user.has_perm('archive_records.rollback_changeorder') or is_admin
        
        return False 