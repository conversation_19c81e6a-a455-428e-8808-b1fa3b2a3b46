#!/bin/bash
# OCR 服务修复脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 OCR 服务修复脚本"
echo "=========================="

# 1. 停止现有的 OCR 服务
log_info "停止现有的 OCR 服务..."
docker-compose stop ocr-service || true
docker-compose rm -f ocr-service || true

# 2. 重新构建 OCR 服务镜像
log_info "重新构建 OCR 服务镜像..."
cd ocr_service
if docker build -t archive-flow-ocr:latest .; then
    log_success "OCR 服务镜像重新构建成功"
else
    log_error "OCR 服务镜像构建失败"
    exit 1
fi
cd ..

# 3. 启动 OCR 服务
log_info "启动 OCR 服务..."
docker-compose up -d ocr-service

# 4. 等待服务启动
log_info "等待 OCR 服务启动..."
sleep 10

# 5. 检查服务状态
log_info "检查服务状态..."
if docker-compose ps ocr-service | grep -q "Up"; then
    log_success "OCR 服务已启动"
else
    log_error "OCR 服务启动失败"
    log_info "查看日志:"
    docker-compose logs --tail=50 ocr-service
    exit 1
fi

# 6. 检查健康状态
log_info "检查健康状态..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:8001/health &> /dev/null; then
        log_success "OCR 服务健康检查通过"
        break
    else
        log_warning "OCR 服务健康检查失败，重试 $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    log_error "OCR 服务健康检查超时"
    log_info "查看详细日志:"
    docker-compose logs --tail=100 ocr-service
    exit 1
fi

# 7. 显示服务信息
log_success "OCR 服务修复完成！"
echo ""
echo "📋 服务信息:"
echo "  🔍 OCR 服务: http://localhost:8001"
echo "  📊 健康检查: http://localhost:8001/health"
echo "  📈 指标: http://localhost:8001/metrics"
echo "  📚 API 文档: http://localhost:8001/docs"
echo ""
echo "📋 查看日志:"
echo "  docker-compose logs -f ocr-service"
