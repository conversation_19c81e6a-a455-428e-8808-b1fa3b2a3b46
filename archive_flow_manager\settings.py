"""
Django settings for archive_flow_manager project.

Generated by 'django-admin startproject' using Django 5.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-2l)edm&c*%-55mj67^d%k)*f)p1lvli2%b5_b@x$svi#1j*+e!'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# 添加 'web' (Docker 服务名)、'nginx' (Nginx代理) 以及 localhost/127.0.0.1 (方便本地直接访问或调试)
ALLOWED_HOSTS = ['web', 'nginx', 'localhost', '127.0.0.1']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',  # 必需，用于django-allauth
    
    # REST Framework
    'rest_framework',
    'rest_framework.authtoken',  # 必需，用于dj-rest-auth
    'rest_framework_simplejwt',  # 添加JWT支持
    'rest_framework_simplejwt.token_blacklist',  # 添加token黑名单支持
    'djangorestframework_camel_case',  # 添加camel case支持
    
    # 认证相关
    'allauth',
    'allauth.account',
    'allauth.socialaccount',  # 可选，用于社交登录
    'dj_rest_auth',
    'dj_rest_auth.registration',  # 可选，用于API注册
    
    # 项目应用
    'dev_tools',    # 开发工具
    'archive_records',
    'archive_processing',
    'report_issuing',
    
    # 第三方库
    'django_filters',
    'corsheaders',
    'test_suite.apps.TestSuiteConfig',
    'django_celery_beat', # For Celery Beat (periodic tasks)
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'allauth.account.middleware.AccountMiddleware', # allauth中间件
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'archive_flow_manager.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'archive_flow_manager.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# CHANGE: [日期] 修改数据库配置以从环境变量读取，支持PostgreSQL
DATABASES = {
    'default': {
        'ENGINE': os.environ.get('DATABASE_ENGINE', 'django.db.backends.postgresql'), # 默认为PostgreSQL引擎
        'NAME': os.environ.get('DATABASE_NAME'), # 数据库名，从环境变量获取
        'USER': os.environ.get('DATABASE_USER'), # 用户名，从环境变量获取
        'PASSWORD': os.environ.get('DATABASE_PASSWORD'), # 密码，从环境变量获取
        'HOST': os.environ.get('DATABASE_HOST'), # 主机名，从环境变量获取 (例如 'db' 服务名)
        'PORT': os.environ.get('DATABASE_PORT', '5432'), # 端口，从环境变量获取，默认为5432
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 配置媒体文件路径
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# --- 新增归档文件根目录设置 ---
ARCHIVE_ROOT = os.path.join(MEDIA_ROOT, 'archives')
# 确保这个目录存在，或者让 FileStorageService 在需要时创建它
os.makedirs(ARCHIVE_ROOT, exist_ok=True) # 可以加这行确保目录存在

# PDF处理相关配置
POPPLER_PATH = os.path.join(BASE_DIR, 'bin', 'poppler')
TESSERACT_CMD_PATH = os.path.join(BASE_DIR, 'bin', 'tesseract', 'tesseract.exe')  # Windows系统
# 如果是Linux/Mac系统，使用：
# TESSERACT_CMD_PATH = os.path.join(BASE_DIR, 'bin', 'tesseract', 'tesseract')

# Tesseract数据路径配置
TESSDATA_PREFIX = os.path.join(BASE_DIR, 'bin', 'tesseract', 'tessdata')

# REST Framework 设置
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    # CHANGE: [2025-06-19] 强制使用 camel case 转换，统一前后端数据格式命名规范
    'DEFAULT_RENDERER_CLASSES': [
        'djangorestframework_camel_case.render.CamelCaseJSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'djangorestframework_camel_case.parser.CamelCaseJSONParser',
    ],
}

# Django Sites Framework配置（django-allauth必需）
SITE_ID = 1

# Django Allauth配置
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',  # Django默认认证后端
    'allauth.account.auth_backends.AuthenticationBackend',  # Allauth认证后端
]

ACCOUNT_EMAIL_VERIFICATION = "none"  # 开发环境禁用邮件验证
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_LOGIN_METHODS = ['username']  # 使用用户名登录
ACCOUNT_RATE_LIMITS = {
    "login_failed": "5/5m",  # 5分钟内最多5次失败尝试
}

# CHANGE: [2025-06-29] 添加新的allauth配置以消除废弃警告
ACCOUNT_SIGNUP_FIELDS = {
    'username': {'required': True},
    'email': {'required': False},  # 根据项目需求设置
}

# dj-rest-auth配置（新版本使用REST_AUTH配置）
REST_AUTH = {
    'USE_JWT': True,  # 使用JWT而不是Token
    'JWT_AUTH_COOKIE': 'jwt-auth',  # JWT cookie名称  
    'JWT_AUTH_REFRESH_COOKIE': 'jwt-refresh',  # 刷新令牌cookie名称
    'JWT_AUTH_HTTPONLY': False,  # 对于API调用设为False
    'JWT_AUTH_RETURN_EXPIRATION': True,  # 返回令牌过期时间
    'LOGIN_SERIALIZER': 'dj_rest_auth.serializers.LoginSerializer',
    'USER_DETAILS_SERIALIZER': 'dj_rest_auth.serializers.UserDetailsSerializer',
}

# 简单JWT配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),  # 访问令牌有效期
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),  # 刷新令牌有效期
    'ROTATE_REFRESH_TOKENS': True,  # 刷新令牌时轮换，生成新的刷新令牌
    'BLACKLIST_AFTER_ROTATION': True,  # 轮换后将旧的刷新令牌加入黑名单
    'UPDATE_LAST_LOGIN': True,  # 更新最后登录时间
    
    'ALGORITHM': 'HS256',  # 算法
    'SIGNING_KEY': SECRET_KEY,  # 签名密钥
    'VERIFYING_KEY': None,  # 仅在使用非对称加密时需要
    'AUDIENCE': None,
    'ISSUER': None,
    
    'AUTH_HEADER_TYPES': ('Bearer',),  # Authorization头部的类型
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',  # 请求头名称
    'USER_ID_FIELD': 'id',  # 用户模型中的ID字段
    'USER_ID_CLAIM': 'user_id',  # 令牌声明中的用户ID字段
    
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    
    'JTI_CLAIM': 'jti',  # JWT ID声明
    
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(days=1),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=7),
}

# CORS设置
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",  # React前端开发服务器
#     "http://127.0.0.1:3000",
#     "http://localhost:8501",  # Streamlit地址（兼容旧前端，可以后续移除）
#     "http://127.0.0.1:8501",  # Streamlit地址（兼容旧前端，可以后续移除）
#     # 添加您的前端域名
# ]

# 或者在开发环境中允许所有源
CORS_ALLOW_ALL_ORIGINS = True  # 仅在开发环境中使用

# 允许凭证（如果您的前端需要发送Cookie）
CORS_ALLOW_CREDENTIALS = True

# 日志配置 (添加)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False, # 保持现有日志器（如 Django 的）有效
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s [%(name)s] %(message)s'
        },
        'simple': {
            'format': '%(levelname)s %(message)s'
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG', # 设置控制台处理器接收 DEBUG 及以上级别
            'class': 'logging.StreamHandler',
            'formatter': 'verbose' # 使用详细格式
        },
        # 可以添加其他处理器，例如文件处理器
        # 'file': {
        #     'level': 'INFO',
        #     'class': 'logging.FileHandler',
        #     'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
        #     'formatter': 'verbose',
        # },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'archive_processing': { 
            'handlers': ['console'], 
            'level': 'INFO', 
            'propagate': False, 
        },
        'archive_records': {  # 新增对 archive_records app 的日志配置
            'handlers': ['console'],
            'level': 'DEBUG',  # 设置为 DEBUG 以捕获 info 和 debug 日志
            'propagate': False, # 通常不需要向上传播给 root logger
        },
        # 可以保留根logger的注释，或者根据需要配置它
        # '': {
        #     'handlers': ['console'],
        #     'level': 'WARNING',
        # },
    },
}

# --- 权限模式设置 ---
# 可选值: 'debug' (需要认证，推荐使用superuser账户), 'prod' (严格权限控制)
# 根据环境变量设置权限模式，如果环境变量未设置，则默认为'prod'
PERMISSION_MODE = os.environ.get('PERMISSION_MODE', 'prod')

# CHANGE: [2025-05-16] 添加会话管理相关超时配置
# 会话心跳超时时间（单位：分钟）
# 用于独占处理阶段（如冲突解决），若处理用户在此时间内无心跳，则会话可被他人接管。
# 前端心跳发送间隔 (例如60秒) 应小于此值。
SESSION_HEARTBEAT_TIMEOUT_MINUTES = 1.5  # 90秒

# REMOVE: [2025-05-30] SESSION_ACTIVITY_TIMEOUT_MINUTES 已被移除，相关逻辑已废弃
# # （可选）会话活动超时时间（单位：分钟）
# # 此配置在当前的接管逻辑中作用已减弱，主要依赖心跳机制。
# # 可能仍用于非常规情况下的清理或作为极端后备。
# SESSION_ACTIVITY_TIMEOUT_MINUTES = 30

# Excel导入会话通用过期时间（分钟）
# 用于处于中间处理状态（如上传、分析中）的会话，如果超过此时间无进展，会被置为ERROR状态。
EXCEL_IMPORT_SESSION_EXPIRATION_MINUTES = 30

# CHANGE: [2025-05-28] 添加导入结果和错误展示期配置
# 成功导入结果的展示期（单位：分钟）
# 用户可在此期间查看导入结果，期满后会话自动转为FINALIZED状态
IMPORT_SUCCESS_DISPLAY_MINUTES = 5

# 带错误导入结果的展示期（单位：分钟）
# 比成功展示期更长，给用户更多时间查看和处理错误信息
IMPORT_WITH_ERRORS_DISPLAY_MINUTES = 30

# 流程性错误的展示期（单位：分钟）
# 用于显示分析、上传等阶段的系统错误信息
ERROR_SESSION_DISPLAY_MINUTES = 15

# --- Celery Configuration ---
# 使用 Redis 作为消息中间件 (Broker)
CELERY_BROKER_URL = 'redis://localhost:6379/0'

# 使用 Redis 作为结果后端 (Result Backend)
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'

# （可选）其他 Celery 设置
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# CHANGE: [2025-07-03] 极简Celery配置
# PDF处理任务通过 dispatch_pdf_processing() 函数中的 apply_async() 显式指定队列
# Worker启动时会自动创建所需队列，无需额外配置
# CHANGE: [2025-05-28] 添加Celery Beat调度配置
CELERY_BEAT_SCHEDULE = {
    'process-finalized-sessions': {
        'task': 'archive_records.tasks.process_finalized_sessions_task',
        'schedule': 300.0,  # 每5分钟执行一次 (300秒)
        'options': {
            'expires': 240,  # 任务过期时间4分钟，确保不会堆积
        }
    },
    # CHANGE: [2025-06-29] 添加用于清理卡住任务的周期性任务
    'cleanup-stuck-processing-tasks': {
        'task': 'archive_processing.cleanup_stuck_tasks',
        'schedule': 900.0, # 每15分钟执行一次 (900秒)
        'options': {
            'expires': 600, # 任务10分钟后过期
        }
    }
}
