import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const activityLogs = [
  {
    id: "a1",
    userId: "u1",
    userName: "张三",
    action: "登录系统",
    timestamp: "2023-05-03T14:30:00Z",
    ipAddress: "*************",
    details: "成功登录",
  },
  {
    id: "a2",
    userId: "u3",
    userName: "王五",
    action: "修改用户权限",
    timestamp: "2023-05-03T13:45:00Z",
    ipAddress: "*************",
    details: "修改了用户'李四'的权限",
  },
  {
    id: "a3",
    userId: "u2",
    userName: "李四",
    action: "创建报告发放单",
    timestamp: "2023-05-03T11:20:00Z",
    ipAddress: "*************",
    details: "创建了新的报告发放单",
  },
  {
    id: "a4",
    userId: "u4",
    userName: "赵六",
    action: "上传档案",
    timestamp: "2023-05-03T10:15:00Z",
    ipAddress: "*************",
    details: "上传了5个PDF文件",
  },
  {
    id: "a5",
    userId: "u5",
    userName: "钱七",
    action: "登录系统",
    timestamp: "2023-05-03T09:30:00Z",
    ipAddress: "*************",
    details: "成功登录",
  },
]

export function UserActivityLog() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>用户</TableHead>
          <TableHead>操作</TableHead>
          <TableHead>详情</TableHead>
          <TableHead>IP地址</TableHead>
          <TableHead>时间</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {activityLogs.map((log) => (
          <TableRow key={log.id}>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={`/placeholder.svg?height=32&width=32&text=${log.userName.charAt(0)}`}
                    alt={log.userName}
                  />
                  <AvatarFallback>{log.userName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="font-medium">{log.userName}</div>
              </div>
            </TableCell>
            <TableCell>{log.action}</TableCell>
            <TableCell>{log.details}</TableCell>
            <TableCell className="font-mono text-xs">{log.ipAddress}</TableCell>
            <TableCell>
              {new Date(log.timestamp).toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
