"use client"

import { PageTitle } from "@/components/page-title"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function RolesPage() {
  const [selectedRole, setSelectedRole] = useState("admin")

  const roleOptions = [
    { value: "admin", label: "系统管理员" },
    { value: "archivist", label: "档案管理员" },
    { value: "reporter", label: "报告发放员" },
  ]

  const getRoleTitle = (role: string) => {
    const roleOption = roleOptions.find((option) => option.value === role)
    return roleOption ? roleOption.label : role
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/users">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">返回</span>
          </Link>
        </Button>
        <PageTitle title="角色权限管理" subtitle="管理系统角色和权限设置" />
      </div>

      <div className="mb-6">
        <label htmlFor="role-select" className="block text-sm font-medium mb-2">
          选择角色
        </label>
        <Select value={selectedRole} onValueChange={setSelectedRole}>
          <SelectTrigger className="w-full sm:w-[250px]">
            <SelectValue placeholder="选择角色" />
          </SelectTrigger>
          <SelectContent>
            {roleOptions.map((role) => (
              <SelectItem key={role.value} value={role.value}>
                {role.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{getRoleTitle(selectedRole)}权限</CardTitle>
          <CardDescription>管理{getRoleTitle(selectedRole)}角色的权限设置</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            {/* TODO: 实现NextAuth.js后，重新添加权限管理组件 */}
            权限管理功能将在认证系统实现后恢复
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
