from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.core.files.storage import FileSystemStorage
from archive_records.services.archive_status_service import ArchiveStatusService
import os
import uuid
import logging
from django.core.cache import cache
from django.utils import timezone
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, Form<PERSON>arser, JSONParser
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework import viewsets
from rest_framework.decorators import action
from django.db.models import Count, Q

# 导入权限管理工具
from archive_flow_manager.permissions import permission_mode_aware, is_debug_mode

from .services import UploadService, TaskService
from .models import UploadedFile, ProcessingTask
from .serializers import PDFUploadSerializer, ProcessingTaskSerializer, UploadedFileSerializer
from archive_records.models import ArchiveRecord
from archive_records.serializers import ArchiveRecordSerializer
import json

logger = logging.getLogger(__name__)

# Create your views here.

# 视图分组


# 1. 废弃的旧文件上传视图 (逻辑已迁移到UploadedFileViewSet)
# @permission_mode_aware
# class PDFUploadView(APIView): ... (代码已移除)


@permission_mode_aware
class ProcessingTaskViewSet(viewsets.ReadOnlyModelViewSet):
    """
    一个只读视图集，用于查看处理任务（ProcessingTask）。
    支持按状态筛选。
    """
    serializer_class = ProcessingTaskSerializer
    # permission_classes = [IsAuthenticated] # 移除硬编码，遵循全局权限管理

    def get_queryset(self):
        """
        重写get_queryset方法，以支持根据URL参数进行筛选。
        支持单个状态或多个逗号分隔的状态。
        """
        # 初始查询集
        queryset = ProcessingTask.objects.select_related('file').order_by('-created_at')

        # 从request.query_params获取status参数
        status_param = self.request.query_params.get('status', None)

        if status_param:
            # 按逗号分割参数，并去除首尾空格
            statuses = [s.strip() for s in status_param.split(',')]
            # 过滤掉空字符串
            statuses = [s for s in statuses if s]

            if statuses:
                queryset = queryset.filter(status__in=statuses)


        # 同样筛选出活跃的文件
        queryset = queryset.filter(file__status='active')
        
        return queryset

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        获取处理任务的统计信息，按状态分组。
        """
        queryset = self.get_queryset()
        
        # 定义状态分组
        status_groups = {
            'pending': ['pending', 'queued'],
            'processing': ['processing', 'chunking', 'processing_parallel', 'aggregating'],
            'completed': ['completed', 'completed_without_report'],
            'failed': ['failed', 'failed_validation']
        }
        
        # 使用 Q 对象和聚合查询一次性获取所有计数
        aggregation_queries = {
            f"{group}_count": Count('pk', filter=Q(status__in=statuses))
            for group, statuses in status_groups.items()
        }
        
        stats = queryset.aggregate(**aggregation_queries)
        
        # 格式化响应
        formatted_stats = {
            "pending": stats.get("pending_count", 0),
            "processing": stats.get("processing_count", 0),
            "completed": stats.get("completed_count", 0),
            "failed": stats.get("failed_count", 0),
            "total": sum(stats.values())
        }
        
        return Response(formatted_stats)

    @action(detail=True, methods=['post'], url_path='retry')
    def retry(self, request, pk=None):
        """
        重试一个失败的处理任务。
        将所有业务逻辑委托给服务层。
        """
        # CHANGE: [YYYY-MM-DD] 重构：移除视图层的业务逻辑校验，完全依赖服务层
        # 视图层唯一的职责就是获取对象并调用服务
        task = self.get_object()

        success, message = TaskService.retry_task(task=task, user=request.user)

        if success:
            return Response(
                {"success": True, "message": message},
                status=status.HTTP_200_OK
            )
        else:
            # 根据服务层返回的错误信息，判断是客户端错误还是服务器错误
            if "服务器内部错误" in message:
                return Response(
                    {"success": False, "error": message},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            return Response(
                {"success": False, "error": message},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'], url_path='records')
    def records(self, request, pk=None):
        """
        获取指定处理任务（ProcessingTask）最终成功入库的所有档案记录（ArchiveRecord）。
        """
        task = self.get_object()
        if task.status not in ['completed', 'completed_without_report']:
            return Response({"error": "此任务尚未完成，无法获取档案记录。"}, status=400)

        # 通过 source_file 关联查询
        records = ArchiveRecord.objects.filter(source_file=task.file).order_by('id')
        
        # 禁用分页，一次性返回所有记录，解决前端只显示部分数据的问题
        # page = self.paginate_queryset(records)
        # if page is not None:
        #     serializer = ArchiveRecordSerializer(page, many=True)
        #     return self.get_paginated_response(serializer.data)

        serializer = ArchiveRecordSerializer(records, many=True)
        return Response(serializer.data)


# CHANGE: [2025-07-30] 新增UploadedFileViewSet，统一文件上传和相关操作
@permission_mode_aware
class UploadedFileViewSet(viewsets.ModelViewSet):
    """
    一个统一的视图集，用于管理文件上传 (UploadedFile) 及其相关操作。

    - `create`: 处理文件上传，替代旧的PDFUploadView。
    - `list`: 列出已上传的文件。
    - `retrieve`: 获取单个文件的详细信息。
    - `destroy`: 删除一个文件记录（逻辑删除）。
    - `re_upload`: (待实现) 处理文件的重新上传流程。
    """
    queryset = UploadedFile.objects.filter(status='active').order_by('-upload_time')
    serializer_class = UploadedFileSerializer # 使用通用的文件序列化器
    parser_classes = (MultiPartParser, FormParser, JSONParser)

    def get_serializer_class(self):
        """根据action选择不同的序列化器"""
        if self.action == 'create':
            return PDFUploadSerializer
        return super().get_serializer_class()

    def create(self, request, *args, **kwargs):
        """
        处理POST请求，接收文件和目标档案盒号，创建上传记录并启动处理任务。
        完全替代旧的 PDFUploadView。
        """
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"文件上传请求验证失败: {serializer.errors}")
            error_detail = {
                "field_errors": serializer.errors,
                "message": "请求数据验证失败",
            }
            return Response(
                {"success": False, "error": error_detail},
                status=status.HTTP_400_BAD_REQUEST,
            )

        validated_data = serializer.validated_data
        file_obj = validated_data["file"]
        
        # 从验证过的数据中获取archive_box_number，如果不存在则为None
        archive_box_number = validated_data.get("archive_box_number")

        # 从请求中获取处理参数
        use_parallel_str = request.data.get('use_parallel', 'true')
        use_parallel = use_parallel_str.lower() == 'true' if isinstance(use_parallel_str, str) else use_parallel_str

        chunk_size_str = request.data.get('chunk_size', '20')
        batch_size_str = request.data.get('batch_size', '5')

        try:
            chunk_size = int(chunk_size_str)
            batch_size = int(batch_size_str)
            if chunk_size <= 0 or batch_size <= 0:
                raise ValueError("chunk_size and batch_size must be positive.")
        except (ValueError, TypeError):
            logger.warning(f"无效的处理参数，将使用默认值。chunk_size='{chunk_size_str}', batch_size='{batch_size_str}'")
            chunk_size = 20
            batch_size = 5

        processing_params = {
            'use_parallel': use_parallel,
            'chunk_size': chunk_size,
            'batch_size': batch_size
        }

        user = request.user
        user_id = user.id if user and user.is_authenticated else None

        try:
            # 调用 UploadService 保存文件，传递从序列化器获取的盒号
            uploaded_file_record = UploadService.save_uploaded_file(
                file_obj=file_obj,
                archive_box_number=archive_box_number,
                user_id=user_id,
            )
            logger.info(
                f"用户 {user_id or '匿名'} 成功上传文件: {uploaded_file_record.file_id} ({uploaded_file_record.original_name}), "
                f"分配盒号: {archive_box_number or '未提供'}"
            )
        except (DjangoValidationError, IOError) as e:
            logger.error(f"文件上传或保存失败 (用户: {user_id}): {e}", exc_info=True)
            return Response(
                {"success": False, "error": {"message": f"文件上传处理失败: {e}"}},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.exception(f"处理上传时发生意外错误 (用户: {user_id}): {e}")
            return Response(
                {"success": False, "error": {"message": "服务器内部错误，无法处理上传请求。"}},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        try:
            task_record = TaskService.create_task(
                uploaded_file=uploaded_file_record,
                params=processing_params,
            )
            logger.info(
                f"为文件 {uploaded_file_record.file_id} 成功创建处理任务: {task_record.task_id} (任务已自动分发)"
            )

            response_data = {
                "success": True,
                "data": {
                    "message": "文件上传成功，处理任务已启动。",
                    "file_id": str(uploaded_file_record.file_id),
                    "task_id": str(task_record.task_id),
                    "task_status": task_record.status,
                },
            }
            return Response(response_data, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.exception(f"文件 {uploaded_file_record.file_id} 已上传，但创建处理任务失败: {e}")
            return Response(
                {
                    "success": True,
                    "data": {
                        "message": "文件上传成功，但自动处理任务启动失败。请稍后检查或联系管理员。",
                        "file_id": str(uploaded_file_record.file_id),
                    },
                    "warning": f"无法启动处理任务: {e}",
                },
                status=status.HTTP_207_MULTI_STATUS,
            )

    @action(detail=True, methods=['post'], url_path='re-upload')
    def re_upload(self, request, pk=None):
        """
        处理文件的重新上传。

        - 标记旧文件为'deleted'。
        - 为新上传的文件创建一个全新的UploadedFile和ProcessingTask记录。
        - 异步清理旧的物理文件。
        """
        old_file = self.get_object()
        new_file_obj = request.data.get('file')

        if not new_file_obj:
            return Response(
                {"success": False, "error": {"message": "未提供新的上传文件。"}},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 核心逻辑委托给服务层
        success, result = UploadService.handle_re_upload(
            old_file=old_file,
            new_file_obj=new_file_obj,
            user=request.user
        )

        if success:
            return Response({"success": True, "data": result}, status=status.HTTP_201_CREATED)
        else:
            # 根据错误信息判断返回400还是500
            if "服务器内部错误" in result:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            else:
                status_code = status.HTTP_400_BAD_REQUEST
            return Response({"success": False, "error": {"message": result}}, status=status_code)


# 后续视图将被删除
