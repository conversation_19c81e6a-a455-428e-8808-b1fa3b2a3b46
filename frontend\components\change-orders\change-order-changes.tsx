"use client"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Filter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useState, useEffect } from "react"

interface ChangeOrderChangesProps {
  changeOrder: any
  editedChanges: any[]
  editMode: boolean
  onChangeEdit: (change: any, field: string, value: string) => void
  onRecordSelection: (recordId: string, isSelected: boolean) => void
}

// 模拟数据 - 台账表
const mockAccountData = [
  {
    id: "1",
    unifiedNumber: "TA-2023-0001",
    title: "某项目可行性研究报告",
    projectName: "项目A",
    sampleNumber: "SP-001",
    clientCompany: "某公司",
    commissionDate: "2023-01-10",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "2",
    unifiedNumber: "TA-2023-0002",
    title: "某项目初步设计报告",
    projectName: "项目A",
    sampleNumber: "SP-002",
    clientCompany: "某公司",
    commissionDate: "2023-02-15",
    commissioner: "李四",
    status: "已归档",
  },
  {
    id: "3",
    unifiedNumber: "TA-2023-0003",
    title: "某项目环境影响评估",
    projectName: "项目B",
    sampleNumber: "SP-003",
    clientCompany: "某科技公司",
    commissionDate: "2023-03-05",
    commissioner: "王五",
    status: "未归档",
  },
  {
    id: "4",
    unifiedNumber: "TA-2023-0004",
    title: "某项目施工图设计",
    projectName: "项目B",
    sampleNumber: "SP-004",
    clientCompany: "某科技公司",
    commissionDate: "2023-04-01",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "5",
    unifiedNumber: "TA-2023-0005",
    title: "某项目竣工验收报告",
    projectName: "项目C",
    sampleNumber: "SP-005",
    clientCompany: "某设计院",
    commissionDate: "2023-05-20",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "6",
    unifiedNumber: "TA-2023-0006",
    title: "某项目水土保持方案",
    projectName: "项目C",
    sampleNumber: "SP-006",
    clientCompany: "某环保局",
    commissionDate: "2023-06-10",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "7",
    unifiedNumber: "TA-2023-0007",
    title: "某项目职业病危害评价",
    projectName: "项目D",
    sampleNumber: "SP-007",
    clientCompany: "某卫生院",
    commissionDate: "2023-07-01",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "8",
    unifiedNumber: "TA-2023-0008",
    title: "某项目安全预评价报告",
    projectName: "项目D",
    sampleNumber: "SP-008",
    clientCompany: "某安监局",
    commissionDate: "2023-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "9",
    unifiedNumber: "TA-2023-0009",
    title: "某项目节能评估报告",
    projectName: "项目E",
    sampleNumber: "SP-009",
    clientCompany: "某能源公司",
    commissionDate: "2023-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "10",
    unifiedNumber: "TA-2023-0010",
    title: "某项目社会稳定风险评估",
    projectName: "项目E",
    sampleNumber: "SP-010",
    clientCompany: "某政府部门",
    commissionDate: "2023-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "11",
    unifiedNumber: "TA-2023-0011",
    title: "某项目地震安全性评价",
    projectName: "项目F",
    sampleNumber: "SP-011",
    clientCompany: "某地震局",
    commissionDate: "2023-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "12",
    unifiedNumber: "TA-2023-0012",
    title: "某项目地质灾害危险性评估",
    projectName: "项目F",
    sampleNumber: "SP-012",
    clientCompany: "某地质勘查院",
    commissionDate: "2023-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
  {
    id: "13",
    unifiedNumber: "TA-2024-0001",
    title: "某项目气候可行性论证",
    projectName: "项目G",
    sampleNumber: "SP-013",
    clientCompany: "某气象局",
    commissionDate: "2024-01-05",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "14",
    unifiedNumber: "TA-2024-0002",
    title: "某项目压覆矿产资源评估",
    projectName: "项目G",
    sampleNumber: "SP-014",
    clientCompany: "某矿产资源厅",
    commissionDate: "2024-02-01",
    commissioner: "李四",
    status: "未归档",
  },
  {
    id: "15",
    unifiedNumber: "TA-2024-0003",
    title: "某项目文物影响评估",
    projectName: "项目H",
    sampleNumber: "SP-015",
    clientCompany: "某文物局",
    commissionDate: "2024-03-10",
    commissioner: "王五",
    status: "已归档",
  },
  {
    id: "16",
    unifiedNumber: "TA-2024-0004",
    title: "某项目环境监理报告",
    projectName: "项目H",
    sampleNumber: "SP-016",
    clientCompany: "某环保公司",
    commissionDate: "2024-04-15",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "17",
    unifiedNumber: "TA-2024-0005",
    title: "某项目竣工环境保护验收调查报告",
    projectName: "项目I",
    sampleNumber: "SP-017",
    clientCompany: "某检测中心",
    commissionDate: "2024-05-05",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "18",
    unifiedNumber: "TA-2024-0006",
    title: "某项目突发环境事件应急预案",
    projectName: "项目I",
    sampleNumber: "SP-018",
    clientCompany: "某应急管理局",
    commissionDate: "2024-06-01",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "19",
    unifiedNumber: "TA-2024-0007",
    title: "某项目水资源论证报告",
    projectName: "项目J",
    sampleNumber: "SP-019",
    clientCompany: "某水利厅",
    commissionDate: "2024-07-10",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "20",
    unifiedNumber: "TA-2024-0008",
    title: "某项目取水许可申请",
    projectName: "项目J",
    sampleNumber: "SP-020",
    clientCompany: "某水务局",
    commissionDate: "2024-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "21",
    unifiedNumber: "TA-2024-0009",
    title: "某项目节能验收报告",
    projectName: "项目K",
    sampleNumber: "SP-021",
    clientCompany: "某能源检测中心",
    commissionDate: "2024-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "22",
    unifiedNumber: "TA-2024-0010",
    title: "某项目固定资产投资项目节能登记表",
    projectName: "项目K",
    sampleNumber: "SP-022",
    clientCompany: "某发改委",
    commissionDate: "2024-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "23",
    unifiedNumber: "TA-2024-0011",
    title: "某项目交通影响评价",
    projectName: "项目L",
    sampleNumber: "SP-023",
    clientCompany: "某交通运输局",
    commissionDate: "2024-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "24",
    unifiedNumber: "TA-2024-0012",
    title: "某项目城市规划方案",
    projectName: "项目L",
    sampleNumber: "SP-024",
    clientCompany: "某规划局",
    commissionDate: "2024-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
  {
    id: "25",
    unifiedNumber: "TA-2025-0001",
    title: "某项目土地利用总体规划",
    projectName: "项目M",
    sampleNumber: "SP-025",
    clientCompany: "某国土资源局",
    commissionDate: "2025-01-05",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "26",
    unifiedNumber: "TA-2025-0002",
    title: "某项目建设用地规划许可证",
    projectName: "项目M",
    sampleNumber: "SP-026",
    clientCompany: "某规划局",
    commissionDate: "2025-02-01",
    commissioner: "李四",
    status: "未归档",
  },
  {
    id: "27",
    unifiedNumber: "TA-2025-0003",
    title: "某项目建设工程规划许可证",
    projectName: "项目N",
    sampleNumber: "SP-027",
    clientCompany: "某住建局",
    commissionDate: "2025-03-10",
    commissioner: "王五",
    status: "已归档",
  },
  {
    id: "28",
    unifiedNumber: "TA-2025-0004",
    title: "某项目建筑工程施工许可证",
    projectName: "项目N",
    sampleNumber: "SP-028",
    clientCompany: "某建委",
    commissionDate: "2025-04-15",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "29",
    unifiedNumber: "TA-2025-0005",
    title: "某项目消防设计审核意见书",
    projectName: "项目O",
    sampleNumber: "SP-029",
    clientCompany: "某消防支队",
    commissionDate: "2025-05-05",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "30",
    unifiedNumber: "TA-2025-0006",
    title: "某项目竣工验收备案表",
    projectName: "项目O",
    sampleNumber: "SP-030",
    clientCompany: "某档案馆",
    commissionDate: "2025-06-01",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "31",
    unifiedNumber: "TA-2025-0007",
    title: "某项目档案专项验收意见",
    projectName: "项目P",
    sampleNumber: "SP-031",
    clientCompany: "某档案局",
    commissionDate: "2025-07-10",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "32",
    unifiedNumber: "TA-2025-0008",
    title: "某项目建设工程质量监督注册登记表",
    projectName: "项目P",
    sampleNumber: "SP-032",
    clientCompany: "某质监站",
    commissionDate: "2025-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "33",
    unifiedNumber: "TA-2025-0009",
    title: "某项目安全生产许可证",
    projectName: "项目Q",
    sampleNumber: "SP-033",
    clientCompany: "某安监局",
    commissionDate: "2025-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "34",
    unifiedNumber: "TA-2025-0010",
    title: "某项目特种设备使用登记证",
    projectName: "项目Q",
    sampleNumber: "SP-034",
    clientCompany: "某特检院",
    commissionDate: "2025-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "35",
    unifiedNumber: "TA-2025-0011",
    title: "某项目防雷装置检测报告",
    projectName: "项目R",
    sampleNumber: "SP-035",
    clientCompany: "某气象局",
    commissionDate: "2025-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "36",
    unifiedNumber: "TA-2025-0012",
    title: "某项目电磁环境影响报告",
    projectName: "项目R",
    sampleNumber: "SP-036",
    clientCompany: "某环保局",
    commissionDate: "2025-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
  {
    id: "37",
    unifiedNumber: "TA-2026-0001",
    title: "某项目环境监测报告",
    projectName: "项目S",
    sampleNumber: "SP-037",
    clientCompany: "某监测中心",
    commissionDate: "2026-01-05",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "38",
    unifiedNumber: "TA-2026-0002",
    title: "某项目清洁生产审核报告",
    projectName: "项目S",
    sampleNumber: "SP-038",
    clientCompany: "某环保咨询公司",
    commissionDate: "2026-02-01",
    commissioner: "李四",
    status: "未归档",
  },
  {
    id: "39",
    unifiedNumber: "TA-2026-0003",
    title: "某项目排污许可证",
    projectName: "项目T",
    sampleNumber: "SP-039",
    clientCompany: "某环保局",
    commissionDate: "2026-03-10",
    commissioner: "王五",
    status: "已归档",
  },
  {
    id: "40",
    unifiedNumber: "TA-2026-0004",
    title: "某项目危险废物转移联单",
    projectName: "项目T",
    sampleNumber: "SP-040",
    clientCompany: "某危废处理中心",
    commissionDate: "2026-04-15",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "41",
    unifiedNumber: "TA-2026-0005",
    title: "某项目水土保持监测总结报告",
    projectName: "项目U",
    sampleNumber: "SP-041",
    clientCompany: "某水保监测站",
    commissionDate: "2026-05-05",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "42",
    unifiedNumber: "TA-2026-0006",
    title: "某项目水土保持设施自主验收报告",
    projectName: "项目U",
    sampleNumber: "SP-042",
    clientCompany: "某水利局",
    commissionDate: "2026-06-01",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "43",
    unifiedNumber: "TA-2026-0007",
    title: "某项目安全评价报告",
    projectName: "项目V",
    sampleNumber: "SP-043",
    clientCompany: "某安评中心",
    commissionDate: "2026-07-10",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "44",
    unifiedNumber: "TA-2026-0008",
    title: "某项目安全生产标准化证书",
    projectName: "项目V",
    sampleNumber: "SP-044",
    clientCompany: "某安标协会",
    commissionDate: "2026-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "45",
    unifiedNumber: "TA-2026-0009",
    title: "某项目职业卫生检测报告",
    projectName: "项目W",
    sampleNumber: "SP-045",
    clientCompany: "某职卫中心",
    commissionDate: "2026-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "46",
    unifiedNumber: "TA-2026-0010",
    title: "某项目工作场所职业病危害因素检测评价报告",
    projectName: "项目W",
    sampleNumber: "SP-046",
    clientCompany: "某卫健委",
    commissionDate: "2026-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "47",
    unifiedNumber: "TA-2026-0011",
    title: "某项目节能技术改造方案",
    projectName: "项目X",
    sampleNumber: "SP-047",
    clientCompany: "某节能服务公司",
    commissionDate: "2026-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "48",
    unifiedNumber: "TA-2026-0012",
    title: "某项目能源审计报告",
    projectName: "项目X",
    sampleNumber: "SP-048",
    clientCompany: "某能源审计中心",
    commissionDate: "2026-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
  {
    id: "49",
    unifiedNumber: "TA-2027-0001",
    title: "某项目社会稳定风险评估报告",
    projectName: "项目Y",
    sampleNumber: "SP-049",
    clientCompany: "某社稳评估公司",
    commissionDate: "2027-01-05",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "50",
    unifiedNumber: "TA-2027-0002",
    title: "某项目重大决策社会风险评估报告",
    projectName: "项目Y",
    sampleNumber: "SP-050",
    clientCompany: "某政府部门",
    commissionDate: "2027-02-01",
    commissioner: "李四",
    status: "未归档",
  },
  {
    id: "51",
    unifiedNumber: "TA-2027-0003",
    title: "某项目地震安全性评价报告",
    projectName: "项目Z",
    sampleNumber: "SP-051",
    clientCompany: "某地震局",
    commissionDate: "2027-03-10",
    commissioner: "王五",
    status: "已归档",
  },
  {
    id: "52",
    unifiedNumber: "TA-2027-0004",
    title: "某项目建设工程抗震设防专项审查意见",
    projectName: "项目Z",
    sampleNumber: "SP-052",
    clientCompany: "某住建局",
    commissionDate: "2027-04-15",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "53",
    unifiedNumber: "TA-2027-0005",
    title: "某项目地质灾害危险性评估报告",
    projectName: "项目AA",
    sampleNumber: "SP-053",
    clientCompany: "某地质勘查院",
    commissionDate: "2027-05-05",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "54",
    unifiedNumber: "TA-2027-0006",
    title: "某项目地质灾害治理工程设计",
    projectName: "项目AA",
    sampleNumber: "SP-054",
    clientCompany: "某地质工程公司",
    commissionDate: "2027-06-01",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "55",
    unifiedNumber: "TA-2027-0007",
    title: "某项目气候可行性论证报告",
    projectName: "项目BB",
    sampleNumber: "SP-055",
    clientCompany: "某气象局",
    commissionDate: "2027-07-10",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "56",
    unifiedNumber: "TA-2027-0008",
    title: "某项目气候变化适应性评估报告",
    projectName: "项目BB",
    sampleNumber: "SP-056",
    clientCompany: "某气候研究中心",
    commissionDate: "2027-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "57",
    unifiedNumber: "TA-2027-0009",
    title: "某项目压覆重要矿产资源储量评估报告",
    projectName: "项目CC",
    sampleNumber: "SP-057",
    clientCompany: "某矿产资源评估中心",
    commissionDate: "2027-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "58",
    unifiedNumber: "TA-2027-0010",
    title: "某项目矿产资源开发利用方案",
    projectName: "项目CC",
    sampleNumber: "SP-058",
    clientCompany: "某矿业公司",
    commissionDate: "2027-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "59",
    unifiedNumber: "TA-2027-0011",
    title: "某项目文物影响评估报告",
    projectName: "项目DD",
    sampleNumber: "SP-059",
    clientCompany: "某文物评估中心",
    commissionDate: "2027-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "60",
    unifiedNumber: "TA-2027-0012",
    title: "某项目考古调查勘探报告",
    projectName: "项目DD",
    sampleNumber: "SP-060",
    clientCompany: "某考古研究所",
    commissionDate: "2027-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
  {
    id: "61",
    unifiedNumber: "TA-2028-0001",
    title: "某项目环境监理月报",
    projectName: "项目EE",
    sampleNumber: "SP-061",
    clientCompany: "某环境监理公司",
    commissionDate: "2028-01-05",
    commissioner: "张三",
    status: "已归档",
  },
  {
    id: "62",
    unifiedNumber: "TA-2028-0002",
    title: "某项目环境监理总结报告",
    projectName: "项目EE",
    sampleNumber: "SP-062",
    clientCompany: "某环保局",
    commissionDate: "2028-02-01",
    commissioner: "李四",
    status: "未归档",
  },
  {
    id: "63",
    unifiedNumber: "TA-2028-0003",
    title: "某项目竣工环境保护验收调查报告",
    projectName: "项目FF",
    sampleNumber: "SP-063",
    clientCompany: "某环保验收中心",
    commissionDate: "2028-03-10",
    commissioner: "王五",
    status: "已归档",
  },
  {
    id: "64",
    unifiedNumber: "TA-2028-0004",
    title: "某项目竣工环境保护验收监测报告",
    projectName: "项目FF",
    sampleNumber: "SP-064",
    clientCompany: "某环境监测站",
    commissionDate: "2028-04-15",
    commissioner: "赵六",
    status: "未归档",
  },
  {
    id: "65",
    unifiedNumber: "TA-2028-0005",
    title: "某项目突发环境事件应急预案",
    projectName: "项目GG",
    sampleNumber: "SP-065",
    clientCompany: "某应急管理局",
    commissionDate: "2028-05-05",
    commissioner: "孙七",
    status: "已归档",
  },
  {
    id: "66",
    unifiedNumber: "TA-2028-0006",
    title: "某项目环境风险评估报告",
    projectName: "项目GG",
    sampleNumber: "SP-066",
    clientCompany: "某风险评估公司",
    commissionDate: "2028-06-01",
    commissioner: "周八",
    status: "未归档",
  },
  {
    id: "67",
    unifiedNumber: "TA-2028-0007",
    title: "某项目水资源论证报告",
    projectName: "项目HH",
    sampleNumber: "SP-067",
    clientCompany: "某水利厅",
    commissionDate: "2028-07-10",
    commissioner: "吴九",
    status: "已归档",
  },
  {
    id: "68",
    unifiedNumber: "TA-2028-0008",
    title: "某项目取水许可申请",
    projectName: "项目HH",
    sampleNumber: "SP-068",
    clientCompany: "某水务局",
    commissionDate: "2028-08-15",
    commissioner: "郑十",
    status: "未归档",
  },
  {
    id: "69",
    unifiedNumber: "TA-2028-0009",
    title: "某项目节水评价报告",
    projectName: "项目II",
    sampleNumber: "SP-069",
    clientCompany: "某节水中心",
    commissionDate: "2028-09-05",
    commissioner: "刘一",
    status: "已归档",
  },
  {
    id: "70",
    unifiedNumber: "TA-2028-0010",
    title: "某项目用水审计报告",
    projectName: "项目II",
    sampleNumber: "SP-070",
    clientCompany: "某审计事务所",
    commissionDate: "2028-10-01",
    commissioner: "陈二",
    status: "未归档",
  },
  {
    id: "71",
    unifiedNumber: "TA-2028-0011",
    title: "某项目节能评估报告",
    projectName: "项目JJ",
    sampleNumber: "SP-071",
    clientCompany: "某节能评估中心",
    commissionDate: "2028-11-10",
    commissioner: "林三",
    status: "已归档",
  },
  {
    id: "72",
    unifiedNumber: "TA-2028-0012",
    title: "某项目固定资产投资项目节能登记表",
    projectName: "项目JJ",
    sampleNumber: "SP-072",
    clientCompany: "某发改委",
    commissionDate: "2028-12-15",
    commissioner: "黄四",
    status: "未归档",
  },
]

export { mockAccountData }

export function ChangeOrderChanges({
  changeOrder,
  editedChanges,
  editMode,
  onChangeEdit,
  onRecordSelection,
}: ChangeOrderChangesProps) {
  const [activeTab, setActiveTab] = useState<string>("selected")
  const [searchTerm, setSearchTerm] = useState("")
  const [localSelectedRecords, setLocalSelectedRecords] = useState<string[]>([])
  const [fieldValues, setFieldValues] = useState<Record<string, Record<string, string>>>({})

  // 初始化本地选中记录状态
  useEffect(() => {
    // 从changeOrder.records中提取记录ID
    if (changeOrder && changeOrder.records) {
      const recordIds = changeOrder.records.map((record: any) => record.id)
      setLocalSelectedRecords(recordIds)
    }
  }, [changeOrder])

  // 初始化字段值状态
  useEffect(() => {
    // 从editedChanges中提取当前值
    const values: Record<string, Record<string, string>> = {}

    editedChanges.forEach((change) => {
      if (!values[change.recordId]) {
        values[change.recordId] = {}
      }
      values[change.recordId][change.field] = change.newValue
    })

    setFieldValues(values)
  }, [editedChanges])

  // 处理记录选择
  const handleRecordSelection = (recordId: string, isSelected: boolean) => {
    // 更新本地选中记录状态
    if (isSelected) {
      if (!localSelectedRecords.includes(recordId)) {
        setLocalSelectedRecords((prev) => [...prev, recordId])
      }
    } else {
      setLocalSelectedRecords((prev) => prev.filter((id) => id !== recordId))
    }

    // 调用父组件的回调函数
    onRecordSelection(recordId, isSelected)
  }

  // 处理字段值变更
  const handleFieldValueChange = (recordId: string, field: string, value: string) => {
    // 更新本地字段值状态
    setFieldValues((prev) => ({
      ...prev,
      [recordId]: {
        ...(prev[recordId] || {}),
        [field]: value,
      },
    }))
  }

  // 处理字段值提交（失焦或按回车）
  const handleFieldValueSubmit = (change: any, field: string, value: string) => {
    // 检查值是否真的发生了变化
    const oldValue = change.oldValue || ""
    const currentValue =
      editedChanges.find((c) => c.recordId === change.recordId && c.field === change.field)?.newValue || oldValue

    // 只有当值真正发生变化时才触发onChangeEdit
    if (value !== currentValue) {
      // 确保传递正确的参数，使父组件知道这是要更新newValue
      onChangeEdit(change, "newValue", value)
    }
  }

  // 过滤可用记录
  const filteredAvailableRecords = mockAccountData.filter((record) => {
    return (
      record.unifiedNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.projectName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  // 获取已选择的记录
  const getSelectedRecords = () => {
    return mockAccountData.filter((record) => localSelectedRecords.includes(record.id))
  }

  // 渲染已选档案标签页内容
  const renderSelectedTab = () => {
    const selectedRecords = getSelectedRecords()

    if (selectedRecords.length === 0) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500 mb-4">暂无选择的档案，请在台账表中选择</p>
          <Button variant="outline" onClick={() => setActiveTab("records")}>
            浏览台账表
          </Button>
        </div>
      )
    }

    // 定义要显示的字段（不包括统一编号）
    const fields = [
      { key: "title", label: "标题" },
      { key: "projectName", label: "项目" },
      { key: "sampleNumber", label: "样品编号" },
      { key: "clientCompany", label: "委托单位" },
      { key: "projectFullName", label: "工程名称" },
      { key: "commissionDate", label: "委托日期" },
      { key: "commissioner", label: "委托人" },
      { key: "保密级别", label: "保密级别" },
      { key: "status", label: "归档状态" },
    ]

    return (
      <div className="overflow-x-auto" style={{ width: "100%" }}>
        <table className="w-full min-w-[1200px] border-collapse">
          <thead>
            <tr className="bg-gray-50 border-y">
              <th className="text-center py-2 px-4 font-medium text-sm border-x border-gray-200 w-[150px]">统一编号</th>
              <th className="text-center py-2 px-4 font-medium text-sm border-r border-gray-200 w-[80px]">类型</th>
              {fields.map((field) => (
                <th key={field.key} className="text-center py-2 px-4 font-medium text-sm border-r border-gray-200">
                  {field.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {selectedRecords.flatMap((record, index) => [
              // 原值行
              <tr key={`${record.id}-old`} className="border-b">
                {index === 0 || selectedRecords[index - 1].unifiedNumber !== record.unifiedNumber ? (
                  <td rowSpan={2} className="text-center py-3 px-4 font-medium border-x border-gray-200 align-middle">
                    {record.unifiedNumber}
                  </td>
                ) : null}
                <td className="text-center py-3 px-4 bg-gray-50 border-r border-gray-200">原值</td>
                {fields.map((field) => (
                  <td key={`${record.id}-${field.key}-old`} className="py-3 px-4 border-r border-gray-200">
                    {record[field.key as keyof typeof record] || ""}
                  </td>
                ))}
              </tr>,
              // 新值行
              <tr key={`${record.id}-new`} className="border-b border-b-gray-300">
                <td className="text-center py-3 px-4 bg-blue-50 border-r border-gray-200">新值</td>
                {fields.map((field) => {
                  const oldValue = record[field.key as keyof typeof record] || ""

                  // 查找是否有对应的编辑过的值
                  const editedChange = editedChanges.find(
                    (change) => change.recordId === record.id && change.field === field.key,
                  )

                  const newValue = editedChange ? editedChange.newValue : oldValue
                  const isChanged = oldValue !== newValue

                  // 从本地状态获取当前输入值
                  const currentInputValue = fieldValues[record.id]?.[field.key] || newValue

                  return (
                    <td key={`${record.id}-${field.key}-new`} className="py-3 px-4 border-r border-gray-200">
                      {editMode ? (
                        <Input
                          value={currentInputValue}
                          onChange={(e) => {
                            // 只更新本地状态，不触发保存
                            handleFieldValueChange(record.id, field.key, e.target.value)
                          }}
                          onBlur={(e) => {
                            // 失焦时提交值
                            const change = {
                              recordId: record.id,
                              field: field.key,
                              fieldLabel: field.label,
                              oldValue,
                              newValue: e.target.value,
                            }
                            handleFieldValueSubmit(change, field.key, e.target.value)
                          }}
                          className={isChanged ? "border-blue-300" : ""}
                        />
                      ) : (
                        <div className={isChanged ? "text-blue-600 font-medium" : ""}>{newValue}</div>
                      )}
                    </td>
                  )
                })}
              </tr>,
            ])}
          </tbody>
        </table>
      </div>
    )
  }

  // 渲染台账表标签页内容
  const renderAccountTab = () => {
    return (
      <div className="overflow-x-auto" style={{ width: "100%" }}>
        <table className="w-full min-w-[1200px] border-collapse">
          <thead>
            <tr className="border-b">
              <th className="text-left py-2 px-4 font-medium text-sm w-[50px]">选择</th>
              <th className="text-left py-2 px-4 font-medium text-sm">统一编号</th>
              <th className="text-left py-2 px-4 font-medium text-sm">标题</th>
              <th className="text-left py-2 px-4 font-medium text-sm">项目</th>
              <th className="text-left py-2 px-4 font-medium text-sm">样品编号</th>
              <th className="text-left py-2 px-4 font-medium text-sm">委托单位</th>
              <th className="text-left py-2 px-4 font-medium text-sm">工程名称</th>
              <th className="text-left py-2 px-4 font-medium text-sm">委托日期</th>
              <th className="text-left py-2 px-4 font-medium text-sm">委托人</th>
              <th className="text-left py-2 px-4 font-medium text-sm">归档状态</th>
            </tr>
          </thead>
          <tbody>
            {filteredAvailableRecords.map((record, index) => {
              // 检查此记录是否已被选择
              const isSelected = localSelectedRecords.includes(record.id)

              return (
                <tr key={index} className="border-b">
                  <td className="py-3 px-4">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => handleRecordSelection(record.id, e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                  </td>
                  <td className="py-3 px-4 font-medium">{record.unifiedNumber}</td>
                  <td className="py-3 px-4">{record.title}</td>
                  <td className="py-3 px-4">{record.projectName}</td>
                  <td className="py-3 px-4">{record.sampleNumber}</td>
                  <td className="py-3 px-4">{record.clientCompany}</td>
                  <td className="py-3 px-4">{record.projectName}</td>
                  <td className="py-3 px-4">{record.commissionDate}</td>
                  <td className="py-3 px-4">{record.commissioner}</td>
                  <td className="py-3 px-4">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-sm text-xs font-medium ${
                        record.status === "未归档"
                          ? "bg-yellow-50 text-yellow-700 border border-yellow-200"
                          : "bg-gray-100 text-gray-800 border border-gray-200"
                      }`}
                    >
                      {record.status}
                    </span>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          更改内容 <span className="text-red-500">*</span>
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            已选条目: {localSelectedRecords.length} 项
          </Badge>
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            更改条目:{" "}
            {
              [...new Set(editedChanges.map((change) => change.recordId))].filter((recordId) =>
                editedChanges.some((change) => change.recordId === recordId && change.oldValue !== change.newValue),
              ).length
            }{" "}
            项
          </Badge>
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            未更改条目:{" "}
            {
              [...new Set(editedChanges.map((change) => change.recordId))].filter(
                (recordId) =>
                  !editedChanges.some((change) => change.recordId === recordId && change.oldValue !== change.newValue),
              ).length
            }{" "}
            项
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="selected">已选档案</TabsTrigger>
              <TabsTrigger value="records">台账表</TabsTrigger>
            </TabsList>

            <TabsContent value="selected" className="space-y-4">
              {renderSelectedTab()}
            </TabsContent>

            <TabsContent value="records" className="space-y-4">
              <div className="flex mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索统一编号或标题..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="ml-2">
                  <Button variant="outline" className="h-10 px-3">
                    <Filter className="h-4 w-4 mr-2" />
                    所有项目
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-2 h-4 w-4"
                    >
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                  </Button>
                </div>
              </div>
              {renderAccountTab()}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  )
}
