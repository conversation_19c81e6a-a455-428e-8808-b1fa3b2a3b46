---
description: 
globs: 
alwaysApply: false
---
# 项目结构概述

## 核心组件

档案流程管理系统主要由以下核心组件构成：

1. **archive_flow_manager** - 系统核心模块，负责协调各个子系统
2. **archive_processing** - 档案处理模块，负责处理和转换档案
   - `services/` - 包含档案处理相关的服务
   - `utils/` - 工具函数
   - `docs/` - 文档
   - `dto/` - 数据传输对象
3. **archive_records** - 档案记录管理模块，负责档案元数据管理
   - `services/` - 档案记录服务
   - `migrations/` - 数据库迁移文件
4. **report_issuing** - 报告生成模块，负责生成和发布报告
   - `services/` - 报告生成相关服务
   - `migrations/` - 数据库迁移文件
5. **test_suite** - 测试套件
   - `unit/` - 单元测试
   - `integration/` - 集成测试
   - `functional/` - 功能测试
   - `test_files/` - 测试用文件
6. **-frontend** - 前端应用
7. **media** - 媒体文件存储
   - `archives/` - 档案存储
   - `uploads/` - 上传文件
   - `temp_split/` - 临时分割文件

## 主要配置文件

- [manage.py](mdc:manage.py) - Django 管理命令入口
- [requirements.txt](mdc:requirements.txt) - 项目依赖
- [docker-compose.yml](mdc:docker-compose.yml) - Docker 配置
- [Dockerfile](mdc:Dockerfile) - Docker 镜像构建配置

## 文档目录

- **AgentReadme/** - 项目文档和规划信息
  - `planning_and_requirements/` - 项目规划和需求文档
  - `design/` - 设计文档
  - `function_map/` - 功能映射
  - `guides/` - 开发指南

