# ReportSplittingService与tasks.py集成设计文档

## 1. 概述与背景

### 1.1 文档目的

本文档详细阐述ReportSplittingService与现有tasks.py流程的集成设计方案，实现两步处理流程的集成。

### 1.2 关联文档

- [Final_Implementation_Plan_V-MAX.md](./Final_Implementation_Plan_V-MAX.md) - 总体实施计划
- [PDF印章检测方案.md](../seal_process/PDF印章检测方案.md) - MA章检测技术方案

### 1.3 核心目标

- 在现有PDF处理流程中集成报告分割功能
- 实现简化的两步处理流程
- 清理冗余参数，优化接口设计

### 1.4 关键业务规则

**🚨 强制性业务规则：档案必须包含报告**  

- 每个档案PDF都**必须**包含报告，这是不可违背的业务规则
- 如果档案PDF中未找到报告（无`report_page_range`或报告分割失败），整个处理流程必须失败
- 不存在"仅档案，无报告"的合法情况
- 报告分割失败视为整个档案处理失败，不进行降级处理

## 2. 流程对比分析

### 2.1 当前流程分析

```mermaid
graph TD
    A[perform_pre_check] --> B[validate_all_parts_processable]
    B --> C[档案分割 - create_temp_pdf_for_single_archive]
    C --> D[串行/并行处理]
    D --> E[archive_single_archive_pdf]
    E --> F[update_archive_record]
```

**关键发现**：

- `validate_all_parts_processable`已经执行了档案分割
- 返回的`processable_parts`包含`temp_path`（已分割的档案PDF）
- 后续处理直接使用这些临时文件

### 2.2 新流程设计（最终简化版本）

```mermaid
graph TD
    A[perform_pre_check] --> B[获取原始parts_info]
    B --> C[ReportRecognitionService.identify_report_ranges]
    C --> D[增强parts_info: 添加report_page_range]
    D --> E[validate_all_parts_processable - 扩展版]
    E --> F[档案分割 + 报告分割按页面范围]
    F --> G[返回 processable_parts 包含 archive_temp_path + report_temp_path]
    G --> H[串行/并行处理 - 两步流程]
    H --> I["第1步: archive_single_archive_report_pdf(统一编号, 档案路径, 报告路径)"]
    I --> J["第2步: update_archive_record(unified_number, 档案路径, 报告路径, task_id)"]
```

**关键变化**：

- ✅ **新增ReportRecognitionService步骤**：负责CMA章识别，生成`report_page_range`
- ✅ **简化报告分割逻辑**：直接按页面范围分割，不需要复杂的识别逻辑
- ✅ **职责分离清晰**：识别服务负责找位置，分割服务负责物理分割

## 3. 核心接口调整

### 3.1 FileStorageService接口变更

#### 3.1.1 原接口（删除）

```python
# 删除：不再使用
def archive_single_archive_pdf(temp_path, unified_number, original_pdf_path=None)
```

#### 3.1.2 新接口（替换）

```python
@staticmethod
def archive_single_archive_report_pdf(
    unified_number: str,
    archive_temp_path: str,
    report_temp_path: str
) -> Dict[str, Any]:
    """
    同时归档档案PDF和报告PDF到最终存储位置
    
    Args:
        unified_number: 统一编号
        archive_temp_path: 档案PDF临时文件路径
        report_temp_path: 报告PDF临时文件路径（必须）
        
    Returns:
        Dict[str, Any]: 归档结果
        {
            'success': bool,
            'archive_final_path': str,
            'report_final_path': str,
            'archive_url': str,
            'report_url': str,
            'error': str              # 如果失败
        }
    """
```

### 3.2 数据库更新接口变更

#### 3.2.1 原接口（删除相关参数）

```python
# 原：需要多个具体参数
def update_archive_record(
    unified_number, file_path, user_id, assigned_box_number  # ← 删除此参数
)
```

#### 3.2.2 新接口（扩展支持报告路径和任务关联）

```python
def update_archive_record(
    unified_number: str,
    archive_file_path: str,
    report_file_path: str,
    task_id: str
) -> Dict[str, Any]:
    """
    更新档案记录，支持档案和报告路径
    
    Args:
        unified_number: 统一编号
        archive_file_path: 档案PDF最终路径
        report_file_path: 报告PDF最终路径（必须）
        task_id: 任务ID，用于关联原PDF文件（必须，便于追溯）
        
    Returns:
        Dict[str, Any]: 更新结果
        {
            'success': bool,
            'record_id': int,               # ArchiveRecord.id
            'archive_datetime': str,        # 归档时间
            'unified_number': str,          # 统一编号
            'archive_url': str,             # 档案PDF访问URL
            'report_url': str,              # 报告PDF访问URL
            'archive_file_path': str,       # 档案PDF文件路径
            'report_file_path': str,        # 报告PDF文件路径
            'source_pdf_file': str,         # 关联的源PDF文件ID
            'error': str                    # 如果失败
        }
        
    内部实现逻辑：
    1. 通过task_id获取ProcessingTask，进而获取UploadedFile外键
    2. 获取Archive记录（通过unified_number）
    3. 更新Archive记录的archive_url、report_url、archive_datetime、source_file等字段
    4. 不处理archive_person字段（前端从source_file.uploader获取）
    
    事务处理：
    - 函数内部使用@transaction.atomic()装饰器确保原子性
    - 可以安全地在外层事务中调用（Django支持嵌套事务）
    """
```

### 3.3 清理assigned_box_number参数

需要在以下位置清理`assigned_box_number`参数：

#### 3.3.1 函数签名清理

```python
# 清理前
def validate_all_parts_processable(task_id, parts_info, pdf_path, assigned_box_number=None)
def process_pdf_serial_task(self, task_id)  # 内部使用assigned_box_number
def process_pdf_with_ocr_results_task(task_id)  # 内部使用assigned_box_number

# 清理后  
def validate_all_parts_processable(task_id, parts_info, pdf_path)
def process_pdf_serial_task(self, task_id)  # 不再使用assigned_box_number
def process_pdf_with_ocr_results_task(task_id)  # 不再使用assigned_box_number
```

#### 3.3.2 数据结构清理

```python
# part_summary结构清理
part_summary = {
    "unified_number": str,
    "archive_page_range": Tuple[int, int],   # 档案页码范围
    "report_page_range": Tuple[int, int],    # 报告页码范围（必须）
    "status": str,
    "archive_temp_path": str,
    "report_temp_path": str,                 # 报告临时文件路径（必须）
    "final_archive_path": str,
    "final_report_path": str,                # 最终报告路径（必须）
    "error": str,
    # 删除："assigned_box_number": str,
}
```

## 4. validate_all_parts_processable扩展设计

### 4.1 扩展后的函数逻辑

```python
def validate_all_parts_processable(
    task_id: str,
    parts_info: List[Dict],           # 扩展：包含archive_page_range和report_page_range
    pdf_path: str                     # 原始PDF路径
) -> Tuple[bool, List[Dict], List[Dict]]:
    """
    扩展版：验证所有部分并执行档案分割+报告分割
    
    Args:
        task_id: 处理任务ID
        parts_info: 部分信息列表，结构如下：
            [
                {
                    "unified_number": "A001", 
                    "archive_page_range": [0, 10],     # 档案页码范围
                    "report_page_range": [5, 10]       # 报告页码范围（必须，第5页开始）
                },
                {
                    "unified_number": "A002", 
                    "archive_page_range": [11, 20],    # 档案页码范围
                    "report_page_range": [15, 20]      # 报告页码范围（必须，第15页开始）
                }
            ]
            注意：每个档案部分都必须包含report_page_range，否则处理失败
        pdf_path: 原始PDF文件路径
    
    Returns:
        Tuple[bool, List[Dict], List[Dict]]:
            - 验证是否通过
            - 可处理的部分列表（包含archive_temp_path和report_temp_path）
            - 失败的部分列表
    """
    processable_parts = []
    failed_parts = []
    all_valid = True

    for part_info in parts_info:
        unified_number = part_info.get("unified_number")
        archive_page_range = part_info.get("archive_page_range")
        report_page_range = part_info.get("report_page_range")  # 强制要求：必须存在
        
        # === 第1步：档案分割（现有逻辑） ===
        temp_dir_path = FileStorageService.get_temp_directory()
        temp_dir = str(temp_dir_path)
        temp_filename = f"validate_{task_id}_{unified_number}.pdf"
        temp_pdf_path = os.path.join(temp_dir, temp_filename)
        
        try:
            start_page, end_page = archive_page_range
            split_success = pdf_utils.create_temp_pdf_for_single_archive(
                pdf_path, start_page + 1, end_page + 1, temp_pdf_path
            )
            
            if not split_success:
                all_valid = False
                failed_parts.append({
                    "unified_number": unified_number,
                    "archive_page_range": archive_page_range,
                    "status": "archive_split_failed",
                    "error": f"档案分割失败: {temp_pdf_path}",
                })
                continue
            
            # === 第2步：报告分割（新增逻辑） ===
            # 🚨 业务规则检查：档案必须包含报告
            if not report_page_range:
                all_valid = False
                failed_parts.append({
                    "unified_number": unified_number,
                    "archive_page_range": archive_page_range,
                    "status": "missing_report_range",
                    "error": f"业务规则违反: 档案 {unified_number} 必须包含报告，但未找到报告页面范围",
                })
                # 清理档案临时文件
                if os.path.exists(temp_pdf_path):
                    os.remove(temp_pdf_path)
                continue
            
            # 执行报告分割
            report_filename = f"report_{task_id}_{unified_number}.pdf"
            report_temp_path = os.path.join(temp_dir, report_filename)
            
            # 使用报告分割服务按页面范围分割
            report_service = ReportSplittingService()
            split_result = report_service.split_report_by_page_range(
                source_pdf_path=temp_pdf_path,
                output_report_path=report_temp_path,
                report_page_range=report_page_range
            )
            
            if not split_result["success"]:
                all_valid = False
                failed_parts.append({
                    "unified_number": unified_number,
                    "archive_page_range": archive_page_range,
                    "report_page_range": report_page_range,
                    "status": "report_split_failed",
                    "error": f"报告分割失败: {split_result['error_message']}",
                })
                # 清理档案临时文件
                if os.path.exists(temp_pdf_path):
                    os.remove(temp_pdf_path)
                continue
            
            # === 第3步：验证存储路径（现有逻辑） ===
            final_dir_path = FileStorageService.get_archive_storage_path(unified_number)
            if not final_dir_path:
                all_valid = False
                failed_parts.append({
                    "unified_number": unified_number,
                    "archive_page_range": archive_page_range,
                    "report_page_range": report_page_range,
                    "status": "storage_path_failed",
                    "error": f"无法确定归档存储路径: {unified_number}",
                })
                # 清理临时文件
                cleanup_temp_files([temp_pdf_path, report_temp_path])
                continue
            
            # 记录成功验证的部分
            processable_parts.append({
                "unified_number": unified_number,
                "archive_page_range": archive_page_range,
                "report_page_range": report_page_range,
                "archive_temp_path": temp_pdf_path,
                "report_temp_path": report_temp_path,
                "status": "valid",
            })
            
        except Exception as e:
            all_valid = False
            failed_parts.append({
                "unified_number": unified_number,
                "archive_page_range": archive_page_range,
                "report_page_range": report_page_range,
                "status": "validation_error",
                "error": f"验证过程异常: {str(e)}",
            })
            # 清理临时文件
            cleanup_temp_files([temp_pdf_path, report_temp_path])

    # 如果验证失败，清理所有临时文件
    if not all_valid:
        for part in processable_parts:
            cleanup_temp_files([
                part.get("archive_temp_path"), 
                part.get("report_temp_path")
            ])
        processable_parts.clear()
    
    return all_valid, processable_parts, failed_parts
```

### 4.2 辅助函数

```python
def cleanup_temp_files(file_paths: List[str]) -> None:
    """清理临时文件列表"""
    for file_path in file_paths:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"已清理临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败 {file_path}: {e}")

def cleanup_archived_files(archive_path: str, report_path: str) -> None:
    """清理已归档的文件（用于事务回滚后的补偿）"""
    for file_path in [archive_path, report_path]:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"已清理归档文件: {file_path}")
            except Exception as e:
                logger.warning(f"清理归档文件失败 {file_path}: {e}")
```

## 5. 串行处理流程调整

### 5.1 process_pdf_serial_task中的处理逻辑

```python
# 在process_pdf_serial_task中，处理每个验证通过的部分
for part in processable_parts:
    unified_number = part.get("unified_number")
    archive_temp_path = part.get("archive_temp_path")
    report_temp_path = part.get("report_temp_path")
    
    try:
        # === 第1步：归档（档案+报告） ===
        logger.info(f"任务 {task_id}: 开始归档部分 {unified_number}...")
        archive_result = FileStorageService.archive_single_archive_report_pdf(
            unified_number=unified_number,
            archive_temp_path=archive_temp_path,
            report_temp_path=report_temp_path
        )
        
        if not archive_result.get("success"):
            logger.error(f"任务 {task_id}: 归档失败: {archive_result.get('error')}")
            continue
        
        # === 第2步：数据库更新 ===
        final_archive_path = archive_result.get("archive_final_path")
        final_report_path = archive_result.get("report_final_path")
        
        update_result = update_archive_record(
            unified_number=unified_number,
            archive_file_path=final_archive_path,
            report_file_path=final_report_path,
            task_id=task_id
        )
        
        if update_result.get("success"):
            successful_updates += 1
            logger.info(f"任务 {task_id}: 成功处理部分 {unified_number}")
        else:
            failed_updates.append({
                "unified_number": unified_number,
                "error": update_result.get("error")
            })
            logger.error(f"任务 {task_id}: 更新记录失败: {update_result.get('error')}")
        
        # 统计更新
        successful_archives += 1
        
    except Exception as e:
        logger.error(f"任务 {task_id}: 处理部分 {unified_number} 时出错: {e}", exc_info=True)
        continue
```

## 6. 并行处理流程调整

### 6.1 process_pdf_with_ocr_results_task中的处理逻辑

```python
# 在process_pdf_with_ocr_results_task中的处理逻辑  
for part in processable_parts:
    unified_number = part.get("unified_number")
    archive_temp_path = part.get("archive_temp_path")
    report_temp_path = part.get("report_temp_path")
    
    try:
        # === 第1步：归档（档案+报告） ===
        archive_result = FileStorageService.archive_single_archive_report_pdf(
            unified_number=unified_number,
            archive_temp_path=archive_temp_path,
            report_temp_path=report_temp_path
        )
        
        if not archive_result.get("success"):
            error_msg = f"归档失败: {archive_result.get('error')}"
            failed_archives.append({
                "unified_number": unified_number,
                "error": error_msg
            })
            continue
        
        # 记录归档成功
        successful_archives += 1
        archived_files_list.append({
            "unified_number": unified_number,
            "archive_final_path": archive_result.get("archive_final_path"),
            "report_final_path": archive_result.get("report_final_path"),
        })
        
    except Exception as e:
        error_msg = f"处理部分时发生错误: {str(e)}"
        processing_errors.append(error_msg)
        failed_archives.append({
            "unified_number": unified_number,
            "error": str(e)
        })

# === 第2步：事务化数据库更新 ===
if len(failed_archives) == 0 and len(processing_errors) == 0:
    try:
        with transaction.atomic():
            # 批量更新数据库
            for item in archived_files_list:
                update_result = update_archive_record(
                    unified_number=item["unified_number"],
                    archive_file_path=item["archive_final_path"],
                    report_file_path=item["report_final_path"],
                    task_id=task_id
                )
                
                if not update_result.get("success"):
                    raise Exception(f"更新记录失败: {update_result.get('error')}")
            
            # 更新统计信息
            successful_updates = len(archived_files_list)
            logger.info(f"任务 {task_id}: 成功更新 {successful_updates} 条记录")
            
    except Exception as e:
        error_msg = f"数据库更新过程中发生错误: {str(e)}"
        logger.error(f"任务 {task_id}: {error_msg}", exc_info=True)
        # 事务会自动回滚，需要清理已归档的文件
        try:
            for item in archived_files_list:
                cleanup_archived_files(item["archive_final_path"], item["report_final_path"])
            logger.info(f"已清理因事务回滚产生的孤儿文件")
        except Exception as cleanup_error:
            logger.error(f"清理孤儿文件失败: {cleanup_error}", exc_info=True)
```

## 7. 配置和统计调整

### 7.1 配置结构简化

```python
PROCESSING_CONFIG_SCHEMA = {
    "pdf_processing": {
        "dpi": 150,
        "crop_ratio": 0.2,
        "batch_size": 5,
    },
    
    "report_splitting": {
        "enabled": True,
        "max_pages_to_check": 50,
        "ma_template_paths": [],
        "feature_match_threshold": 10,
        "timeout_seconds": 300,
    },
    
    "storage": {
        "archive_base_path": "archives",
        "create_subdirectories": True,
        "backup_enabled": False,
    }
}
```

### 7.2 统计信息结构

```python
part_summary = {
    "unified_number": str,
    "archive_page_range": Tuple[int, int],    # 档案页码范围
    "report_page_range": Tuple[int, int],     # 报告页码范围（必须）
    "status": str,  # pending|completed|archive_split_failed|report_split_failed|storage_failed|missing_report_range
    
    # 处理结果
    "archive_temp_path": str,      # 档案PDF临时路径
    "report_temp_path": str,       # 报告PDF临时路径（必须）
    "final_archive_path": str,     # 最终档案PDF路径
    "final_report_path": str,      # 最终报告PDF路径（必须）
    "archive_url": str,            # 档案PDF访问URL
    "report_url": str,             # 报告PDF访问URL（必须）
    
    # 错误信息
    "error": str,                  # 错误描述
    
    # 报告分割详情（如果有报告分割）
    "report_splitting": {
        "success": bool,
        "report_start_page": int,     # 报告起始页
        "report_end_page": int,       # 报告结束页
        "processing_time": float,
        "error_message": str,
    }
}
```

## 8. Task模型和数据流分析

### 8.1 Task模型关键字段

通过分析ProcessingTask模型，确认以下关键信息：

```python
class ProcessingTask(models.Model):
    task_id = models.UUIDField(primary_key=True)
    file = models.ForeignKey(UploadedFile)          # ← 关联原始PDF文件
    result_data = models.JSONField()                # ← 保存处理结果（包括归档路径）
    processing_params = models.JSONField()          # 处理参数
    status = models.CharField()                     # 任务状态
    # ... 其他字段
```

**关键发现**：

- ❌ **Task模型中没有temp_path字段** - temp_path只在`validate_all_parts_processable`中临时创建用于验证
- ✅ **Task有result_data字段** - 用于保存归档后的最终文件路径信息  
- ✅ **Task关联原始文件** - 通过file字段可获取原始PDF路径

### 8.2 简化数据流设计

采用用户建议的直接参数传递方式，避免复杂的JSON数据结构：

```python
# 直接参数传递，避免复杂的task.result_data结构
for part in processable_parts:
    unified_number = part.get("unified_number")
    archive_temp_path = part.get("archive_temp_path")
    report_temp_path = part.get("report_temp_path")
    
    # 1. 归档
    archive_result = archive_single_archive_report_pdf(
        unified_number=unified_number,
        archive_temp_path=archive_temp_path,
        report_temp_path=report_temp_path
    )
    
    # 2. 数据库更新（直接传递路径参数）
    update_archive_record(
        unified_number=unified_number,
        archive_file_path=archive_result["archive_final_path"],
        report_file_path=archive_result["report_final_path"],
        task_id=task_id  # 用于追溯关联
    )
```

**设计优势**：

- ✅ **参数明确**：函数参数直接明了，不需要从复杂结构中提取
- ✅ **解耦性强**：不依赖task.result_data的JSON结构
- ✅ **易于测试**：可以直接传递简单参数进行单元测试
- ✅ **追溯性好**：task_id直接关联原PDF文件

### 8.3 perform_pre_check复用确认

✅ **现有的`perform_pre_check`函数可以直接使用**，无需修改：

```python
def perform_pre_check(splitting_info_results, task_id):
    """
    数据完整性检查：
    1. 检查统一编号识别情况（是否为空）
    2. 调用check_records_exist()检查数据库记录存在性
    3. 返回检查结果和错误信息
    """
    # 现有逻辑完全适用，与报告分割无关
```

**原因**：预检查只关注统一编号和数据库记录，与PDF文件分割方式（档案分割+报告分割）无关。

## 9. 关键实现细节

### 9.1 update_archive_record内部实现

```python
def update_archive_record(
    unified_number: str,
    archive_file_path: str,
    report_file_path: str,
    task_id: str
) -> Dict[str, Any]:
    """更新档案记录，支持档案和报告路径"""
    try:
        # === 第1步：获取Archive记录 ===
        archive_record = Archive.objects.get(unified_number=unified_number)
        
        # === 第2步：通过task_id获取UploadedFile外键 ===
        uploaded_file = None
        if task_id:
            try:
                processing_task = ProcessingTask.objects.get(task_id=task_id)
                uploaded_file = processing_task.file  # 获取关联的UploadedFile
            except ProcessingTask.DoesNotExist:
                logger.warning(f"任务 {task_id} 不存在，跳过UploadedFile关联")
        
        # === 第3步：生成访问URL ===
        archive_url = generate_file_url(archive_file_path)
        report_url = generate_file_url(report_file_path)
        
        # === 第4步：更新Archive记录 ===
        current_time = timezone.now()
        archive_record.archive_url = archive_url
        archive_record.report_url = report_url
        archive_record.archive_datetime = current_time
        archive_record.archive_status = '已归档'
        
        # 关联原始PDF文件（如果task_id有效）
        if uploaded_file:
            archive_record.source_file = uploaded_file
            
        archive_record.save()
        
        return {
            'success': True,
            'record_id': archive_record.id,
            'archive_datetime': current_time.isoformat(),
            'unified_number': unified_number,
            'archive_url': archive_url,
            'report_url': report_url,
            'archive_file_path': archive_file_path,
            'report_file_path': report_file_path,
            'source_pdf_file': str(uploaded_file.file_id) if uploaded_file else None,
        }
        
    except Archive.DoesNotExist:
        return {
            'success': False,
            'error': f'档案记录不存在: {unified_number}'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'更新档案记录失败: {str(e)}'
        }
```

### 9.2 Archive模型字段调整

需要在Archive模型中添加以下字段：

```python
class Archive(models.Model):
    unified_number = models.CharField(max_length=50, unique=True)
    
    # 现有字段
    archive_file_path = models.CharField(max_length=500, blank=True, null=True)
    
    # 新增字段
    report_url = models.URLField(
        blank=True, 
        null=True, 
        verbose_name="报告URL链接",
        help_text="报告PDF访问URL"
    )
    # 注意：现有模型已有source_file字段，无需重复添加
    
    # 时间戳
    updated_at = models.DateTimeField(auto_now=True)
```

### 9.3 ReportSplittingService接口要求（简化版）

```python
class ReportSplittingService:
    """
    报告分割服务：负责按页面范围物理分割报告PDF
    职责简化：不负责CMA章识别，只负责按给定页面范围分割
    """
    
    def split_report_by_page_range(
        self, 
        source_pdf_path: str,
        output_report_path: str,
        report_page_range: List[int]  # [起始页, 结束页]
    ) -> Dict[str, Any]:
        """
        按照指定页面范围分割报告PDF
        
        Args:
            source_pdf_path: 源PDF路径（已分割的档案PDF）
            output_report_path: 输出报告PDF路径
            report_page_range: 报告页码范围 [起始页, 结束页]（1-based）
            
        Returns:
            {
                'success': bool,
                'report_start_page': int,     # 报告起始页
                'report_end_page': int,       # 报告结束页
                'output_path': str,           # 输出路径（如果成功）
                'processing_time': float,     # 处理耗时
                'error_message': str,         # 错误信息（如果失败）
            }
        """
        start_page, end_page = report_page_range
        start_time = time.time()
        
        try:
            # 执行PDF页面分割
            success = pdf_utils.create_temp_pdf_for_page_range(
                source_pdf_path, start_page, end_page, output_report_path
            )
            
            processing_time = time.time() - start_time
            
            return {
                'success': success,
                'report_start_page': start_page,
                'report_end_page': end_page,
                'output_path': output_report_path if success else None,
                'processing_time': processing_time,
                'error_message': None if success else "PDF页面分割失败"
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'success': False,
                'report_start_page': None,
                'report_end_page': None,
                'output_path': None,
                'processing_time': processing_time,
                'error_message': f"分割过程异常: {str(e)}"
            }
```

### 9.4 ReportRecognitionService接口要求（新增）

```python
class ReportRecognitionService:
    """
    报告识别服务：负责识别CMA章位置，确定报告页面范围
    这是一个独立的服务，在validate_all_parts_processable之前调用
    """
    
    def __init__(self, template_base_path: Optional[str] = None):
        """初始化报告识别服务"""
        self.template_base_path = template_base_path or self.get_default_template_path()
    
    def identify_report_ranges(
        self, 
        pdf_path: str, 
        parts_info: List[Dict]
    ) -> List[Dict]:
        """
        识别各部分的报告页面范围
        
        Args:
            pdf_path: 原始PDF路径
            parts_info: 原始部分信息（只包含unified_number和archive_page_range）
            
        Returns:
            扩展后的parts_info（包含report_page_range）
        """
        enhanced_parts_info = []
        
        for part in parts_info:
            unified_number = part["unified_number"]
            archive_page_range = part["archive_page_range"]
            
            # 在档案页面范围内查找CMA章
            report_start_page = self.detect_cma_seal_position(
                pdf_path, archive_page_range
            )
            
            enhanced_part = part.copy()
            if report_start_page:
                # 发现CMA章，设置报告页面范围
                enhanced_part["report_page_range"] = [
                    report_start_page, 
                    archive_page_range[1]  # 报告从CMA章开始到档案结尾
                ]
            
            enhanced_parts_info.append(enhanced_part)
            
        return enhanced_parts_info
    
    def detect_cma_seal_position(
        self, 
        pdf_path: str, 
        archive_page_range: List[int]
    ) -> Optional[int]:
        """
        在指定档案页面范围内检测CMA章位置
        
        Args:
            pdf_path: PDF文件路径
            archive_page_range: 档案页面范围 [起始页, 结束页]
            
        Returns:
            CMA章起始页位置（1-based），如果未找到返回None
        """
        # 具体的CMA章检测实现...
        pass
    
    @staticmethod
    def get_default_template_path() -> str:
        """获取默认的CMA章模板路径"""
        return os.path.join(settings.BASE_DIR, "templates", "ma_seal")
```

### 9.5 完整流程调用示例

```python
# === 示例：在串行处理中的完整调用流程 ===

# 1. 准备原始parts_info（只包含基本信息）
original_parts_info = [
    {
        "unified_number": "A001", 
        "archive_page_range": [0, 10]    # 只有档案页码范围
    },
    {
        "unified_number": "A002", 
        "archive_page_range": [11, 20]   # 只有档案页码范围
    }
]

# 2. 使用报告识别服务增强parts_info（新增步骤）
try:
    report_recognition = ReportRecognitionService()
    enhanced_parts_info = report_recognition.identify_report_ranges(
        pdf_path="/path/to/original.pdf",
        parts_info=original_parts_info
    )
except Exception as e:
    logger.error(f"报告识别失败: {e}", exc_info=True)
    # 根据业务规则，报告识别失败意味着整个处理失败
    raise Exception(f"报告识别失败，无法继续处理: {str(e)}")

# enhanced_parts_info可能变成：
# [
#     {
#         "unified_number": "A001", 
#         "archive_page_range": [0, 10],
#         "report_page_range": [5, 10]     # 识别出的报告页面范围
#     },
#     {
#         "unified_number": "A002", 
#         "archive_page_range": [11, 20]   # 没有识别出报告，无report_page_range
#     }
# ]

# 3. 调用扩展的验证函数
all_valid, processable_parts, failed_parts = validate_all_parts_processable(
    task_id=task_id,
    parts_info=enhanced_parts_info,  # 使用增强后的信息
    pdf_path="/path/to/original.pdf"
)

# 4. 处理每个验证通过的部分
for part in processable_parts:
    # 归档（档案+报告）
    archive_result = FileStorageService.archive_single_archive_report_pdf(
        unified_number=part["unified_number"],
        archive_temp_path=part["archive_temp_path"],
        report_temp_path=part["report_temp_path"]  # 强制要求：必须存在
    )
    
    # 数据库更新
    update_result = update_archive_record(
        unified_number=part["unified_number"],
        archive_file_path=archive_result["archive_final_path"],
        report_file_path=archive_result["report_final_path"],  # 强制要求：必须存在
        task_id=task_id  # 用于关联UploadedFile
    )
    
    logger.info(f"处理完成: {part['unified_number']}, "
                f"档案和报告均已成功处理")
```

## 10. 实施步骤

### 10.1 Phase 1: ReportRecognitionService开发

1. 实现CMA章检测算法
2. 开发`identify_report_ranges`函数
3. 配置模板管理机制
4. 单元测试和集成测试

### 10.2 Phase 2: ReportSplittingService开发

1. 实现`split_report_by_page_range`函数
2. 优化PDF页面分割性能
3. 完善错误处理机制
4. 验证分割质量

### 10.3 Phase 3: 核心流程集成

1. 扩展`validate_all_parts_processable`函数
2. 集成报告识别和分割逻辑
3. 修改串行和并行处理流程
4. 实现两步处理流程

### 10.4 Phase 4: 接口和数据库调整

1. **FileStorageService重构**：
   - 删除`archive_single_archive_pdf`函数
   - 实现`archive_single_archive_report_pdf`函数
   - 支持同时归档档案PDF和报告PDF

2. **update_archive_record函数增强**：
   - 支持报告URL更新
   - 实现task_id到UploadedFile的关联逻辑
   - 完善错误处理和事务控制

3. **参数清理**：
   - 清理`assigned_box_number`参数
   - 更新相关数据结构

### 10.5 Phase 5: 测试和优化

1. 单元测试
2. 集成测试
3. 性能测试
4. 错误场景测试
5. 文档更新

## 11. 风险评估

### 11.1 技术风险

| 风险项 | 影响等级 | 缓解措施 |
|--------|----------|----------|
| 报告分割失败率高 | 中 | 完善MA章检测算法；提供降级机制 |
| 临时文件清理不及时 | 低 | 实施定期清理；监控磁盘空间 |
| 数据库更新失败 | 高 | 事务化处理；数据一致性检查 |

### 11.2 兼容性风险

| 风险项 | 影响等级 | 缓解措施 |
|--------|----------|----------|
| 接口不兼容导致调用失败 | 高 | 全面测试；渐进式部署 |
| 数据结构变更影响下游 | 中 | 文档更新；通知相关团队 |

## 12. 总结

本次重构的核心变更：

1. **接口简化**：删除冗余的`assigned_box_number`参数
2. **流程优化**：改为两步处理（归档+数据库更新）
3. **集成增强**：在`validate_all_parts_processable`中集成报告分割
4. **参数明确化**：函数参数直接传递，避免复杂的数据结构

## 13. 关键技术决策和设计优势

### 13.1 参数设计优化

采用直接参数传递方式，避免复杂数据结构：

| 方面 | 原复杂设计 | 简化设计 ✅ |
|------|------------|------------|
| **参数传递** | 依赖task.result_data复杂结构 | 直接传递明确路径参数 |
| **函数解耦** | 函数间通过JSON数据耦合 | 函数参数独立明确 |
| **可测试性** | 需要构造复杂的task对象 | 直接传递简单参数 |
| **可读性** | 需要理解数据结构格式 | 参数含义一目了然 |
| **追溯性** | 难以关联回原PDF | task_id直接关联 |

### 13.2 业务逻辑简化

**parts_info结构对比**：

```python
# 原复杂设计
{
    "unified_number": "A001",
    "page_range": [0, 10],
    "enable_report_splitting": True,
    "report_type": "MA",
    "ma_template_base_path": "/path/to/template"
}

# 简化设计 ✅
{
    "unified_number": "A001", 
    "archive_page_range": [0, 10],     # 明确为档案页码范围
    "report_page_range": [5, 10]       # 报告页码范围（第5页开始）
}
```

**判断逻辑简化**：

- 原设计：`if enable_report_splitting and report_type == "MA"`
- 简化设计：`if report_page_range:` ✅

### 13.3 职责分离设计

1. **ReportRecognitionService**：负责CMA章识别，生成页面范围
2. **ReportSplittingService**：负责按页面范围物理分割PDF  
3. **validate_all_parts_processable**：只负责验证和调用
4. **FileStorageService**：负责文件归档和URL生成

### 13.4 现有逻辑复用

- **perform_pre_check直接复用**：数据完整性检查逻辑与报告分割无关
- **validate_all_parts_processable扩展**：在现有档案分割基础上增加报告分割
- **temp_path不保存到数据库**：临时文件路径仅在验证阶段使用

### 13.5 实施优势

1. **逻辑更清晰**：页面范围一目了然，判断简单
2. **接口简化**：移除复杂配置参数，服务内部管理配置  
3. **易于维护**：处理流程线性，错误处理简单
4. **渐进式集成**：可以先实现档案分割，后续再加入报告功能

这种设计保持了现有流程的稳定性，同时以最简洁的方式集成了报告分割功能，实现了更清晰和高效的处理流程。

## 15. 错误处理和恢复机制

### 15.1 业务规则失败处理

**场景**: 档案PDF中未找到报告（违反强制性业务规则）

**处理策略**:

```python
# 在validate_all_parts_processable中
if not report_page_range:
    # 立即失败，清理已分割的档案PDF
    if os.path.exists(temp_pdf_path):
        os.remove(temp_pdf_path)
    
    failed_parts.append({
        "unified_number": unified_number,
        "status": "missing_report_range",
        "error": f"业务规则违反: 档案 {unified_number} 必须包含报告"
    })
    continue  # 不处理该部分，继续下一个
```

### 15.2 报告识别失败处理

**场景**: ReportRecognitionService.identify_report_ranges失败

**处理策略**:

```python
try:
    enhanced_parts_info = report_recognition.identify_report_ranges(pdf_path, parts_info)
except Exception as e:
    logger.error(f"报告识别失败: {e}", exc_info=True)
    # 整个任务失败，更新任务状态
    task.status = 'failed'
    task.error_message = f"报告识别失败: {str(e)}"
    task.save()
    raise Exception(f"报告识别失败，无法继续处理: {str(e)}")
```

### 15.3 文件孤儿处理

**场景**: 数据库更新失败，但文件已归档

**处理策略**:

```python
# 在并行处理的事务回滚中
except Exception as e:
    # 清理已归档但数据库未更新的文件
    for item in archived_files_list:
        cleanup_archived_files(item["archive_final_path"], item["report_final_path"])
    
    # 记录清理日志
    logger.info(f"已清理因事务回滚产生的 {len(archived_files_list)} 对孤儿文件")
```

### 15.4 临时文件清理机制

**策略**: 多层清理确保无文件泄漏

```python
# 1. 单个部分失败时立即清理
cleanup_temp_files([temp_pdf_path, report_temp_path])

# 2. 整体验证失败时清理所有临时文件
if not all_valid:
    for part in processable_parts:
        cleanup_temp_files([part.get("archive_temp_path"), part.get("report_temp_path")])

# 3. 定期清理任务（建议添加）
@periodic_task(run_every=crontab(hour=2, minute=0))  # 每天凌晨2点
def cleanup_orphaned_temp_files():
    """清理超过24小时的孤儿临时文件"""
    # 实现逻辑...
```

### 15.5 实施完成后的系统状态

**系统保证**: 所有新处理的档案都包含档案PDF和报告PDF

**数据完整性**:

- 每个`ArchiveRecord`都有有效的`archive_url`和`report_url`
- 通过`source_file`字段可以追溯到原始PDF文件
- 任务失败时确保不会产生不完整的记录

**前端显示**:

```python
# 前端可以确信每个记录都有档案和报告链接
def show_archive_record_links(record):
    # 显示档案PDF链接
    display_archive_link(record.archive_url)
    # 显示报告PDF链接  
    display_report_link(record.report_url)
    # 两个链接都保证存在且有效
```

---
**文档版本**: V2.2  
**更新日期**: 2025-06-23  
**作者**: AI Assistant  
**变更**: 清理向后兼容逻辑，强化强制性业务规则描述，确保与实际实施一致
