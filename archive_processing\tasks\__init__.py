# This file makes the 'tasks' directory a Python package.
# By importing the tasks from other modules here, we make them discoverable by Celery.

from .core_tasks import (
    process_pdf_serial_task,
    process_pdf_parallel_task,
    cleanup_expired_files_task,
    process_pdf_ocr_task,
    aggregate_pdf_ocr_results_task,
    process_pdf_with_ocr_results_task,
    process_pdf_three_phase_coordinator_task,
)

from .cleanup import (
    periodic_cleanup_deleted_files,
    cleanup_stuck_tasks,
)

# It's good practice to define __all__ to specify what gets imported with 'from .tasks import *'
__all__ = [
    'process_pdf_serial_task',
    'process_pdf_parallel_task',
    'cleanup_expired_files_task',
    'process_pdf_ocr_task',
    'aggregate_pdf_ocr_results_task',
    'process_pdf_with_ocr_results_task',
    'process_pdf_three_phase_coordinator_task',
    'periodic_cleanup_deleted_files',
    'cleanup_stuck_tasks',
]
