import { PageTitle } from "@/components/page-title"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { UserEditForm } from "@/components/users/user-edit-form"

export default async function EditUserPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href={`/users/detail/${id}`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">返回</span>
          </Link>
        </Button>
        <PageTitle title="编辑用户" subtitle={`编辑用户 ID: ${id} 的信息`} />
      </div>

      <UserEditForm userId={id} />
    </div>
  )
}
