"use client"

import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Archive,
  ClipboardList,
  FileText,
  Upload,
  AlertCircle,
  Clock,
  FileEdit,
  Database,
  FileCheck,
  Calendar,
  FileArchive,
  AlertTriangle,
  Activity,
} from "lucide-react"
// TODO: Replace with NextAuth imports

import { But<PERSON> } from "@/components/ui/button"
import { PageTitle } from "@/components/page-title"
import Link from "next/link"
import { Progress } from "@/components/ui/progress"
import { ArchiveCalendar } from "@/components/archive/archive-calendar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ReportsTrendChart } from "@/components/domain/reports/reports-trend-chart"
import { PageLayout } from "@/components/common/page-layout"

export default function Dashboard() {
    // TODO: Replace with useSession() hook

  // 系统概览统计数据 - 按照四大功能块组织
  const stats = [
    // 台账记录统计
    {
      title: "档案条目数",
      value: "12,345",
      change: "+2.5%",
      trend: "up",
      icon: ClipboardList,
      permission: "view_records",
      href: "/records",
      description: "系统中的总档案条目数量",
      category: "records",
      color: "blue",
    },
    {
      title: "台账导入批次数",
      value: "78",
      change: "+12.3%",
      trend: "up",
      icon: Database,
      permission: "view_records",
      href: "/records/import",
      description: "已导入的台账批次总数",
      category: "records",
      color: "indigo",
    },

    // 归档统计
    {
      title: "已归档档案",
      value: "8,765",
      change: "+3.2%",
      trend: "up",
      icon: Archive,
      permission: "view_records",
      href: "/records?status=archived",
      description: "已完成归档的档案数量",
      category: "archive",
      color: "emerald",
    },
    {
      title: "待处理归档",
      value: "42",
      change: "-5.1%",
      trend: "down",
      icon: Clock,
      permission: "upload_pdf",
      href: "/archive?status=pending",
      description: "等待处理的归档任务",
      category: "archive",
      color: "amber",
    },

    // 报告发放统计
    {
      title: "今日已发放报告项数",
      value: "24",
      change: "+8.7%",
      trend: "up",
      icon: FileCheck,
      permission: "manage_reports",
      href: "/reports?status=completed&period=today",
      description: "今日已完成发放的报告数量",
      category: "reports",
      color: "green",
    },
    {
      title: "待处理发放单",
      value: "156",
      change: "+12.3%",
      trend: "up",
      icon: FileText,
      permission: "manage_reports",
      href: "/reports?status=pending",
      description: "等待处理的报告发放单",
      category: "reports",
      color: "orange",
    },

    // 更改单统计
    {
      title: "本月已归档更改单",
      value: "87",
      change: "+4.8%",
      trend: "up",
      icon: FileEdit,
      permission: "view_records",
      href: "/change-orders?status=archived&period=month",
      description: "本月已归档的更改单数量",
      category: "change_orders",
      color: "purple",
    },
    {
      title: "待处理更改单",
      value: "28",
      change: "-2.1%",
      trend: "down",
      icon: Clock,
      permission: "view_records",
      href: "/change-orders?status=pending",
      description: "等待处理的更改单数量",
      category: "change_orders",
      color: "rose",
    },
  ]

  // 告警信息
  const alerts = [
    {
      title: "台账导入异常",
      value: "5",
      icon: AlertTriangle,
      permission: "view_records",
      href: "/records/import?status=failed",
      description: "需要处理的台账导入异常",
    },
    {
      title: "PDF处理异常",
      value: "7",
      icon: AlertCircle,
      permission: "upload_pdf",
      href: "/archive?status=failed",
      description: "需要处理的PDF文件处理异常",
    },
  ]

  // 获取异常卡片的颜色
  const getAlertColor = (value: string) => {
    const numValue = Number.parseInt(value)
    if (numValue <= 3) return "blue"
    if (numValue <= 5) return "amber"
    if (numValue <= 10) return "orange"
    return "red"
  }

  // 最近活动数据
  const recentActivities = [
    {
      id: 1,
      user: "张三",
      action: "上传了新的PDF文件",
      target: "项目文档.pdf",
      time: "10分钟前",
      icon: Upload,
      color: "blue",
    },
    {
      id: 2,
      user: "李四",
      action: "创建了新的报告发放单",
      target: "REP-2023-0042",
      time: "30分钟前",
      icon: FileText,
      color: "green",
    },
    {
      id: 3,
      user: "王五",
      action: "归档了文件",
      target: "合同文件-2023-156",
      time: "1小时前",
      icon: Archive,
      color: "amber",
    },
    {
      id: 4,
      user: "赵六",
      action: "创建了更改单",
      target: "CHG-2023-0028",
      time: "2小时前",
      icon: FileEdit,
      color: "purple",
    },
    {
      id: 5,
      user: "系统",
      action: "自动归档了过期文件",
      target: "15个文件",
      time: "昨天",
      icon: Clock,
      color: "gray",
    },
  ]

  // 按类别分组统计数据
  const recordsStats = stats.filter((stat) => stat.category === "records")
  const archiveStats = stats.filter((stat) => stat.category === "archive")
  const reportsStats = stats.filter((stat) => stat.category === "reports")
  const changeOrdersStats = stats.filter((stat) => stat.category === "change_orders")

  // 系统健康状态
  const systemHealth = {
    status: "正常",
    uptime: "30天4小时",
    lastBackup: "2023-05-15 03:00",
    storageUsed: 68,
  }

  return (
    <PageLayout 
      title="系统概览" 
      subtitle="查看系统关键指标和最近活动"
    >
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...recordsStats, ...archiveStats, ...reportsStats, ...changeOrdersStats].map((stat, index) => {
            // CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护
            
            return (
              <Link href={stat.href} key={index} passHref>
                <Card
                  className={`hover:shadow-lg transition-shadow cursor-pointer border-${stat.color}-500`}
                >
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                    <stat.icon className={`h-5 w-5 text-${stat.color}-600`} />
                  </CardHeader>
                  <CardContent>
                    <div className={`text-2xl font-bold text-${stat.color}-700`}>{stat.value}</div>
                    <p className="text-xs text-muted-foreground">
                      <span
                        className={stat.trend === "up" ? "text-green-600" : "text-red-600"}
                      >
                        {stat.change}
                      </span>{" "}
                      {stat.description.split(" ").slice(-3).join(" ")}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            )
          })}
        </div>

        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none shadow-md">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold text-blue-800">系统状态</CardTitle>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-green-500"></div>
                <span className="text-sm font-medium text-green-700">运行正常</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col">
                <span className="text-sm text-blue-700 font-medium">系统运行时间</span>
                <div className="flex items-center gap-2 mt-1">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-semibold">{systemHealth.uptime}</span>
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-blue-700 font-medium">最后备份时间</span>
                <div className="flex items-center gap-2 mt-1">
                  <Archive className="h-4 w-4 text-blue-600" />
                  <span className="font-semibold">{systemHealth.lastBackup}</span>
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-blue-700 font-medium">存储空间使用</span>
                <div className="flex items-center gap-2 mt-1 w-full">
                  <Database className="h-4 w-4 text-blue-600" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium">{systemHealth.storageUsed}%</span>
                      <span className="text-xs text-gray-500">100GB 可用</span>
                    </div>
                    <Progress value={systemHealth.storageUsed} className="h-2" />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-red-700">告警信息</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {alerts.map((alert, index) => (
              <Link href={alert.href} key={index} passHref>
                <Card
                  className={`hover:shadow-lg transition-shadow cursor-pointer border-${getAlertColor(alert.value)}-500`}
                >
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{alert.title}</CardTitle>
                    <alert.icon className={`h-5 w-5 text-${getAlertColor(alert.value)}-600`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-${getAlertColor(alert.value)}-700">{alert.value}</div>
                    <p className="text-xs text-muted-foreground">{alert.description}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">最近活动</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-4">
                {recentActivities.map((activity) => (
                  <li key={activity.id} className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full bg-${activity.color}-100`}>
                      <activity.icon className={`h-5 w-5 text-${activity.color}-600`} />
                    </div>
                    <div>
                      <p className="text-sm">
                        {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
                        <span className="font-medium">{activity.user}</span> {activity.action}{" "}
                        <span className="font-semibold text-blue-600">{activity.target}</span>
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <div className="lg:col-span-2 space-y-6">
            {/* CHANGE: [2025-06-12] 移除权限检查，使用全局登录保护 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-800">报告发放趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <ReportsTrendChart />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}

function cn(...classes: any[]) {
  return classes.filter(Boolean).join(" ")
} 