import { PageTitle } from "@/components/page-title"
import { UserForm } from "@/components/users/user-form"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NewUserPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/users">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">返回</span>
          </Link>
        </Button>
        <PageTitle title="新建用户" subtitle="创建新的系统用户并分配权限" />
      </div>

      <Card>
        <CardContent className="pt-6">
          <UserForm />
        </CardContent>
      </Card>
    </div>
  )
}
