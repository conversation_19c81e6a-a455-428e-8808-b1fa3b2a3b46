# Operation Document: Final Refactor of _safe_bulk_create for Robust Transaction Handling

## 📋 Change Summary

**Purpose**: To definitively resolve the persistent "You can't execute queries until the end of the 'atomic' block." error during Excel imports by thoroughly refactoring the exception handling logic within the `_safe_bulk_create` method.
**Scope**: Major refactoring of the `_safe_bulk_create` method in `archive_records/services/excel_import.py`.
**Associated**: Addresses the root cause of the transaction error identified in the latest server logs, where a `DataError` ("value too long") was caught by a generic `except Exception` and retried on an already broken transaction.

## 🔧 Operation Steps

### 📊 OP-001: Analyze Server Logs and Pinpoint Root Cause in `_safe_bulk_create`

**Precondition**: Despite previous fixes, the transaction error persists. New server logs show a `DataError` ("value too long") during `bulk_create`, followed by retries on a broken transaction.
**Operation**:
    1. Examined the server log sequence: initial `bulk_create` fails with `DataError` ("value too long").
    2. This `DataError` was caught by the generic `except Exception as e:` within `_safe_bulk_create`'s retry loop (around L1535 of the version at that time).
    3. This generic handler logged the error and retried the `bulk_create` operation.
    4. The retried `bulk_create` then immediately failed with "An error occurred in the current transaction..." because the initial `DataError` had already broken the transaction state.
    5. This broken transaction error then propagated upwards, causing the user-facing 500 error.
**Postcondition**: Identified that the generic `except Exception:` in `_safe_bulk_create` incorrectly handled `DataError` (and potentially other non-`IntegrityError` database errors) by retrying instead of immediately re-throwing.

### ✏️ OP-002: Refactor Exception Handling in `_safe_bulk_create`

**Precondition**: The `_safe_bulk_create` method's exception handling did not sufficiently differentiate between `IntegrityError` (for unique constraint fallback), other `django.db.Error` types (like `DataError`), and truly non-DB exceptions.
**Operation**:
    1. Restructured the `try-except` blocks within `_safe_bulk_create`:
        - **`try ArchiveRecord.objects.bulk_create(records)`** is the primary operation.
        - **`except django.db.utils.IntegrityError as ie:`**: This block is now more specific. If the `IntegrityError` contains "UNIQUE constraint" AND it's the first attempt (`retries == 0`), it logs the issue and proceeds to the fallback mechanism of creating records individually. The individual creation loop itself was already updated in a prior step to re-throw any `django.db.Error` it encounters. If the `IntegrityError` is not a unique constraint for fallback or occurs on a retry of `bulk_create`, it's logged and re-thrown.
        - **`except django.db.Error as dbe:` (New Block)**: This new block specifically catches any other `django.db.Error` (e.g., `DataError` like "value too long") that is not an `IntegrityError`. These errors are logged and re-thrown *immediately*. This is crucial as retrying `bulk_create` after such an error is futile and occurs on a broken transaction.
        - **`except Exception as e:`**: This block now only catches non-database exceptions. It contains the retry logic (`while retries < max_retries`). If retries are exhausted, the non-DB exception is re-thrown.
    2. Ensured logging messages are clear and provide `exc_info=True` for better debugging.
**Postcondition**: The `_safe_bulk_create` method now handles exceptions with more precision:
    - Unique constraint `IntegrityError` on the first attempt leads to a single attempt at individual record creation (which itself propagates DB errors).
    - Other `IntegrityError` or `IntegrityError` on retries are re-thrown.
    - Other `django.db.Error` (like `DataError`) are immediately re-thrown, preventing retries on broken transactions.
    - Only non-DB `Exception` types trigger the retry loop for the `bulk_create` operation.
    This structure should prevent the transaction from breaking due to mishandled database errors within `_safe_bulk_create`.

## 📝 Change Details

### CH-001: Refactor `_safe_bulk_create` Exception Handling Logic

**File**: `archive_records/services/excel_import.py`
**Method**: `_safe_bulk_create`
**Before** (simplified problematic flow):

```python
# try:
#     ArchiveRecord.objects.bulk_create(records)
# except django.db.utils.IntegrityError as e:
#     if "UNIQUE constraint" in str(e) and retries < max_retries - 1:
#         # Fallback to individual create (which might have its own issues)
#     else:
#         raise
# except Exception as e: # Problem: Catches DataError here
#     if retries < max_retries - 1:
#         retries += 1
#         logger.warning(f"批量创建失败，重试 {retries}/{max_retries}: {str(e)}") # Retries on broken transaction
#     else:
#         raise
```

**After** (summarized refined flow):

```python
# try:
#     ArchiveRecord.objects.bulk_create(records)
# except django.db.utils.IntegrityError as ie:
#     if "UNIQUE constraint" in str(ie) and retries == 0: # Fallback only on first attempt for UNIQUE
#         # Attempt individual creates (this loop now correctly re-throws its own DB errors)
#     else: # Other IntegrityErrors or IntegrityError on retry
#         raise ie
# except django.db.Error as dbe: # NEW: Catch other DB errors like DataError
#     logger.error(f"批量创建时发生数据库错误 (例如 DataError): {str(dbe)}", exc_info=True)
#     raise dbe # Re-throw immediately
# except Exception as e: # For non-DB errors for the bulk_create operation itself
#     if retries < max_retries - 1:
#         retries += 1
#         logger.warning(f"批量创建时发生非数据库错误，重试 {retries}/{max_retries}: {str(e)}")
#         time.sleep(0.5)
#     else:
#         logger.error(f"批量创建时发生非数据库错误，已达最大重试次数: {str(e)}", exc_info=True)
#         raise e
```

**Rationale**: This hierarchical and specific exception handling in `_safe_bulk_create` ensures that database errors that compromise transaction integrity (like `DataError` or non-fallback `IntegrityError`) are immediately propagated. This prevents the retry mechanism from operating on an already broken transaction. The fallback to individual inserts is now a more controlled, one-time attempt for specific unique constraint issues. This comprehensive approach should finally eliminate the "You can't execute queries until the end of the 'atomic' block." error originating from this part of the code.

## ✅ Verification Results

**Method**: Analysis of server logs and detailed code review of the `_safe_bulk_create` method and its interaction with the calling `transaction.atomic()` block in `_process_dataframe`.
**Results**: The implemented changes create a robust error handling flow that should correctly manage various database error scenarios during bulk creation, maintain transaction integrity, and prevent the specific error reported by the user.
**Problems**: None anticipated. This is believed to be the definitive fix for the transaction errors discussed.
**Solutions**: N/A.
