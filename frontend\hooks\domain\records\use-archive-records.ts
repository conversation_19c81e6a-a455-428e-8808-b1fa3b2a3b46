/**
 * 档案记录管理Hook
 * 
 * 提供档案记录的状态管理和API交互功能
 * 使用React Query进行数据缓存和状态管理
 * 
 * <AUTHOR> Team
 * @since 2025-06-19
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import archiveRecordService from '@/services/domain/records/archive-record-service';
import type { 
  ArchiveRecord, 
  ArchiveRecordQueryParams,
  ArchiveRecordPaginatedResponse 
} from '@/types/archive-record';
import { useToast } from '@/components/ui/use-toast';

// ==================== Hook类型定义 ====================

export interface UseArchiveRecordsOptions {
  initialParams?: ArchiveRecordQueryParams;
  enableAutoRefresh?: boolean;
  refetchInterval?: number;
}

export interface UseArchiveRecordsReturn {
  // 数据状态
  records: ArchiveRecord[];
  totalCount: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // 查询参数状态
  queryParams: ArchiveRecordQueryParams;
  setQueryParams: (params: ArchiveRecordQueryParams) => void;
  updateQueryParam: (key: keyof ArchiveRecordQueryParams, value: any) => void;
  
  // 分页状态
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // 操作方法
  refetch: () => void;
  goToPage: (page: number) => void;
  setPageSize: (size: number) => void;
  search: (searchTerm: string) => void;
  filter: (filters: Partial<ArchiveRecordQueryParams>) => void;
  sort: (field: string, direction?: 'asc' | 'desc') => void;
  
  // 数据操作
  createRecord: (data: Partial<ArchiveRecord>) => Promise<ArchiveRecord>;
  updateRecord: (id: number, data: Partial<ArchiveRecord>) => Promise<ArchiveRecord>;
  deleteRecord: (id: number) => Promise<void>;
  batchUpdateStatus: (ids: number[], status: string) => Promise<void>;
  
  // 选中状态管理
  selectedIds: number[];
  setSelectedIds: (ids: number[]) => void;
  toggleSelection: (id: number) => void;
  selectAll: () => void;
  clearSelection: () => void;
  isSelected: (id: number) => boolean;
}

// ==================== Query Keys ====================

export const archiveRecordKeys = {
  all: ['archiveRecords'] as const,
  lists: () => [...archiveRecordKeys.all, 'list'] as const,
  list: (params: ArchiveRecordQueryParams) => [...archiveRecordKeys.lists(), params] as const,
  details: () => [...archiveRecordKeys.all, 'detail'] as const,
  detail: (id: number) => [...archiveRecordKeys.details(), id] as const,
  statistics: () => [...archiveRecordKeys.all, 'statistics'] as const,
};

// ==================== 主Hook ====================

export function useArchiveRecords(options: UseArchiveRecordsOptions = {}): UseArchiveRecordsReturn {
  const { 
    initialParams = {}, 
    enableAutoRefresh = false, 
    refetchInterval = 30000 
  } = options;

  const queryClient = useQueryClient();
  const { toast } = useToast();

  // 查询参数状态
  const [queryParams, setQueryParams] = useState<ArchiveRecordQueryParams>({
    page: 1,
    pageSize: 20,
    ...initialParams,
  });

  // 选中状态
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  // 获取档案记录列表
  const {
    data: response,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: archiveRecordKeys.list(queryParams),
    queryFn: () => archiveRecordService.getArchiveRecords(queryParams),
    refetchInterval: enableAutoRefresh ? refetchInterval : false,
    staleTime: 5 * 60 * 1000, // 5分钟
  });

  // 数据解构
  const records = response?.results || [];
  const totalCount = response?.count || 0;
  const currentPage = queryParams.page || 1;
  const pageSize = queryParams.pageSize || 20;
  const totalPages = Math.ceil(totalCount / pageSize);
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;

  // 创建记录
  const createMutation = useMutation({
    mutationFn: (data: Partial<ArchiveRecord>) => 
      archiveRecordService.createArchiveRecord(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: archiveRecordKeys.lists() });
      toast({
        title: '创建成功',
        description: '档案记录已成功创建',
      });
    },
    onError: (error) => {
      toast({
        title: '创建失败',
        description: error.message || '创建档案记录时发生错误',
        variant: 'destructive',
      });
    },
  });

  // 更新记录
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ArchiveRecord> }) =>
      archiveRecordService.updateArchiveRecord(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: archiveRecordKeys.lists() });
      queryClient.invalidateQueries({ queryKey: archiveRecordKeys.details() });
      toast({
        title: '更新成功',
        description: '档案记录已成功更新',
      });
    },
    onError: (error) => {
      toast({
        title: '更新失败',
        description: error.message || '更新档案记录时发生错误',
        variant: 'destructive',
      });
    },
  });

  // 删除记录
  const deleteMutation = useMutation({
    mutationFn: (id: number) => archiveRecordService.deleteArchiveRecord(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: archiveRecordKeys.lists() });
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== deleteMutation.variables));
      toast({
        title: '删除成功',
        description: '档案记录已成功删除',
      });
    },
    onError: (error) => {
      toast({
        title: '删除失败',
        description: error.message || '删除档案记录时发生错误',
        variant: 'destructive',
      });
    },
  });

  // 批量更新状态
  const batchUpdateMutation = useMutation({
    mutationFn: ({ ids, status }: { ids: number[]; status: string }) =>
      archiveRecordService.batchUpdateStatus(ids, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: archiveRecordKeys.lists() });
      clearSelection();
      toast({
        title: '批量更新成功',
        description: '所选记录状态已成功更新',
      });
    },
    onError: (error) => {
      toast({
        title: '批量更新失败',
        description: error.message || '批量更新状态时发生错误',
        variant: 'destructive',
      });
    },
  });

  // 操作方法
  const updateQueryParam = (key: keyof ArchiveRecordQueryParams, value: any) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      page: key === 'pageSize' ? 1 : prev.page, // 改变页面大小时重置到第一页
    }));
  };

  const goToPage = (page: number) => {
    updateQueryParam('page', page);
  };

  const setPageSize = (size: number) => {
    updateQueryParam('pageSize', size);
  };

  const search = (searchTerm: string) => {
    updateQueryParam('search', searchTerm);
  };

  const filter = (filters: Partial<ArchiveRecordQueryParams>) => {
    setQueryParams(prev => ({
      ...prev,
      ...filters,
      page: 1, // 过滤时重置到第一页
    }));
  };

  const sort = (field: string, direction: 'asc' | 'desc' = 'desc') => {
    const ordering = direction === 'asc' ? field : `-${field}`;
    updateQueryParam('ordering', ordering);
  };

  // 选中状态管理
  const toggleSelection = (id: number) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  };

  const selectAll = () => {
    const allIds = records.map(record => record.id);
    setSelectedIds(allIds);
  };

  const clearSelection = () => {
    setSelectedIds([]);
  };

  const isSelected = (id: number) => {
    return selectedIds.includes(id);
  };

  // 数据操作包装
  const createRecord = async (data: Partial<ArchiveRecord>): Promise<ArchiveRecord> => {
    const result = await createMutation.mutateAsync(data);
    return result;
  };

  const updateRecord = async (id: number, data: Partial<ArchiveRecord>): Promise<ArchiveRecord> => {
    const result = await updateMutation.mutateAsync({ id, data });
    return result;
  };

  const deleteRecord = async (id: number): Promise<void> => {
    await deleteMutation.mutateAsync(id);
  };

  const batchUpdateStatus = async (ids: number[], status: string): Promise<void> => {
    await batchUpdateMutation.mutateAsync({ ids, status });
  };

  return {
    // 数据状态
    records,
    totalCount,
    isLoading,
    isError,
    error: error as Error | null,
    
    // 查询参数状态
    queryParams,
    setQueryParams,
    updateQueryParam,
    
    // 分页状态
    currentPage,
    pageSize,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    
    // 操作方法
    refetch,
    goToPage,
    setPageSize,
    search,
    filter,
    sort,
    
    // 数据操作
    createRecord,
    updateRecord,
    deleteRecord,
    batchUpdateStatus,
    
    // 选中状态管理
    selectedIds,
    setSelectedIds,
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected,
  };
}

// ==================== 单个记录详情Hook ====================

export function useArchiveRecord(id: number) {
  const { toast } = useToast();

  const query = useQuery({
    queryKey: archiveRecordKeys.detail(id),
    queryFn: () => archiveRecordService.getArchiveRecord(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });

  // 处理错误状态
  if (query.error) {
    toast({
      title: '获取详情失败',
      description: query.error.message || '获取档案记录详情时发生错误',
      variant: 'destructive',
    });
  }

  return query;
}

// ==================== 统计信息Hook ====================

export function useArchiveStatistics() {
  return useQuery({
    queryKey: archiveRecordKeys.statistics(),
    queryFn: () => archiveRecordService.getArchiveStatistics(),
    staleTime: 2 * 60 * 1000, // 2分钟
  });
} 