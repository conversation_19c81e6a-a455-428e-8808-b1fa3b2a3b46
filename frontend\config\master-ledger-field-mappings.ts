/**
 * 总台账字段映射配置
 * 将camelCase字段名映射到中文显示名称
 * CHANGE: [2025-06-19] 应用前后端字段命名统一迁移指南，从snake_case迁移到camelCase
 */

// 基础标识信息字段映射
export const BASIC_IDENTIFICATION_FIELDS = {
  sampleNumber: "样品编号",
  commissionNumber: "委托编号", 
  unifiedNumber: "统一编号",
  reportNumber: "报告编号",
  provinceUnifiedNumber: "省统一编号",
  stationCode: "站点编号",
  organizationCode: "机构代号",
  accountFromExcel: "账号"
} as const;

// 项目与委托信息字段映射
export const PROJECT_COMMISSION_FIELDS = {
  projectName: "工程名称",
  clientUnit: "委托单位",
  projectNumber: "工程编号", 
  subProject: "分项工程",
  projectLocation: "工程部位",
  projectAddress: "工程地址",
  clientName: "委托人",
  commissionDatetime: "委托日期"
} as const;

// 试验与结果信息字段映射
export const TEST_RESULT_FIELDS = {
  testResult: "检测结果",
  conclusion: "结论",
  testParameters: "检测参数",
  unqualifiedParameters: "不合格参数",
  testStartDatetime: "测试开始日期",
  testEndDatetime: "测试结束日期",
  testPerson1: "试验人1",
  testPerson2: "试验人2", 
  dataEntryPerson: "数据录入人"
} as const;

// 档案生命周期信息字段映射
export const ARCHIVE_LIFECYCLE_FIELDS = {
  archiveStatus: "归档状态",
  archiveBoxNumber: "档案盒号",
  currentStatus: "当前数据状态",
  processingStatus: "待处理状态",
  changeCount: "更改次数",
  archiveUrl: "档案URL链接",
  attachmentsFromExcel: "附件",
  storageDatetime: "入库日期",
  storagePerson: "入库人",
  outboundDatetime: "出库日期", 
  outboundPerson: "出库人",
  archiveDatetime: "归档日期",
  archivePerson: "归档人"
} as const;

// 报告管理信息字段映射
export const REPORT_MANAGEMENT_FIELDS = {
  reportIssueStatus: "报告发放状态",
  firstIssueCopies: "第一次发放份数",
  firstIssueDatetime: "第一次发放日期",
  firstIssuePerson: "第一次发放人",
  firstReceiverName: "第一次领取人",
  firstReceiverUnit: "第一次领取单位",
  firstReceiverPhone: "第一次领取人电话",
  secondIssueCopies: "第二次发放份数",
  secondIssueDatetime: "第二次发放日期",
  secondIssuePerson: "第二次发放人",
  secondReceiverName: "第二次领取人",
  secondReceiverUnit: "第二次领取单位",
  secondReceiverPhone: "第二次领取人电话",
  totalIssueCopies: "总发放份数"
} as const;

// 样品信息字段映射
export const SAMPLE_INFO_FIELDS = {
  groupNumber: "组号",
  sampleName: "样品/项目名称",
  assignedPerson: "分配人",
  componentCount: "构件(桩)数",
  testPointCount: "测点数",
  unqualifiedPointCount: "不合格点数",
  sampleRetentionDatetime: "样品留样时间",
  sampleRemainingTime: "样品剩余时间(天)"
} as const;

// 财务信息字段映射
export const FINANCIAL_FIELDS = {
  paymentStatus: "付款状态",
  priceAdjustmentStatus: "价格调整状态",
  standardPrice: "标准价格费用",
  discountPrice: "折扣价格费用",
  actualPrice: "实际价格费用"
} as const;

// 系统元数据字段映射
export const SYSTEM_METADATA_FIELDS = {
  importUserName: "导入人",
  importDate: "导入时间",
  batchNumber: "批次号",
  sourceSystem: "数据来源系统",
  createdAt: "创建时间", 
  updatedAt: "更新时间"
} as const;

// 合并所有字段映射 - 总台账完整字段映射
export const MASTER_LEDGER_FIELD_MAPPINGS = {
  ...BASIC_IDENTIFICATION_FIELDS,
  ...PROJECT_COMMISSION_FIELDS,
  ...TEST_RESULT_FIELDS,
  ...ARCHIVE_LIFECYCLE_FIELDS,
  ...REPORT_MANAGEMENT_FIELDS,
  ...SAMPLE_INFO_FIELDS,
  ...FINANCIAL_FIELDS,
  ...SYSTEM_METADATA_FIELDS
} as const;

// 字段分组配置 - 用于需要分组显示的场景
export const FIELD_GROUPS = {
  基础标识: BASIC_IDENTIFICATION_FIELDS,
  项目与委托: PROJECT_COMMISSION_FIELDS,
  试验与结果: TEST_RESULT_FIELDS,
  档案流转状态: ARCHIVE_LIFECYCLE_FIELDS,
  报告管理: REPORT_MANAGEMENT_FIELDS,
  样品信息: SAMPLE_INFO_FIELDS,
  财务信息: FINANCIAL_FIELDS,
  系统信息: SYSTEM_METADATA_FIELDS
} as const;

// 工具函数：根据字段名获取中文显示名称
export function getFieldDisplayName(fieldName: string): string {
  return MASTER_LEDGER_FIELD_MAPPINGS[fieldName as keyof typeof MASTER_LEDGER_FIELD_MAPPINGS] || fieldName;
}

// 工具函数：获取某个分组的所有字段
export function getFieldsByGroup(groupName: keyof typeof FIELD_GROUPS): Record<string, string> {
  return FIELD_GROUPS[groupName];
}

// 工具函数：获取所有字段名列表
export function getAllFieldNames(): string[] {
  return Object.keys(MASTER_LEDGER_FIELD_MAPPINGS);
}

// 工具函数：获取所有显示名称列表
export function getAllDisplayNames(): string[] {
  return Object.values(MASTER_LEDGER_FIELD_MAPPINGS);
} 