# Excel导入功能的会话管理需求与解决方案

## 1. 引言与问题背景

Excel导入作为一项关键的数据交互功能，其会话管理机制的完善程度直接影响到用户操作的流畅性、数据的准确性、多用户协作的效率以及系统资源的有效利用。当前或潜在的会话管理机制可能面临以下具体挑战：

1. **用户操作的即时响应与状态同步**：当用户执行如"取消导入"等操作时，系统前端与后端的状态可能无法瞬时、完全同步。这可能导致用户在取消后仍感知到旧状态，无法立即开始新的导入流程，或看到与实际业务状态不符的界面，需要通过刷新等额外操作才能恢复，影响操作的直接性和顺畅性。

2. **会话的有效占用与及时释放**：
    * **长时间固定超时问题**：传统的基于长时间固定超时（例如30分钟）来判断会话是否"僵死"并允许释放的机制，在快节奏的业务场景下可能过于迟钝。一个实际上已被用户放弃处理的会话（如因浏览器崩溃、用户离岗等）可能会长时间无效占用系统资源和全局唯一的导入名额，阻碍其他用户及时使用该功能。
    * **缺乏主动的活性检测**：系统可能缺少一种机制来主动、及时地检测当前会话处理者的真实活跃状态，导致对"僵尸会话"的判断和处理滞后。

3. **多用户协作环境下的挑战**：
    * **全局唯一活动会话的管理**：在允许多个用户可能关注或有权操作Excel导入功能的场景下，如果缺乏一个明确的、系统级的"当前唯一活动导入会话"机制，可能会导致用户间的操作冲突、状态误判，或不清楚当前系统是否已有其他用户正在进行导入。
    * **会话接管的迫切性与复杂性**：当一个活动会话的处理者因故中断操作时，其他有权限的用户或管理员需要一种清晰、可靠且及时的机制来接管该会话，以保证业务流程的持续性。现有的接管逻辑可能不够灵活或响应不够迅速。

4. **前端UI展示与状态同步的复杂性**：
    * **确保UI反映唯一的"真实"状态**：在系统状态可能因后台任务、当前用户操作、其他潜在协同用户操作或系统自动机制（如超时、心跳中断）而动态变化时，前端UI如何准确、及时且高效地追踪并展示那个当前系统中"唯一权威的"活动会话状态，是一个核心挑战。
    * **避免基于陈旧或不完整信息的UI决策**：如果前端UI仅基于简单的状态名（如"分析完成"）或不完整的本地缓存信息来决定是否更新，当实际的活动会话实例（Session ID）已在后端发生变更时，用户可能会继续看到针对旧会话的过时数据，导致误操作。
    * **渲染性能考量**：在需要频繁与后端同步状态（如通过轮询）的场景下，如何避免因后端数据（即使是同一会话的）微小或非关键性变动而导致的前端不必要的、干扰性的UI重渲染，也是提升用户体验的关键。

这些挑战共同指向了对一个更为健壮、智能、并能良好支持多用户协作环境的Excel导入会话管理新机制的迫切需求。该新机制旨在通过引入诸如全局唯一活动会话管理、心跳活性检测、快速标记与异步清理、以及前端智能状态同步等策略，来全面提升用户体验、数据一致性和系统效率。

## 2. 核心需求

为提升用户体验和系统健壮性，针对Excel导入的会话管理，明确以下核心需求：

1. **快速响应用户操作**：尤其是在用户执行取消导入等操作时，系统前端应能立即给出反馈，不应因等待后台资源（如大文件）的实际清理完成而出现长时间无响应或操作阻塞。
2. **确保状态可靠与一致**：前端展示的会话信息（如当前活动会话ID、状态、进度等）必须与后端权威的会话状态保持严格一致，避免出现"幽灵会话"或不一致的状态显示。
3. **具备自动恢复能力与及时的会话释放**：在发生意外状态（如网络中断、浏览器关闭）时，系统应能通过更灵活的机制（如心跳检测）及时发现处理者失联，以便会话能被合理接管或清理，而不是长时间等待固定超时。
4. **支持全局唯一活动会话**：在任何时刻，系统（针对Excel导入功能）只应维护一个全局性的活动导入会话，多个有权限的用户可以协同查看或操作此会话。

## 3. 整体解决方案架构

为满足上述核心需求，我们提议采用一种结合后端"快速标记、异步清理"、"心跳活性检测"模式与前端"统一API查询、智能状态同步、心跳维持"的综合解决方案。

### 3.1. 核心思想

* **后端："快速标记，异步清理"模式** (针对取消操作)：
  * **取消时快速标记状态**：用户发起取消时，后端迅速在事务内将会话状态标记为"已取消"并记录取消时间，然后立即响应前端。
  * **后台异步清理资源**：实际的资源清理（文件删除、缓存清理等）通过独立的后台异步任务（Celery Task）处理。
* **后端："心跳活性检测与超时管理"模式** (针对会话处理权与接管)：
  * **处理者心跳维持**：当一个用户成为某个活动会话的 `processing_user` 并开始进行需要独占处理的交互时（如解决冲突），前端应定期向后端发送"心跳"请求。
  * **后端记录心跳**：后端记录每次有效心跳的时间戳（如 `last_heartbeat_at`）。
  * **基于心跳的超时判断**：会话是否可被接管，主要依据 `last_heartbeat_at` 是否在设定的较短时间内（例如1-2分钟）未更新。如果心跳中断，则认为原处理者已失联，会话可被接管。原先基于 `last_activity` 的长固定超时（如30分钟）将不再是判断可接管性的主要依据，或仅作为无心跳时的最终后备。
* **前端："统一API查询、智能状态同步、心跳维持"**：
  * **统一API入口**：前端总是通过一个API获取当前系统全局唯一的活动会话信息及其对当前用户的权限。
  * **智能同步**：前端Hook在获取API数据后，通过"ID优先，再关键状态比较"的逻辑，智能决定UI是否需要更新，以兼顾准确性和性能。
  * **心跳发送**：当用户正在积极处理一个会话时（例如，在冲突解决界面），前端Hook负责启动并维持心跳轮询。

### 3.2. 关键机制与职责

#### 3.2.1. 后端关键职责

1. **事务性核心状态更新**：会话的创建、取消标记 (`status`, `cancelled_at`) 等核心状态变更需在数据库事务内完成。
2. **可靠的异步任务触发**：取消标记后，可靠触发Celery任务进行异步资源清理，并记录 `cleaned_up_at`。
3. **心跳处理API与逻辑**：

* 提供一个新的API端点（例如 `POST /api/excel-import/session/<session_id>/heartbeat/`）用于接收前端发送的心跳。
* 该API验证请求用户是否为当前会话的合法 `processing_user`，若是，则更新对应 `ImportSession` 的 `last_heartbeat_at` 字段为当前时间，并可同时更新 `last_activity`。
* `ImportSession` 模型需添加 `last_heartbeat_at` 字段。
* 重写 `ImportSession.can_be_taken_over()` 方法，主要逻辑基于 `last_heartbeat_at` 是否超时（例如，超过1-2分钟未收到心跳）。可以保留一个基于 `last_activity` 的更长后备超时机制，用于处理从未发送过心跳的"僵尸"会话。

4. **统一的活动会话状态查询接口** (`GetActiveImportSessionView`)：

* 继续负责查询并返回当前系统中全局唯一的活动导入会话的完整信息。
* 附加信息中，`can_takeover` 标志的计算现在将主要依赖新的 `ImportSession.can_be_taken_over()`（基于心跳）的逻辑。

5. **会话创建控制** (`create_session` 服务方法)：继续在创建新会话前检查是否存在全局活动会话。
6. **接管会话API** (`TakeoverSessionView`): 其执行条件将依赖于新的 `can_be_taken_over()` 逻辑。

#### 3.2.2. 前端关键职责与异步交互策略 (通过 `useExcelImportSession` Hook 实现)

1. **核心原则：统一API获取全局状态，前端智能决策UI同步，按需发送心跳**

* **用户场景解读与优化后的交互模型**：保持之前的描述，即前端通过统一API获取全局活动会话，Hook内部进行ID优先、再关键状态比较的智能UI更新。
* **新增心跳维持职责**：
  * 当Hook同步到的 `activeSessionInfo` 表明当前用户是 `processing_user`，并且会话状态处于需要用户长时间交互的阶段（例如 `CONFLICT_RESOLUTION`，或未来可能的其他交互式处理阶段），Hook应启动一个定时器，定期调用后端的心跳API。
  * 心跳的目的是告知后端"当前用户仍在此会话上活跃操作"。
  * 当用户完成交互、离开相关界面、会话状态改变为不再需要心跳（如变为 `IMPORT_COMPLETE`, `CANCELLED`），或Hook卸载时，必须清除心跳定时器。

2. **精细化的异步交互控制与UI更新策略 (在Hook内部或由Hook支持)**:

* (此部分关于"逃逸回调处理"和"UI更新防抖"的通用健壮性措施保持不变，它们与心跳机制互补)

## 4. 方案优势与价值 (引入心跳机制后)

1. **解耦关注点**：保持不变。
2. **提高可靠性与一致性**：通过心跳，能更准确地判断处理者是否在线，减少因误判导致的会话状态不一致。
3. **改善用户体验**：

* 取消操作依然快速。
* **更及时的会话释放**：如果处理者意外离线（如关闭浏览器），心跳中断能使会话更快地变为可接管状态，减少其他用户的等待时间。
* UI渲染优化通过前端Hook的智能比对实现。

4. **增强系统健壮性**：心跳机制为会话锁定提供更强的保障，减少"僵尸会话"的产生。

## 5. 详细实施计划 (概要调整，具体细节待后续任务中展开)

以下为分阶段的实施计划，**已根据引入心跳机制的决策进行调整**。

### 阶段一：后端核心功能实现 (会话状态管理、心跳与异步清理)

* **任务 1.1**：增强会话模型 (Django Model)
  * [x] 审视 `ImportSession` 模型状态枚举。
  * [x] 添加 `cancelled_at`, `cleaned_up_at`。(已完成)
  * [x] 新增 `last_heartbeat_at = models.DateTimeField(null=True, blank=True, db_index=True)` 字段。 (此项在之前的心跳机制初步实现中已添加)
  * [x] **重写 `can_be_taken_over()` 方法，主要逻辑基于 `last_heartbeat_at` 是否超时（例如，默认1-2分钟），可保留基于 `last_activity` 的更长后备超时。** (CHANGE: [2025-05-16] 已完成此部分的重构)
* **任务 1.2**：实现"快速标记"取消接口 与 Celery任务触发点调整
  * [x] 优化 `ExcelImportCancelView` 和服务层 `_cleanup_session_resources`：事务内标记 `status` 和 `cancelled_at`。 (基本完成)
  * [x] 服务层方法在标记取消后，触发任务1.3的Celery异步清理任务。 (基本完成)
  * *说明：确保此接口的响应速度，实际资源清理完全异步。*
* **任务 1.3**：设计并实现异步清理任务 (Celery Task)
  * [x] Celery任务 `cleanup_cancelled_session_task` 已创建，负责删除临时文件和更新 `cleaned_up_at`。(骨架完成)
  * [x] **待完善**：加入清理相关缓存和可能的其他临时数据库条目的逻辑。 (CHANGE: [2025-05-16] 已添加缓存清理逻辑，数据库条目清理待进一步明确需求)
* **新增任务 1.3.A**：实现心跳API端点和服务 (CHANGE: [2025-05-16] 已完成)
  * [x] 在 `archive_records/views.py` 中创建 `SessionHeartbeatView(APIView)`。
  * [x] `POST` 方法接收 `session_id`。
  * [x] 在 `ImportSessionManager` 中添加 `record_heartbeat(session_id, user)` 服务方法：验证用户是否为 `processing_user`，若是则更新 `last_heartbeat_at` 和 `last_activity`。
  * [x] `SessionHeartbeatView` 调用此服务方法。
  * [x] 在 `archive_records/urls.py` 中为此视图配置URL路由 (已存在)。
* **任务 1.4**：调整会话创建/获取逻辑
  * [x] `ImportSessionManager.create_session`：创建前调用 `get_system_active_session()` 检查全局唯一活动会话。(已完成)
  * [x] `ImportSessionManager.get_system_active_session()`：获取全局唯一活动会话。(已完成)
  * [x] `GetActiveImportSessionView`：调用 `get_system_active_session()`，其返回的 `can_takeover` 标志现在将依赖于新的 `can_be_taken_over()` (基于心跳)的逻辑。(视图本身已调整，依赖的方法将改变)
* **新增任务 1.5**：调整会话接管API (`TakeoverSessionView`) (CHANGE: [2025-05-16] 已验证符合要求)
  * [x] 确保 `TakeoverSessionView` 的接管条件判断正确使用了 `ImportSession.can_be_taken_over()` 的新逻辑（基于心跳超时）。

### 阶段二：前端核心功能与状态同步实现 (集成心跳机制)

* **任务 2.1**：实现前端会话状态管理器/Hook (`useExcelImportSession`) (CHANGE: [2025-05-16] 心跳管理核心逻辑已验证基本实现)
  * [x] **状态管理**：除原有状态外，可能需要管理心跳定时器ID。 (已通过 `heartbeatIntervalRef` 和 `activeSessionIdForHeartbeat` 实现)
  * [x] **核心方法 `fetchSystemActiveSession()`**：保持其获取全局会话及用户权限，并进行智能比对更新UI的逻辑。 (已实现并集成心跳启停判断)
  * [x] **新增心跳管理方法**：
    * `startHeartbeat(sessionId)`: 当用户成为 `processing_user` 且会话处于需交互状态时调用，启动定时器定期调用后端心跳API。 (已实现)
    * `stopHeartbeat()`: 在适当时候（用户完成操作、离开界面、会话结束、Hook卸载）调用，清除心跳定时器。 (已实现)
  * [x] 封装其他API调用方法（创建、取消、获取结果、确认导入、接管）并确保它们与心跳逻辑协调。 (已通过在关键操作前调用 `stopHeartbeat` 实现)
* **任务 2.2**：实现健壮的状态验证与UI同步逻辑 (利用Hook) (CHANGE: [2025-05-16] 已验证Hook现有逻辑满足核心要求)
  * [x] UI组件通过Hook获取并展示全局活动会话及用户权限。 (Hook核心功能)
  * [x] 在用户开始处理会话的特定阶段（如打开冲突解决模态框）时，触发Hook启动心跳。 (已由Hook内部基于状态的自动心跳管理逻辑覆盖)
* **任务 2.3**：实现用户取消操作的前端逻辑** (基本不变，但Hook内部的心跳应停止)
* **任务 2.4**：处理"在途/逃逸"的异步回调** (保持不变)

### 阶段三：高级健壮性与体验优化 (防抖等) (基本保持不变)

* **任务 3.1**：UI更新防抖机制实现**
* **任务 3.2**：全面的错误处理与用户友好提示**
* **任务 3.3**：UI状态转换的平滑性与视觉反馈**

### 阶段四：测试与验证 (需增加心跳相关测试)

* **任务 4.1**：后端单元/集成测试
  * [ ] **新增**：测试心跳API是否正确更新 `last_heartbeat_at`。
  * [ ] **新增**：测试新的 `can_be_taken_over()` 逻辑（基于心跳超时和后备超时）。
  * [ ] **新增**：测试 `TakeoverSessionView` 是否能正确基于新的接管逻辑工作。
* **任务 4.2**：前端单元/组件测试**
  * [ ] **新增**：测试 `useExcelImportSession` Hook中 `startHeartbeat` 和 `stopHeartbeat` 的逻辑，以及心跳API的调用。
* **任务 4.3**：端到端（E2E）测试**
  * [ ] **新增场景**：心跳与接管
    * 用户A成为处理者并开始交互（前端发送心跳）。
    * 模拟用户A前端心跳中断（例如，通过工具停止JS执行或关闭标签页）。
    * 用户B在心跳超时后，应能成功接管会话。
    * 用户A的原页面（如果还能访问）应能正确反映会话已被他人接管。
  * 其他场景视心跳机制引入的影响进行调整。

这个实施计划提供了一个大致的框架，具体任务的优先级和细节可以根据项目实际情况进行调整。

### 阶段总结

* 目前，除了需要您进一步配置（如Admin中的定时任务调度）、进行用户界面/端到端测试、或提供更具体的权限/通知需求之外，计划中的主要开发和优化任务已处理完毕。
