# Operation Document: Refactor tasks.py for Parallel Processing

## 📋 Change Summary

**Purpose**: Refactor `archive_processing/tasks.py` to support parallel PDF chunk analysis using Celery Chord.
**Scope**: `archive_processing/tasks.py`
**Associated**: AFM-Parallel (Placeholder Issue ID)

## 🔧 Operation Steps

### 📊 OP-001: Analyze Existing Task

**Precondition**: `process_pdf_task` exists and performs serial processing including PDF analysis, pre-checks, file splitting, archiving, and DB updates.
**Operation**: Identified reusable logic blocks for pre-checks and single-part processing. Identified parts to be removed (serial analysis, main loop) and parts to be added (chunking, dispatch).
**Postcondition**: Clear understanding of which parts to extract, remove, and add.

### ✏️ OP-002: Refactor `tasks.py`

**Precondition**: Original `tasks.py` file.
**Operation**:
    - Extracted pre-check logic (lines ~194-297) into `_perform_pre_checks` helper function.
    - Extracted single-part processing logic (loop body, lines ~301-450) into `_process_and_archive_single_part` helper function.
    - Added placeholder `@shared_task` definition for `processing_pdf_chunk_task` (intended to call a service method).
    - Added placeholder `@shared_task` definition for `stitch_and_execute_results` callback, marking where helpers will be called.
    - Modified `process_pdf_task`:
        - Removed calls to `PdfProcessingService`, the pre-check block, and the main processing loop.
        - Added logic to use `fitz` to get total pages.
        - Added logic to calculate chunks based on `CHUNK_SIZE`.
        - Added logic to create `processing_pdf_chunk_task` signatures.
        - Added logic to create `stitch_and_execute_results` callback signature.
        - Added logic to update task status to `processing_chunks` before dispatch.
        - Added logic to call `celery.chord` to dispatch tasks.
        - Updated error handling for the dispatch phase.
    - Added necessary imports (`chord`, `fitz`, `math`).
**Postcondition**: `tasks.py` now contains the structural framework for parallel processing via Chord. `process_pdf_task` dispatches work, `processing_pdf_chunk_task` and `stitch_and_execute_results` are placeholders, and helper functions contain extracted logic.

## 📝 Change Details

### CH-001: Extract `_perform_pre_checks`

**File**: `archive_processing/tasks.py`
**Before**: Logic embedded within `process_pdf_task` (approx. lines 194-297).
**After**: Logic moved to `_perform_pre_checks(task_id, splitting_info_results)`.
**Rationale**: Improve modularity, create reusable pre-check function for callback task.
**Potential Impact**: None expected, pure refactoring.

### CH-002: Extract `_process_and_archive_single_part`

**File**: `archive_processing/tasks.py`
**Before**: Logic embedded within the main `for` loop in `process_pdf_task` (approx. lines 301-450).
**After**: Logic moved to `_process_and_archive_single_part(task_id, pdf_path, part_info, user_id, assigned_box_number)`.
**Rationale**: Improve modularity, create reusable function for processing a single identified part, callable from the callback task.
**Potential Impact**: None expected, pure refactoring.

### CH-003: Add Placeholders `processing_pdf_chunk_task`, `stitch_and_execute_results`

**File**: `archive_processing/tasks.py`
**Before**: Tasks did not exist.
**After**: `@shared_task` definitions added with parameters and TODO comments for future implementation. `processing_pdf_chunk_task` docstring updated to reflect it calls a service.
**Rationale**: Define the necessary Celery tasks for the parallel workflow (chunk processing and result stitching/execution).
**Potential Impact**: None until implemented.

### CH-004: Modify `process_pdf_task` for Dispatch

**File**: `archive_processing/tasks.py`
**Before**: Performed full serial processing.
**After**: Retains initial checks, removes serial processing logic, adds logic to get total pages, calculate chunks, create task/callback signatures, update status to `processing_chunks`, and dispatch using `celery.chord`.
**Rationale**: Transform the main task into a dispatcher for the parallel workflow.
**Potential Impact**: Changes the fundamental behavior of the task from serial execution to parallel dispatch.

## ✅ Verification Results

**Method**: Code review of the applied diff.
**Results**: The changes align with the planned structural refactoring. Helpers extracted, placeholders added, main task modified for dispatch.
**Problems**: None observed in the structure.
**Solutions**: N/A.
