# Operation Document: 实现严格后端驱动的Excel导入重置逻辑

## 📋 Change Summary

**Purpose**: 根据用户要求，修改Excel导入功能中的"取消并重新选择文件"操作，使其严格遵循后端状态来驱动UI更新，以增强状态的严谨性和一致性。
**Scope**: 主要影响 `frontend/components/records/import/excel-import-with-conflict-resolution.tsx` 组件中的 `handleReset` 函数及其相关的状态管理和副作用钩子。
**Associated**: 用户关于Excel导入取消功能行为的反馈和需求。

## 🔧 Operation Steps

### 📊 OP-001: 分析现有问题与用户需求

**Precondition**: 用户反馈，在点击"取消并重新选择文件"后，UI可能因前端乐观更新而与后端实际状态不一致，尤其是在后端取消操作失败或缓慢时。用户要求UI更新严格依赖后端确认。
**Operation**:
    - 分析了当前 `handleReset` 的乐观UI更新机制。
    - 明确了严格后端驱动UI更新的流程：等待后端操作 -> 获取新状态 -> UI根据新状态刷新。
**Postcondition**: 确定了修改方案，以满足用户对严谨性的要求。

### ✏️ OP-002: 实现严格后端驱动的重置逻辑

**Precondition**: `excel-import-with-conflict-resolution.tsx` 文件包含旧的 `handleReset` 实现。
**Operation**:
    1.  在组件中添加新的 `useState` 变量 `isHardResetting`，用于在 `handleReset` 执行期间（等待后端响应时）控制UI的加载/禁用状态。
    2.  添加一个新的 `useEffect` 钩子，该钩子监听 `derivedCurrentStep` 状态。当 `derivedCurrentStep` 变为 `'select'` 时，此钩子负责清理所有与单个导入会话相关的组件级UI状态（如 `selectedFile`, `analysisStats`, `uploadProgress` 等），确保选择文件界面总是干净的。
    3.  完全重写 `handleReset` 函数的实现逻辑：
        a.  开始时，设置 `isHardResetting = true` 并可显示一个进行中的toast。
        b.  不再立即清除组件的局部UI状态。
        c.  如果存在活动会话，则 `await cancelCurrentImport(sessionId)`。此函数内部在后端API调用后会通过 `fetchSystemActiveSession` 更新全局的 `activeSessionInfo`。
        d.  如果不存在活动会话，则 `await resetImportState()`。此函数内部也会通过 `fetchSystemActiveSession` 更新（或确认）`activeSessionInfo`。
        e.  捕获上述异步操作中可能发生的错误，并设置 `operationSuccess` 标志。
        f.  在 `finally` 块中，清除 `isHardResetting` 状态。
        g.  根据 `operationSuccess` 和 `errorLoadingSession` 的状态，显示最终的操作结果toast。
        h.  UI的实际刷新（例如，从分析步骤回到选择文件步骤）完全依赖于因 `activeSessionInfo` 变化而触发的顶层 `useEffect`，该 `useEffect` 会更新 `derivedCurrentStep`。
    4.  调整了 `handleReset` 的依赖数组，移除了不再由其直接管理的UI状态setter。
    5.  修复了之前修改引入的 `toast` variant 类型错误。
**Postcondition**: `handleReset` 函数现在遵循严格的后端状态驱动逻辑。UI更新在后端操作（取消、状态获取）完成后，基于最新的 `activeSessionInfo` 进行。添加了加载状态管理和状态清理的副作用钩子。

### 🧪 OP-003: 后续验证与建议 (未执行，作为计划)

**Precondition**: 代码已按上述逻辑修改。
**Operation**:
    - **UI加载状态实现**: 在JSX中实际使用 `isHardResetting` 状态来禁用按钮或显示加载指示。
    - **全面测试**: 执行单元测试、组件测试和端到端测试，覆盖取消成功、取消失败、无会话时重置、网络延迟等多种场景。
    - **代码审查**: 由团队成员审查新的实现逻辑。
**Postcondition**: 功能的正确性、鲁棒性和用户体验得到验证和确认。

## 📝 Change Details

### CH-001: 添加 `isHardResetting` state 和 `useEffect([derivedCurrentStep])`

**File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
**Before**:

```typescript
// const [isHardResetting, setIsHardResetting] = useState<boolean>(false); // 不存在
// useEffect(() => { if (derivedCurrentStep === 'select') { /* 清理逻辑 */ } }, [derivedCurrentStep]); // 不存在
```

**After**:

```typescript
// ... (在 useActiveSessionWarning 之后)
  const [isHardResetting, setIsHardResetting] = useState<boolean>(false);

  useEffect(() => {
    logger.info(`[ExcelImport.derivedCurrentStepEffect] Derived current step changed to: ${derivedCurrentStep}`);
    if (derivedCurrentStep === 'select') {
      logger.info('[ExcelImport.derivedCurrentStepEffect] Now in \'select\' step, ensuring UI components are reset.');
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setUploadProgress(0);
      setAnalysisStats(null); 
      setConflictRecordsInternal([]); 
      setFilteredConflictRecords([]); 
      setShowConflictModal(false); 
      setImportLogDetails(null);
      setDataLoadingPhase(-1); 
      setDataLoadingProgress(0); 
      setIsConfirmingCompletion(false); 
    }
  }, [derivedCurrentStep]);
// ...
```

**Rationale**: `isHardResetting` 用于在等待后端时提供UI反馈。`useEffect([derivedCurrentStep])` 确保在流程回到选择文件步骤时，相关的UI数据状态被彻底清理，这是严格后端驱动模式下保持UI纯净的关键。

### CH-002: 重写 `handleReset` 函数

**File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
**Before**: (旧的 `handleReset` 实现，部分乐观更新UI)

```typescript
  const handleReset = useCallback(async (options?: { showToast?: boolean, toastMessage?: string }) => {
    // ... 旧的实现，会立即清除 selectedFile, analysisStats 等 ...
    // ... 然后调用 await cancelCurrentImport() 和 await resetImportState() ...
  }, [
    activeSessionInfo, 
    authUser, 
    cancelCurrentImport, 
    resetImportState, 
    toast, 
    setActiveSessionToProcess, 
    setIsSessionOverrideActive, 
    clearSessionStorageFromComp, 
  ]);
```

**After**: (新的 `handleReset` 实现，严格后端驱动)

```typescript
  const handleReset = useCallback(async (options?: { showToast?: boolean, toastMessage?: string }) => {
    const { showToast = true, toastMessage = "正在重置导入操作，请稍候..." } = options || {};
    logger.info('[ExcelImport] handleReset (Strict Backend Driven): 重置操作开始。');

    if (isResettingRef.current) { /* ...防重入... */ return; }
    isResettingRef.current = true;
    setIsHardResetting(true); 

    if (showToast) { toast({ title: "请稍候", description: toastMessage, duration: 15000 }); }

    const currentSessionIdToCancel = activeSessionInfo?.session_id;
    let operationSuccess = true; 

    try {
      if (currentSessionIdToCancel) {
        const cancelAcknowledged = await cancelCurrentImport(currentSessionIdToCancel);
        if (!cancelAcknowledged) { operationSuccess = false; /* ...log... */ }
        else { /* ...log... */ }
      } else {
        await resetImportState(); 
      }
    } catch (error) {
      operationSuccess = false; /* ...log... */
    } finally {
      // @ts-ignore 
      window.lastResetTime = Date.now(); 
      setTimeout(() => {
        isResettingRef.current = false;
        setIsHardResetting(false); 
        // ... (根据 operationSuccess 和 errorLoadingSession 显示最终toast) ...
      }, 200);
    }
  }, [
    activeSessionInfo, 
    cancelCurrentImport, 
    resetImportState, 
    toast,
    errorLoadingSession 
  ]);
```

**Rationale**: 实现用户要求的严格后端状态驱动UI更新。移除了立即的UI状态清理，转而依赖 `activeSessionInfo` 更新后由 `useEffect` 触发的UI刷新和状态清理。添加了 `isHardResetting` 来管理加载状态。

### CH-003: 修复Toast Variant错误

**File**: `frontend/components/records/import/excel-import-with-conflict-resolution.tsx`
**Before**:

```typescript
// toast({ title: "重置部分失败", ..., variant: "warning", ... });
```

**After**:

```typescript
// toast({ title: "重置操作提醒", description: "尝试重置导入时遇到一些问题...", duration: 6000 }); // 移除了 variant: "warning"
```

**Rationale**: 项目中使用的Toast组件不支持 "warning" 变体，修正为使用默认变体并调整提示文本。

## ✅ Verification Results

**Method**: Linter检查通过。代码逻辑基于用户需求进行了重构。
**Results**: 代码修改已应用，Linter错误已解决。
**Problems**: `edit_file` 工具在处理小范围精确插入或文件处于潜在Lint错误状态时，多次未能按预期工作，导致需要采用更大块代码替换的策略。
**Solutions**: 采用一次性提交包含所有相关改动的较大代码块进行替换，并仔细验证应用后的结果。

## 🤖 Priority Levels

- **P0**: 确保 `handleReset` 功能在各种情况下都能正确取消会话（如果需要）并最终使UI反映正确的后端状态。
- **P1**: 实现 `isHardResetting` 状态在UI上的视觉反馈（如加载指示）。
- **P1**: 对新的 `handleReset` 逻辑进行全面的测试。
- **P2**: 优化 `handleReset` 中 `finally` 块的toast提示逻辑，使其更智能地避免与 `errorLoadingSession` 的toast重复。
