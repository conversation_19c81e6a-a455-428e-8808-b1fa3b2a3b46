
# Excel导入冲突确认功能设计方案---前端设计示例

```js
/**
 * 注意：此文件仅作为设计文档的一部分，展示概念实现，不是实际的代码文件。
 * 在实际项目中需要正确导入相应组件库并处理依赖关系。
 * 
 * Excel导入冲突确认前端组件
 * 
 * 用于展示Excel导入时发现的冲突记录，并允许用户确认更新决策。
 * 该代码作为设计方案的一部分，展示实现思路。
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, AlertTriangle, Check, X, FileUp, RefreshCw } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

// 字段差异类型
interface FieldDifference {
  field: string;
  field_label: string;
  existing: string;
  imported: string;
}

// 冲突记录类型
interface ConflictRecord {
  row: number;
  commission_number: string;
  sample_number?: string;
  field_differences: FieldDifference[];
  action: 'update' | 'skip';
}

// 导入分析结果类型
interface ImportAnalysisResult {
  import_session_id: string;
  total_records: number;
  new_records: number;
  conflict_records: Omit<ConflictRecord, 'action'>[];
}

// 导入服务类型
interface ImportService {
  analyzeExcelFile: (file: File, options?: any) => Promise<ImportAnalysisResult>;
  confirmImport: (sessionId: string, resolutions: { commission_number: string; row: number; action: string }[]) => Promise<any>;
}

// 冲突确认模态框属性
interface ConflictResolutionModalProps {
  isOpen: boolean;
  onClose: () => void;
  conflicts: ConflictRecord[];
  onResolve: (resolvedConflicts: ConflictRecord[]) => void;
  isProcessing: boolean;
}

/**
 * 冲突确认对话框组件
 */
export const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  isOpen,
  onClose,
  conflicts,
  onResolve,
  isProcessing
}) => {
  // 状态：处理后的冲突记录
  const [resolvedConflicts, setResolvedConflicts] = useState<ConflictRecord[]>(conflicts);
  // 状态：全选按钮状态
  const [selectAll, setSelectAll] = useState(true);
  // 状态：当前活跃的标签
  const [activeTab, setActiveTab] = useState<string>('all');
  // 状态：已过滤的冲突列表
  const [filteredConflicts, setFilteredConflicts] = useState<ConflictRecord[]>(conflicts);

  // 当传入冲突列表变化时，更新内部状态
  useEffect(() => {
    if (conflicts.length > 0) {
      // 默认将所有冲突设置为"更新"操作
      const initialResolvedConflicts = conflicts.map(conflict => ({
        ...conflict,
        action: 'update'
      }));
      setResolvedConflicts(initialResolvedConflicts);
      setFilteredConflicts(initialResolvedConflicts);
      setSelectAll(true);
    }
  }, [conflicts]);

  // 根据标签过滤冲突记录
  useEffect(() => {
    if (activeTab === 'all') {
      setFilteredConflicts(resolvedConflicts);
    } else if (activeTab === 'update') {
      setFilteredConflicts(resolvedConflicts.filter(c => c.action === 'update'));
    } else if (activeTab === 'skip') {
      setFilteredConflicts(resolvedConflicts.filter(c => c.action === 'skip'));
    }
  }, [activeTab, resolvedConflicts]);

  // 切换单个记录的操作
  const toggleRecordAction = useCallback((index: number) => {
    setResolvedConflicts(prev => {
      const newConflicts = [...prev];
      const targetIndex = newConflicts.findIndex(
        c => c.commission_number === prev[index].commission_number && c.row === prev[index].row
      );
      
      if (targetIndex !== -1) {
        newConflicts[targetIndex] = {
          ...newConflicts[targetIndex],
          action: newConflicts[targetIndex].action === 'update' ? 'skip' : 'update'
        };
      }
      
      // 检查是否所有记录都被选为更新
      const allUpdate = newConflicts.every(c => c.action === 'update');
      setSelectAll(allUpdate);
      
      return newConflicts;
    });
  }, []);

  // 切换全部记录的操作
  const toggleSelectAll = useCallback(() => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    
    setResolvedConflicts(prev => 
      prev.map(conflict => ({
        ...conflict,
        action: newSelectAll ? 'update' : 'skip'
      }))
    );
  }, [selectAll]);

  // 处理确认按钮点击
  const handleConfirm = useCallback(() => {
    if (isProcessing) return;
    onResolve(resolvedConflicts);
  }, [isProcessing, onResolve, resolvedConflicts]);

  // 计算各类操作的数量
  const updateCount = resolvedConflicts.filter(c => c.action === 'update').length;
  const skipCount = resolvedConflicts.filter(c => c.action === 'skip').length;

  return (
    <Dialog open={isOpen} onOpenChange={isProcessing ? undefined : onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <AlertTriangle className="text-amber-500 h-5 w-5" />
            导入文件存在冲突记录
          </DialogTitle>
          <DialogDescription>
            系统发现以下记录在数据库中已存在（委托编号相同），但部分字段存在差异。
            请确认是要更新还是跳过这些记录。
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-between mt-4 mb-2">
          <div className="flex items-center gap-2">
            <Checkbox 
              id="select-all"
              checked={selectAll}
              onCheckedChange={toggleSelectAll}
              disabled={isProcessing}
            />
            <label htmlFor="select-all" className="text-sm font-medium cursor-pointer select-none">
              {selectAll ? '全部更新' : '全部跳过'}
            </label>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Badge className="bg-green-50 hover:bg-green-100 text-green-700">{updateCount}</Badge>
              <span className="text-sm">更新</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge className="bg-slate-50 hover:bg-slate-100 text-slate-700">{skipCount}</Badge>
              <span className="text-sm">跳过</span>
            </div>
          </div>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-full grid grid-cols-3">
            <TabsTrigger value="all">全部冲突 ({resolvedConflicts.length})</TabsTrigger>
            <TabsTrigger value="update">待更新 ({updateCount})</TabsTrigger>
            <TabsTrigger value="skip">待跳过 ({skipCount})</TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-0">
            <ScrollArea className="h-[50vh] border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell className="w-16">操作</TableCell>
                    <TableCell className="w-16">行号</TableCell>
                    <TableCell className="w-28">委托编号</TableCell>
                    <TableCell className="w-28">样品编号</TableCell>
                    <TableCell>字段差异</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConflicts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-32 text-center text-muted-foreground">
                        {activeTab === 'all' ? '暂无冲突记录' : `暂无${activeTab === 'update' ? '待更新' : '待跳过'}记录`}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredConflicts.map((conflict, index) => (
                      <TableRow key={`${conflict.commission_number}-${conflict.row}`} className="group">
                        <TableCell>
                          <Button
                            variant={conflict.action === 'update' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => toggleRecordAction(
                              resolvedConflicts.findIndex(
                                c => c.commission_number === conflict.commission_number && c.row === conflict.row
                              )
                            )}
                            disabled={isProcessing}
                            className="h-8 px-2"
                          >
                            {conflict.action === 'update' ? (
                              <Check className="h-4 w-4 mr-1" />
                            ) : (
                              <X className="h-4 w-4 mr-1" />
                            )}
                            {conflict.action === 'update' ? '更新' : '跳过'}
                          </Button>
                        </TableCell>
                        <TableCell className="font-mono">{conflict.row}</TableCell>
                        <TableCell className="max-w-[112px] truncate">{conflict.commission_number}</TableCell>
                        <TableCell className="max-w-[112px] truncate">{conflict.sample_number}</TableCell>
                        <TableCell>
                          <div className="space-y-1.5">
                            {conflict.field_differences.map((diff, i) => (
                              <div key={i} className="text-xs flex flex-wrap gap-x-2 items-center">
                                <span className="font-medium min-w-[80px] truncate" title={diff.field_label}>
                                  {diff.field_label}:
                                </span>
                                <div className="flex flex-wrap gap-1 items-center">
                                  <span className="bg-red-50 px-1 py-0.5 rounded text-red-700 line-through max-w-[200px] truncate" title={diff.existing}>
                                    {diff.existing || '(空)'}
                                  </span>
                                  <span className="text-slate-400">→</span>
                                  <span className="bg-green-50 px-1 py-0.5 rounded text-green-700 max-w-[200px] truncate" title={diff.imported}>
                                    {diff.imported || '(空)'}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            取消
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={isProcessing}
            className="min-w-[120px]"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                处理中...
              </>
            ) : (
              <>确认并执行导入</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Excel导入组件 - 支持冲突确认
 */
export const ExcelImportWithConflictResolution: React.FC = () => {
  // 状态：选择的文件
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // 状态：上传进度
  const [uploadProgress, setUploadProgress] = useState(0);
  // 状态：是否正在上传
  const [isUploading, setIsUploading] = useState(false);
  // 状态：模拟导入服务
  const [importService] = useState<ImportService>({
    analyzeExcelFile: async () => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        import_session_id: "session123",
        total_records: 100,
        new_records: 95,
        conflict_records: [
          {
            row: 5,
            commission_number: "C2023001",
            sample_number: "S2023001",
            field_differences: [
              {
                field: "client_name",
                field_label: "委托单位",
                existing: "公司A",
                imported: "公司A(新)"
              }
            ]
          },
          {
            row: 10,
            commission_number: "C2023002",
            sample_number: "S2023002",
            field_differences: [
              {
                field: "project_name",
                field_label: "工程名称",
                existing: "项目B",
                imported: "项目B(更新)"
              },
              {
                field: "standard_price",
                field_label: "标准价格",
                existing: "1000.00",
                imported: "1200.00"
              }
            ]
          }
        ]
      };
    },
    confirmImport: async () => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000));
      return { success: true };
    }
  });
  
  // 状态：冲突记录
  const [conflictRecords, setConflictRecords] = useState<ConflictRecord[]>([]);
  // 状态：导入会话ID
  const [importSessionId, setImportSessionId] = useState<string>("");
  // 状态：显示冲突对话框
  const [showConflictModal, setShowConflictModal] = useState(false);
  // 状态：处理冲突中
  const [isProcessingConflicts, setIsProcessingConflicts] = useState(false);
  
  const { toast } = useToast();

  // 处理文件选择变更
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  // 处理文件上传和分析
  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "请选择文件",
        description: "请先选择一个Excel文件进行导入",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 300);

    try {
      // 调用文件分析API
      const result = await importService.analyzeExcelFile(selectedFile);
      setImportSessionId(result.import_session_id);
      clearInterval(progressInterval);
      setUploadProgress(100);

      // 检查是否有冲突记录
      if (result.conflict_records && result.conflict_records.length > 0) {
        // 转换冲突记录格式，添加默认action
        const conflicts = result.conflict_records.map(record => ({
          ...record,
          action: 'update' as const
        }));
        
        setConflictRecords(conflicts);
        setShowConflictModal(true);
      } else {
        // 没有冲突，直接确认导入
        await confirmImportWithoutConflicts(result.import_session_id);
      }
    } catch (error) {
      console.error("导入分析出错:", error);
      toast({
        title: "导入分析失败",
        description: error instanceof Error ? error.message : "处理Excel文件时出错",
        variant: "destructive"
      });
    } finally {
      clearInterval(progressInterval);
      setIsUploading(false);
    }
  };

  // 处理无冲突情况的导入确认
  const confirmImportWithoutConflicts = async (sessionId: string) => {
    setIsUploading(true);
    setUploadProgress(0);

    // 模拟进度
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 5;
      });
    }, 200);

    try {
      // 调用导入确认API
      await importService.confirmImport(sessionId, []);
      clearInterval(progressInterval);
      setUploadProgress(100);

      toast({
        title: "导入成功",
        description: "Excel文件已成功导入",
        variant: "default"
      });

      // 重置状态
      setSelectedFile(null);
      setImportSessionId("");
    } catch (error) {
      console.error("导入确认出错:", error);
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "导入过程中出错",
        variant: "destructive"
      });
    } finally {
      clearInterval(progressInterval);
      setIsUploading(false);
    }
  };

  // 处理冲突解决
  const handleConflictResolution = async (resolvedConflicts: ConflictRecord[]) => {
    setIsProcessingConflicts(true);

    try {
      // 准备导入确认请求参数
      const resolutions = resolvedConflicts.map(conflict => ({
        commission_number: conflict.commission_number,
        row: conflict.row,
        action: conflict.action
      }));

      // 调用导入确认API
      await importService.confirmImport(importSessionId, resolutions);

      setShowConflictModal(false);
      toast({
        title: "导入成功",
        description: `已成功导入Excel文件，共处理 ${resolvedConflicts.length} 条冲突记录`,
        variant: "default"
      });

      // 重置状态
      setSelectedFile(null);
      setConflictRecords([]);
      setImportSessionId("");
    } catch (error) {
      console.error("处理冲突导入出错:", error);
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "处理冲突过程中出错",
        variant: "destructive"
      });
    } finally {
      setIsProcessingConflicts(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className="border rounded-lg p-6 bg-card">
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="text-lg font-medium">选择Excel文件</div>
            <p className="text-sm text-muted-foreground">
              请选择要导入的Excel文件，系统将分析文件内容并处理可能的冲突。
            </p>
          </div>

          <div className="flex items-center gap-4">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <label htmlFor="excel-file" className="text-sm font-medium">
                Excel文件
              </label>
              <input
                id="excel-file"
                type="file"
                accept=".xlsx,.xls"
                className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 flex-1 text-sm text-gray-600 cursor-pointer"
                onChange={handleFileChange}
                disabled={isUploading}
              />
            </div>

            <Button 
              onClick={handleFileUpload} 
              disabled={!selectedFile || isUploading}
              className="mt-auto"
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <FileUp className="mr-2 h-4 w-4" />
                  开始导入
                </>
              )}
            </Button>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>正在处理...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {selectedFile && (
            <div className="text-sm">
              <span className="font-medium">已选择文件:</span> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
            </div>
          )}
        </div>
      </div>

      {/* 冲突确认对话框 */}
      <ConflictResolutionModal
        isOpen={showConflictModal}
        onClose={() => setShowConflictModal(false)}
        conflicts={conflictRecords}
        onResolve={handleConflictResolution}
        isProcessing={isProcessingConflicts}
      />
    </div>
  );
}; 
```
